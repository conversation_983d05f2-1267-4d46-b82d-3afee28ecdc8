{
    "editor.formatOnSave": true,
    "editor.mouseWheelZoom": true,
    "typescript.tsdk": "node_modules/typescript/lib",
    "prettier.prettierPath": "",
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "i18n-ally.localesPaths": [
        "projects/app/public/locales",
    ],
    "i18n-ally.enabledParsers": [
        "json"
    ],
    "i18n-ally.keystyle": "nested",
    "i18n-ally.sortKeys": true,
    "i18n-ally.keepFulfilled": true,
    "i18n-ally.sourceLanguage": "zh", // 根据此语言文件翻译其他语言文件的变量和内容
    "i18n-ally.displayLanguage": "en",
    "search.exclude": {
        "**/.history": true,
        "/.history": true
    },
}