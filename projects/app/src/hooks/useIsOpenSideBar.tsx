import { useEffect, useMemo, useState } from 'react';

/**
 * 自定义钩子，用于根据 URL 参数控制侧边栏的打开和关闭状态。
 * 当 URL 参数 `isOpenSidebar` 为 `false` 时，调用 `closeSidebar` 函数关闭侧边栏。
 *
 * @param {() => void} closeSidebar - 用于关闭侧边栏的函数。
 * @param {() => void} openSidebar - 用于打开侧边栏的函数。
 * @returns {{ isOpenSidebar: boolean; closeSidebar: () => void; openSidebar: () => void }} - 包含侧边栏是否打开的布尔值和关闭侧边栏的函数的对象。
 */
function useIsOpenSideBar(closeSidebar?: () => void, openSidebar?: () => void) {
  const isOpenSidebar = useMemo(() => {
    return new URLSearchParams(window.location.search).get('isOpenSidebar');
  }, [window.location.search]);

  useEffect(() => {
    if (isOpenSidebar === 'false') {
      closeSidebar?.();
    }

    if (isOpenSidebar === 'true') {
      openSidebar?.();
    }
  }, [isOpenSidebar]);

  return {
    isOpenSidebar: isOpenSidebar === 'true',
    closeSidebar: () => closeSidebar?.(),
    openSidebar: () => openSidebar?.()
  };
}

export default useIsOpenSideBar;
