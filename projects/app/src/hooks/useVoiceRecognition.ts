import { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/hooks/useToast';
import { eventBus, multiEventBus, EventNameEnum } from '@/utils/eventbus';
// 添加AudioContext和MediaStream相关的类型声明
interface Window {
  webkitAudioContext: typeof AudioContext;
}

interface UseVoiceRecognitionOptions {
  appkey: string;
  token: string;
  onResult: (text: string) => void;
  onError?: (error: string) => void;
  onStart?: () => void;
}

interface VoiceRecognitionReturnType {
  isSpeaking: boolean;
  startSpeak: (isRouse?: boolean, rouseWord?: string[]) => Promise<void>;
  stopSpeak: () => void;
  isConnected: boolean;
  isTranscription: boolean;
}

/**
 * 注意事项:
 * 如遇到语音识别异常情况(SentenceEnd内容不准且内容重复): 正常的心跳是大概100毫秒1次,语音识别异常是可能是react组件刷新异常导致心跳过频到几十毫秒/几毫秒内,让心跳维持到100毫秒即可解决
 * 如遇到意外断连: 观察错误码,如果是40000002一般是阿里入参错误,如果是1006说明是正常关闭,一般是自己意外销毁的,检查销毁事件
 */

/**
 * 语音识别Hook
 * @param options 配置选项
 * @returns 包含语音识别状态和控制方法的对象
 */
const useVoiceRecognition = (options: UseVoiceRecognitionOptions): VoiceRecognitionReturnType => {
  const { toast } = useToast();
  const { appkey, token, onResult, onError, onStart } = options;

  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  // 是否正在转录
  const [isTranscription, setIsTranscription] = useState<boolean>(false);

  // Refs
  const websocketRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioSourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);
  const heartbeatIntervalRef = useRef<number | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  // 新增：存储最后一次TranscriptionResultChanged的结果
  const lastTranscriptionResultRef = useRef<string>('');
  // 新增：标记最后一次接收的消息类型
  const lastMessageTypeRef = useRef<string>('');
  // 是否为唤醒模式
  const isRouseRef = useRef<boolean>(false);
  // 唤醒词
  const rouseWordRef = useRef<string[]>([]);

  // 生成 UUID
  const generateUUID = () => {
    return '10000000-1000-4000-8000-100000000000'
      .replace(/[018]/g, (c: any) =>
        (c ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (c / 4)))).toString(16)
      )
      .replace(/-/g, '');
  };

  // 停止心跳
  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current !== null) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
      console.log('心跳已停止');
    }
  }, []);

  // 发送心跳
  const sendHeartbeat = useCallback(() => {
    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      try {
        const silenceFrame = new Int16Array(2048);
        websocketRef.current.send(silenceFrame.buffer);
      } catch (error) {
        console.error('发送心跳失败:', error);
        if (websocketRef.current && websocketRef.current.readyState !== WebSocket.OPEN) {
          console.log('WebSocket连接已断开');
        }
      }
    }
  }, []);

  // 开始心跳
  const startHeartbeat = useCallback(() => {
    // 停止现有心跳
    stopHeartbeat();

    // 发送第一次心跳
    sendHeartbeat();

    // 设置定期发送心跳
    heartbeatIntervalRef.current = window.setInterval(sendHeartbeat, 1000);
    console.log('心跳已启动，间隔：1000ms');
  }, [sendHeartbeat, stopHeartbeat]);

  // 连接WebSocket
  const connect = useCallback(() => {
    // 验证token
    if (!token || token.trim() === '') {
      console.error('无效的token，无法建立连接');
      onError?.('无效的token，无法建立连接');
      return;
    }

    if (isConnected && websocketRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接，无需重新连接');
      return;
    }

    const socketUrl = `wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token=${token}`;

    // 断开现有连接
    if (websocketRef.current) {
      console.log('关闭现有连接...');
      websocketRef.current.close();
      websocketRef.current = null;
    }

    // 停止现有心跳
    stopHeartbeat();

    // 创建新连接
    console.log('连接WebSocket:', socketUrl);
    try {
      websocketRef.current = new WebSocket(socketUrl);
      websocketRef.current.binaryType = 'arraybuffer';

      websocketRef.current.onopen = function () {
        console.log('WebSocket已连接, 状态:', websocketRef.current?.readyState);
        setIsConnected(true);

        const startTranscriptionMessage = {
          header: {
            appkey: appkey,
            namespace: 'SpeechTranscriber',
            name: 'StartTranscription',
            task_id: generateUUID(),
            message_id: generateUUID()
          },
          payload: {
            format: 'pcm',
            sample_rate: 16000,
            enable_intermediate_result: true,
            enable_punctuation_prediction: true,
            enable_inverse_text_normalization: true
          }
        };

        try {
          websocketRef.current?.send(JSON.stringify(startTranscriptionMessage));
          console.log('发送StartTranscription消息成功');
        } catch (error) {
          console.error('发送StartTranscription消息失败:', error);
        }

        // 连接后启动心跳
        console.log('准备开始心跳...');
        setTimeout(function () {
          if (websocketRef.current?.readyState === WebSocket.OPEN) {
            startHeartbeat();
            console.log('心跳开始');
          } else {
            console.error('WebSocket未就绪，无法开始心跳');
          }
        }, 500);
      };

      websocketRef.current.onmessage = function (event) {
        if (event.data instanceof ArrayBuffer) {
          // 二进制数据，可能是心跳响应
          return;
        }

        try {
          const message = JSON.parse(event.data);
          console.log('收到WebSocket消息:', message.header?.name);

          // 更新最后一次消息类型
          lastMessageTypeRef.current = message.header.name;

          if (
            message.header.name === 'TranscriptionResultChanged' ||
            message.header.name === 'TranscriptionCompleted'
          ) {
            setIsTranscription(true);

            // 保存最后一次TranscriptionResultChanged或TranscriptionCompleted的结果
            if (message.payload.result) {
              lastTranscriptionResultRef.current = message.payload.result;
              console.log('保存中间结果:', lastTranscriptionResultRef.current);
            }
          } else if (
            (message.header.name === 'SentenceEnd' ||
              message.header.name === 'TranscriptionCompleted') &&
            message.payload.result
          ) {
            setIsTranscription(false);
            // 句子结束事件，返回完整的识别结果
            let finalText = message.payload.result;
            console.log('句子结束:', finalText);

            // 清空中间结果
            lastTranscriptionResultRef.current = '';

            // result的loading
            console.log('已得到解析结果...');

            // 给唤醒词做同音字兼容
            if (isRouseRef.current) {
              // 定义替换规则：[原字符集合, 替换字符]
              const replacements = [
                ['你拟泥', '你'],
                ['好蒿', '好']
              ];

              // 检查字符是否符合替换规则，返回替换字符或null
              const getReplacementChar = (char: string) => {
                for (const [originals, replacement] of replacements) {
                  if (originals.includes(char)) return replacement;
                }
                return null;
              };

              // 检查相邻字符并替换
              for (let i = 0; i < finalText.length - 1; i++) {
                const rep1 = getReplacementChar(finalText[i]);
                const rep2 = getReplacementChar(finalText[i + 1]);

                if (rep1 && rep2) {
                  finalText = finalText.substring(0, i) + rep1 + rep2 + finalText.substring(i + 2);
                  i++; // 跳过已处理的下一个字符
                }
              }
            }
            console.log('同音字兼容后:', finalText);

            // 如果为唤醒模式,且全文中存在唤醒词
            if (
              isRouseRef.current &&
              rouseWordRef.current.some((word) => finalText.includes(word))
            ) {
              console.log('打断数字人说话');
              multiEventBus.emit(EventNameEnum.stopSpeak);
              // 找出哪个唤醒词被识别到了，以及它的位置
              let matchedWord = '';
              let index = -1;

              for (const word of rouseWordRef.current) {
                const wordIndex = finalText.indexOf(word);
                if (wordIndex !== -1) {
                  // 如果找到了唤醒词，并且它的位置比当前找到的更靠前（或者是第一个找到的）
                  if (index === -1 || wordIndex < index) {
                    index = wordIndex;
                    matchedWord = word;
                  }
                }
              }

              // 无论唤醒词在什么位置，都截取唤醒词后面的内容(如果截取后为空，使用整个文本)
              let newFinalText = finalText.substring(index + matchedWord.length);

              // 如果唤醒词后面紧跟着标点符号，去除这个符号
              // 正则匹配常见的中英文标点符号：，。！？、：；"'【】（）,\.!?:;"'\[\]\(\)
              if (
                newFinalText.length > 0 &&
                /^[，。！？、：；"'【】（）,\.!?:;"'\[\]\(\)]+/.test(newFinalText[0])
              ) {
                newFinalText = newFinalText.substring(1);
              }

              // 如果截取后为空，使用整个文本
              if (newFinalText.trim() === '') {
                newFinalText = finalText;
              }

              // 打印出识别到的唤醒词
              console.log(
                '已识别到唤醒词:',
                rouseWordRef.current.filter((word) => finalText.includes(word))
              );
              // 调用回调函数，将识别结果返回
              onResult(newFinalText);
              // 非唤醒模式,直接返回识别结果
            } else if (!isRouseRef.current) {
              onResult(finalText);
            }
          } else if (message.header.name === 'TaskFailed') {
            setIsTranscription(false);
            console.error('任务失败:', message);
            onError?.('语音识别任务失败: ' + message.header.status);
          }
        } catch (error) {
          setIsTranscription(false);
          console.error('解析消息失败:', error);
        }
      };

      websocketRef.current.onerror = function (error) {
        console.error('WebSocket错误:', error);
        setIsConnected(false);
        onError?.('WebSocket错误: ' + error.toString());
      };

      websocketRef.current.onclose = function (event) {
        console.log('WebSocket已关闭, 代码:', event.code);
        setIsConnected(false);

        // 停止心跳
        stopHeartbeat();
      };
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      onError?.('创建WebSocket连接失败: ' + error);
    }
  }, [appkey, isConnected, onError, startHeartbeat, stopHeartbeat, token]);

  // 断开连接
  const disconnect = useCallback(() => {
    stopHeartbeat();

    if (isSpeaking) {
      stopSpeak();
    }

    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = null;
    }

    setIsConnected(false);
    setIsTranscription(false);
  }, [isSpeaking, stopHeartbeat]);

  // 停止说话
  const stopSpeak = useCallback(() => {
    // 检查最后一条消息是否为SentenceEnd，如果不是，且有中间结果，则调用onResult返回中间结果
    if (
      lastMessageTypeRef.current !== 'SentenceEnd' &&
      lastMessageTypeRef.current !== 'TranscriptionCompleted' &&
      lastTranscriptionResultRef.current
    ) {
      console.log(
        '停止说话时未收到句子结束消息，返回最后的中间结果:',
        lastTranscriptionResultRef.current
      );
      onResult(lastTranscriptionResultRef.current);
      lastTranscriptionResultRef.current = ''; // 使用后清空
    }

    if (audioProcessorRef.current) {
      audioProcessorRef.current.disconnect();
      audioProcessorRef.current = null;
    }

    if (audioSourceRef.current) {
      audioSourceRef.current.disconnect();
      audioSourceRef.current = null;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    setIsSpeaking(false);
    setIsRecording(false);
    setIsTranscription(false);
  }, []);

  // 开始录音并发送音频数据
  const startSpeak = useCallback(
    async (isRouse?: boolean, rouseWord?: string[]) => {
      try {
        if (isRouse) {
          // 把是否唤醒状态存起来
          isRouseRef.current = isRouse;
        }
        if (rouseWord) {
          rouseWordRef.current = rouseWord;
        }
        if (!websocketRef.current || websocketRef.current.readyState !== WebSocket.OPEN) {
          // 如果websocket没有连接，先建立连接
          connect();

          // 等待连接建立
          await new Promise<void>((resolve) => {
            const checkConnection = () => {
              if (websocketRef.current?.readyState === WebSocket.OPEN) {
                resolve();
              } else {
                setTimeout(checkConnection, 100);
              }
            };
            checkConnection();
          });
        }

        if (isRecording) {
          console.log('录音已在进行中');
          return;
        }

        setIsSpeaking(true);

        // 请求麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        streamRef.current = stream;

        // 创建音频上下文
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        const audioContext = new AudioContextClass({ sampleRate: 16000 });
        audioContextRef.current = audioContext;

        // 创建音频源和处理节点
        const source = audioContext.createMediaStreamSource(stream);
        audioSourceRef.current = source;

        const processor = audioContext.createScriptProcessor(8192, 1, 1);
        audioProcessorRef.current = processor;

        setIsRecording(true);

        if (onStart) {
          onStart();
        }

        processor.onaudioprocess = (e) => {
          const inputData = e.inputBuffer.getChannelData(0);
          const inputData16 = new Int16Array(inputData.length);
          for (let i = 0; i < inputData.length; ++i) {
            inputData16[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7fff;
          }

          if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
            try {
              websocketRef.current.send(inputData16.buffer);
            } catch (error) {
              console.error('发送音频数据失败:', error);
              stopSpeak();
            }
          } else {
            stopSpeak();
            console.error('WebSocket未连接，停止录音');
          }
        };

        // 连接音频节点
        source.connect(processor);
        processor.connect(audioContext.destination);
      } catch (error: unknown) {
        console.error('启动录音失败:', error instanceof Error ? error.message : String(error));
        setIsSpeaking(false);
        setIsRecording(false);
        // 通知用户出错
        onError?.(error instanceof Error ? error.message : String(error));
      }
    },
    [connect, isRecording, onError, onStart, stopSpeak]
  );

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  // 返回状态和方法
  return {
    isSpeaking,
    startSpeak,
    stopSpeak,
    isConnected,
    isTranscription
  };
};

export default useVoiceRecognition;
