import { DatasetTypeEnum } from '@/constants/api/dataset';
import { DataSource } from '@/constants/common';
import {
  AppContextDetailType,
  DatasetSimpleItemType,
  SubmitAppContextProps
} from '@/fastgpt/global/core/dataset/type';
import { ParentTreePathItemType } from '@/types';
import {
  CreateDatasetProps,
  DatasetItemType,
  UpdateDatasetProps,
  TenantDatasetUserCreateRequest,
  DetailRequest,
  TenantDatasetUserPageRequest,
  TenantDatasetUsersVO,
  PagingData,
  TenantUserVO,
  TenantDatasetUserUpdateAuthorityRequest,
  TenantDatasetListRequest,
  TenantDatasetResp
} from '@/types/api/dataset';
import { POST } from '@/utils/request';

/* ======================== dataset ======================= */
export const getDatasets = ({
  parentId,
  type
}: {
  parentId?: string;
  type?: `${DatasetTypeEnum}`;
}) => POST<DatasetItemType[]>('/client/my/dataset/list', { parentId: parentId || '0', type });

export const getDatasetById = (id: string) =>
  POST<DatasetItemType>(`/client/my/dataset/detail?id=${id}`);

export const postCreateDataset = (data: CreateDatasetProps) =>
  POST<boolean>(`/client/my/dataset/create`, data);

export const putDatasetById = (data: UpdateDatasetProps) =>
  POST<void>(`/client/my/dataset/update`, data);

export const delDatasetById = (id: string) => POST(`/client/my/dataset/delete`, { id });

export const getDatasetPaths = (parentId?: string) =>
  POST<ParentTreePathItemType[]>('/client/my/dataset/paths', {
    parentId: parentId || '0'
  });

/* ================== file ======================== */
export const getFileViewUrl = (fileId: string) =>
  POST<string>('/client/my/dataset/file/getPreviewUrl', { fileId });

export const getTeamMembers = (fileId: string) =>
  POST<string>('/client/my/dataset/file/getPreviewUrl', { fileId });

/* ================== file ======================== */
export const onUpdateCollaborators = (fileId: any) =>
  POST<string>('/client/my/dataset/file/getPreviewUrl', { fileId });
export const onDelOneCollaborator = (fileId: string) =>
  POST<string>('/client/my/dataset/file/getPreviewUrl', { fileId });

export const getCollaboratorList = (fileId: string) =>
  POST<string>('/client/my/dataset/file/getPreviewUrl', { fileId });

// 添加知识库用户
export const createTenantDatasetUser = (data: TenantDatasetUserCreateRequest) =>
  POST<boolean>('/client/tenant/dataset/user/create', data);

// 删除知识库用户
export const deleteTenantDatasetUser = (data: DetailRequest) =>
  POST<boolean>('/client/tenant/dataset/user/delete', data);

// 租户知识库用户分页
export const getTenantDatasetUserPage = (data: TenantDatasetUserPageRequest) =>
  POST<PagingData<TenantDatasetUsersVO>>('/client/tenant/dataset/user/page', data);

// 选择租户人员分页
export const getTenantUserPage = (data: TenantDatasetUserPageRequest) =>
  POST<PagingData<TenantUserVO>>('/client/tenant/dataset/user/tenantUserPage', data);

// 修改知识库用户权限
export const updateTenantDatasetUserAuthority = (data: TenantDatasetUserUpdateAuthorityRequest) =>
  POST<boolean>('/client/tenant/dataset/user/updateAuthority', data);

// 获取租户知识库列表
export const getDatasetsList = (
  data: TenantDatasetListRequest,
  sourceKey: DataSource,
  isAdmin: boolean
) => {
  let { source, tmbId } = data;
  if (sourceKey === DataSource.Personal) {
    return POST<DatasetItemType[]>('/client/tenant/dataset/list', { source, tmbId });
  } else {
    return POST<DatasetItemType[]>('/client/tenant/dataset/list', { source });
  }
};
export const getAllDataset = () => {
  return POST<DatasetSimpleItemType[]>('/client/tenant/dataset/getAllDataset');
};

export const getAppContextDetail = (data: { tenantAppId: string }) => {
  return POST<AppContextDetailType>('/client/app/context/detail', data);
};

export const getAppContextDatasets = (data: { spaceIds: string[] }) => {
  return POST('/client/app/context/datasets', data);
};

export const submitAppContext = (data: SubmitAppContextProps) => {
  return POST<boolean>('/client/app/context/submit', data);
};
