import {
  PromptCenterCreateParams,
  PromptCenterListParams,
  PromptCenterListType,
  PromptDeleteValidParams,
  PromptDetailParams,
  PromptLabelCreateParams,
  PromptLabelListParams,
  PromptLabelType,
  PromptLabelUpdateParams,
  PromptListParams,
  PromptReorderParams,
  PromptType
} from '@/types/api/prompt';
import { POST } from '@/utils/request';
import { MessageBox, promisifyConfirm, promisifyDelete } from '@/utils/ui/messageBox';

export const getPromptList = (data: PromptListParams) =>
  POST<PromptType[]>(`/client/tenant/prompt/list`, data);

export const reorderPrompt = (data: PromptReorderParams) =>
  POST('/client/tenant/prompt/reorder', data);

export const getPromptLabelList = (data: PromptLabelListParams) =>
  POST<PromptLabelType[]>(`/core/prompt/label/list`, data);

export const createPromptLabel = (data: PromptLabelCreateParams) =>
  POST<PromptLabelType>('/core/prompt/label/create', data);

export const getPromptCenterList = (data: PromptCenterListParams) =>
  POST<PromptCenterListType[]>('/client/prompt/center/list', data);

export const promptCenterCreate = (data: PromptCenterCreateParams) =>
  POST<PromptCenterListType>('/client/prompt/center/create', data);

export const promptCenterDelete = async (data: PromptDetailParams) => {
  try {
    const valid = await validPromptWorkflow({ id: data.id });

    if (valid) {
      promisifyConfirm({
        title: '删除警告',
        content: '当前指令正在被工作流进行关联，请将关联指令进行解除后再进行删除！',
        cancelButtonProps: {
          hidden: true
        }
      });
      return Promise.reject();
    } else {
      return POST('/client/prompt/center/delete', data);
    }
  } catch (error) {
    return Promise.reject();
  }
};

export const promptCenterUpdate = (data: PromptLabelUpdateParams) =>
  POST<PromptCenterListType>('/client/prompt/center/update', data);

export const validPromptWorkflow = (data: PromptDeleteValidParams) =>
  POST<boolean>('/client/prompt/center/validPromptWorkflow', data);
