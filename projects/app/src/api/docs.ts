import { GET } from '@/utils/request';

export interface DocItem {
  id: string;
  title: string;
  path: string;
  category: string;
  order: number;
  isPublic: boolean;
}

export interface DocContent {
  id: string;
  title: string;
  content: string;
  category: string;
  lastUpdated: string;
}

/**
 * 获取文档列表
 */
export const getDocList = () => GET<DocItem[]>('/docs/list', '', { isResponseData: true });

/**
 * 获取指定文档的内容
 * @param docId 文档ID
 */
export const getDocById = (docId: string) =>
  GET<DocContent>(`/docs/${docId}`, '', { isResponseData: true });

/**
 * 获取文档分类列表
 */
export const getDocCategories = () =>
  GET<string[]>('/docs/categories', '', { isResponseData: true });

// 由于目前可能没有API可以直接调用，这里提供一个获取模拟数据的方法
export const getMockDocContent = (): Promise<DocContent> => {
  const mockContent = `
# 华云AI平台使用指南

## 产品介绍

华云AI平台是一个全功能的人工智能应用开发和部署平台，集成了多种AI模型和工具，帮助开发者和企业快速构建智能应用。

### 核心特性

- **多模型支持**：集成GPT、文心一言等主流大语言模型
- **知识库管理**：支持私有知识库构建和检索
- **应用开发**：低代码应用构建工具
- **数据处理**：内置数据清洗和标注工具
- **安全合规**：企业级数据安全和访问控制

## 快速开始

### 账号注册

1. 访问华云AI平台官网
2. 点击"注册"按钮
3. 填写必要信息并验证邮箱
4. 完成账号创建

### 创建第一个应用

\`\`\`javascript
// 示例代码 - 调用华云AI接口
const huayunai = require('huayunai-sdk');

// 初始化客户端
const client = new huayunai.Client({
  apiKey: 'YOUR_API_KEY'
});

// 调用大语言模型
async function callLLM() {
  const response = await client.chat.completions.create({
    model: 'gpt-4',
    messages: [
      { role: 'system', content: '你是一个助手' },
      { role: 'user', content: '请介绍一下华云AI平台' }
    ]
  });
  
  console.log(response.choices[0].message.content);
}

callLLM();
\`\`\`

## 知识库功能

### 创建知识库

知识库是华云AI平台的核心功能之一，允许用户导入自己的文档、网页和数据，构建专属知识体系。

创建知识库步骤：

1. 进入知识库管理页面
2. 点击"新建知识库"
3. 设置知识库名称和描述
4. 选择数据导入方式
5. 上传文档或提供API

### 知识检索

一旦知识库建立完成，您可以通过以下方式进行检索：

- **自然语言查询**：直接提问，系统会返回最相关的内容
- **高级搜索**：使用过滤器和布尔操作符进行精确搜索
- **API集成**：通过API在您自己的应用中调用知识库能力

## 应用开发指南

### 应用类型

华云AI平台支持多种应用类型：

| 应用类型 | 适用场景 | 开发难度 |
|---------|---------|---------|
| 聊天机器人 | 客服、助手 | 低 |
| 知识问答 | 内部知识库、FAQ | 中 |
| 内容生成 | 营销文案、报告 | 中 |
| 流程自动化 | 业务流程、审批 | 高 |

### 最佳实践

开发高质量AI应用的建议：

1. **明确应用场景**：确定具体问题和目标用户
2. **数据质量优先**：确保训练数据的质量和相关性
3. **迭代优化**：基于用户反馈持续改进
4. **安全防护**：实施防止滥用的机制
5. **性能监控**：建立监控指标，优化响应时间

## API参考

### 认证

所有API请求需要包含认证信息：

\`\`\`http
GET /api/v1/resources HTTP/1.1
Host: api.huayunai.com
Authorization: Bearer YOUR_API_KEY
\`\`\`

### 主要端点

- \`/api/v1/chat/completions\` - 聊天完成API
- \`/api/v1/embeddings\` - 文本嵌入API
- \`/api/v1/knowledge\` - 知识库操作API
- \`/api/v1/applications\` - 应用管理API

## 高级主题

### 自定义模型训练

对于特定领域的需求，华云AI平台支持自定义模型训练：

$$
L = -\\frac{1}{N}\\sum_{i=1}^{N}\\sum_{j=1}^{V}y_{i,j}\\log(p_{i,j})
$$

其中：
- $L$ 是损失函数
- $N$ 是训练样本数
- $V$ 是词汇表大小
- $y_{i,j}$ 是真实标签
- $p_{i,j}$ 是预测概率

## 常见问题

### API调用失败

**问题**: API调用返回401错误  
**解决方案**: 检查API密钥是否正确，以及是否有相应的访问权限

### 模型性能问题

**问题**: 模型回答不够精准  
**解决方案**: 尝试调整提示词，或者优化知识库内容

## 支持与社区

如需进一步支持，请通过以下渠道联系我们：

- [帮助中心](https://help.huayunai.com)
- [开发者社区](https://community.huayunai.com)
- [GitHub仓库](https://github.com/huayunai)

华云AI团队随时为您提供帮助！
`;

  const mockDoc: DocContent = {
    id: 'getting-started',
    title: '华云AI平台使用指南',
    content: mockContent,
    category: '使用指南',
    lastUpdated: new Date().toISOString()
  };

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockDoc);
    }, 500);
  });
};

// 根据markdown文件名获取内容
export const getMarkdownContent = async (filename: string): Promise<DocContent> => {
  try {
    const response = await fetch(`/markdown/${filename}`);
    if (!response.ok) {
      throw new Error(`Failed to load ${filename}`);
    }
    let content = await response.text();

    // 处理图片路径：将相对路径转换为绝对路径
    // 匹配所有的图片标签（包括文件名中的空格和特殊字符）
    content = content.replace(/!\[([^\]]*)\]\(\.\/([^)]+)\)/g, (match, alt, src) => {
      // URL编码文件名以处理空格和特殊字符
      const encodedSrc = encodeURIComponent(src);
      return `![${alt}](/markdown/${encodedSrc})`;
    });

    // 处理HTML img标签中的src属性
    content = content.replace(
      /<img\s+([^>]*?)src="\.\/([^"]+)"([^>]*?)>/g,
      (match, before, src, after) => {
        // URL编码文件名以处理空格和特殊字符
        const encodedSrc = encodeURIComponent(src);
        return `<img ${before}src="/markdown/${encodedSrc}"${after}>`;
      }
    );

    // 从markdown内容中提取标题（第一个一级标题）
    const titleMatch = content.match(/^#\s+(.+)$/m);
    const title = titleMatch ? titleMatch[1] : filename.replace('.markdown', '');

    return {
      id: filename.replace('.markdown', ''),
      title,
      content,
      category: '使用指南',
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error loading markdown file: ${filename}`, error);
    // 返回默认内容
    return getMockDocContent();
  }
};
