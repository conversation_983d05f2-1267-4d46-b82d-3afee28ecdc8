import { NextApiRequest, NextApiResponse } from 'next';
import { PagingData } from '@/types';
import { hashStr } from '@/utils/string';
import {
  TenantType,
  UpdateTenantDetailParams,
  DeptListType,
  CreateDeptParams,
  UpdateDeptParams,
  CreateTenantUserParams,
  UpdateTenantUserParams,
  DetailTenantUserParams,
  UpdateStatusTenantUserParams,
  RoleListType,
  SortDeptParams,
  TransferResourcesParams,
  GetTenantUserPageParams,
  TenantUserType,
  IndustryListType,
  ReplaceDeptParams,
  CreateRoleParams,
  CreateRoleType,
  CreateRoleMenuParams,
  DetailRoleType,
  RoleCountUserType,
  RoleUpdateUserParams,
  MenuListType
} from '@/types/api/tenant';
import { POST } from '@/utils/request';

export const getTenantDetail = () => POST<TenantType>(`/client/tenant/detail`, {});

export const updateTenantDetail = (data: UpdateTenantDetailParams) =>
  POST(`/client/tenant/update`, data);

export const getDeptList = () => POST<DeptListType>(`/client/dept/list`, {});

export const createDept = (data: CreateDeptParams) => POST(`/client/dept/create`, data);

export const updateDept = (data: UpdateDeptParams) => POST(`/client/dept/update`, data);

export const deleteDept = ({ id }: { id: string }) => POST(`/client/dept/delete`, { id });

export const sortDept = (data: SortDeptParams) => POST(`/client/dept/sort`, data);

export const replaceDept = (data: ReplaceDeptParams) => POST(`/client/dept/replace`, data);

export const getTenantUserPage = (data: GetTenantUserPageParams) =>
  POST<PagingData<TenantUserType>>(`/client/tenant/user/page`, data);

export const createTenantUser = ({ password, ...data }: CreateTenantUserParams) =>
  POST(`/client/tenant/user/create`, { password: hashStr(password), ...data });

export const updateTenantUser = ({ password, ...data }: UpdateTenantUserParams) => {
  const payload = password ? { ...data, password: hashStr(password) } : { ...data };
  return POST(`/client/tenant/user/update`, payload);
};

export const deleteTenantUser = (id: string) => POST(`/client/tenant/user/delete`, { id });

export const updateStatusTenantUser = (data: UpdateStatusTenantUserParams) =>
  POST(`/client/tenant/user/updateStatus`, data);

export const detailTenantUser = (id: string) =>
  POST<DetailTenantUserParams>(`/client/tenant/user/detail`, { id });

export const transferResources = (data: TransferResourcesParams) =>
  POST<DeptListType>(`/client/tenant/user/transferResources`, data);

export const getIndustryList = () => POST<IndustryListType>('/admin/industry/list');

export const getRoleList = (oldRoleId?: string) =>
  POST<RoleListType>('/client/role/list', { oldRoleId });

export const createRole = (data: CreateRoleParams) =>
  POST<CreateRoleType>(`/client/role/create`, data);

export const updateRole = (data: CreateRoleParams) =>
  POST<CreateRoleType>(`/client/role/update`, data);

export const createRoleMenu = (data: CreateRoleMenuParams) =>
  POST(`/client/role/menu/create`, data);

export const deleteRoleMenu = (id: string) => POST(`/client/role/delete`, { id });

export const getDetailRole = (id: string) => POST<DetailRoleType>(`/client/role/detail`, { id });

export const getRoleCountUser = (id: string) =>
  POST<RoleCountUserType>(`/client/role/count/user`, { id });

export const roleUpdateUser = (data: RoleUpdateUserParams) =>
  POST(`/client/role/update/user`, data);

export const getMenuTreeList = () => POST<MenuListType>(`/client/menu/tree`);

export const getApplicationList = () => POST<MenuListType>(`/client/tenant/scene/list`);

export const getRedirectPath = async (req: NextApiRequest, res: NextApiResponse) => {
  const redirectHomePathMap: Record<any, string> = {
    1: '/home/<USER>', // 普教
    6: '/home/<USER>', // 企业
    7: '/home/<USER>' // 职教
  };
  try {
    const tenantDetail = await getTenantDetail();
    const redirectPath = redirectHomePathMap[tenantDetail.industry] || '/home/<USER>';
    res.status(200).json({ redirectPath });
  } catch (error) {
    res.status(200).json({ redirectPath: '/home/<USER>' }); // 默认路径
  }
};
