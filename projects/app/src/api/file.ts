import { cloudFilePdf2txtObtainUrlType, FileMetaType } from '@/types/api/file';
import { ConfigType, POST } from '@/utils/request';
import { AxiosProgressEvent, AxiosResponse } from 'axios';

export const uploadFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType>('/system/file/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const uploadFilePublic = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType>('/system/file/public/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const uploadImageCompressionApi = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType>('/system/file/public/uploadImageAndCompression', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const uploadMultipleFiles = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType[]>('/system/file/batch/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const downloadFile = (
  fileKey: string,
  config?: { signal?: AbortSignal; onUploadProgress?: (progressEvent: AxiosProgressEvent) => void }
) =>
  POST<AxiosResponse>(
    '/system/file/download',
    { objectKey: fileKey },
    {
      timeout: 480000,
      responseType: 'blob',
      ...config
    }
  );
export const downloadPublicFile = (
  fileKey: string,
  config?: { signal?: AbortSignal; onUploadProgress?: (progressEvent: AxiosProgressEvent) => void }
) =>
  POST<AxiosResponse>(
    '/system/file/public/download',
    { objectKey: fileKey },
    {
      timeout: 480000,
      responseType: 'blob',
      ...config
    }
  );

export const cloudFilePdf2txtObtainUrl = (fileId: string) =>
  POST<cloudFilePdf2txtObtainUrlType>('/cloud/file/pdf2txt/obtainUrl', { fileId });

export const downloadFileByUrl = (
  url: string,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  } & ConfigType,
  params?: Record<string, any>
) =>
  POST<AxiosResponse>(url, params, {
    timeout: 480000,
    responseType: 'blob',
    ...config
  });

export const getFileList = (fileKeys: string[]) =>
  POST<FileMetaType[]>('/system/file/list', { fileKeys });

export const getFileMeta = (fileKey: string) =>
  POST<FileMetaType>('/system/file/detail', { objectKey: fileKey });
