import { InitDateResponse } from '@/fastgpt/global/common/api/systemRes';
import type { ExaminationTokenType, FastGPTDataType } from '@/types/api/system';
import { GET, POST } from '@/utils/request';

export const getSystemInitData = () => POST<InitDateResponse>('/system/init/data');

export const getSystemFastData = () => POST<FastGPTDataType>('/system/fast/data');
export const getExaminationToken = () => POST<ExaminationTokenType>('/system/examination/getToken');

export const getSystemConfig = () =>
  GET<Record<string, string>>('/api/config', {}, { baseURL: '/' });
