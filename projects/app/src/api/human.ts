import { HumanType } from '@/types/api/human';
import { POST, GET } from '@/utils/request';
import { getToken, getEnterpriseId } from '@/utils/humanAuth';
const MOOC_BASE_URL = '/huayun-mooc';

const API_BASE_URL = 'https://api-mp.x-era.com';

// 流式数智人音色查询
export const eraTimbresStream = (data: any) =>
  GET(
    '/openapi/v1/timbres/stream',
    { data: data, enterpriseId: getEnterpriseId() },
    {
      headers: {
        Authorization: getToken()
      },
      baseURL: API_BASE_URL
    }
  );

// 流式数智人查询
export const eraHumansStream = (data: any) =>
  GET(
    '/openapi/v1/humans/stream',
    { data: data, enterpriseId: getEnterpriseId() },
    {
      headers: {
        Authorization: getToken()
      },
      baseURL: API_BASE_URL
    }
  );

// 主动销毁实例(接口不可用)
export const eraDestroyStream = (data: any) =>
  POST('/v1/instance/destroy', data, {
    headers: {
      Authorization: getToken()
    },
    baseURL: API_BASE_URL
  });

// 数字人音色列表
export const timbresList = () => POST<any>('/client/digitalhuman/timbre/list', {});

// 数字人形象列表
export const imageList = () => POST<any>('/client/digitalhuman/image/list', {});

// 主动销毁视频流实例(防止并发1问题)
export const isDestroyInstance = (data: any) =>
  POST<any>('/client/digitalhuman/isDestroyInstance', data);

export const eraLogin = (data: any) => POST<HumanType>('/client/digitalhuman/oauthToken', data);

export const digitalhumanV1Instance = (data: any) =>
  POST<any>('/client/digitalhuman/v1/instance', data, { noToast: true });

// 获取当前环节对应的智能体ID
export const activityGetUsing = (data: any) =>
  POST<any>('/activity/get/using', data, {
    baseURL: MOOC_BASE_URL
  });

/** 把消息推到大屏上 */
export const studentSaveDiscussFromDigitalHuman = (data: any) =>
  POST<any>(
    `/student/saveDiscussFromDigitalHuman?text=${data.text}&groupId=${data.groupId}`,
    {},
    { baseURL: MOOC_BASE_URL }
  );

/** 获取阿里云语音转文字的token */
export const nlsToken = (data: any) => POST<any>(`/nls/token`, data);
