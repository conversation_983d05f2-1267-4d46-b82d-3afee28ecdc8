import { PagingData } from '@/types';
import {
  ResponseType,
  ClientUserValidType,
  UpdateUserProps,
  WxBindUserLoginParams,
  UserGetDeptType,
  UserDetailParams,
  UserDetailType,
  updateUserPhoneNumParams,
  UserDeptUserTreeType,
  SpaceTransferParams,
  ClientAuthResetPwdParams,
  CaptchaType,
  FirstLoginUpdatePwdParams,
  FirstLoginUpdatePwdResponse
} from '@/types/api/user';
import { GET, POST } from '@/utils/request';
import { hashStr } from '@/utils/string';
import { Toast } from '@/utils/ui/toast';

export const updateUser = (data: UpdateUserProps) =>
  POST(
    '/client/user/update',
    data.oldPassword && data.password
      ? { ...data, oldPassword: hashStr(data.oldPassword), password: hashStr(data.password) }
      : data
  );
export const getClientUserSmsCode = (
  bizType: number,
  mobile: string,
  newMobile?: string,
  ticket?: string,
  moveLength?: number
) => {
  let url = `/client/user/smsCode?bizType=${bizType}&mobile=${mobile}&ticket=${ticket}&moveLength=${moveLength}`;
  if (newMobile) {
    url += `&newMobile=${newMobile}`;
  }
  return POST<ResponseType>(url, '', { isResponseData: true });
};

export const getCaptcha = () => POST<CaptchaType>('/client/auth/getCaptcha');

export const getClientUserValid = (bizType: number, mobile: string, code: number) =>
  POST<ClientUserValidType[]>(
    `/client/user/valid?bizType=${bizType}&mobile=${mobile}&code=${code}`
  );

export const getWxBindUserLogin = (data: WxBindUserLoginParams) => POST(`/wx/bindUserLogin`, data);

export const getUpdateUserPhoneNum = (data: updateUserPhoneNumParams) =>
  POST(`/client/user/changePhoneNum`, data);

export const clientUserGetDept = () => GET<UserGetDeptType>(`/client/user/getDept`);

export const getClientUserDetail = (data: UserDetailParams) =>
  POST<UserDetailType>(`/client/user/detail`, data);

export const getClientTenantUserDeptUserTree = () =>
  POST<UserDeptUserTreeType[]>(`/client/tenant/user/deptUserTree`);

export const spaceTransfer = (data: SpaceTransferParams) => POST(`/cloud/space/transfer`, data);

// 校验密码更新是否超过90天
export const checkPasswordUpdate = () =>
  GET<boolean>('/client/user/validPwdUpdateTime').then((res) => {
    if (res) {
      Toast.info('您的密码超过90天未变更，为了保护账户安全，建议您更新密码哦～');
    }
    return res;
  });

export const setClientAuthResetPwd = ({ password, password1, ...data }: ClientAuthResetPwdParams) =>
  POST(`/client/auth/resetPwd`, {
    password: hashStr(password),
    password1: hashStr(password1),
    ...data
  });

/** 首次登录修改密码 */
export const setFirstLoginUpdatePwd = ({ password }: FirstLoginUpdatePwdParams) =>
  POST<FirstLoginUpdatePwdResponse>(`/client/user/first/update`, {
    password: hashStr(password)
  });
