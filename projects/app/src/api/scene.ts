import { AppListItemType } from '@/types/api/app';
import {
  SceneReorderParams,
  SceneType,
  SceneUpdateParams,
  tenantLabeParams,
  SubSceneType,
  TenantSceneNavbarType,
  TenantAppDetailParams,
  TenantAppDetailType,
  CommonAppType
} from '@/types/api/scene';
import { POST } from '@/utils/request';

export const getTenantSceneList = () => POST<SceneType[]>('/client/tenant/scene/getDisplayedList');
export const getTenantSceneListOfficialHome = () =>
  POST<SceneType[]>('/client/tenant/scene/webAppDisplayedList');

export const getAppCenterRecentlyUsedList = () =>
  POST<AppListItemType[]>('/client/app/center/recentlyUsedList');
export const getTenantLabelList = (data: tenantLabeParams) =>
  POST<SubSceneType[]>('client/tenant/label/list', data);

export const updateScene = (data: SceneReorderParams) => POST('/client/tenant/scene/update', data);

export const reorderScene = (data: SceneReorderParams) => POST('/core/scene/reorder', data);

export const deleteScene = (data: SceneUpdateParams) => POST(`/core/scene/delete`, data);

export const tenantAppDetail = (data: TenantAppDetailParams) =>
  POST<TenantAppDetailType>(`/client/tenant/app/detail`, data);

export const tenantSceneNavbar = () => POST<TenantSceneNavbarType[]>(`/client/tenant/scene/navbar`);

//判断应用是否关联工作流(个人中心)
export const getCenterValidAppWorkflow = (data: { id: string }) =>
  POST('/client/app/center/validAppWorkflow', data);

export const getAppCenterHomePageUsedList = () =>
  POST<AppListItemType[]>(`/client/app/center/homePageUsedList`);

export const getOtherAppList = () => POST<AppListItemType[]>(`/client/other/app/list`);

// 应用访问埋点
export const reportAppsVisit = (data: { tenantAppId: string }) =>
  POST('/client/activity/report/apps/visit', data);

// 文件上传埋点
export const reportCloudFileUpload = () => POST('/client/activity/report/cloud/file/upload', {});

// 设为常用应用
export const setCommonApp = (data: { id: string }) => POST('/tenant/user/common/app/create', data);

// 移除常用应用
export const rmCommonApp = (data: { id: string }) => POST('/tenant/user/common/app/remove', data);

// 常用应用排序
export const sortCommonApp = (data: { param: { id: string; sort: number }[] }) =>
  POST('/tenant/user/common/app/sort', data);

// 常用应用导航栏排序
export const sortNavCommonApp = (data: { param: { id: string; navbarOrder: number }[] }) =>
  POST('/tenant/user/common/app/navbarOrder', data);

// 常用应用列表
export const commonAppList = () => POST<CommonAppType[]>('/tenant/user/common/app/list');

// 添加到导航栏
export const addToNavbarCommonAppList = (data: { id: string | number }) =>
  POST('/tenant/user/common/app/addToNavbar', data);

// 从导航栏移除
export const removeFromNavbarCommonAppList = (data: { id: string | number }) =>
  POST('/tenant/user/common/app/removeFromNavbar', data);

// 导航栏应用列表
export const navbarCommonAppList = () =>
  POST<CommonAppType[]>('/tenant/user/common/app/navbarList');
