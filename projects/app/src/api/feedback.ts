import {
  DeleteFeedbackRequest,
  DictSublistRequest,
  DictSublistResponse,
  FeedbackDetailRequest,
  FeedbackDetailResponse,
  FeedbackPageRequest,
  FeedbackPageResponse,
  FeedbackSubmitRequest,
  FeedbackUpdateRequest
} from '@/types/api/feedback';
import { POST } from '@/utils/request';

/** 用户提交反馈 */
export const feedbackSubmit = (data: FeedbackSubmitRequest) =>
  POST<boolean>('/client/feedback/submit', data);

/** 用户修改反馈 */
export const feedbackUpdate = (data: FeedbackUpdateRequest) =>
  POST<boolean>('/client/feedback/update', data);

/** 反馈详情 */
export const feedbackDetail = (data: FeedbackDetailRequest) =>
  POST<FeedbackDetailResponse>('/client/feedback/detail', data);

/** 反馈类型列表 */
export const dictSublist = (data: DictSublistRequest) =>
  POST<DictSublistResponse[]>('/system/dict/sublist', data);

/** 反馈列表 */
export const feedbackPage = (data: FeedbackPageRequest) =>
  POST<FeedbackPageResponse>('/client/feedback/page', data);

/** 删除反馈 */
export const deleteFeedback = (data: DeleteFeedbackRequest) =>
  POST<boolean>('/client/feedback/delete', data);
