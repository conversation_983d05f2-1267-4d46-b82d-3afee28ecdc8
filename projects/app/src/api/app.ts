import { GET, POST } from '@/utils/request';

import { RequestPaging } from '@/types';
import { addDays } from 'date-fns';

import {
  AppLabelListParams,
  AppLabelDetailParams,
  CreateAppParams,
  DeleteAppParams,
  AppUpdateParams,
  AppLabelReorderParams,
  AppLabelType,
  AppLabelAppType,
  AppListItemType,
  AppListParams,
  transitionWorkflowBody,
  PostPublishAppProps,
  countChatInputGuideTotalQuery,
  countChatInputGuideTotalResponse,
  getLatestVersionQuery,
  getLatestVersionBody,
  getLatestVersionResponse,
  AppDetailInfo,
  CreateAppType,
  appCenterCopyParams,
  ClientAppFormDetailType,
  ClientAppFormCompletionsParams,
  SystemTenantGuideValidFinishType,
  ClientUseGuidanceDetailType,
  DynamicRadioFormListFromFieldParams,
  CloudFileGetContentStatusType
} from '@/types/api/app';
import { AppDetailType } from '@/fastgpt/global/core/app/type';
import {
  deleteTenantApp,
  getPersonalAppList,
  getTenantAppDetail,
  getTenantAppPage,
  publishTenantApp,
  tenantTransition2Workflow,
  updateTenantApp
} from './tenant/app';
import { GetPersonalAppListParams, TenantAppUpdateParams } from '@/types/api/tenant/app';
import { getListFromPage } from '@/utils/api';
import { ClientUseGuidanceType, DataSource } from '@/constants/common';

/**
 * 获取模型标签列表
 */
export const getAppLabelList = (data: AppLabelListParams) =>
  POST<AppLabelType[]>('/client/tenant/label/list', data);

/**
 * 获取模型标签列表(包含应用数量)
 */
export const getAppLabelAppList = (data: AppLabelListParams) =>
  POST<AppLabelAppType[]>('/client/app/center/label/listIncludeAppCount', data);

/**
 * 创建模型标签
 */
export const updateAppLabel = (data: AppLabelReorderParams) =>
  POST('/client/app/center/label/update', data);

/**
 * 删除模型标签
 */
export const deleteAppLabel = (data: AppLabelDetailParams) => POST('/core/app/label/delete', data);

/**
 * 获取模型列表
 */
export const getMyAppList = (data: AppListParams) =>
  POST<AppListItemType[]>('/client/app/center/list', data);

export const getTenantSceneListOfficialHome = (data: AppListParams) =>
  POST<AppListItemType[]>('/client/app/center/webAppList', data);

/**
 * 创建一个模型
 */
export const createApp = (data: CreateAppParams) =>
  POST<CreateAppType>('/client/app/center/create', data);

export const appCenterCopy = (data: appCenterCopyParams) => POST('/client/app/center/copy', data);

/* 共享市场 */
/**
 * 获取共享市场模型
 */
export const getShareModelList = (data: { searchText?: string } & RequestPaging) =>
  POST(`/core/app/share/getModels`, data);

/**
 * 收藏/取消收藏模型
 */
export const triggerModelCollection = (appId: string) =>
  POST<number>(`/core/app/share/collection?appId=${appId}`);

// ====================== data
export const getAppTotalUsage = (data: { appId: string }) =>
  POST<{ date: String; total: number }[]>(`/core/app/data/totalUsage`, {
    ...data,
    start: addDays(new Date(), -13),
    end: addDays(new Date(), 1)
  }).then((res) => (res.length === 0 ? [{ date: new Date(), total: 0 }] : res));

/**
 * 根据 ID 更新模型
 */
export const updateClientApp = (data: AppUpdateParams) => POST(`/client/app/center/update`, data);

// =================== chat logs
/**
 * 根据 ID 删除模型
 */
export const deleteClientApp = (data: DeleteAppParams) => POST(`/client/app/center/delete`, data);
/* detail */

export const getClientAppDetail = (id: string) =>
  POST<AppDetailInfo>('/client/app/center/detail', { id });

export const getAppDetailById = async (id: string, finalAppId: string, isAdmin: boolean) => {
  try {
    const [clientAppDetail, originAppDetail] = await Promise.all([
      getAppDetail(id, isAdmin),
      getOriginAppDetail(finalAppId)
    ]);

    return {
      ...clientAppDetail,
      ...originAppDetail
    };
  } catch (error) {
    console.error('Error fetching app details:', error);
    throw error;
  }
};
//  fastgpt相关接口
export const getAppLatestVersion = (data: getLatestVersionQuery) =>
  POST<getLatestVersionResponse>(`/system/fast/getAppVersionLatest`, data);

export const getCountChatInputGuideTotal = (data: countChatInputGuideTotalQuery) =>
  POST<countChatInputGuideTotalResponse>('/system/fast/getChatInputGuideCountTotal', data);
export const getOriginAppDetail = (id: string) =>
  POST<AppDetailType>(`/system/fast/getAppDetail`, { appId: id });

export const clientTransition2Workflow = (data: transitionWorkflowBody) =>
  POST<AppListItemType>('/client/app/center/transitionWorkflow', data);

export const getClientAppFormDetail = (id: string) =>
  POST<ClientAppFormDetailType>('/client/app/form/detail', { id });

export const dynamicRadioFormListFromField = (data: DynamicRadioFormListFromFieldParams) =>
  POST('/dynamic/radio/form/listFromField', data);

// 判断是否完成新功能指引
export const getSystemTenantGuideValidFinish = (type: ClientUseGuidanceType) =>
  POST<SystemTenantGuideValidFinishType>('/system/tenant/guide/validFinish', {
    guideType: type
  }).then((res) => {
    if (res) {
      return true;
    } else {
      return false;
    }
  });

// 完成新功能指引
export const setSystemTenantGuideFinish = (type: ClientUseGuidanceType) =>
  POST('/system/tenant/guide/finish', { guideType: type });

// 判断是否引导
export const getClientUseGuidanceDetail = (type: ClientUseGuidanceType) => {
  return POST<ClientUseGuidanceDetailType>('/client/use/guidance/detail', { guideType: type }).then(
    (res) => {
      if (res) {
        return true;
      } else {
        return false;
      }
    }
  );
};

// 完成指引
export const setClientUseGuidanceCreate = (type: ClientUseGuidanceType) =>
  POST('client/use/guidance/create', { type });

export const setCloudFileGetContentStatus = (fileKey: string) =>
  POST<CloudFileGetContentStatusType>('/cloud/file/getContentStatus', { fileKey });

// 首页使用智能体列表
export const getHomePageUsedList = (data: any) => POST('/client/app/center/homePageUsedList', data);

export const postTransition2Workflow = (data: transitionWorkflowBody, isAdmin: boolean) => {
  if (!isAdmin) {
    return clientTransition2Workflow(data);
  } else {
    return tenantTransition2Workflow(data).then((res) => {
      return {
        ...res
      } as AppListItemType;
    });
  }
};

export const updateApp = (data: AppUpdateParams & TenantAppUpdateParams, isAdmin: boolean) => {
  if (isAdmin) {
    return updateTenantApp(data);
  } else {
    return updateClientApp(data);
  }
};

export const deleteAppModel = (data: DeleteAppParams, isAdmin: boolean) => {
  if (isAdmin) {
    return deleteTenantApp(data);
  } else {
    return deleteClientApp(data);
  }
};

export const getAppDetail = (id: string, isAdmin: boolean) => {
  if (isAdmin) {
    return getTenantAppDetail(id);
  } else {
    return getClientAppDetail(id);
  }
};

export const publishApp = (data: AppUpdateParams & TenantAppUpdateParams, isAdmin: boolean) => {
  if (isAdmin) {
    return publishTenantApp(data);
  } else {
    return publishClientApp(data);
  }
};

export const publishClientApp = (data: AppUpdateParams) => POST(`/client/app/center/publish`, data);

/**
 * 获取应用列表
 */
export const getSelectAppList = async (
  data: GetPersonalAppListParams,
  source: DataSource,
  isTenantAdmin: boolean
) => {
  if (!isTenantAdmin) {
    return getMyAppList({});
  } else if (source == DataSource.Tenant) {
    return getListFromPage(getTenantAppPage, {}, 9999) as Promise<AppListItemType[]>;
  } else if (source == DataSource.Personal) {
    return getPersonalAppList(data) as Promise<AppListItemType[]>;
  }
};

// 获取指定应用列表
export const getAppointedAppList = (data: {}) =>
  POST<AppListItemType[]>('/client/app/center/appointedAppList', data);

// 一级模块埋点
export const trackAppNavItemClick = (data: { menuCode: string }) =>
  POST('/client/activity/report/menus/visit', data);
