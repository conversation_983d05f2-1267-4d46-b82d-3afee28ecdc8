import {
  ChatSearchDatasetProps,
  ChatSearchDatasetResponse,
  Document2MdProps,
  ParseResultProps
} from './../types/chat.d';
import { POST, DELETE } from '@/utils/request';
import { PagingData } from '@/types';
import {
  ClearHistoriesProps,
  DelHistoryProps,
  DeleteChatItemProps,
  InitChatProps,
  InitChatResponse,
  UpdateChatFeedbackProps,
  UpdateHistoryProps,
  getHistoriesProps,
  FileContentParams,
  FileContentType,
  ChatPptUrlParams,
  UpdateChatItemProps,
  CleanChatProps,
  InitChatSharingProps,
  UpdateChatItemBySharingProps,
  UpdateHistoryBySharingProps
} from '@/types/api/chat';
import { AxiosProgressEvent } from 'axios';
import { FileMetaType } from '@/types/api/file';
import { ChatHistoryItemType } from '@/fastgpt/global/core/chat/type';
import {
  ChatInputGuideProps,
  ChatInputGuideResponse,
  createInputGuideBody,
  createInputGuideResponse,
  deleteAllInputGuideBody,
  deleteInputGuideBody,
  updateInputGuideBody,
  FilesParseProps
} from '@/types/chat';
import { downloadFile } from '@/utils/file';
import { downloadFileByUrl } from './file';

/**
 * 获取初始化聊天内容
 */
export const getInitChatInfo = (data: InitChatProps) =>
  POST<InitChatResponse>(`/client/chat/new/init`, data);

export const getInitChatInfoBySharing = (data: InitChatSharingProps) =>
  POST<InitChatResponse>(`/client/sharing/app/chat/new/init`, data);

/**
 * get current window history(appid or shareId)
 */
export const getChatHistories = (data: getHistoriesProps) =>
  POST<PagingData<ChatHistoryItemType>>('/client/chat/page', data);

export const getChatHistoryPage = (data: getHistoriesProps) =>
  POST<PagingData<ChatHistoryItemType>>('/client/chat/history/page', data);
/**
 * delete one history
 */
export const delChatHistoryById = (data: DelHistoryProps) => POST(`/client/chat/delete`, data);
/**
 * clear all history by appid
 */
export const clearChatHistoryByAppId = (data: ClearHistoriesProps) =>
  DELETE(`/core/chat/clearHistories`, data);

/**
 * delete one chat record
 */
export const delChatRecordById = (data: DeleteChatItemProps) =>
  POST<number>(`/client/chat/item/delete`, data);

/**
 * 修改历史记录: 标题/置顶
 */
export const chatUpdate = (data: UpdateHistoryProps) => POST('/client/chat/update', data);
export const chatUpdateBySharing = (data: UpdateHistoryBySharingProps) =>
  POST('/client/sharing/app/chat/update', data);

export const cleanChat = (data: CleanChatProps) => POST('/client/chat/clean', data);

export const updateChatItem = (data: UpdateChatItemProps) => POST('/client/chat/item/update', data);

export const updateChatItemBySharing = (data: UpdateChatItemBySharingProps) =>
  POST('/client/sharing/app/chat/item/update', data);

/* -------------- feedback ------------ */
export const updateChatUserFeedback = (data: UpdateChatFeedbackProps) =>
  POST('/client/chat/update/user/feedback', data);

export const getChatFileContent = (data: FileContentParams) =>
  POST<FileContentType>(`/client/chat/analysis/file/content`, data);

export const uploadChatFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType>('/system/file/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const fetchChatPptUrl = (data: ChatPptUrlParams) => POST(`/client/chat/get/ppt/url`, data);

/**
 * Convert HTML to Markdown
 */
export const convertHtmlToMd = (data: { filename: string; htmlStr: string }) =>
  POST<Blob>('/system/convert/html2md', data, {
    responseType: 'blob'
  });

/**
 * Convert HTML to PDF
 */
export const convertHtmlToPdfOld = (data: { filename: string; htmlStr: string }) =>
  downloadFile('/system/convert/html2pdf', {}, data);

export const covertHtmlToMarkdown = (data: { htmlStr: string }) =>
  POST<string>('/huayun-tool/convert/html2markdown', data, {
    baseURL: '/'
  });

/**
 * Convert HTML to Word
 */
export const convertHtmlToWord = (data: { filename: string; htmlStr: string }) => {
  return downloadFile(
    '/huayun-tool/convert/html2word',
    {
      baseURL: '/'
    },
    {
      filename: data.filename,
      htmlStr: data.htmlStr
    }
  );
};

export const convertMdToHtml = (data: { htmlStr: string }) =>
  POST<string>('/huayun-tool/convert/markdown2html', data, {
    baseURL: '/'
  });

export const convertHtmlToPdf = (data: { filename: string; htmlStr: string }) =>
  downloadFile(
    '/huayun-tool/convert/html2pdf',
    {
      baseURL: '/'
    },
    {
      filename: data.filename,
      htmlStr: data.htmlStr
    }
  );

export const convertWordToHtml = (data: FormData) =>
  POST<string>('/huayun-tool/convert/word2html', data, {
    baseURL: '/',
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const postChatInputGuides = (data: createInputGuideBody) =>
  POST<createInputGuideResponse>(`/system/fast/createChatInputGuide`, data);
export const putChatInputGuide = (data: updateInputGuideBody) =>
  POST(`/system/fast/updateChatInputGuide`, data);
export const delChatInputGuide = (data: deleteInputGuideBody) =>
  POST(`/system/fast/delChatInputGuide`, data);
export const delAllChatInputGuide = (data: deleteAllInputGuideBody) =>
  POST(`/system/fast/delAllChatInputGuide`, data);

export const getChatInputGuideList = (data: ChatInputGuideProps) =>
  POST<ChatInputGuideResponse>(`/system/fast/getChatInputGuideList`, data);
export const parseCurrentFiles = (data: FilesParseProps) =>
  POST<ParseResultProps | null>(`/client/chat/filesParse`, data, {
    noToast: true
  });

export const parseTemplateFile = (data: Document2MdProps) =>
  POST<{ ocrFileContent: string }>(`/system/convert/document2md`, data, {
    noToast: true
  });

export const parseUploadFile = (data: { fileKey: string }) =>
  POST<{ fileUrl: string; fileContent: string }>(`/system/convert/fileAnalysis`, data, {
    noToast: true
  });

// export const convertHtmlToWord = (data: {
//   filename: string;
//   htmlStr: string;
//   markdownStr?: string;
//   fileKey?: string;
// }) => POST<{ fileUrl: string }>('/system/convert/html2word/obtainUrl', data);

// 应用生产文档埋点
export const reportAppsProduceDocument = (data: { tenantAppId: string }) =>
  POST('/client/activity/report/apps/produce/document', data);

export const chatSearchDataset = (data: ChatSearchDatasetProps) =>
  POST<ChatSearchDatasetResponse[]>('/client/chat/searchDataset', data);

// 转markdown
export const convertMarkdown = (data: {
  messages: { dataId: string; content: string; role: string }[];
}) => POST('/client/chat/getMarkdownMindMap', data);
