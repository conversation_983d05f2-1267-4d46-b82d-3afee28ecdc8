import { batchEditPrivilegeTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { PagingData } from '@/types';
import {
  AddSpaceProps,
  AuditorType,
  FileType,
  SpaceType,
  GetSpaceFilePageProps,
  GetSpaceListProps,
  SortSpaceProps,
  UpdateSpaceProps,
  AddSpaceFolderProps,
  UpdateSpaceFolderProps,
  CloudFileDetailRequest,
  CloudFileDetailResponse,
  CloudFileVersionRequest,
  CloudFileVersionResponse,
  CloudFileShareRequest,
  CloudFileShareResponse,
  CloudSpaceShareRequest,
  CloudSpaceShareResponse,
  CreateLabelRequest,
  CreateLabelResponse,
  ListLabelsRequest,
  ListLabelsResponse,
  SetLabelRequest,
  SetLabelResponse,
  GetSetLabelsRequest,
  GetSetLabelsResponse,
  BatchRecoveryParams,
  AuditRequestProps,
  GetFoldVersionFilePageProps,
  RecycleSubListPageType,
  RecyclePageType,
  CloudNoticeListPageType,
  CloudNoticeListPageParams,
  AuditCountType,
  AuditListType,
  AuditListParams,
  FolderMixBatchDeleteParams,
  DoAuditBatchParams,
  AuditSubListParams,
  CreateTenantUploadFolderProps,
  FinishTenantUploadFolderProps,
  CreateMyUploadFolderProps,
  FinishMyUploadFolderProps,
  RemoveSpaceFileBatchProps,
  SpaceAuditorAddParams,
  SpaceShareAddParams,
  AddMyFolderProps,
  GetMyFilePageProps,
  RemoveMyFileBatchProps,
  RenameMyFolderProps,
  NoticeNotReadNumType,
  ShareUpdatePrivilegeParams,
  ShareDeleteParams,
  FileShareAddParams,
  FilesMixFolderShareParams,
  SpaceAuditorListParams,
  GetDownloadSizeProps,
  DownloadSizeType,
  CreateDownloadZipProps,
  DownloadRecordType,
  GetDownloadRecordPageProps,
  GetDownloadActiveCountProps,
  DownloadActiveCountType,
  CreateDownloadRecordProps,
  UpdateDownloadRecordProps,
  GetMyFlatFilePageProps,
  UploadCloudFileProps,
  UpdateSpaceParentProps,
  AuditCountParams,
  CloudRecyclePageProps,
  CloudRecycleSubListPageProps,
  MyFlatFileType,
  SpaceAuditorListType,
  GetUsageStatsProps,
  UpdateFileParentProps,
  GetSpaceFileViewPageProps,
  GetSpaceViewListProps,
  FileSearchHistoryType,
  FileGlobalListPageParams,
  FileSearchMediaWordParams,
  FileSearchMediaWordType,
  SpaceShareParentCloudSpaceListType,
  CloudBatchEditPrivilegeParams,
  SpaceAuditSwitchUpdateParams,
  Update2DatasetParams,
  CloudSpaceAuditSwitchDetailType,
  UpdateFolderParentProps,
  UploadTaskInfo,
  MergeFilePartsResponse,
  BatchUpdateParentProps,
  SpacePrimaryListProps,
  SpaceTreeListType
} from '@/types/api/cloud';
import { POST } from '@/utils/request';
import { AxiosProgressEvent } from 'axios';

const processFiles = <
  T extends { fileType: FileTypeEnum; id: string; fileName: string; rowKey?: string }
>(
  files: T[]
) => {
  files.forEach((it) => {
    it.rowKey = `${it.fileType}-${it.id}`;
    if (!it.fileName) {
      it.fileName = (it as any).spaceName || (it as any).folderName;
    }
  });
  return files;
};

const processFilePage = <
  T extends { fileType: FileTypeEnum; id: string; fileName: string; rowKey?: string }
>(
  page: PagingData<T>
) => {
  processFiles(page.records);
  return page;
};

// 包括权限的空间列表，用于当前用户
export const getSpaceList = (data: GetSpaceListProps) =>
  POST<SpaceType[]>('/cloud/space/subList', data);

export const getFolderList = (data: GetSpaceListProps) =>
  POST<SpaceType[]>('/cloud/folder/subList', data);

export const getSpaceViewList = (data: GetSpaceViewListProps) =>
  POST<SpaceType[]>('/cloud/space/subViewList', data);

// 不包括权限的空间树，用于管理员选择空间
export const getSpaceTree = () => POST<SpaceType[]>('/cloud/space/fullTreeList', {});

// 添加空间
export const addSpace = (data: AddSpaceProps) => POST('/cloud/space/add', data);

// 更新空间
export const updateSpace = (data: UpdateSpaceProps) => POST('/cloud/space/update', data);

export const updateSpaceParent = (data: UpdateSpaceParentProps) =>
  POST('/cloud/space/updateParent', data);

export const batchUpdateParent = (data: BatchUpdateParentProps) =>
  POST('/cloud/file/batchUpdateParent', data);

// 删除空间
export const removeSpace = (id: string) => POST('/cloud/space/delete', { id });

// 空间排序
export const sortSpace = (data: SortSpaceProps) => POST('/cloud/space/reSort', data);

export const getSpaceAuditorIds = (spaceId: string) =>
  POST<AuditorType[]>('/cloud/space/auditor/getAuditorIdsBySpace', { spaceId });

// 获取所有空间审核人员
export const getAuditorList = () => POST<AuditorType[]>('/cloud/space/auditor/list');

export const getSpaceFilePage = (data: GetSpaceFilePageProps) =>
  POST<PagingData<FileType>>('/cloud/space/folder/subListPage', data).then(processFilePage);

export const getSpaceFilePages = (data: GetSpaceFilePageProps) =>
  POST<PagingData<FileType>>('/cloud/space/folder/subListPages', data).then(processFilePage);

export const getSpacePrimaryList = (data: SpacePrimaryListProps) =>
  POST<PagingData<FileType>>('/cloud/space/primaryList', data).then(processFilePage);

export const getSpaceFileViewPage = (data: GetSpaceFileViewPageProps) =>
  POST<PagingData<FileType>>('/cloud/space/folder/subViewListPage', data).then(processFilePage);

export const removeSpaceFileBatch = (data: RemoveSpaceFileBatchProps) =>
  POST('/cloud/space/folder/mixBatchDelete', data);

export const addSpaceFolder = (data: AddSpaceFolderProps) => POST('/cloud/space/folder/add', data);

export const updateSpaceFolder = (data: UpdateSpaceFolderProps) =>
  POST('/cloud/space/folder/update', data);

export const addMyFolder = (data: AddMyFolderProps) => POST('/cloud/folder/add', data);

export const removeMyFileBatch = (data: RemoveMyFileBatchProps) =>
  POST('/cloud/folder/mixBatchDelete', data);

export const renameMyFolder = (data: RenameMyFolderProps) => POST('/cloud/folder/rename', data);

export const getMyFilePage = (data: GetMyFilePageProps) =>
  POST<PagingData<FileType>>('/cloud/folder/subListPage', data).then(processFilePage);

export const getMyFlatFilePage = (data: GetMyFlatFilePageProps) =>
  POST<PagingData<MyFlatFileType>>('/cloud/file/listPageByMyself', data).then(processFilePage);

export const uploadMyFileToCloud = (data: UploadCloudFileProps) =>
  POST('/cloud/file/addFromMyself', data);

// 获取回收站列表
export const getCloudRecyclePage = (data: CloudRecyclePageProps) =>
  POST<PagingData<RecyclePageType>>('/cloud/recycle/page', data).then(processFilePage);

// 查看
export const getCloudRecycleSubListPage = (data: CloudRecycleSubListPageProps) =>
  POST<PagingData<RecycleSubListPageType>>('/cloud/recycle/subListPage', data).then(
    processFilePage
  );

// 彻底删除
export const cloudRecycleDelete = (id: string) => POST('/cloud/recycle/delete', { id });

// 恢复
export const cloudRecycleRecovery = (id: string) => POST('/cloud/recycle/recovery', { id });

// 清空回收站
export const cloudRecycleClearAll = () => POST('/cloud/recycle/clearAll');

// 批量删除
export const cloudRecycleBatchDelete = (data: BatchRecoveryParams[]) =>
  POST('/cloud/recycle/batchDelete', data);

// 批量恢复
export const cloudRecycleBatchRecovery = (data: BatchRecoveryParams[]) =>
  POST('/cloud/recycle/batchRecovery', data);

// 网盘通知接口
export const getCloudNoticeListPage = (data: CloudNoticeListPageParams) =>
  POST<PagingData<CloudNoticeListPageType>>('/cloud/notice/listPage', data);

export const uploadFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST('/cloud/file/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });
export const uploadByHtmlStr = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST('/cloud/file/uploadByHtmlStr', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const updateUploadFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST('/cloud/file/updateUpload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

export const copyFile = (id: string) => POST('/cloud/file/copy', { id });

export const copyFileBatch = (ids: string[]) => POST('/cloud/file/batchCopy', { ids });

export const updateFileParent = (data: UpdateFileParentProps) =>
  POST('/cloud/file/updateParent', data);

export const updateFolderParent = (data: UpdateFolderParentProps) =>
  POST('/cloud/folder/updateParent', data);

export const getUsageStats = (data: GetUsageStatsProps) =>
  POST<number>('/cloud/file/statisFileSize', data);

export const createTenantUploadFolder = (data: CreateTenantUploadFolderProps) =>
  POST<string>('/cloud/space/folder/upload/create', data);

export const finishTenantUploadFolder = (data: FinishTenantUploadFolderProps) =>
  POST('/cloud/space/folder/upload/finish', data);

export const cancelTenantUploadFolder = (id: string) =>
  POST('/cloud/space/folder/upload/cancel', { id });

export const createMyUploadFolder = (data: CreateMyUploadFolderProps) =>
  POST<string>('/cloud/folder/upload/create', data);

export const finishMyUploadFolder = (data: FinishMyUploadFolderProps) =>
  POST('/cloud/folder/upload/finish', data);

export const cancelMyUploadFolder = (id: string) => POST('/cloud/folder/upload/cancel', { id });

export const getDownloadSize = (data: GetDownloadSizeProps) =>
  POST<DownloadSizeType>('/cloud/space/download/countFileSize', data);

export const createDownloadZip = (data: CreateDownloadZipProps) =>
  POST<DownloadRecordType>('/cloud/space/download/zip', data);

export const getDownloadActiveCount = (data: GetDownloadActiveCountProps) =>
  POST<DownloadActiveCountType>('/cloud/transmission/count', data);

export const createDownloadRecord = (data: CreateDownloadRecordProps) =>
  POST<DownloadRecordType>('/cloud/transmission/create', data);

export const updateDownloadRecord = (data: UpdateDownloadRecordProps) =>
  POST('/cloud/transmission/update', data);

export const removeDownloadRecord = (id: string) => POST('/cloud/transmission/delete', { id });

export const getDownloadRecordPage = (data: GetDownloadRecordPageProps) =>
  POST<PagingData<DownloadRecordType>>('/cloud/transmission/page', data);

// 获取文件详情
export const getFileDetail = (data: CloudFileDetailRequest) =>
  POST<CloudFileDetailResponse>('/cloud/file/detail', data);

// 获取文件历史版本
export const getFileVersionList = (data: CloudFileVersionRequest) =>
  POST<CloudFileVersionResponse[]>('/cloud/file/version/list', data);

export const getFoldVersionFilePage = (data: GetFoldVersionFilePageProps) =>
  POST<PagingData<CloudFileVersionResponse>>('/cloud/file/version/subListPage', data);

// 获取文件共享列表
export const getFileShareList = (data: CloudFileShareRequest) =>
  POST<CloudFileShareResponse[]>('/cloud/file/share/list', data);

// 获取空间共享列表
export const getSpaceShareList = (data: CloudSpaceShareRequest) =>
  POST<CloudSpaceShareResponse[]>('/cloud/space/share/list', data);

// 创建标签
export const createLabel = (data: CreateLabelRequest) =>
  POST<CreateLabelResponse>('/cloud/label/create', data);

// 标签列表
export const listLabels = (data: ListLabelsRequest) =>
  POST<ListLabelsResponse>('/cloud/label/list', data);

// 设置标签
export const setLabel = (data: SetLabelRequest) => POST<SetLabelResponse>('/cloud/label/set', data);

// 获取已设置标签列表
export const getSetLabels = (data: GetSetLabelsRequest) =>
  POST<GetSetLabelsResponse>('/cloud/label/set/list', data);

// 审批
export const doAudit = (data: AuditRequestProps) => POST<{}>('/cloud/space/audit/doAudit', data);

export const getAuditCount = (data: AuditCountParams) =>
  POST<AuditCountType>('/cloud/space/audit/count', data);

export const getAuditList = (data: AuditListParams) =>
  POST<AuditListType[]>('/cloud/space/audit/list', data);

export const getAuditSubList = (data: AuditSubListParams) =>
  POST<AuditListType[]>('/cloud/space/audit/subList', data);

export const folderMixBatchDelete = (data: FolderMixBatchDeleteParams) =>
  POST('/cloud/space/folder/mixBatchDelete', data);

export const getAuditDoAuditBatch = (data: DoAuditBatchParams) =>
  POST<AuditCountType>('/cloud/space/audit/doAudit/batch', data);

export const spaceAuditorAdd = (data: SpaceAuditorAddParams) =>
  POST('/cloud/space/auditor/add', data);

export const spaceAuditorDeleteByUser = (tmbId: string) =>
  POST('/cloud/space/auditor/deleteByUser', { tmbId });

export const getSpaceAuditorList = (data: SpaceAuditorListParams) =>
  POST<SpaceAuditorListType[]>('/cloud/space/auditor/list', data);

export const spaceAuditorUpdate = (data: SpaceAuditorAddParams) =>
  POST('/cloud/space/auditor/update', data);

// 获取未读数量
export const getNoticeNotReadNum = () => POST<NoticeNotReadNumType>('/cloud/notice/notReadNum');

// 标记为已读
export const setNoticeDoRead = () => POST('/cloud/notice/doRead');

export const spaceShareAdd = (data: SpaceShareAddParams) => POST('/cloud/space/share/addNew', data);

export const spaceShareDelete = (data: ShareDeleteParams) =>
  POST('/cloud/space/share/delete', data);

export const getSpaceShareUpdatePrivilege = (data: ShareUpdatePrivilegeParams) =>
  POST('/cloud/space/share/updatePrivilege', data);

export const fileShareAdd = (data: FileShareAddParams) => POST('/cloud/file/share/addNew', data);

export const fileShareDelete = (data: ShareDeleteParams) => POST('/cloud/file/share/delete', data);

export const fileShareUpdatePrivilege = (data: ShareUpdatePrivilegeParams) =>
  POST('/cloud/file/share/updatePrivilege', data);

export const getFileShareMixFolderShare = (data: FilesMixFolderShareParams) =>
  POST('/cloud/space/share/mixFolderShare', data);

export const fileClearSearchHistory = () => POST<boolean>('/cloud/file/clearSearchHistory');

export const getFileSearchHistory = () =>
  POST<FileSearchHistoryType[]>('/cloud/file/getSearchHistory');

export const getFileGlobalListPage = (data: FileGlobalListPageParams) =>
  POST<PagingData<FileType>>('/cloud/file/globalListPage', data).then(processFilePage);

export const fileSearchMediaWord = (data: FileSearchMediaWordParams) =>
  POST<FileSearchMediaWordType>('/cloud/file/searchMediaWord', data);

export const spaceShareParentCloudSpaceList = (data: { spaceId: string }) =>
  POST<SpaceShareParentCloudSpaceListType[]>('/cloud/space/share/parentCloudSpaceList', data);

export const cloudFileRename = (data: { id: string; fileName: string }) =>
  POST('/cloud/file/rename', data);

export const cloudBatchEditPrivilege = (data: CloudBatchEditPrivilegeParams) =>
  POST('/cloud/space/share/batchEditPrivilege', data);

export const fileShareBatchEditPrivilege = (data: CloudBatchEditPrivilegeParams) =>
  POST('/cloud/file/share/batchEditPrivilege', data);

export const cloudSpaceAuditSwitchDetail = () =>
  POST<CloudSpaceAuditSwitchDetailType>('/client/cloud/space/audit/switch/detail');

export const spaceAuditSwitchUpdate = (data: SpaceAuditSwitchUpdateParams) =>
  POST('/client/cloud/space/audit/switch/update', data);

// 将数据空间文件导入知识库
export const update2Dataset = (data: Update2DatasetParams) =>
  POST<boolean>('/cloud/file/update2Dataset', data);
export const getSpaceAuditInfo = (data: { id: string }) => POST<any>('/cloud/space/detail', data);

// 分片上传文件
export const uploadPart = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST('/system/file/uploadPart', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });

// 获取上传进度
export const getUploadTaskInfo = (data: { fileKey: string }) =>
  POST<UploadTaskInfo>('/system/file/taskInfo', data);

// 合并分片
export const mergeFileParts = (data: { fileKey: string; fileName: string }) =>
  POST<MergeFilePartsResponse>('/system/file/merge', data);

// 初始化上传任务
export const initUploadTask = (data: { fileName: string }) =>
  POST<UploadTaskInfo>('/system/file/initTask', data);

export const getCloudChatReferenceFiles = () => POST<any>('/client/activity/report/cloud/chat/use');

export const getEditorCloudReference = () => POST<any>('/client/activity/report/editor/cloud');

export const saveEditorToCloud = () => POST<any>('/client/activity/report/editor/cloud/save');

export const saveEditorToCloudTemporarily = (data: {
  id?: string | number;
  title: string;
  fileJson: string;
}) =>
  POST<{
    id?: string;
    updateTime: string;
  }>('/client/tenant/user/temporarily/save', data);

export interface TemporarilyDetailResponse {
  id: string;
  tenantId: string;
  tmbId: string;
  title: string;
  fileJson: string;
  isDeleted: boolean;
  createTime: string;
  updateTime: string;
}

export const getTemporarilyDetail = (data: { id: string }) =>
  POST<TemporarilyDetailResponse>('/client/tenant/user/temporarily/detail', data);

export interface TemporarilyListResponse {
  id: string;
  tenantId: string;
  tmbId: string;
  title: string;
  fileJson: string;
  isDeleted: boolean;
  createTime: string;
  updateTime: string;
}

export const getTemporarilyList = () =>
  POST<TemporarilyListResponse[]>('/client/tenant/user/temporarily/list');

//判断文件是否在数据空间中
export const isSpaceFile = (data: { fileKeys: string[] }) =>
  POST<string>('/cloud/file/isSpaceFile', data);

// 获取空间树
export const getSpaceTreeList = () => POST<SpaceTreeListType[]>('/cloud/space/treeList');
// 文件下载埋点
export const reportCloudFileDownload = () =>
  POST('/client/activity/report/cloud/file/download', {});
