import { IndactorTypeEnum, IndactorTypeNameMap } from '@/constants/api/tenant/evaluate/process';
import { EvaluateType } from '@/constants/api/tenant/evaluate/rule';
import { PagingData } from '@/types';
import {
  AddClazzEvaluateProps,
  AddHomeworkProps,
  EvaluateClazzType,
  EvaluateGroupType,
  EvaluateHomeworkType,
  EvaluateIndactorType,
  EvaluateStudentType,
  EvaluateSubjectType,
  GetClazzIndactorListProps,
  GetClazzStudentListByCodeProps,
  GetClazzStudentListByGroupProps,
  GetClazzStudentListBySeatProps,
  GetGroupListProps,
  GetHomeworkIndactorListProps,
  GetHomeworkPageProps,
  GetHomeworkStudentListByCodeProps,
  GetHomeworkStudentListBySeatProps,
  RemoveClazzEvaluateProps,
  RemoveHomeworkProps,
  ResetClazzEvaluateProps,
  ResetHomeworkEvaluateProps,
  SubmitGroupsProps,
  UpdateHomeworkProps,
  SubmitSeatsProps,
  AddHomeworkEvaluateProps,
  RemoveHomeworkEvaluateProps,
  GetClazzStudentPageProps,
  GetAllGroupListProps,
  ClazzEvaluateStatType,
  HomeworkEvaluateStatType,
  GetClazzEvaluateStatsProps,
  GetClazzEvaluateRecordPageProps,
  GetClazzEvaluateRankListProps,
  GetClazzEvaluateIndactorStatListProps,
  ClazzEvaluateRecordType,
  GetHomeworkListProps,
  GetHomeworkStudentsListByClazzProps,
  GetHomeworkRecordListByHomeworkIdProps,
  GetHomeworkRecordListByStudentIdProps,
  GetEvaluateSubjectListProps
} from '@/types/api/tenant/evaluate/process';
import { POST } from '@/utils/request';

const asArray = <T>(arr: T[]) => arr || ([] as T[]);

const asClazzEvaluates = <
  T extends {
    clazzEvaluaStatises?: ClazzEvaluateStatType[];
    evaluaStatises?: ClazzEvaluateStatType[];
    good?: string;
    bad?: string;
  }
>(
  data: T[]
) => {
  data.forEach((item) => {
    const stats = item.clazzEvaluaStatises || item.evaluaStatises;
    item.good = `${stats?.find((it) => it.indactorName === IndactorTypeNameMap[IndactorTypeEnum.Good])?.score || 0}`;
    item.bad = `${stats?.find((it) => it.indactorName === IndactorTypeNameMap[IndactorTypeEnum.Bad])?.score || 0}`;
  });
  return data;
};

const asHomeworkEvaluates = <
  T extends { homeworkEvaluas?: HomeworkEvaluateStatType[]; score?: string }
>(
  data: T[]
) => {
  data.forEach((item) => {
    const stats = item.homeworkEvaluas;
    if (stats?.length) {
      const stat = stats[stats.length - 1];
      item.score = `${stat.evaluateType === EvaluateType.Grade ? stat.scoreLevelValue : stat.score}`;
    } else {
      item.score = '0';
    }
  });
  return data;
};

export const getEvaluateSubjectList = (data: GetEvaluateSubjectListProps) =>
  POST<EvaluateSubjectType[]>('/evalua/classEvaluate/getSubjectsByTeacher', data).then(asArray);

export const getClazzStudentPage = (data: GetClazzStudentPageProps) =>
  POST<PagingData<EvaluateStudentType>>('/client/student/page', data);

// 课堂班级
export const getClazzEvaluateClazzList = () =>
  POST<EvaluateClazzType[]>('/schoolDept/manage/getClazzsOfKTBXByTeacher').then((res) => {
    res?.forEach((it) => {
      it.clazzName = `${it.parentName}${it.deptName}`;
      let good = 0;
      let bad = 0;
      it.clazzEvaluaStatises?.forEach((it) => {
        if (it.indactorName === IndactorTypeNameMap[IndactorTypeEnum.Good]) {
          good += it.score;
        } else {
          bad += it.score;
        }
      });
      it.good = good > 0 ? `+${good}` : `${good}`;
      it.bad = bad > 0 ? `+${bad}` : `${bad}`;
    });
    return res || [];
  });

export const submitSeats = (data: SubmitSeatsProps) => POST('/clazz/student/doSeat', data);

export const getAllGroupList = (data: GetAllGroupListProps) =>
  POST<EvaluateGroupType[]>('/clazz/student/listAllByGroup', data).then(asArray);

export const getGroupList = (data: GetGroupListProps) =>
  POST<EvaluateGroupType[]>('/clazz/student/groupList', data).then(asArray).then(asClazzEvaluates);

export const submitGroups = (data: SubmitGroupsProps) => POST('/clazz/student/doGroup', data);

export const getClazzStudentListByCode = (data: GetClazzStudentListByCodeProps) =>
  POST<EvaluateStudentType[]>('/clazz/student/listByNo', data).then(asArray).then(asClazzEvaluates);

export const getClazzStudentListBySeat = (data: GetClazzStudentListBySeatProps) =>
  POST<EvaluateStudentType[][]>('/clazz/student/listBySeat', data)
    .then((res) => res?.flatMap((it) => it)?.filter((it) => it.id) || [])
    .then(asClazzEvaluates);

export const getClazzStudentListByGroup = (data: GetClazzStudentListByGroupProps) =>
  POST<EvaluateStudentType[]>('/clazz/student/listByGroup', data)
    .then(asArray)
    .then(asClazzEvaluates);

export const getClazzIndactorList = (data: GetClazzIndactorListProps) =>
  POST<EvaluateIndactorType[]>('/evalua/classEvaluate/getIndactors', data)
    .then(asArray)
    .then((res) => {
      const list: EvaluateIndactorType[] = [];
      [IndactorTypeEnum.Good, IndactorTypeEnum.Bad].forEach((indactorType) => {
        const indactorName = IndactorTypeNameMap[indactorType];
        const indactor = res.find((it) => it.indactorName === indactorName);
        const subs = indactor?.subs?.filter((it) => it.score);
        if (!subs?.length) {
          return;
        }
        subs.forEach((it) => {
          it.indactorType = indactorType;
        });
        list.push({ ...indactor!, subs });
      });
      return list;
    });

export const addClazzEvaluate = (data: AddClazzEvaluateProps) =>
  POST('/evalua/classEvaluate/doEvaluate', data);

export const removeClazzEvaluate = (data: RemoveClazzEvaluateProps) =>
  POST('/evalua/classEvaluate/delete', data);

export const resetClazzEvaluate = (data: ResetClazzEvaluateProps) =>
  POST('/evalua/classEvaluate/refresh', data);

export const getClazzEvaluateStats = (data: GetClazzEvaluateStatsProps) =>
  POST<ClazzEvaluateStatType[]>('/evalua/classEvaluate/clazzView/scoreStatis', data).then(asArray);

export const getClazzEvaluateIndactorStatList = (data: GetClazzEvaluateIndactorStatListProps) =>
  POST<ClazzEvaluateStatType[]>('/evalua/classEvaluate/clazzView/indactorScoreStatis', data).then(
    asArray
  );

export const getClazzEvaluateRecordPage = (data: GetClazzEvaluateRecordPageProps) =>
  POST<PagingData<ClazzEvaluateRecordType>>('/evalua/classEvaluate/clazzView/scoreListPage', data);

export const getClazzEvaluateRankList = (data: GetClazzEvaluateRankListProps) =>
  POST<ClazzEvaluateRecordType[]>('/evalua/classEvaluate/clazzView/studentScoreRank', data);

// 作业班级
export const getHomeworkEvaluateClazzList = () =>
  POST<EvaluateClazzType[]>('/schoolDept/manage/getClazzsOfZYPJByTeacher').then((res) => {
    res?.forEach((it) => {
      it.clazzName = `${it.parentName}${it.deptName}`;
    });
    return res || [];
  });

export const addHomework = (data: AddHomeworkProps) => POST('/evalua/homework/add', data);

export const updateHomework = (data: UpdateHomeworkProps) => POST('/evalua/homework/update', data);

export const removeHomeowk = (data: RemoveHomeworkProps) => POST('/evalua/homework/delete', data);

export const getHomeworkPage = (data: GetHomeworkPageProps) =>
  POST<PagingData<EvaluateHomeworkType>>('/evalua/homework/listPage', data);

export const getHomeworkList = (data: GetHomeworkListProps) =>
  POST<EvaluateHomeworkType[]>('/evalua/homework/getListByGradeAndSubject', data);

export const getHomeworkStudentListByClazz = (data: GetHomeworkStudentsListByClazzProps) =>
  POST<EvaluateStudentType[]>('/evalua/homework/getListByClazz', data).then(asArray);

export const getHomeworkStudentListByCode = (data: GetHomeworkStudentListByCodeProps) =>
  POST<EvaluateStudentType[]>('/clazz/student/listByNoOfHomework', data)
    .then(asArray)
    .then(asHomeworkEvaluates);

export const getHomeworkStudentListBySeat = (data: GetHomeworkStudentListBySeatProps) =>
  POST<EvaluateStudentType[][]>('/clazz/student/listBySeatOfHomework', data)
    .then(asArray)
    .then((res) => res.flatMap((it) => it).filter((it) => it.id))
    .then(asHomeworkEvaluates);

export const getHomeworkIndactorList = (data: GetHomeworkIndactorListProps) =>
  POST<EvaluateIndactorType[]>('/evalua/homeworkEvaluate/getIndactors', data)
    .then(asArray)
    .then((res) =>
      res.filter(
        (it) => it.evaluateType == EvaluateType.Grade || it.evaluateType == EvaluateType.Score
      )
    );

export const addHomeworkEvaluate = (data: AddHomeworkEvaluateProps) =>
  POST<{ id: string; name: string }[]>('/evalua/homeworkEvaluate/doEvaluate', data);

export const removeHomeworkEvaluate = (data: RemoveHomeworkEvaluateProps) =>
  POST('/evalua/homeworkEvaluate/delete', data);

export const resetHomeworkEvaluate = (data: ResetHomeworkEvaluateProps) =>
  POST('/evalua/homeworkEvaluate/refresh', data);

export const getHomeworkRecordListByHomeworkId = (data: GetHomeworkRecordListByHomeworkIdProps) =>
  POST<HomeworkEvaluateStatType[]>('/evalua/homeworkEvaluate/getListByHomeworkId', data);

export const getHomeworkRecordListByStudentId = (data: GetHomeworkRecordListByStudentIdProps) =>
  POST<HomeworkEvaluateStatType[]>('/evalua/homeworkEvaluate/getListByStudentId', data);
