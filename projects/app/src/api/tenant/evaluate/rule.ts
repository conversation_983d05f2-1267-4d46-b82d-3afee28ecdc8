import { log } from 'console';
// api.ts
import { POST } from '@/utils/request';
import { PagingData } from '@/types';
import {
  DeleteEvaluationRuleParams,
  AddEvaluationRuleParams,
  EvaluationRuleDetailParams,
  EvaluationRuleListParams,
  EvaluationRuleDetail,
  GetTeachersParams,
  TeacherType,
  EvaluationRule,
  CopyEvaluationRuleParams,
  DimenType,
  AddDimenTypeParams,
  DeleteDimenTypeParams,
  ListDimenTypeParams,
  UpdateDimenTypeParams,
  EvaluaDimension,
  EvaluaProject,
  EvaluaIndactor,
  EvaluaIndicatorQueryReq,
  EvaluaIndactorVO,
  EvaluaIndactorSortParams,
  EvaluaScoreLevel,
  EvaluaScoreLevelValueCreateParams,
  EvaluaScoreLevelValue,
  EvaluaScoreLevelValueUpdateParams,
  TenantEvaluateIconAddParams,
  IconListType,
  EvaluaIndactorParams,
  GetIndactorTreeByProjectParams,
  EvaluaIndactorDetailParams,
  EvaluaViewListNewParams,
  EvaluaViewListNewList,
  HomeworkEvaluateDeleteParams,
  HomeworkClassEvaluateDeleteParams,
  DeptTeacherType,
  BatchUpdateParams
} from '@/types/api/tenant/evaluate/rule';
import { getListFromPage } from '@/utils/api';
import { DepartmentTree } from '@/types/api/tenant/teamManagement/student';
import { HasSubIndicator, PeriodType } from '@/constants/api/tenant/evaluate/rule';
import { filterTree } from '@/utils/tree';
// async function validUsedIndactors(data: AddEvaluationRuleParams) {
//   if (!data.evaluateeIds || !data.copyId) {
//     throw new Error('缺少必要的参数');
//   }

//   const clazzIds = data.evaluateeIds;

//   const usedIndactors = await getUsedIndactorsByClazzs({
//     clazzIds,
//     ruleId: data.copyId
//   });

//   // 过滤掉已经选择的指标
//   const unavailableIndactors = usedIndactors.filter(
//     (item) => !data.indacatorIds?.includes(item.id!)
//   );

//   if (unavailableIndactors.length > 0) {
//     // 如果有不可用的指标，返回这些指标的信息
//     return {
//       valid: false,
//       unavailableIndactors
//     };
//   }

//   return { valid: true };
// }
export const addEvaluationRule = (data: AddEvaluationRuleParams) => {
  const addApi = () => POST<boolean>('/evalua/rule/add', data);
  // if (data.copyId) {
  //   return validUsedIndactors(data).then((result) => {
  //     if (result.valid) {
  //       return addApi();
  //     } else {
  //       throw new Error(
  //         `以下指标不可用于选中的班级：${result.unavailableIndactors?.map((i) => i.indactorName).join(', ')}`
  //       );
  //     }
  //   });
  // } else {
  return addApi();
  // }
};
export const deleteEvaluationRule = (data: DeleteEvaluationRuleParams) =>
  POST<boolean>('/evalua/rule/delete', data);

export const copyEvaluationRule = (data: CopyEvaluationRuleParams) =>
  POST<boolean>('/evalua/rule/copy', data);

export const getEvaluationRuleDetail = (data: EvaluationRuleDetailParams) =>
  POST<EvaluationRuleDetail>('/evalua/rule/detail', data);

export const getEvaluationRuleList = (data: EvaluationRuleListParams) =>
  POST<PagingData<EvaluationRuleDetail>>('/evalua/rule/listPage', data);

export const getTeachers = (data: GetTeachersParams) =>
  POST<TeacherType[]>('/evalua/rule/schoolDept/getTeachers', data);

export const updateEvaluationRule = (data: EvaluationRule) =>
  POST<boolean>('/evalua/rule/update', data);

export const addDimenType = (data: AddDimenTypeParams) =>
  POST<boolean>('/evalua/dimenType/add', data);

export const deleteDimenType = (data: DeleteDimenTypeParams) =>
  POST<boolean>('/evalua/dimenType/delete', data);

export const listDimenType = (data: ListDimenTypeParams) =>
  POST<DimenType[]>('/evalua/dimenType/list', data);

export const updateDimenType = (data: UpdateDimenTypeParams) =>
  POST<boolean>('/evalua/dimenType/update', data);

// 评价维度相关API函数
export const addDimension = (data: EvaluaDimension) =>
  POST<boolean>('/evalua/indactor/dimen/add', data);

export const listPageDimension = (data: EvaluaIndicatorQueryReq) =>
  POST<PagingData<EvaluaDimension>>('/evalua/indactor/dimen/listPage', data);

export const updateDimension = (data: EvaluaDimension) =>
  POST<boolean>('/evalua/indactor/dimen/update', data);

export const deleteDimension = (data: { id: string }) =>
  POST<boolean>('/evalua/indactor/delete', data);

// 评价项目相关API函数
export const addProject = (data: EvaluaProject) =>
  POST<boolean>('/evalua/indactor/project/add', data);

export const listPageProject = (data: EvaluaIndicatorQueryReq) =>
  POST<PagingData<EvaluaProject>>('/evalua/indactor/project/listPage', data);

export const listProject = (data: EvaluaIndicatorQueryReq) =>
  getListFromPage(listPageProject, data, 9999);
export const updateProject = (data: EvaluaProject) =>
  POST<boolean>('/evalua/indactor/project/update', data);

export const deleteProject = (data: { id: string }) =>
  POST<boolean>('/evalua/indactor/delete', data);

// 评价指标相关API函数
export const addIndactor = (data: EvaluaIndactor) => POST<boolean>('/evalua/indactor/add', data);

export const deleteIndactor = (data: { id: string }) =>
  POST<boolean>('/evalua/indactor/delete', data);

export const getIndactorsByProject = (data: { projectId: string; ruleId?: string }) =>
  POST<EvaluaIndactorVO[]>('/evalua/indactor/getIndactorsByProject', data);

export const getIndactorTreeByAuto = (data: {
  gradeIds: string[];
  clazzIds: string[];
  ruleId?: string;
  semesterId: string;
  periodType: PeriodType;
  startTime: string;
  endTime: string;
}) => POST<EvaluaIndactorVO[]>('/evalua/indactor/getIndactorTreeByAuto', data);

export const getIndactorTreeByProject = async (data: GetIndactorTreeByProjectParams) => {
  const { status, parentId, evaluateType, searchKey, stage } = data;

  // 获取原始数据
  const result = await POST<EvaluaIndactorVO[]>('/evalua/indactor/getIndactorTreeByProject', {
    status,
    parentId
  });

  const filteredResult = filterTree(result, (node) => {
    const matchesEvaluateType = evaluateType
      ? node.isHasSub == HasSubIndicator.Yes
        ? false
        : node.evaluateType === evaluateType
      : true;
    const matchesSearchKey = searchKey ? node.indactorName?.includes(searchKey) : true;
    const matchesStage = stage
      ? node.isHasSub == HasSubIndicator.Yes
        ? false
        : (node.stage as string)?.includes(`${stage}`)
      : true;
    if (matchesEvaluateType && matchesSearchKey && matchesStage) {
      return true;
    } else {
      return false;
    }
  });

  return filteredResult;
};
export const getUsedIndactorsByClazzs = (data: {
  clazzIds: string[];
  ruleId?: string;
  semesterId: string;
  periodType: PeriodType;
  startTime: string;
  endTime: string;
}) => POST<EvaluaIndactorVO[]>('/evalua/indactor/getUsedIndactorsByClazzs', data);

export const updateIndactor = (data: EvaluaIndactor) =>
  POST<boolean>('/evalua/indactor/update', data);

export const listPageIndactor = (data: EvaluaIndicatorQueryReq) =>
  POST<PagingData<EvaluaIndactorVO>>('/evalua/indactor/getIndactorTreeByProject', data);
export const runSortIndicators = (data: EvaluaIndactorSortParams[]) =>
  POST<boolean>('/evalua/indactor/updateSortNo', { indactors: data });

export const schoolDeptSubjectManageSubjects = () =>
  POST<{ id: string; name: string }[]>('/client/schoolDeptSubjectManage/subjects');

// 创建评分等级
export const createScoreLevel = (
  data: Omit<EvaluaScoreLevel, 'id' | 'createTime' | 'updateTime' | 'isDeleted'>
) => POST<boolean>('/evalua/scoreLevel/create', data);

// 删除评分等级
export const deleteScoreLevel = (data: Pick<EvaluaScoreLevel, 'id'>) =>
  POST<boolean>('/evalua/scoreLevel/delete', data);

// 获取评分等级列表
export const getScoreLevelList = (data: Partial<EvaluaScoreLevel>) =>
  POST<EvaluaScoreLevel[]>('/evalua/scoreLevel/getList', data);

// 修改评分等级
export const updateScoreLevel = (data: EvaluaScoreLevel) =>
  POST<boolean>('/evalua/scoreLevel/update', data);

// 添加评分等级设置
export const createScoreLevelValue = (data: EvaluaScoreLevelValueCreateParams[]) =>
  POST<boolean>('/evalua/scoreLevelValue/create', data);

// 删除评分等级设置
export const deleteScoreLevelValue = (data: Pick<EvaluaScoreLevelValue, 'id'>) =>
  POST<boolean>('/evalua/scoreLevelValue/delete', data);

// 获取评分等级设置列表
export const getScoreLevelValueList = (data: Partial<EvaluaScoreLevelValue>) =>
  POST<EvaluaScoreLevelValue[]>('/evalua/scoreLevelValue/getList', data);

// 修改评分等级设置
export const updateScoreLevelValue = (data: EvaluaScoreLevelValueUpdateParams[]) =>
  POST<boolean>('/evalua/scoreLevelValue/update', data);

// 修改评分等级设置
export const batchUpdateScoreLevelValue = (data: BatchUpdateParams) =>
  POST<boolean>('/evalua/scoreLevelValue/processCreateAndUpdate', data);

export const tenantEvaluateIconList = () => POST<IconListType[]>('/tenant/evaluateIcon/list');

export const tenantEvaluateIconAdd = (data: TenantEvaluateIconAddParams) =>
  POST<boolean>('/tenant/evaluateIcon/add', data);

export const evaluaIndactorDisable = (data: EvaluaIndactorParams) =>
  POST(`/evalua/indactor/disable`, data);

export const evaluaIndactorEnable = (data: EvaluaIndactorParams) =>
  POST(`/evalua/indactor/enable`, data);

export const evaluaIndactorDetail = (data: EvaluaIndactorDetailParams) =>
  POST<EvaluaViewListNewList>(`/evalua/indactor/detail`, data);

export const evaluaViewListPageNew = (data: EvaluaViewListNewParams) =>
  POST<PagingData<EvaluaViewListNewList>>(`/evalua/view/listPageNew`, data);

export const evaluaHomeworkEvaluateDelete = (data: HomeworkEvaluateDeleteParams) =>
  POST<boolean>(`/evalua/homeworkEvaluate/delete`, data);

export const evaluaClassEvaluateDelete = (data: HomeworkClassEvaluateDeleteParams) =>
  POST<boolean>(`/evalua/classEvaluate/delete`, data);

export const getTeacherByTree = () =>
  POST<DeptTeacherType[]>(`/evalua/rule/schoolDept/getTeacherByTree`);
