import { PagingData } from '@/types';
import {
  PromptDeleteValidParams,
  PromptListParams,
  PromptPersonalListParams,
  PromptType,
  TenantPromptCreateParams,
  TenantPromptDeleteParams,
  TenantPromptPageParams,
  TenantPromptPageType,
  TenantPromptUpdateParams,
  TenantPromptUpdateStatusParams
} from '@/types/api/tenant/prompt';
import { POST } from '@/utils/request';
import { promisifyConfirm } from '@/utils/ui/messageBox';

export const getTenantPromptList = (data: PromptListParams) =>
  POST<PromptType[]>(`/client/tenant/prompt/list`, data);

// 个人快捷指令列表
export const getPersonalPromptList = (data: PromptPersonalListParams) =>
  POST<PromptType[]>(`/client/tenant/prompt/personal/list`, data);

export const tenantPromptCreate = (data: TenantPromptCreateParams) =>
  POST('/client/tenant/prompt/create', data);

export const tenantPromptDelete = (data: TenantPromptDeleteParams) =>
  POST('/client/tenant/prompt/delete', data);

export const getTenantPromptPage = (data: TenantPromptPageParams) =>
  POST<PagingData<TenantPromptPageType>>('/client/tenant/prompt/page', data);

export const getTenantPromptPersonalPage = (data: TenantPromptPageParams) =>
  POST<PagingData<TenantPromptPageType>>('/client/tenant/prompt/personal/page', data);

export const tenantPromptUpdate = (data: TenantPromptUpdateParams) =>
  POST('/client/tenant/prompt/update', data);

export const tenantPromptUpdateStatus = (data: TenantPromptUpdateStatusParams) =>
  POST('/client/tenant/prompt/updateStatus', data);

export const tenantValidPromptWorkflow = (data: PromptDeleteValidParams) =>
  POST<boolean>('/client/tenant/prompt/validPromptWorkflow', data);
