import { POST } from '@/utils/request';
import { PagingData, RequestPageParams } from '@/types';
import {
  CreateWorkflowParams,
  DetailRequest,
  TenantWorkflowsPageRequest,
  UpdateWorkflowParams,
  UpdateWorkflowStatusParams,
  TenantWorkflow,
  TenantWorkflowProcess,
  CreateWorkflowProcessParams,
  DeleteWorkflowProcessParams,
  ListWorkflowProcessesParams,
  ReSortWorkflowProcessesParams,
  UpdateWorkflowProcessParams
} from '@/types/api/tenant/workflow';

/**
 * 创建工作流
 */
export const createWorkflow = (data: CreateWorkflowParams) =>
  POST<string>('/client/tenantWorkflow/create', data);

/**
 * 删除工作流
 */
export const deleteWorkflow = (data: DetailRequest) => POST('/client/tenantWorkflow/delete', data);

/**
 * 获取工作流分页列表
 */
export const getWorkflowPage = (data: TenantWorkflowsPageRequest) =>
  POST<PagingData<TenantWorkflow>>('/client/tenantWorkflow/page', data);

/**
 * 获取个人工作流分页列表
 */
export const getPersonalWorkflowPage = (data: TenantWorkflowsPageRequest) =>
  POST<PagingData<TenantWorkflow>>('/client/tenantWorkflow/personal/page', data);

/**
 * 更新工作流状态
 */
export const updateWorkflowStatus = (data: UpdateWorkflowStatusParams) =>
  POST('/client/tenantWorkflow/udpateStatus', data);

/**
 * 编辑工作流
 */
export const updateWorkflow = (data: UpdateWorkflowParams) =>
  POST('/client/tenantWorkflow/update', data);

/**
 * 创建工作环节
 */
export const createWorkflowProcess = (data: CreateWorkflowProcessParams) =>
  POST('/client/tenantWorkflowProcess/create', data);

/**
 * 删除工作环节
 */
export const deleteWorkflowProcess = (data: DeleteWorkflowProcessParams) =>
  POST('/client/tenantWorkflowProcess/delete', data);

/**
 * 获取工作环节列表
 */
export const listWorkflowProcesses = (data: ListWorkflowProcessesParams) =>
  POST<TenantWorkflowProcess[]>('/client/tenantWorkflowProcess/list', data);

/**
 * 工作环节重排序
 */
export const reSortWorkflowProcesses = (data: ReSortWorkflowProcessesParams) =>
  POST('/client/tenantWorkflowProcess/reSort', data);

/**
 * 更新工作环节
 */
export const updateWorkflowProcess = (data: UpdateWorkflowProcessParams) =>
  POST('/client/tenantWorkflowProcess/update', data);
