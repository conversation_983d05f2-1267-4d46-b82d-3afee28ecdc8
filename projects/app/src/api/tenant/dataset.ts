import { DatasetTypeEnum } from '@/constants/api/dataset';
import { PagingData, ParentTreePathItemType } from '@/types';
import {
  CreateDatasetProps,
  DatasetItemType,
  UpdateDatasetProps,
  TenantDatasetPageRequest,
  TenantDatasetPageRequestPage
} from '@/types/api/tenant/dataset';
import { POST } from '@/utils/request';

/* ======================== dataset ======================= */
export const getDatasets = (data: TenantDatasetPageRequest) =>
  POST<DatasetItemType[]>('/client/tenant/dataset/list', data);

export const getDatasetsPage = (data: TenantDatasetPageRequestPage) =>
  POST<PagingData<DatasetItemType>>('/client/tenant/dataset/page', data);

export const getDatasetById = (id: string) =>
  POST<DatasetItemType>(`/client/tenant/dataset/detail?id=${id}`);

export const postCreateDataset = (data: CreateDatasetProps) =>
  POST<boolean>(`/client/tenant/dataset/create`, data);

export const putDatasetById = (data: UpdateDatasetProps) =>
  POST<void>(`/client/tenant/dataset/update`, data);

export const delDatasetById = (id: string) => POST(`/client/tenant/dataset/delete`, { id });

export const getDatasetPaths = (parentId?: string) =>
  POST<ParentTreePathItemType[]>('/client/tenant/dataset/paths', {
    parentId: parentId || '0'
  });

/* ================== file ======================== */
export const getFileViewUrl = (fileId: string) =>
  POST<string>('/client/tenant/dataset/file/getPreviewUrl', { fileId });

export const getTeamMembers = (fileId: string) =>
  POST<string>('/client/tenant/dataset/file/getPreviewUrl', { fileId });

/* ================== file ======================== */
export const onUpdateCollaborators = (fileId: any) =>
  POST<string>('/client/tenant/dataset/file/getPreviewUrl', { fileId });
export const onDelOneCollaborator = (fileId: string) =>
  POST<string>('/client/tenant/dataset/file/getPreviewUrl', { fileId });

export const getCollaboratorList = (fileId: string) =>
  POST<string>('/client/tenant/dataset/file/getPreviewUrl', { fileId });
