import { PagingData, RequestPageParams } from '@/types';
import { TenantAppListItemType } from '@/types/api/tenant/app';
import {
  SubSceneCreateParams,
  SceneType,
  SceneUpdateParams,
  SceneCreateParams,
  GetSubSceneListParams,
  SceneSortParams,
  SubSceneUpdateParams,
  SubSceneSortParams,
  SubSceneType,
  GetSceneList
} from '@/types/api/tenant/scene';

import { POST } from '@/utils/request';

export const getSceneList = (data: GetSceneList) =>
  POST<SceneType[]>('/client/tenant/scene/list', data);

export const getDisplayedList = (data: GetSceneList) =>
  POST<SceneType[]>('/client/tenant/scene/getDisplayedList', data);

export const createScene = (data: SceneCreateParams) => POST('/client/tenant/scene/create', data);

export const updateScene = (data: SceneUpdateParams) => POST('/client/tenant/scene/update', data);

export const sortScene = (data: SceneSortParams) => POST('/client/tenant/scene/sort', data);

export const sortApp = (data: SceneSortParams) => POST('/client/tenant/appLabel/sort', data);

export const deleteScene = (data: { id: string }) => POST(`/client/tenant/scene/delete`, data);

export const getMyAppList = () => POST<{}>('/client/tenant/tenant/page');

export const updateSceneListSort = () => POST<SceneType[]>('/client/tenant/scene/list');

export const createSubScene = (data: SubSceneCreateParams) =>
  POST('/client/tenant/label/create', data);

export const updateSubScene = (data: SubSceneUpdateParams) =>
  POST('/client/tenant/label/update', data);

export const sortSubScene = (data: SubSceneSortParams) => POST('/client/tenant/label/sort', data);

export const deleteSubScene = (data: { id: string }) => POST(`/client/tenant/label/delete`, data);

export const getAppOptions = () => POST<SceneType[]>('/client/tenant/tenant/page');

export const getSubSceneList = (data: GetSubSceneListParams) =>
  POST<SubSceneType[]>('/client/tenant/label/list', data);

/**
 * 获取模型分页列表
 */
export const getAppLabelPage = (
  data: RequestPageParams & {
    excludeLabelId?: string;
    ascs?: string;
    current?: number;
    descs?: string;
    industry?: string;
    tenantLabelId?: string;
    tenantSceneId?: string;
    searchKey?: string;
    size?: number;
    source?: string;
    tenantAppLabelId?: string;
  }
) => POST<PagingData<TenantAppListItemType>>('/client/tenant/appLabel/page', data);
