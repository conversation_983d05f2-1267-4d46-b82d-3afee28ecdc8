import { POST } from '@/utils/request';
import { PagingData, RequestPageParams } from '@/types';

import {
  CreateAppParams,
  deleteAppParams,
  TenantAppUpdateParams,
  TenantAppListItemType,
  getTenantAppListParams,
  GetPersonalAppListParams,
  TenantAppDetailInfo,
  transitionWorkflowBody,
  transitionWorkflowResponse,
  TenantAppUpdateConfigParams
} from '@/types/api/tenant/app';

/**
 * 获取模型分页列表
 */
export const getMyAppPage = (
  data: RequestPageParams & {
    excludeLabelId?: string;
    ascs?: string;
    current?: number;
    descs?: string;
    industry?: string;
    tenantLabelId?: string;
    tenantSceneId?: string;
    searchKey?: string;
    size?: number;
    source?: string;
  }
) => POST<PagingData<TenantAppListItemType>>('/client/tenant/app/page', data);

/**
 * 获取个人模型分页列表
 */
export const getPersonalAppPage = (data: RequestPageParams) =>
  POST<PagingData<TenantAppListItemType>>('/client/tenant/app/personal/page', data);

/**
 * 获取个人模型分页列表
 */
export const getPersonalAppList = (data: GetPersonalAppListParams) =>
  POST<TenantAppListItemType[]>('/client/tenant/app/personal/list', data);

/**
 * 获取租户模型分页列表
 */
export const getTenantAppPage = (data: RequestPageParams) =>
  POST<PagingData<TenantAppListItemType>>('/client/tenant/app/page', data);

/**
/**
 * 获取租户模型分页列表
 */
export const getTenantAppList = (data: getTenantAppListParams) =>
  POST<TenantAppListItemType[]>('/client/tenant/app/list', data);

/**
 * 创建一个模型
 */
export const createApp = (data: CreateAppParams) => POST<string>('/client/tenant/app/create', data);

/**
 * 根据 ID 删除模型
 */
export const deleteTenantApp = (data: deleteAppParams) => POST('/client/tenant/app/delete', data);

/**
 * 根据 ID 更新模型
 */
export const updateTenantApp = (data: TenantAppUpdateParams) =>
  POST('/client/tenant/app/update', data);

export const publishTenantApp = (data: TenantAppUpdateParams) =>
  POST('/client/tenant/app/publish', data);

export const getTenantAppDetail = (id: string) =>
  POST<TenantAppDetailInfo>('/client/tenant/app/detail', { id });

/**
 * 取消置顶
 */
export const cancelTop = (data: { id: string }) => POST('/client/tenant/app/cancelTop', data);

/**
 * 从其他类型导入
 */
export const importFromOtherType = (data: {
  createUsername?: string;
  industry: string;
  otherIndustry: string;
  updateUsername?: string;
}) => POST('/client/tenant/app/importFromOtherType', data);

/**
 * 移入应用
 */
export const moveApp = (data: {
  tenantAppIds: string[];
  tenantLabelId: string;
  tenantSceneId: string;
}) => POST('/client/tenant/app/move', data);

/**
 * 移出应用
 */
export const removeApp = (data: {
  tenantAppId: string;
  tenantLabelId: string;
  tenantSceneId: string;
}) => POST('/client/tenant/app/remove', data);

/**
 * 置顶
 */
export const topApp = (data: { id: string }) => POST('/client/tenant/app/top', data);

export const cancelTopApp = (data: { id: string }) => POST('/client/tenant/app/cancelTop', data);

export const copyApp = (data: { id: string }) => POST('/client/tenant/app/copy', data);

/**
 * 更新应用状态
 */
export const updateAppStatus = (data: { id: string; status: number }) =>
  POST('/client/tenant/app/updateStatus', data);

//判断应用是否关联工作流
export const getTenantValidAppWorkflow = (data: { id: string }) =>
  POST('/client/tenant/app/validAppWorkflow', data);

export const setTenantAppUpdateConfig = (data: TenantAppUpdateConfigParams) =>
  POST('/client/tenant/app/updateConfig', data);

export const tenantTransition2Workflow = (data: transitionWorkflowBody) =>
  POST<TenantAppListItemType>('/client/tenant/app/transitionWorkflow', data);

export const upPersonalApp = (data: { id: string }) =>
  POST('/client/tenant/app/upPersonalApp', data);
