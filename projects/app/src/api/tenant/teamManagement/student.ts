import { POST } from '@/utils/request';
import { PagingData, RequestPaging } from '@/types';

import {
  DepartmentTree,
  CreateClientSchoolDeptParasm,
  UpdateClientSchoolDeptParams,
  CreateClientStudentParams,
  UpdatelientStudentParams,
  SystemRegionSelectType,
  ClientStudentPageType,
  DetailClientStudentType,
  ChangeStatusClientStudentParams,
  ChangeClazzClientStudentParams
} from '@/types/api/tenant/teamManagement/student';
import { getListFromPage } from '@/utils/api';

export const getClientSchoolDeptTree = () => POST<DepartmentTree>('/client/schoolDept/tree');

export const createClientSchoolDept = (data: CreateClientSchoolDeptParasm) =>
  POST('/client/schoolDept/create', data);

export const updateClientSchoolDept = (data: UpdateClientSchoolDeptParams) =>
  POST('/client/schoolDept/update', data);

export const setClientSchoolDeptReSort = (data: any) => POST('/client/schoolDept/reSort', data);

export const getClientStudentPage = (
  data: { name?: string; stageId?: string; gradeId?: string; clazzId?: string } & RequestPaging
) => POST<PagingData<ClientStudentPageType>>('/client/student/page', data);

export const getClientStudentList = (data: {
  stageId?: string;
  gradeId?: string;
  clazzId?: string;
}) => getListFromPage(getClientStudentPage, data, 9999);

export const getSystemRegionSelect = () => POST<SystemRegionSelectType>('/system/region/select');

export const getSystemRegionLazyList = (parentCode: string) =>
  POST<SystemRegionSelectType>('/system/region/lazy-list', { parentCode });

export const createClientStudent = (data: CreateClientStudentParams) =>
  POST('/client/student/create', data);

export const updateClientStudent = (data: UpdatelientStudentParams) =>
  POST('/client/student/update', data);

export const detailClientStudent = (id: string) =>
  POST<DetailClientStudentType>('/client/student/detail', { id });

export const changeStatusClientStudent = (data: ChangeStatusClientStudentParams) =>
  POST('/client/student/changeStatus', data);

export const changeClazzClientStudent = (data: ChangeClazzClientStudentParams) =>
  POST('/client/student/changeClazz', data);

export const deleteClientSchoolDept = (id: string) => POST('/client/schoolDept/delete', { id });
