import { POST } from '@/utils/request';
import { PagingData, RequestPageParams } from '@/types';
import {
  ClientSchoolDeptManageTreeType,
  ClientSchoolDeptManageValid,
  ClientSchoolDeptManageTreeParams,
  ClientSchoolDeptManageBindParams,
  ClientSchoolDeptManageCopyParams,
  ClientSchoolDeptSubjectManageTreeParams,
  ClientSchoolDeptSubjectManageTreeType,
  ClientSchoolDeptSubjectManageBindParams
} from '@/types/api/tenant/teamManagement/teach';

export const getClientSchoolDeptManageTree = (data: ClientSchoolDeptManageTreeParams) =>
  POST<ClientSchoolDeptManageTreeType>('/client/schoolDeptManage/tree', data);

export const getClientSchoolDeptManageValid = (semesterId: string) =>
  POST<ClientSchoolDeptManageValid>('/client/schoolDeptManage/valid', { semesterId });

export const getClientSchoolDeptManageCreate = (semesterId: string) =>
  POST('/client/schoolDeptManage/create', { semesterId });

export const setClientSchoolDeptManageBind = (data: ClientSchoolDeptManageBindParams) =>
  POST('/client/schoolDeptManage/bind', data);

export const setClientSchoolDeptManageCopy = (data: ClientSchoolDeptManageCopyParams) =>
  POST('/client/schoolDeptManage/copy', data);

export const getClientSchoolDeptSubjectManageTree = (
  data: ClientSchoolDeptSubjectManageTreeParams
) => POST<ClientSchoolDeptSubjectManageTreeType>('/client/schoolDeptSubjectManage/tree', data);

export const setClientSchoolDeptSubjectManageBind = (
  data: ClientSchoolDeptSubjectManageBindParams
) => POST('/client/schoolDeptSubjectManage/bind', data);
