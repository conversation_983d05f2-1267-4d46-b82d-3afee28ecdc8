import { Semester, SemesterListOptions } from './../../../types/api/tenant/teamManagement/semester';
import { POST } from '@/utils/request';
import { PagingData, RequestPageParams } from '@/types';
import {
  ClientSemesterPageType,
  DictTree,
  ClientSemesterGetDelayList,
  CreateClientSemesterParams,
  UpdateClientSemesterParams,
  DetailClientSemester,
  SetCurrentClientSemesterParams
} from '@/types/api/tenant/teamManagement/semester';
import { getListFromPage } from '@/utils/api';
import { TermMap } from '@/constants/api/tenant/evaluate/rule';

export const getClientSemesterPage = (data: any) =>
  POST<PagingData<ClientSemesterPageType>>('/client/semester/page', data);

export const getClientSemesterList = (data: any) =>
  getListFromPage(getClientSemesterPage, data, 9999).then((res) => {
    const list: SemesterListOptions[] = [];
    res.forEach((item) => {
      let target = list.find((it) => it.year == item.year);
      if (!target) {
        let semesterItem: SemesterListOptions = {
          ...item,
          termList: [
            {
              label: TermMap[item.type].label,
              value: item.type
            }
          ]
        };
        list.push(semesterItem);
      } else {
        target.termList.push({
          label: TermMap[item.type].label,
          value: item.type
        });
      }
    });
    return list;
  });

export const getClientSemesterGetDelayList = (id: string) =>
  POST<ClientSemesterGetDelayList>('/client/semester/getDelayList', { id });

export const getSystemDictTree = (code: string) => POST<DictTree>('/system/dict/tree', { code });

export const createClientSemester = (data: CreateClientSemesterParams) =>
  POST('/client/semester/create', data);

export const updateClientSemester = (data: UpdateClientSemesterParams) =>
  POST('/client/semester/update', data);

export const getDetailClientSemester = (id: string) =>
  POST<DetailClientSemester>('/client/semester/detail', { id });

export const setCurrentClientSemester = (data: SetCurrentClientSemesterParams) =>
  POST('/client/semester/setCurrent', data);
