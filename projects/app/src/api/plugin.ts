import { POST, DELETE } from '@/utils/request';
import { PagingData } from '@/types';
import {
  ClearHistoriesProps,
  DelHistoryProps,
  DeleteChatItemProps,
  InitChatProps,
  InitChatResponse,
  UpdateChatFeedbackProps,
  UpdateHistoryProps,
  getHistoriesProps,
  FileContentParams,
  FileContentType,
  ChatPptUrlParams,
  UpdateChatItemProps
} from '@/types/api/chat';
import { AxiosProgressEvent } from 'axios';
import { FileMetaType } from '@/types/api/file';
import { ChatHistoryItemType } from '@/fastgpt/global/core/chat/type';
import {
  FlowNodeTemplateType,
  GetPreviewNodeQuery,
  GetSystemPluginTemplatesBody,
  NodeTemplateListItemType
} from '@/types/api/plugin';

/**
 * 获取初始化聊天内容
 */
export const getPreviewPluginNode = (data: GetPreviewNodeQuery) =>
  POST<FlowNodeTemplateType>(`/system/fast/getPreviewNode`, data);

export const getSystemPlugTemplates = (data: GetSystemPluginTemplatesBody) =>
  POST<NodeTemplateListItemType[]>(`/system/fast/getSystemPluginTemplates`, data);

/**
 * get current window history(appid or shareId)
 */
export const getTeamPlugTemplates = (data: getHistoriesProps) =>
  POST<PagingData<ChatHistoryItemType>>('/client/chat/page', data);
