// src/api/tenantWorkflow.ts

import { POST } from '@/utils/request';
import {
  TenantWorkflow,
  CreateWorkflowParams,
  DeleteWorkflowParams,
  CopyWorkflowParams,
  ListWorkflowsParams,
  UpdateWorkflowParams,
  TenantWorkflowProcess,
  CreateWorkflowProcessParams,
  DeleteWorkflowProcessParams,
  ListWorkflowProcessesParams,
  ReSortWorkflowProcessesParams,
  UpdateWorkflowProcessParams
} from '@/types/api/workflow';
import { DataSource } from '@/constants/common';
import {
  listWorkflowProcesses as tenantListWorkflowProcesses,
  deleteWorkflowProcess as tenantDeleteWorkflowProcess,
  reSortWorkflowProcesses as tenantReSortWorkflowProcesses,
  createWorkflowProcess as TenantCreateWorkflowProcess,
  updateWorkflowProcess as TenantUpdateWorkflowProcess
} from '@/api/tenant/workflow';
import { getTenantLabelList, getTenantSceneList } from '@/api/scene';
import { getMyAppList } from '@/api/app';
import {
  getSubSceneList as getAdminLabelList,
  getSceneList as getAdminSceneList
} from '@/api/tenant/scene';
import { getPersonalAppList, getTenantAppList } from '@/api/tenant/app';
import { AppListItemType } from '@/types/api/app';
import { SceneType, SubSceneType } from '@/types/api/scene';
import { GetSubSceneListParams } from '@/types/api/tenant/scene';
import { GetPersonalAppListParams } from '@/types/api/tenant/app';
import { getPromptCenterList } from './prompt';
import { PromptCenterListParams, PromptCenterListType } from '@/types/api/prompt';
import { getPersonalPromptList, getTenantPromptList } from './tenant/prompt';
import { PromptListParams, PromptPersonalListParams } from '@/types/api/tenant/prompt';

/**
 * 复制工作流
 */
export const copyWorkflow = (data: CopyWorkflowParams) =>
  POST('/client/user/tenantWorkflow/copy', data);

/**
 * 创建工作流
 */
export const createWorkflow = (data: CreateWorkflowParams) =>
  POST('/client/user/tenantWorkflow/create', data);

/**
 * 删除工作流
 */
export const deleteWorkflow = (data: DeleteWorkflowParams) =>
  POST('/client/user/tenantWorkflow/delete', data);

/**
 * 获取某个应用下的全部工作流列表
 */
export const listAllWorkflows = (data: ListWorkflowsParams) =>
  POST<TenantWorkflow[]>('/client/user/tenantWorkflow/listAll', data);

/**
 * 获取某个应用下的官方工作流列表
 */
export const listOfficialWorkflows = (data: ListWorkflowsParams) =>
  POST<TenantWorkflow[]>('/client/user/tenantWorkflow/offical/list', data);

/**
 * 获取我的工作流列表
 */
export const listPersonalWorkflows = (data: ListWorkflowsParams) =>
  POST<TenantWorkflow[]>('/client/user/tenantWorkflow/personal/list', data);

/**
 * 获取某个应用下的专属工作流列表
 */
export const listTenantWorkflows = (data: ListWorkflowsParams) =>
  POST<TenantWorkflow[]>('/client/user/tenantWorkflow/tenant/list', data);

/**
 * 编辑工作流
 */
export const updateWorkflow = (data: UpdateWorkflowParams) =>
  POST('/client/user/tenantWorkflow/update', data);

/**
 * 创建工作环节
 */
export const createWorkflowProcess = (data: CreateWorkflowProcessParams) =>
  POST('/client/user/tenantWorkflowProcess/create', data);

/**
 * 删除工作环节
 */
export const deleteWorkflowProcess = (data: DeleteWorkflowProcessParams) =>
  POST('/client/user/tenantWorkflowProcess/delete', data);

/**
 * 获取工作环节列表
 */
export const listWorkflowProcesses = (data: ListWorkflowProcessesParams) =>
  POST<TenantWorkflowProcess[]>('/client/user/tenantWorkflowProcess/list', data);

/**
 * 工作环节重排序
 */
export const reSortWorkflowProcesses = (data: ReSortWorkflowProcessesParams) =>
  POST('/client/user/tenantWorkflowProcess/reSort', data);

/**
 * 更新工作环节
 */
export const updateWorkflowProcess = (data: UpdateWorkflowProcessParams) =>
  POST('/client/user/tenantWorkflowProcess/update', data);

// 判断

export const listWorkflowProcessesFn = (data: ListWorkflowProcessesParams, source: DataSource) => {
  if (source === DataSource.Personal) {
    return listWorkflowProcesses(data);
  } else {
    return tenantListWorkflowProcesses(data);
  }
};

/**
 * 工作环节重排序
 */
export const reSortWorkflowProcessesFn = (
  data: ReSortWorkflowProcessesParams,
  source: DataSource
) => {
  if (source === DataSource.Personal) {
    return reSortWorkflowProcesses(data);
  } else {
    return tenantReSortWorkflowProcesses(data);
  }
};

/**
 * 更新工作环节
 */
export const updateWorkflowProcessFn = (data: UpdateWorkflowProcessParams, source: DataSource) => {
  if (source === DataSource.Personal) {
    return updateWorkflowProcess(data);
  } else {
    return TenantUpdateWorkflowProcess(data);
  }
};

/**
 * 创建工作环节
 */
export const createWorkflowProcessFn = (data: CreateWorkflowProcessParams, source: DataSource) => {
  if (source === DataSource.Personal) {
    return createWorkflowProcess(data);
  } else {
    return TenantCreateWorkflowProcess(data);
  }
};

/**
 * 删除工作环节
 */
export const deleteWorkflowProcessFn = (data: DeleteWorkflowProcessParams, source: DataSource) => {
  if (source === DataSource.Personal) {
    return deleteWorkflowProcess(data);
  } else {
    return tenantDeleteWorkflowProcess(data);
  }
};

/**
 * 获取应用列表
 */
export const getAppList = async (
  data: GetPersonalAppListParams,
  source: DataSource,
  isTenantAdmin: boolean
) => {
  if (!isTenantAdmin) {
    return getMyAppList({});
  } else if (source == DataSource.Tenant) {
    return getTenantAppList({}) as Promise<AppListItemType[]>;
  } else if (source == DataSource.Personal) {
    return getPersonalAppList(data) as Promise<AppListItemType[]>;
  }
};

/**
 * 获取场景列表
 */
export const getSceneList = async (
  data: GetSubSceneListParams | {},
  source: DataSource,
  isTenantAdmin: boolean
): Promise<SceneType[]> => {
  if (!isTenantAdmin) {
    return getTenantSceneList();
  } else {
    return getAdminSceneList(data) as Promise<SceneType[]>;
  }
};

/**
 * 获取标签列表
 */
export const getLabelList = async (sceneId: string, source: DataSource, isTenantAdmin: boolean) => {
  if (!isTenantAdmin) {
    return getTenantLabelList({ tenantSceneId: sceneId });
  } else {
    return getAdminLabelList({ tenantSceneId: sceneId }) as Promise<SubSceneType[]>;
  }
};

/**
 * 获取快捷指令
 */
export const getPromptList = async (
  data: PromptListParams,
  source: DataSource,
  isTenantAdmin: boolean
): Promise<PromptCenterListType[]> => {
  let { tmbId, tenantAppId, tenantId, source: ProSource } = data;
  if (!isTenantAdmin) {
    return getPromptCenterList({ tenantAppId, source: ProSource });
  } else if (source == DataSource.Tenant) {
    return getTenantPromptList({ tenantAppId, source: ProSource, tenantId }) as Promise<
      PromptCenterListType[]
    >;
  } else {
    return getPersonalPromptList({ tenantAppId, source: ProSource, tenantId, tmbId }) as Promise<
      PromptCenterListType[]
    >;
  }
};
