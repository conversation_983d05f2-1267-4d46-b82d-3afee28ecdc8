import { POST, GET } from '@/utils/request';
import { hashStr } from '@/utils/string';
import {
  LoginRes,
  PostLoginProps,
  DingConfig,
  QywxConfig,
  WxTicketType,
  WxUnBindType,
  LoginQRCodeParams,
  TocWxQRCodeResultType
} from '@/types/api/auth';

export const getTokenLogin = () => POST<LoginRes>('/client/auth/token', {}, { maxQuantity: 1 });

export const postLogin = ({ password, ...props }: PostLoginProps) =>
  POST<LoginRes>('/client/auth/login', {
    ...props,
    password: hashStr(password)
  });

export const loginOut = () => POST('/client/auth/logout', {}, { noToast: true });

//根据当前域名获取租户配置的钉钉应用信息
export const getDingConfig = () => GET<DingConfig>(`/ding/dingConfig`);
//根据当前域名获取租户配置的企业微信应用信息
export const getQywxConfig = () => POST<QywxConfig>(`/qywx/qywxConfig`);
export const tocWxIsTocTenant = () =>
  GET<{ data: boolean }>(`/toc/wx/isTocTenant`, '', { isResponseData: true });
export const getTocWxQRCodeResult = (ticket: string) =>
  GET<TocWxQRCodeResultType>(`/toc/wx/getLoginQRCodeResult?ticket=${ticket}`);

export const TocWxLoginByOpenId = (openId: string) =>
  GET<LoginRes>(`/toc/wx/loginByOpenId?openId=${openId}`);
export const getTocWxLoginQRCode = () => GET<LoginQRCodeParams>(`/toc/wx/getLoginQRCode`);

//获取微信临时二维码
export const getLoginQRCode = () => GET<LoginQRCodeParams>(`/wx/getLoginQRCode`);

export const getBindQRCode = () => GET(`/wx/getBindQRCode`);

//轮询获取微信扫码结果
export const getQRCodeResult = (ticket: string) =>
  GET<WxTicketType>(`/wx/getLoginQRCodeResult?ticket=${ticket}`);

export const loginByUnionId = (unionId: string) =>
  GET<LoginRes>(`/wx/loginByUnionId?unionId=${unionId}`);

export const wxUserInfo = () => GET<LoginRes>(`/wx/userInfo`);

export const wxBind = (unionId: string) =>
  POST<WxUnBindType>(`/wx/wxBind?unionId=${unionId}`, '', { isResponseData: true });

export const wxUnBind = () => POST<WxUnBindType>(`/wx/wxUnBind`, '', { isResponseData: true });

export const wxIsBindWx = () => GET(`/wx/isBindWx`);

export const tocWxNeedPay = () => GET(`/toc/wx/needPay`);

export const tocWxPayNative = () => GET(`/toc/wx/payNative`);

export const tocWxPayResult = (codeUrl: string) =>
  GET<boolean>(`/toc/wx/payResult?codeUrl=${codeUrl}`);

export const thirdLogin = (data: { code: string; orgCode: string }) =>
  POST<LoginRes>('/client/auth/code', data, { noToast: true });

export const getCloudFileUsageReport = () =>
  POST<any>('/client/activity/report/cloud/file/use', {});

// 登陆埋点
export const getClientLogin = () => POST<any>('/client/activity/report/client/login', {});

// 陕西学前师院私有化登陆接口
export const getPrivateLoginToken = (data: { code: string }) =>
  POST<any>('/client/auth/oauth2/snsy', data);

//西安建筑科技大学私有化登陆接口
export const getXauatLoginToken = (data: { code: string }) =>
  POST<any>('/client/auth/oauth2/xauat', data);

// 添加oauth2接口
export const getOauth2Token = (data: { code: string }) => POST<any>('/client/auth/oauth2', data);

// 添加cas登录接口
export const getCasLogin = (ticket: string) => GET<any>(`/client/auth/casLogin?ticket=${ticket}`);
