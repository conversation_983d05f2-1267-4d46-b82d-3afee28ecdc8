import { TaskCorrectionStatus } from '@/constants/ai/correction';
import {
  TaskInfoType,
  CompositionData,
  LatestComposition,
  StudentListType,
  CreateStudentListType,
  UpdateStudentListType,
  ParsedFileType,
  CreateCompositionInfoType,
  TempListItem,
  UpdateCompositionInfoType,
  CorrectionResultType,
  PageListResponseType,
  BatchParams,
  BatchCreateAndUploadResponse
} from '@/types/api/ai/correction/task';
import { GET, POST } from '@/utils/request';
import { BASE_URL } from '.';

export const getTaskInfo = async (data: { id: string }) => {
  return POST<TaskInfoType>(`/composition/rule/getDetail`, data, {
    baseURL: BASE_URL
  });
};

export const createComposition = async (data: CompositionData): Promise<LatestComposition | null> =>
  POST('/composition/rule/create', data, {
    baseURL: BASE_URL
  });

export const updateComposition = async (data: CompositionData): Promise<LatestComposition | null> =>
  POST('/composition/rule/update', data, {
    baseURL: BASE_URL
  });

export const getSubjectsForComposition = async () => {
  return POST(`/client/schoolDeptSubjectManage/subjectsFroComposition`, {});
};

export const getGradesOfZWPGByTeacher = async () => {
  return POST(`/client/composition/getGradeList`, {});
};

export const clearAllCompositions = async (data: { compositionRuleId: number }) => {
  return POST(
    '/composition/rule/clearAll?id=' + data.compositionRuleId,
    {},
    {
      baseURL: BASE_URL
    }
  );
};

export const getLatestComposition = async (data: { id?: string }) => {
  return POST<LatestComposition>(
    `/composition/rule/getLatest?id=${data?.id || ''}`,
    {},
    {
      baseURL: BASE_URL
    }
  );
};

export const getStudentList = async (data: {
  compositionRuleId: string;
}): Promise<StudentListType[]> => {
  return POST(
    `/composition/student/getList`,
    { compositionRuleId: data.compositionRuleId },
    {
      baseURL: BASE_URL
    }
  );
};

export const createStudentList = async (data: CreateStudentListType[]) => {
  return POST(`/composition/student/create`, data, {
    baseURL: BASE_URL
  });
};

export const updateStudentList = async (data: UpdateStudentListType[]): Promise<void> => {
  return POST(`/composition/student/update`, data, {
    baseURL: BASE_URL
  });
};

export const deleteStudentList = async (data: { id: number }[]): Promise<void> => {
  return POST(`/composition/student/delete`, data, {
    baseURL: BASE_URL
  });
};

export const uploadFileKey = async (data: {
  fileKeys: string;
  compositionRuleId: number;
}): Promise<void> => {
  return POST(`/composition/file/upload`, data, {
    baseURL: BASE_URL
  });
};

export const parseAliyunFile = async ({
  compositionRuleId,
  fileKey
}: {
  compositionRuleId: number;
  fileKey: string;
}): Promise<ParsedFileType> => {
  return POST(
    `/composition/file/aliyun/parse`,
    {
      fileKey: fileKey,
      compositionRuleId: compositionRuleId
    },
    {
      baseURL: BASE_URL
    }
  );
};

export const getTempList = async (data: { compositionRuleId: string }): Promise<TempListItem[]> => {
  return POST(`/composition/getTempList`, data, {
    baseURL: BASE_URL
  });
};

export const createCompositionInfo = async (data: CreateCompositionInfoType[]): Promise<void> => {
  return POST(`/composition/create`, data, {
    baseURL: BASE_URL
  });
};

export const updateCompositionInfo = async (data: UpdateCompositionInfoType): Promise<void> => {
  return POST(`/composition/update`, data, {
    baseURL: BASE_URL
  });
};

export const deleteCompositionFile = async (data: {
  objectKey: string;
  compositionRuleId: number | string;
}): Promise<void> => {
  return POST(`/composition/file/delete`, data, {
    baseURL: BASE_URL
  });
};

export const getCorrectionResultById = async (data: {
  compositionId: number;
}): Promise<CorrectionResultType> => {
  return POST(`/composition/correctionResults/getResultById`, data, {
    baseURL: BASE_URL
  });
};

export const getPageList = async (data: {
  current: number;
  searchKey: string;
  status: number | '';
  size: number;
}): Promise<PageListResponseType> => {
  return POST(`/composition/rule/getPageList`, data, {
    baseURL: BASE_URL
  });
};

export const submitCorrection = async (data: { compositionRuleId: number }): Promise<void> => {
  return POST(`/composition/submitCorrection`, data, {
    baseURL: BASE_URL
  });
};

export const cleanAll = async (data: { compositionRuleId: number }): Promise<void> => {
  return POST(`/composition/cleanAll`, data, {
    baseURL: BASE_URL
  });
};

export const batchCreateAndUpload = async (
  data: BatchParams[]
): Promise<BatchCreateAndUploadResponse> => {
  return POST(`/composition/batchCreateAndUpload`, data, {
    baseURL: BASE_URL
  });
};

export interface OcrParseResponseType {
  content?: string;
  url?: string;
  fileKey: string;
  parseStatus: string;
  parseResult: string;
}

export const parseOcrFile = async (data: {
  action: string;
  urls: string;
  compositionRuleId: number;
}): Promise<OcrParseResponseType[]> => {
  return POST(`/composition/file/ocr/parse`, data, {
    baseURL: BASE_URL
  });
};

export const createExample = async (data: {
  compositionRuleId: number;
  content?: string;
  inputMethod: number;
  studentName: string;
}): Promise<void> => {
  return POST(`/composition/createExample`, data, {
    baseURL: BASE_URL
  });
};

export const searchStudentList = async (data: {
  compositionRuleId: string;
}): Promise<StudentListType[]> => {
  return POST(`/composition/student/getStudentList`, data, {
    baseURL: BASE_URL
  });
};

export const agentParse = async (data: {
  compositionRuleId: number;
  fileKey: string;
  subject: string;
}): Promise<OcrParseResponseType> => {
  return POST(`/composition/file/agentParseSingle`, data, {
    baseURL: BASE_URL
  });
};

export const downloadAndUploadFile = async (data: {
  compositionRuleId: any;
  fileKey: string;
}): Promise<{ fileKey: string; fileName: string; fileUrl: string }> => {
  return POST(`/composition/file/downloadAndUploadFile`, data, {
    baseURL: BASE_URL
  });
};

export const batchAgentParse = async (data: {
  compositionRuleId: number;
  subject: string;
}): Promise<OcrParseResponseType> => {
  return POST(`/composition/file/agentParse`, data, {
    baseURL: BASE_URL
  });
};

export interface FileItemType {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  tmbId: string;
  fileName: string;
  fileKey: string;
  fileUrl: string;
  fileSuffix: string;
  fileSize: number;
  fileContent: string;
  status: number;
  fileParseStatus: number;
  compositionRuleId: number;
}

export interface FilesResponseType {
  code: number;
  success: boolean;
  data: FileItemType[];
  msg: string;
}

export const getFiles = async (data: { compositionRuleId: number }): Promise<FileItemType[]> => {
  return POST(`/composition/file/getFiles`, data, {
    baseURL: BASE_URL
  });
};

export const isPageCountExceedsFive = async (data: { fileUrl: string }): Promise<boolean> => {
  const formData = new FormData();
  formData.append('fileUrl', data.fileUrl);
  try {
    return await POST(`/composition/file/isPageCountExceedsFive`, formData, {
      baseURL: BASE_URL,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  } catch (error: any) {
    return false;
  }
};
