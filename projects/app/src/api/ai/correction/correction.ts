import { GET, POST } from '@/utils/request';
import {
  StudentInfoType,
  StudentCorrectionDetailType,
  StudentListItemType
} from '@/types/api/ai/correction/correction';
import { BASE_URL } from '.';

// 模拟学生列表数据
export const getStudentCorrectionStatusList = async (data: { compositionRuleId: string }) => {
  return POST<StudentListItemType[]>(`/composition/getStudentNameAndStatusList`, data, {
    baseURL: BASE_URL
  });
};

// 模拟学生批改详情
export const getStudentCorrectionDetail = async (compositionId: string) => {
  return POST<StudentCorrectionDetailType>(
    `/composition/correctionResults/getResultById?compositionId=${compositionId}`,
    {},
    {
      baseURL: BASE_URL
    }
  );
};

// 更新学生批改详情
export const updateStudentCorrectionDetail = async (data: Partial<StudentCorrectionDetailType>) => {
  return POST(`/composition/correctionResults/update`, data, {
    baseURL: BASE_URL
  });
};

export const getStudentCorrectionResultList = async (data: { compositionRuleId: string }) => {
  return POST<StudentInfoType[]>(`/composition/getResultList`, data, {
    baseURL: BASE_URL
  });
};

export const retryCorrection = async (data: {
  compositionRuleId: string;
  compositionId?: string;
  isAllRetry: boolean;
}) => {
  return POST(`/composition/retry`, data, {
    baseURL: BASE_URL
  });
};

export const retryCorrectionOne = async (data: {
  compositionRuleId: string;
  compositionId?: string;
}) => {
  return POST(`/composition/retryOne`, data, {
    baseURL: BASE_URL
  });
};

export const cancelCorrection = async (data: { compositionRuleId: string }) => {
  return POST(`/composition/stopCorrection`, data, {
    baseURL: BASE_URL
  });
};
export const reviewCorrection = async (data: { id: string }) => {
  return POST<{ compositionRuleId: string }>(
    `/composition/reCorrection?id=${data.id}`,
    {},
    {
      baseURL: BASE_URL
    }
  );
};

// 提交批改
export const submitCorrection = async (data: { compositionRuleId: string }) => {
  return POST(`/composition/submitCorrection`, data, {
    baseURL: BASE_URL
  });
};

export const reportCompositionCorrection = () =>
  POST('/client/activity/report/compositionCorrection');

export const reportChineseCompositionCorrectionTask = async () => {
  return POST(`/client/activity/report/chineseCompositionCorrectionTask`, {});
};

export const reportEnglishCompositionCorrectionTask = async () => {
  return POST(`/client/activity/report/englishCompositionCorrectionTask`, {});
};

export const activityReportExamination = async () => {
  return POST(`/client/activity/report/examination`, {});
};
