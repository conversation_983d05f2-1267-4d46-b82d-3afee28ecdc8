import { GET, POST } from '@/utils/request';
import { BASE_URL } from '.';

export const uploadCorrectionFile = async (data: {
  fileKeys: string;
  compositionRuleId: string;
}) => {
  return POST(`/composition/file/upload`, data, {
    baseURL: BASE_URL
  });
};

export const zipByFileKey = async (fileKeys: string) => {
  return POST(
    `/composition/file/zipByFileKey`,
    {
      fileKeys
    },
    {
      baseURL: BASE_URL
    }
  );
};
