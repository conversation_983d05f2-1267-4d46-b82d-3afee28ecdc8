import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex, useDisclosure } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import React, { useEffect, useState, useMemo } from 'react';
import { useSystemStore } from '@/store/useSystemStore';
import ChatPanel from './components/ChatPanel';
import PageLayout from './components/PageLayout';
import HomeNavbar from './components/HomeNavbar';
import { useChatStore } from '@/store/useChatStore';
import { getClientAppFormDetail } from '@/api/app';
import { ClientAppFormDetailType } from '@/types/api/app';
import { nanoid } from 'nanoid';
import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';

const schoolChat = ({
  appId = '',
  chatId = '',
  chatIdParam = '',
  activeRoute = ''
}: {
  appId?: string;
  chatId?: string;
  chatIdParam?: string;
  activeRoute?: string;
}) => {
  const { isPc } = useSystemStore();
  const newChatId = useMemo(() => (appId && chatIdParam ? '' : nanoid()), [appId, chatIdParam]);

  const { chatData, setClientAppFormDetail, setQuestionFormGuideStep } = useChatStore();

  const { isOpen: isOpenApplist, onOpen: onOpenAppList, onClose: onCloseAppList } = useDisclosure();

  useEffect(() => {
    if (!chatData.appId && (chatData.app as any)?.type !== 'simple') {
      return;
    }

    if (chatData.appId) {
      getClientAppFormDetail(chatData.appId)
        .then((res) => {
          setClientAppFormDetail(res as ClientAppFormDetailType);
          setQuestionFormGuideStep(1);
        })
        .catch((error) => {});
    }
  }, [chatData, setClientAppFormDetail, setQuestionFormGuideStep]);

  return (
    <PageLayout
      {...(!isPc && {
        bgImage: 'url(/imgs/v2/gradient_bg4.png)',
        bgRepeat: 'no-repeat',
        bgSize: '100% auto',
        navbar: (
          <>
            <HomeNavbar onOpenAppList={onOpenAppList} />
          </>
        )
      })}
    >
      <Flex flexDir="column" w="100%" h="100%">
        <Flex h="100%" w="100%" alignItems="stretch">
          <Flex flex="1 0 0" overflow="hidden" pos="relative">
            <ChatPanel
              appId={appId}
              chatId={chatIdParam || newChatId}
              activeRoute={activeRoute}
              flex="1"
            />
          </Flex>
        </Flex>
      </Flex>
    </PageLayout>
  );
};
export async function getServerSideProps(context: any) {
  return {
    props: {
      appId: context.query?.appId || '',
      activeRoute: context.query?.activeRoute || '',
      chatId: context.query?.chatId || '',
      chatIdParam: context.query?.chatIdParam || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default schoolChat;
