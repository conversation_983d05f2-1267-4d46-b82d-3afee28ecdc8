import React, { ReactNode } from 'react';
import { useDisclosure } from '@chakra-ui/react';
import MyMenu from '@/components/MyMenu';

import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import ChatSettingsModal from '../ChatSettingsModal';
import { useChatStore } from '@/store/useChatStore';
import { useSystemStore } from '@/store/useSystemStore';
import { EventNameEnum, eventBus } from '@/utils/eventbus';

const ChatMenu = ({ Button, onChatShare }: { Button?: ReactNode; onChatShare?: () => void }) => {
  const router = useRouter();
  const { isPc } = useSystemStore();
  const { chatData, cleanChat } = useChatStore();
  const {
    isOpen: isOpenSettings,
    onOpen: onOpenSettings,
    onClose: onCloseSettings
  } = useDisclosure();

  const onClearHistory = () => {
    if (chatData.chatId) {
      cleanChat({ chatId: chatData.chatId });
      eventBus.emit(EventNameEnum.historyCleared);
    }
  };

  return (
    <>
      <MyMenu
        trigger="hover"
        placement="bottom"
        width={20}
        Button={Button}
        menuList={[
          ...(isPc
            ? [
                {
                  label: '新建对话',
                  icon: <SvgIcon name="newChat" w="16px" h="16px" />,
                  onClick: () => {
                    const query = { ...router.query };
                    delete query.chatId;
                    router.push({
                      query
                    });
                  }
                }
              ]
            : []),
          {
            label: '重命名',
            icon: <SvgIcon name="settings" w="16px" h="16px" />,
            isDisabled: !chatData.chatId,
            onClick: () => onOpenSettings()
          },
          {
            label: '清空历史',
            icon: <SvgIcon name="brush" w="16px" h="16px" />,
            isDisabled: !chatData.chatId,
            onClick: () => onClearHistory()
          },
          {
            label: '分享会话',
            icon: <SvgIcon name="chatShare" w="16px" h="16px" />,
            isDisabled: !chatData.chatId,
            onClick: () => onChatShare!()
          }
        ]}
      />
      {isOpenSettings && chatData.chatId && (
        <ChatSettingsModal chatId={chatData.chatId} onClose={onCloseSettings} />
      )}
    </>
  );
};

export default ChatMenu;
