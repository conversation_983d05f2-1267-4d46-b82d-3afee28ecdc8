import MobileNavbar from '@/components/MobileNavbar';
import SvgIcon from '@/components/SvgIcon';
import { rpxDim } from '@/utils/chakra';
import { useChatStore } from '@/store/useChatStore';
import { TriangleDownIcon } from '@chakra-ui/icons';
import { Box, Flex, Image } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';
import { useSystemStore } from '@/store/useSystemStore';
const HomeNavbar = ({ onOpenAppList }: { onOpenAppList: () => void }) => {
  const router = useRouter();
  const { isPc } = useSystemStore();

  const { chatData } = useChatStore();

  const [isDomain, setIsDomain] = useState(false);
  useEffect(() => {
    if (!!isBaoAnDomain()) {
      setIsDomain(true);
    } else {
      setIsDomain(false);
    }
  }, [window.location]);

  return (
    <>
      <MobileNavbar
        autoback={false}
        sidebarButton={true}
        title={
          !isPc ? (
            <Flex alignItems="center" cursor="pointer">
              <Image w={rpxDim(40)} h={rpxDim(40)} src={chatData.appAvatarUrl} alt="" />
              <Box
                mx={rpxDim(12)}
                maxW={rpxDim(360)}
                fontSize={rpxDim(32)}
                lineHeight={rpxDim(32)}
                overflow="hidden"
                textOverflow="ellipsis"
                whiteSpace="nowrap"
              >
                {chatData.appName}
              </Box>
            </Flex>
          ) : (
            <Flex alignItems="center" cursor="pointer" onClick={onOpenAppList}>
              <Image w={rpxDim(40)} h={rpxDim(40)} src={chatData.appAvatarUrl} alt="" />
              <Box
                mx={rpxDim(12)}
                maxW={rpxDim(360)}
                fontSize={rpxDim(32)}
                lineHeight={rpxDim(32)}
                overflow="hidden"
                textOverflow="ellipsis"
                whiteSpace="nowrap"
              >
                {chatData.appName}
              </Box>
              <TriangleDownIcon fontSize={rpxDim(24)} />
            </Flex>
          )
        }
        right={
          <Flex alignItems="center" mr={rpxDim(32)}>
            <SvgIcon
              name="newChat"
              w={rpxDim(48)}
              h={rpxDim(48)}
              color="#0A0A0A"
              cursor="pointer"
              onClick={() => {
                const query = { ...router.query };
                delete query.chatId;
                router.push({
                  query
                });
              }}
            />
          </Flex>
        }
        bgColor=""
      />
    </>
  );
};

export default HomeNavbar;
