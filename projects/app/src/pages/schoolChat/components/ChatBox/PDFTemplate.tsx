import React, { useMemo, useState } from 'react';
import { Box, BoxProps, Flex, Image } from '@chakra-ui/react';
import {
  ChatItemValueTypeEnum,
  ChatFileTypeEnum,
  ChatRoleEnum
} from '@/fastgpt/global/core/chat/constants';
import Markdown from '@/components/Markdown';
import ImageBlock from '@/components/Markdown/img/Image';
import { useSystemStore } from '@/store/useSystemStore';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { ChatItemType, ChatSiteItemType } from '@/fastgpt/global/core/chat/type';
import { useChatStore } from '@/store/useChatStore';
import { ChatFileType } from '@/types/api/chat';
import { useUserStore } from '@/store/useUserStore';
import { APP_ICON } from '@/constants/common';
import ChatItemPDFTemplate from './ChatItemPDFTemplate';

interface PDFRenderProps {
  history: ChatSiteItemType[];
}

const PDFTemplate: React.FC<PDFRenderProps> = ({ history: history }) => {
  const { userInfo } = useUserStore();

  const { chatFiles, chatData } = useChatStore();
  const userAvatar = useMemo(() => {
    return userInfo?.avatar;
  }, [userInfo]);
  const appAvatar = useMemo(() => {
    return chatData.appAvatarUrl || APP_ICON;
  }, [chatData]);
  const chatFileMap = useMemo(
    () =>
      chatFiles.reduce((map, it) => ((map[it.id] = it), map), {} as Record<string, ChatFileType>),
    [chatFiles]
  );
  const getChatFilesByIds = (ids: string[]) => {
    return ids.map((it) => chatFileMap[it]).filter((it) => it);
  };

  return (
    <div id="history-pdf">
      <table className="history-table">
        <tbody>
          {history.map((item, index) => (
            <tr key={index} id={item.dataId}>
              {item.obj === 'Human' ? (
                <>
                  <td className="human-message" />
                  <td className="human-message-content">
                    <div className="human-message-inner">
                      <div className="human-message-card">
                        {item.value.map((subItem, subIndex) => (
                          <div key={subIndex}>
                            {subItem.type === ChatItemValueTypeEnum.text && subItem.text ? (
                              <Markdown obj={item.obj} source={subItem.text.content}></Markdown>
                            ) : subItem.type === ChatItemValueTypeEnum.file &&
                              subItem.file?.type === ChatFileTypeEnum.image ? (
                              <ImageBlock src={subItem.file?.url} />
                            ) : null}
                          </div>
                        ))}
                      </div>
                      {!!item.value &&
                        ((files) =>
                          !!files.length && (
                            <div
                              style={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                marginTop: '16px',
                                alignItems: 'flex-start'
                              }}
                            >
                              {files.map((it, index) => (
                                <div
                                  key={index}
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    width: '234px',
                                    padding: '12px',
                                    backgroundColor: '#FFFFFF',
                                    boxShadow: '0px 0px 10px 0px rgba(62,71,83,0.12)',
                                    borderRadius: '8px',
                                    marginTop: '16px',
                                    marginLeft: '16px'
                                  }}
                                >
                                  {it.svgIcon ? (
                                    <SvgIcon
                                      name={it.svgIcon}
                                      style={{
                                        flexShrink: '0',
                                        width: '44px',
                                        height: '44px'
                                      }}
                                    />
                                  ) : it.imgIcon ? (
                                    <img
                                      src={it.imgIcon}
                                      alt=""
                                      style={{
                                        flexShrink: '0',
                                        width: '44px',
                                        height: '44px'
                                      }}
                                    />
                                  ) : undefined}
                                  <div
                                    style={{
                                      flex: '1',
                                      marginLeft: '12px',
                                      whiteSpace: 'nowrap',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                      textAlign: 'left'
                                    }}
                                  >
                                    <div
                                      style={{
                                        color: '#1D2129',
                                        fontSize: '14px',
                                        lineHeight: '22px',
                                        overflowX: 'scroll',
                                        scrollbarWidth: 'none'
                                      }}
                                    >
                                      {it.name}
                                    </div>
                                    <div
                                      style={{
                                        marginTop: '4px',
                                        color: '#909399',
                                        fontSize: '13px',
                                        lineHeight: '22px'
                                      }}
                                    >
                                      {it.type}, {it.sizeText}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ))(
                          getChatFilesByIds(
                            item.value
                              ?.filter((it) => it.type === ChatItemValueTypeEnum.file)
                              ?.map((it) => it.file?.fileId!)
                          )
                        )}
                    </div>
                  </td>
                  <td
                    style={{
                      padding: '0',
                      width: '50px',
                      textAlign: 'left',
                      paddingBottom: '32px',
                      verticalAlign: 'top'
                    }}
                  >
                    <ChatAvatar src={userAvatar} type="Human" style={{ marginLeft: '4px' }} />
                  </td>
                </>
              ) : (
                <>
                  <td className="ai-message">
                    <ChatAvatar src={appAvatar} type="AI" style={{ marginRight: '4px' }} />
                  </td>
                  <td className="ai-message-content">
                    <div className="ai-message-inner">
                      <div className="ai-message-card">
                        <ChatItemPDFTemplate key={index} chatItem={item} />
                      </div>
                    </div>
                  </td>
                  <td style={{ padding: '0', width: '50px' }} />
                </>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default PDFTemplate;
function ChatAvatar({ src, type, ...props }: { src?: string; type: 'Human' | 'AI' } & BoxProps) {
  return (
    <Image
      w={respDims(40)}
      h={respDims(40)}
      borderRadius="50%"
      overflow="hidden"
      src={src}
      alt=""
      objectFit="cover"
      {...props}
      style={{
        width: '40px',
        height: '40px',
        borderRadius: '50%',
        overflow: 'hidden',
        objectFit: 'cover'
      }}
    />
  );
}
