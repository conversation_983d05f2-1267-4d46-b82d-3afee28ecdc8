import {
  Box,
  Flex,
  Button,
  Input,
  InputGroup,
  InputRightElement,
  ModalFooter,
  Text,
  Spinner
} from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { Tree } from 'antd';
import { type Key, useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getSpaceTreeList } from '@/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import React from 'react';
import styles from '@/pages/cloud/cloud.module.scss';

interface SelectCloudModalProps {
  onClose: () => void;
  onComplete: (selectedKeys: SelectedCloudProps[]) => void;
  initialSelectedKeys: SelectedCloudProps[];
}

interface SelectedCloudProps {
  key: string;
  title: string;
  description?: string;
  id?: string;
  children?: SelectedCloudProps[];
}
interface TreeDataNode {
  id?: string;
  key: string;
  title: React.ReactNode;
  description?: string;
  spaceName?: string;
  children?: TreeDataNode[];
  isLeaf?: boolean;
}

const SelectCloudModal = ({ onClose, onComplete, initialSelectedKeys }: SelectCloudModalProps) => {
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<{
    checked: Key[];
    halfChecked: Key[];
  }>({
    checked: initialSelectedKeys?.map((item) => item.key) || [],
    halfChecked: []
  });
  const [selectedKeys, setSelectedKeys] = useState<SelectedCloudProps[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);

  const { data: knowledgeTreeData = [], isLoading } = useQuery(['knowledgeTree'], async () => {
    const response = await getSpaceTreeList();
    const formattedData = formatTreeData(response);

    const parentKeys = getAllParentKeys(formattedData);
    setExpandedKeys(parentKeys);

    setTreeData(formattedData);

    if (initialSelectedKeys?.length) {
      const selectedParentKeys = getAllParentKeysForNodes(
        formattedData,
        initialSelectedKeys.map((item) => item.key)
      );
      setSelectedKeys(
        initialSelectedKeys.map((item) => ({
          key: item.key,
          title: item.title,
          description: item.description || ''
        }))
      );
      setExpandedKeys([...new Set([...parentKeys, ...selectedParentKeys])]);
    }

    return formattedData;
  });

  const getAllParentKeys = (nodes: TreeDataNode[]): string[] => {
    const keys: string[] = [];
    const traverse = (nodes: TreeDataNode[]) => {
      nodes.forEach((node) => {
        if (node.children?.length) {
          keys.push(node.key as string);
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return keys;
  };

  const getAllParentKeysForNodes = (nodes: TreeDataNode[], targetKeys: string[]): string[] => {
    const parentKeys: string[] = [];

    const findParents = (nodes: TreeDataNode[], targetKey: string, path: string[] = []) => {
      for (const node of nodes) {
        const currentPath = [...path, node.key as string];

        if (node.children) {
          if (node.children.some((child) => child.key === targetKey)) {
            parentKeys.push(...currentPath);
          }
          findParents(node.children, targetKey, currentPath);
        }
      }
    };

    targetKeys.forEach((key) => {
      findParents(nodes, key);
    });

    return [...new Set(parentKeys)];
  };

  const filterTreeData = (nodes: TreeDataNode[], searchValue: string): TreeDataNode[] => {
    const searchLower = searchValue.toLowerCase();

    const filterNode = (node: TreeDataNode): TreeDataNode | null => {
      const searchableText = [node.spaceName, node.description]
        .filter(Boolean)
        .join(' ')
        .toLowerCase();

      const filteredChildren = node.children
        ?.map(filterNode)
        .filter((n): n is TreeDataNode => n !== null);

      if (
        searchableText.includes(searchLower) ||
        (filteredChildren && filteredChildren.length > 0)
      ) {
        return {
          ...node,
          children: filteredChildren?.length ? filteredChildren : undefined
        };
      }

      return null;
    };

    return nodes.map(filterNode).filter((n): n is TreeDataNode => n !== null);
  };

  const formatTreeData = (nodes: any[]): TreeDataNode[] => {
    return nodes.map((node, i) => ({
      key: node.id,
      spaceName: node.spaceName,
      title: (
        <Flex alignItems="center" w="100%">
          <SvgIcon name="childSpace" mr="8px" w="30px" h="30px" />
          <Flex direction="column" p="2px 4px">
            <Text fontSize="15px" color="#303133" lineHeight="22px" fontWeight="500">
              {node.spaceName}
            </Text>
            {node.description && (
              <Text
                fontSize="14px"
                fontWeight="400"
                color="#606266"
                mt="2px"
                textOverflow="ellipsis"
                overflow="hidden"
                w="400px"
                whiteSpace="nowrap"
              >
                {node.description}
              </Text>
            )}
          </Flex>
        </Flex>
      ),
      description: node.description,
      children: node.children ? formatTreeData(node.children) : undefined,
      isLeaf: !node.children?.length
    }));
  };

  const onCheck = (checked: any, info: any) => {
    setCheckedKeys(checked);
    const selectedNodes = info.checkedNodes.filter((node: TreeDataNode) => node.isLeaf);
    const selectedData = selectedNodes.map((node: TreeDataNode) => ({
      key: node.key,
      title: node.spaceName,
      description: node.description
    }));
    setSelectedKeys(selectedData);
  };

  useEffect(() => {
    if (!searchValue.trim()) {
      setTreeData((prevTreeData) => {
        if (JSON.stringify(prevTreeData) !== JSON.stringify(knowledgeTreeData)) {
          return knowledgeTreeData;
        }
        return prevTreeData;
      });
      return;
    }

    const filteredData = filterTreeData(knowledgeTreeData, searchValue.trim());
    setTreeData(filteredData);
  }, [searchValue, knowledgeTreeData]);

  return (
    <MyModal title="选择数据空间" isOpen isCentered onClose={onClose} minW="700px">
      <Box p="24px 32px">
        <InputGroup mb={4}>
          <Input
            placeholder="请输入关键词，搜索空间名称和空间介绍"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
          <InputRightElement>
            <SvgIcon name="search" />
          </InputRightElement>
        </InputGroup>

        <Box h="420px" overflow="auto" border="1px solid #E5E6EB" borderRadius="8px">
          {isLoading ? (
            <Flex justify="center" p={4}>
              <Spinner />
            </Flex>
          ) : (
            <Tree
              checkable
              selectable={false}
              checkedKeys={checkedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onExpand={(keys) => {
                setExpandedKeys(keys);
                setAutoExpandParent(false);
              }}
              className={styles['ant-tree']}
              onCheck={onCheck}
              treeData={treeData}
            />
          )}
        </Box>
      </Box>

      <ModalFooter>
        <Button
          variant="ghost"
          mr={3}
          _hover={{ bgColor: '#f1edfe', color: '#7D4DFF' }}
          bgColor="#F2F3F5"
          color="#4E5969"
          onClick={onClose}
        >
          取消
        </Button>
        <Button
          colorScheme="purple"
          onClick={() => {
            onComplete(selectedKeys);
            onClose();
          }}
        >
          确定
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default SelectCloudModal;
