import React, {
  useCallback,
  useRef,
  useState,
  useMemo,
  forwardRef,
  useImperativeHandle,
  ForwardedRef,
  useEffect,
  DragEvent
} from 'react';
import Script from 'next/script';
import { throttle } from 'lodash';
import type { ExportChatType, ParseResultProps } from '@/types/chat.d';
import { useAudioPlay } from '@/utils/voice';
import { getErrText } from '@/utils/string';
import { useCopyData } from '@/hooks/useCopyData';
import { useMindMapStore } from '@/store/useMindMapStore';
import { Spin } from 'antd';
import {
  Box,
  Flex,
  BoxProps,
  FlexProps,
  Textarea,
  ChakraProps,
  Center,
  Image,
  Card,
  Input,
  Checkbox,
  useDisclosure,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  keyframes
} from '@chakra-ui/react';
import { EventNameEnum, eventBus } from '@/utils/eventbus';
import { Controller, FieldValues, UseFormReturn, useForm } from 'react-hook-form';
import { htmlTemplate } from '@/constants/chat';
import { useRouter } from 'next/router';
import { useSystemStore } from '@/store/useSystemStore';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { useTranslation } from 'next-i18next';
import { customAlphabet } from 'nanoid';
import {
  chatSearchDataset,
  convertHtmlToPdf,
  convertHtmlToWord,
  fetchChatPptUrl,
  parseUploadFile,
  updateChatItem,
  updateChatUserFeedback
} from '@/api/chat';
import { streamFetch, type StreamResponseType } from '@/utils/fetch';
import Markdown, { CodeClassName } from '@/components/Markdown';
import MyTooltip from '@/components/MyTooltip';
import dynamic from 'next/dynamic';
import PDFTemplate from './PDFTemplate';
import FileInCloud from './FileInCloud';
const FeedbackModal = dynamic(() => import('./FeedbackModal'));
const ReadFeedbackModal = dynamic(() => import('./ReadFeedbackModal'));
const ResponseTags = dynamic(() => import('./ResponseTags'));

import MessageInput, {
  ChatAppType,
  MessageFileType,
  MessageInputRef,
  MessagePromptType,
  RawInputType,
  fileTypeInfos
} from './MessageInput';
import { respDims, rpxDim } from '@/utils/chakra';
import Lottie from '@/components/Lottie';
import { getDropFiles } from '@/utils/drop';
import SvgIcon from '@/components/SvgIcon';
import { PromptExternalTypeEnum } from '@/constants/api/prompt';
import styles from '@/styles/variable.module.scss';
import { formatFileSize } from '@/utils/tools';
import { useChatStore } from '@/store/useChatStore';
import { Toast } from '@/utils/ui/toast';
import { cloneWithStyles, convertHtmlToXhtml } from '@/utils/html';
import { ChatBoxRef, StartChatFnProps, generatingMessageProps } from './type';
import { chats2GPTMessages, updateChatListWithParseResult } from '@/fastgpt/global/core/chat/adapt';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  ChatStatusEnum,
  FeedbackTypeEnum,
  IMG_BLOCK_KEY
} from '@/fastgpt/global/core/chat/constants';
import { SseResponseEventEnum } from '@/fastgpt/global/core/workflow/runtime/constants';
import {
  AIChatItemValueItemType,
  ChatSiteItemType,
  UserChatItemValueItemType
} from '@/fastgpt/global/core/chat/type';
import {
  AppChatConfigType,
  AppTTSConfigType,
  VariableItemType
} from '@/fastgpt/global/core/app/type';
import { accessFileUrlWithFilename, getParseResult, replacePreWithDiv } from '@/utils/chat';
import { useQuery } from '@tanstack/react-query';
import { getFileList } from '@/api/file';
import useCompDom from '@/hooks/useCompDom';
import { ChatFileType } from '@/types/api/chat';
import { downloadFile } from '@/utils/file';
import { VariableInputEnum } from '@/fastgpt/global/core/workflow/constants';
import MySelect from '@/components/MySelect';
import { APP_ICON } from '@/constants/common';
import { useAppStore } from '@/store/useAppStore';
import { ChatBoxMode } from './constant.d';
import CustomTour from '@/components/CustomTour';
import useFilePreview from '@/hooks/useFilePreview';
import FilePreviewModal from '@/components/FilePreviewModal';
import AIResponseBox, { RenderResoningContent } from './AIResponseBox';
import ChatItemPDFTemplate from './ChatItemPDFTemplate';
import ChatItemWordTemplate from './ChatItemWordTemplate';
const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyz1234567890', 24);
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';
import MyMenu from '@/components/MyMenu';

// 添加微信 JS-SDK 的类型声明
declare global {
  interface Window {
    wx: {
      config: (config: {
        debug: boolean;
        appId: string;
        timestamp: string | number;
        nonceStr: string;
        signature: string;
        jsApiList: string[];
      }) => void;
      ready: (callback: () => void) => void;
      error: (callback: (err: any) => void) => void;
    };
  }
}
import { AppContextDetailType } from '@/fastgpt/global/core/dataset/type';

const shine = keyframes`
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
`;

enum AuthorityTypeEnum {
  Invalid = 0,
  Valid = 1
}

const parseFileRegex = /``` parseFiles([\s\S]*?)```([\s\S]*)/;

const messageCardStyle: BoxProps = {
  px: respDims('32rpx', 32),
  py: respDims('28rpx', 20),
  paddingBottom: respDims('28rpx'),
  maxW: '100%',
  display: 'inline-block',
  borderRadius: respDims('40rpx', 24),
  fontSize: respDims('28rpx', '14fpx')
};

const chatEditModeStyle: BoxProps = {
  width: respDims('180rpx', 119),
  height: respDims('34rpx'),
  fontWeight: 500,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  background: 'linear-gradient(90deg, #FFDE8D 0%, #FCE5AD 100%)',
  borderRadius: respDims('50rpx', 25),
  fontSize: respDims('24rpx', '12fpx'),
  color: '#9F642D'
};

const aiMessageCardStyle: BoxProps = {
  ...messageCardStyle,
  color: '#1D2129',
  bgColor: '#ffffff !important',
  borderTopLeftRadius: respDims('6rpx', 3),
  boxShadow: '0px 1px 8px 0px rgba(0,0,0,0.12)',
  _hover: {
    '& .chat-controller': {
      visibility: 'visible'
    }
  }
};

const humanMessageCardStyle: BoxProps = {
  ...messageCardStyle,
  color: '##303133',
  bgColor: '#E7F0FF !important',
  borderTopRightRadius: respDims('40rpx', 3),
  borderBottomRightRadius: respDims('6rpx', 24),
  boxShadow: '0px 3px 13px 0px rgba(0,0,0,0.04)',
  paddingBottom: respDims('28rpx', 18),
  _hover: {
    '& .chat-controller': {
      visibility: 'visible'
    }
  }
};

const fullscreenKey = 'lk-fullscreen';

type ChatCompleteItemType = {
  type: ChatItemValueTypeEnum;
  reasoning?: {
    content: string;
  };
  text?: {
    content: string;
  };
};
type Props = {
  showMarkIcon?: boolean; // admin mark dataset
  showEmptyIntro?: boolean;
  appAvatar?: string;
  userAvatar?: string;
  useVision?: boolean;
  useInternet?: boolean;
  active?: boolean; // can use
  chatConfig?: AppChatConfigType;
  isShare?: boolean;
  onShare?: () => void;
  onCancelShare?: () => void;
  mode?: ChatBoxMode;
  // not chat test params
  appId?: string;
  chatId?: string;
  content?: string;
  shareId?: string;
  outLinkUid?: string;
  onUpdateVariable?: (e: Record<string, any>) => void;

  onStartChat?: (e: StartChatFnProps) => Promise<
    StreamResponseType & {
      isNewChat?: boolean;
    }
  >;
  onDelMessage?: (e: { contentId: string }) => Promise<boolean>;
  onUpdateChatItem?: (e: {
    responseChatItemId: string;
    value: string;
    responseData: string;
  }) => Promise<any>;
};

const ChatBox = (
  {
    showMarkIcon = false,
    showEmptyIntro = false,
    appAvatar,
    userAvatar,
    useVision,
    useInternet,
    active = true,
    chatConfig,
    appId,
    chatId,
    content,
    shareId,
    outLinkUid,
    onUpdateVariable,
    onStartChat,
    onDelMessage,
    onUpdateChatItem,
    isShare,
    onShare,
    onCancelShare: onCancelShare,
    mode = ChatBoxMode.Chat,
    ...props
  }: Props & ChakraProps,
  ref: ForwardedRef<ChatBoxRef>
) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const lastScrollTopRef = useRef(0);
  const [fileLoadingMap, setFileLoadingMap] = useState<Record<string, boolean>>({});
  const router = useRouter();
  const { copyData } = useCopyData();
  const { t } = useTranslation();
  const { isPc, setLoading, feConfigs } = useSystemStore();
  const messageInputRef = useRef<MessageInputRef>(null);
  const chatController = useRef<AbortController>();
  const questionGuideController = useRef(new AbortController());
  const isNewChatReplace = useRef(false);
  const {
    chatData,
    setChatFiles: setChatFileStore,
    setChatHistory: setChatHistoryStore,
    initChatInputs,
    appContextDetail,
    setAppContextDetail
  } = useChatStore();

  const setStoreChatHistory = mode === ChatBoxMode.Chat ? setChatHistoryStore : () => {};
  const setStoreChatFiles = mode === ChatBoxMode.Chat ? setChatFileStore : () => {};

  const { previewFile, modalVisible, iframeSrc, modalTitle, closeModal } = useFilePreview();

  const [scale, setScale] = useState(1);

  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const { isOpen, onOpen, onClose } = useDisclosure();

  const [, setRefresh] = useState(false);
  const [isSelectAllShare, setIsSelectAllShare] = useState(false);
  const [chatHistory, setChatHistory] = useState<ChatSiteItemType[]>([]);
  const [feedbackId, setFeedbackId] = useState<string>();
  const [readFeedbackData, setReadFeedbackData] = useState<{
    chatItemId: string;
    content: string;
  }>();
  const [questionGuides, setQuestionGuide] = useState<string[]>([]);

  const [draggingFile, setDraggingFile] = useState(false);
  const draggingFileCounter = useRef(0);

  const [chatItemEditDataId, setChatItemEditDataId] = useState<string>();
  const [chatItemEditValue, setChatItemEditValue] = useState<string>();
  const [parseLoading, setParseLoading] = useState(false);
  const [fileContentLoading, setFileContentLoading] = useState(false);
  const [isDomain, setIsDomain] = useState(false);
  const shouldShowAppTip =
    feConfigs.show_appTip !== false && window.location.href.includes('huayuntiantu.com'); // 默认显示，除非明确设置为false
  console.log('window.location.href', window.location.href);
  const chatItemEditRef = useRef<HTMLTextAreaElement>(null);

  const pendingResendData = useRef<{ dataId: string; inputVal?: string }>();

  const isChatting = useMemo(
    () =>
      chatHistory[chatHistory.length - 1] &&
      chatHistory[chatHistory.length - 1]?.status !== 'finish',
    [chatHistory]
  );

  const { setViewApp } = useAppStore();

  const { onExportChat } = useChatBox();

  const { getFastGPTData } = useSystemStore();
  const { data: fastGPTData } = useQuery(['fastGPTData'], () => getFastGPTData(true));

  const [fullscreen, setFullscreen] = useState(
    () => localStorage.getItem(fullscreenKey) === 'true'
  );

  const maxWidth = fullscreen ? '100%' : '768px';

  useEffect(() => {
    if (initChatInputs && router.query.init && messageInputRef.current) {
      const query = { ...router.query };
      delete query.init;
      router.replace({
        pathname: '/home',
        query
      });
      messageInputRef.current?.setRawInput(initChatInputs as RawInputType);
    }
  }, [initChatInputs, messageInputRef, router]);

  useEffect(() => {
    if (isBaoAnDomain()) {
      setIsDomain(true);
    } else {
      setIsDomain(false);
    }
  }, [window.location]);

  // 存到store
  useEffect(() => {
    setStoreChatHistory(chatHistory);
  }, [chatHistory]);

  const { welcomeText, variableList, questionGuide, ttsConfig, allVariableList } = useMemo(
    () => ({
      welcomeText: chatConfig?.welcomeText || '',
      variableList:
        chatConfig?.variables?.filter((it) => it.type !== VariableInputEnum.custom) || [],
      allVariableList: chatConfig?.variables || [],

      questionGuide: '',
      ttsConfig: chatConfig?.ttsConfig
    }),
    [chatConfig]
  );

  const variablesForm = useForm();

  const [chatFiles, setChatFiles] = useState<ChatFileType[]>([]);

  const chatFileMap = useMemo(
    () =>
      chatFiles.reduce((map, it) => ((map[it.id] = it), map), {} as Record<string, ChatFileType>),
    [chatFiles]
  );

  const getChatFilesByIds = (ids: string[]) => {
    return ids.map((it) => chatFileMap[it]).filter((it) => it);
  };

  const initChatFileIds = useMemo(() => {
    const ids = [] as string[];
    chatHistory.forEach((it) => {
      it.value?.forEach((it) => {
        it.type;
        if (
          it.type === ChatItemValueTypeEnum.file &&
          it.file?.type === ChatFileTypeEnum.file &&
          !chatFileMap[it.file?.fileId!]
        ) {
          ids.push(it.file?.fileId!);
        }
      });
    });
    return ids;
  }, [chatHistory, chatFileMap]);

  // 滚动到底部
  const scrollToBottom = (behavior: 'smooth' | 'auto' = 'smooth') => {
    if (!contentRef.current) return;
    if (behavior === 'auto') {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    } else {
      contentRef.current.scrollTo({
        top: contentRef.current.scrollHeight,
        behavior
      });
    }
    lastScrollTopRef.current = contentRef.current.scrollTop;
  };

  // 聊天信息生成中……获取当前滚动条位置，判断是否需要滚动到底部
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const generatingScroll = useCallback(
    throttle(() => {
      if (!contentRef.current) return;
      const isBottom = contentRef.current.scrollTop >= lastScrollTopRef.current - 100;
      isBottom && scrollToBottom('auto');
    }, 50),
    []
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const generatingMessage = useCallback(
    ({
      event,
      text = '',
      status,
      name,
      reasoningText,
      tool,
      variables
    }: generatingMessageProps) => {
      setChatHistory((state) =>
        state.map((item, index) => {
          if (index !== state.length - 1) return item;
          if (item.obj !== ChatRoleEnum.AI) return item;
          const lastValue: AIChatItemValueItemType =
            item.value && item.value.length
              ? JSON.parse(JSON.stringify(item.value![item.value!.length - 1]))
              : '';
          if (event === SseResponseEventEnum.flowNodeStatus && status) {
            return {
              ...item,
              status,
              moduleName: name
            };
          } else if (event === SseResponseEventEnum.answer && reasoningText) {
            if (lastValue.type === ChatItemValueTypeEnum.reasoning && lastValue.reasoning) {
              lastValue.reasoning.content += reasoningText;
              return {
                ...item,
                value: item.value.slice(0, -1).concat(lastValue)
              };
            } else {
              const val: AIChatItemValueItemType = {
                type: ChatItemValueTypeEnum.reasoning,
                reasoning: {
                  content: reasoningText
                }
              };
              return {
                ...item,
                value: item.value.concat(val)
              };
            }
          } else if (
            (event === SseResponseEventEnum.answer || event === SseResponseEventEnum.fastAnswer) &&
            text
          ) {
            if (!lastValue || !lastValue.text) {
              const newValue: AIChatItemValueItemType = {
                type: ChatItemValueTypeEnum.text,
                text: {
                  content: text
                }
              };
              return {
                ...item,
                value: item.value ? item.value!.concat(newValue) : []
              };
            } else {
              lastValue.text.content += text;
              return {
                ...item,
                value: item.value!.slice(0, -1).concat(lastValue)
              };
            }
          } else if (event === SseResponseEventEnum.toolCall && tool) {
            const val: AIChatItemValueItemType = {
              type: ChatItemValueTypeEnum.tool,
              tools: [tool]
            };
            return {
              ...item,
              value: item.value!.concat(val)
            };
          } else if (
            event === SseResponseEventEnum.toolParams &&
            tool &&
            lastValue.type === ChatItemValueTypeEnum.tool &&
            lastValue?.tools
          ) {
            lastValue.tools = lastValue.tools.map((item) => {
              if (item.id === tool.id) {
                item.params += tool.params;
              }
              return item;
            });
            return {
              ...item,
              value: item.value!.slice(0, -1).concat(lastValue)
            };
          } else if (event === SseResponseEventEnum.toolResponse && tool) {
            // replace tool response
            return {
              ...item,
              value: item.value!.map((val) => {
                if (val.type === ChatItemValueTypeEnum.tool && val.tools) {
                  const tools = val.tools.map((item) =>
                    item.id === tool.id ? { ...item, response: tool.response } : item
                  );
                  return {
                    ...val,
                    tools
                  };
                }
                return val;
              })
            };
          } else if (event === SseResponseEventEnum.updateVariables && variables) {
            variablesForm.reset(variables);
          }

          return item;
        })
      );
      generatingScroll();
    },
    [generatingScroll, setChatHistory, variablesForm]
  );

  const getHistory = useCallback(() => {
    return chatHistory.map((item) => {
      return {
        ...item
      };
    });
  }, [chatHistory]);

  // 重置输入内容
  const resetInputVal = useCallback((val: string | RawInputType) => {
    setTimeout(() => {
      messageInputRef.current?.setRawInput(
        typeof val === 'string'
          ? {
              inputVal: val
            }
          : val
      );
      setRefresh((state) => !state);
    }, 100);
  }, []);

  const abortSendMessage = useCallback((reason: 'stop' | 'cancel' = 'stop') => {
    chatController.current?.abort(reason);
    chatController.current = undefined;
  }, []);

  const removeMessageById = useCallback(
    (dataId?: string) => {
      if (!dataId || !onDelMessage) {
        return;
      }
      // 在删除未结束对话的记录时可能出现先调了删除接口但后端completions未结束，
      // 在completions结束时会保存记录，所以导致未实际删除
      let count = 0;
      const tryRemove = (): any =>
        mode == ChatBoxMode.Chat &&
        ++count < 10 &&
        onDelMessage({ contentId: dataId }).then((res) => {
          !res && setTimeout(tryRemove, count * 1000);
        });
      tryRemove();
    },
    [onDelMessage]
  );

  const removeMessageAt = useCallback(
    (index: number) => {
      // 同时删除用户和AI的聊天记录

      let humanDataId: string | undefined;
      let aiDataId: string | undefined;
      if (chatHistory[index]?.obj === 'Human') {
        humanDataId = chatHistory[index].dataId;
        if (chatHistory[index + 1]?.obj === 'AI') {
          aiDataId = chatHistory[index + 1].dataId;
          if (chatHistory[index + 1].status !== 'finish') {
            abortSendMessage('cancel');
          }
        }
      } else if (chatHistory[index]?.obj === 'AI') {
        if (chatHistory[index - 1]?.obj === 'Human') {
          humanDataId = chatHistory[index - 1].dataId;
        }
        if (chatHistory[index].status !== 'finish') {
          abortSendMessage('cancel');
        }
        aiDataId = chatHistory[index].dataId;
      }

      if (!humanDataId && !aiDataId) {
        return;
      }

      removeMessageById(humanDataId);
      removeMessageById(aiDataId);

      setChatHistory((state) =>
        state.filter((chat) => chat.dataId !== humanDataId && chat.dataId !== aiDataId)
      );
    },
    [chatHistory, removeMessageById, abortSendMessage]
  );

  const sendMessage = useCallback(
    ({
      ocrFileKey,
      chatApp,
      inputVal = '',
      files,
      prompt,
      images,
      rawInput,
      history = chatHistory,
      hideInUI = false
    }: {
      ocrFileKey?: string;
      chatApp?: ChatAppType;
      inputVal?: string;
      prompt?: MessagePromptType;
      files?: MessageFileType[];
      images?: MessageFileType[];
      rawInput?: RawInputType;
      history?: ChatSiteItemType[];
      hideInUI?: boolean;
      appContextDetail?: AppContextDetailType;
    }): Promise<void> => {
      if (inputVal === '蓝青666进入全屏') {
        if (
          window.origin.includes('learnking') ||
          window.origin.includes('-pre') ||
          window.origin.includes('-test') ||
          window.origin.includes('localhost')
        ) {
          setFullscreen(true);
          localStorage.setItem(fullscreenKey, 'true');
        }
      } else if (inputVal === '蓝青666退出全屏') {
        setFullscreen(false);
        localStorage.removeItem(fullscreenKey);
      }

      if (prompt?.externalType === PromptExternalTypeEnum.PPT) {
        fetchChatPptUrl({ topic: inputVal })
          .then((res) => {
            resetInputVal('');
            window.open(res, '_ppt');
          })
          .catch(() => {
            resetInputVal(rawInput || inputVal);
            Toast.error({
              title: '发送失败'
            });
          });
        return Promise.resolve();
      }

      return variablesForm.handleSubmit(async (variables) => {
        if (!onStartChat) return;
        if (isChatting) {
          !hideInUI &&
            Toast.warning({
              title: '正在聊天中...请等待结束'
            });
          return;
        }
        questionGuideController.current?.abort('stop');
        // get input value
        const val = inputVal.trim();

        if (!val && !images?.length && !files?.length) {
          Toast.warning({
            title: '内容为空'
          });
          return;
        }

        const humanDataId = nanoid();
        const aiDataId = nanoid();
        let newChatList: ChatSiteItemType[] = [
          ...history,
          {
            dataId: humanDataId,
            obj: ChatRoleEnum.Human,
            isShareContent: false,
            ocrFileKey: ocrFileKey,
            chatAppId: chatApp?.id,
            chatAppName: chatApp?.name,
            chatAppAvatarUrl: chatApp?.avatarUrl,
            hideInUI: hideInUI,
            value: [
              ...(files?.map((file) => ({
                type: ChatItemValueTypeEnum.file,
                file: {
                  type: ChatFileTypeEnum.file,
                  name: file.name || '',
                  url: accessFileUrlWithFilename(file.fileUrl!, file.name!),
                  fileId: file.fileKey || ''
                }
              })) || []),
              ...(images?.map((image) => ({
                type: ChatItemValueTypeEnum.file,
                file: {
                  type: ChatFileTypeEnum.image,
                  url: accessFileUrlWithFilename(image.fileUrl!, image.name!)
                }
              })) || []),
              ...(prompt && prompt.hiddenContent
                ? [
                    {
                      type: ChatItemValueTypeEnum.prompt,
                      prompt: {
                        content: prompt.hiddenContent
                      }
                    }
                  ]
                : []),
              ...(val
                ? [
                    {
                      type: ChatItemValueTypeEnum.text,
                      text: {
                        content: val
                      }
                    }
                  ]
                : [])
            ] as UserChatItemValueItemType[],
            status: 'finish'
          },
          {
            dataId: aiDataId,
            obj: ChatRoleEnum.AI,
            ocrFileKey: ocrFileKey,
            isShareContent: false,
            chatAppId: chatApp?.id,
            chatAppName: chatApp?.name,
            chatAppAvatarUrl: chatApp?.avatarUrl,
            value: [
              {
                type: ChatItemValueTypeEnum.text,
                text: {
                  content: ''
                }
              }
            ],
            status: 'loading'
          }
        ];
        // 插入内容
        setChatHistory(newChatList);

        // 清空输入内容
        resetInputVal('');
        setQuestionGuide([]);
        setTimeout(() => {
          scrollToBottom();
        }, 100);

        // Only declared variables are kept
        const requestVariables: Record<string, any> = {};
        let parseResult: ParseResultProps | undefined | null;
        allVariableList.forEach((item) => {
          requestVariables[item.key] = variables[item.key] || '';
        });
        let sendMessage = inputVal;
        try {
          // create abort obj
          const abortSignal = new AbortController();
          chatController.current = abortSignal;
          // 用户输入value,不用带文件解析，后端会查出来没有带的
          const originValue = JSON.stringify(newChatList[newChatList.length - 2].value);
          // 解析文件 start
          try {
            setParseLoading(true);
            parseResult = await getParseResult({
              files: files || [],
              images: images || [],
              ocrFileKey,
              appId: appId || '',
              chatId: chatId || '',
              chatConfig
            });

            newChatList = await updateChatListWithParseResult({
              parseResult: parseResult!,
              newChatList,
              humanDataId,
              inputVal: inputVal.trim(),
              ocrFileKey: ocrFileKey || ''
            });
          } catch (error) {
            console.log(error);
          } finally {
            setParseLoading(false);
          }
          // 解析文件 end
          const messages = chats2GPTMessages({
            messages: newChatList,
            reserveId: true
          });
          setFileContentLoading(true);
          const fileContentListPromise = appContextDetail?.files.length
            ? Promise.all(
                appContextDetail.files
                  .filter((item) => item.authority !== AuthorityTypeEnum.Invalid)
                  .map(async (item) => {
                    try {
                      const res = await parseUploadFile({ fileKey: item.fileKey });
                      return {
                        fileContent: res.fileContent,
                        fileName: item.fileName!
                      };
                    } catch (error) {
                      console.error('Error parsing file:', error);
                      return { fileContent: '', fileName: '' };
                    }
                  })
              )
            : Promise.resolve([]);

          const spaceContentListPromise =
            appContextDetail?.spaces.length &&
            appContextDetail.spaces.filter((item) => item.authority !== AuthorityTypeEnum.Invalid)
              .length
              ? chatSearchDataset({
                  messages: [
                    {
                      dataId: nanoid(),
                      content: inputVal,
                      role: 'user'
                    }
                  ],
                  spaceIds:
                    appContextDetail.spaces
                      .filter((item) => item.authority !== AuthorityTypeEnum.Invalid)
                      .map((item) => item.spaceId) || []
                }).catch((error) => {
                  console.error('Error searching dataset:', error);
                  return [];
                })
              : Promise.resolve([]);

          const [fileContentList, spaceContentList] = await Promise.all([
            fileContentListPromise,
            spaceContentListPromise.then((res) => res || [])
          ]);

          setFileContentLoading(false);

          const { responseData, isNewChat = false } = await onStartChat({
            ocrFileKey: ocrFileKey,
            chatAppId: chatApp?.id,
            value: originValue,
            content: sendMessage,
            quotedRef:
              fileContentList.length &&
              fileContentList.every((item) => item.fileContent && item.fileName)
                ? fileContentList
                : undefined,
            searchSelectedRef: spaceContentList.length ? spaceContentList : undefined,
            fileKeys: [
              ...(files?.map((file) => file.fileKey!) || []),
              ...(images?.filter((image) => image.fileKey)?.map((image) => image.fileKey!) || [])
            ],
            messages: messages.slice(0, -1),
            responseChatItemId: aiDataId,
            controller: abortSignal,
            getHistory: getHistory,
            generatingMessage: (e) => {
              generatingMessage({ ...e });
            },
            variables: requestVariables,
            inputVal: sendMessage,
            rawInput: rawInput,
            rawParseResult: parseResult
          });

          if (responseData?.[responseData.length - 1]?.error) {
            Toast.error({
              title: t(responseData[responseData.length - 1].error?.message)
            });
          }

          isNewChatReplace.current = isNewChat;

          // set finish status
          setChatHistory((state) =>
            state.map((item) => {
              if (item.dataId !== aiDataId) return item;
              onUpdateChatItem?.({
                responseChatItemId: aiDataId,
                value: JSON.stringify(item.value),
                responseData: JSON.stringify(responseData)
              });

              return {
                ...item,
                status: 'finish',
                responseData
              };
            })
          );
        } catch (err: any) {
          err &&
            Toast.error({
              title: t(getErrText(err, 'core.chat.error.Chat error')),
              duration: 5000,
              isClosable: true
            });

          if (ocrFileKey) {
            resetInputVal('');
            setChatHistory(newChatList.slice(0, newChatList.length - 2));
          } else if (!err?.responseText) {
            resetInputVal(rawInput || inputVal);
            setChatHistory(newChatList.slice(0, newChatList.length - 2));
          }

          // set finish status
          setChatHistory((state) =>
            state.map((item, index) => {
              if (index !== state.length - 1) return item;
              return {
                ...item,
                status: 'finish'
              };
            })
          );
        }
      })();
    },
    [
      chatHistory,
      generatingMessage,
      isChatting,
      onStartChat,
      resetInputVal,
      allVariableList,
      t,
      variablesForm,
      chatConfig,
      generatingScroll,
      isChatting,
      isPc,
      onStartChat,
      resetInputVal,
      scrollToBottom,
      appId,
      chatId
    ]
  );

  // useFormCreate(sendMessage);

  const resendMessage = useCallback(
    async (dataId: string, inputVal?: string) => {
      if (!onDelMessage) return;

      if (isChatting) {
        abortSendMessage('cancel');
        pendingResendData.current = { dataId, inputVal };
        return;
      }

      let index = chatHistory.findIndex((it) => it.dataId === dataId);
      while (index >= 0 && chatHistory[index].obj !== 'Human') {
        index--;
      }
      if (index < 0) {
        return;
      }

      const delHistory = chatHistory.slice(index);

      setLoading(true);

      try {
        ChatBoxMode.Chat == mode && delHistory.forEach((item) => removeMessageById(item.dataId));
        setChatHistory((state) => (index === 0 ? [] : state.slice(0, index)));

        const item = delHistory[0];

        if (item?.obj !== ChatRoleEnum.Human) {
          return;
        }

        const promptValue = item.value
          .filter((it) => it.type === ChatItemValueTypeEnum.prompt)
          .map((it) => ({
            hiddenContent: it.prompt?.content || ''
          }));

        const files = item.value
          .filter(
            (it) =>
              it.type === ChatItemValueTypeEnum.file && it.file?.type === ChatFileTypeEnum.file
          )
          .map((it) => ({
            name: it.file?.name || '',
            fileUrl: it.file?.url || '',
            fileKey: it.file?.fileId || ''
          }));

        const images = item.value
          .filter(
            (it) =>
              it.type === ChatItemValueTypeEnum.file && it.file?.type === ChatFileTypeEnum.image
          )
          .map((it) => ({
            fileUrl: it.file?.url || ''
          }));
        const { filesResult, userChatInput } = parseFileAndInput(
          item.value
            .map((it) =>
              it.type === ChatItemValueTypeEnum.text
                ? it.text?.content.replace(parseFileRegex, '$2')
                : ''
            )
            .join('')
        );

        sendMessage({
          ocrFileKey: item.ocrFileKey,
          chatApp: item.chatAppId
            ? { id: item.chatAppId, name: item.chatAppName!, avatarUrl: item.chatAppAvatarUrl! }
            : undefined,
          inputVal: inputVal ?? userChatInput,
          files,
          images,
          ...(promptValue && {}),
          history: chatHistory.slice(0, index)
        });
      } catch (error) {
        console.log(error);
      }
      setLoading(false);
    },
    [
      mode,
      chatHistory,
      onDelMessage,
      removeMessageById,
      sendMessage,
      setLoading,
      abortSendMessage,
      isChatting
    ]
  );

  const onStartChatItemEdit = (item: ChatSiteItemType) => {
    setChatItemEditDataId(item.dataId);
    let text =
      item.text ||
      item.value
        .filter((it) => it.type === ChatItemValueTypeEnum.text && it.text?.content)
        .map((it) => it.text?.content)
        .join('\n');
    text = text ? text.replace(new RegExp(`\`\`\`${IMG_BLOCK_KEY}.*\`\`\`\\s?`, 'gs'), '') : '';

    // 解析内容
    // 使用正则表达式解析内容
    const { filesResult, userChatInput } = parseFileAndInput(text);
    text = userChatInput.trim();

    setChatItemEditValue(userChatInput);
  };

  const onConfirmChatItemEdit = () => {
    if (!chatItemEditDataId || !chatItemEditValue?.trim()) return;
    const index = chatHistory.findIndex((it) => it.dataId === chatItemEditDataId);
    if (index < 0) {
      return;
    }
    const history = chatHistory[index];
    const m = history.text
      ? history.text.match(new RegExp(`\`\`\`${IMG_BLOCK_KEY}.*\`\`\``, 'gs'))
      : '';
    resendMessage(
      chatItemEditDataId,
      m?.[0] ? `${m?.[0]}\n${chatItemEditValue}` : chatItemEditValue
    );
    setChatItemEditDataId(undefined);
    setChatItemEditValue(undefined);
  };

  const onCancelChatItemEdit = () => {
    setChatItemEditDataId(undefined);
    setChatItemEditValue(undefined);
  };

  const parseFileAndInput = (
    text: string
  ): {
    filesResult: ParseResultProps;
    userChatInput: string;
  } => {
    // 使用正则表达式解析内容
    const match = text?.match(parseFileRegex);
    let filesResult = [] as any;
    let userChatInput = text;
    if (match) {
      if (match[1].trim()) {
        filesResult = JSON.parse(match[1].trim() || '{}');
      }
      userChatInput = match[2].trim(); // 提取后面的用户输入
    } else {
      console.log('没有匹配到相应的内容');
    }
    return {
      filesResult,
      userChatInput
    };
  };

  const onAddPrompt = (val: string) => {
    messageInputRef.current?.addPrompt(val);
  };

  const onDragEnter = () => {
    draggingFileCounter.current++;
  };

  const onDragLeave = () => {
    if (--draggingFileCounter.current === 0) {
      setDraggingFile(false);
    }
  };

  const onDragOver = (e: DragEvent) => {
    e.preventDefault();
    const accepted = e?.dataTransfer?.items?.[0]?.kind === 'file';
    e.dataTransfer.dropEffect = accepted ? 'copy' : 'none';
    setDraggingFile(accepted);
  };

  const onDrop = (e: DragEvent) => {
    e.preventDefault();
    draggingFileCounter.current = 0;
    if (e.dataTransfer?.items?.length) {
      setDraggingFile(false);
      getDropFiles(Array.from(e.dataTransfer.items)).then((files) => {
        messageInputRef.current?.addFile(files.map((it) => it.rawFile));
      });
    }
  };

  // output data
  useImperativeHandle(ref, () => ({
    getChatHistories: () => chatHistory,
    resetVariables(variables) {
      const data = variablesForm.getValues();
      for (const key in data) {
        data[key] = '';
      }
      variablesForm.reset({
        ...data,
        ...variables
      });
    },
    resetHistory(e) {
      setChatHistory(e);
    },
    scrollToBottom,
    resetInputVal // 新增这一行
  }));

  /* style start */
  const showEmpty = useMemo(
    () =>
      feConfigs?.show_emptyChat &&
      showEmptyIntro &&
      chatHistory.length === 0 &&
      !variableList?.length &&
      !welcomeText,
    [
      chatHistory.length,
      feConfigs?.show_emptyChat,
      showEmptyIntro,
      variableList?.length,
      welcomeText
    ]
  );

  const chatGenerating = useMemo(
    () => isChatting && chatHistory[chatHistory.length - 1]?.status !== ChatStatusEnum.finish,
    [isChatting, chatHistory]
  );

  const cancelShare = () => {
    setChatHistory((state) => state.map((item) => ({ ...item, isShareContent: false })));
    setIsSelectAllShare(false);
    if (onCancelShare) {
      onCancelShare();
    }
  };

  const onCopyContent = () => {
    const filteredData = chatHistory.filter((item) => item.isShareContent);
    if (filteredData.length === 0) {
      Toast.error('请选择对话内容');
      return;
    }
    const dataToCopy = filteredData.map((item) => {
      if (!item.text && !item.value) {
        return '';
      }

      const texts = item.text
        ? [item.text]
        : item.value
            .filter((it) => it.type === 'text' && it.text?.content)
            .map((it) => it.text?.content);

      return texts.join('\n\n');
    });

    copyData(dataToCopy.map((item) => item).join('\n\n'));
  };

  const handleExportChat = async (type: 'pdf' | 'md') => {
    const filteredData = chatHistory.filter((item) => item.isShareContent);
    if (filteredData.length === 0) {
      Toast.error('请选择对话内容');
      return;
    }
    setLoading(true);
    try {
      await onExportChat({
        type,
        history: filteredData,
        filename: chatData.customTitle || chatData.title
      });
    } finally {
      setLoading(false);
    }
  };

  useQuery(['getChatFiles', initChatFileIds], () => getFileList(initChatFileIds), {
    enabled: initChatFileIds?.length > 0,
    onSuccess: (res) => {
      let data =
        res?.map((it) => {
          const type = it.fileName.substring(it.fileName.lastIndexOf('.') + 1).toLocaleLowerCase();
          const typeInfo = fileTypeInfos.find((it) => it.name === type);
          return {
            id: it.fileKey,
            name: it.fileName,
            svgIcon: typeInfo?.svgIcon,
            imgIcon: it.fileUrl,
            type,
            sizeText: formatFileSize(it.fileSize).replaceAll(' ', '')
          };
        }) || [];
      if (onCancelShare) {
        onCancelShare();
      }
      setChatFiles((state) => [...state, ...data]);
      setStoreChatFiles(data);
    }
  });

  // page change and abort request
  useEffect(() => {
    isNewChatReplace.current = false;
    setQuestionGuide([]);
    return () => {
      chatController.current?.abort('leave');
      if (!isNewChatReplace.current) {
        questionGuideController.current?.abort('leave');
      }
    };
  }, [router.query]);

  // add listener
  useEffect(() => {
    const windowMessage = ({ data }: MessageEvent<{ type: 'sendPrompt'; text: string }>) => {
      if (data?.type === 'sendPrompt' && data?.text) {
        sendMessage({
          inputVal: data.text
        });
      }
    };
    window.addEventListener('message', windowMessage);

    eventBus.on(
      EventNameEnum.sendQuestion,
      ({
        text,
        files,
        images,
        ocrFileKey,
        chatApp
      }: {
        text: string;
        files: any;
        images: any;
        ocrFileKey: any;
        chatApp: any;
      }) => {
        if (!text) return;
        sendMessage({
          inputVal: text,
          files,
          images,
          ocrFileKey
        });
      }
    );
    eventBus.on(EventNameEnum.editQuestion, ({ text }: { text: string }) => {
      if (!text) return;
      resetInputVal(text);
    });

    return () => {
      window.removeEventListener('message', windowMessage);
      eventBus.off(EventNameEnum.sendQuestion);
      eventBus.off(EventNameEnum.editQuestion);
    };
  }, [resetInputVal, sendMessage]);

  useEffect(() => {
    if (!chatItemEditRef.current) {
      return;
    }
    const pos = chatItemEditRef.current.value.length;
    chatItemEditRef.current.selectionStart = pos;
    chatItemEditRef.current.selectionEnd = pos;
  }, [chatItemEditDataId]);

  useEffect(() => {
    if (!chatItemEditRef.current) {
      return;
    }
    chatItemEditRef.current.style.width = '1em';
    chatItemEditRef.current.style.whiteSpace = 'nowrap';

    const style = getComputedStyle(chatItemEditRef.current);

    chatItemEditRef.current.style.width =
      chatItemEditRef.current.offsetHeight -
      chatItemEditRef.current.clientHeight +
      parseInt(style.getPropertyValue('padding-right')) +
      chatItemEditRef.current.scrollWidth +
      2 +
      'px';
    chatItemEditRef.current.style.whiteSpace = 'normal';

    chatItemEditRef.current.style.height = '1em';
    chatItemEditRef.current.style.height =
      chatItemEditRef.current.offsetHeight -
      chatItemEditRef.current.clientHeight +
      chatItemEditRef.current.scrollHeight +
      'px';
  }, [chatItemEditDataId, chatItemEditValue]);

  useEffect(() => {
    if (!isChatting && pendingResendData.current) {
      const data = pendingResendData.current;
      pendingResendData.current = undefined;
      resendMessage(data.dataId, data.inputVal);
    }
  }, [isChatting, resendMessage]);

  useEffect(() => {
    setChatHistory((state) =>
      state.map((item) => ({ ...item, isShareContent: isSelectAllShare ? true : false }))
    );
  }, [isSelectAllShare]);

  useEffect(() => {
    content && chatData.appId === appId && sendMessage({ inputVal: content });
  }, [content, chatData.appId]);

  const handleFile = async (file: any, type: string) => {
    const fileUrl = decodeURIComponent(type === 'image' ? file.url : file.imgIcon) || '';
    const res = {
      fileUrl,
      fileType: 1,
      searchContent: '',
      useModal: true,
      title: file.name,
      fileKey: file.id,
      updateFileUrl: true
    };

    try {
      setFileLoadingMap((prev) => ({ ...prev, [file.id]: true })); // 开始加载
      if (type === 'image' && file?.url) {
        setSelectedImage(file.url);
        onOpen();
      } else {
        await previewFile(res); // 假设 previewFile 是一个异步函数
      }
    } catch (error) {
      Toast.error('文件预览失败，请稍后重试');
    } finally {
      setFileLoadingMap((prev) => ({ ...prev, [file.id]: false })); // 结束加载
    }
  };

  const isLastFn = (index: number) => {
    let lastAIIndex = -1;
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].obj === 'AI') {
        lastAIIndex = i;
        break;
      }
    }
    const isLastAIMessage = index === lastAIIndex;
    return isLastAIMessage;
  };

  return (
    <Flex
      position="relative"
      flexDir="column"
      alignItems="center"
      {...props}
      // onDragEnter={onDragEnter}
      // onDragLeave={onDragLeave}
      // onDragOver={onDragOver}
      // onDrop={onDrop}
    >
      <FilePreviewModal
        visible={modalVisible}
        onClose={closeModal}
        iframeSrc={iframeSrc}
        title={modalTitle} // 传递标题
      />
      <Script src="/js/html2pdf.bundle.min.js" strategy="lazyOnload"></Script>

      <Box
        ref={contentRef}
        flex="1"
        w="100%"
        overflow="scroll"
        pl={respDims(32)}
        pr={'30px!important'}
        {...(!isPc && {
          pb: rpxDim(108),
          pr: respDims('50rpx', 50)
        })}
      >
        <Modal isOpen={isOpen} onClose={onClose} isCentered>
          <ModalOverlay />
          <ModalContent boxShadow={'none'} maxW={'auto'} w="auto" bg={'transparent'}>
            <Image
              transform={`scale(${scale})`}
              borderRadius={'md'}
              src={selectedImage || ''}
              alt={''}
              w={'100%'}
              maxH={'80vh'}
              referrerPolicy="no-referrer"
              fallbackSrc={'/imgs/errImg.png'}
              fallbackStrategy={'onError'}
              objectFit={'contain'}
              // onWheel={handleWheel}
            />
          </ModalContent>
          <ModalCloseButton bg={'myWhite.500'} zIndex={999999} />
        </Modal>
        <Flex
          id="chat-container"
          flexDir="column"
          alignItems="center"
          mr={[`-${styles.scrollbarSmWidth}`, `-${styles.scrollbarWidth}`]}
        >
          <Box w="100%" {...(isPc && { maxW: maxWidth })}>
            {showEmpty && <Empty />}
            {/* 用户引导对话开场白 */}

            {!!welcomeText && (
              <WelcomeText
                appAvatar={appAvatar}
                welcomeText={welcomeText}
                onClickAvatar={() => setViewApp(appId)}
              />
            )}

            {!!variableList?.length && (
              <VariableInput
                appAvatar={appAvatar}
                variableList={variableList}
                variablesForm={variablesForm}
              />
            )}

            {/* 用户聊天消息列表 */}
            <Box id={'history'} pt={respDims('32rpx', 32)}>
              {chatHistory.map((item, index) => (
                <>
                  <Box id={item.dataId} key={index}>
                    {item.obj === 'Human' && (
                      <>
                        <Flex
                          w="100%"
                          justifyContent="flex-end"
                          sx={{
                            '& .chakra-link': {
                              color: '#3366ff !important'
                            }
                          }}
                        >
                          {isShare && (
                            <Checkbox
                              mb="28px"
                              flex="1"
                              colorScheme="primary"
                              className={item.isShareContent ? 'select-share' : ''}
                              isChecked={item.isShareContent}
                              onChange={() =>
                                setChatHistory((prevState) => {
                                  const newState = [...prevState];
                                  newState[index] = {
                                    ...newState[index],
                                    isShareContent: !item.isShareContent
                                  };
                                  return newState;
                                })
                              }
                              size="lg"
                            />
                          )}
                          <Box
                            minW="280px"
                            ml={respDims('0rpx', 100, 10)}
                            pb={respDims('32rpx', 32)}
                            textAlign="right"
                          >
                            <Box
                              className="markdown"
                              pos="relative"
                              textAlign="left"
                              {...humanMessageCardStyle}
                            >
                              {chatItemEditDataId === item.dataId ? (
                                <Textarea
                                  ref={chatItemEditRef}
                                  value={chatItemEditValue}
                                  p="0"
                                  borderRadius="0"
                                  maxW="100%"
                                  minW="10em"
                                  minH="1em"
                                  border="none"
                                  outline="none"
                                  boxShadow="none"
                                  resize="none"
                                  autoFocus
                                  overflow="hidden"
                                  _focus={{
                                    bgColor: 'transparent',
                                    border: 'none',
                                    outline: 'none',
                                    boxShadow: 'none'
                                  }}
                                  onChange={(e) => setChatItemEditValue(e.target.value)}
                                />
                              ) : (
                                item.value.map((subItem: UserChatItemValueItemType, subIndex) => (
                                  <Box key={subIndex} {...subItem}>
                                    {subItem.type === ChatItemValueTypeEnum.text && subItem.text ? (
                                      <Markdown source={subItem.text.content} isChatting={false} />
                                    ) : null}
                                  </Box>
                                ))
                              )}
                              {!isShare && (
                                <ChatControllerComponent
                                  chat={item}
                                  isLastAIMessage={isLastFn(index)}
                                  pos="absolute"
                                  right={respDims('16rpx', 24)}
                                  // bottom={respDims('-24rpx', -17)}
                                  chatMode={mode}
                                  showCopy={chatItemEditDataId !== item.dataId}
                                  onDelete={
                                    chatItemEditDataId !== item.dataId && onDelMessage
                                      ? () => {
                                          removeMessageAt(index);
                                        }
                                      : undefined
                                  }
                                  onAddPrompt={
                                    chatItemEditDataId !== item.dataId
                                      ? () => {
                                          onAddPrompt(item.text!);
                                        }
                                      : undefined
                                  }
                                  onEdit={
                                    chatItemEditDataId !== item.dataId
                                      ? () => onStartChatItemEdit(item)
                                      : undefined
                                  }
                                  onConfirm={
                                    chatItemEditDataId === item.dataId
                                      ? () => onConfirmChatItemEdit()
                                      : undefined
                                  }
                                  onCancel={
                                    chatItemEditDataId === item.dataId
                                      ? () => onCancelChatItemEdit()
                                      : undefined
                                  }
                                />
                              )}
                            </Box>

                            {!!item.value &&
                              ((files) =>
                                !!files.length && (
                                  <Flex
                                    {...(isPc
                                      ? { flexWrap: 'wrap', justifyContent: 'end' }
                                      : {
                                          flexDir: 'column',
                                          mt: rpxDim(16),
                                          alignItems: 'flex-end'
                                        })}
                                  >
                                    {files.map((it, index) => (
                                      <Flex
                                        key={index}
                                        onClick={() => handleFile(it, 'file')}
                                        mt={respDims('16rpx', 16)}
                                        ml={respDims('0rpx', 16)}
                                        alignItems="center"
                                        w={respDims('404rpx', 234, 200)}
                                        px={respDims('12rpx', 12)}
                                        py={respDims('10rpx', 10)}
                                        bgColor="#FFFFFF"
                                        boxShadow="0px 0px 10px 0px rgba(62,71,83,0.12)"
                                        borderRadius={respDims('16rpx', 8)}
                                      >
                                        {it.svgIcon ? (
                                          <SvgIcon
                                            flexShrink="0"
                                            name={it.svgIcon}
                                            w={respDims('88rpx', '40fpx')}
                                            h={respDims('88rpx', '40fpx')}
                                          />
                                        ) : it.imgIcon ? (
                                          <Image
                                            flexShrink="0"
                                            w={respDims('88rpx', '40fpx')}
                                            h={respDims('88rpx', '40fpx')}
                                            src={it.imgIcon}
                                            alt=""
                                          />
                                        ) : undefined}

                                        {/* 统一容器 */}
                                        <Box
                                          flex="1"
                                          ml={respDims('12rpx', 12)}
                                          minW="200px" // 固定最小宽度
                                          minH="54px" // 固定最小高度
                                          display="flex"
                                          alignItems="center"
                                          justifyContent="center"
                                          my={respDims('-6rpx', -6)}
                                        >
                                          {fileLoadingMap[it.id] ? ( // 加载中显示动画
                                            <Flex
                                              color="#7D4DFF"
                                              textAlign="left"
                                              pr={respDims(82)}
                                            >
                                              <Box pr={respDims(22)}>正在打开</Box>
                                              <Box>
                                                <Spin></Spin>
                                              </Box>
                                            </Flex>
                                          ) : (
                                            <Box
                                              w="100%"
                                              h="100%"
                                              whiteSpace="nowrap"
                                              overflow="hidden"
                                              textOverflow="ellipsis"
                                              textAlign="left"
                                              css={{
                                                '&:hover': {
                                                  color: '#7D4DFF' // 悬停时的文字颜色
                                                },
                                                '&:hover .name, &:hover .details': {
                                                  color: '#7D4DFF' // 悬停时的文字颜色
                                                }
                                              }}
                                            >
                                              <Box
                                                className="name"
                                                color="#1D2129"
                                                fontSize={respDims('24rpx', '14fpx')}
                                                lineHeight={respDims('44rpx', '22fpx')}
                                                overflow="hidden"
                                                whiteSpace="nowrap"
                                                textOverflow="ellipsis"
                                                pr={respDims(42)}
                                                css={{
                                                  '&::-webkit-scrollbar': {
                                                    display: 'none'
                                                  },
                                                  scrollbarWidth: 'none'
                                                }}
                                              >
                                                {it.name}
                                              </Box>
                                              <Box
                                                className="details"
                                                mt={respDims('0rpx', 4)}
                                                color="#909399"
                                                fontSize={respDims('24rpx', '13fpx')}
                                                lineHeight={respDims('44rpx', '22fpx')}
                                              >
                                                {it.type}, {it.sizeText}
                                              </Box>
                                            </Box>
                                          )}
                                        </Box>
                                      </Flex>
                                    ))}
                                  </Flex>
                                ))(
                                getChatFilesByIds(
                                  item.value
                                    ?.filter((it) => it.type === ChatItemValueTypeEnum.file)
                                    ?.map((it) => it.file?.fileId!)
                                )
                              )}
                            {!!item.value &&
                              ((images) =>
                                !!images.length && (
                                  <Flex
                                    {...(isPc
                                      ? { flexWrap: 'wrap', justifyContent: 'end' }
                                      : {
                                          flexDir: 'column',
                                          mt: rpxDim(16),
                                          alignItems: 'flex-end'
                                        })}
                                  >
                                    {images.map((it, index) => (
                                      <Image
                                        key={index}
                                        {...it}
                                        src={it.file?.url}
                                        onClick={() => handleFile(it.file, 'image')}
                                        alt=""
                                        objectFit="cover"
                                        mt={respDims('16rpx', 16)}
                                        ml={respDims('0rpx', 16)}
                                        alignItems="center"
                                        w={respDims('404rpx', 150, 200)}
                                        px={respDims('12rpx', 12)}
                                        py={respDims('12rpx', 10)}
                                        bgColor="#FFFFFF"
                                        boxShadow="0px 0px 10px 0px rgba(62,71,83,0.12)"
                                        borderRadius={respDims('16rpx', 8)}
                                      ></Image>
                                    ))}
                                  </Flex>
                                ))(
                                item.value?.filter(
                                  (it) =>
                                    it.type === ChatItemValueTypeEnum.file &&
                                    it.file?.type === ChatFileTypeEnum.image
                                )
                              )}

                            <FileInCloud
                              files={item.value
                                ?.filter((it) => it.type === ChatItemValueTypeEnum.file)
                                .map((it) => it.file)}
                            />
                          </Box>

                          {isPc && (
                            <ChatAvatar
                              src={userAvatar}
                              type="Human"
                              flexShrink="0"
                              ml={respDims(14, 4)}
                            />
                          )}
                        </Flex>
                      </>
                    )}
                    {item.obj === 'AI' && (
                      <>
                        <Flex w="100%" pt={respDims(8)}>
                          {isShare && (
                            <Checkbox
                              alignSelf="flex-start"
                              mr="16px"
                              mt="10px"
                              colorScheme="primary"
                              className={item.isShareContent ? 'select-share' : ''}
                              isChecked={item.isShareContent}
                              onChange={() =>
                                setChatHistory((prevState) => {
                                  const newState = [...prevState];
                                  newState[index] = {
                                    ...newState[index],
                                    isShareContent: !item.isShareContent
                                  };
                                  return newState;
                                })
                              }
                              size="lg"
                            />
                          )}
                          {isPc && (
                            <ChatAvatar
                              src={item.chatAppAvatarUrl || appAvatar}
                              type="AI"
                              flexShrink="0"
                              box-shadow="0px 4px 4px 0px rgba(0, 0, 0, 0.07)"
                              mr={respDims(14, 4)}
                              cursor="pointer"
                              onClick={() => setViewApp(item.chatAppId || appId)}
                            />
                          )}

                          <Flex
                            flexDir="column"
                            alignItems="flex-start"
                            minW="280px"
                            mr={respDims('0rpx', 62, 10)}
                            pb={respDims('46rpx', 46)}
                          >
                            <Box
                              pos="relative"
                              className="markdown"
                              {...aiMessageCardStyle}
                              pb={
                                !chatGenerating && index === chatHistory.length - 1
                                  ? respDims('10rpx', 20)
                                  : ''
                              }
                              {...(!isPc && {
                                pt: respDims('36rpx'),
                                pb: respDims('36rpx')
                              })}
                            >
                              {chatGenerating && index === chatHistory.length - 1 && (
                                <Flex alignItems="center">
                                  <Lottie
                                    name="chating"
                                    w={respDims(`${(130 / 3) * 2}rpx`, 130 / 3)}
                                    h={respDims(`${(45 / 3) * 2}rpx`, 45 / 3)}
                                  />

                                  {fileContentLoading ? (
                                    <Box
                                      ml={respDims('12rpx', 6)}
                                      color="#606266"
                                      fontSize={respDims('28rpx', '14fpx')}
                                    >
                                      {fileContentLoading ? '背景知识引用中' : ''}
                                    </Box>
                                  ) : (
                                    <Box
                                      ml={respDims('12rpx', 6)}
                                      color="#606266"
                                      fontSize={respDims('28rpx', '14fpx')}
                                    >
                                      {parseLoading
                                        ? '解析中...'
                                        : chatData?.app?.type === 'advanced'
                                          ? item.moduleName || '生成中...'
                                          : '生成中...'}
                                    </Box>
                                  )}
                                </Flex>
                              )}
                              {!item.hideInUI &&
                                item.value.map((subItem, subIndex) => {
                                  if (subItem.type === ChatItemValueTypeEnum.reasoning) {
                                    return (
                                      <RenderResoningContent
                                        key={subIndex}
                                        index={subIndex}
                                        content={subItem.reasoning?.content || ''}
                                        isChatting={index === chatHistory.length - 1 && isChatting}
                                        isLastResponseValue={
                                          index === chatHistory.length - 1 &&
                                          subIndex === item.value.length - 1
                                        }
                                      />
                                    );
                                  }
                                  if (
                                    subItem.type === ChatItemValueTypeEnum.text &&
                                    subItem.text?.content
                                  ) {
                                    return (
                                      <Markdown
                                        obj={item.obj}
                                        key={subIndex}
                                        source={(() => {
                                          let text = '';
                                          if (item.value && Array.isArray(item.value)) {
                                            text = subItem.text?.content || '';
                                          }
                                          // replace quote tag: [source1] 标识第一个来源，需要提取数字1，从而去数组里查找来源
                                          const quoteReg = /\[source:(.+)\]/g;
                                          const replaceText = text
                                            ? text.replace(quoteReg, `[QUOTE SIGN]($1)`)
                                            : '';

                                          // question guide
                                          if (
                                            index === chatHistory.length - 1 &&
                                            !isChatting &&
                                            questionGuides?.length > 0
                                          ) {
                                            return `${replaceText}\n\`\`\`${
                                              CodeClassName.questionGuide
                                            }\n${JSON.stringify(questionGuides)}`;
                                          }
                                          return replaceText;
                                        })()}
                                        isChatting={index === chatHistory.length - 1 && isChatting}
                                      />
                                    );
                                  }
                                })}

                              <ResponseTags responseData={item.responseData} isShare={!!shareId} />
                              {!(index === chatHistory.length - 1 && isChatting) && !isShare && (
                                <ChatControllerComponent
                                  chat={item}
                                  isLastAIMessage={isLastFn(index)}
                                  {...(!isPc && index === chatHistory.length - 1
                                    ? {
                                        alwaysShow: true,
                                        isFlat: true,
                                        mt: rpxDim(28)
                                      }
                                    : isLastFn(index)
                                      ? {
                                          paddingTop: respDims(16)
                                        }
                                      : {
                                          pos: 'absolute',
                                          left: 0,
                                          bottom: respDims('-34rpx', -22),
                                          minW: '100%',
                                          paddingLeft: respDims('16rpx', 24),
                                          paddingRight: respDims('16rpx', 24)
                                        })}
                                  setChatHistory={setChatHistory}
                                  ttsConfig={ttsConfig}
                                  chatMode={mode}
                                  onRetry={() => resendMessage(item.dataId!)}
                                  onAddUserLike={
                                    item.feedbackType == FeedbackTypeEnum.Downvote
                                      ? undefined
                                      : () => {
                                          if (!item.dataId || !chatId || !appId) return;

                                          const feedbackType =
                                            item.feedbackType == FeedbackTypeEnum.Upvote
                                              ? FeedbackTypeEnum.None
                                              : FeedbackTypeEnum.Upvote;

                                          setChatHistory((state) =>
                                            state.map((chatItem) =>
                                              chatItem.dataId === item.dataId
                                                ? {
                                                    ...chatItem,
                                                    feedbackType
                                                  }
                                                : chatItem
                                            )
                                          );

                                          updateChatItem({
                                            dataId: item.dataId,
                                            feedbackType
                                          });
                                        }
                                  }
                                  onAddUserDislike={(() => {
                                    if (item.feedbackType == FeedbackTypeEnum.Upvote) {
                                      return;
                                    }
                                    if (item.feedbackType == FeedbackTypeEnum.Downvote) {
                                      return () => {
                                        if (!item.dataId || !chatId || !appId) return;
                                        setChatHistory((state) =>
                                          state.map((chatItem) =>
                                            chatItem.dataId === item.dataId
                                              ? {
                                                  ...chatItem,
                                                  feedbackType: FeedbackTypeEnum.None,
                                                  customFeedback: ''
                                                }
                                              : chatItem
                                          )
                                        );
                                        updateChatItem({
                                          dataId: item.dataId,
                                          feedbackType: FeedbackTypeEnum.None,
                                          customFeedback: ''
                                        });
                                      };
                                    } else {
                                      return () => setFeedbackId(item.dataId);
                                    }
                                  })()}
                                />
                              )}
                            </Box>

                            {chatGenerating && index === chatHistory.length - 1 && (
                              <>
                                {parseLoading ? (
                                  <Box
                                    visibility="hidden"
                                    ml={respDims('8rpx', 4)}
                                    mt={respDims('32rpx', 16)}
                                    px={respDims('36rpx', 18)}
                                    py={respDims('6rpx', 3)}
                                    color="#606266"
                                    fontSize={respDims('28rpx', '14fpx')}
                                    lineHeight={respDims('44rpx', '22fpx')}
                                    bgColor="#F2F3F5"
                                    cursor="pointer"
                                    borderRadius={respDims('16rpx', 8)}
                                  ></Box>
                                ) : (
                                  <Box
                                    ml={respDims('8rpx', 4)}
                                    mt={respDims('32rpx', 16)}
                                    px={respDims('36rpx', 18)}
                                    py={respDims('6rpx', 3)}
                                    color="#606266"
                                    fontSize={respDims('28rpx', '14fpx')}
                                    lineHeight={respDims('44rpx', '22fpx')}
                                    bgColor="#F2F3F5"
                                    cursor="pointer"
                                    borderRadius={respDims('16rpx', 8)}
                                    onClick={() => abortSendMessage()}
                                  >
                                    停止生成
                                  </Box>
                                )}
                              </>
                            )}
                          </Flex>
                        </Flex>
                      </>
                    )}
                  </Box>
                </>
              ))}
            </Box>
          </Box>
        </Flex>
      </Box>
      <Box>
        {!isPc && shouldShowAppTip && (
          <Flex
            bottom={'12%'}
            left={'50%'}
            transform="translateX(-50%)"
            position="fixed"
            height={respDims('88rpx')}
            width={'95%'}
            bg="white"
            alignItems="center"
            borderRadius={'8px'}
            boxShadow="0px 1px 10px 0px rgba(0, 0, 0, 0.05), 0px 4px 5px 0px rgba(0, 0, 0, 0.08), 0px 2px 4px -1px rgba(0, 0, 0, 0.12)"
            px={respDims('32rpx')}
            gap={respDims('16rpx')}
            zIndex={999}
            display={localStorage.getItem('hideAppTip') === 'true' ? 'none' : 'flex'}
          >
            <Flex flex={1} alignItems="center" justifyContent="space-between">
              <Box color="#303133" fontSize={respDims('28rpx')}>
                使用「华云天图」小程序体验更多功能
              </Box>
              <Flex alignItems="center">
                <Box
                  as="button"
                  color="#7D4DFF"
                  fontSize={respDims('28rpx')}
                  onClick={() => {
                    window.location.href =
                      'weixin://dl/business/?appid=wxd32f7040ae577a88&path=pages/chat/chat';
                  }}
                  style={{
                    background: 'none',
                    border: 'none',
                    padding: 0,
                    cursor: 'pointer'
                  }}
                >
                  点击使用
                </Box>
                <Box
                  pos="absolute"
                  right={-2}
                  top={respDims('-14rpx')}
                  cursor="pointer"
                  // ml={respDims('24rpx')}
                  onClick={(e) => {
                    e.stopPropagation();
                    localStorage.setItem('hideAppTip', 'true');
                    setRefresh((prev) => !prev);
                  }}
                >
                  <SvgIcon
                    name="xCircle"
                    w={respDims('32rpx')}
                    h={respDims('32rpx')}
                    color="#909399"
                  />
                </Box>
              </Flex>
            </Flex>
          </Flex>
        )}
      </Box>
      {/* message input */}
      {onStartChat && active && (
        <Flex
          flexDir="column"
          alignItems="center"
          w="100%"
          px={respDims('0rpx', 32)}
          // pt={respDims('0rpx', 24)}
        >
          {!isShare ? (
            <>
              <MessageInput
                ref={messageInputRef}
                w="100%"
                {...(isPc && { maxW: maxWidth })}
                appId={appId}
                chatId={chatId}
                mode={mode}
                onSendMessage={sendMessage}
                isChatting={isChatting}
                useVision={useVision}
                useInternet={useInternet}
                chatConfig={chatConfig}
              />

              {isPc && (
                <Box
                  py={respDims(6)}
                  color="#A8ABB2"
                  fontSize={respDims('14fpx')}
                  lineHeight={respDims('28fpx')}
                >
                  内容由AI生成,请核查重要信息
                </Box>
              )}
            </>
          ) : (
            <Flex mb="31px" direction="column" w="80%" pl="32px" pr="32px">
              <Box mx={respDims(16)} w="100%" h={respDims(1)} bgColor="#D1D5DB" />
              <Flex mt="30px" justifyContent="space-around" alignItems="center">
                <Flex alignItems="center">
                  <Checkbox
                    colorScheme="primary"
                    isChecked={isSelectAllShare}
                    onChange={() => {
                      setIsSelectAllShare(!isSelectAllShare);
                    }}
                    size="lg"
                  />
                  <Box
                    color="#303133"
                    fontSize="15px"
                    fontWeight="400"
                    ml="16px"
                    onClick={() => {
                      setIsSelectAllShare(!isSelectAllShare);
                    }}
                    cursor="pointer"
                  >
                    全选
                  </Box>
                </Flex>
                <Flex>
                  <Flex direction="column" alignItems="center" cursor="pointer" mr="32px">
                    <Box
                      padding="6px 8px 4px 8px"
                      backgroundColor="#F3F4F6"
                      mb="8px"
                      borderRadius="50%"
                      onClick={() => onCopyContent()}
                    >
                      <SvgIcon name="chatCopyText" w="24px" h="24px" />
                    </Box>
                    <Box color="#303133" fontSize="14px" fontWeight="400">
                      复制文本
                    </Box>
                  </Flex>
                  <Flex direction="column" alignItems="center" cursor="pointer" mr="32px">
                    <Box
                      padding="8px 8px 4px 8px"
                      backgroundColor="#F3F4F6"
                      mb="8px"
                      borderRadius="50%"
                      onClick={() => handleExportChat('md')}
                    >
                      <SvgIcon name="chatExportMD" w="24px" h="24px" />
                    </Box>
                    <Box color="#303133" fontSize="14px" fontWeight="400">
                      导出md
                    </Box>
                  </Flex>
                  <Flex direction="column" alignItems="center" cursor="pointer" mr="32px">
                    <Box
                      padding="8px 8px 4px 8px"
                      backgroundColor="#F3F4F6"
                      mb="8px"
                      borderRadius="50%"
                      onClick={() => handleExportChat('pdf')}
                    >
                      <SvgIcon name="chatExportPDF" w="24px" h="24px" />
                    </Box>
                    <Box color="#303133" fontSize="14px" fontWeight="400">
                      导出pdf
                    </Box>
                  </Flex>
                </Flex>

                <Flex
                  direction="column"
                  alignItems="center"
                  cursor="pointer"
                  mr="32px"
                  onClick={() => {
                    cancelShare();
                  }}
                >
                  <Box
                    padding="8px 11px 6px 11px"
                    backgroundColor="#F3F4F6"
                    mb="8px"
                    borderRadius="50%"
                  >
                    <SvgIcon name="close" w="14px" h="14px" color="#505968" />
                  </Box>
                  <Box color="#303133" fontSize="15px" cursor="pointer">
                    取消
                  </Box>
                </Flex>
              </Flex>
            </Flex>
          )}
        </Flex>
      )}
      {/* user feedback modal */}
      {!!feedbackId && chatId && appId && (
        <FeedbackModal
          appId={appId}
          chatId={chatId}
          chatItemId={feedbackId}
          shareId={shareId}
          outLinkUid={outLinkUid}
          onClose={() => setFeedbackId(undefined)}
          onSuccess={(content: string) => {
            setChatHistory((state) =>
              state.map((item) =>
                item.dataId === feedbackId
                  ? { ...item, feedbackType: FeedbackTypeEnum.Downvote, customFeedback: content }
                  : item
              )
            );
            setFeedbackId(undefined);
          }}
        />
      )}
      {/* admin read feedback modal */}
      {!!readFeedbackData && (
        <ReadFeedbackModal
          content={readFeedbackData.content}
          onClose={() => setReadFeedbackData(undefined)}
          onCloseFeedback={() => {
            setChatHistory((state) =>
              state.map((chatItem) =>
                chatItem.dataId === readFeedbackData.chatItemId
                  ? { ...chatItem, userBadFeedback: undefined }
                  : chatItem
              )
            );
            try {
              if (!chatId || !appId) return;
              updateChatUserFeedback({
                tenantAppId: appId,
                chatId,
                chatItemId: readFeedbackData.chatItemId
              });
            } catch (error) {}
            setReadFeedbackData(undefined);
          }}
        />
      )}
      {draggingFile && (
        <Center
          flexDir="column"
          position="absolute"
          left="0"
          top="0"
          right="0"
          bottom="0"
          color="#303133"
          bgColor="primary.50"
          fontSize="14px"
          border="2px dotted primary.500"
          borderRadius={respDims(20)}
          zIndex="999"
        >
          <SvgIcon name="chatDrop" w={respDims(118)} h={respDims(80)} />
          <Box mt="21px">释放鼠标，上传文件到输入框</Box>
          <Box mt="10px">最多上传10个文件，每个文件不超过50M</Box>
        </Center>
      )}
    </Flex>
  );
};

export default React.memo(forwardRef(ChatBox));

export const useChatBox = () => {
  const { getCompDom } = useCompDom();

  const onExportChat = useCallback(
    async ({
      type,
      history,
      filename
    }: {
      type: ExportChatType;
      history: ChatSiteItemType[];
      filename?: string;
    }) => {
      const getHistoryHtml = async () => {
        const historyDom = await getCompDom(() => {
          return (
            <>
              <PDFTemplate history={history} />
            </>
          );
        });
        if (!historyDom) return;

        replacePreWithDiv(historyDom);
        const html = htmlTemplate.replace('{{CHAT_CONTENT}}', historyDom.outerHTML);
        return html;
      };

      const html = await getHistoryHtml();

      if (!html) return;

      const payload = {
        filename: `聊天记录-${filename || ''}`,
        htmlStr: html
      };

      const map: Record<ExportChatType, () => Promise<void> | void> = {
        md: () => {
          let content = history
            .map((item) => {
              let role = `${item.obj}:\n`;
              return (
                role +
                item.value
                  .map((item) => {
                    return item.text?.content;
                  })
                  .join('\n\n')
              );
            })
            .join('\n\n');
          downloadFile({
            content,
            type: 'text/markdown',
            filename: `聊天记录-${filename || ''}.md`
          });
        },
        html: async () => {
          const blob = new Blob([html], { type: 'text/html' });
          const url = window.URL.createObjectURL(blob);

          const a = document.createElement('a');
          a.href = url;
          a.download = `${payload.filename}.html`;
          a.click();
          window.URL.revokeObjectURL(url);
        },
        pdf: async () => {
          try {
            await convertHtmlToPdf(payload);
          } catch (error) {
            console.error('Error exporting to PDF:', error);
          }
        }
      };

      await map[type]();
    },
    []
  );

  return {
    onExportChat
  };
};

const WelcomeText = React.memo(function Welcome({
  appAvatar,
  welcomeText,
  onClickAvatar
}: {
  appAvatar?: string;
  welcomeText: string;
  onClickAvatar?: () => void;
}) {
  const { isPc, feConfigs } = useSystemStore();
  return (
    <Flex mt={respDims(32)} pr={respDims(48)}>
      {/* avatar */}
      {isPc && (
        <ChatAvatar
          src={appAvatar}
          type="AI"
          flexShrink="0"
          mr={respDims(14, 4)}
          cursor={onClickAvatar ? 'pointer' : 'default'}
          onClick={onClickAvatar}
        />
      )}

      {/* message */}
      <Box pr={respDims(100, 10)} overflow="hidden" {...aiMessageCardStyle}>
        <Markdown source={`~~~guide \n${welcomeText}`} isChatting={false} obj={ChatRoleEnum.AI} />
      </Box>
    </Flex>
  );
});

const VariableInput = ({
  appAvatar,
  variableList,
  variablesForm
}: {
  appAvatar?: string;
  variableList: VariableItemType[];
  variablesForm: UseFormReturn<FieldValues, any>;
}) => {
  const { isPc } = useSystemStore();
  const { register, setValue, control } = variablesForm;

  return (
    <Flex mt={respDims(32)}>
      {/* avatar */}
      {isPc && <ChatAvatar src={appAvatar} type="AI" flexShrink="0" mr={respDims(14, 4)} />}
      {/* message */}
      <Box textAlign={'left'}>
        <Card
          order={2}
          mt={2}
          w={'400px'}
          {...messageCardStyle}
          bg={'white'}
          boxShadow={'0 0 8px rgba(0,0,0,0.15)'}
        >
          {variableList.map((item) => (
            <Box key={item.id} mb={4}>
              <Box as={'label'} display={'inline-block'} position={'relative'} mb={1}>
                {item.label}
                {item.required && (
                  <Box
                    position={'absolute'}
                    top={'-2px'}
                    right={'-10px'}
                    color={'red.500'}
                    fontWeight={'bold'}
                  >
                    *
                  </Box>
                )}
              </Box>
              {item.type === VariableInputEnum.input && (
                <Input
                  bg={'myWhite.400'}
                  {...register(item.key, {
                    required: item.required
                  })}
                />
              )}
              {item.type === VariableInputEnum.textarea && (
                <Textarea
                  bg={'myWhite.400'}
                  {...register(item.key, {
                    required: item.required
                  })}
                  rows={5}
                  maxLength={4000}
                />
              )}
              {item.type === VariableInputEnum.select && (
                <Controller
                  key={item.key}
                  control={control}
                  name={item.key}
                  rules={{ required: item.required }}
                  render={({ field: { ref, value } }) => {
                    return (
                      <MySelect
                        ref={ref}
                        width={'100%'}
                        list={(item.enums || []).map((item) => ({
                          label: item.value,
                          value: item.value
                        }))}
                        value={value}
                        onchange={(e) => setValue(item.key, e)}
                      />
                    );
                  }}
                />
              )}
            </Box>
          ))}
        </Card>
      </Box>
    </Flex>
  );
};

function ChatAvatar({ src, type, ...props }: { src?: string; type: 'Human' | 'AI' } & BoxProps) {
  return (
    <Image
      w={respDims(40)}
      h={respDims(40)}
      borderRadius="50%"
      overflow="hidden"
      src={src}
      alt=""
      objectFit="cover"
      fallbackSrc={APP_ICON}
      {...props}
      border="2px solid #FFF"
      boxShadow="0px 4px 4px 0px rgba(0, 0, 0, 0.07)"
    />
  );
}

function Empty() {
  return (
    <Box
      pt={6}
      w={'85%'}
      maxW={'600px'}
      m={'auto'}
      alignItems={'center'}
      justifyContent={'center'}
    ></Box>
  );
}

const ChatControllerComponent = React.memo(function ChatControllerComponent({
  chat,
  isLastAIMessage,
  setChatHistory,
  showCopy = true,
  ttsConfig,
  alwaysShow,
  isFlat,
  onRetry,
  onDelete,
  onAddPrompt,
  onEdit,
  onConfirm,
  onCancel,
  onAddUserDislike,
  onAddUserLike,
  ml,
  mr,
  chatMode,
  ...props
}: {
  chat: ChatSiteItemType;
  isLastAIMessage: boolean;
  setChatHistory?: React.Dispatch<React.SetStateAction<ChatSiteItemType[]>>;
  showCopy?: boolean;
  ttsConfig?: AppTTSConfigType;
  alwaysShow?: boolean;
  isFlat?: boolean;
  onRetry?: () => void;
  onDelete?: () => void;
  onAddPrompt?: () => void;
  onEdit?: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
  onAddUserLike?: () => void;
  onAddUserDislike?: () => void;
  chatMode: ChatBoxMode;
} & FlexProps) {
  const { isPc, setLoading } = useSystemStore();
  const { copyData } = useCopyData();
  const { chatData } = useChatStore();
  const { getCompDom } = useCompDom();

  const { audioLoading, audioPlaying, hasAudio, playAudio, cancelAudio } = useAudioPlay({
    ttsConfig
  });
  const { setDeepEditChatItem, setEditType } = useDeepEditStore();
  const { setSelectedChat } = useMindMapStore();
  const router = useRouter();
  const controlIconStyle = {
    w: respDims('36rpx', '24fpx'),
    h: respDims('36rpx', '24fpx'),
    cursor: 'pointer',
    color: '#909399'
  };

  const aloneItemStyle = isPc
    ? {
        w: respDims('32fpx'),
        h: respDims('32fpx'),
        backgroundColor: !isLastAIMessage ? '#ffffff' : '',
        border: !isLastAIMessage ? '1px solid #F3F4F6' : 'none',
        borderRadius: respDims(4)
      }
    : {};

  const onCopy = () => {
    const markdownContainer = document
      .getElementById(chat.dataId!)
      ?.getElementsByClassName('markdown')?.[0];
    if (!markdownContainer) return;
    // 创建一个临时的 div 来处理格式化内容
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = markdownContainer.innerHTML;

    // 移除所有 chat-controller 类的元素
    const controllers = tempDiv.getElementsByClassName('chat-controller');
    while (controllers.length > 0) {
      controllers[0].remove();
    }
    // 移除所有 reasoning-content 类的元素
    const reasoningContents = tempDiv.getElementsByClassName('reasoning-content');
    while (reasoningContents.length > 0) {
      reasoningContents[0].remove();
    }
    // 创建剪贴板数据
    const clipboardData = new ClipboardItem({
      'text/html': new Blob([tempDiv.innerHTML], { type: 'text/html' }),
      'text/plain': new Blob([tempDiv.textContent || ''], { type: 'text/plain' })
    });

    // 使用新的 Clipboard API
    navigator.clipboard
      .write([clipboardData])
      .then(() => {
        console.log('复制成功');
      })
      .catch(() => {
        // 如果新 API 失败，回退到选区方式
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(markdownContainer);

        selection?.removeAllRanges();
        selection?.addRange(range);

        try {
          document.execCommand('copy');
          console.log('复制成功');
        } catch (err) {
          console.error('复制失败:', err);
        }

        window.setTimeout(() => {
          selection?.removeAllRanges();
        }, 0);
      });
  };

  const onDeepEdit = () => {
    // 打开深入编辑弹窗
    //获取到当前的chatItem
    const currentChatItem = chat;
    // 使用redux派发当前currentChatItem和打开弹窗状态
    setEditType('edit');
    setDeepEditChatItem(currentChatItem);
    window.open(`/deepeditor?appId=${chatData?.appId}&mode=chat&init=1`, '_blank');
  };

  const onExportMindMap = async () => {
    const currentChatItem = chat;
    setSelectedChat(currentChatItem);
    window.open(`/MindMapPage?appId=${chatData?.appId}&mode=chat&init=1`, '_blank');
  };

  const onExportPDF = async () => {
    setLoading(true);
    try {
      const chatItemHtml = await (async () => {
        const historyDom = await getCompDom(() => {
          return <ChatItemWordTemplate chatItem={chat} />;
        });
        if (!historyDom) return;
        replacePreWithDiv(historyDom);
        const html = htmlTemplate.replace('{{CHAT_CONTENT}}', historyDom.outerHTML);
        return html;
      })();
      downloadFile('/system/convert/html2pdf', undefined, {
        filename: `当轮聊天记录`,
        htmlStr: chatItemHtml || ''
      });
    } catch (error) {}
    setLoading(false);
  };

  const onExportWord = async () => {
    setLoading(true);
    try {
      const chatItemHtml = await (async () => {
        const historyDom = await getCompDom(() => {
          return <ChatItemWordTemplate chatItem={chat} />;
        });
        if (!historyDom) return;
        replacePreWithDiv(historyDom);
        const html = htmlTemplate.replace('{{CHAT_CONTENT}}', historyDom.outerHTML);
        return html;
      })();

      await convertHtmlToWord({
        filename: `当轮聊天记录`,
        htmlStr: convertHtmlToXhtml(chatItemHtml || '')
      });
    } catch (error) {}
    setLoading(false);
  };

  const { chatId, getCurrentStep } = useChatStore();

  const currentStep = getCurrentStep();

  return (
    <Flex
      className="chat-controller"
      alignItems="center"
      justifyContent="space-between"
      visibility={alwaysShow || audioLoading || isLastAIMessage ? 'visible' : 'hidden'}
      {...props}
    >
      <Flex
        alignItems="center"
        h={respDims('48rpx', '52fpx')}
        {...(!isFlat &&
          !isLastAIMessage && {
            px: respDims('20rpx', 16),
            boxShadow: '0px 1px 8px 0px rgba(0,0,0,0.12)',
            borderRadius: respDims('16rpx', 8)
          })}
        bgColor={isPc ? '#ffffff' : 'rgba(255,255,255,0.9)'}
        borderRadius={'50px'}
        css={{
          '&>:not(:first-child)': {
            marginLeft: respDims('20rpx', 16)
          }
        }}
      >
        {/* {!onEdit ? (
          <Box>
            <Box position="relative">
              <MyTooltip label="深入编辑">
                <SvgIcon onClick={onDeepEdit} name="quill_pen_ai_line" {...controlIconStyle} />
              </MyTooltip>
              {
                <CustomTour
                  chat={chat}
                  chatId={chatId}
                  maskExclude={['.exclude']}
                  targetIdentifier="custom-target"
                />
              }
            </Box>
          </Box>
        ) : null} */}

        {/* {!onEdit ? <Chat2Cloud chatItem={chat} /> : null} */}

        {/* {!onEdit && ( */}
        {/* <MyMenu */}
        {/* Button={<SvgIcon name="classify_add_2_line" {...controlIconStyle} />} */}
        {/* menuList={[ */}
        {/* { */}
        {/* icon: <SvgIcon name="mindMap" {...controlIconStyle} />, */}
        {/* label: '思维导图', */}
        {/* onClick: onExportMindMap */}
        {/* }, */}
        {/* { */}
        {/* icon: <SvgIcon name="doc_line" {...controlIconStyle} />, */}
        {/* label: '导出Word', */}
        {/* onClick: onExportWord */}
        {/* }, */}
        {/* { */}
        {/* icon: <SvgIcon name="pdf_line" {...controlIconStyle} />, */}
        {/* label: '导出PDF', */}
        {/* onClick: onExportPDF */}
        {/* } */}
        {/* ]} */}
        {/* /> */}
        {/* )} */}

        {/* {getCurrentStep() === 1 && (
          <CustomTour
            chat={chat}
            chatId={chatId}
            maskExclude={['.exclude']}
            targetIdentifier="custom-target"
          />
        )} */}

        {/* {!onEdit && isLastAIMessage && <Box w={respDims(2)} h="50%" bg="#E5E7EB"></Box>} */}

        {showCopy && (
          <MyTooltip label="复制">
            <SvgIcon name="copy" {...controlIconStyle} onClick={onCopy} />
          </MyTooltip>
        )}
        {hasAudio &&
          (audioLoading || audioPlaying ? (
            <MyTooltip label="停止播报">
              <Lottie name="waveform" {...controlIconStyle} onClick={cancelAudio} />
            </MyTooltip>
          ) : (
            <MyTooltip label="语音播报">
              <SvgIcon
                name="voice"
                {...controlIconStyle}
                onClick={async () => {
                  const response = await playAudio({
                    buffer: chat.ttsBuffer,
                    chatItemId: chat.dataId,
                    appId: chatData.finalAppId,
                    text: chat.value.map((item) => item.text?.content).join(',') || ''
                  });
                  if (!setChatHistory || !response.buffer) return;
                  setChatHistory((state) =>
                    state.map((item) =>
                      item.dataId === chat.dataId
                        ? {
                            ...item,
                            ttsBuffer: response.buffer
                          }
                        : item
                    )
                  );
                }}
              ></SvgIcon>
            </MyTooltip>
          ))}
        {onEdit && (
          <MyTooltip label="编辑">
            <SvgIcon name="edit" {...controlIconStyle} onClick={onEdit} />
          </MyTooltip>
        )}

        {onDelete && (
          <MyTooltip label="删除">
            <SvgIcon name="trash" {...controlIconStyle} onClick={onDelete} />
          </MyTooltip>
        )}

        {onConfirm && (
          <MyTooltip label="确定">
            <SvgIcon name="check" {...controlIconStyle} onClick={onConfirm} />
          </MyTooltip>
        )}

        {onCancel && (
          <MyTooltip label="取消">
            <SvgIcon name="close" {...controlIconStyle} onClick={onCancel} />
          </MyTooltip>
        )}

        {onRetry && (
          <MyTooltip label="重新生成">
            <SvgIcon name="repeat" {...controlIconStyle} onClick={onRetry} />
          </MyTooltip>
        )}

        {/* {isPc && onAddPrompt && chatMode == ChatBoxMode.Chat && (
          <MyTooltip label="添加快捷指令">
            <SvgIcon name="circlePlus" {...controlIconStyle} onClick={onAddPrompt} />
          </MyTooltip>
        )} */}
      </Flex>
      {(onAddUserLike || onAddUserDislike) && (
        <Flex
          h={respDims('48rpx', '34fpx')}
          ml={respDims('20rpx', 16)}
          {...(!isPc &&
            !isFlat && {
              px: respDims('20rpx', 16),
              boxShadow: '1px 1px 5px rgba(0,0,0,0.12)',
              borderRadius: respDims('16rpx', 8),
              bgColor: 'rgba(255,255,255,0.9)'
            })}
          css={{
            '&>:not(:first-child)': {
              marginLeft: respDims('20rpx', 16)
            }
          }}
        >
          {onAddUserLike && (
            <Center {...aloneItemStyle}>
              <SvgIcon
                name={
                  chat.obj === ChatRoleEnum.AI && chat.feedbackType == FeedbackTypeEnum.Upvote
                    ? 'upvoteFill'
                    : 'upvote'
                }
                {...controlIconStyle}
                onClick={onAddUserLike}
                lineHeight="60px"
              />
            </Center>
          )}

          {onAddUserDislike && (
            <Center {...aloneItemStyle}>
              <SvgIcon
                name={
                  chat.obj === ChatRoleEnum.AI && chat.feedbackType == FeedbackTypeEnum.Downvote
                    ? 'downvoteFill'
                    : 'downvote'
                }
                {...controlIconStyle}
                onClick={onAddUserDislike}
              />
            </Center>
          )}
        </Flex>
      )}
    </Flex>
  );
});
