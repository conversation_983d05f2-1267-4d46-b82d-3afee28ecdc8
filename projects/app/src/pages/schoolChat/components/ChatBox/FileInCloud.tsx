import { ChatFileTypeEnum } from '@/fastgpt/global/core/chat/constants';
import { respDims } from '@/utils/chakra';
import { Box, Button, Center, Flex, Text } from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import { isSpaceFile } from '@/api/cloud';
import Chooser, { ModalModeEnum } from '@/pages/cloud/list/components/Chooser';
import { useOverlayManager } from '@/hooks/useOverlayManager';

type FileInCloudProps = {
  files: (
    | {
        type: ChatFileTypeEnum;
        name?: string | undefined;
        url: string;
        fileId?: string | undefined;
        content?: string | undefined;
      }
    | undefined
  )[];
};

const FileInCloud = ({ files }: FileInCloudProps) => {
  const docFileKeyList = useMemo(() => {
    return files
      .filter((file) => file?.type == ChatFileTypeEnum.file)
      .map((file) => file?.fileId!)
      .filter((file) => file !== undefined);
  }, [files]);
  const [isSpaceFileResult, setIsSpaceFileResult] = useState<string[]>([]);

  const { isLoading, refetch } = useQuery(
    ['isSpaceFile', docFileKeyList],
    () => {
      return isSpaceFile({
        fileKeys: docFileKeyList
      });
    },
    {
      enabled: docFileKeyList.length > 0,
      onSuccess: (data) => {
        if (data) {
          // 使用正则表达式匹配括号内的内容

          const match = data.match(/\[(.*?)\]/);
          if (match && match[1]) {
            const fileNames = match[1].split(',').map((fileName) => fileName.trim());
            setIsSpaceFileResult(fileNames);
          } else {
            setIsSpaceFileResult([]);
          }
        } else {
          setIsSpaceFileResult([]);
        }
      }
    }
  );

  const [close, setClose] = useState(false);
  const { openOverlay } = useOverlayManager();

  const handleSaveToCloud = () => {
    openOverlay({
      Overlay: Chooser,
      props: {
        title: '保存至数据空间',
        modalMode: ModalModeEnum.SaveToLocalUpload,
        showCreateFolderBtn: true,
        files: files
          .filter((file) => isSpaceFileResult.includes(file?.fileId!))
          .map((file) => ({
            fileKey: file?.fileId!,
            fileName: file?.name!,
            fileId: file?.fileId!
          })),

        onSuccess(files, inputFileName) {
          refetch();
        }
      }
    });
  };

  if (docFileKeyList.length === 0 || !isSpaceFileResult.length || isLoading || close) return null;

  return (
    <Flex
      mt={2}
      display="inline-flex"
      borderRadius="33px"
      border="1px solid #fff"
      background="linear-gradient(90deg, rgba(231, 145, 255, 0.21) 0%, rgba(146, 158, 255, 0.21) 100%)"
      backdropFilter="blur(1.2999999523162842px)"
      p={2}
      px={respDims(20, 14)}
      py={respDims(4, 2)}
      alignItems="center"
      _hover={{
        '& .close_icon': {
          display: 'block'
        }
      }}
    >
      <Text mr={respDims(40, 30)}>文件还没有加入数据空间哦～快存进数据空间吧！➡️</Text>
      <Center
        color="primary.500"
        fontSize="12px"
        px={respDims(16, 10)}
        py={respDims(6, 4)}
        _hover={{ bgColor: '#fff' }}
        bgColor="#fff"
        cursor="pointer"
        borderRadius="50px"
        onClick={() => {
          handleSaveToCloud();
        }}
      >
        立即保存
      </Center>
      <SvgIcon
        name="circleClose"
        position="absolute"
        className="close_icon"
        right="-8px"
        top="-8px"
        cursor="pointer"
        w={respDims(30)}
        display="block"
        h={respDims(30)}
        onClick={() => setClose(true)}
      ></SvgIcon>
    </Flex>
  );
};

export default FileInCloud;
