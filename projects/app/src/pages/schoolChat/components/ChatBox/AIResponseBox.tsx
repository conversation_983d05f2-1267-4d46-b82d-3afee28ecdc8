import Markdown from '@/components/Markdown';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  HStack
} from '@chakra-ui/react';
import React from 'react';
import { ChatItemValueTypeEnum } from '@/fastgpt/global/core/chat/constants';
import {
  AIChatItemValueItemType,
  UserChatItemValueItemType
} from '@/fastgpt/global/core/chat/type';
import SvgIcon from '@/components/SvgIcon';

type props = {
  value: UserChatItemValueItemType | AIChatItemValueItemType;
  isLastResponseValue: boolean;
  isChatting: boolean;
  index: number;
};

export const RenderResoningContent = React.memo(function RenderResoningContent({
  content,
  isChatting,
  isLastResponseValue,
  index
}: {
  content: string;
  isChatting: boolean;
  isLastResponseValue: boolean;
  index: number;
}) {
  const showAnimation = isChatting && isLastResponseValue;
  console.log(index);

  return (
    <Accordion
      allowToggle
      defaultIndex={isLastResponseValue ? 0 : undefined}
      mb="10px"
      className="reasoning-content"
    >
      <AccordionItem borderTop={'none'} borderBottom={'none'}>
        <AccordionButton
          w="{'auto'}"
          borderRadius="8px"
          bgColor="#F2F3F5"
          pl={3}
          pr={2.5}
          py={1}
          _hover={{
            bg: 'auto'
          }}
          {...(index !== 0 &&
            index !== 1 && {
              mt: '12px'
            })}
        >
          <HStack spacing={1}>
            <SvgIcon name="chatThink" w="14px" h="14px" />
            <Box fontSize="14px" color="#1D2129" fontWeight="400">
              思考过程
            </Box>
          </HStack>

          {showAnimation && <SvgIcon name="chatLoading" w={'0.85rem'} />}
          <AccordionIcon ml="4px" />
        </AccordionButton>
        <AccordionPanel
          py={0}
          pr={0}
          pl={3}
          mt={2}
          borderLeft={'3px solid'}
          borderColor="#D9D9D9"
          fontWeight="500"
          lineHeight="28px"
          color="#909399"
        >
          <Markdown source={content} showAnimation={showAnimation} />
        </AccordionPanel>
      </AccordionItem>
    </Accordion>
  );
});

const AIResponseBox = ({ value, isLastResponseValue, isChatting, index }: props) => {
  if (value.type === ChatItemValueTypeEnum.reasoning && value.reasoning) {
    return (
      <RenderResoningContent
        index={index}
        isChatting={isChatting}
        isLastResponseValue={isLastResponseValue}
        content={value.reasoning.content}
      />
    );
  }
  return null;
};

export default React.memo(AIResponseBox);
