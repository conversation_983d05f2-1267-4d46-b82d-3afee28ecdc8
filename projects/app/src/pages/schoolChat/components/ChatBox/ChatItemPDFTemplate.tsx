import { ChatItemValueTypeEnum, ChatRoleEnum } from '@/fastgpt/global/core/chat/constants';
import Markdown from '@/components/Markdown';
import { AIChatItemType, ChatSiteItemType } from '@/fastgpt/global/core/chat/type';

const ChatItemPDFTemplate = ({ chatItem }: { chatItem: ChatSiteItemType }) => {
  return (
    <>
      {chatItem.value.map((subItem, subIndex) => {
        if (subItem.type === ChatItemValueTypeEnum.reasoning && subItem.reasoning?.content) {
          return (
            <div key={subIndex}>
              <div style={{ fontSize: '14px', color: '#1D2129', fontWeight: '400' }}>思考过程</div>
              <div
                style={{
                  borderLeft: '3px solid #D9D9D9',
                  paddingLeft: '12px',
                  marginTop: '16px',
                  fontSize: '14px',
                  color: '#909399',
                  lineHeight: '28px',
                  fontWeight: '500',
                  marginBottom: '16px'
                }}
              >
                <Markdown source={subItem.reasoning?.content} />
              </div>
            </div>
          );
        }
        if (subItem.type === ChatItemValueTypeEnum.text && subItem.text?.content) {
          return (
            <Markdown
              key={subIndex}
              obj={chatItem.obj}
              source={(() => {
                const text = subItem.text?.content;
                const quoteReg = /\[QUOTE SIGN\]\((.+)\)/g;
                const replaceText = text ? text.replace(quoteReg, `[QUOTE SIGN]($1)`) : '';
                return replaceText;
              })()}
            />
          );
        }
      })}
    </>
  );
};

export default ChatItemPDFTemplate;
