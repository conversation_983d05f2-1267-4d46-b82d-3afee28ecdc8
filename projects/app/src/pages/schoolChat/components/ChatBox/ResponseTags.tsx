import React, { useMemo, useState } from 'react';
import {
  Flex,
  useDisclosure,
  useTheme,
  Box,
  Grid,
  useBreakpointValue,
  Center
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useSystemStore } from '@/store/useSystemStore';
import type { SearchDataResponseItemType } from '@/types/api/dataset';
import dynamic from 'next/dynamic';
import { strIsLink } from '@/utils/string';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { getFileSvgIcon } from '@/components/SvgIcon/utils';
import { getSourceNameIcon } from '@/utils/dataset';
import { ChatHistoryItemResType, ChatItemType } from '@/fastgpt/global/core/chat/type';
import { FlowNodeTypeEnum } from '@/fastgpt/global/core/workflow/node/constant';
import { DispatchNodeResponseType } from '@/fastgpt/global/core/workflow/runtime/type';

const QuoteModal = dynamic(() => import('./QuoteModal'), { ssr: false });
const ContextModal = dynamic(() => import('./ContextModal'), { ssr: false });
const WholeResponseModal = dynamic(() => import('./WholeResponseModal'), { ssr: false });

const ResponseTags = ({
  responseData = [],
  isShare
}: {
  responseData?: ChatHistoryItemResType[];
  isShare: boolean;
}) => {
  const gridColumnCount =
    useBreakpointValue({
      base: 1,
      sm: 2,
      md: 3
    }) || 2;

  const minGridWidth = gridColumnCount * 120 + 'px';

  const [quoteModalData, setQuoteModalData] = useState<{
    rawSearch: SearchDataResponseItemType[];
    metadata?: {
      collectionId: string;
      sourceId?: string;
      sourceName: string;
    };
  }>();
  const [contextModalData, setContextModalData] =
    useState<DispatchNodeResponseType['historyPreview']>();
  const {
    isOpen: isOpenWholeModal,
    onOpen: onOpenWholeModal,
    onClose: onCloseWholeModal
  } = useDisclosure();

  const { quoteList = [], sourceList = [] } = useMemo(() => {
    const chatData = responseData.find((item) => item.moduleType === FlowNodeTypeEnum.chatNode);
    const quoteList = responseData
      .filter((item) => item.moduleType === FlowNodeTypeEnum.chatNode)
      .map((item) => item.quoteList)
      .flat()
      .filter(Boolean) as SearchDataResponseItemType[];
    const sourceList = quoteList.reduce(
      (acc: Record<string, SearchDataResponseItemType[]>, cur) => {
        if (!acc[cur.collectionId]) {
          acc[cur.collectionId] = [cur];
        }
        return acc;
      },
      {}
    );

    return {
      chatAccount: responseData.filter((item) => item.moduleType === FlowNodeTypeEnum.chatNode)
        .length,
      quoteList,
      sourceList: Object.values(sourceList)
        .flat()
        .map((item) => {
          const dotIndex = item.sourceName.lastIndexOf('.');
          return {
            sourceName: dotIndex > 0 ? item.sourceName.substring(0, dotIndex) : item.sourceName,
            sourceNameSuffix: dotIndex > 0 ? item.sourceName.substring(dotIndex + 1) : '',
            sourceId: item.sourceId,
            icon: getSourceNameIcon({ sourceId: item.sourceId, sourceName: item.sourceName }),
            canReadQuote: !isShare || strIsLink(item.sourceId),
            collectionId: item.collectionId
          };
        }),
      historyPreview: chatData?.historyPreview,
      runningTime: +responseData.reduce((sum, item) => sum + (item.runningTime || 0), 0).toFixed(2)
    };
  }, [isShare, responseData]);

  const [isExpandSource, setIsExpandSource] = useState(false);
  const presentSourceList = useMemo(() => {
    return isExpandSource ? sourceList : sourceList.slice(0, gridColumnCount * 2);
  }, [sourceList, isExpandSource, gridColumnCount]);

  return responseData.length === 0 ? null : (
    <>
      {presentSourceList.length > 0 && (
        <Box w="100%" maxW="100%" minW={minGridWidth}>
          <Flex mt={respDims('20rpx', 22)} w="100%" color="#C0C4CC" alignItems="center">
            <Box fontSize={respDims('24rpx', '12fpx')} lineHeight="2em">
              引用
            </Box>
            <SvgIcon name="chevronRight" w="14px" h="14px" />
            <Box flex="1" h="1px" bgColor="#F3F4F6" />
          </Flex>

          <Box
            mt={respDims('12rpx', 0)}
            w="100%"
            px={respDims('20rpx', 16)}
            py={respDims('26rpx', 10)}
            bgColor="#F8FAFC"
            borderRadius={respDims('16rpx', 8)}
          >
            <Grid
              w="100%"
              gap={respDims('16rpx', 8)}
              gridTemplateColumns={'1fr '.repeat(gridColumnCount).trim()}
            >
              {presentSourceList.map((item) => (
                <Flex
                  key={item.collectionId}
                  px={respDims('8rpx', 4)}
                  py={respDims('12rpx', 4)}
                  bgColor="#ffffff"
                  borderRadius={respDims('10rpx', 5)}
                  overflow="hidden"
                  alignItems="center"
                  cursor="pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setQuoteModalData({
                      rawSearch: quoteList,
                      metadata: {
                        collectionId: item.collectionId,
                        sourceId: item.sourceId,
                        sourceName: `${item.sourceName}.${item.sourceNameSuffix}`
                      }
                    });
                  }}
                >
                  <SvgIcon
                    name={getFileSvgIcon(item.sourceNameSuffix)}
                    w={respDims('36rpx', '18fpx')}
                    h={respDims('36rpx', '18fpx')}
                    flexShrink="0"
                  />

                  <Box
                    ml={respDims('14rpx', 8)}
                    display="flex"
                    alignItems="center"
                    flex="1"
                    color="#303133"
                    fontSize="12px"
                    whiteSpace="nowrap"
                    overflow="hidden"
                  >
                    <Box flex="0 1 auto" overflow="hidden" textOverflow="ellipsis">
                      {item.sourceName + '.'}
                    </Box>
                    {!!item.sourceNameSuffix && <Box flexShrink="0">{item.sourceNameSuffix}</Box>}
                  </Box>
                </Flex>
              ))}
            </Grid>

            {sourceList.length > presentSourceList.length && (
              <Center
                mt={respDims('16rpx', 16)}
                color="#C0C4CC"
                fontSize={respDims('24rpx', '12fpx')}
                cursor="pointer"
                onClick={() => setIsExpandSource(true)}
              >
                <Box>展开</Box>
                <SvgIcon
                  name="chevronDown"
                  w={respDims('20rpx', 14, 14)}
                  h={respDims('20rpx', 14, 14)}
                />
              </Center>
            )}
          </Box>
        </Box>
      )}

      {!!quoteModalData && (
        <QuoteModal
          {...quoteModalData}
          isShare={isShare}
          onClose={() => setQuoteModalData(undefined)}
        />
      )}
      {!!contextModalData && (
        <ContextModal context={contextModalData} onClose={() => setContextModalData(undefined)} />
      )}
      {isOpenWholeModal && (
        <WholeResponseModal response={responseData} isShare={isShare} onClose={onCloseWholeModal} />
      )}
    </>
  );
};

export default React.memo(ResponseTags);
