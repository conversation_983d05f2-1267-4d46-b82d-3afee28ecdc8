import React, { useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useRequest } from '@/hooks/useRequest';
import { useTranslation } from 'next-i18next';
import { updateChatItem } from '@/api/chat';
import { FeedbackTypeEnum } from '@/fastgpt/global/core/chat/constants';

const FeedbackModal = ({
  appId,
  chatId,
  chatItemId,
  onSuccess,
  onClose
}: {
  appId: string;
  chatId: string;
  chatItemId: string;
  shareId?: string;
  outLinkUid?: string;
  onSuccess: (e: string) => void;
  onClose: () => void;
}) => {
  const ref = useRef<HTMLTextAreaElement>(null);
  const { t } = useTranslation();

  const { mutate, isLoading } = useRequest({
    mutationFn: async () => {
      const val = ref.current?.value || t('core.chat.feedback.No Content');
      return updateChatItem({
        dataId: chatItemId,
        feedbackType: FeedbackTypeEnum.Downvote,
        customFeedback: val || ''
      });
    },
    onSuccess() {
      onSuccess(ref.current?.value || t('core.chat.feedback.No Content'));
    },
    successToast: t('core.chat.Feedback Success')
  });

  return (
    <MyModal
      isOpen={true}
      onClose={onClose}
      iconSrc="/imgs/modal/badAnswer.svg"
      title={t('core.chat.Feedback Modal')}
    >
      <ModalBody>
        <Textarea ref={ref} rows={10} placeholder={t('core.chat.Feedback Modal Tip')} />
      </ModalBody>
      <ModalFooter>
        <Button variant={'grayBase'} mr={2} onClick={onClose}>
          {t('common.Close')}
        </Button>
        <Button isLoading={isLoading} onClick={mutate}>
          {t('core.chat.Feedback Submit')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default FeedbackModal;
