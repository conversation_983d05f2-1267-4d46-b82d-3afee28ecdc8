import ChatBox from '../ChatBox';
import { respDims } from '@/utils/chakra';
import { streamFetch } from '@/utils/fetch';
import { useLoading } from '@/hooks/useLoading';
import { chatUpdate, getInitChatInfo, updateChatItem } from '@/api/chat';
import { useChatStore } from '@/store/useChatStore';
import { useUserStore } from '@/store/useUserStore';
import { Box, Center, Flex, Image, useToast } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import { useQuery } from '@tanstack/react-query';
import { nanoid } from 'nanoid';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppStore } from '@/store/useAppStore';
import ChatMenu from '../ChatMenu';
import { useSystemStore } from '@/store/useSystemStore';
import { EventNameEnum, eventBus } from '@/utils/eventbus';
import { getNanoid } from '@/utils/tools';
import {
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  ChatStatusEnum
} from '@/fastgpt/global/core/chat/constants';
import { ChatBoxRef, StartChatFnProps } from '../ChatBox/type';
import { UpdateHistoryProps } from '@/types/api/chat';
import { APP_ICON, LOGO_ICON } from '@/constants/common';
import { Toast } from '@/utils/ui/toast';
import useHistoryData from '@/hooks/useHistoryData';
import SvgIcon from '@/components/SvgIcon';
import { useTenantStore } from '@/store/useTenantStore';
import { ChatSiteItemType, UserChatItemValueItemType } from '@/fastgpt/global/core/chat/type';
import { serviceSideProps } from '@/utils/i18n';

const titlePromptTemplate = `
目标：
-通过一轮对话内容生成一个简短的标题。

回答要求：
- 标题应该与对话内容一致，且与对话内容相关。
- 标题不带标点符号。
- 标题不带任何解释性前缀。
- 标题长度少于20个字。

下面是一个对话内容：

问题:’‘’{{question}}‘’‘

回答:‘’‘{{answer}}‘’‘

`;

type ChatCompleteItemType = {
  type: ChatItemValueTypeEnum;
  reasoning?: {
    content: string;
  };
  text?: {
    content: string;
  };
};
const ChatPanel = ({
  appId,
  chatId,
  activeRoute,
  ...props
}: {
  appId: string;
  activeRoute?: string; // 将 activeRoute 设置为可选参数
  chatId: string;
} & ChakraProps) => {
  const chatBoxRef = useRef<ChatBoxRef>(null);
  const forbidRefresh = useRef(false);
  const toast = useToast({ position: 'top' });
  const router = useRouter();
  const { isPc } = useSystemStore();
  const { generalAppId, loadMyApps, setViewApp, viewApp } = useAppStore();
  const {
    setLastChatAppId,
    setLastChatId,
    pushHistory,
    updateHistory,
    chatData,
    setChatData,
    delOneHistoryItem,
    setIsAppListVisible,
    viewType, // 使用 store 中的 viewType
    setViewType, // 使用 store 中的 setViewType
    formData,
    setChatId,
    clientAppFormDetail,
    setAllIsDisplayeZero,
    allIsDisplayeZero
  } = useChatStore();
  const { tenant } = useTenantStore();

  const { userInfo } = useUserStore();

  const { Loading, setIsLoading } = useLoading();

  const newChatId = useMemo(() => (appId && chatId ? '' : nanoid()), [appId, chatId]);

  const routeHistory = useRef<string[]>([]);

  const [isShare, setIsShare] = useState(false);

  const { updateData } = useHistoryData({ size: 20, current: 1, keyword: '' }, 'histories');

  const startChat = useCallback(
    async ({
      chatAppId,
      value,
      ocrFileKey,
      content,
      fileKeys,
      messages,
      responseChatItemId,
      controller,
      generatingMessage,
      getHistory,
      variables,
      quotedRef,
      searchSelectedRef,
      inputVal,
      rawInput,
      rawParseResult
    }: StartChatFnProps) => {
      const prompts = messages.slice(-1);

      const completionChatId = chatId || newChatId;

      const data = {
        ocrFileKey,
        chatAppId: chatAppId || appId,
        value,
        content,
        fileKeys,
        quotedRef,
        searchSelectedRef,
        messages: prompts,
        responseChatItemId,
        variables,
        tenantAppId: appId,
        chatId: completionChatId,
        rawParseResult
      };

      // 打印传入的data对象
      const { responseText, responseData } = await streamFetch({
        data: data,
        onMessage: (e) => {
          // console.log(111, e);
          console.time('streamFetch');
          generatingMessage(e);
          console.timeEnd('streamFetch');
        },
        abortCtrl: controller
      });
      if (responseText.trim() === '') {
        return Promise.reject('');
      }

      // updateChatItem({
      //   dataId: responseChatItemId,
      //   value: JSON.stringify([{ type: 'text', text: { content: responseText } }]),
      //   content: responseText,
      //   responseData: JSON.stringify(responseData)
      // });

      const newTitle = '新建对话';

      // new chat
      if (completionChatId !== chatId && controller.signal.reason !== 'cancel') {
        const newHistory: UpdateHistoryProps = {
          title: chatData.customTitle || '',
          chatId: completionChatId,
          tenantAppId: appId
        };

        pushHistory(newHistory);
        if (chatData.customTitle) {
          updateHistory(newHistory);
        }
        if (controller.signal.reason !== 'leave') {
          forbidRefresh.current = true;
          router.replace({
            query: {
              chatId: completionChatId,
              appId,
              sceneId: router.query.sceneId
            }
          });
          if (!chatData.customTitle) {
            streamFetch({
              url: '/huayun-ai/client/chat/once',
              data: {
                messages: [
                  {
                    role: prompts[0]?.role,
                    content: titlePromptTemplate
                      .replace(
                        '{{question}}',
                        typeof prompts[0]?.content === 'string'
                          ? prompts[0]?.content
                          : prompts[0]?.content
                              ?.map((it) => (it.type === 'text' ? it.text : ''))
                              .join('') || ''
                      )
                      .replace('{{answer}}', responseText)
                  }
                ],
                variables,
                tenantAppId: appId,
                chatId: completionChatId
              },
              onMessage: () => {},
              abortCtrl: new AbortController()
            }).then(({ responseText: title }) => {
              setChatData((state) =>
                state.chatId === completionChatId ? { ...state, customTitle: title } : state
              );
              updateHistory({ ...newHistory, title: title });
              chatUpdate({ chatId: newHistory.chatId, title }).then((res) => {
                if (res) {
                  updateData({
                    chatId: newHistory.chatId,
                    title
                  });
                }
              });
            });
          }
        }
      } else {
        // update chat
        updateHistory({
          chatId: completionChatId,
          title: chatData.customTitle || '',
          tenantAppId: appId
        });
      }
      // update chat window
      setChatData((state) => ({
        ...state,
        ...(!state.chatId && { chatId: completionChatId }),
        title: newTitle,
        history: chatBoxRef.current?.getChatHistories() || state.history
      }));

      setChatId(completionChatId);

      return { responseText, responseData, isNewChat: forbidRefresh.current };
    },
    [appId, chatId, newChatId, chatData, router, pushHistory, setChatData, updateHistory]
  );

  // get chat app info
  const loadChatInfo = useCallback(
    async ({
      appId,
      chatId,
      loading = false
    }: {
      appId: string;
      chatId: string;
      loading?: boolean;
    }) => {
      try {
        loading && setIsLoading(true);

        const res = await getInitChatInfo({
          tenantAppId: appId,
          chatId
        });

        const history = res.history.map(
          (item) =>
            ({
              ...item,
              dataId: item.dataId || getNanoid(),
              status: ChatStatusEnum.finish,
              value: item.value || [],
              responseData: (item.obj === ChatRoleEnum.AI && item.responseData) || []
            }) as ChatSiteItemType
        );

        setChatData({
          ...res,
          history
        });

        // have records.
        chatBoxRef.current?.resetHistory(history);
        chatBoxRef.current?.resetVariables(res.variables);
        if (res.history.length > 0) {
          setTimeout(() => {
            chatBoxRef.current?.scrollToBottom('auto');
          }, 500);
        }
      } catch (e: any) {
        setLastChatAppId('');
        setLastChatId('');
        e?.code !== 406 && Toast.error('初始化对话失败');
      }
      setIsLoading(false);
      return null;
    },
    [setIsLoading, setChatData, setLastChatAppId, setLastChatId]
  );

  const onDelMessage = useCallback(
    (e: { contentId: string }) => delOneHistoryItem({ ...e, tenantAppId: appId, chatId }),
    [appId, chatId, delOneHistoryItem]
  );

  const onChatShare = () => {
    setIsShare(true);
  };

  useEffect(() => {
    if (chatData && chatData.sceneList) {
      const allZero = chatData.sceneList.every((item) => item.isDisplayed === 1);
      setAllIsDisplayeZero(allZero);
    }
  }, [chatData.sceneList]);

  // 初始化聊天框
  useQuery(['init', { appId, chatId }], () => {
    if (!appId) {
      return loadMyApps()
        .then((apps) => {
          const chatAppId = generalAppId || apps[0]?.id;
          if (chatAppId) {
            return router.replace({
              query: {
                ...router.query,
                appId: chatAppId,
                chatId: chatId,
                sceneId:
                  apps.find((it) => it.id === chatAppId)?.labelList?.[0]?.tenantSceneId ||
                  router.query.sceneId
              }
            });
          } else {
            toast({
              title: '请先创建应用',
              status: 'error'
            });
          }
        })
        .catch(() => {
          toast({
            title: '初始化对话失败',
            status: 'error'
          });
        });
    }

    // store id
    appId && setLastChatAppId(appId);
    setLastChatId(chatId);

    if (forbidRefresh.current) {
      forbidRefresh.current = false;
      return null;
    }

    // 打开右侧弹窗
    setIsAppListVisible(true);
    viewApp && setViewApp();

    return loadChatInfo({
      appId,
      chatId,
      loading: appId !== chatData.appId
    });
  });
  useEffect(() => {
    return () => {
      setChatData((state) => ({
        ...state,
        appId: '',
        chatId: ''
      }));
    };
  }, [setChatData]);

  useEffect(() => {
    eventBus.on(EventNameEnum.historyCleared, () => chatBoxRef.current?.resetHistory([]));
    return () => eventBus.off(EventNameEnum.historyCleared);
  }, []);

  useEffect(() => {
    // 在组件挂载时记录当前路由
    routeHistory.current.push(router.asPath);

    // 在组件卸载时清除路由记录
    return () => {
      routeHistory.current = [];
    };
  }, [router.asPath]);

  const handleBack = () => {
    // 如果最近使用，则返回最近使用页面
    if (router.query.recentlyUsed) {
      router.replace({
        pathname: '/app/list'
      });
    } else {
      router.back();
    }
  };

  const onUpdateChatItem = useCallback(
    (e: { responseChatItemId: string; value: string; responseData: string }) => {
      return updateChatItem({
        dataId: e.responseChatItemId,
        value: e.value,
        content: e.value,
        responseData: e.responseData
      });
    },
    []
  );

  return (
    <Flex
      flexDir="column"
      alignItems="center"
      overflow="hidden"
      {...props}
      backgroundImage={
        tenant?.industry === 1 ? '/imgs/app/app_center_bg.png' : '/imgs/app/app_center_bg4.png'
      }
      backgroundRepeat="no-repeat"
      backgroundSize="cover"
      mr="-14px"
    >
      {isPc && (
        <Center flex="0 0 auto" w="100%" h={respDims(58, 40)} px={respDims(32)}>
          {/* {activeRoute && ( */}
          {/* <Box */}
          {/* w={respDims(100)} */}
          {/* h={respDims(34)} */}
          {/* background="#FFFFFF" */}
          {/* borderRadius="50px" */}
          {/* border="1px solid #D9CCFF" */}
          {/* display="flex" */}
          {/* justifyContent="center" */}
          {/* alignItems="center" */}
          {/* cursor="pointer" */}
          {/* position="absolute" */}
          {/* left={respDims(32)} */}
          {/* onClick={handleBack} */}
          {/* > */}
          {/* <SvgIcon name="chevronLeft" w={respDims(18)} h={respDims(19)} mr={respDims(2)} /> 返回 */}
          {/* </Box> */}
          {/* )} */}
          {/* <ChatMenu
            Button={
              <Flex
                alignItems="center"
                cursor="pointer"
                borderRadius={respDims(8)}
                px={respDims(9)}
                py={respDims(3)}
                _hover={{
                  bgColor: '#EEF1F5'
                }}
              >
                <Image
                  src={chatData.appAvatarUrl || LOGO_ICON}
                  w={respDims(28)}
                  h={respDims(28)}
                  borderRadius="50%"
                  cursor="pointer"
                  alt=""
                  border="1px solid #FFFFFF"
                  onClick={() => setViewApp(chatData.appId)}
                />
                <Box
                  mx={respDims(12)}
                  overflow="hidden"
                  color="#030712"
                  fontSize={respDims(16, 14)}
                  fontWeight="bold"
                  textAlign="center"
                  whiteSpace="nowrap"
                  textOverflow="ellipsis"
                >
                  {chatData.appName || '新建对话'}
                </Box>
                <SvgIcon name="chevronDown" w={respDims(24)} h={respDims(24)}></SvgIcon>
              </Flex>
            }
            onChatShare={onChatShare}
          ></ChatMenu> */}
          <Flex alignItems="center" borderRadius={respDims(8)} px={respDims(9)} py={respDims(3)}>
            <Image
              src={chatData.appAvatarUrl || LOGO_ICON}
              w={respDims(28)}
              h={respDims(28)}
              borderRadius="50%"
              alt=""
              border="1px solid #FFFFFF"
              onClick={() => setViewApp(chatData.appId)}
            />
            <Box
              mx={respDims(12)}
              overflow="hidden"
              color="#030712"
              fontSize={respDims(16, 14)}
              fontWeight="bold"
              textAlign="center"
              whiteSpace="nowrap"
              textOverflow="ellipsis"
            >
              {chatData.appName || '智能客服'}
            </Box>
          </Flex>
        </Center>
      )}

      <ChatBox
        ref={chatBoxRef}
        flex="1"
        w="100%"
        overflow="hidden"
        showEmptyIntro
        appAvatar={chatData.appAvatarUrl || APP_ICON}
        userAvatar={userInfo?.avatar}
        useVision={true}
        chatConfig={chatData.app.chatConfig}
        useInternet={chatData.app?.useInternet}
        onStartChat={startChat}
        onDelMessage={onDelMessage}
        onUpdateChatItem={onUpdateChatItem}
        onCancelShare={() => {
          setIsShare(false);
        }}
        appId={appId}
        isShare={isShare}
        chatId={chatId || newChatId}
      />

      <Loading fixed={false} />
    </Flex>
  );
};

export default React.memo(ChatPanel);
