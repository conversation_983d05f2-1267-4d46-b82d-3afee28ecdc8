import React, { useEffect, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, ModalBody, Input, FormControl, FormLabel } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import { useSystemStore } from '@/store/useSystemStore';
import { useRequest } from '@/hooks/useRequest';
import { useChatStore } from '@/store/useChatStore';
import { useQuery } from '@tanstack/react-query';
import useHistoryData from '@/hooks/useHistoryData';

type FormType = {
  title: string;
};

const ChatSettingsModal = ({
  chatId,
  onClose,
  onSuccess
}: {
  chatId: string;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const { isPc } = useSystemStore();

  const { chatData, setChatData } = useChatStore();

  const { data: chats = [], updateData } = useHistoryData({ size: 20, current: 1 }, 'histories');

  const { register, setValue, handleSubmit } = useForm<FormType>({
    defaultValues: { title: '' }
  });

  const chat = useMemo(() => chats.find((it) => it.chatId === chatId), [chatId, chats]);

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      setChatData({
        ...chatData,
        customTitle: data.title
      });
      return updateData({
        chatId,
        title: data.title
      });
    },
    onSuccess(id: string) {
      onSuccess?.();
      onClose();
    },
    successToast: '操作成功'
  });

  const onSubmit = () => handleSubmit((data) => onClickConfirm(data))();

  useEffect(() => {
    if (chat) {
      setValue('title', chat.title);
    }
  }, [chat, setValue]);

  return (
    <MyModal
      iconSrc="/imgs/module/ai.svg"
      title="编辑对话"
      isOpen
      isCentered
      returnFocusOnClose={false}
    >
      <ModalBody>
        <FormControl isRequired>
          <FormLabel>标题</FormLabel>
          <Input
            autoFocus
            placeholder="请输入标题"
            {...register('title', {
              required: '请输入标题'
            })}
            onKeyDown={(e) => e.key === 'Enter' && onSubmit()}
          />
        </FormControl>
      </ModalBody>

      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          关闭
        </Button>
        <Button isLoading={creating} onClick={onSubmit}>
          确认
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default ChatSettingsModal;
