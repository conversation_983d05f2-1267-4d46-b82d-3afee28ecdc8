import { useSystemStore } from '@/store/useSystemStore';
import { Box, BoxProps } from '@chakra-ui/react';
import { rpxDim } from '@/utils/chakra';

const PageLayout = ({
  navbar,
  children,
  ...props
}: { navbar?: JSX.Element; children: JSX.Element } & BoxProps) => {
  const { isPc } = useSystemStore();
  if (isPc || !navbar) {
    return children;
  }
  return (
    <Box w="100%" h="100%" {...props}>
      {navbar}
      <Box w="100%" h={`calc(100% - ${rpxDim(96)})`}>
        {children}
      </Box>
    </Box>
  );
};

export default PageLayout;
