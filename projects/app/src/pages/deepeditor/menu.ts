export const tableOperations = [
  { title: '插入表格', key: 'inserttable' },
  { title: '删除表格', key: 'deletetable' },
  { title: '表格前插入行', key: 'insertparagraphbeforetable' },
  { title: '前插入行', key: 'insertrow' },
  { title: '删除行', key: 'deleterow' },
  { title: '前插入列', key: 'insertcol' },
  { title: '删除列', key: 'deletecol' },
  { title: '合并多个单元格', key: 'mergecells' },
  { title: '右合并单元格', key: 'mergeright' },
  { title: '下合并单元格', key: 'mergedown' },
  { title: '完全拆分单元格', key: 'splittocells' },
  { title: '拆分成行', key: 'splittorows' },
  { title: '拆分成列', key: 'splittocols' }
];
export const paragraphOptions = [
  { key: 'p', label: '段落', title: '段落' },
  { key: 'h1', label: '标题 1', style: { fontSize: '24px', fontWeight: 'bold' }, title: '标题 1' },
  { key: 'h2', label: '标题 2', style: { fontSize: '20px', fontWeight: 'bold' }, title: '标题 2' },
  { key: 'h3', label: '标题 3', style: { fontSize: '18px', fontWeight: 'bold' }, title: '标题 3' },
  { key: 'h4', label: '标题 4', style: { fontSize: '16px', fontWeight: 'bold' }, title: '标题 4' },
  { key: 'h5', label: '标题 5', style: { fontSize: '14px', fontWeight: 'bold' }, title: '标题 5' }
];

export enum ParagraphKeys {
  段落格式 = '段落格式',
  p = '段落',
  h1 = '标题 1',
  h2 = '标题 2',
  h3 = '标题 3',
  h4 = '标题 4',
  h5 = '标题 5'
}

export const fontOptions = [
  { key: '默认', label: '默认', title: '默认' },
  { key: '宋体', label: '宋体', title: '宋体' },
  { key: '微软雅黑', label: '微软雅黑', title: '微软雅黑' },
  { key: '楷体', label: '楷体', title: '楷体' },
  { key: '黑体', label: '黑体', title: '黑体' },
  { key: '隶书', label: '隶书', title: '隶书' },
  { key: 'arial', label: 'arial', title: 'arial' },
  { key: 'timesNewRoman', label: 'times new roman', title: 'times new roman' }
];

export const listStyleOptions = [
  { key: 'decimal', label: '1,2,3...', title: '1,2,3...' },
  { key: 'lower-alpha', label: 'a,b,c...', title: 'a,b,c...' },
  { key: 'lower-roman', label: 'i,ii,iii...', title: 'i,ii,iii...' },
  { key: 'upper-alpha', label: 'A,B,C...', title: 'A,B,C...' },
  { key: 'upper-roman', label: 'I,II,III...', title: 'I,II,III...' }
];

export const bulletStyleOptions = [
  { key: 'circle', label: '○ 大圆圈', title: '○ 大圆圈' },
  { key: 'disc', label: '● 小黑点', title: '● 小黑点' },
  { key: 'square', label: '■ 小方块', title: '■ 小方块' }
];

export const lineSpacingOptions = [
  { key: '1', label: '1', title: '1' },
  { key: '1.5', label: '1.5', title: '1.5' },
  { key: '1.75', label: '1.75', title: '1.75' },
  { key: '2', label: '2', title: '2' },
  { key: '3', label: '3', title: '3' },
  { key: '4', label: '4', title: '4' },
  { key: '5', label: '5', title: '5' }
];

export const alignOptions = [
  { key: 'left', label: '左对齐', title: '左对齐' },
  { key: 'center', label: '居中', title: '居中' },
  { key: 'right', label: '右对齐', title: '右对齐' },
  { key: 'justify', label: '两端对齐', title: '两端对齐' }
];

export enum AlignOptions {
  对齐方式 = '对齐方式',
  left = '左对齐',
  center = '居中',
  right = '右对齐',
  justify = '两端对齐'
}

export const fontSizeOptions = [
  { key: '10px', size: '10pt', title: '10pt' },
  { key: '11px', size: '11pt', title: '11pt' },
  { key: '12px', size: '12pt', title: '12pt' },
  { key: '14px', size: '14pt', title: '14pt' },
  { key: '16px', size: '16pt', title: '16pt' },
  { key: '18px', size: '18pt', title: '18pt' },
  { key: '20px', size: '20pt', title: '20pt' },
  { key: '22px', size: '22pt', title: '22pt' },
  { key: '24px', size: '24pt', title: '24pt' },
  { key: '36px', size: '36pt', title: '36pt' }
];

const Dom = () => {
  return null;
};

export default Dom;
