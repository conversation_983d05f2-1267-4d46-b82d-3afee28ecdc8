import React, { useState, useRef, useCallback, useEffect } from 'react';
import QuestionForm from '@/pages/home/<USER>/QuestionForm'; // 假设你要使用的表单组件
import { Box, BoxProps, Button } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { useAppStore } from '@/store/useAppStore';
import { DynamicFormDataType } from '@/types/api/chat';
import { getClientAppFormDetail, setSystemTenantGuideFinish } from '@/api/app';
import { useQuery } from '@tanstack/react-query';
import MyBox from '@/components/common/MyBox';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { useDrawer } from '../LeftPlatform/DrawerContext';
import { respDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import { treeToList, treeTraverse } from '@/utils/tree';
import { streamFetch } from '@/utils/fetch';
import { getChatMessages, formData2ChatData, getParseResult } from '@/utils/chat';
import { useRequest } from '@/hooks/useRequest';
import { chats2GPTMessages, updateChatListWithParseResult } from '@/fastgpt/global/core/chat/adapt';
import { useChatMessage } from '@/hooks/chat/useChatMessage';
import { marked } from 'marked';
import { useUpdateEffect } from 'ahooks';
import { Component } from '@/types/api/app';
import { ComponentType } from '@/constants/api/app';
import { FileMetaType } from '@/types/api/file';

// 定义 formRef 的类型
interface QuestionFormRef {
  validateForm: () => Promise<{ valid: boolean; values?: Record<string, any>; error?: any }>;
}

const FormDrawerInner = ({
  appId,
  generateType,
  ...props
}: {
  appId: string;
  generateType: 'content' | 'overview';
} & BoxProps) => {
  const formRef = useRef<QuestionFormRef>(null); // 使用定义的类型
  const { initChatInfo, setActiveTabIndex, formDetail, editor, appDetail } = useDrawer();
  const [buttonText, setButtonText] = useState('开始一键生成');
  const abortCtrl = useRef<AbortController | null>(null);
  const [parseLoading, setParseLoading] = useState(false);
  const {
    setOverviewTreeData,
    overviewTreeData,
    setGenerateFormData,
    generateFormData,
    setReturnFormData,
    setGenerateType
  } = useDeepEditStore();
  const { onMessage, intervalText } = useChatMessage((subText, full_text) => {
    if (generateType == 'content') {
      const html = marked.parse(full_text);
      editor?.setContent(html);
      const editorDocument =
        editor?.iframe.contentDocument || editor?.iframe.contentWindow.document;
      if (editorDocument) {
        editorDocument.documentElement.scrollTop = editorDocument?.documentElement.scrollHeight;
      }
    }
  });

  const getMessages2 = useCallback(async () => {
    try {
      setParseLoading(true);
      if (generateFormData) {
        const chatData = formData2ChatData(generateFormData, formDetail!);

        const { text, images, files, ocrFileKey } = chatData || {};

        const parseResult = await getParseResult({
          files: [...(files || []), ...(images || [])],
          appId: appDetail?.finalAppId || '',
          chatId: '',
          chatConfig: initChatInfo?.app.chatConfig,
          ocrFileKey
        });

        let { newChatList, humanDataId } = getChatMessages({
          text,
          files: [...(files || []), ...(images || [])]
        });

        newChatList = await updateChatListWithParseResult({
          parseResult: parseResult!,
          newChatList,
          humanDataId,
          inputVal: text || '',
          ocrFileKey: ocrFileKey || ''
        });

        const messages = chats2GPTMessages({
          messages: newChatList,
          reserveId: true
        }).slice(0, -1);
        return { messages, parseResult };
      }
    } catch (error) {
      console.error(error);
    } finally {
      setParseLoading(false);
    }

    return [];
  }, [generateFormData, formDetail, initChatInfo, appId]);

  const { mutateAsync: mutateOverviewAsync, isLoading: isGenerating } = useRequest({
    mutationFn: async () => {
      if (formRef.current) {
        const { valid } = await formRef.current.validateForm();
        if (valid && generateFormData) {
          setActiveTabIndex(1);
          setGenerateType(undefined);
          setOverviewTreeData(undefined);
          setTimeout(() => {
            setGenerateType('overview');
          }, 0);
        } else {
          console.error('表单校验失败:');
        }
      }
    }
  });

  const { mutateAsync: mutateContentAsync, isLoading: isGeneratingContent } = useRequest({
    mutationFn: async () => {
      if (formRef.current) {
        try {
          const { valid } = await formRef.current.validateForm();
          if (valid && generateFormData) {
            const { messages, parseResult } = (await getMessages2()) as any;
            const abortSignal = new AbortController();
            abortCtrl.current = abortSignal;

            const { responseText } = await streamFetch({
              url: '/huayun-ai/client/chat/once',
              onMessage,
              abortCtrl: abortSignal,
              data: {
                messages,
                appId: appDetail?.finalAppId,
                rawParseResult: parseResult
              }
            });
            console.log('responseText', responseText);

            if (!responseText) {
              Toast.error('生成内容为空');
              return;
            }
            setButtonText('点击重新生成');
          } else {
            console.error('表单校验失败:');
          }
        } catch (error) {
          Toast.error('生成内容为空');
        }
      }
    }
  });

  useUpdateEffect(() => {
    if (isGeneratingContent) {
      const dots = ['.', '..', '...'];
      let index = 0;
      const interval = setInterval(() => {
        if (parseLoading) {
          setButtonText(`解析中${dots[index]}`);
          index = (index + 1) % dots.length;
        } else {
          setButtonText(`生成中${dots[index]}，点击暂停`);
          index = (index + 1) % dots.length;
        }
      }, 500); // 每500毫秒更新一次

      return () => clearInterval(interval); // 清除定时器
    } else {
      if (generateType == 'content') {
        setButtonText('开始一键生成');
      } else {
        setButtonText('重新生成大纲');
      }
    }
  }, [isGeneratingContent, parseLoading]);

  useEffect(() => {
    setButtonText('开始一键生成');
  }, [generateType]);

  return (
    <MyBox
      display="flex"
      flexDir="column"
      width="100%"
      h="100%"
      position="relative"
      overflow="hidden"
      {...props}
    >
      {formDetail && (
        <>
          <Box flex={1} display="flex" flexDir="column" overflow="hidden">
            <QuestionForm
              ref={formRef}
              clientAppFormDetail={formDetail} // 传递实际的表单详情
              appId={appId} // 传递实际的应用ID
              value={generateFormData}
              layout="narrow"
              onChange={setGenerateFormData}
              boxProps={{
                flex: 1,
                overflow: 'auto',
                px: respDims(24, 16),
                pt: respDims(8)
              }}
            />
          </Box>
          <Box left="0" w="100%" bg="#FFF" p="24px">
            {generateType == 'overview' && (
              <Button
                width="100%"
                fontWeight="500"
                variant="primaryOutline"
                onClick={() => mutateOverviewAsync({})}
                isLoading={isGenerating}
              >
                {overviewTreeData?.length ? '重新生成大纲' : '生成大纲'}
              </Button>
            )}
            {generateType == 'content' && (
              <Button
                width="100%"
                fontWeight="500"
                variant="primaryOutline"
                onClick={() => mutateContentAsync({})}
                isLoading={isGenerating}
              >
                {buttonText}
              </Button>
            )}
          </Box>
        </>
      )}
    </MyBox>
  );
};

export default FormDrawerInner;
