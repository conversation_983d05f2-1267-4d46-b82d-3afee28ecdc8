import React, { useState, useRef, useCallback } from 'react';
import QuestionForm from '@/pages/home/<USER>/QuestionForm'; // 假设你要使用的表单组件
import { Box, BoxProps, Button, HStack } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { useAppStore } from '@/store/useAppStore';
import { DynamicFormDataType } from '@/types/api/chat';
import { getClientAppFormDetail, setSystemTenantGuideFinish } from '@/api/app';
import { useQuery } from '@tanstack/react-query';
import MyBox from '@/components/common/MyBox';
import { FormData2ChatDataType } from '@/types/chat';
import { formData2ChatData, getChatMessages, getParseResult } from '@/utils/chat';
import { useGuideStep } from '../GuideStepModal/GuideStepContext';
import { chats2GPTMessages, updateChatListWithParseResult } from '@/fastgpt/global/core/chat/adapt';
import { streamFetch } from '@/utils/fetch';
import { useChatMessage } from '@/hooks/chat/useChatMessage';
import { treeToList, treeTraverse } from '@/utils/tree';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { Toast } from '@/utils/ui/toast';
import { useRequest } from '@/hooks/useRequest';
import { Component } from '@/types/api/app';
import { ComponentType } from '@/constants/api/app';
import { FileMetaType } from '@/types/api/file';
import { promisifyConfirm } from '@/utils/ui/messageBox';
import { routeros } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { useRouter } from 'next/router';
import { respDims } from '@/utils/chakra';

// 定义 formRef 的类型
interface QuestionFormRef {
  validateForm: () => Promise<{ valid: boolean; values?: Record<string, any>; error?: any }>;
}

const FormModalInner = ({
  appId,
  ...props
}: {
  appId: string;
} & BoxProps) => {
  const { setStep, initChatInfo: initChatInfo, formDetail } = useGuideStep();
  const formRef = useRef<QuestionFormRef>(null); // 使用定义的类型
  const { setGenerateFormData, generateFormData } = useDeepEditStore();
  const router = useRouter();
  const { mutateAsync, isLoading: isGenerating } = useRequest({
    mutationFn: async () => {
      if (formRef.current) {
        const { valid } = await formRef.current.validateForm();
        if (valid) {
          setStep(1);
        } else {
          console.error('表单校验失败:');
        }
      } else {
        console.error('表单校验失败:');
      }
    }
  });

  return (
    <MyBox
      display="flex"
      flexDir="column"
      width="100%"
      h="100%"
      position="relative"
      overflow="hidden"
      {...props}
    >
      {formDetail && (
        <>
          <Box flex={1} display="flex" flexDir="column" overflow="hidden">
            <QuestionForm
              ref={formRef}
              clientAppFormDetail={formDetail} // 传递实际的表单详情
              appId={appId} // 传递实际的应用ID
              value={generateFormData}
              onChange={setGenerateFormData}
              boxProps={{
                flex: 1,
                overflow: 'auto',
                padding: '0 20px',
                css: {
                  '&::-webkit-scrollbar': {
                    width: '7px!important',
                    background: '#f2f2f2'
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#d8d8d8 !important',
                    borderRadius: '20px'
                  },
                  '&::-webkit-scrollbar-thumb:hover': {
                    backgroundColor: '#d8d8d8 !important'
                  }
                }
              }}
              layout="wide"
            />
          </Box>
          <HStack
            left="0"
            w="100%"
            h="80px"
            bg="#FFF"
            px="20px"
            pb="10px"
            borderTop="1px #eee solid"
            spacing={5}
          >
            <Button
              variant="grayBase"
              onClick={() => {
                // 判断路由有没有上一级，没有关闭弹窗

                if (router.query.mode !== 'smartClass') {
                  window.close();
                } else {
                  router.back();
                }
              }}
              w="50%"
            >
              返回上一步
            </Button>
            <Button
              width="50%"
              color="#fff"
              height="36px"
              display="flex"
              justifyContent="center"
              cursor="pointer"
              alignItems="center"
              background="#7D4DFF"
              borderRadius="8px 8px 8px 8px"
              onClick={mutateAsync}
              isLoading={isGenerating}
            >
              生成大纲
            </Button>
          </HStack>
        </>
      )}
    </MyBox>
  );
};

export default FormModalInner;
