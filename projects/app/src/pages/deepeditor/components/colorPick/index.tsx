import {
  <PERSON>,
  Button,
  Popover,
  <PERSON><PERSON>Trigger,
  <PERSON>over<PERSON>ontent,
  SimpleGrid,
  Text,
  VStack
} from '@chakra-ui/react';
import { useState } from 'react';
import { rpxDim } from '@/utils/chakra';

interface ColorPickerProps {
  value?: string;
  onChange?: (color: string) => void;
}

const themeColors = [
  '#FFFFFF',
  '#000000',
  '#0066CC',
  '#FF0000',
  '#FF6600',
  '#99CC00',
  '#99CCFF',
  '#FF9900',
  '#FF99CC',
  '#99CC33'
];

const grayScaleColors = [
  ['#F8F9FA', '#F1F3F5', '#E9ECEF', '#DEE2E6', '#CED4DA'],
  ['#ADB5BD', '#868E96', '#495057', '#343A40', '#212529'],
  ['#C92A2A', '#A61E4D', '#862E9C', '#5F3DC4', '#364FC7'],
  ['#1864AB', '#0B7285', '#087F5B', '#2B8A3E', '#5C940D']
];

const standardColors = [
  '#FF0000',
  '#FF9900',
  '#FFFF00',
  '#00FF00',
  '#00FFFF',
  '#0000FF',
  '#9900FF',
  '#FF00FF',
  '#FF99CC',
  '#000000'
];

export function ColorPicker({ value = '#000000', onChange }: ColorPickerProps) {
  const [selectedColor, setSelectedColor] = useState(value);

  const handleColorChange = (color: string) => {
    setSelectedColor(color);
    onChange?.(color);
  };

  return (
    <Popover placement="bottom-start">
      <PopoverTrigger>
        <Button
          width={rpxDim(24)}
          height={rpxDim(24)}
          padding="0"
          minWidth="unset"
          backgroundColor={selectedColor}
          _hover={{ backgroundColor: selectedColor }}
          border="3px solid"
          borderColor="gray.200"
        >
          <Box
            width="0"
            height="0"
            marginLeft={rpxDim(36)}
            borderLeft={rpxDim(4) + ' solid transparent'}
            borderRight={rpxDim(4) + ' solid transparent'}
            borderTop={rpxDim(4) + ' solid #52525b'}
          />
        </Button>
      </PopoverTrigger>
      <Box zIndex={9999999999999}>
        <PopoverContent width={rpxDim(200)} padding="3">
          <VStack align="stretch" spacing="3">
            <Box>
              <Text fontSize="sm" color="gray.700" mb="2">
                主题颜色
              </Text>
              <SimpleGrid columns={5} spacing="1">
                {themeColors.map((color) => (
                  <Button
                    key={color}
                    width={rpxDim(20)}
                    height={rpxDim(20)}
                    padding="0"
                    minWidth="unset"
                    backgroundColor={color}
                    _hover={{ backgroundColor: color }}
                    onClick={() => handleColorChange(color)}
                    aria-label={`Select color ${color}`}
                    border="1px solid"
                    borderColor="gray.200"
                  />
                ))}
              </SimpleGrid>
            </Box>

            <Box>
              {grayScaleColors.map((row, index) => (
                <SimpleGrid key={index} columns={5} spacing="1" mb="1">
                  {row.map((color) => (
                    <Button
                      key={color}
                      width={rpxDim(20)}
                      height={rpxDim(20)}
                      padding="0"
                      minWidth="unset"
                      backgroundColor={color}
                      _hover={{ backgroundColor: color }}
                      onClick={() => handleColorChange(color)}
                      aria-label={`Select color ${color}`}
                      border="1px solid"
                      borderColor="gray.200"
                    />
                  ))}
                </SimpleGrid>
              ))}
            </Box>

            <Box>
              <Text fontSize="sm" color="gray.700" mb="2">
                标准颜色
              </Text>
              <SimpleGrid columns={10} spacing="1">
                {standardColors.map((color) => (
                  <Button
                    key={color}
                    width={rpxDim(20)}
                    height={rpxDim(20)}
                    padding="0"
                    minWidth="unset"
                    backgroundColor={color}
                    _hover={{ backgroundColor: color }}
                    onClick={() => handleColorChange(color)}
                    aria-label={`Select color ${color}`}
                    border="1px solid"
                    borderColor="gray.200"
                  />
                ))}
              </SimpleGrid>
            </Box>
          </VStack>
        </PopoverContent>
      </Box>
    </Popover>
  );
}

const Dom = () => {
  return null;
};

export default Dom;
