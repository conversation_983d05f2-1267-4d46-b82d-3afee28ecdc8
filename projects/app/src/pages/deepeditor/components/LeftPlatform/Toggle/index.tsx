import MyTooltip from '@/components/MyTooltip';
import { Box, Center, Text } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import { useContext, useEffect, useRef, useState } from 'react';
import { respDims } from '@/utils/chakra';
import { useDrawer } from '../DrawerContext';
import SvgIcon from '@/components/SvgIcon';

const Toggle = ({
  buttonWidth = 10,
  buttonHeight = 32,
  stickWidth = 5,
  ...props
}: { buttonWidth?: number; buttonHeight?: number; stickWidth?: number } & ChakraProps) => {
  const toggleRef = useRef<HTMLDivElement>(null);

  const [isHover, setIsHover] = useState(false);

  const { isOpenSidebar, openSidebar, closeSidebar } = useDrawer();

  const buttonHalfWidth = buttonWidth / 2;
  const buttonHalfHeight = buttonHeight / 2;
  const stickHalfWidth = stickWidth / 2;

  const buttonW = `${buttonWidth}px`;
  const buttonH = `${buttonHeight}px`;
  const stickW = `${stickWidth}px`;
  const stickH = `${buttonHalfHeight + stickHalfWidth}px`;
  const stickMaxH = `${Math.sqrt(buttonHalfWidth * buttonHalfWidth + buttonHalfHeight * buttonHalfHeight) + stickHalfWidth * 0.8}px`;
  const degV = Math.atan(buttonHalfWidth / buttonHalfHeight) * (180 / Math.PI);
  const borderR = `${stickHalfWidth}px`;

  const stickStyle: ChakraProps = {
    w: stickW,
    h: isOpenSidebar ? stickH : stickMaxH,
    pos: 'absolute',
    left: '0',
    right: '0',
    mx: 'auto',
    bgColor: '#D2D2D2',
    borderRadius: borderR,
    transition: 'all .3s ease-in-out'
  };

  const onClick = isOpenSidebar ? closeSidebar : openSidebar;

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      const dx = toggleRef.current
        ? event.pageX - (toggleRef.current.offsetLeft + toggleRef.current.offsetWidth)
        : 0;
      setIsHover((state) => dx < 0 || (state ? dx < 10 : false));
    };
    document.addEventListener('mousemove', handleMouseMove);
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <Center
      ref={toggleRef}
      pos="absolute"
      h={buttonH}
      px={isOpenSidebar ? '5px' : '0px'}
      top="0"
      bottom="0"
      my="auto"
      zIndex={1000}
      pointerEvents="none"
      {...props}
    >
      <MyTooltip
        label={isOpenSidebar ? '收起侧边栏' : '打开侧边栏'}
        placement="right"
        shouldWrapChildren={false}
        color="#FFFFFF"
        bg="rgba(0,0,0,0.9)"
        fontSize={respDims('14fpx')}
        lineHeight={respDims('22fpx')}
      >
        {isOpenSidebar ? (
          <Box
            visibility={isOpenSidebar && !isHover ? 'hidden' : 'visible'}
            pos="relative"
            w={buttonW}
            h={buttonH}
            cursor="pointer"
            _hover={{
              '.stick': {
                bgColor: '#A8A8A8'
              },
              ...(isOpenSidebar && {
                '.top-stick': {
                  h: stickMaxH,
                  transform: `rotate(${degV}deg)`
                },
                '.bottom-stick': {
                  h: stickMaxH,
                  transform: `rotate(-${degV}deg)`
                }
              })
            }}
            pointerEvents="auto"
            onClick={onClick}
          >
            <Box
              className="stick top-stick"
              {...stickStyle}
              top="0"
              transformOrigin="50% top"
              transform={`rotate(${isOpenSidebar ? 0 : -degV}deg)`}
            />

            <Box
              className="stick bottom-stick"
              {...stickStyle}
              bottom="0"
              transformOrigin="50% bottom"
              transform={`rotate(${isOpenSidebar ? 0 : degV}deg)`}
            />
          </Box>
        ) : (
          <Box
            borderRadius="0px 10px 10px 0px"
            background="#fff"
            px={respDims(10)}
            py={respDims(15)}
            justifyContent="center"
            alignItems="center"
            flexDirection="column"
            display="flex"
            boxShadow="0px 0px 16.3px 0px rgba(0, 0, 0, 0.11)"
            onClick={onClick}
            cursor="pointer"
            pointerEvents="auto"
            pos="relative"
          >
            {/* 文字方向向下 */}
            <Text
              css={{
                writingMode: 'vertical-rl'
              }}
            >
              展开侧边栏
            </Text>

            <SvgIcon name="chevronRight" mt={respDims(5)}></SvgIcon>
          </Box>
        )}
      </MyTooltip>
    </Center>
  );
};

export default Toggle;
