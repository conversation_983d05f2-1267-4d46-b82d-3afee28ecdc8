import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Tabs, TabList, Tab, TabPanels, TabPanel } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import FormDrawerInner from '../Form/FormDrawerInner';
import OverviewDrawerInner from '../Overview/OverviewDrawerInner';
import { useDrawer } from './DrawerContext';
import { InitModeEnum } from '../..';

const FormComp = () => {
  const { appId, activeTabIndex, setActiveTabIndex } = useDrawer();
  const router = useRouter();
  const { query } = router;
  useEffect(() => {
    setActiveTabIndex?.(0);
  }, [query.initMode, setActiveTabIndex]);

  return (
    <Tabs
      display="flex"
      flexDir="column"
      h="100%"
      index={activeTabIndex}
      onChange={(index) => setActiveTabIndex(index as 0 | 1)}
    >
      <TabList borderBottom="1px solid #F3F4F6">
        <Tab
          w="100%"
          borderBottom="none"
          fontSize={respDims(16, 14)}
          py={respDims(10, 8)}
          _active={{
            color: 'primary.500',
            fontWeight: 'bold'
          }}
          _selected={{
            color: 'primary.500',
            fontWeight: 'bold',
            backgroundColor: '#f7f7ff'
          }}
          _focus={{
            color: 'primary.500',
            backgroundColor: '#f7f7ff',
            fontWeight: 'bold'
          }}
        >
          基本信息
        </Tab>
      </TabList>

      <TabPanels flex="1" overflow="hidden">
        <TabPanel h="100%" p={0}>
          <FormDrawerInner appId={appId} height="100%" overflow="auto" generateType="content" />
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

export default FormComp;
