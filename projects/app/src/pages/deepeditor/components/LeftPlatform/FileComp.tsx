import React, { useCallback, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>b,
  TabPanels,
  TabPanel,
  Flex,
  Box,
  Button,
  Text,
  VStack,
  Image,
  Center
} from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { Upload } from 'antd';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import Chooser, { ChooserFileType, ChooserModeEnum } from '@/pages/cloud/list/components/Chooser';
import { downloadFile, downloadPublicFile, uploadFile, uploadFilePublic } from '@/api/file';
import { convertFileToHtml } from '@/utils/file/parse';
import { useDrawer } from './DrawerContext';
import { convertWordToHtml, parseTemplateFile } from '@/api/chat';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { isImage } from '@/utils/chat';
import MyBox from '@/components/common/MyBox';
import { UploadChangeParam } from 'antd/es/upload';
import { useUpdateEffect } from 'ahooks';
import { useDeepEditStore } from '@/store/useDeepEdit';
import MyTooltip from '@/components/MyTooltip';
import { handleFormula } from '@/utils/export';
import { parse } from 'marked';
import { Toast } from '@/utils/ui/toast';

const { Dragger } = Upload;

const FileComp = () => {
  const { file, setFile } = useDeepEditStore();
  const [currentItem, setCurrentItem] = useState<any>(null);
  const { editor } = useDrawer();
  const { openOverlay } = useOverlayManager();
  const [loading, setLoading] = useState(false);

  const handleUpload = async (info: UploadChangeParam) => {
    setLoading(true);

    if (info.file.status === 'done') {
      const rawFile = info.file.originFileObj;
      if (
        !rawFile ||
        rawFile.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        setLoading(false);
        return false;
      }

      try {
        const formData = new FormData();
        formData.append('file', rawFile);

        const fileInfo = await uploadFilePublic(formData);

        const wordFormData = new FormData();
        wordFormData.append('file', rawFile);
        let html = await convertWordToHtml(wordFormData);

        html = handleFormula(html);
        html = parse(html) as string;

        if (!html) {
          Toast.warning({ title: 'word格式有误,请编辑该word后重新导入' });
          setLoading(false);
          return;
        }

        setFile({
          fileUrl: fileInfo.fileUrl,
          fileKey: fileInfo.fileKey,
          fileName: fileInfo.fileName,
          fileType: fileInfo.fileType,
          fileId: fileInfo.fileKey,
          fileFrom: 'upload' as 'upload' | 'cloud'
        });

        if (editor) {
          editor.setContent(html);
        }
      } catch (error) {
        console.error(error);
        Toast.warning({ title: '上传失败，请重试。' });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
  };

  const handleSelectFromCloud = () => {
    openOverlay({
      Overlay: Chooser,
      props: {
        title: '选择文件',
        accept: ['docx', 'doc'],
        maxCount: 1,
        mode: ChooserModeEnum.Self,
        onSuccess: async (files) => {
          if (files.length > 0) {
            const file = {
              ...files[0],
              fileFrom: 'cloud' as 'upload' | 'cloud'
            };
            setFile(file);
          }
        }
      }
    });
  };

  const generateDoc = useCallback(async () => {
    try {
      const fileKey: string = file?.fileKey || '';
      if (file?.fileFrom === 'cloud') {
        const res = await downloadFile(fileKey);
        const formData = new FormData();
        formData.append('file', res.data);
        let html = await convertWordToHtml(formData);
        html = handleFormula(html);
        html = parse(html) as string;

        editor?.setContent(html);
      } else {
        const res = await downloadPublicFile(fileKey);
        const formData = new FormData();
        formData.append('file', res.data);
        let html = await convertWordToHtml(formData);
        html = handleFormula(html);
        html = parse(html) as string;

        editor?.setContent(html);
      }
    } catch (error) {
      console.log(error);
    }
  }, [file, editor]);

  useEffect(() => {
    if (file) {
      generateDoc();
    }
  }, [file, generateDoc]);

  return (
    <Tabs display="flex" flexDir="column" h="100%">
      <TabList borderBottom="1px solid #F3F4F6">
        <Tab
          w="100%"
          borderBottom="none"
          fontSize={respDims(16, 14)}
          py={respDims(10, 8)}
          _active={{
            color: 'primary.500',
            fontWeight: 'bold'
          }}
          _selected={{
            color: 'primary.500',
            fontWeight: 'bold',
            backgroundColor: '#f7f7ff'
          }}
          _focus={{
            color: 'primary.500',
            backgroundColor: '#f7f7ff',
            fontWeight: 'bold'
          }}
        >
          基本信息
        </Tab>
      </TabList>

      <TabPanels flex="1" overflow="hidden">
        <TabPanel h="100%" p={0}>
          <Flex flexDir="column" w="100%">
            <Box
              fontSize={respDims(16, 14)}
              fontWeight="bold"
              pl={respDims(24, 14)}
              pt={respDims(20, 20)}
              pb={respDims(16, 12)}
            >
              上传编辑文件
            </Box>
            <MyBox px={respDims(24, 14)} isLoading={loading}>
              {file ? (
                <Flex flexDir="column" alignItems="center" justifyContent="center" mt={2}>
                  <Flex
                    position="relative"
                    border="1px solid #E5E6EB"
                    borderRadius="16px"
                    p={respDims(16, 12)}
                    alignItems="center"
                  >
                    <SvgIcon name="file2Doc2" w="40px" h="40px" />
                    <SvgIcon
                      name="circleClose"
                      position="absolute"
                      className="close_icon"
                      right="-10px"
                      top="-10px"
                      cursor="pointer"
                      w="30px"
                      h="30px"
                      onClick={() => setFile(null)}
                    />
                  </Flex>
                  <MyTooltip label={file.fileName} placement="bottom">
                    <Box
                      mt={3}
                      color="#1D2129"
                      fontSize="14px"
                      maxW="200px"
                      className={'textEllipsis'}
                    >
                      {file.fileName}
                    </Box>
                  </MyTooltip>
                </Flex>
              ) : (
                <Dragger
                  name="file"
                  multiple={false}
                  onChange={handleUpload}
                  showUploadList={false}
                  accept=".doc,.docx"
                  style={{
                    border: '1px dashed #E5E6EB',
                    borderRadius: '8px',
                    backgroundColor: '#F8FAFC',
                    padding: '16px',
                    textAlign: 'center'
                  }}
                >
                  <SvgIcon name="uploadFile" w="80px" h="80px" />
                  <VStack
                    style={{ fontSize: '13px', color: '#1D2129' }}
                    lineHeight={respDims(18, 14)}
                  >
                    <Box>点击或拖拽文件到此处上传，或</Box>
                    <Button
                      variant="primary"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleSelectFromCloud();
                      }}
                    >
                      <SvgIcon name="download" w="16px" h="16px" mr={respDims(8, 4)} />
                      数据空间选择
                    </Button>
                    <Box color="#86909c" fontSize={respDims(12, 10)}>
                      仅支持上传 docx 格式
                    </Box>
                  </VStack>
                  <p style={{ fontSize: '12px', color: '#86909c', paddingTop: '8px ' }}>
                    {currentItem?.placeholder}
                  </p>
                </Dragger>
              )}
            </MyBox>
          </Flex>
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

export default FileComp;
