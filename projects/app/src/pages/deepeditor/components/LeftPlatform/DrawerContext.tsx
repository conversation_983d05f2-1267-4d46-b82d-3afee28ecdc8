import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useMemo,
  useEffect,
  useCallback
} from 'react';
import { DynamicFormDataType, InitChatResponse } from '@/types/api/chat';
import { useQuery } from '@tanstack/react-query';
import { getInitChatInfo } from '@/api/chat';
import { getAppDetail, getClientAppFormDetail } from '@/api/app';
import { TenantAppDetailInfo } from '@/types/api/tenant/app';
import { ClientAppFormDetailType } from '@/types/api/app';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { useRouter } from 'next/router';
import { useUpdateEffect } from 'ahooks';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import GuideStepModal from '../GuideStepModal';
import { ChatItemType } from '@/fastgpt/global/core/chat/type';

interface DrawerContextType {
  appDetail?: TenantAppDetailInfo;
  initChatInfo?: InitChatResponse;
  leftDrawerStatus: 'file' | 'overview' | 'form' | 'hide';
  appId: string;
  editor: any;
  activeTabIndex: number;
  formDetail?: ClientAppFormDetailType;
  setActiveTabIndex: (tab: 0 | 1) => void;
  isOpenSidebar: boolean;
  closeSidebar: () => void;
  openSidebar: () => void;
}

const DrawerContext = createContext<DrawerContextType | undefined>(undefined);

export const DrawerProvider = ({
  children,
  appId,
  editor
}: {
  children: ReactNode;
  appId: string;
  editor: any;
}) => {
  const [isOpenSidebar, setIsOpenSidebar] = useState(true);
  const {
    activeTab,
    setActiveTab,
    setFile,
    setOverviewTreeData,
    setGenerateFormData,
    setDeepEditChatItem
  } = useDeepEditStore();
  const router = useRouter();

  const { data: formDetail, isLoading } = useQuery(
    ['getClientAppFormDetail', appId],
    () => {
      return getClientAppFormDetail(appId);
    },
    {
      enabled: !!appId
    }
  );

  const { data } = useQuery(
    ['appDetail_deep_editor', appId],
    async () => {
      const [appDetail, initChatInfo] = await Promise.all([
        getAppDetail(appId, false),
        getInitChatInfo({ tenantAppId: appId })
      ]);
      return { appDetail, initChatInfo };
    },
    {
      enabled: !!appId
    }
  );

  const { appDetail, initChatInfo } = data || {};

  const leftDrawerStatus = useMemo(() => {
    if (appId) {
      if (appDetail?.appTaskTypeId) {
        return 'overview';
      } else if (formDetail) {
        return 'form';
      } else {
        return 'hide';
      }
    } else {
      return 'file';
    }
  }, [appDetail, formDetail, appId]);

  useEffect(() => {
    if (router.query.init == '1') {
      setFile(null);
      setOverviewTreeData([]);
      setActiveTab(0);
    }
  }, [router.query, setFile, setOverviewTreeData, setGenerateFormData, setActiveTab]);

  return (
    <DrawerContext.Provider
      value={{
        appDetail,
        initChatInfo,
        formDetail,
        leftDrawerStatus: leftDrawerStatus,
        appId,
        editor,
        activeTabIndex: activeTab,
        setActiveTabIndex: setActiveTab,
        isOpenSidebar,
        closeSidebar: () => setIsOpenSidebar(false),
        openSidebar: () => setIsOpenSidebar(true)
      }}
    >
      {children}
    </DrawerContext.Provider>
  );
};

export const useDrawer = () => {
  const context = useContext(DrawerContext);
  if (!context) {
    throw new Error('useDrawer must be used within a DrawerProvider');
  }
  return context;
};

const Dom = () => {
  return null;
};

export default Dom;
