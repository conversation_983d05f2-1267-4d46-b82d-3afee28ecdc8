import React, { useCallback, useEffect } from 'react';
import { Box } from '@chakra-ui/react';
import { DrawerProvider, useDrawer } from './DrawerContext';
import { respDims } from '@/utils/chakra';
import OverviewComp from './OverviewComp';
import FileComp from './FileComp';
import FormComp from './FormComp';
import { useSystemStore } from '@/store/useSystemStore';
import Toggle from './Toggle';
import { useUpdateEffect } from 'ahooks';
import GuideStepModal from '../GuideStepModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useRouter } from 'next/router';
import { useDeepEditStore } from '@/store/useDeepEdit';
const LeftPlatformContent = ({ appId }: { appId: string }) => {
  const { leftDrawerStatus: leftDrawerStatus } = useDrawer();
  const { isPc } = useSystemStore();
  const { isOpenSidebar, closeSidebar } = useDrawer();
  const { setGenerateFormData, setGenerateType, setReturnDocumentTitle } = useDeepEditStore();
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const router = useRouter();
  const sidebarW = isOpenSidebar ? respDims(360, 224) : respDims(0);

  useUpdateEffect(() => {
    if (leftDrawerStatus == 'overview') {
      if (router.query.init == '1') {
        if (router.query.appTaskTypeId) {
          setGenerateFormData({});
        }
        setGenerateType(undefined);
        setReturnDocumentTitle('');
        openAPPTaskStep(appId);
        // 清空init
        delete router.query.init;

        router.replace({
          pathname: router.pathname,
          query: router.query
        });
      }
    }
  }, [leftDrawerStatus, appId]);

  const openAPPTaskStep = useCallback(
    (appId: string) => {
      openOverlay({
        Overlay: GuideStepModal,
        props: {
          appId
        }
      });
    },
    [openOverlay]
  );

  return (
    <Box w={respDims(360, 224)} h="100%" position="relative">
      {leftDrawerStatus !== 'hide' && (
        <>
          <Box
            w={'100%'}
            display={isOpenSidebar ? 'display' : 'none'}
            h="100%"
            top="0"
            left="0"
            position="absolute"
            bg="#fff"
            borderRight="1px solid #F3F4F6"
          >
            {leftDrawerStatus === 'overview' && <OverviewComp />}
            {leftDrawerStatus === 'form' && <FormComp />}
            {leftDrawerStatus === 'file' && <FileComp />}
          </Box>
          {<Toggle left={sidebarW} />}
          {appId && <OverlayContainer></OverlayContainer>}
        </>
      )}
    </Box>
  );
};

const LeftPlatform = ({ appId, editor }: { appId: string; editor: any }) => {
  return (
    <DrawerProvider appId={appId} editor={editor}>
      <LeftPlatformContent appId={appId} />
    </DrawerProvider>
  );
};

export default LeftPlatform;
