import React, { useState, useEffect, useRef } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import ChatPanelMini from '@/components/ChatPanelMini';
import { serviceSideProps } from '@/utils/i18n';
import Toggle from '@/components/ToggleMini';

const RightPlatform = ({
  appId = '',
  chatId = '',
  activeRoute = ''
}: {
  appId?: string;
  chatId?: string;
  activeRoute?: string;
}) => {
  const [isOpenSidebar, setIsOpenSidebar] = useState(true);
  const openSidebar = () => setIsOpenSidebar(true);
  const closeSidebar = () => {
    setIsOpenSidebar(false);
  };
  const flexRef = useRef<HTMLDivElement>(null);

  return (
    <>
      <Flex w={respDims(360, 224)} flexShrink="0">
        <Flex
          display={isOpenSidebar ? 'flex' : 'none'}
          ref={flexRef}
          bg="#fff"
          pos="relative"
          w="100%"
        >
          <Toggle
            isOpenSidebar={isOpenSidebar}
            onOpenSidebar={openSidebar}
            onCloseSidebar={closeSidebar}
            direction="right"
            left="-24px"
            top="0px"
            zIndex="9999"
          />
          <ChatPanelMini chatId={chatId} activeRoute={activeRoute} />
        </Flex>

        <Flex
          pos="fixed"
          right="16px"
          display={isOpenSidebar ? 'none' : 'flex'}
          alignSelf="center"
          p="6px"
          cursor="pointer"
          w="90px"
          h="103px"
          bgColor="white"
          borderRadius="10px"
          boxShadow="0px 0px 16.3px 0px rgba(0, 0, 0, 0.11)"
          onClick={() => {
            openSidebar();
          }}
        >
          <Flex
            flexDirection="column"
            alignItems="center"
            flex="1"
            _hover={{
              bgColor: '#f8fafc'
            }}
          >
            <Flex w="44px" h="44px" borderRadius="50%" overflow="hidden" mt="15.5px" flexShrink="0">
              <Flex
                flexDir="column"
                alignItems="center"
                overflow="hidden"
                backgroundImage="/imgs/module/EllipseAiBg.svg"
                backgroundRepeat="no-repeat"
                backgroundSize="cover"
                w="44px"
                h="44px"
              >
                <Flex
                  flexDir="column"
                  alignItems="center"
                  overflow="hidden"
                  backgroundImage="/imgs/module/womenAI.png"
                  backgroundRepeat="no-repeat"
                  backgroundSize="cover"
                  w="37.278px"
                  h="44px"
                  mt="1.83px"
                ></Flex>
              </Flex>
            </Flex>
            <Flex mt="6px" color="#000" fontSize="14px" whiteSpace="nowrap">
              AI 助手
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      appId: context.query?.appId || '',
      activeRoute: context.query?.activeRoute || '',
      chatId: context.query?.chatId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default RightPlatform;
