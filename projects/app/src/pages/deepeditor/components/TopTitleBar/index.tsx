import React, { memo, useEffect, useState } from 'react';
import { Box, Text, Flex, Input } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import styles from '../../EditorBox.module.scss';
import { respDims, rpxDim } from '@/utils/chakra';
import { Button } from 'antd';
import { DeepEditorTabs, TabType } from '../Tabs';
import SaveMessageStatus from '../saveMessageStatus';
import { RecentSavePopover, SavedItem } from '../RecentSavePopover';
import { useSaveObj, useSaveStatus, useSaveTimes } from '@/store/useDeepEdit';
import { getTemporarilyList, TemporarilyListResponse } from '@/api/cloud';
import MyMenu from '@/components/MyMenu';
interface TopTitleBarProps {
  title: string;
  isEditingTitle: boolean;
  handleTitleClick: () => void;
  handleTitleBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
  handleEditorCommand: (command: string, value?: any) => void;
  saveAsWord: (type: 'word' | 'pdf') => Promise<void>;
  onCloseLayoutOver: (...args: any[]) => void;
  onHeaderTabChange: (tab: TabType) => void;
  onRecentSaveChange?: (item: TemporarilyListResponse) => void;
  editor: any;
  onRefreshTemporarilyList?: (refresh: () => Promise<void>) => void;
}

export const TopTitleBar = (props: TopTitleBarProps) => {
  const {
    onCloseLayoutOver,
    title,
    isEditingTitle,
    handleTitleClick,
    handleTitleBlur,
    handleEditorCommand,
    saveAsWord,
    onHeaderTabChange,
    onRecentSaveChange,
    editor,
    onRefreshTemporarilyList
  } = props;
  const [activeTab, setActiveTab] = useState<TabType>('start');
  const [temporarilyList, setTemporarilyList] = useState<TemporarilyListResponse[]>([]);

  const [isExporting, setIsExporting] = useState(false);

  const saveObj = useSaveObj();
  useEffect(() => {
    onHeaderTabChange?.(activeTab);
  }, [activeTab, onHeaderTabChange]);

  const onTabChange = (tab: TabType) => {
    setActiveTab(tab);
  };

  const onItemClick = (item: TemporarilyListResponse) => {
    console.log('item', item);
    onRecentSaveChange?.(item);
  };

  const refreshTemporarilyList = async () => {
    const res = await getTemporarilyList();
    setTemporarilyList(res);
  };

  useEffect(() => {
    if (onRefreshTemporarilyList) {
      onRefreshTemporarilyList(refreshTemporarilyList);
    }
  }, [onRefreshTemporarilyList]);

  useEffect(() => {
    refreshTemporarilyList();
  }, []);

  return (
    <Box className={styles.topTitleBar}>
      <Flex w="100%" justifyContent="space-between" alignItems="center">
        <Flex alignItems="center" flex="1">
          <Flex
            color="#1A5EFF"
            alignItems="center"
            justifyContent="center"
            cursor="pointer"
            w={respDims(82)}
            h={respDims(34)}
            background="#fff"
            borderRadius={respDims(50)}
            textAlign="center"
            border="1px solid #D9CCFF"
            onClick={() => onCloseLayoutOver()}
          >
            <Box
              color="#4E5969"
              display="flex"
              alignItems="center"
              _hover={{
                color: '#7D4DFF'
              }}
            >
              <SvgIcon name="chevronLeft" w={respDims('16fpx')} h={respDims('16fpx')} /> 退出
            </Box>
          </Flex>
          {isEditingTitle ? (
            <Input
              defaultValue={title}
              w={188}
              style={{ marginLeft: '10px', cursor: 'pointer', fontSize: '18px', fontWeight: 600 }}
              onBlur={(e) => {
                handleTitleBlur(e);
              }}
              autoFocus
            />
          ) : (
            <Text
              onClick={handleTitleClick}
              style={{ marginLeft: '10px', cursor: 'pointer', fontSize: '18px', fontWeight: 600 }}
              maxWidth={188}
              overflow="hidden"
              textOverflow="ellipsis"
              whiteSpace="nowrap"
            >
              {title}
            </Text>
          )}
          <Box ml={2} pr={4}>
            {(saveObj.saveStatus === 'saved' || saveObj.saveStatus === 'saving') &&
              new URLSearchParams(window.location.search).get('id') && (
                <SaveMessageStatus status={saveObj.saveStatus} time={saveObj.updateTime} />
              )}
          </Box>
          <RecentSavePopover items={temporarilyList} onItemClick={onItemClick}>
            <Box
              pl={4}
              color="var(--purple-600, #663ED3)"
              fontFamily="PingFang SC"
              fontSize={rpxDim(14)}
              fontStyle="normal"
              fontWeight="500"
              lineHeight={rpxDim(23)}
              cursor="pointer"
              borderLeft="1px solid #E5E7EB"
            >
              最近暂存
            </Box>
          </RecentSavePopover>
        </Flex>

        <Box flex="1" textAlign="center">
          <DeepEditorTabs activeTab={activeTab} onTabChange={onTabChange} />
        </Box>

        <Flex gap={1} flex="1" justifyContent="flex-end">
          <MyMenu
            Button={
              <Button style={{ marginRight: '10px' }} loading={isExporting} disabled={isExporting}>
                导出
              </Button>
            }
            menuList={[
              {
                label: '导出Word',
                onClick: async () => {
                  setIsExporting(true);
                  saveAsWord('word').finally(() => {
                    setIsExporting(false);
                  });
                }
              },
              {
                label: '导出PDF',
                onClick: () => {
                  setIsExporting(true);
                  saveAsWord('pdf').finally(() => {
                    setIsExporting(false);
                  });
                }
              }
            ]}
            offset={[-14, 5]}
            menuItemStylesProps={{
              px: '14px'
            }}
          ></MyMenu>
          <Button
            type="primary"
            className={styles.buttonBox}
            onClick={() => handleEditorCommand('save')}
          >
            保存到数据空间
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
};
TopTitleBar.displayName = 'Toolbar';

export default memo(TopTitleBar);
