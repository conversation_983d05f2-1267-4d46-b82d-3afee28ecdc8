import { TemporarilyListResponse } from '@/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import { rpxDim } from '@/utils/chakra';
import { Box, Popover, PopoverContent, PopoverTrigger } from '@chakra-ui/react';
import { FC } from 'react';

export interface SavedItem {
  title: string;
  date: string;
  time: string;
}

interface RecentSavePopoverProps {
  children: React.ReactNode;
  items?: TemporarilyListResponse[];
  onItemClick?: (item: TemporarilyListResponse) => void;
}

export const RecentSavePopover: FC<RecentSavePopoverProps> = ({ children, items, onItemClick }) => {
  return (
    <div style={{ zIndex: 9999999 }}>
      <Popover trigger="hover" placement="bottom-start">
        <PopoverTrigger>{children}</PopoverTrigger>
        <PopoverContent
          w={rpxDim(515)}
          maxH={rpxDim(650)}
          flexShrink={0}
          borderRadius="8px"
          border="1px solid #EFEFEF"
          background="#FFF"
          boxShadow="0px 3px 10.1px 0px rgba(0, 0, 0, 0.07)"
          p={4}
          overflow="auto"
        >
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box
              color="var(--text-primary, #303133)"
              fontFamily="PingFang SC"
              fontSize="16px"
              fontStyle="normal"
              fontWeight="500"
              lineHeight="23px"
              top={0}
              bg="white"
              pb={2}
              zIndex={1}
            >
              最近暂存
            </Box>
            <Box
              color="var(--text-2, #4E5969)"
              textAlign="right"
              textOverflow="ellipsis"
              fontFamily="PingFang SC"
              fontSize="12px"
              fontStyle="normal"
              fontWeight="400"
              lineHeight="22px"
            >
              仅暂存最近10次编辑内容
            </Box>
          </Box>

          {items && items.length > 0 ? (
            items.map((item: TemporarilyListResponse, index: number) => (
              <Box
                key={index}
                p={2}
                px={4}
                _hover={{ bg: '#F9F9F9' }}
                borderRadius="8px"
                cursor="pointer"
                display="flex"
                justifyContent="space-between"
                mb={2}
                mt={2}
                onClick={() => {
                  onItemClick?.(item);
                }}
              >
                <Box display="flex" justifyContent="center" alignItems="center">
                  <SvgIcon name="Doc" w={rpxDim(28)} h={rpxDim(32)} />
                </Box>
                <Box
                  display="flex"
                  ml={2}
                  alignItems="center"
                  flex={1}
                  maxWidth="312px"
                  overflow="hidden"
                  textOverflow="ellipsis"
                  whiteSpace="nowrap"
                  title={item.title}
                >
                  {item.title}
                </Box>
                <Box display="flex" alignItems="center" color="#999" ml={2} whiteSpace="nowrap">
                  {new Date(item.updateTime)
                    .toLocaleString('zh-CN', {
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })
                    .replace('/', '月')
                    .replace(' ', '日 ')}
                </Box>
              </Box>
            ))
          ) : (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              height="100%"
              minHeight={rpxDim(561)}
            >
              <SvgIcon name="editNoData" w={rpxDim(65)} h={rpxDim(65)} />
              <Box
                color="var(--text-2, #4E5969)"
                textAlign="center"
                fontFamily="PingFang SC"
                fontSize="14px"
                fontStyle="normal"
                fontWeight="400"
                lineHeight="23px"
                mt={2}
              >
                当前无暂存内容
              </Box>
            </Box>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
};

const Dom = () => {
  return null;
};

export default Dom;
