import { Box } from '@chakra-ui/react';
import React, { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';

interface LatexEasyEditorProps {
  onFormulaChange: (formula: string) => void;
}

// 添加接口，定义ref暴露的方法
interface LatexEasyEditorRef {
  getFormula: () => Promise<string>;
  setFormula: (formula?: string) => Promise<boolean>;
}

declare global {
  interface Window {
    LatexEasy: any;
  }
}

const LatexEasyEditor = forwardRef<LatexEasyEditorRef, LatexEasyEditorProps>(
  ({ onFormulaChange }: LatexEasyEditorProps, ref) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const latexEasyInstanceRef = useRef<any>(null); // 保存 `latexeasy` 实例的引用
    const [lateEdit, setLateEdit] = useState<any>(null);

    const initLatexEasy = () => {
      if (iframeRef.current && window.LatexEasy && !latexEasyInstanceRef.current) {
        // 创建并保存 `latexeasy` 实例
        const instance = new window.LatexEasy(iframeRef.current);
        latexEasyInstanceRef.current = instance;

        instance.on('ready', () => {
          setLateEdit(instance);
        });

        instance.init();
      }
    };

    useEffect(() => {
      if (typeof window === 'undefined') return;
      initLatexEasy();
      const script = document.createElement('script');
      script.src = 'https://latexeasy.com/vendor/LatexEasyEditor/editor/sdk.js';
      script.onload = initLatexEasy;
      document.body.appendChild(script);

      return () => {
        document.body.removeChild(script);
      };
    }, []);

    const getFormula = () => {
      return new Promise<string>((resolve) => {
        lateEdit.call('get.latex', {}, function (data: any) {
          const cleanedLatex = data.latex.replace(/\s+/g, ''); // 去除空格
          const latexImage = `https://r.latexeasy.com/image.svg?${cleanedLatex}`;
          resolve(latexImage);
        });
      });
    };

    const setFormula = (formula?: string) => {
      return new Promise<boolean>((resolve) => {
        if (lateEdit) {
          lateEdit.call('set.latex', {}, formula || '');
          resolve(true);
        } else {
          resolve(false);
        }
      });
    };

    useImperativeHandle(ref, () => ({
      getFormula,
      setFormula
    }));

    return (
      <Box h="400px">
        <iframe
          id="liveEditor"
          ref={iframeRef}
          frameBorder="0"
          style={{ width: '100%', height: '400px', border: '0', outline: 'none' }}
          src="https://latexeasy.com/editor"
          title="Latex Easy Editor"
        ></iframe>
      </Box>
    );
  }
);

LatexEasyEditor.displayName = 'LatexEasyEditor';

export default LatexEasyEditor;
