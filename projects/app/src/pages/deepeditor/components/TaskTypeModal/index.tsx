import SvgIcon from '@/components/SvgIcon';
import { Box, Button, Flex, useTheme, VStack } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Upload } from 'antd';
import { getTaskList } from '@/api/task';
import { useRouter } from 'next/router';
import { TaskListType } from '@/types/api/task';
import Chooser, { ChooserFileType, ChooserModeEnum } from '@/pages/cloud/list/components/Chooser';
import { Toast } from '@/utils/ui/toast';
import { uploadFile } from '@/utils/file';
import { UploadChangeParam } from 'antd/es/upload';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { AnyCnameRecord } from 'dns';
import MyTooltip from '@/components/MyTooltip';
import { reportAppsVisit } from '@/api/scene';
export default function TaskGrid({ onSuccess }: { onSuccess: (appId: string) => void }) {
  const [isModal, setIsModal] = useState(false);
  const [isChooserOpen, setIsChooserOpen] = useState(false);
  const { file, setFile } = useDeepEditStore();
  const router = useRouter();
  const { Dragger } = Upload;

  const [selectedFiles, setSelectedFiles] = useState<ChooserFileType[]>([]);
  const [fileList, setFileList] = useState<any[]>([]);

  const [courseTypes, setCourseTypes] = useState<TaskListType[]>([]);
  type TaskIconNames = `task${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8}`;

  // 当前页数,从0开始
  const [pageNo, setPageNo] = useState(0);
  // 每页条数,4/8
  const pageSize = courseTypes.length < 8 ? 4 : 8;
  // 总页数,从1开始

  const totalPage = Math.ceil(courseTypes.length / pageSize);

  const handleWorkPanelOpen = (appId: string) => {
    reportAppsVisit({ tenantAppId: appId });
    onSuccess(appId);
    setIsModal(false);
  };

  const handlePrevPage = () => {
    if (pageNo > 0) setPageNo(pageNo - 1);
  };

  const handleNextPage = () => {
    if (pageNo < totalPage - 1) setPageNo(pageNo + 1);
  };

  const theme = useTheme();
  const {
    data: taskList = [],
    isLoading: isLoadingTaskList,
    refetch
  } = useQuery(
    ['getTaskList'],
    () =>
      getTaskList({
        taskTypeId: router.query.appTaskTypeId as string
      }).then((res: TaskListType[]) => {
        setCourseTypes(res);
      }),
    {
      enabled: !!router.query.appTaskTypeId
    }
  );

  const handleSelectFromCloud = (event: React.MouseEvent) => {
    event.stopPropagation();
    setIsChooserOpen(true);
  };

  const handleChooserClose = () => {
    setIsChooserOpen(false);
  };

  const handleChooserSuccess = async (files: ChooserFileType[]) => {
    // 只支持上传docx格式文件
    if (files.length > 0 && !files[0].fileUrl?.includes('.docx')) {
      Toast.warning({
        title: '只支持上传docx格式文件'
      });
      return;
    }
    if (files.length > 0) {
      console.log(files);
      const file: any = {
        ...files[0],
        fileFrom: 'cloud' as 'upload' | 'cloud'
      };
      setFile({
        fileUrl: file.fileUrl,
        fileKey: file.fileKey,
        fileName: file.fileName,
        fileType: file.fileType,
        fileId: file.fileKey,
        fileFrom: 'cloud' as 'upload' | 'cloud'
      });
      redirectWithQuery();
      const fileKey: string = files[0]?.fileKey || '';
      if (!fileKey) return;
    }

    setIsModal(false);
  };

  const handleRemoveFile = (file: any) => {
    setFileList([]);
  };

  const beforeUpload = (file: any) => {
    // 清空上一次的文件列表
    setFileList([]);
    // 仅支持docx格式文件上传 且不能大于10mb
    if (
      file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      file.size > 10 * 1024 * 1024
    ) {
      Toast.warning({
        title: '仅支持docx格式文件上传且文件不能大于10mb'
      });
      return false;
    }
  };

  const redirectWithQuery = () => {
    const query = {
      ...router.query,
      mode: 'chat'
    } as any;
    delete query.init;
    delete query.appTaskTypeId;
    router.push({
      pathname: router.pathname,
      query: query
    });
  };

  const handleUpload = async (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const file = info.fileList[0].originFileObj;
      if (
        !file ||
        file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        return false;
      }
      try {
        // 上传文件
        const uploadedFiles = await Promise.all(
          info.fileList.map((file) => {
            if (
              file.originFileObj &&
              file.originFileObj.type ===
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ) {
              return uploadFile(file.originFileObj, {});
            }
            return Promise.resolve(file);
          })
        );
        const file: any = {
          ...uploadedFiles[0],
          fileFrom: 'upload' as 'upload' | 'cloud'
        };
        setFile({
          fileUrl: file.fileUrl,
          fileKey: file.fileKey,
          fileName: file.fileName,
          fileType: file.fileType,
          fileId: file.id,
          fileFrom: 'upload' as 'upload' | 'cloud'
        });

        // 重定向，刷新不需要弹窗
        redirectWithQuery();
        setIsModal(false);
      } catch (err) {
        Toast.warning({ title: '上传失败，请重试。' }); // 提示用户
      }
    }
  };

  useEffect(() => {
    if (router.query.mode === 'smartClass' && !router.query.appId) {
      setIsModal(true);
    } else {
      setIsModal(false);
    }
  }, [router.query.mode, router.query.appId]);

  return (
    <>
      {isModal && (
        <Flex
          justifyContent="center"
          alignItems="center"
          position="fixed"
          bgColor="rgba(0,0,0,0.48)"
          w="100vw"
          h="100vh"
          zIndex="99999"
        >
          <Box
            position="relative"
            w="888px"
            zIndex="1"
            bgColor="white"
            borderRadius="20px"
            p="86px 40px 26px 40px"
            mt="25px"
          >
            {/* close */}
            <SvgIcon
              cursor="pointer"
              name="taskClose"
              w={rpxDim(24)}
              h={rpxDim(24)}
              pos="absolute"
              right="32px"
              top="20px"
              onClick={() => {
                redirectWithQuery();
                setIsModal(false);
              }}
            />
            {/* top */}
            <Flex
              flexWrap="wrap"
              rowGap="21px"
              columnGap="18px"
              margin="0 auto"
              px="47px"
              position="relative"
            >
              {/* 上一页按钮 */}
              <Flex
                cursor="pointer"
                position="absolute"
                left="0px"
                top="50%"
                transform="translate(0,-50%)"
                justifyContent="center"
                alignItems="center"
                w={rpxDim(40)}
                h={rpxDim(40)}
                bgColor="white"
                borderRadius="36px"
                boxShadow="0px 0px 15.6px 0px rgba(92, 92, 92, 0.15)"
                onClick={handlePrevPage}
              >
                {pageNo > 0 ? (
                  <SvgIcon name="taskLeft" w={rpxDim(14)} h={rpxDim(14)} />
                ) : (
                  <MyTooltip label="暂无更多">
                    <SvgIcon name="taskLeft" w={rpxDim(14)} h={rpxDim(14)} color="#D1D5DB" />
                  </MyTooltip>
                )}
              </Flex>

              {/* list */}
              {courseTypes
                .slice(pageNo * pageSize, (pageNo + 1) * pageSize)
                .map((course, index) => (
                  <Flex
                    key={index}
                    bgColor="#f8f8f8"
                    flexDirection="column"
                    p="14px"
                    w="165px"
                    h="135px"
                    borderRadius="8px"
                    flexShrink="0"
                    userSelect="none"
                    cursor="pointer"
                    border="1px solid transparent"
                    _hover={{
                      border: '1px solid' + theme.colors.primary[500]
                    }}
                    onClick={() => {
                      handleWorkPanelOpen(course.tenantAppId);
                    }}
                  >
                    <SvgIcon
                      name={('task' + ((index + 1) % 8 || '8').toString()) as TaskIconNames}
                      w={rpxDim(24)}
                      h={rpxDim(24)}
                    />
                    <Box mt="10px" fontWeight="500" fontSize="16px" color="#000">
                      {course.name}
                    </Box>
                    <Box
                      mt="2px"
                      fontSize="14px"
                      display="-webkit-box"
                      overflow="hidden"
                      textOverflow="ellipsis"
                      css={{
                        WebkitBoxOrient: 'vertical',
                        WebkitLineClamp: 2
                      }}
                    >
                      {course.taskIntro}
                    </Box>
                  </Flex>
                ))}

              {/* 下一页按钮 */}
              <Flex
                cursor="pointer"
                position="absolute"
                right="0px"
                top="50%"
                transform="translate(0,-50%)"
                justifyContent="center"
                alignItems="center"
                w={rpxDim(40)}
                h={rpxDim(40)}
                bgColor="white"
                borderRadius="36px"
                boxShadow="0px 0px 15.6px 0px rgba(92, 92, 92, 0.15)"
                onClick={handleNextPage}
              >
                {pageNo < totalPage - 1 ? (
                  <SvgIcon name="taskRight" w={rpxDim(14)} h={rpxDim(14)} />
                ) : (
                  <MyTooltip label="暂无更多">
                    <SvgIcon name="taskRight" w={rpxDim(14)} h={rpxDim(14)} color="#D1D5DB" />
                  </MyTooltip>
                )}
              </Flex>
            </Flex>
            {/* bottom */}
            <Flex
              userSelect="none"
              mt="25px"
              flexDirection="column"
              justifyContent="center"
              alignItems="center"
              bgColor="#f7f8fa"
              borderRadius="14px"
              border="1px dashed #E5E6EB"
              cursor="pointer"
              _hover={{
                border: '1px dashed' + theme.colors.primary[500]
              }}
            >
              <Dragger
                //只支持上传一个文件和docx文件
                accept={'.docx'}
                maxCount={1}
                name="file"
                onChange={(info) => handleUpload(info)}
                beforeUpload={(file) => beforeUpload(file)}
                onRemove={(file) => handleRemoveFile(file)}
                showUploadList={false}
                style={{
                  border: 'none',
                  borderRadius: '8px',
                  backgroundColor: '#f7f8fa',
                  padding: '16px',
                  textAlign: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center'
                }}
              >
                <SvgIcon name="uploadFile" w="80px" h="56px" />
                <VStack>
                  <Box mt="16px" fontSize="14px" fontWeight="500" color="#1d2129">
                    直接上传现有文档编辑，点击或拖动文件到此处上传，或
                  </Box>
                  <Button
                    variant="primary"
                    onClick={(event) => {
                      event.stopPropagation();
                      handleSelectFromCloud(event);
                    }}
                  >
                    <SvgIcon name="download" w="16px" h="16px" mr={respDims(8, 4)} />
                    数据空间选择
                  </Button>

                  <Box mt="16px" fontSize="12px" color="#86909c">
                    仅支持word
                  </Box>
                </VStack>
              </Dragger>
            </Flex>
          </Box>
          {isChooserOpen && (
            <Chooser
              mode={ChooserModeEnum.Self}
              title="选择文件"
              maxCount={1}
              files={selectedFiles}
              accept={['doc', 'docx']}
              onSuccess={handleChooserSuccess}
              onClose={handleChooserClose}
            />
          )}
        </Flex>
      )}
    </>
  );
}
