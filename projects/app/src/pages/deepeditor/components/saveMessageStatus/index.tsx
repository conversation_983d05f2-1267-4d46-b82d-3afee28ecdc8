import SvgIcon from '@/components/SvgIcon';
import { SaveStatus } from '@/store/useDeepEdit';
import { respDims } from '@/utils/chakra';
import { Box, Flex } from '@chakra-ui/react';
import { FC } from 'react';

interface SaveMessageStatusProps {
  status: SaveStatus;
  time?: string;
}

const SaveMessageStatus: FC<SaveMessageStatusProps> = ({ status, time }) => {
  const style = {
    fontSize: respDims('14fpx')[0],
    color: '#4E5969',
    fontWeight: 400,
    userSelect: 'none' as const,
    display: 'flex',
    padding: '1px 8px',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '4px',
    borderRadius: '4px',
    background: 'var(--fill-2, #F2F3F5)',
    height: '100%'
  };

  const getContent = () => {
    switch (status) {
      case 'saving':
        return (
          <>
            <span>正在保存...</span>
          </>
        );
      case 'saved':
        return (
          <>
            <SvgIcon name="saveTips" w={respDims(12)} h={respDims(12)} />
            <Box>
              已保存于{' '}
              {new Date(time || '').toLocaleString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Box>
          </>
        );
      default:
        return '';
    }
  };

  return status !== 'idle' ? <Flex style={style}>{getContent()}</Flex> : null;
};

export default SaveMessageStatus;
