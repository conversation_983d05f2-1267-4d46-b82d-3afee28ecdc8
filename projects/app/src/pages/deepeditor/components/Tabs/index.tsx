'use client';

import { Box, Flex } from '@chakra-ui/react';
import cn from 'classnames';
import { respDims, rpxDim } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useEffect } from 'react';

export type TabType = 'start' | 'insert' | 'ai';

interface DeepEditorTabsProps {
  activeTab?: TabType;
  onTabChange?: (value: TabType) => void;
  className?: string;
}

const TABS = [
  { id: 'start', label: '开始', width: rpxDim(30), left: '50%' },
  { id: 'insert', label: '插入', width: rpxDim(30), left: '50%' },
  { id: 'ai', label: 'AI编辑', isAI: true, width: rpxDim(50), left: '45%' }
] as const;

export function DeepEditorTabs({
  activeTab = 'start',
  onTabChange,
  className
}: DeepEditorTabsProps) {
  const handleTabClick = (tabId: TabType) => {
    onTabChange?.(tabId);
  };

  const tabStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: rpxDim(8),
    backgroundColor: 'transparent',
    color: '#000',
    fontFamily: '"PingFang SC"',
    fontSize: rpxDim(16),
    fontWeight: 400,
    lineHeight: rpxDim(28),
    position: 'relative',
    cursor: 'pointer',
    border: 'none',
    outline: 'none',
    transform: 'translateZ(0)',
    backfaceVisibility: 'hidden',
    WebkitFontSmoothing: 'antialiased',
    transition: 'all 0.2s ease-in-out, font-weight 0.15s ease',
    '&:hover': {
      cursor: 'pointer'
    },
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: rpxDim(-1),
      left: TABS.find((tab) => tab.id === activeTab)?.left,
      transform: 'translateX(-50%)',
      width: TABS.find((tab) => tab.id === activeTab)?.width,
      height: rpxDim(4),
      borderRadius: rpxDim(50),
      opacity: 0,
      transition: 'all 0.3s ease-in-out',
      background: '#E5E7EB'
    },
    '&:hover::after': {
      opacity: 1
    },
    '&[aria-selected="true"]': {
      color: '#000',
      transform: 'translateZ(0)'
    },
    '&[aria-selected="true"]::after': {
      opacity: 1,
      background: 'linear-gradient(131deg, #DC7EFF 0%, #601CFF 105.7%)'
    }
  };

  return (
    <Box className={cn('w-full border-b border-[#E5E7EB]', className)}>
      <Flex role="tablist" gap={rpxDim(78)} justify="center">
        {TABS.map((tab) => (
          <Box
            key={tab.id}
            as="button"
            role="tab"
            aria-selected={activeTab === tab.id}
            onClick={() => handleTabClick(tab.id)}
            sx={{
              ...tabStyle,
              fontWeight: activeTab === tab.id ? 600 : 400,
              width: tab.id === 'ai' ? rpxDim(120) : 'auto'
            }}
          >
            {tab.id === 'ai' && <SvgIcon name="chatAi" w={rpxDim(24)} h={rpxDim(24)} />}
            {tab.label}
          </Box>
        ))}
      </Flex>
    </Box>
  );
}

export default DeepEditorTabs;
