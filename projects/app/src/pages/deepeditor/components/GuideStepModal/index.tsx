import React from 'react';
import MyModal from '@/components/MyModal';
import { ModalBody, Button, Box, Center, HStack } from '@chakra-ui/react';
import FormModalInner from '../Form/FormModalInner';
import OverviewModalInner from '../Overview/OverviewModalInner';
import { GuideStepProvider, useGuideStep } from './GuideStepContext';
import { promisifyConfirm } from '@/utils/ui/messageBox';
import { respDims, rpxDim } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';

const stepTitles = ['填写信息', '生成大纲'];

const GuideStepModal = ({
  onClose,
  appId,
  onSuccess
}: {
  onClose: () => void;
  appId: string;
  onSuccess?: () => void;
}) => {
  return (
    <GuideStepProvider appId={appId} onClose={onClose} onSuccess={onSuccess}>
      <InnerGuideStepModal onClose={onClose} appId={appId} />
    </GuideStepProvider>
  );
};

const InnerGuideStepModal = ({ onClose, appId }: { onClose: () => void; appId: string }) => {
  const { step, setStep } = useGuideStep();

  const handleBack = () => {
    promisifyConfirm({
      title: '返回后会需要重新生成大纲，确定要返回上一步吗？'
    }).then(() => {
      setStep(1);
    });
  };

  return (
    <MyModal
      isOpen={true}
      // title={`步骤 ${step}: ${stepTitles[step - 1]}`}
      onClose={onClose}
      closeOnOverlayClick={false}
      isCentered
      minW="700px"
      hideCloseButton
      borderRadius="20px"
      overflow="hidden"
    >
      {/* close */}
      <SvgIcon
        cursor="pointer"
        name="taskClose"
        w={rpxDim(24)}
        h={rpxDim(24)}
        pos="absolute"
        right="32px"
        top="20px"
        onClick={() => {
          onClose();
        }}
      />
      <ModalBody p={0} overflow="hidden">
        <Center mt={12}>
          <HStack>
            <HStack>
              <Center
                w="28px"
                h="28px"
                borderRadius="50%"
                {...(step === 0
                  ? {
                      background: 'primary.500',
                      color: '#fff'
                    }
                  : {
                      background: '#F6F6F6',
                      color: 'primary.500'
                    })}
              >
                {step === 0 ? 1 : <SvgIcon name="check"></SvgIcon>}
              </Center>
              <Box
                color="primary.500"
                css={{
                  ...(step === 0
                    ? {
                        color: '#7D4DFF',
                        fontWeight: '600'
                      }
                    : {
                        color: 'primary.500',
                        fontWeight: '400'
                      })
                }}
              >
                填写信息
              </Box>
            </HStack>

            <Box
              w={respDims(140, 120)}
              color="primary.500"
              h="1px"
              bg={step === 0 ? '#E5E5E5' : 'primary.500'}
            ></Box>

            <HStack>
              <Center
                w="28px"
                h="28px"
                borderRadius="50%"
                {...(step === 1
                  ? {
                      background: 'primary.500',
                      color: '#fff'
                    }
                  : {
                      background: '#F6F6F6',
                      color: '#4E5969'
                    })}
              >
                2
              </Center>
              <Box
                // color='primary.500'
                {...(step === 0
                  ? {
                      color: '#4E5969',
                      fontWeight: '400'
                    }
                  : {
                      color: 'primary.500',
                      fontWeight: '600'
                    })}
              >
                生成大纲
              </Box>
            </HStack>
          </HStack>
        </Center>
        <Box h="600px" pt={3}>
          <FormModalInner appId={appId} display={step === 0 ? 'flex' : 'none'} />
          <OverviewModalInner appId={appId} display={step === 1 ? 'flex' : 'none'} />
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default GuideStepModal;
