import React, { createContext, useContext, useState, ReactNode } from 'react';
import { FormData2ChatDataType } from '@/types/chat';
import { DynamicFormDataType, InitChatResponse } from '@/types/api/chat';
import { getAppDetail, getClientAppFormDetail, getOriginAppDetail } from '@/api/app';
import { useQuery } from '@tanstack/react-query';
import { getInitChatInfo } from '@/api/chat';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { ClientAppFormDetailType } from '@/types/api/app';
import { AppDetailType } from '@/fastgpt/global/core/app/type';
import { TenantAppDetailInfo } from '@/types/api/tenant/app';

interface GuideStepContextType {
  step: number;
  setStep: (step: 0 | 1) => void;
  chatData?: FormData2ChatDataType;
  setChatData: (data: FormData2ChatDataType) => void;
  formData?: DynamicFormDataType;
  formDetail?: ClientAppFormDetailType;
  setFormData: (data: DynamicFormDataType) => void;
  appId: string;
  initChatInfo?: InitChatResponse;
  appDetail?: TenantAppDetailInfo;
  onClose: () => void;
  onSuccess?: () => void;
}

const GuideStepContext = createContext<GuideStepContextType | undefined>(undefined);

export const GuideStepProvider = ({
  children,
  appId,
  onClose,
  onSuccess
}: {
  children: ReactNode;
  appId: string;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const { activeTab, setActiveTab } = useDeepEditStore();
  const [chatData, setChatData] = useState<FormData2ChatDataType | undefined>();
  const [formData, setFormData] = useState<DynamicFormDataType | undefined>();

  const { data } = useQuery(
    ['appDetail_deep_editor', appId],
    async () => {
      const [appDetail, initChatInfo] = await Promise.all([
        getAppDetail(appId, false),
        getInitChatInfo({ tenantAppId: appId })
      ]);
      return { appDetail, initChatInfo };
    },
    {
      enabled: !!appId
    }
  );

  const { appDetail, initChatInfo } = data || {};

  const { data: formDetail, isLoading } = useQuery(
    ['getClientAppFormDetail', appId],
    () => {
      return getClientAppFormDetail(appId);
    },
    {
      enabled: !!appId
    }
  );

  return (
    <GuideStepContext.Provider
      value={{
        step: activeTab,
        setStep: setActiveTab,
        chatData,
        setChatData,
        formData,
        setFormData,
        appId,
        initChatInfo: initChatInfo,
        onClose,
        formDetail,
        appDetail,
        onSuccess
      }}
    >
      {children}
    </GuideStepContext.Provider>
  );
};

export const useGuideStep = () => {
  const context = useContext(GuideStepContext);
  if (!context) {
    throw new Error('useGuideStep must be used within a GuideStepProvider');
  }
  return context;
};

const Dom = () => {
  return null;
};

export default Dom;
