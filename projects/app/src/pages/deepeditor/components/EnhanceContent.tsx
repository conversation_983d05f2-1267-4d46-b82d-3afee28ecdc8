import { MessagePromptType } from '@/components/ChatBox/MessageInput';
import InteractEditor, { EditorModeEnum, EditorRef, TagOption } from '@/components/InteractEditor';
import Lottie from '@/components/Lottie';
import MyTooltip from '@/components/MyTooltip';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { chats2GPTMessages } from '@/fastgpt/global/core/chat/adapt';
import { ChatItemValueTypeEnum, ChatRoleEnum } from '@/fastgpt/global/core/chat/constants';
import { ChatSiteItemType, UserChatItemValueItemType } from '@/fastgpt/global/core/chat/type';
import { SseResponseEventEnum } from '@/fastgpt/global/core/workflow/runtime/constants';
import { respDims, rpxDim } from '@/utils/chakra';
import { streamFetch } from '@/utils/fetch';
import { Toast } from '@/utils/ui/toast';
import {
  Box,
  Button,
  Flex,
  NumberDecrementStepper,
  NumberIncrementStepper,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Stack,
  Text,
  useColorModeValue
} from '@chakra-ui/react';
import { nanoid } from 'nanoid';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Markdown from '@/components/Markdown';
import { formatChatValue2InputType } from '@/utils/chat';
import { StyledTextNode } from '@/components/InteractEditor/nodes/StyledTextNode';

const iconMap = {
  润色: 'chatPolishing2',
  改写: 'chatRewrite2',
  续写: 'chatContinuedWriting2',
  总结: 'chatSummary2',
  扩写: 'chatExpandWrittenArticle2',
  缩写: 'chatAbbreviation2',
  AI编辑: 'chatAiEdit'
} as const;

export const TAG_OPTIONS: TagOption[] = [
  // 润色
  { label: '通顺表达', value: '1' },
  { label: '提升文采', value: '2' },
  { label: '增强感情', value: '3' },
  { label: '丰富细节', value: '4' },
  { label: '正式', value: '5' },
  { label: '党政', value: '6' },
  { label: '活泼', value: '7' },
  { label: '简洁', value: '8' },
  { label: '口语', value: '9' },
  { label: '保持', value: '10' },
  { label: '扩展', value: '11' },
  { label: '精简', value: '12' },

  // 改写
  { label: '保持', value: '13' },
  { label: '扩展', value: '14' },
  { label: '精简', value: '15' },

  // 续写
  { label: '多样扩展', value: '16' },
  { label: '深入阐述', value: '17' },
  { label: '照例延伸', value: '18' },

  // 总结
  { label: '简短', value: '19' },
  { label: '充分', value: '20' },
  { label: '抓住核心', value: '21' },
  { label: '囊括要点', value: '22' }
];
interface SliderConfig {
  min: number;
  max: number;
  step: number;
  marks: number[];
  defaultValue: number;
}
const EnhanceContent = ({
  selectedText,
  style,
  operationType,
  onContentGenerated,
  onSendingStatusChange,
  editor1,
  position,
  enhanceContentType,
  editorContent,
  labels,
  text,
  fullLatexText,
  selectedLatexText
}: {
  selectedText?: string;
  style?: React.CSSProperties;
  operationType?: string;
  onContentGenerated: (content: string, type: string) => void;
  onSendingStatusChange: (isSending: boolean) => void;
  editor1?: any;
  position: {
    top: number;
    left: number;
    type?: 'top' | 'bottom';
  } | null;
  enhanceContentType: string;
  editorContent: string;
  labels: string;
  text: string;
  fullLatexText: string;
  selectedLatexText: string;
}) => {
  const editorRef = useRef<EditorRef>(null);
  const [prompt, setPrompt] = useState<MessagePromptType>();
  const promptTitleRef = useRef<HTMLDivElement>(null);
  const [editorValue, setEditorValue] = useState('');
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [toolbarExtraWidth] = useState(0);
  const [showMenu, setShowMenu] = useState(true);
  const [chatHistory, setChatHistory] = useState<ChatSiteItemType[]>([]);
  const [isSending, setIsSending] = useState(false);
  const chatController = useRef<AbortController>();
  const [savedEditorValue, setSavedEditorValue] = useState('');
  const [canPause, setCanPause] = useState(false);
  const [lastOperationType, setLastOperationType] = useState<string | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const outerBoxRef = useRef<HTMLDivElement>(null);
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
  const bgColor = useColorModeValue('#f2f3f5', '#f2f3f5');
  const selectedColor = useColorModeValue('#7D4DFF', '#7D4DFF');
  const [value, setValue] = useState<number>(0);
  const [selectedMenu, setSelectedMenu] = useState<string | null>(null);
  const tagNodeMap = useRef<Map<string, StyledTextNode>>(new Map());
  const [optionValues, setOptionValues] = useState<string[]>([]);
  const valueRef = useRef<number>(value);
  const [sendable, setSendable] = useState(false);
  const selectedMenuRef = useRef<string | null>(selectedMenu);
  // 记录最后一次非NaN的值
  const lastValidMinRef = useRef<number>(0);
  const lastValidMaxRef = useRef<number>(0);
  const editorPrefix = useMemo(
    () =>
      prompt?.promptTitle && (
        <Flex
          display="inline-flex"
          h="28px"
          fontSize="14px"
          lineHeight="28px"
          alignItems="center"
          bgColor="#FFFFFF"
          userSelect="none"
        >
          <Flex
            ref={promptTitleRef}
            display="inline-flex"
            h="28px"
            fontSize="14px"
            lineHeight="28px"
            alignItems="center"
            bgColor="#FFFFFF"
            userSelect="none"
          >
            <Box
              mr="8px"
              px="8px"
              borderRadius="4px"
              bg="linear-gradient( 90deg, rgba(235,253,255,0.85) 0%, rgba(236,240,255,0.76) 100%)"
            >
              <Box
                bg="linear-gradient(228.23395011634184deg, #1A5EFF 0%, #2194FF 100%)"
                bgClip="text"
                bgSize="100% 100%"
                maxW="12em"
                whiteSpace="nowrap"
                overflow="hidden"
                textOverflow="ellipsis"
              >
                {prompt.promptTitle}
              </Box>
            </Box>
          </Flex>
        </Flex>
      ),
    [prompt?.promptTitle]
  );
  const handleSliderChange = (val: number, type: string) => {
    setValue(val);
    setSelectedMenu(type);
    valueRef.current = val;
    selectedMenuRef.current = type;
  };

  const handleNumberInputChange = (val: string, type: string) => {
    setValue(Number(val));
    setSelectedMenu(type);
    valueRef.current = Number(val);
    selectedMenuRef.current = type;
  };

  const handleButtonClick = (value: string, label: string) => {
    editorRef.current?.focus();
    const newOptionValues = new Set(optionValues);
    if (newOptionValues.has(value)) {
      newOptionValues.delete(value);
    } else {
      newOptionValues.add(value);
    }
    console.log('newOptionValues', newOptionValues);
    setOptionValues(Array.from(newOptionValues));
    // handleInputChange(Array.from(newOptionValues).join(','));

    // setSelectedTags((prevSelectedTags) => {
    //   const newSelected = new Set(prevSelectedTags);
    //   if (newSelected.has(value)) {
    //     newSelected.delete(value);

    //     const node = tagNodeMap.current.get(value);
    //     if (node) {
    //       editorRef.current?.removeStyledText(node);
    //       tagNodeMap.current.delete(value);
    //     }

    //   } else {
    //     newSelected.add(value);

    //     const styledNode = editorRef.current?.insertStyledText(label, {
    //       background: 'linear-gradient(0deg, #734BFF 0%, #AD50FF 100%)',
    //       WebkitBackgroundClip: 'text',
    //       WebkitTextFillColor: 'transparent',
    //       overflow: 'hidden',
    //       fontSize: '14px',
    //       fontWeight: '500',
    //       backgroundColor: '#f0f0f0'
    //     });
    //     if (styledNode) {
    //       tagNodeMap.current.set(value, styledNode);
    //     }
    //   }
    //   console.log('newSelected3', newSelected);

    //   return newSelected;
    // });
    // setEditorValue(selectedTags);
  };

  const unsendableReason = useMemo(() => {
    // 没有选择任何操作类型
    if (!selectedMenu) {
      return '请选择编辑类型';
    }

    // 扩写/缩写模式
    if (selectedMenu === '扩写' || selectedMenu === '缩写') {
      if (!value) {
        return '请设置目标字数';
      }
      return ''; // 可发送
    }

    // 其他模式
    if (!optionValues.length && !editorValue.trim()) {
      return '请选择选项或输入需求';
    }

    return ''; // 可发送
  }, [selectedMenu, value, optionValues, editorValue]);

  useEffect(() => {
    console.log('unsendableReason3333', !unsendableReason);
    setSendable(!unsendableReason);
  }, [unsendableReason]);

  useEffect(() => {
    console.log('optionValues', optionValues);
  }, [optionValues]);

  const tagOptions = {
    润色: [
      {
        title: '方向',
        options: [
          { label: '通顺表达', value: '1' },
          { label: '提升文采', value: '2' },
          { label: '增强感情', value: '3' },
          { label: '丰富细节', value: '4' }
        ]
      },
      {
        title: '风格',
        options: [
          { label: '正式', value: '5' },
          { label: '党政', value: '6' },
          { label: '活泼', value: '7' },
          { label: '简洁', value: '8' },
          { label: '口语', value: '9' }
        ]
      },
      {
        title: '长度',
        options: [
          { label: '保持', value: '10' },
          { label: '扩展', value: '11' },
          { label: '精简', value: '12' }
        ]
      }
    ],
    改写: [
      {
        title: '长度',
        options: [
          { label: '保持', value: '13' },
          { label: '扩展', value: '14' },
          { label: '精简', value: '15' }
        ]
      }
    ],
    续写: [
      {
        title: '方向',
        options: [
          { label: '多样扩展', value: '16' },
          { label: '深入阐述', value: '17' },
          { label: '照例延伸', value: '18' }
        ]
      }
    ],
    总结: [
      {
        title: '方向',
        options: [
          { label: '简短', value: '19' },
          { label: '充分', value: '20' }
        ]
      },
      {
        title: '风格',
        options: [
          { label: '抓住核心', value: '21' },
          { label: '囊括要点', value: '22' }
        ]
      }
    ],
    扩写: [
      {
        title: '字数',
        options: [{ label: '扩展', value: value.toString(), min: 1000, max: 4000, step: 100 }]
      }
    ],
    缩写: [
      {
        title: '字数',
        options: [{ label: '缩写', value: value.toString(), min: 1000, max: 4000, step: 100 }]
      }
    ]
  };

  useEffect(() => {
    const config = calculateSliderConfig(
      text?.length || 0,
      selectedMenu === '扩写' ? 'expand' : 'shrink'
    );
    setValue(config.defaultValue);
  }, [text?.length, selectedMenu]);

  const calculateY = (x: number): number => {
    console.log('calculateY', x);

    if (x < 1000) {
      // 获取最高位数字
      const digitCount = Math.floor(Math.log10(x)) + 1;
      const highestDigit = Math.floor(x / Math.pow(10, digitCount - 1));

      if (highestDigit < 8) {
        // 最高位取下一个偶数，其他位数置为0
        return (highestDigit + (2 - (highestDigit % 2))) * Math.pow(10, digitCount - 1);
      } else {
        // 进位到下一个十位，其他位数置为0
        return (highestDigit + (10 - (highestDigit % 10))) * Math.pow(10, digitCount - 1);
      }
    } else {
      // 获取百位数字
      const hundredthDigit = Math.floor((x / 100) % 10);

      if (hundredthDigit < 8) {
        // 百位取下一个偶数，保留更高位
        return Math.floor(x / 1000) * 1000 + (hundredthDigit + (2 - (hundredthDigit % 2))) * 100;
      } else {
        // 进位到下一个百位，保留更高位
        return Math.floor(x / 1000) * 1000 + (hundredthDigit + (10 - (hundredthDigit % 10))) * 100;
      }
    }
  };

  const calculateSliderConfig = (
    selectedTextLength: number,
    type: 'expand' | 'shrink'
  ): SliderConfig => {
    const x = selectedTextLength;

    if (type === 'expand') {
      // 扩写逻辑
      const originMin = calculateY(x); // 最小值使用Y的取值逻辑
      let min = 0;
      // 如果min不是NaN，则更新min
      if (!isNaN(originMin)) {
        min = originMin;
        // 记录最后一次非NaN的min值
        lastValidMinRef.current = min;
        // 如果min是NaN，则使用lastValidMin
      } else {
        min = lastValidMinRef.current;
      }
      const max = min * 4; // 最大值是最小值的4倍
      const step = Math.max(1, Math.floor((max - min) / 10)); // 均匀分10段

      // 生成4个均匀分布的刻度标记
      const marks = [
        min,
        min + Math.floor((max - min) / 3),
        min + Math.floor((2 * (max - min)) / 3),
        max
      ];

      return {
        min,
        max,
        step,
        marks,
        defaultValue: min // 默认滑块初始化在最小值
      };
    } else {
      let max;
      let min;
      // 缩写逻辑
      if (x < 10) {
        const originMax = x;
        if (originMax) {
          max = originMax;
          // 记录最后一次非NaN的max值
          lastValidMaxRef.current = max;
        } else {
          max = lastValidMaxRef.current;
        }
        min = 2;
      } else {
        const originMax = calculateY(x);
        if (originMax) {
          max = originMax;
          // 记录最后一次非NaN的max值
          lastValidMaxRef.current = max;
        } else {
          max = lastValidMaxRef.current;
        }
        min = Math.floor(max / 4); // 最小值是最大值的1/4
      }
      const step = Math.max(1, Math.floor((max - min) / 10));
      const marks = [
        min,
        min + Math.floor((max - min) / 3),
        min + Math.floor((2 * (max - min)) / 3),
        max
      ];
      return {
        min,
        max,
        step,
        marks,
        defaultValue: max // 默认滑块初始化在最大值
      };
    }
  };

  const abortSendMessage = useCallback(
    (reason: 'stop' | 'cancel' = 'stop') => {
      if (isSending && canPause) {
        chatController.current?.abort(reason);
        chatController.current = undefined;
        setIsSending(false);
        setEditorValue(savedEditorValue);
        // setChatHistory([]);
      } else {
        console.log('sendable', sendable);
        if (sendable) {
          handleSend();
        }
      }
    },
    [isSending, canPause, editorValue, savedEditorValue, optionValues, sendable, value]
  );

  const renderContent = () => {
    if (selectedMenu === '扩写' || selectedMenu === '缩写') {
      const isExpand = selectedMenu === '扩写';
      const title = isExpand ? '扩写' : '缩写';
      const { min, max, step, marks } = calculateSliderConfig(
        text?.length || 0,
        selectedMenu === '扩写' ? 'expand' : 'shrink'
      );
      return (
        <Box
          maxW="666px"
          px={4}
          py={6}
          bgColor="#fff"
          borderRadius="8px"
          border="1px solid #F3F4F6"
          mt="8px"
          boxShadow="0px 0px 15.6px 0px rgba(92, 92, 92, 0.11);"
        >
          <Flex alignItems="center" w="100%">
            <Flex direction="column" w="100%">
              <Flex alignItems="center">
                <Text fontSize="14px" pt="4px" fontWeight="400" color="#606266" whiteSpace="nowrap">
                  字数
                </Text>
                <Slider
                  flex="1"
                  min={min}
                  max={max}
                  step={step}
                  m="0 14px 0 19px"
                  value={value}
                  onChange={(val) => handleSliderChange(val, selectedMenu)}
                  aria-label="word-count-slider"
                >
                  <SliderTrack h="5px">
                    <SliderFilledTrack bg="purple.500" />
                  </SliderTrack>
                  <SliderThumb
                    boxSize={4}
                    bg="white"
                    border="2px"
                    borderColor="purple.500"
                    _focus={{ boxShadow: '0 0 0 3px #7D4DFF' }}
                  />
                </Slider>
              </Flex>

              <Flex justify="space-between" mt="6px" ml="35px">
                {marks.map((mark) => (
                  <Text key={mark} fontSize="sm" color="gray.500">
                    {mark}
                  </Text>
                ))}
              </Flex>
            </Flex>

            <Flex align="center" ml="14px">
              <NumberInput
                value={value}
                onChange={(val) => handleNumberInputChange(val, selectedMenu)}
                min={min}
                max={max}
                step={step}
                w="100px"
                borderRadius="3px"
                size="md"
              >
                <NumberInputField bgColor="#fff" />
                <NumberInputStepper>
                  <NumberIncrementStepper bgColor={'##f3f3f3'}>
                    <SvgIcon w="16px" h="16px" name="chevronUp" />
                  </NumberIncrementStepper>
                  <NumberDecrementStepper bgColor={'##f3f3f3'} borderTop="1px solid #fff">
                    <SvgIcon w="16px" h="16px" name="aiEditChevronDown" />
                  </NumberDecrementStepper>
                </NumberInputStepper>
              </NumberInput>
              <Text
                fontSize="14px"
                fontWeight="400"
                color="rgba(0, 0, 0, 0.90)"
                whiteSpace="nowrap"
                ml="10px"
              >
                个字
              </Text>
            </Flex>
          </Flex>
        </Box>
      );
    }

    const groups = tagOptions[selectedMenu as keyof typeof tagOptions];
    console.log('groups', groups);
    if (!groups || isSending) return null;

    return (
      <Stack
        spacing={6}
        p={4}
        bgColor="#fff"
        boxShadow="0px 0px 15.6px 0px rgba(92, 92, 92, 0.11);"
        borderRadius="8px"
        w="521px"
        mt="10px"
        pointerEvents="none" // 让元素不拦截鼠标事件
        position="relative" // 确保不影响背景滚动
        sx={{
          '& > *': {
            pointerEvents: 'auto' // 恢复子元素的鼠标事件
          }
        }}
      >
        {groups.map((group) => (
          <Flex key={group.title} alignItems="center">
            <Text color="#606266" mr="16px" fontSize="14px" fontWeight="400">
              {group.title}
            </Text>
            <Stack direction="row" spacing={2} flexWrap="wrap" gap={2}>
              {group.options.map((option) => (
                <Button
                  key={option.value}
                  size="sm"
                  bg={bgColor}
                  color={optionValues.includes(option.value) ? selectedColor : '#4E5969'}
                  variant="ghost"
                  fontSize="14px"
                  fontWeight="400"
                  borderRadius="8px"
                  bgColor={optionValues.includes(option.value) ? '#F4F4FF' : '#F2F3F5'}
                  border={
                    optionValues.includes(option.value) ? '1px solid #7D4DFF' : '1px solid #F2F3F5'
                  }
                  _hover={{
                    bg: useColorModeValue('gray.200', 'whiteAlpha.300')
                  }}
                  onClick={() => handleButtonClick(option.value, option.label)}
                  px={4}
                  py={2}
                >
                  {option.label}
                </Button>
              ))}
            </Stack>
          </Flex>
        ))}
      </Stack>
    );
  };

  useEffect(() => {
    console.log('labelslabels', labels);

    if (labels !== '' && labels !== 'AI编辑') {
      setSelectedMenu(labels);
      setShowMenu(false);
    } else {
      setSelectedMenu('AI编辑');
      setShowMenu(false);
    }
  }, [labels]);

  const getSpecificGoals = () => {
    // Handle special cases for 扩写 and 缩写
    console.log('selectedMenu333', selectedMenu);
    if (
      (selectedMenu || selectedMenuRef.current) === '扩写' ||
      (selectedMenu || selectedMenuRef.current) === '缩写'
    ) {
      return [
        {
          label: '字数',
          value: [(value || valueRef.current).toString()] // Use the slider value
        }
      ];
    }

    // Original logic for other menu types
    const groups = tagOptions[selectedMenu as keyof typeof tagOptions];
    const goalsMap = new Map<string, string[]>();

    optionValues.forEach((tag) => {
      const group = groups?.find((group) => group.options.some((option) => option.value === tag));
      const tagLabels =
        group?.options
          .filter((option) => optionValues.includes(option.value))
          .map((option) => option.label) || [];

      if (group) {
        const existingLabels = goalsMap.get(group.title) || [];
        goalsMap.set(group.title, [...existingLabels, ...tagLabels]);
      }
    });

    const specificGoals = Array.from(goalsMap.entries()).map(([label, values]) => ({
      label,
      value: Array.from(new Set(values))
    }));
    return specificGoals;
  };
  const MenuItem = ({ icon, label }: { icon: SvgIconNameType; label: string }) => {
    if (label === 'AI编辑') {
      return (
        <Flex
          alignItems="center"
          p="8px"
          style={style}
          cursor="pointer"
          // _hover={{ bg: '#F8FAFC' }}
          fontWeight="600"
          bg="linear-gradient(359.9999992173779deg, #D459FF 0%, #802FFF 100%)"
          backgroundClip="text"
          borderBottom="1px solid #E2E8F0"
          onClick={() => {
            setSelectedMenu(label);
            setShowMenu(false);
            setSelectedTags(new Set<string>());
            setOptionValues([]);
            setEditorValue('');
          }}
        >
          <SvgIcon mr="8px" w="20px" h="20px" name={icon} />
          <Text fontSize="14px" fontWeight="500">
            {label}
          </Text>
        </Flex>
      );
    }

    return (
      <Flex
        alignItems="center"
        p="8px"
        style={style}
        cursor="pointer"
        _hover={{ bg: '#F8FAFC' }}
        fontWeight="600"
        onClick={() => {
          setSelectedMenu(label);
          setShowMenu(false);
          setSelectedTags(new Set<string>());
          setOptionValues([]);
          setEditorValue('');
        }}
      >
        <SvgIcon mr="8px" w="20px" h="20px" name={icon} />
        <Text fontSize="14px" color="#303133" fontWeight="500">
          {label}
        </Text>
      </Flex>
    );
  };

  const handleSend = useCallback(
    async (
      // value = `## 这是我的全文内容：<br/>${editorContent!}<br/><br/>## 请结合全文内容，帮我按“撰写要求”撰写以下内容：<br/>${selectedText}<br/><br/>撰写要求：<br/>${editorValue}`
      values = (() => {
        const payload = {
          edit_type: selectedMenu || selectedMenuRef.current,
          full_text: fullLatexText,
          selected_text: selectedLatexText,
          user_input: editorValue,
          specific_goals: getSpecificGoals()
        };

        return JSON.stringify(payload);
      })()
    ) => {
      if (!values.trim()) {
        Toast.error('请输入内容');
        return Promise.reject();
      }
      setIsSending(true);
      setCanPause(true);
      setSavedEditorValue(editorValue);
      setChatHistory([]); // 清空历史记录

      const humanDataId = nanoid();
      const aiDataId = nanoid();
      let newChatList: ChatSiteItemType[] = [
        {
          dataId: humanDataId,
          obj: ChatRoleEnum.Human,
          isShareContent: false,
          value: [
            {
              type: ChatItemValueTypeEnum.text,
              text: {
                content: values.trim()
              }
            }
          ] as UserChatItemValueItemType[],
          status: 'finish'
        },
        {
          dataId: aiDataId,
          obj: ChatRoleEnum.AI,
          isShareContent: false,
          value: [
            {
              type: ChatItemValueTypeEnum.text,
              text: {
                content: ''
              }
            }
          ],
          status: 'loading'
        }
      ];
      console.log('newChatList', newChatList);

      setChatHistory((prevHistory) => [...prevHistory, ...newChatList]);
      chatController.current = new AbortController();

      const originValue = JSON.stringify(newChatList[newChatList.length - 2].value);
      const messages = chats2GPTMessages({ messages: newChatList, reserveId: true });
      const prompts = messages.slice(0, 1);
      const data = {
        value: originValue,
        content: value,
        fileKeys: [],
        messages: prompts,
        type: 2,
        responseChatItemId: aiDataId,
        chatId: nanoid(),
        dataId: nanoid()
      };
      setEditorValue('');
      setShowMenu(false);
      setOptionValues([]);
      try {
        await streamFetch({
          url: '/huayun-ai/client/chat/once',
          data: data,
          onMessage: (message) => {
            setChatHistory((prevHistory) => {
              return prevHistory.map((item) => {
                if (item.dataId === aiDataId) {
                  if (
                    message.event === SseResponseEventEnum.answer ||
                    message.event === SseResponseEventEnum.fastAnswer
                  ) {
                    const lastValue = item.value[item.value.length - 1];
                    if (lastValue.type === ChatItemValueTypeEnum.text && lastValue.text) {
                      lastValue.text.content += message.text;
                    }
                  }
                }
                return item;
              });
            });
          },
          abortCtrl: chatController.current
        });
      } catch (error) {
        Toast.error('发送失败，请重试');
      } finally {
        setIsSending(false); // 发送完成，重置状态
        setCanPause(false);
      }
    },
    [selectedMenu, optionValues, editorContent, selectedText, editorValue] // 更新依赖
  );

  const handleInputChange = (value: any) => {
    if (!isSending) {
      setEditorValue(value.trim());
      // const newSelectedTags = new Set<string>();
      // Object.values(tagOptions).forEach((groups) => {
      //   groups.forEach((group) => {
      //     group.options.forEach((option) => {
      //       if (value.includes(option.label)) {
      //         newSelectedTags.add(option.value);
      //       }
      //     });
      //   });
      // });
      // setOptionValues(Array.from(newSelectedTags));
      // setSelectedTags(newSelectedTags);
    }
  };

  const handleTagChange = useCallback(
    (tags: string[]) => {
      setOptionValues(tags);
    },
    [editorValue, setOptionValues]
  );

  const onCopyContent = () => {
    if (!isSending) {
      const text = chatHistory
        .filter((item) => item.obj === ChatRoleEnum.AI)
        .map((item) => formatChatValue2InputType(item.value).text)
        .join(' ');

      const plainText = text.replace(/<[^>]+>/g, '').replace(/\n/g, ' ');

      const textArea = document.createElement('textarea');
      textArea.value = plainText;
      document.body.appendChild(textArea);
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      Toast.success({ title: '内容已复制到剪贴板' });
    } else {
      Toast.error('请生成完成后复制内容');
    }
  };

  const highlightSelectedText = () => {
    if (editor1) {
      const selection = editor1.selection;
      if (!selection?.isCollapsed) {
        editor1.execCommand('backcolor', '#eff0f1');
      }
    }
  };

  useEffect(() => {
    highlightSelectedText();
  }, [editor1, selectedText]);

  useEffect(() => {
    onSendingStatusChange(isSending);
  }, [isSending, onSendingStatusChange]);

  useEffect(() => {
    if ((operationType && selectedText) || (operationType && value)) {
      const newValue = (() => {
        const payload = {
          edit_type: operationType,
          full_text: fullLatexText,
          selected_text: selectedLatexText,
          user_input: editorValue,
          specific_goals: getSpecificGoals()
        };
        return JSON.stringify(payload);
      })();
      // const newValue = `## 这是我的全文内容：<br/>${editorContent!}<br/><br/>## 请结合全文内容帮我${operationType}以下内容：<br/>${selectedText!}`;
      // setEditorValue(editorValue);
      // handleSend(newValue);
      setShowMenu(false);
      setSelectedMenu(operationType);
      setLastOperationType(newValue);
    }
  }, [operationType]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatHistory]);

  return (
    <>
      <Box
        ref={outerBoxRef}
        bgColor="#F3F4F6"
        boxShadow=" 0px 0px 16px 0px rgba(92,92,92,0.11)"
        borderRadius={respDims(20)}
        p="1px"
        _focusWithin={{
          bgImage: 'linear-gradient( 270deg, #774CFF 0%, #AC51FF 100%)'
        }}
        position="relative"
        tabIndex={0}
        border="1px solid #E5E7EB"
        w="100%"
      >
        <Box
          borderRadius={respDims('15fpx')}
          bgColor="#ffffff"
          overflow="hidden"
          onKeyDownCapture={(e) => {
            if (isSending) {
              e.stopPropagation();
              e.preventDefault();
            }
          }}
        >
          {chatHistory.length > 0 && (
            <Flex flexDir="column" p="16px 16px 0 16px" onClick={() => setShowMenu(false)}>
              <Box
                ref={chatContainerRef}
                fontSize={respDims(14)}
                fontWeight="400"
                color="#000000"
                overflow="auto"
                maxH={respDims(180)}
              >
                {chatHistory.map((item) => (
                  <React.Fragment key={item.dataId}>
                    {item.obj === ChatRoleEnum.AI && (
                      <Flex flexDir="column">
                        {isSending && (
                          <Flex alignItems="center">
                            <Lottie
                              name="chating"
                              w={respDims(`${(130 / 3) * 2}rpx`, 130 / 3)}
                              h={respDims(`${(45 / 3) * 2}rpx`, 45 / 3)}
                            />
                            <Box
                              ml={respDims('12rpx', 6)}
                              color="#606266"
                              fontSize={respDims('28rpx', '14fpx')}
                            >
                              生成中...
                            </Box>
                          </Flex>
                        )}
                        <Flex
                          onClick={(e) => {
                            setShowMenu(false);
                            e.stopPropagation();
                          }}
                          ml={respDims(6)}
                          fontSize="14px"
                          color="#000000"
                          fontWeight="400"
                          lineHeight="27px"
                        >
                          <Markdown
                            obj={item.obj}
                            source={(() => {
                              let text = '';
                              if (item.value && Array.isArray(item.value)) {
                                text = item.value
                                  .map((it) => {
                                    if (typeof it.text?.content === 'string') {
                                      return it.text.content;
                                    }
                                    return '';
                                  })
                                  .join('');
                              }
                              const quoteReg = /\[source:(.+)\]/g;
                              const replaceText = text
                                ? text.replace(quoteReg, `[QUOTE SIGN]($1)`)
                                : '';
                              return replaceText;
                            })()}
                          />
                        </Flex>
                      </Flex>
                    )}
                  </React.Fragment>
                ))}
              </Box>
              {!isSending && (
                <Flex
                  mt={respDims(26)}
                  mb={respDims(12)}
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Flex alignItems="center">
                    <Flex
                      alignItems="center"
                      borderRadius="8px"
                      bgColor="rgba(0,0,0,0.03)"
                      p="7px 16px"
                      mr="10px"
                      _hover={{
                        '& > div': {
                          color: '#764ff6 !important'
                        },
                        '& svg': {
                          color: '#764ff6 !important'
                        }
                      }}
                      cursor="pointer"
                      onClick={async () => {
                        if (!isSending) {
                          onContentGenerated(
                            chatHistory
                              .filter((item) => item.obj === ChatRoleEnum.AI)
                              .map((item) => item.value.map((val) => val.text?.content).join(' '))
                              .join('\n\n'),
                            '替换'
                          );
                        } else {
                          Toast.error('请生成完成后替换内容');
                        }
                      }}
                    >
                      <SvgIcon w="16px" h="16px" name="chatReplace" mr="4px" />
                      <Box fontSize="14px" fontWeight="bold" color="#4E5969">
                        替换
                      </Box>
                    </Flex>
                    <Flex
                      alignItems="center"
                      borderRadius="8px"
                      bgColor="rgba(0,0,0,0.03)"
                      p="7px 16px"
                      cursor="pointer"
                      _hover={{
                        '& > div': {
                          color: '#764ff6 !important'
                        },
                        '& svg': {
                          color: '#764ff6 !important'
                        }
                      }}
                      onClick={async () => {
                        if (!isSending) {
                          onContentGenerated(
                            chatHistory
                              .filter((item) => item.obj === ChatRoleEnum.AI)
                              .map((item) => item.value.map((val) => val.text?.content).join(' '))
                              .join('\n\n'),
                            '插入'
                          );
                        } else {
                          Toast.error('请生成完成后插入内容');
                        }
                      }}
                    >
                      <SvgIcon w="16px" h="16px" name="chatInsert" mr="4px" />
                      <Box fontSize="14px" fontWeight="bold" color="#4E5969">
                        插入
                      </Box>
                    </Flex>
                  </Flex>

                  <Flex alignItems="center">
                    <MyTooltip label="复制">
                      <SvgIcon
                        bgColor="rgba(0,0,0,0.03)"
                        w="32px"
                        h="32px"
                        name="copy"
                        borderRadius="50%"
                        p="9px"
                        mr="10px"
                        onClick={() => onCopyContent()}
                        cursor="pointer"
                      />
                    </MyTooltip>
                    <MyTooltip label="重新生成">
                      <SvgIcon
                        bgColor="rgba(0,0,0,0.03)"
                        w="32px"
                        h="32px"
                        borderRadius="50%"
                        name="evaluationLoop"
                        p="9px"
                        cursor="pointer"
                        onClick={async () => {
                          if (!isSending) {
                            console.log('lastOperationType', lastOperationType);
                            console.log('lastOperationType', savedEditorValue);
                            if (editorValue.trim() || getSpecificGoals().length > 0 || value > 0) {
                              // const newSavedEditorValue = `## 这是我的全文内容：<br/>${editorContent!}<br/><br/>## 请结合全文内容，帮我按“撰写要求”撰写以下内容：<br/>${selectedText}<br/><br/>撰写要求：<br/>${savedEditorValue}`;
                              const newSavedEditorValue = (() => {
                                const payload = {
                                  edit_type: selectedMenu || selectedMenuRef.current,
                                  full_text: fullLatexText,
                                  selected_text: selectedLatexText,
                                  user_input: editorValue,
                                  specific_goals: getSpecificGoals()
                                };
                                return JSON.stringify(payload);
                              })();
                              setEditorValue(
                                savedEditorValue ? savedEditorValue : lastOperationType!
                              );
                              setChatHistory([]);
                              try {
                                await handleSend(newSavedEditorValue);
                              } catch (error) {
                                Toast.error('重新生成失败，请重试');
                              }
                            } else {
                              Toast.error('请输入内容');
                            }
                          } else {
                            Toast.error('请生成完成后重新生成内容');
                          }
                        }}
                      />
                    </MyTooltip>
                  </Flex>
                </Flex>
              )}
            </Flex>
          )}
          {chatHistory.length > 0 && !isSending && <Box borderBottom="1px solid #E5E7EB" />}

          <Flex justifyContent="center" alignItems="center">
            <Box width="100%" bgColor="#ffffff" px="16px" py="12px">
              <Flex flexWrap="nowrap" pos="relative" alignItems="center">
                <Flex
                  alignItems="center"
                  bgColor="#F8FAFC"
                  borderRadius="8px"
                  p="6px 8px"
                  flexShrink="0"
                >
                  <SvgIcon
                    w={respDims(22)}
                    h={respDims(22)}
                    name={iconMap[selectedMenu as keyof typeof iconMap]}
                  />

                  <Box
                    ml="2px"
                    fontSize="14px"
                    fontWeight="500"
                    color="transparent"
                    cursor="pointer"
                    bg="linear-gradient(359.9999992173779deg, #D459FF 0%, #802FFF 100%)"
                    backgroundClip="text"
                    onClick={() => {
                      setShowMenu((prevShowMenu) => !prevShowMenu);
                    }}
                    style={{
                      WebkitTextFillColor: 'transparent'
                    }}
                  >
                    {selectedMenu || 'AI编辑'}
                  </Box>
                </Flex>
                <Box
                  border="1px solid #E5E7EB"
                  h={respDims(21)}
                  ml={respDims(12)}
                  mr={respDims(12)}
                  mt={respDims(2)}
                  flexShrink="0"
                />
                <Flex flex="1 1 auto" alignItems="center" flexWrap="wrap">
                  <InteractEditor
                    options={TAG_OPTIONS}
                    onTagChange={handleTagChange}
                    optionValues={optionValues}
                    // onOptionValuesChange={setOptionValues}
                    ref={editorRef}
                    flex="1 1 auto"
                    mode={EditorModeEnum.plain}
                    prefix={editorPrefix}
                    p="0"
                    border="none"
                    boxShadow="none !important"
                    placeholder={optionValues.length > 0 ? '' : '请输入优化选中文本的需求'}
                    maxHeight="140px"
                    fontSize="14px"
                    lineHeight="28px"
                    value={isSending ? '正在优化文本' : editorValue}
                    color={isSending ? 'transparent' : '#111824'}
                    onChange={handleInputChange}
                    bg={
                      isSending
                        ? 'linear-gradient(359.9999992173779deg, #D459FF 0%, #802FFF 100%)'
                        : undefined
                    }
                    backgroundClip={isSending ? 'text' : undefined}
                    onKeyDownCapture={(e) => {
                      if (e.key === 'Enter' && !e.altKey && !e.ctrlKey && !e.shiftKey) {
                        if (!!sendable) {
                          handleSend();
                        }
                        e.stopPropagation();
                        e.preventDefault();
                      }
                    }}
                    onKeyDown={(e) => {
                      e.stopPropagation();
                    }}
                  />
                </Flex>
                {/* 工具栏 */}
                <Flex
                  ref={toolbarRef}
                  ml="auto"
                  h={respDims('32fpx')}
                  flexShrink="0"
                  alignSelf="flex-end"
                  alignItems="center"
                  bgColor="#FFFFFF"
                  zIndex="2"
                >
                  {toolbarExtraWidth > 0 && <Box w={`${toolbarExtraWidth}px`} />}

                  <MyTooltip label={isSending ? '暂停生成' : unsendableReason || '发送'}>
                    <SvgIcon
                      name={isSending ? 'chatInInput' : 'chatSend'}
                      flexShrink="0"
                      w={respDims('32fpx')}
                      h={respDims('32fpx')}
                      color={sendable || isSending ? 'primary.500' : '#C0C4CC'}
                      borderRadius="50%"
                      cursor="pointer"
                      onClick={() => {
                        abortSendMessage();
                      }}
                    />
                  </MyTooltip>
                </Flex>
              </Flex>
            </Box>
          </Flex>
        </Box>

        {showMenu && !isSending && (
          <Box
            bg="white"
            borderRadius="md"
            boxShadow="md"
            p={2}
            w="112px"
            pos="absolute"
            top={(() => {
              if (enhanceContentType) {
                if (chatHistory.length <= 0) {
                  return enhanceContentType === 'top' ? '70px' : '-250px';
                } else {
                  return undefined;
                }
              } else {
                if (chatHistory.length <= 0) {
                  return position?.type === 'top' ? '70px' : '-280px';
                } else {
                  return undefined;
                }
              }
            })()}
            bottom={chatHistory.length > 0 ? '66px' : undefined}
            zIndex="1200"
          >
            <MenuItem icon={'chatAiEdit'} label="AI编辑" />
            <MenuItem icon={'chatPolishing'} label="润色" />
            <MenuItem icon={'chatRewrite'} label="改写" />
            <MenuItem icon={'chatExpandWrittenArticle'} label="扩写" />
            <MenuItem icon={'chatContinuedWriting'} label="续写" />
            <MenuItem icon={'chatEditorAbbreviation'} label="缩写" />
            <MenuItem icon={'chatSummary'} label="总结" />
          </Box>
        )}
      </Box>

      {renderContent()}
    </>
  );
};

export default EnhanceContent;
