import React, { use, useCallback, useEffect, useMemo, useState } from 'react';
import { Box, Text, Flex, Input, Menu, Stack, Fade } from '@chakra-ui/react';
import { Tooltip } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import styles from './../EditorBox.module.scss';
import { respDims, rpxDim } from '@/utils/chakra';
import cn from 'classnames';
import { Affix, Dropdown, Button } from 'antd';
import CustomTour from '@/components/CustomTour';
import { TabType } from './Tabs';
import { ColorPicker } from './colorPick';
import { Toast } from '@/utils/ui/toast';
export interface HeaderProps {
  title: string;
  handleEditorCommand: (command: string, value?: any) => void;
  selectionState: {
    bold: number;
    italic: number;
    underline: boolean;
    strikethrough: boolean;
    formatmatch: number;
    isJustifyLeft: boolean;
    isJustifyCenter: boolean;
    isJustifyRight: boolean;
    isJustifyJustify: boolean;
    isH1: boolean;
    isH2: boolean;
    isH3: boolean;
  };
  selectedParagraphFormat: string;
  selectedFont: string;
  selectedFontSize: string;
  selectedJustify: string;
  isAIEnabled: boolean;
  handleSummaryClick: (event: React.MouseEvent, operationType: string) => void;
  onOpenSelectFile: () => void;
  ListStyleMenu: () => JSX.Element;
  BulletStyleMenu: () => JSX.Element;
  LineSpacingMenu: () => JSX.Element;
  AlignMenu: () => JSX.Element;
  ParagraphMenu: () => JSX.Element;
  fontMenu: () => JSX.Element;
  fontSizeMenu: () => JSX.Element;
  TableOperationsMenu: () => JSX.Element;
  getCurrentStep: () => number;
  handleOpenFile: () => void;
  chatId: string;
  activeTab: TabType;
  editor: any;
  saveEditorToTemporarily: () => void;
}

const Header: React.FC<HeaderProps> = ({
  handleEditorCommand,
  selectedFont,
  selectedFontSize,
  isAIEnabled,
  handleSummaryClick,
  onOpenSelectFile,
  ListStyleMenu,
  BulletStyleMenu,
  LineSpacingMenu,
  fontMenu,
  fontSizeMenu,
  TableOperationsMenu,
  handleOpenFile,
  activeTab,
  saveEditorToTemporarily,
  selectionState
}) => {
  const [isAIEnableds, setIsAIEnableds] = useState(false);
  useEffect(() => {
    setIsAIEnableds(isAIEnabled);
  }, [isAIEnabled]);

  const LeftContent = () => {
    const isFormatmatch = selectionState.formatmatch === 1;
    return (
      <Stack id="step3" direction="row" spacing="15px" alignItems="center" pr={4}>
        <Tooltip title="暂存" placement="bottom" trigger="hover">
          <Box
            onClick={() => saveEditorToTemporarily()}
            cursor="pointer"
            _hover={{
              backgroundColor: '#f3f4f6'
            }}
            display="flex"
            alignItems="center"
            justifyContent="center"
            width={rpxDim(28)}
            height={rpxDim(28)}
            borderRadius="8px"
          >
            <SvgIcon name="editSave" w={rpxDim(22)} h={rpxDim(22)} />
          </Box>
        </Tooltip>
        <Tooltip title="撤销" placement="bottom" trigger="hover">
          <Box
            onClick={() => handleEditorCommand?.('undo')}
            cursor="pointer"
            _hover={{
              backgroundColor: '#f3f4f6'
            }}
            display="flex"
            alignItems="center"
            justifyContent="center"
            width={rpxDim(28)}
            height={rpxDim(28)}
            borderRadius="8px"
          >
            <SvgIcon name="chatUndo" w={rpxDim(22)} h={rpxDim(22)} />
          </Box>
        </Tooltip>
        <Tooltip title="重做" placement="bottom" trigger="hover">
          <Box
            onClick={() => handleEditorCommand?.('redo')}
            cursor="pointer"
            _hover={{
              backgroundColor: '#f3f4f6'
            }}
            display="flex"
            alignItems="center"
            justifyContent="center"
            width={rpxDim(28)}
            height={rpxDim(28)}
            borderRadius="8px"
          >
            <SvgIcon name="chatUndoRedo" w={rpxDim(22)} h={rpxDim(22)} />
          </Box>
        </Tooltip>
      </Stack>
    );
  };

  const startContent = useCallback(() => {
    const isFormatmatch = selectionState.formatmatch === 1;
    return (
      <>
        <LeftContent />
        <Stack
          id="step3"
          direction="row"
          spacing="15px"
          alignItems="center"
          borderRight="1px solid var(--chakra-colors-gray-200)"
          borderLeft="1px solid var(--chakra-colors-gray-200)"
          pr={4}
          pl={4}
        >
          <Tooltip title="格式刷" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('formatmatch')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editPaint" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
          <Tooltip title="清除格式" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('removeformat')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="eraserLine" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
          <Box>
            <Dropdown overlay={fontMenu} className={styles['dropdown_del_border']}>
              <a
                className={styles['dropdown_a']}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '14px',
                  color: '#52525B'
                }}
              >
                {selectedFont} <SvgIcon name="chatDown" />
              </a>
            </Dropdown>
          </Box>

          <Box>
            <Dropdown overlay={fontSizeMenu} className={styles['dropdown_del_border']}>
              <a
                className={styles['dropdown_a']}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '14px',
                  color: '#52525B'
                }}
              >
                {selectedFontSize} <SvgIcon name="chatDown" />
              </a>
            </Dropdown>
          </Box>

          <Tooltip title="加粗" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('bold')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="chatBold" w={rpxDim(28)} h={rpxDim(28)} />
            </Box>
          </Tooltip>

          <Tooltip title="斜体" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('italic')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="chatItalic" w={rpxDim(28)} h={rpxDim(28)} />
            </Box>
          </Tooltip>

          <Tooltip title="下划线" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('underline')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="chatBottomLine" w={rpxDim(28)} h={rpxDim(28)} />
            </Box>
          </Tooltip>

          <Tooltip title="删除线" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('strikethrough')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="chatDelLine" w={rpxDim(28)} h={rpxDim(28)} />
            </Box>
          </Tooltip>
          <ColorPicker
            onChange={(color) => {
              handleEditorCommand?.('forecolor', color);
            }}
          />
        </Stack>
        <Stack
          id="step3"
          direction="row"
          spacing="15px"
          alignItems="center"
          borderRight="1px solid var(--chakra-colors-gray-200)"
          pr={4}
          pl={4}
        >
          <Tooltip title="一级标题" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('paragraph', 'h1')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editorH1" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
          <Tooltip title="二级标题" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('paragraph', 'h2')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editorH2" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
          <Tooltip title="三级标题" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('paragraph', 'h3')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editorH3" w={rpxDim(28)} h={rpxDim(28)} />
            </Box>
          </Tooltip>
        </Stack>
        <Stack
          id="step3"
          direction="row"
          spacing="15px"
          alignItems="center"
          borderRight="1px solid var(--chakra-colors-gray-200)"
          pr={4}
          pl={4}
        >
          <Tooltip title="左对齐" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('justify', 'left')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editorAlignLeft" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
          <Tooltip title="居中对齐" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('justify', 'center')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editorAlignCenter" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
          <Tooltip title="右对齐" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('justify', 'right')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editorAlignRight" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
          <Tooltip title="两端对齐" placement="bottom" trigger="hover">
            <Box
              onClick={() => handleEditorCommand?.('justify', 'justify')}
              cursor="pointer"
              _hover={{
                backgroundColor: '#f3f4f6'
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              width={rpxDim(28)}
              height={rpxDim(28)}
              borderRadius="8px"
            >
              <SvgIcon name="editorJustifyText" w={rpxDim(22)} h={rpxDim(22)} />
            </Box>
          </Tooltip>
        </Stack>
        <Stack id="step3" direction="row" spacing="15px" alignItems="center" pr={4} pl={4}>
          <Box>
            <Tooltip title="有序列表" placement="bottom" trigger="hover">
              <Dropdown overlay={ListStyleMenu} trigger={['click']}>
                <a
                  className={styles['dropdown_a']}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <SvgIcon name="chatNumberList" w={rpxDim(22)} h={rpxDim(22)} />{' '}
                  <SvgIcon name="chatDown" w={rpxDim(22)} h={rpxDim(22)} />
                </a>
              </Dropdown>
            </Tooltip>
          </Box>
          <Box>
            <Tooltip title="无序列表" placement="bottom" trigger="hover">
              <Dropdown overlay={BulletStyleMenu} trigger={['click']}>
                <a
                  className={styles['dropdown_a']}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <SvgIcon name="chatList" w={rpxDim(22)} h={rpxDim(22)} />{' '}
                  <SvgIcon name="chatDown" w={rpxDim(22)} h={rpxDim(22)} />
                </a>
              </Dropdown>
            </Tooltip>
          </Box>
          <Box>
            <Tooltip title="首行缩进" placement="bottom" trigger="hover">
              <Box
                onClick={() => handleEditorCommand?.('indent')}
                cursor="pointer"
                _hover={{
                  backgroundColor: '#f3f4f6'
                }}
                display="flex"
                alignItems="center"
                justifyContent="center"
                width={rpxDim(28)}
                height={rpxDim(28)}
                borderRadius="8px"
              >
                <SvgIcon name="chatAlignCenter" w={rpxDim(28)} h={rpxDim(28)} />
              </Box>
            </Tooltip>
          </Box>
          <Box>
            <Tooltip title="左缩进" placement="bottom" trigger="hover">
              <Box
                onClick={() => handleEditorCommand?.('indent')}
                cursor="pointer"
                _hover={{
                  backgroundColor: '#f3f4f6'
                }}
                display="flex"
                alignItems="center"
                justifyContent="center"
                width={rpxDim(28)}
                height={rpxDim(28)}
                borderRadius="8px"
              >
                <SvgIcon name="indentLeft" w={rpxDim(22)} h={rpxDim(22)} />
              </Box>
            </Tooltip>
          </Box>
          <Box>
            <Tooltip title="行间距" placement="bottom" trigger="hover">
              <Dropdown overlay={LineSpacingMenu} trigger={['click']}>
                <a
                  className={styles['dropdown_a']}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <SvgIcon name="chatSpacing" w={rpxDim(22)} h={rpxDim(22)} />{' '}
                  <SvgIcon name="chatDown" w={rpxDim(22)} h={rpxDim(22)} />
                </a>
              </Dropdown>
            </Tooltip>
          </Box>
        </Stack>
      </>
    );
  }, [selectionState]);

  const insertContent = () => {
    return (
      <>
        <LeftContent />
        <Stack
          id="step3"
          direction="row"
          spacing="15px"
          alignItems="center"
          borderLeft="1px solid var(--chakra-colors-gray-200)"
          pr={4}
          pl={4}
        >
          <Box
            h={rpxDim(28)}
            cursor="pointer"
            _hover={{
              backgroundColor: '#f3f4f6'
            }}
            display="flex"
            alignItems="center"
            justifyContent="center"
            borderRadius="8px"
            onClick={() => onOpenSelectFile()}
            px={2}
          >
            <SvgIcon name="chatImage" w={rpxDim(22)} h={rpxDim(22)} />
            图片
          </Box>
          <Box
            h={rpxDim(28)}
            cursor="pointer"
            _hover={{
              backgroundColor: '#f3f4f6'
            }}
            display="flex"
            alignItems="center"
            justifyContent="center"
            borderRadius="8px"
            onClick={() => handleEditorCommand?.('table')}
            px={2}
          >
            <Dropdown
              overlay={TableOperationsMenu}
              trigger={['click']}
              className={styles['dropdown_del_border']}
            >
              <a
                className={styles['dropdown_a']}
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <SvgIcon name="chatTable" w={rpxDim(22)} h={rpxDim(22)} /> 表格
              </a>
            </Dropdown>
          </Box>
          <Box
            cursor="pointer"
            _hover={{
              backgroundColor: '#f3f4f6'
            }}
            display="flex"
            alignItems="center"
            justifyContent="center"
            borderRadius="8px"
            onClick={() => handleEditorCommand?.('formula')}
          >
            <SvgIcon name="chatFormula" w={rpxDim(22)} h={rpxDim(22)} />
            公式
          </Box>
          <Box
            cursor="pointer"
            _hover={{
              backgroundColor: '#f3f4f6'
            }}
            display="flex"
            alignItems="center"
            justifyContent="center"
            h={rpxDim(28)}
            borderRadius="8px"
            onClick={() => handleOpenFile()}
            px={2}
          >
            <SvgIcon name="chatFile" w={rpxDim(22)} h={rpxDim(22)} />
            插入内容
          </Box>
        </Stack>
      </>
    );
  };

  const aiContent = () => {
    console.log('isAIEnable3333d', isAIEnabled);
    return (
      <>
        <LeftContent />
        <Stack
          id="step3"
          direction="row"
          spacing="15px"
          alignItems="center"
          borderLeft="1px solid var(--chakra-colors-gray-200)"
          pr={4}
          pl={4}
        >
          <Box className={styles['toolbar-icon-item']}>
            <Tooltip title="润色">
              <Box
                onMouseDown={(e) => handleSummaryClick(e, '润色')}
                className={styles['toolbar-icon-item']}
                onClick={(e) => {
                  if (isAIEnableds) {
                    handleSummaryClick(e, '润色');
                  } else {
                    Toast.info({ title: '请先选中文本，再进行操作' });
                  }
                }}
                // style={{
                //   cursor: isAIEnableds ? 'pointer' : 'not-allowed',
                //   opacity: isAIEnableds ? 1 : 0.5
                // }}
              >
                <SvgIcon name="chatRemark" w={rpxDim(22)} h={rpxDim(22)} />
                润色
              </Box>
            </Tooltip>
          </Box>

          <Box className={styles['toolbar-icon-item']}>
            <Tooltip title="改写">
              <Box
                onMouseDown={(e) => handleSummaryClick(e, '改写')}
                className={styles['toolbar-icon-item']}
                onClick={(e) => {
                  if (isAIEnableds) {
                    handleSummaryClick(e, '改写');
                  } else {
                    Toast.info({ title: '请先选中文本，再进行操作' });
                  }
                }}
                // style={{
                //   cursor: isAIEnableds ? 'pointer' : 'not-allowed',
                //   opacity: isAIEnableds ? 1 : 0.5
                // }}
              >
                <SvgIcon name="chatRewrite" w={rpxDim(22)} h={rpxDim(22)} />
                改写
              </Box>
            </Tooltip>
          </Box>

          <Box className={styles['toolbar-icon-item']}>
            <Tooltip title="扩写">
              <Box
                onMouseDown={(e) => handleSummaryClick(e, '扩写')}
                className={styles['toolbar-icon-item']}
                onClick={(e) => {
                  if (isAIEnableds) {
                    handleSummaryClick(e, '扩写');
                  } else {
                    Toast.info({ title: '请先选中文本，再进行操作' });
                  }
                }}
                // style={{
                //   cursor: isAIEnableds ? 'pointer' : 'not-allowed',
                //   opacity: isAIEnableds ? 1 : 0.5
                // }}
              >
                <SvgIcon name="chatAbbrAI" w={rpxDim(22)} h={rpxDim(22)} />
                扩写
              </Box>
            </Tooltip>
          </Box>

          <Box className={styles['toolbar-icon-item']}>
            <Tooltip title="续写">
              <Box
                onMouseDown={(e) => handleSummaryClick(e, '续写')}
                className={styles['toolbar-icon-item']}
                onClick={(e) => isAIEnableds && handleSummaryClick(e, '续写')}
              >
                <SvgIcon name="chatContentAI" w={rpxDim(22)} h={rpxDim(22)} /> 续写
              </Box>
            </Tooltip>
          </Box>

          <Box className={styles['toolbar-icon-item']}>
            <Tooltip title="缩写">
              <Box
                onMouseDown={(e) => handleSummaryClick(e, '缩写')}
                className={styles['toolbar-icon-item']}
                onClick={(e) => {
                  if (isAIEnableds) {
                    handleSummaryClick(e, '缩写');
                  } else {
                    Toast.info({ title: '请先选中文本，再进行操作' });
                  }
                }}
                // style={{
                //   cursor: isAIEnableds ? 'pointer' : 'not-allowed',
                //   opacity: isAIEnableds ? 1 : 0.5
                // }}
              >
                <SvgIcon w={rpxDim(22)} h={rpxDim(22)} name="chatAbbr" /> 缩写
              </Box>
            </Tooltip>
          </Box>

          <Box className={styles['toolbar-icon-item']}>
            <Tooltip title="总结">
              <Box
                onMouseDown={(e) => handleSummaryClick(e, '总结')}
                className={styles['toolbar-icon-item']}
                onClick={(e) => {
                  if (isAIEnableds) {
                    handleSummaryClick(e, '总结');
                  } else {
                    Toast.info({ title: '请先选中文本，再进行操作' });
                  }
                }}
                // style={{
                //   cursor: isAIEnableds ? 'pointer' : 'not-allowed',
                //   opacity: isAIEnableds ? 1 : 0.5
                // }}
              >
                <SvgIcon w={rpxDim(22)} h={rpxDim(22)} name="chatGroup" /> 总结
              </Box>
            </Tooltip>
          </Box>
        </Stack>
      </>
    );
  };

  const contenEum = {
    start: startContent,
    insert: insertContent,
    ai: aiContent
  } as const;

  const [currentContent, setCurrentContent] = useState({
    content: contenEum[activeTab](),
    key: activeTab
  });

  useEffect(() => {
    // 当 tab 改变时，先添加退出动画类
    const container = document.querySelector(`.${styles['content-container']}`);
    if (container) {
      container.classList.add(styles['exit']);

      // 等待退出动画完成后更新内容
      setTimeout(() => {
        setCurrentContent({
          content: contenEum[activeTab](),
          key: activeTab
        });
        container.classList.remove(styles['exit']);
      }, 200); // 匹配 CSS 动画时间
    }
  }, [activeTab]);

  useEffect(() => {
    if (activeTab === 'ai') {
      setCurrentContent({
        content: contenEum[activeTab](),
        key: activeTab
      });
    }
  }, [isAIEnableds]);

  return (
    <Affix offsetTop={0}>
      <Box id="toolbar">
        <Box className={styles['toolbar']} height={rpxDim(56)} display="flex" alignItems="center">
          <Box className={styles['content-container']} key={currentContent.key} alignItems="center">
            {currentContent.content}
          </Box>
        </Box>
      </Box>
    </Affix>
  );
};

export default Header;
