.draggable-tree {
  :global {
    .ant-tree-node-selected {
      background-color: #fff !important;

      &:hover {
        background-color: #fff !important;
      }
    }

    // .ant-tree-drop-indicator {
    //   display: none !important;
    // }

    .ant-tree-switcher-icon {
      padding-top: 12px;
    }

    .ant-tree-node-content-wrapper {
      background-color: transparent;
      overflow: hidden;
      &:hover {
        background-color: transparent !important;
      }
    }

    .ant-tree-treenode {
      &:hover {
        border-radius: 4px;
        background-color: #f8fafc !important;
      }
      &:hover {
        .ant-tree-switcher {
          opacity: 0;
        }
      }
    }

    .ant-tree-draggable-icon {
      display: none;
      width: 0;
    }
  }
}
