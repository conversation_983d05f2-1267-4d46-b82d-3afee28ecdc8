import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Box, BoxProps, Button, Flex, HStack } from '@chakra-ui/react';
import MyBox from '@/components/common/MyBox';
import SvgIcon from '@/components/SvgIcon';
import OverviewTree, { OverviewTreeNode } from './OverviewTree';
import { InitModeEnum } from '../..';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { useGuideStep } from '../GuideStepModal/GuideStepContext';
import { FormData2ChatDataType } from '@/types/chat';
import {
  checkJsonFields,
  formData2ChatData,
  getChatMessages,
  getJsonFromText,
  getParseResult
} from '@/utils/chat';
import { chats2GPTMessages, updateChatListWithParseResult } from '@/fastgpt/global/core/chat/adapt';
import { streamFetch } from '@/utils/fetch';
import { useChatMessage } from '@/hooks/chat/useChatMessage';
import { treeToList, treeTraverse } from '@/utils/tree';
import { Toast } from '@/utils/ui/toast';
import { useRequest } from '@/hooks/useRequest';
import { Component } from '@/types/api/app';
import { ComponentType } from '@/constants/api/app';
import { FileMetaType } from '@/types/api/file';
import Lottie from '@/components/Lottie';
import { respDims } from '@/utils/chakra';
import { useUpdateEffect } from 'ahooks';
import { promisifyConfirm } from '@/utils/ui/messageBox';
import { outlineTemplate } from '@/utils/chatTemplate';
const OverviewModalInner = ({
  appId,
  ...props
}: {
  appId: string;
} & BoxProps) => {
  const {
    setOverviewTreeData,
    overviewTreeData,
    setEditType,
    generateFormData,
    setGenerateType,
    generateType,
    setReturnFormData,
    returnFormData,
    returnDocumentTitle,
    activeTab,
    setGenerateOverviewLoading,
    generateOverviewLoading,
    setReturnDocumentTitle
  } = useDeepEditStore();
  const { onMessage, intervalText } = useChatMessage();

  // console.log(intervalText);
  const { setStep, onSuccess, onClose, formDetail, initChatInfo, appDetail } = useGuideStep();
  const getMessages = useCallback(async () => {
    try {
      if (generateFormData) {
        const chatData = formData2ChatData(generateFormData, formDetail!);

        const { text, images, files, ocrFileKey, components } = chatData || {};
        const componentsList = treeToList(components || []);

        const parseResult = await getParseResult({
          files: [...(files || []), ...(images || [])],
          appId,
          chatId: '',
          chatConfig: initChatInfo?.app.chatConfig,
          ocrFileKey
        });

        const form_data = await Promise.all(
          componentsList.map(async (component: Component) => {
            if (component.type == ComponentType.Upload) {
              const value = component.value as FileMetaType[];
              if (value.length > 0) {
                const fileValue = value.map((item: FileMetaType) => {
                  return {
                    filename: item.fileName,
                    url: item.fileUrl,
                    full_text: component.isCallOcr
                      ? parseResult?.TemplateFileParsingResult?.Files[0]?.FileContent
                      : parseResult?.UploadedFileParsingResult?.Files.find(
                          (file: any) => file.FileName == item.fileName
                        )?.FileContent
                  };
                });
                return {
                  label: component.title,
                  value: fileValue
                };
              } else {
                return {
                  label: component.title,
                  value: []
                };
              }
            } else {
              return {
                label: component.title,
                value: Array.isArray(component.value)
                  ? component.value.join(',')
                  : component.value || ''
              };
            }
          })
        );

        setReturnFormData(form_data);

        const chatText = JSON.stringify({
          form_data
        });

        let { newChatList } = getChatMessages({
          text: chatText,
          files: [...(files || []), ...(images || [])]
        });

        const messages = chats2GPTMessages({
          messages: newChatList,
          reserveId: true
        }).slice(0, -1);
        return messages;
      }
    } catch (error) {
      console.error(error);
    }

    return [];
  }, [generateFormData, formDetail, initChatInfo, appId, setReturnFormData]);
  const abortCtrlOverview = useRef<AbortController | null>(null);
  const { mutateAsync, isLoading: isGenerating } = useRequest({
    mutationFn: async () => {
      try {
        setGenerateOverviewLoading(true);
        if (generateFormData) {
          try {
            const messages = await getMessages();

            const abortSignal = new AbortController();
            abortCtrlOverview.current = abortSignal;

            const { responseText, responseData } = await streamFetch({
              url: '/huayun-ai/client/chat/once',
              onMessage,
              abortCtrl: abortSignal,
              data: {
                messages,
                appId: appDetail?.finalAppId
              }
            });
            if (!responseText) {
              return;
            }
            const parseResult = await getJsonFromText(responseText);

            if (!checkJsonFields(parseResult, outlineTemplate)) {
              Toast.error('生成大纲失败,请检查返回内容');
              return;
            }

            if (parseResult.outline && parseResult.outline.length) {
              let i = 0;
              treeTraverse(parseResult.outline, (node, parent, level) => {
                node.id = i + '';
                node.key = i + '';
                i++;
              });

              setOverviewTreeData(parseResult.outline);
              setReturnDocumentTitle(parseResult.title || parseResult.documentTitle);
              setStep(1);
            }
          } catch (error) {
            console.error(error);
            Toast.error('生成大纲失败');
          }
        } else {
          console.error('表单校验失败:');
        }
      } catch (error) {}
      setGenerateOverviewLoading(false);
    }
  });
  const handlePrepareClass = () => {
    onSuccess?.();
    onClose();
    setGenerateType('content');
  };

  useUpdateEffect(() => {
    if (activeTab == 1) {
      mutateAsync({});
    }
  }, [activeTab]);

  return (
    <MyBox
      display="flex"
      flexDir="column"
      width="100%"
      h="100%"
      position="relative"
      overflow="hidden"
      {...props}
    >
      <Box flex={1} overflow="hidden" h="100%" display="flex" flexDir="column">
        {generateOverviewLoading ? (
          <Flex flexDirection="column" minH="500px" justifyContent="center" alignItems="center">
            <Lottie name="generateLoading" w="100px" h="100px" />
            <Box color="#4E5969" fontSize={respDims(14, 13)}>
              大纲正在生成中，麻烦您等候片刻哦~
            </Box>
          </Flex>
        ) : (
          <>
            <Box
              fontSize={respDims(16, 14)}
              fontWeight="bold"
              pl={respDims(24, 14)}
              pt={respDims(20, 20)}
              pb={respDims(16, 12)}
            >
              {returnDocumentTitle}
            </Box>
            <OverviewTree treeData={overviewTreeData || []} onChange={setOverviewTreeData} />
          </>
        )}
      </Box>
      <HStack
        mt="10px"
        left="0"
        w="100%"
        h="80px"
        bg="#FFF"
        px="20px"
        pb="10px"
        borderTop="1px #eee solid"
        spacing={5}
      >
        <Button
          variant="grayBase"
          onClick={() => {
            promisifyConfirm({
              title: '提示',
              content: '返回上一步后，将会导致大纲数据清空或者生成终止，您确定要返回吗。'
            }).then(() => {
              setStep(0);
              setGenerateType(undefined);
              setGenerateOverviewLoading(false);
              abortCtrlOverview.current?.abort();
            });
          }}
          w="50%"
        >
          返回上一步
        </Button>
        <Button
          width="50%"
          color="#fff"
          height="36px"
          display="flex"
          justifyContent="center"
          cursor="pointer"
          alignItems="center"
          background="#7D4DFF"
          borderRadius="8px 8px 8px 8px"
          isDisabled={!overviewTreeData?.length || generateOverviewLoading}
          onClick={() => handlePrepareClass()}
        >
          开始一键生成
        </Button>
      </HStack>
    </MyBox>
  );
};

export default OverviewModalInner;
