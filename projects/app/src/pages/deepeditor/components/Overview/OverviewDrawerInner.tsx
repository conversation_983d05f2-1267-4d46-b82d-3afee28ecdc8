import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Box, BoxProps, Button, Center, Flex, useUpdateEffect } from '@chakra-ui/react';
import MyBox from '@/components/common/MyBox';
import { useRouter } from 'next/router';
import OverviewTree, { OverviewTreeNode } from './OverviewTree';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { treeMap, treeToList, treeTraverse } from '@/utils/tree';
import { respDims } from '@/utils/chakra';
import { InitModeEnum } from '../..';
import { useDrawer } from '../LeftPlatform/DrawerContext';
import { formData2ChatData, getChatMessages, getParseResult } from '@/utils/chat';
import { chats2GPTMessages } from '@/fastgpt/global/core/chat/adapt';
import { streamFetch } from '@/utils/fetch';
import { useChatMessage } from '@/hooks/chat/useChatMessage';
import { marked } from 'marked';
import Lottie from '@/components/Lottie';
import { Component } from '@/types/api/app';
import { ComponentType } from '@/constants/api/app';
import { FileMetaType } from '@/types/api/file';
import { useRequest } from '@/hooks/useRequest';
import { Toast } from '@/utils/ui/toast';
import { throttle } from 'lodash';
import { handleFormula } from '@/utils/export';

const OverviewDrawerInner = ({
  appId,
  ...props
}: {
  appId: string;
} & BoxProps) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [buttonText, setButtonText] = useState('开始一键生成');
  const {
    overviewTreeData,
    setOverviewTreeData,
    setReturnDocumentTitle,
    generateType,
    returnFormData,
    generateFormData,
    setReturnFormData,
    setGenerateType,
    returnDocumentTitle,
    setGenerateOverviewLoading,
    generateOverviewLoading,
    setGenerateContentLoading
  } = useDeepEditStore();
  const { query } = router;
  const initMode = query.initMode as InitModeEnum;
  const { initChatInfo, setActiveTabIndex, formDetail, editor, leftDrawerStatus, appDetail } =
    useDrawer();
  const [parseLoading, setParseLoading] = useState(false);

  const abortCtrl = useRef<AbortController | null>(null);
  const abortCtrlOverview = useRef<AbortController | null>(null);

  const markdownRegex = /[#*_\[\]`~\-]/; // 简单的正则表达式，用于检测 Markdown 特征

  const throttledChatMessage = useCallback(
    throttle(
      (subText, fullText) => {
        try {
          const html = handleFormula(fullText);
          const parseRes: string = marked.parse(html) as string;
          editor?.setContent(parseRes);
        } catch (error) {
          console.error('Markdown parsing error:', error);
        }

        const editorDocument =
          editor?.iframe.contentDocument || editor?.iframe.contentWindow.document;
        if (editorDocument) {
          editorDocument.documentElement.scrollTop = editorDocument?.documentElement.scrollHeight;
        }
      },
      300, // 300ms 的节流时间，可以根据需要调整
      { trailing: true } // 确保最后一次调用会被执行
    ),
    [editor]
  );

  const { onMessage, intervalText, setText, isGeneratingBeforeText, setIsGeneratingBeforeText } =
    useChatMessage(throttledChatMessage);

  const { onMessage: onMessage2 } = useChatMessage((str) => {});
  const getMessages = useCallback(async () => {
    try {
      setParseLoading(true);
      if (generateFormData) {
        const chatData = formData2ChatData(generateFormData, formDetail!);
        const { text, images, files, ocrFileKey, components } = chatData || {};
        const componentsList = treeToList(components || []);

        const parseResult = await getParseResult({
          files: [...(files || []), ...(images || [])],
          appId,
          chatId: '',
          chatConfig: initChatInfo?.app.chatConfig,
          ocrFileKey
        });

        const form_data = await Promise.all(
          componentsList.map(async (component: Component) => {
            if (component.type == ComponentType.Upload) {
              const value = component.value as FileMetaType[];
              if (value.length > 0) {
                const fileValue = value.map((item: FileMetaType) => {
                  return {
                    filename: item.fileName,
                    url: item.fileUrl,
                    full_text: component.isCallOcr
                      ? parseResult?.TemplateFileParsingResult?.Files[0]?.FileContent
                      : parseResult?.UploadedFileParsingResult?.Files.find(
                          (file: any) => file.FileName == item.fileName
                        )?.FileContent
                  };
                });
                return {
                  label: component.title,
                  value: fileValue
                };
              } else {
                return {
                  label: component.title,
                  value: []
                };
              }
            } else {
              return {
                label: component.title,
                value: Array.isArray(component.value)
                  ? component.value.join(',')
                  : component.value || ''
              };
            }
          })
        );

        setReturnFormData(form_data);

        const chatText = JSON.stringify({
          form_data
        });

        let { newChatList } = getChatMessages({
          text: chatText,
          files: [...(files || []), ...(images || [])]
        });

        const messages = chats2GPTMessages({
          messages: newChatList,
          reserveId: true
        }).slice(0, -1);
        return messages;
      }
    } catch (error) {
      console.error(error);
    } finally {
      setParseLoading(false);
    }

    return [];
  }, [generateFormData, formDetail, initChatInfo, appId, setReturnFormData]);

  const { mutateAsync: mutateOverviewAsync, isLoading: isGenerating } = useRequest({
    mutationFn: async () => {
      setGenerateOverviewLoading(true);
      setGenerateType(undefined);
      try {
        if (isGenerating) {
          abortCtrlOverview.current?.abort();
          return;
        }
        if (generateFormData) {
          const messages = await getMessages();

          const abortSignal = new AbortController();
          abortCtrlOverview.current = abortSignal;

          const { responseText } = await streamFetch({
            url: '/huayun-ai/client/chat/once',
            onMessage: onMessage2,
            abortCtrl: abortSignal,
            data: {
              messages,
              appId: appDetail?.finalAppId
            }
          });
          try {
            // 使用正则表达式提取JSON字符串
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);

            const parseResult = JSON.parse(jsonMatch?.[0] || '{}');
            if (!responseText) {
              Toast.error('生成内容为空');
              return;
            }

            if (parseResult.outline && parseResult.outline.length) {
              let i = 0;
              treeTraverse(parseResult.outline, (node, parent, level) => {
                node.id = i + '';
                node.key = i + '';
                i++;
              });

              setOverviewTreeData(parseResult?.outline || []);
              setReturnDocumentTitle(parseResult?.documentTitle || '');
            }
          } catch (error: any) {
            console.error(error);
            setActiveTabIndex(0);
            const errorMessage = error?.toString().includes(`Unexpected end of JSON in`)
              ? '解析大纲失败,返回内容不是JSON格式'
              : '解析大纲失败';
            Toast.error(errorMessage);
          }
        } else {
          console.error('表单校验失败:');
        }
      } catch (error) {
        console.error(error);
      } finally {
        setGenerateOverviewLoading(false);
      }
    }
  });

  const handlePrepareClass = useCallback(async () => {
    if (overviewTreeData?.length == 0) {
      return;
    }

    if (loading) {
      abortCtrl.current?.abort();
      return;
    }
    try {
      editor?.setContent('');
      setText('');
      const outline = treeMap(overviewTreeData || [], (node) => {
        return {
          title: node.title,
          content: node.content,
          ...(node.children && node.children.length > 0 ? { children: node.children } : {})
        };
      });

      const chatText = JSON.stringify({
        form_data: returnFormData,
        documentTitle: returnDocumentTitle,
        outline
      });

      let { newChatList, humanDataId } = getChatMessages({
        text: chatText,
        files: []
      });
      const messages = chats2GPTMessages({
        messages: newChatList,
        reserveId: true
      }).slice(0, -1);

      const abortSignal = new AbortController();
      abortCtrl.current = abortSignal;
      setLoading(true);
      setIsGeneratingBeforeText(true);
      const { responseText, responseData } = await streamFetch({
        url: '/huayun-ai/client/chat/once',
        onMessage,
        abortCtrl: abortSignal,
        data: {
          messages,

          appId: appDetail?.finalAppId
        }
      });
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
    setIsGeneratingBeforeText(false);
  }, [
    overviewTreeData,
    setIsGeneratingBeforeText,
    returnDocumentTitle,
    abortCtrl,
    onMessage,
    loading,
    returnFormData,
    editor,
    setText
  ]);

  useEffect(() => {
    setGenerateContentLoading(isGeneratingBeforeText);
  }, [isGeneratingBeforeText, setGenerateContentLoading]);

  useUpdateEffect(() => {
    if (generateType == 'content' && !loading) {
      handlePrepareClass();
    }
  }, [generateType]);

  useUpdateEffect(() => {
    if (generateType == 'overview' && !isGenerating) {
      if (loading) {
        abortCtrl.current?.abort();
      }

      mutateOverviewAsync({});
    }
  }, [generateType]);

  useUpdateEffect(() => {
    if (loading) {
      const dots = ['.', '..', '...'];
      let index = 0;
      const interval = setInterval(() => {
        setButtonText(`生成中${dots[index]}，点击暂停`);
        index = (index + 1) % dots.length;
      }, 500); // 每500毫秒更新一次

      return () => clearInterval(interval); // 清除定时器
    } else {
      setButtonText('重新生成内容');
    }
  }, [loading]);

  return (
    <MyBox
      display="flex"
      flexDir="column"
      width="100%"
      h="100%"
      position="relative"
      overflow="hidden"
      {...props}
    >
      <Box flex={1} overflow="hidden" h="100%" display="flex" flexDir="column">
        {generateOverviewLoading ? (
          <Flex flexDirection="column" minH="500px" justifyContent="center" alignItems="center">
            <Lottie name="generateLoading" w="100px" h="100px" />
            <Box color="#4E5969" fontSize={respDims(14, 13)}>
              大纲正在生成中，麻烦您等候片刻哦~
            </Box>
          </Flex>
        ) : !overviewTreeData?.length ? (
          <Flex flexDirection="column" minH="500px" justifyContent="center" alignItems="center">
            <Box color="#4E5969" fontSize={respDims(14, 13)}>
              请先根据表单生成大纲
            </Box>
          </Flex>
        ) : (
          <>
            <Box
              fontSize={respDims(16, 14)}
              fontWeight="bold"
              pl={respDims(24, 14)}
              pt={respDims(20, 20)}
              pb={respDims(16, 12)}
            >
              {returnDocumentTitle}
            </Box>
            <OverviewTree treeData={overviewTreeData || []} onChange={setOverviewTreeData} />
          </>
        )}
      </Box>
      <Box w="100%" bg="#FFF" p="24px">
        <Button
          width="100%"
          variant="primaryOutline"
          isDisabled={!overviewTreeData?.length}
          fontWeight="500"
          onClick={() => handlePrepareClass()}
        >
          {buttonText}
        </Button>
      </Box>
    </MyBox>
  );
};

export default OverviewDrawerInner;
