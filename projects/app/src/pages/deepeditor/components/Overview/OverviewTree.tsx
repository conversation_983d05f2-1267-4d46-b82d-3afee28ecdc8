import { Tree, Input, Dropdown, Menu } from 'antd';
import type { DataNode, TreeProps } from 'antd/es/tree';
import React, { useState, useEffect, useCallback } from 'react';
import { PlusOutlined, DeleteOutlined, DragOutlined, DownOutlined } from '@ant-design/icons';
import { Box, BoxProps, Center, Flex, HStack } from '@chakra-ui/react';
import { treeToList } from '@/utils/tree';
import { respDims } from '@/utils/chakra';
import { useDroppable } from '@dnd-kit/core';
import { DragHandleIcon } from '@chakra-ui/icons';
import SvgIcon from '@/components/SvgIcon';
import { promisifyConfirm } from '@/utils/ui/messageBox';
import styles from './OverviewTree.module.scss';
import MyMenu from '@/components/MyMenu';
import { Textarea } from '@chakra-ui/react';

export interface OverviewTreeNode {
  key: string;
  id: string;
  title: string;
  content?: string;
  children: OverviewTreeNode[];
}

interface OverviewTreeProps {
  treeData: OverviewTreeNode[];
  onChange: (newData: OverviewTreeNode[]) => void;
  expandedKeys?: string[];
  onExpandedKeysChange?: (newKeys: string[]) => void;
  boxProps?: BoxProps;
}

const OverviewTree: React.FC<OverviewTreeProps> = ({
  treeData,
  expandedKeys: expandedKeys = [],
  onChange: onGDataChange,
  onExpandedKeysChange,
  boxProps
}) => {
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [dragOverKey, setDragOverKey] = useState('');

  const generateKey = () => `node-${Math.random().toString(36).substr(2, 9)}`;

  const onAddSibling = useCallback(
    (key: string) => {
      const addSibling = (nodes: OverviewTreeNode[]): OverviewTreeNode[] => {
        return nodes
          .map((node) => {
            if (node.id === key) {
              const newKey = generateKey();
              return [node, { title: '', content: '', id: newKey, key: newKey, children: [] }];
            }
            if (node.children) {
              return {
                ...node,
                children: addSibling(node.children)
              };
            }
            return node;
          })
          .flat();
      };
      onGDataChange(addSibling(treeData));
    },
    [treeData, onGDataChange]
  );

  const onAddChild = useCallback(
    (key: string) => {
      const addChild = (nodes: OverviewTreeNode[]): OverviewTreeNode[] => {
        return nodes.map((node) => {
          if (node.id === key) {
            const newKey = generateKey();
            return {
              ...node,
              children: [
                ...(node.children || []),
                { title: '', content: '', id: newKey, key: newKey, children: [] }
              ]
            };
          }
          if (node.children) {
            return {
              ...node,
              children: addChild(node.children)
            };
          }
          return node;
        });
      };
      onGDataChange(addChild(treeData));
    },
    [treeData, onGDataChange]
  );

  const onDelete = useCallback(
    async (key: string) => {
      await promisifyConfirm({
        content: '确定要删除章节吗？',
        okText: '删除',
        cancelText: '取消'
      });
      const deleteNode = (nodes: OverviewTreeNode[]): OverviewTreeNode[] => {
        return nodes
          .filter((node) => node.id !== key)
          .map((node) => {
            if (node.children) {
              return {
                ...node,
                children: deleteNode(node.children)
              };
            }
            return node;
          });
      };
      onGDataChange(deleteNode(treeData));
    },
    [treeData, onGDataChange]
  );

  const onEdit = useCallback(
    (key: string, newTitle: string, type: 'title' | 'content') => {
      const editNode = (nodes: OverviewTreeNode[]): OverviewTreeNode[] => {
        return nodes.map((node) => {
          if (node.id === key) {
            return {
              ...node,
              [type]: newTitle
            };
          }
          if (node.children) {
            return {
              ...node,
              children: editNode(node.children)
            };
          }
          return node;
        });
      };
      onGDataChange(editNode(treeData));
    },
    [treeData, onGDataChange]
  );

  const onDrop: TreeProps['onDrop'] = (info) => {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
    const cloneDeepTreeData = JSON.parse(JSON.stringify(treeData));
    const loop = (
      data: OverviewTreeNode[],
      key: React.Key,
      callback: (node: OverviewTreeNode, i: number, data: OverviewTreeNode[]) => void
    ) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children!, key, callback);
        }
      }
    };
    const data = [...cloneDeepTreeData];

    // Find dragObject
    let dragObj: OverviewTreeNode;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!info.dropToGap) {
      // Drop on the content
      loop(data, dropKey, (item) => {
        item.children = item.children || [];
        // where to insert 示例添加到，可以是随意位置
        item.children.unshift(dragObj);
      });
    } else {
      let ar: OverviewTreeNode[] = [];
      let i: number;
      loop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i!, 0, dragObj!);
      } else {
        ar.splice(i! + 1, 0, dragObj!);
      }
    }
    onGDataChange(data);
  };

  const TreeTitle = useCallback(
    ({ node, level }: { node: OverviewTreeNode; level: number }) => {
      const [isDropdownHovered, setIsDropdownHovered] = useState(false);

      return (
        <Flex
          w="100%"
          display="flex"
          alignItems="flex-start"
          flexDir="column"
          justify="space-between"
          py={respDims(8, 6)}
          px={respDims(6)}
          pos="relative"
          onDoubleClick={() => setEditingKey(node.id)}
          _hover={{
            '.hover-actions': {
              opacity: 1
            },
            '.drag-icon': {
              opacity: 1
            }
          }}
        >
          <Flex flex={1} alignItems="center" minH={respDims(28)} pr={respDims(80)}>
            <Center
              pr={respDims(4, 4)}
              opacity="0"
              className="drag-icon"
              pos="absolute"
              left="-24px"
              pb={respDims(4, 4)}
            >
              <SvgIcon name="move" fontSize={respDims(16, 14)} color="#626774" />
            </Center>
            <Flex alignItems="center">
              <Box
                as="div"
                contentEditable
                suppressContentEditableWarning
                onBlur={(e) => {
                  onEdit(node.id, e.currentTarget.textContent || '', 'title');
                }}
                minW="100px"
                onDrag={(e) => {
                  e.preventDefault();
                  console.log('drag');
                }}
                placeholder="请输入标题"
                style={{
                  paddingLeft: 0,
                  fontWeight: level === 0 ? '500' : '400',
                  fontSize: level === 0 ? '16px' : '14px',
                  outline: 'none',
                  minHeight: '28px',
                  width: '100%',
                  wordBreak: 'break-word'
                }}
                css={
                  node.title
                    ? {}
                    : {
                        '&::before': {
                          content: 'attr(placeholder)',
                          position: 'absolute',
                          color: 'rgba(0, 0, 0, 0.3)',
                          pointerEvents: 'none',
                          userSelect: 'none'
                        },
                        '&:focus::before': {
                          content: 'none'
                        }
                      }
                }
              >
                {node.title}
              </Box>
            </Flex>
          </Flex>
          {!node.children?.length && (
            <Flex flex={1} alignItems="center" minH={respDims(28)}>
              <Box
                as="div"
                minW="100px"
                contentEditable
                suppressContentEditableWarning
                onBlur={(e) => {
                  onEdit(node.id, e.currentTarget.textContent || '', 'content');
                }}
                onDrag={(e) => {
                  e.preventDefault();
                  console.log('drag');
                }}
                placeholder="请输入章节描述"
                style={{
                  paddingLeft: 0,
                  color: 'rgba(34, 47, 64, .75)',
                  outline: 'none',
                  minHeight: '28px',
                  width: '100%',
                  wordBreak: 'break-word'
                }}
                css={
                  node.content
                    ? {}
                    : {
                        '&::before': {
                          content: 'attr(placeholder)',
                          position: 'absolute',
                          color: 'rgba(0, 0, 0, 0.3)',
                          pointerEvents: 'none',
                          userSelect: 'none'
                        },
                        '&:focus::before': {
                          content: 'none'
                        }
                      }
                }
              >
                {node.content || ''}
              </Box>
            </Flex>
          )}

          <HStack
            className="hover-actions"
            pos="absolute"
            opacity={isDropdownHovered ? 1 : 0}
            right="6px"
            top="6px"
          >
            <Center
              bg="#fff"
              p={respDims(6)}
              borderRadius="4px"
              _hover={{
                color: 'primary.500'
              }}
              // color="#626774"
            >
              <SvgIcon name="trash" onClick={() => onDelete(node.id)} w={respDims(18)} />
            </Center>

            <Center
              bg="#fff"
              p={respDims(6)}
              borderRadius="4px"
              _hover={{
                color: 'primary.500'
              }}
              color="#626774"
            >
              <Dropdown
                overlayStyle={{
                  zIndex: 9999,
                  position: 'absolute'
                }}
                menu={{
                  items: [
                    {
                      label: '增加同级大纲',
                      key: 'addSibling',
                      onClick: () => onAddSibling(node.id)
                    },
                    ...(level >= 2
                      ? ([] as any)
                      : ([
                          {
                            label: '增加子级大纲',
                            key: 'addChild',
                            onClick: () => onAddChild(node.id)
                          }
                        ] as any))
                  ],
                  onMouseEnter: () => setIsDropdownHovered(true),
                  onMouseLeave: () => setIsDropdownHovered(false)
                }}
              >
                <SvgIcon name="plus" />
              </Dropdown>
            </Center>
          </HStack>
        </Flex>
      );
    },
    [onAddSibling, onAddChild, onDelete, onEdit]
  );

  const renderTreeNodes = (data: OverviewTreeNode[], level: number) =>
    data.map((item) => {
      return (
        <Tree.TreeNode
          {...item}
          title={<TreeTitle node={item} level={level} />}
          key={item.key}
          dragOver
        >
          {!!item.children?.length && renderTreeNodes(item.children, level + 1)}
        </Tree.TreeNode>
      );
    });

  return (
    <Box
      flex={1}
      overflow="auto"
      mx={respDims(24, 14)}
      border="1px solid #E5E7EB"
      borderRadius={respDims(14, 12)}
      p={respDims(16, 12)}
      {...boxProps}
    >
      {treeData?.length > 0 ? (
        <Tree
          className={styles['draggable-tree']}
          defaultExpandedKeys={[...treeToList(treeData).map((item) => item.id)]}
          expandedKeys={[...treeToList(treeData).map((item) => item.id)]}
          draggable={{
            icon: <></>
          }}
          showLine={{
            showLeafIcon: <></>
          }}
          switcherIcon={
            <Center pt={respDims(12, 6)}>
              <SvgIcon name="caretDownSmall" />
            </Center>
          }
          blockNode
          onDrop={onDrop}
        >
          {renderTreeNodes(treeData, 0)}
        </Tree>
      ) : (
        <></>
      )}
    </Box>
  );
};

export default OverviewTree;
