'use client';
import { Box, Text, Flex, Input } from '@chakra-ui/react';
import { Affix, Dropdown, Menu, Tooltip, Upload, Button, Modal, message } from 'antd';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import styles from './EditorBox.module.scss';
import SvgIcon from '@/components/SvgIcon';
import { respDims, rpxDim } from '@/utils/chakra';
import { useSelectFile } from '@/hooks/useSelectFile';
import { uploadFile, uploadImage } from '@/utils/file';
import { getErrText } from '@/utils/string';
import { Toast } from '@/utils/ui/toast';
import Loading from '@/components/Loading';
import mammoth from 'mammoth';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { FileMetaType } from '@/types/api/file';
import Chooser, {
  ChooserFileType,
  ChooserModeEnum,
  ModalModeEnum
} from '@/pages/cloud/list/components/Chooser';
import EnhanceContent from './components/EnhanceContent';
import CustomTour from '@/components/CustomTour';
import { parse } from 'marked';
import { marked } from 'marked';
const { Dragger } = Upload;
import LayoutOverlay from '@/components/LayoutOverlay';
import { useDeepEditStore, useSaveObj } from '@/store/useDeepEdit';
import { formatChatValue2InputType } from '@/utils/chat';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useChatStore } from '@/store/useChatStore';
import LatexEasyEditor from './components/LatexEasyEditor';
import {
  tableOperations,
  fontSizeOptions,
  paragraphOptions,
  fontOptions,
  listStyleOptions,
  bulletStyleOptions,
  lineSpacingOptions,
  alignOptions,
  ParagraphKeys,
  AlignOptions
} from './menu';
import { UploadChangeParam } from 'antd/es/upload';
import { downloadFile } from '@/api/file';
import {
  getEditorCloudReference,
  saveEditorToCloud,
  saveEditorToCloudTemporarily,
  getTemporarilyDetail,
  TemporarilyListResponse
} from '@/api/cloud';
import htmlDocx from 'html-docx-js/dist/html-docx';
import { saveAs } from 'file-saver';
import LeftPlatform from './components/LeftPlatform';
import RightPlatform from './components/RightPlatform';
import Header from './components/Header';
import { TabType } from './components/Tabs';
import TopTitleBar from './components/TopTitleBar';
import TaskGrid from './components/TaskTypeModal';
import JSZip, { JSZipObject } from 'jszip';
import { set } from 'lodash';
import { GetServerSideProps } from 'next';
import GuideStepModal from './components/GuideStepModal';
import { serviceSideProps } from '@/utils/i18n';
import { Document as DocxDocument, Packer, Paragraph, TextRun } from 'docx';
import Lottie from '@/components/Lottie';
import { convertHtmlToXhtml } from '@/utils/html';
import {
  convertHtmlToPdf,
  convertHtmlToWord,
  convertMdToHtml,
  convertWordToHtml,
  covertHtmlToMarkdown,
  reportAppsProduceDocument
} from '@/api/chat';
import { convertLatexImgToFormula, handleFormula, removeHeight } from '@/utils/export';

const placeholderText = `您可以通过以下方式快速编辑或创作内容：\n 1、在此处键入或粘贴需要进行编辑的文本内容，可以是一段文字、整篇文章或文档中的内容，\n2、点击 （导入内容）按钮，从本地或数据空间选择需要进行编辑的文档内容`;
interface IEditorBoxProps {
  value?: string;
  onChange?: (value: string) => void;
  appId?: string;
  appTaskTypeId?: string;
}

declare global {
  interface Window {
    updateEnhanceContent?: (position: { top: number; left: number }) => void;
    isSummaryClick?: boolean;
  }
}

export enum InitModeEnum {
  FORM = 'form',
  OVERVIEW = 'overview'
}

export const EditorBox = (props: IEditorBoxProps) => {
  const { value = '', onChange, appId, appTaskTypeId } = props;
  const router = useRouter();
  const editorRef = useRef<HTMLDivElement | null>(null);
  const editorWrapRef = useRef<HTMLDivElement | null>(null);
  const [editor1, setEditor] = useState<any>(null);
  const [selectedParagraphFormat, setSelectedParagraphFormat] =
    useState<keyof typeof ParagraphKeys>('段落格式');
  const [selectedFont, setSelectedFont] = useState<string>('字体');
  const [selectedJustify, setSelectedJustify] = useState<keyof typeof AlignOptions>('对齐方式');
  const [selectedFontSize, setSelectedFontSize] = useState<string>('字号');
  //字体

  const [selectionState, setSelectionState] = useState({
    bold: 0,
    italic: 0,
    underline: false,
    strikethrough: false,
    formatmatch: 0,
    isJustifyLeft: false,
    isJustifyCenter: false,
    isJustifyRight: false,
    isJustifyJustify: false,
    isH1: false,
    isH2: false,
    isH3: false
  });
  const [openFile, setOpenFile] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [html, setHtml] = useState('');
  const [fileList, setFileList] = useState<any[]>([]);
  const [isChooserOpen, setIsChooserOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<ChooserFileType[]>([]);
  const [title, setTitle] = useState<string>('未命名');
  const [isTemporarilyDetail, setIsTemporarilyDetail] = useState<any>();
  const [isUnblur, setIsUnblur] = useState<number>(0);
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isSaveToCloud, setIsSaveToCloud] = useState(false);
  const scrollTopButtonRef = useRef<any>(null);
  const overlayRef = useRef<any>(null);
  const enhanceContentRef = useRef<any>(null);
  const [selectedText, setSelectedText] = useState('');
  const [selectedLatexText, setSelectedLatexText] = useState('');

  // 通过convertLatexImgToFormula将图片公式转为md
  const [fullLatexText, setFullLatexText] = useState('');
  const [enhanceContentVisible, setEnhanceContentVisible] = useState(false);
  const [isSaveflag, setIsSaveflag] = useState(false);
  const [enhanceContentPosition, setEnhanceContentPosition] = useState<{
    top: number;
    left: number;
    type?: 'top' | 'bottom';
  } | null>(null);
  const [selectedOperationType, setSelectedOperationType] = useState<string>('');
  const [isAIEnabled, setIsAIEnabled] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [isRecentSave, setIsRecentSave] = useState(false);
  // 公式弹窗
  const [isFormulaVisible, setIsFormulaVisible] = useState(false);
  const LatexEasyRef = useRef<{ getFormula: () => any; setFormula: () => any }>(null);
  const id = 'editor1';

  const { chatId, getCurrentStep, currentStep } = useChatStore();

  // 获取深度编辑的chatItem和modalOpen
  const {
    chatItem,
    modalOpen,
    setEditType,
    editType,
    setSaveObj,
    generateContentLoading,
    setDeepEditChatItem
  } = useDeepEditStore();
  const saveObj = useSaveObj();

  const { openOverlay } = useOverlayManager();

  const [prevStep, setPrevStep] = useState(currentStep);
  const [isScrolling, setIsScrolling] = useState(false);
  const [enhanceContentType, setEnhanceContentType] = useState<string>('');
  const [editorContent, setEditorContent] = useState<string>('');
  const [activeTab, setActiveTab] = useState<TabType>('start');
  const [labels, setLabels] = useState<string>('');
  const refreshListRef = useRef<() => Promise<void>>();
  const [saveFlag, setSaveFlag] = useState<string>('');
  const [cursorIndicator, setCursorIndicator] = useState({
    visible: false,
    top: 0
  });
  // 修改这个 useEffect
  useEffect(() => {
    if (prevStep === 2 && currentStep === 1) {
      // 在这里添加您需要执行的操作
      setEditType('new');
      window.close();
    }
    setPrevStep(currentStep);
  }, [currentStep]);

  const handleSendingStatusChange = (sending: boolean) => {
    setIsSending(sending);
  };

  const scrollToTop = () => {
    if (editor1) {
      const editorDocument =
        editor1.iframe.contentDocument || editor1.iframe.contentWindow.document;
      editorDocument.documentElement.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // useEffect(() => {
  //   const handleKeyDown = (event: KeyboardEvent) => {
  //     if ((event.ctrlKey || event.metaKey) && event.key === 's') {
  //       event.preventDefault();
  //       handleSave(editorContent);
  //     }
  //   };
  //   window.addEventListener('keydown', handleKeyDown);
  //   return () => {
  //     window.removeEventListener('keydown', handleKeyDown);
  //   };
  // }, []);

  useEffect(() => {
    const updateEnhanceContent = (position: {
      top: number;
      left: number;
      type?: 'top' | 'bottom';
    }) => {
      console.log('position', position);
      setEnhanceContentPosition(position);
      setIsScrolling(false);
      setEnhanceContentType('');
    };

    const resetSelectedOperationType = () => {
      setSelectedOperationType('');
    };
    window.addEventListener('resetSelectedOperationType', resetSelectedOperationType);

    window.updateEnhanceContent = updateEnhanceContent;

    const handleShowEnhanceContent = () => {
      setEnhanceContentVisible(true);
      setLabels('AI编辑');
    };

    const handleShowChatremarkContent = () => {
      console.log('handleShowChatremarkContent');
      setEnhanceContentVisible(true);
      setLabels('改写');
    };

    const handleShowChatAbbrContent = () => {
      setEnhanceContentVisible(true);
      setLabels('扩写');
    };

    const handleShowChatSummaryContent = () => {
      setEnhanceContentVisible(true);
      setLabels('总结');
    };

    const handleShowChatContinuationContent = () => {
      setEnhanceContentVisible(true);
      setLabels('续写');
    };

    const handleShowChatPolishContent = () => {
      setEnhanceContentVisible(true);
      setLabels('润色');
    };

    const handleShowChatAbbreviationContent = () => {
      setEnhanceContentVisible(true);
      setLabels('缩写');
    };

    window.addEventListener('showEnhanceContent', handleShowEnhanceContent);
    window.addEventListener('showChatremarkContent', handleShowChatremarkContent);
    window.addEventListener('showChatAbbrContent', handleShowChatAbbrContent);
    window.addEventListener('showChatSummaryContent', handleShowChatSummaryContent);
    window.addEventListener('showChatContinuationContent', handleShowChatContinuationContent);
    window.addEventListener('showChatPolishContent', handleShowChatPolishContent);
    window.addEventListener('showChatAbbreviationContent', handleShowChatAbbreviationContent);
    window.addEventListener('chatsummary', handleShowChatSummaryContent);
    return () => {
      delete window.updateEnhanceContent;
      window.removeEventListener('showEnhanceContent', handleShowEnhanceContent);
      window.removeEventListener('showChatremarkContent', handleShowChatremarkContent);
      window.removeEventListener('showChatAbbrContent', handleShowChatAbbrContent);
      window.removeEventListener('showChatSummaryContent', handleShowChatSummaryContent);
      window.removeEventListener('showChatContinuationContent', handleShowChatContinuationContent);
      window.removeEventListener('showChatPolishContent', handleShowChatPolishContent);
      window.removeEventListener('showChatAbbreviationContent', handleShowChatAbbreviationContent);
      window.removeEventListener('resetSelectedOperationType', resetSelectedOperationType);
      window.removeEventListener('chatsummary', handleShowChatSummaryContent);
    };
  }, []);

  const updateSelectionState = useCallback(() => {
    if (editor1) {
      const isBold = editor1.queryCommandState('bold');
      const isItalic = editor1.queryCommandState('italic');
      const isUnderline = editor1.queryCommandState('underline');
      const isStrikethrough = editor1.queryCommandState('strikethrough');
      const isFormatmatch = editor1.queryCommandState('formatmatch');
      const isJustifyLeft = editor1.queryCommandState('justifyleft');
      const isJustifyCenter = editor1.queryCommandState('justifycenter');
      const isJustifyRight = editor1.queryCommandState('justifyright');
      const isJustifyJustify = editor1.queryCommandState('justifyjustify');
      const isH1 = editor1.queryCommandState('h1');
      const isH2 = editor1.queryCommandState('h2');
      const isH3 = editor1.queryCommandState('h3');

      setSelectionState((prevState) => {
        if (
          prevState.bold !== isBold ||
          prevState.italic !== isItalic ||
          prevState.underline !== isUnderline ||
          prevState.strikethrough !== isStrikethrough ||
          prevState.formatmatch !== isFormatmatch ||
          prevState.isJustifyLeft !== isJustifyLeft ||
          prevState.isJustifyCenter !== isJustifyCenter ||
          prevState.isJustifyRight !== isJustifyRight ||
          prevState.isJustifyJustify !== isJustifyJustify ||
          prevState.isH1 !== isH1 ||
          prevState.isH2 !== isH2 ||
          prevState.isH3 !== isH3
        ) {
          return {
            bold: isBold,
            italic: isItalic,
            underline: isUnderline,
            strikethrough: isStrikethrough,
            formatmatch: isFormatmatch,
            isJustifyLeft: isJustifyLeft,
            isJustifyCenter: isJustifyCenter,
            isJustifyRight: isJustifyRight,
            isJustifyJustify: isJustifyJustify,
            isH1: isH1,
            isH2: isH2,
            isH3: isH3
          };
        }
        return prevState;
      });
      const selectionText = editor1.selection.getText();
      setIsAIEnabled(!!selectionText.toString());
    }
  }, [editor1]);

  useEffect(() => {
    if (editor1) {
      editor1.addListener('selectionchange', updateSelectionState);
    }
    return () => {
      if (editor1) {
        editor1.removeListener('selectionchange', updateSelectionState);
      }
    };
  }, [editor1, updateSelectionState]);

  useEffect(() => {
    // 更新编辑器高度
    const updateEditorHeight = () => {
      const toolbarHeight = document.getElementById('toolbar')?.offsetHeight || 0;
      const titleBarHeight = document.getElementById('topTitleBar')?.offsetHeight || 0;
      const availableHeight = window.innerHeight - toolbarHeight - titleBarHeight;
      if (editorRef.current) {
        editorRef.current.style.height = `${availableHeight - 200}px`;
      }
    };

    window.addEventListener('resize', updateEditorHeight);
    updateEditorHeight();

    return () => {
      window.removeEventListener('resize', updateEditorHeight);
    };
  }, []);

  // 获取选中的HTML内容
  const getSelectedHtml = () => {
    try {
      if (editor1) {
        const range = editor1.selection.getRange();
        if (range) {
          // 创建临时容器
          const container = document.createElement('div');
          // 将选区内容克隆到临时容器
          container?.appendChild(range.cloneContents());
          // 获取HTML内容
          const selectedHtml = container.innerHTML;
          return selectedHtml;
        }
      }
    } catch (error) {
      console.error('获取选中内容失败:', error);
    }
    return '';
  };

  useEffect(() => {
    const handleSelectionChange = () => {
      //获取鼠标点击位置
      if (editor1) {
        const selectionText = editor1.selection.getText();
        const selection = window.getSelection();
        setSelectedText(selectionText.toString());

        // 将图片公式转为md获取selectionLatexText，给ai编辑使用
        let selectionHtml = getSelectedHtml();
        let selectionLatexText = convertLatexImgToFormula(selectionHtml);
        const parse = new DOMParser();
        const doc = parse.parseFromString(selectionLatexText, 'text/html');
        selectionLatexText = doc.body.textContent || '';
        setSelectedLatexText(selectionLatexText);

        setIsAIEnabled(false);
        if (selection && selection.rangeCount > 0) {
          const selectedText = selection.toString().trim();
          setIsAIEnabled(!!selectedText);

          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();
          showFloatingWindow(rect, selectedText);
        } else {
          setIsAIEnabled(false);
          hideFloatingWindow();
        }
      }
    };

    if (editor1) {
      editor1.addListener('selectionchange', handleSelectionChange);
    }
    return () => {
      if (editor1) {
        editor1.removeListener('selectionchange', handleSelectionChange);
      }
    };
  }, [editor1, window.getSelection()]);

  const handleFormulaChange = (newFormula: string) => {};

  const onFormulaCancel = () => {
    setIsFormulaVisible(false);
    if (LatexEasyRef?.current) {
      LatexEasyRef.current.setFormula();
    }
  };

  const getFormulas = async () => {
    if (LatexEasyRef?.current) {
      const formula = await LatexEasyRef?.current.getFormula();
      editor1.execCommand('formula', formula);
      editor1.fireEvent('saveScene');
      if (editor1) {
        editor1.execCommand('insertimage', {
          src: formula
        });
      }
      setIsFormulaVisible(false);
      LatexEasyRef?.current.setFormula();
    }
  };

  const handleTitleClick = () => {
    setIsEditingTitle(true);
  };

  const handleRefreshCallback = (refresh: () => Promise<void>) => {
    refreshListRef.current = refresh;
  };

  const saveAPI = useCallback(
    async (content: string, titles?: string) => {
      const urlParams = new URLSearchParams(window.location.search);
      const id = urlParams.get('id');
      if (titles === '未命名') {
        const timestamp = Date.now();
        titles = `${titles}_${timestamp}`;
        setTitle(titles);
      }
      setSaveObj({
        saveStatus: 'saving',
        updateTime: new Date().toISOString()
      });
      saveEditorToCloudTemporarily({
        id: id || '',
        title: titles || '',
        fileJson: content
      })
        .then((res) => {
          setSaveObj({
            saveStatus: 'saved',
            updateTime: res.updateTime
          });
          const url = new URL(window.location.href);
          url.searchParams.set('id', res.id || '');
          window.history.replaceState({}, '', url.toString());
          Toast.success({
            title: '文档已暂存'
          });
          refreshListRef.current?.();
        })
        .catch((err) => {
          Toast.error({
            title: getErrText(err, '暂存失败，请稍后重试')
          });
        });
    },
    [title]
  );

  useEffect(() => {
    console.log('isEditingTitle', isEditingTitle);
    if (editor1 && isUnblur) {
      try {
        const content = editor1.getContent();
        saveAPI(content, title);
      } catch (error) {
        console.error('Error getting content from editor:', error);
      }
    } else {
      console.warn('Editor is not initialized');
    }
  }, [isUnblur, editor1]);

  const handleSave = useCallback(
    (content?: any, titles?: string) => {
      try {
        if (content) {
          saveAPI(content, titles);
        }
      } catch (error) {
        console.error('Error saving content:', error);
      }
    },
    [saveAPI, title]
  );
  const handleTitleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      console.log('e.target.value', e.target.value);
      if (e.target.value !== '' && editor1) {
        const newTitle = e.target.value;
        setTitle(newTitle);
        setIsEditingTitle(false);
        setIsUnblur(Math.random());
      }
    },
    [setTitle, editor1]
  );

  useEffect(() => {
    if (editor1) {
      const keydownHandler = (type: string, e: KeyboardEvent) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
          e.preventDefault();
          handleSave(editor1?.getContent(), title);
        }
      };

      editor1.addListener('keydown', keydownHandler);

      return () => {
        editor1.removeListener('keydown', keydownHandler);
      };
    }
  }, [editor1, title, handleSave]);

  useEffect(() => {
    const saveInterval = setInterval(() => {
      if (editor1) {
        handleSave(editor1.getContent(), title);
      }
    }, 60000); // 1 minute

    return () => {
      clearInterval(saveInterval);
    };
  }, [editor1, title]);

  const showFloatingWindow = (rect: DOMRect, content: string) => {};

  const hideFloatingWindow = () => {
    const floatingWindow = document.getElementById('floatingWindow');
    if (floatingWindow) {
      floatingWindow.style.display = 'none';
    }
  };

  const handleParagraphMenuClick = (e: any, cmd: string) => {
    if (cmd === 'paragraph') {
      setSelectedParagraphFormat(e.key);
    } else if (cmd === 'fontsize') {
      setSelectedFontSize(e.key);
    } else if (cmd === 'fontfamily') {
      setSelectedFont(e.key);
    } else if (cmd === 'justify') {
      setSelectedJustify(e.key);
    }
    handleEditorCommand(cmd, e.key);
  };

  const TableOperationsMenu = () => (
    <Menu onClick={(e) => handleEditorCommand(e.key)}>
      {tableOperations.map((item) => {
        return (
          <Menu.Item title={item.title} key={item.key}>
            {item.title}
          </Menu.Item>
        );
      })}
    </Menu>
  );

  const fontSizeMenu = () => (
    <Menu
      onClick={(e) => handleParagraphMenuClick(e, 'fontsize')}
      style={{ maxHeight: '300px', overflowY: 'auto' }}
    >
      {fontSizeOptions.map(({ key, size }) => (
        <Menu.Item key={key} style={{ fontSize: size }}>
          {size}
        </Menu.Item>
      ))}
    </Menu>
  );

  // 段落格式菜单
  const ParagraphMenu = () => {
    return (
      <Menu onClick={(e) => handleParagraphMenuClick(e, 'paragraph')}>
        {paragraphOptions.map(({ key, label, style }) => (
          <Menu.Item key={key} style={style}>
            {label}
          </Menu.Item>
        ))}
      </Menu>
    );
  };
  // 字体菜单
  const fontMenu = () => {
    return (
      <Menu onClick={(e) => handleParagraphMenuClick(e, 'fontfamily')}>
        {fontOptions.map(({ key, label }) => (
          <Menu.Item key={key}>{label}</Menu.Item>
        ))}
      </Menu>
    );
  };

  // 列表样式菜单
  const ListStyleMenu = () => {
    return (
      <Menu onClick={(e) => handleParagraphMenuClick(e, 'insertorderedlist')}>
        {listStyleOptions.map(({ key, label }) => (
          <Menu.Item key={key}>{label}</Menu.Item>
        ))}
      </Menu>
    );
  };
  // 项目符号样式菜单
  const BulletStyleMenu = () => {
    return (
      <Menu onClick={(e) => handleParagraphMenuClick(e, 'insertunorderedlist')}>
        {bulletStyleOptions.map(({ key, label }) => (
          <Menu.Item key={key}>{label}</Menu.Item>
        ))}
      </Menu>
    );
  };
  // 行间距菜单
  const LineSpacingMenu = () => {
    return (
      <Menu onClick={(e) => handleParagraphMenuClick(e, 'lineheight')}>
        {lineSpacingOptions.map(({ key, label }) => (
          <Menu.Item key={key}>{label}</Menu.Item>
        ))}
      </Menu>
    );
  };

  // 对齐方式
  const AlignMenu = () => {
    return (
      <Menu onClick={(e) => handleParagraphMenuClick(e, 'justify')}>
        {alignOptions.map(({ key, label }) => (
          <Menu.Item key={key}>{label}</Menu.Item>
        ))}
      </Menu>
    );
  };

  //选择图片
  const { File: Files, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png,.jpeg',
    multiple: false
  });

  const onSelectFile = async (e: any) => {
    const file = e[0];
    if (!file) return;
    setIsUploading(true);
    try {
      const data = await uploadImage(file, {
        maxWidthOrHeight: 300
      });
      if (editor1) {
        editor1.execCommand('insertimage', {
          src: data.fileUrl
        });
      }
    } catch (err: any) {
      Toast.warning({
        title: getErrText(err, '上传图片失败')
      });
    } finally {
      setIsUploading(false);
    }
  };

  useEffect(() => {
    console.log('saveFlag', saveFlag);
    if (editor1) {
      handleSave(editor1.getContent(), title);
    }
  }, [saveFlag]);

  const saveEditorToTemporarily = () => {
    setSaveFlag(Math.random().toString());
  };

  const handleEditorCommand = useCallback(
    (command: string, value?: any) => {
      if (!editor1) return;
      if (value) {
        editor1.execCommand(command, value);
      } else {
        editor1.execCommand(command);
      }
      // 如果command为公式 打开公式弹窗
      if (command == 'formula') {
        setIsFormulaVisible(true);
      }
      const saveHtml = convertHtmlToXhtml(exportWordDocument());
      if (command == 'save') {
        openOverlay({
          Overlay: Chooser,
          props: {
            title: '保存至数据空间',
            modalMode: ModalModeEnum.Save,
            showCreateFolderBtn: true,
            filename: title,
            htmlStr: saveHtml,
            setTitle,
            onSuccess(files, inputFileName) {
              saveEditorToCloud();
              setIsSaveToCloud(true);
              handleSave(saveHtml, title);
              reportAppsProduceDocument({ tenantAppId: appId as string });
            }
          }
        });
      }
    },
    [editor1, title, handleSave]
  );

  // 打开dialog
  const handleOpenFile = () => {
    setFileList([]);
    setOpenFile(true);
  };

  const handleSelectFromCloud = (event: React.MouseEvent) => {
    event.stopPropagation();
    setIsChooserOpen(true);
  };

  const handleRemoveFile = (file: any) => {
    setFileList([]);
    setHtml('');
  };

  // 读取文件流转换成html格式
  const convertFileToHtml = (file: Blob, callback: (html: string, fileList: any[]) => void) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e?.target?.result;
      console.log('content', content);
      mammoth
        .convertToHtml(
          {
            arrayBuffer: content as ArrayBuffer
          },
          {
            styleMap: [
              "p[style-name='文档正文'] => p.body-text", // 映射 "文档正文" 为特定类名的段落
              'table => table.custom-table' // 自定义表格样式
            ]
          }
        )
        .then((result) => {
          console.log('result', result);
          callback(result.value, []);
        })
        .catch((error) => {
          console.error('Error converting document:', error);
        });
    };
    reader.readAsArrayBuffer(file);
  };

  // 使用示例
  document.addEventListener('DOMContentLoaded', () => {
    const fileInput = document.getElementById('fileInput') as HTMLElement;
    const output = document.getElementById('output') as HTMLElement;

    if (fileInput) {
      fileInput.addEventListener('change', (event: Event) => {
        const target = event.target as HTMLInputElement;
        if (target && target.files && target.files.length > 0) {
          const file = target.files[0];
          if (file) {
            convertFileToHtml(file, (html, fileList) => {
              output.innerHTML = html; // 将转换后的 HTML 显示在页面上
            });
          }
        }
      });
    }
  });

  const handleUpload = async (info: UploadChangeParam) => {
    setIsUploading(true);

    if (info.file.status == 'done') {
      const file = info.fileList[0].originFileObj;
      if (
        !file ||
        file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        return false;
      }
      try {
        // 上传文件
        const uploadedFiles = await Promise.all(
          info.fileList.map((file: any) => {
            if (
              file.originFileObj &&
              file.originFileObj.type ===
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ) {
              return uploadFile(file.originFileObj, {});
            }
            return Promise.resolve(file);
          })
        );

        // 更新文件列表状态
        const updatedFileList = uploadedFiles.map((file: FileMetaType) => {
          return {
            uid: file.fileKey,
            name: file.fileName,
            fileSize: file?.fileSize,
            fileType: file?.fileType,
            status: 'done',
            url: file?.fileUrl
          };
        });

        if (file) {
          const formData = new FormData();
          formData.append('file', file as any);
          convertWordToHtml(formData).then((res) => {
            setTimeout(() => {
              setFileList([...updatedFileList]);
              let html = handleFormula(res);
              html = parse(html) as string;
              if (!html) message.error('word格式有误,请编辑该word后重新导入');
              setUploadSuccess(true);
              setIsUploading(false);
              // 不再直接设置内容到编辑器，而是保存到state中
              setHtml(html);
              // 确保Modal对话框显示
              setOpenFile(true);
            }, 100);
          });
        }
      } catch (err) {
        setIsUploading(false); // 发生错误时停止加载
        Toast.warning({ title: '上传失败，请重试。' }); // 提示用户
      }
    }
  };

  const handleChooserSuccess = async (files: ChooserFileType[]) => {
    // 只支持上传docx格式文件
    if (files.length > 0 && !files[0].fileUrl?.includes('.docx')) {
      Toast.warning({
        title: '只支持上传docx格式文件'
      });
      return;
    }

    // 更新文件列表状态
    const updatedFileList = files.map((file: any) => {
      return {
        uid: file.fileKey,
        name: file.fileName,
        fileSize: file?.fileSize,
        fileType: file?.fileType,
        status: 'done',
        url: file?.fileUrl
      };
    });

    if (files.length > 0) {
      const fileKey: string = files[0]?.fileKey || '';
      if (!fileKey) return;
      try {
        const res = await downloadFile(fileKey);
        const formData = new FormData();
        formData.append('file', res.data);
        convertWordToHtml(formData).then((res) => {
          setTimeout(() => {
            setFileList([...updatedFileList]);
            const html = handleFormula(res);
            const parseRes: string = parse(html) as string;
            if (!parseRes) message.error('word格式有误,请编辑该word后重新导入');
            setUploadSuccess(true);
            setIsChooserOpen(false);
            // 不再直接设置内容到编辑器，而是保存到state中
            setHtml(parseRes);
            // 确保Modal对话框显示
            setOpenFile(true);
          }, 100);
        });
      } catch (error) {
        Toast.warning({
          title: '文件下载或转换失败'
        });
      }
    }
  };

  const handleChooserClose = () => {
    setIsChooserOpen(false);
  };

  const beforeUpload = (file: any) => {
    // 清空上一次的文件列表
    setFileList([]);
    // 仅支持docx格式文件上传 且不能大于10mb
    if (
      file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      file.size > 10 * 1024 * 1024
    ) {
      setIsUploading(false);
      Toast.warning({
        title: '仅支持docx格式文件上传且文件不能大于10mb'
      });

      return false;
    }
  };

  //弹窗点击确认
  const handleConfirm = () => {
    if (editor1) {
      setTimeout(() => {
        editor1.setContent(html, true);
        const titleName = fileList[0].name.replace('.docx', '');
        // 手动触发编辑器内容更新
        editor1.fireEvent('contentchange');
        setOpenFile(false);
        setTitle(titleName);
        setFileList([]); // 清空文件列表
      }, 100);
    }
  };
  const searchParamId = new URL(window.location.href).searchParams.get('id');

  useEffect(() => {
    if (searchParamId && editor1) {
      setIsUploading(true);

      getTemporarilyDetail({ id: searchParamId })
        .then((res) => {
          const { fileJson, title } = res;

          // 1. 确保编辑器已完全初始化
          if (!editor1.isReady) {
            editor1.addListener('ready', () => {
              safeSetContent(fileJson, title);
            });
          } else {
            safeSetContent(fileJson, title);
          }
        })
        .catch((err) => {
          console.error('Failed to load content:', err);
          setIsUploading(false);
          Toast.error({ title: '加载内容失败' });
        });
    }

    // 安全的设置内容方法
    function safeSetContent(content: string, docTitle: string) {
      try {
        // 2. 多重检查确保内容可以设置
        if (editor1 && content) {
          // 3. 使用 requestAnimationFrame 确保DOM更新
          requestAnimationFrame(() => {
            // 4. 尝试多种方式设置内容
            try {
              editor1.setContent(content);
            } catch (e) {
              console.warn('First setContent attempt failed, trying alternative', e);

              // 5. 备选方案：直接设置body内容
              const editorDoc = editor1.getDocument();
              if (editorDoc && editorDoc.body) {
                editorDoc.body.innerHTML = content;
              }
            }

            setTitle(docTitle || '');
            setIsUploading(false);

            // 6. 确保触发内容变更事件
            setTimeout(() => {
              editor1.fireEvent('contentchange');
            }, 100);
          });
        }
      } catch (err) {
        console.error('Error setting content:', err);
        setIsUploading(false);
      }
    }
  }, [editor1, searchParamId]);

  // 处理公式

  // 初始化编辑器
  useEffect(() => {
    let ueditor: any;

    const initializeEditor = () => {
      const { text }: any = formatChatValue2InputType(chatItem.value);
      console.log('text', text);
      const htmlContent = handleFormula(text) || '';
      const html: string = parse(htmlContent) as string;
      import('../../../public/static/ueditor/ueditor.all.js').then((UE) => {
        const ueditorObj: any = UE.default;
        ueditor = ueditorObj.getEditor(id, {
          toolbars: [],
          initialContent: html,
          wordCount: false,
          enableAutoSave: true,
          shortcutMenu: [
            'myblockquote',
            'chatPolish', // 润色
            'chatRemark', // 改写
            'chatExpand', // 扩写
            'chatContinuation', // 续写
            'chatAbbreviation', // 缩写
            'chatSummary', // 总结
            'formula' // 公式
          ],
          initialFrameWidth: '100%'
        });
        ueditor.addListener('keydown', async (type: string, e: KeyboardEvent) => {
          if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
          }
          // if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
          //   e.preventDefault();
          //   try {
          //     const clipboardItems = await navigator.clipboard.read();
          //     console.log('剪贴板内容:', clipboardItems);

          //     // 检查剪贴板中是否有图片
          //     let hasHandledContent = false;

          //     // 尝试处理图片内容
          //     for (const item of clipboardItems) {
          //       try {
          //         // 检查是否有图片类型
          //         const types = item.types;
          //         const imageType = types.find((type: string) => type.startsWith('image/'));

          //         if (imageType) {
          //           // 处理图片粘贴
          //           const imageBlob = await item.getType(imageType);
          //           const imageUrl = URL.createObjectURL(imageBlob);

          //           // 将图片插入编辑器
          //           ueditor.execCommand('insertimage', {
          //             src: imageUrl,
          //             alt: '粘贴的图片'
          //           });

          //           hasHandledContent = true;
          //           break;
          //         }
          //       } catch (e) {
          //         console.warn('处理剪贴板图片失败:', e);
          //       }
          //     }

          //     // 如果没有图片，尝试处理文本内容
          //     if (!hasHandledContent) {
          //       try {
          //         let content = '';
          //         let isHtml = false;

          //         // 先尝试获取HTML格式
          //         try {
          //           const htmlBlob = await clipboardItems[0].getType('text/html');
          //           content = await htmlBlob.text();
          //           isHtml = true;
          //           console.log('获取到HTML格式内容');
          //         } catch (htmlError) {
          //           // 如果没有HTML格式，获取纯文本
          //           console.log('没有HTML格式，尝试获取纯文本');
          //           const textBlob = await clipboardItems[0].getType('text/plain');
          //           content = await textBlob.text();
          //         }

          //         console.log('粘贴的原始内容:', content);

          //         // 无论是HTML还是纯文本，都先处理其中的公式
          //         let processedContent = handleFormula(content);
          //         console.log('处理公式后的内容:', processedContent);

          //         // 如果是纯文本，转换为HTML
          //         if (!isHtml) {
          //           processedContent = marked(processedContent) as string;
          //           console.log('转换为HTML后的内容:', processedContent);

          //           // 对转换后的HTML内容再次处理公式，确保所有公式都被转换
          //           processedContent = handleFormula(processedContent);
          //           console.log('再次处理公式后的HTML内容:', processedContent);
          //         }

          //         // 将处理后的内容插入到编辑器
          //         if (ueditor.selection) {
          //           ueditor.execCommand('inserthtml', processedContent);
          //         } else {
          //           // 如果没有选择区域，添加到内容末尾
          //           const currentContent = ueditor.getContent() || '';
          //           ueditor.setContent(currentContent + processedContent);
          //         }

          //         // 手动触发内容更新事件
          //         ueditor.fireEvent('contentchange');
          //       } catch (e) {
          //         console.warn('处理剪贴板内容失败:', e);
          //         // 失败时使用编辑器默认粘贴功能
          //         ueditor.execCommand('paste');
          //       }
          //     }

          //     // 手动触发内容更新事件
          //     ueditor.fireEvent('contentchange');
          //   } catch (error) {
          //     console.error('粘贴处理失败:', error);
          //     // 如果出错，允许默认粘贴行为
          //     ueditor.execCommand('paste');
          //   }
          // }
        });

        ueditor.addListener('ready', function () {
          const editorBody = ueditor.body;
          const placeholder = document.createElement('div');
          placeholder.className = 'editor-placeholder';
          placeholder.innerText = placeholderText;
          placeholder.style.cssText = `
            position: absolute;
            top: 10px;
            left: 10px;
            color: #aaa;
            pointer-events: none;
            font-size: 14px;
          `;
          editorBody.parentNode.style.position = 'relative';
          editorBody.parentNode.appendChild(placeholder);

          // 添加粘贴事件监听
          ueditor.addListener(
            'beforepaste',
            async function (type: string, html: { html: string }, root: any) {
              const originalHtml = html.html;
              html.html = ''; // 清空原始html内容

              if (
                originalHtml.includes('class="markdown') ||
                originalHtml.includes('katex-mathml')
              ) {
                html.html = originalHtml;
                return;
              }
              let isHtml = false;
              try {
                setIsUploading(true);
                // 获取剪贴板内容
                const clipboardData = await navigator.clipboard
                  .read()
                  .then(async (items) => {
                    for (const item of items) {
                      // 优先尝试获取 HTML 格式
                      if (item.types.includes('text/html')) {
                        const blob = await item.getType('text/html');
                        isHtml = true;
                        return await blob.text();
                      }
                      // 如果没有 HTML 格式，尝试获取富文本
                      if (item.types.includes('text/rtf')) {
                        const blob = await item.getType('text/rtf');
                        return await blob.text();
                      }
                      // 最后尝试获取纯文本
                      if (item.types.includes('text/plain')) {
                        const blob = await item.getType('text/plain');
                        return await blob.text();
                      }
                    }
                    return '';
                  })
                  .catch(() => {
                    // 如果 Clipboard API 不可用，尝试使用 clipboardData
                    const clipboardEvent = new ClipboardEvent('paste');
                    const clipboardData = clipboardEvent.clipboardData;
                    if (clipboardData) {
                      return (
                        clipboardData.getData('text/html') ||
                        clipboardData.getData('text/rtf') ||
                        clipboardData.getData('text/plain')
                      );
                    }
                    return '';
                  });

                if (!clipboardData) {
                  setIsUploading(false);
                  return;
                }
                let clipboardData2 = clipboardData.replace(/&amp;/g, '');

                //   .replace(/<meta[^>]*>/g, '') // 移除所有meta标签
                //   .replace(/&quot;/g, '"')
                //   .replace(/style="max-width: 100%; cursor: pointer;"/g, '')
                //   .replace(/style="max-width: 100%; cursor: pointer; display: block;"/g, '')
                //   .replace(/data-formula-image="[^"]*"/g, '') // 移除data-formula-image属性
                //   .trim();

                if (
                  clipboardData2.includes('-apple-system') ||
                  clipboardData2.includes('data-formula-image')
                ) {
                  // 使用DOMParser解析HTML并清理样式
                  const parser = new DOMParser();
                  const doc = parser.parseFromString(clipboardData2, 'text/html');

                  // 移除所有元素的style属性
                  const allElements = doc.querySelectorAll('*');
                  allElements.forEach((element) => {
                    element.removeAttribute('style');
                    element.removeAttribute('data-formula-image');
                  });

                  // 移除所有style标签
                  const styleElements = doc.querySelectorAll('style');
                  styleElements.forEach((style) => {
                    style.parentNode?.removeChild(style);
                  });

                  // 将清理后的HTML转回字符串
                  clipboardData2 = new XMLSerializer().serializeToString(doc.body);
                  // 如果输出包含<body>标签，移除它们
                  clipboardData2 = clipboardData2.replace(/<\/?body[^>]*>/g, '');
                }

                // 转换为markdown
                const mdStr = await covertHtmlToMarkdown({
                  htmlStr: clipboardData2
                }).catch((err) => {
                  console.error('Convert to markdown failed:', err);
                  return clipboardData; // 如果转换失败，使用原始内容
                });
                console.log(mdStr, 'mdStr');

                // 处理公式
                const formulaStr = !isHtml ? mdStr : handleFormula(mdStr);

                // 转换为HTML
                const htmlStr = parse(formulaStr) as string;
                console.log(htmlStr, 'htmlStr');

                // 获取当前选区
                const range = ueditor.selection.getRange();

                if (range.collapsed) {
                  // 如果没有选中内容，在光标位置插入
                  ueditor.execCommand('inserthtml', htmlStr);
                } else {
                  // 如果有选中内容，替换选中内容
                  range.deleteContents();
                  ueditor.execCommand('inserthtml', htmlStr);
                }

                // 更新选区
                range.collapse(false); // 将光标移到插入内容的末尾
                ueditor.selection.getRange().select();
              } catch (error) {
                console.error('Paste operation failed:', error);
              } finally {
                setIsUploading(false);
              }
            }
          );

          function updatePlaceholderVisibility() {
            const hasContent = ueditor.getContent().trim() !== '';
            const isFocused = ueditor.isFocus();
            placeholder.style.display = !hasContent && isFocused ? 'block' : 'none';
            placeholder.style.display = hasContent ? 'none' : 'block';
          }

          ueditor.addListener('focus', updatePlaceholderVisibility);
          ueditor.addListener('blur', updatePlaceholderVisibility);
          ueditor.addListener('contentchange', updatePlaceholderVisibility);
          ueditor.addListener('input', updatePlaceholderVisibility);
          ueditor.addListener('keydown', () => {
            setTimeout(updatePlaceholderVisibility, 0);
          });
          updatePlaceholderVisibility();

          const styleElement = document.createElement('style');
          styleElement.textContent = `
            ::selection {
              background-color: #D0E9C9 !important;
              color: inherit;
            }
          `;
          // 添加光标指示器样式
          ueditor.iframe.contentDocument.head.appendChild(styleElement);
          // 更新光标指示器位置
          function updateCursorIndicator() {
            const range = ueditor.selection.getRange();
            if (range && range.startContainer) {
              const node =
                range.startContainer.nodeType === 3
                  ? range.startContainer.parentNode
                  : range.startContainer;

              if (node) {
                const editorBody = ueditor.body;
                const editorDocument =
                  ueditor.iframe.contentDocument || ueditor.iframe.contentWindow.document;

                // 获取当前行的位置信息
                const rect = node.getBoundingClientRect();
                const editorRect = editorBody.getBoundingClientRect();
                const scrollTop = editorDocument.documentElement.scrollTop;

                // 计算相对于编辑器的行位置
                const linePosition = rect.top - editorRect.top - scrollTop + 45;

                setCursorIndicator({
                  visible: true,
                  top: linePosition
                });
              }
            }
          }

          if (text) {
            const editorBody = ueditor.body;
            const placeholder = editorBody.parentNode.querySelector('div'); // Get the placeholder
            if (placeholder) {
              placeholder.style.display = 'none'; // Hide the placeholder
            }
          }

          // ueditor.addEventListener('saveEvent', () => {
          //   handleSave();
          // });
          ueditor.addListener('focus', () => {
            placeholder.style.display = 'none';
            updateCursorIndicator();
          });
          // ueditor.addListener('blur', () => {
          //   console.log('blur');
          //   // 重置选取 - 使用UEditor特有的API
          //   if (ueditor) {
          //     try {
          //       // 方法1: 直接重置选区对象
          //       ueditor.selection.clearRange();

          //       // 方法2: 尝试创建并使用空Range
          //       const range = ueditor.selection.getRange();
          //       if (range) {
          //         range.collapse(true); // 折叠到起点
          //         range.select(true); // 选择并清除其他选区
          //       }

          //       // 方法3: 直接操作DOM
          //       try {
          //         const editorWindow = ueditor.iframe.contentWindow;
          //         const editorDoc = ueditor.getDocument();
          //         if (editorWindow && editorWindow.getSelection) {
          //           const selection = editorWindow.getSelection();
          //           selection && selection.removeAllRanges();
          //         } else if (editorDoc && editorDoc.selection) {
          //           // IE兼容
          //           if (editorDoc.selection.empty) {
          //             editorDoc.selection.empty();
          //           } else if (editorDoc.selection.removeAllRanges) {
          //             editorDoc.selection.removeAllRanges();
          //           }
          //         }
          //       } catch (e) {
          //         console.warn('清除DOM选区失败', e);
          //       }

          //       // 清除所有背景高亮
          //       const editorDoc = ueditor.getDocument();
          //       if (editorDoc && editorDoc.body) {
          //         const elements = editorDoc.body.querySelectorAll('[style*="background-color"]');
          //         elements.forEach((element: Element) => {
          //           (element as HTMLElement).style.backgroundColor = '';
          //         });
          //       }

          //       // 更新状态
          //       setSelectedText('');
          //       setIsAIEnabled(false);

          //       // 隐藏增强内容面板
          //       if (!isSending) {
          //         setEnhanceContentVisible(false);
          //       }

          //       // 更新光标位置指示器
          //       setCursorIndicator({
          //         visible: false,
          //         top: 0
          //       });
          //     } catch (err) {
          //       console.error('重置选取失败:', err);
          //     }
          //   }
          // });
          ueditor.addListener('selectionchange', () => {
            updateCursorIndicator();
          });

          ueditor.addListener('contentchange', () => {
            setIsSaveToCloud(false);
            const content = ueditor.getContentTxt();

            let fullHtml = ueditor.getContent();
            let fullLatexText = convertLatexImgToFormula(fullHtml);

            const parse = new DOMParser();
            const doc = parse.parseFromString(fullLatexText, 'text/html');
            fullLatexText = doc.body.textContent || '';
            setFullLatexText(fullLatexText);

            setEditorContent(content);

            placeholder.style.display = 'none';
            if (ueditor.getContentTxt() === '') {
              placeholder.style.display = 'block';
            } else {
              placeholder.style.display = 'none';
            }
            if (ueditor.getContentTxt()) {
              const content = ueditor.getContentTxt();
              if (content.length > 200000) {
                // 截取内容到200,000个字符

                const truncatedContent = content.substring(0, 200000);

                ueditor.setContent(truncatedContent);
                // 处理截取后的内容
                Toast.error('不能插入超过20w字');
              }
            }
          });
          const editorDocument =
            ueditor.iframe.contentDocument || ueditor.iframe.contentWindow.document;

          editorDocument.addEventListener('click', () => {
            if (!isSending) {
              setEnhanceContentVisible(false);
              const iframe = document.getElementById('editor1')?.querySelector('iframe');
              if (iframe) {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
                if (iframeDocument) {
                  setTimeout(() => {
                    const elements = iframeDocument.querySelectorAll('[style*="background-color"]');
                    elements.forEach((element: Element) => {
                      (element as HTMLElement).style.backgroundColor = ''; // 清除背景颜色
                    });
                  }, 0);
                } else {
                  console.warn('Iframe document is not available');
                }
              } else {
                console.error('Editor iframe not found');
              }
            }
          });
          editorDocument.addEventListener('scroll', () => {
            setIsScrolling(true);
            requestAnimationFrame(() => {
              updateCursorIndicator(); // 使用 requestAnimationFrame 优化性能
            });
            const scrollTop = editorDocument.documentElement.scrollTop;
            const topButton = document.getElementById('top-button');
            if (scrollTop > 300) {
              if (topButton) {
                topButton.style.display = 'block';
              }
            } else {
              if (topButton) {
                topButton.style.display = 'none';
              }
            }

            const range = ueditor.selection.getRange();
            if (!range.collapsed) {
              const firstNode =
                range.startContainer.nodeType === 1
                  ? range.startContainer
                  : range.startContainer.parentNode;
              const lastNode =
                range.endContainer.nodeType === 1
                  ? range.endContainer
                  : range.endContainer.parentNode;
              const firstNodeRect = firstNode.getBoundingClientRect();
              const lastNodeRect = lastNode.getBoundingClientRect();
              const viewportHeight = window.innerHeight;
              const availableSpaceBelow = viewportHeight - lastNodeRect.bottom;

              const offset = 165;

              let position: { top: number; left: number; type?: 'bottom' | 'top' };
              if (availableSpaceBelow < viewportHeight / 2) {
                if (
                  availableSpaceBelow <= 220 ||
                  (firstNodeRect.top < 100 && lastNodeRect.top > 600 && firstNodeRect.top > 0)
                ) {
                  position = {
                    top: firstNodeRect.top + window.scrollY + offset - 250, // 上方展示
                    left: firstNodeRect.left + window.scrollX,
                    type: 'top'
                  };
                } else if (firstNodeRect.top < 0) {
                  position = {
                    top: 160, // 上方展示
                    left: firstNodeRect.left + window.scrollX,
                    type: 'top'
                  };
                } else {
                  console.log('test333');
                  position = {
                    top: firstNodeRect.top + window.scrollY + offset - 20, // 上方展示
                    left: firstNodeRect.left + window.scrollX,
                    type: 'bottom'
                  };
                }
              } else {
                position = {
                  top: lastNodeRect.bottom + window.scrollY + offset, // 下方展示
                  left: lastNodeRect.left + window.scrollX,
                  type: 'top'
                };
              }

              // 直接更新 DOM
              if (enhanceContentRef.current) {
                if (position.type === 'top') {
                  enhanceContentRef.current.style.top = `${position.top}px`;
                  enhanceContentRef.current.style.bottom = 'auto'; // 确保 bottom 不影响
                } else if (position.type === 'bottom') {
                  enhanceContentRef.current.style.bottom = `calc(100vh - ${position.top}px)`;
                  enhanceContentRef.current.style.top = 'auto'; // 确保 top 不影响
                }
                enhanceContentRef.current.style.display = 'block'; // 根据需要显示或隐藏
                setEnhanceContentType(position.type || '');
              }
            }
          });

          // togglePlaceholder();
        });

        setEditor(ueditor);
      });
    };

    if (typeof window !== 'undefined') {
      initializeEditor();
    }

    return () => {
      if (ueditor) {
        ueditor.destroy();
      }
    };
  }, [modalOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (enhanceContentRef.current && !enhanceContentRef.current.contains(event.target as Node)) {
        if (!isSending) {
          setEnhanceContentVisible(false);
          if (editor1) {
            const spans = editor1.iframe.contentDocument.querySelectorAll('span');
            spans.forEach((span: HTMLSpanElement) => {
              span.style.backgroundColor = '';
            });
          }
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [enhanceContentRef, editorRef, isSending, editor1, selectedText]);
  // 复制全文
  const copyToClipboard = () => {
    const content = editor1?.getContentTxt();
    if (content) {
      navigator.clipboard
        .writeText(content)
        .then(() => {
          Toast.success({ title: '内容已复制到剪贴板' });
        })
        .catch((err) => {
          Toast.error({ title: '复制失败', description: err.message });
        });
    }
  };

  const exportWordDocument = () => {
    const domParser = new DOMParser();
    const htmlDocument = domParser.parseFromString(editor1?.getContent(), 'text/html');
    let content = editor1?.getContent();

    // 设置表格样式
    htmlDocument.body.querySelectorAll('table').forEach((table) => {
      table.style.borderCollapse = 'collapse';
      table.style.border = '1px solid #DDD';

      table.querySelectorAll('th, td').forEach((cell) => {
        const htmlCell = cell as HTMLTableCellElement;
        if (!htmlCell.hasAttribute('style')) {
          // htmlCell.style.padding = '3px 8px';
        }
        htmlCell.style.border = '1px solid #DDD';
      });
    });

    // 将所有 ol 和 ul 标签内的元素抽出来;移除 ol 和 ul 标签;将 li 标签改为 div 标签;
    htmlDocument.body.querySelectorAll('ul, ol').forEach((list) => {
      const parent = list.parentNode;
      const isOrderedList = list.tagName === 'OL';
      let counter = 1;

      while (list.firstChild) {
        const li = list.firstChild as HTMLElement;
        if (li.tagName === 'LI') {
          const div = document.createElement('div');
          // 模拟ul和ol的符号序号;
          const bullet = isOrderedList ? `${counter}. ` : '• ';
          const span = document.createElement('span');
          span.textContent = bullet;
          span.style.marginRight = '8px';

          div.appendChild(span);
          while (li.firstChild) {
            div.appendChild(li.firstChild);
          }

          parent?.insertBefore(div, list);
          counter++;
        } else {
          parent?.insertBefore(li, list);
        }
        if (list.contains(li)) {
          list.removeChild(li);
        }
      }
      if (parent?.contains(list)) {
        parent.removeChild(list);
      }
    });

    // 将每个span元素(ol和ul符号的模拟元素)的同级p元素替换为包含相同内容的span元素
    htmlDocument.body.querySelectorAll('span').forEach((span) => {
      const parentNode = span.parentNode;
      if (parentNode) {
        const siblingNodes = Array.from(parentNode.childNodes);
        siblingNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.nodeName === 'P') {
            const pElement = node as HTMLParagraphElement;
            const spanElement = document.createElement('span');
            spanElement.innerHTML = pElement.innerHTML;
            parentNode.replaceChild(spanElement, pElement);
          }
        });
      }
    });

    // 给所有的div和p标签设置CSS样式
    htmlDocument.body.querySelectorAll('div, p').forEach((element) => {
      const htmlElement = element as HTMLElement;
      // 检查是否没有style属性
      if (!htmlElement.hasAttribute('style')) {
        // 所有父级不是td的p标签,和div标签
        if (
          (htmlElement.tagName === 'P' && htmlElement.parentElement?.tagName !== 'TD') ||
          htmlElement.tagName === 'DIV'
        ) {
          htmlElement.style.marginTop = '5px';
          htmlElement.style.marginBottom = '1em';
        }
      }
    });

    // 样式设置完毕,重新清理和规范化HTML内容
    content = `<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"></head><body>${htmlDocument.body.innerHTML}</body></html>`;
    return content;
  };

  const saveAsWord = async (type: 'word' | 'pdf') => {
    if (editor1) {
      reportAppsProduceDocument({ tenantAppId: appId as string });
      let content = exportWordDocument();
      const timestampInSeconds = Math.floor(Date.now() / 1000);
      let htmlWithLatexMd = convertLatexImgToFormula(content);
      htmlWithLatexMd = htmlWithLatexMd.replace(/&amp;/g, '&');
      let htmlStr = await convertMdToHtml({
        htmlStr: htmlWithLatexMd
      });

      htmlStr = convertHtmlToXhtml(htmlStr);

      if (content) {
        if (type === 'word') {
          await convertHtmlToWord({
            filename: `${title}_${timestampInSeconds}`,
            htmlStr: htmlStr
          });
        } else if (type === 'pdf') {
          await convertHtmlToPdf({
            filename: `${title}_${timestampInSeconds}`,
            htmlStr: htmlStr
          });
        }
      } else {
        Toast.warning({ title: '内容为空，无法导出！' });
      }
    }
  };

  const debounce = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: any[]) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };
  const onSave = () => {
    // 打开数据空间
    setIsSaveflag(false);
    const saveHtml = convertHtmlToXhtml(exportWordDocument());
    openOverlay({
      Overlay: Chooser,
      props: {
        title: '保存至数据空间',
        modalMode: ModalModeEnum.Save,
        showCreateFolderBtn: true,
        filename: title,
        htmlStr: saveHtml,
        setTitle,
        onSuccess(files, inputFileName) {
          setIsSaveToCloud(true);
          handleSave(saveHtml, title);
        }
      }
    });
  };

  const onOk = () => {
    // 如果是编辑模式
    if (editType === 'edit') {
      window.close();
      handleSave(editor1.getContent(), title);
    } else {
      window.close();
      handleSave(editor1.getContent(), title);
    }
  };

  const onCenter = () => {
    // 关闭弹窗
    setIsSaveflag(false);
  };
  const SaveBtn = () => {
    return (
      <Box>
        <Button type="primary" onClick={onSave}>
          保存
        </Button>
        <Button onClick={onOk} style={{ margin: '0 10px' }}>
          不保存
        </Button>
        <Button onClick={onCenter}>取消</Button>
      </Box>
    );
  };

  const onCloseLayoutOver = debounce(() => {
    if (editor1 && editor1.getContentTxt()) {
      if (!isSaveToCloud) {
        setIsSaveflag(true);
      } else {
        window.close();
      }
    } else {
      window.close();
    }
  }, 300);

  const handleSummaryClick = (event: React.MouseEvent, operationType: string) => {
    event.preventDefault();
    event.stopPropagation();
    if (isAIEnabled) {
      console.log('operationType', operationType);
      setSelectedOperationType(operationType);
      window.isSummaryClick = true;
      if (editor1) {
        editor1.execCommand('myblockquote');
      }
    }
  };
  const handleContentGenerated = async (
    generatedContent: string | Promise<string>,
    type: string
  ) => {
    if (editor1) {
      console.log('handleContentGenerated', 32323232);
      const editorDoc = editor1.iframe.contentDocument || editor1.iframe.contentWindow?.document;
      if (editorDoc) {
        const placeholder = editorDoc.querySelector('.editor-placeholder');
        if (placeholder) {
          placeholder.style.display = 'none';
        }
      }
      const selection = editor1.selection.getRange();
      const content = await (typeof generatedContent === 'string'
        ? Promise.resolve(generatedContent)
        : generatedContent);

      const formulaStr = handleFormula(content);
      const htmlStr = await convertMdToHtml({
        htmlStr: formulaStr
      });

      const parsedContent = parse(htmlStr) as any;

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = parsedContent;

      const fragment = document.createDocumentFragment();

      if (type === '插入') {
        selection.collapse(false);
        const lineBreak = document.createElement('br');
        fragment.appendChild(lineBreak);

        while (tempDiv.firstChild) {
          fragment.appendChild(tempDiv.firstChild);
        }
        selection.insertNode(fragment);
      } else if (type === '替换') {
        selection.deleteContents();
        while (tempDiv.firstChild) {
          fragment.appendChild(tempDiv.firstChild);
        }
        selection.insertNode(fragment);
      }
      setEnhanceContentVisible(false);
    }
  };

  const onHeaderTabChange = (tab: TabType) => {
    setActiveTab(tab);
  };

  // useEffect(() => {
  //   const url = new URL(window.location.href);
  //   if (url.searchParams.has('id')) {
  //     url.searchParams.delete('id');
  //     window.history.replaceState({}, '', url.toString());
  //   }
  // }, []);
  const onRecentSaveChange = async (item: TemporarilyListResponse) => {
    try {
      setIsRecentSave(true); // 显示加载状态

      // 1. 先保存当前内容，并等待保存完成
      if (editor1?.getContent()) {
        const saveResult = await saveEditorToCloudTemporarily({
          id: searchParamId || '',
          title: title || '',
          fileJson: editor1.getContent()
        });

        // 更新保存状态
        setSaveObj({
          saveStatus: 'saved',
          updateTime: saveResult.updateTime
        });

        // 刷新暂存列表
        await refreshListRef.current?.();
      }

      // 2. 保存成功后，获取并切换到新的暂存内容
      const res = await getTemporarilyDetail({ id: item.id });
      if (res.fileJson) {
        editor1.setContent(res.fileJson as string);
        setTitle(res.title);

        // 更新 URL 参数
        const url = new URL(window.location.href);
        url.searchParams.set('id', item.id);
        window.history.replaceState({}, '', url.toString());

        // 刷新暂存列表
        refreshListRef.current?.();
      }
    } catch (err) {
      Toast.error({
        title: getErrText(err, '切换文件失败')
      });
    } finally {
      setIsRecentSave(false); // 隐藏加载状态
    }
  };

  // const openAPPTaskStep = useCallback(
  //   (appId: string) => {
  //     openOverlay({
  //       Overlay: GuideStepModal,
  //       props: {
  //         appId
  //       }
  //     });
  //   },
  //   [openOverlay]
  // );

  // useEffect(() => {
  //   appId && openAPPTaskStep(appId);
  // }, [appId, openAPPTaskStep]);

  return (
    <>
      {isRecentSave && <Loading zIndex={1000000} text="加载中..." />}
      <Box
        ref={overlayRef}
        backdropFilter="blur(10px) hue-rotate(90deg)"
        display="flex"
        flexDir="column"
        h="100%"
        overflow="hidden"
      >
        {/* 弹窗-选择任务类型 */}
        <TaskGrid
          onSuccess={(appId) => {
            router.push({
              query: {
                ...router.query,
                appId: appId,
                init: '1'
              },
              pathname: router.pathname
            });
          }}
        />
        {editor1 && (
          <TopTitleBar
            title={title}
            isEditingTitle={isEditingTitle}
            handleTitleClick={handleTitleClick}
            handleTitleBlur={handleTitleBlur}
            handleEditorCommand={handleEditorCommand}
            saveAsWord={saveAsWord}
            onCloseLayoutOver={onCloseLayoutOver}
            onHeaderTabChange={onHeaderTabChange}
            onRecentSaveChange={(item: TemporarilyListResponse) => onRecentSaveChange(item)}
            editor={editor1}
            onRefreshTemporarilyList={handleRefreshCallback}
          />
        )}
        {editor1 && (
          <Header
            saveEditorToTemporarily={saveEditorToTemporarily}
            activeTab={activeTab}
            title={title}
            handleEditorCommand={handleEditorCommand}
            selectionState={selectionState}
            selectedParagraphFormat={selectedParagraphFormat}
            selectedFont={selectedFont}
            selectedFontSize={selectedFontSize}
            selectedJustify={selectedJustify}
            isAIEnabled={isAIEnabled}
            handleSummaryClick={handleSummaryClick}
            onOpenSelectFile={onOpenSelectFile}
            ListStyleMenu={ListStyleMenu}
            BulletStyleMenu={BulletStyleMenu}
            LineSpacingMenu={LineSpacingMenu}
            AlignMenu={AlignMenu}
            ParagraphMenu={ParagraphMenu}
            fontMenu={fontMenu}
            fontSizeMenu={fontSizeMenu}
            TableOperationsMenu={TableOperationsMenu}
            getCurrentStep={getCurrentStep}
            handleOpenFile={handleOpenFile}
            chatId={chatId || ''}
            editor={editor1}
          />
        )}
        <Flex
          h="100%"
          style={{ backgroundColor: '#F3F4F6' }}
          // overflowX="auto"
          overflow="hidden"
          justifyContent="space-between"
        >
          <Box h="100%">
            <LeftPlatform appId={appId as string} editor={editor1} />
          </Box>
          {/* 占满整个屏幕 */}
          <Box pb={rpxDim(40)} height="100%" ref={editorWrapRef}>
            <Box position="relative">
              <Box
                id="editorContainer"
                width={respDims(847)}
                mt={respDims(24, 10)}
                mx={respDims(44, 10)}
                p={respDims(38, 10)}
                height="100vh"
                // height={`calc(100vh - ${rpxDim(40)})`}
                position="relative"
                bg="#fff"
                style={{ boxShadow: '0px 0px 16.3px 0px rgba(0, 0, 0, 0.07)', zIndex: 1 }}
              >
                {generateContentLoading && (
                  <Flex
                    flexDirection="column"
                    minH="500px"
                    justifyContent="center"
                    position="absolute"
                    alignItems="center"
                    zIndex={1000}
                    left={0}
                    top={0}
                    w="calc(100%)"
                    bg="rgba(255, 255, 255, 1)"
                    style={{ backdropFilter: 'blur(10px) hue-rotate(90deg)' }}
                  >
                    <Lottie name="generateLoading" w="100px" h="100px" />
                    <Box color="#4E5969" fontSize={respDims(14, 13)}>
                      AI正在撰写，请稍等片刻~
                    </Box>
                  </Flex>
                )}
                {/* 光标指示器 */}
                {cursorIndicator.visible && (
                  <Tooltip title="AI编辑" placement="bottom" trigger="hover">
                    <Box
                      position="absolute"
                      left={respDims(38, 10)}
                      marginLeft="-20px"
                      display="flex"
                      w="20px"
                      h="20px"
                      padding={rpxDim(4)}
                      justifyContent="center"
                      alignItems="center"
                      gap={rpxDim(10)}
                      flexShrink="0"
                      backgroundColor="#F3F4F6"
                      onClick={() => {
                        if (editor1) {
                          // 获取编辑器 iframe 文档
                          const editorDoc =
                            editor1.iframe.contentDocument ||
                            editor1.iframe.contentWindow?.document;
                          if (!editorDoc) return;

                          // 计算点击位置对应的垂直偏移
                          const clickOffset = cursorIndicator.top - 40;

                          // 获取所有文本节点
                          const textNodes = [];
                          const walker = editorDoc.createTreeWalker(
                            editorDoc.body,
                            NodeFilter.SHOW_TEXT,
                            null
                          );

                          let node;
                          while ((node = walker.nextNode())) {
                            textNodes.push(node);
                          }

                          // 找到最接近指示器位置的文本节点
                          let targetNode = null;
                          let minDistance = Infinity;

                          textNodes.forEach((node) => {
                            const range = editorDoc.createRange();
                            range.selectNode(node);
                            const rect = range.getBoundingClientRect();
                            const distance = Math.abs(rect.top - clickOffset);

                            if (distance < minDistance) {
                              minDistance = distance;
                              targetNode = node;
                            }
                          });

                          // 选中目标行
                          if (targetNode) {
                            const range = editorDoc.createRange();
                            range.selectNode(targetNode);

                            const selection = editorDoc.getSelection();
                            selection?.removeAllRanges();
                            selection?.addRange(range);

                            // 触发编辑器选区更新
                            editor1.fireEvent('selectionchange');
                            editor1.execCommand('openshortmenu');
                          }
                        }
                      }}
                      zIndex="9999"
                      style={{
                        // top: `${rpxDim(cursorIndicator.top)}`,
                        transform: `translateY(${rpxDim(cursorIndicator.top - 38)})`,
                        borderRadius: rpxDim(5),
                        cursor: 'pointer',
                        transition: 'transform 0.05s linear',
                        willChange: 'transform'
                      }}
                    >
                      <SvgIcon name="aiRight" w={rpxDim(14)} h={rpxDim(14)} />
                    </Box>
                  </Tooltip>
                )}
                <Box
                  ref={editorRef}
                  id={id}
                  style={{
                    minHeight: rpxDim(300),
                    // maxHeight: `calc(100vh - ${rpxDim(40)})`,
                    margin: '0 auto',
                    paddingBottom: rpxDim(30),
                    // 高度自适应
                    height: 'auto',
                    overflowY: 'auto',
                    pointerEvents: isSending ? 'none' : 'auto',
                    overflowX: 'hidden'
                  }}
                />
              </Box>
              <Modal
                width={rpxDim(964)}
                visible={openFile}
                footer={null}
                onCancel={() => {
                  setOpenFile(false);
                  setFileList([]);
                }}
                // onOk={() => handleConfirm()}
                title="导入内容"
              >
                <Box
                  style={{
                    padding: '-2px',
                    height: rpxDim(534),
                    textAlign: 'center'
                  }}
                >
                  {fileList.length === 0 && (
                    <Dragger
                      //只支持上传一个文件和docx文件
                      accept={'.docx'}
                      maxCount={1}
                      name="file"
                      onChange={(info) => handleUpload(info)}
                      beforeUpload={(file) => beforeUpload(file)}
                      onRemove={(file) => handleRemoveFile(file)}
                      showUploadList={false}
                      style={{
                        border: '1px dashed #E5E6EB',
                        borderRadius: '8px',
                        backgroundColor: '#F8FAFC',
                        padding: '16px',
                        textAlign: 'center'
                      }}
                    >
                      <SvgIcon name="uploadFile" w="80px" h="80px" />
                      <p style={{ fontSize: '13px', color: '#1D2129' }}>
                        点击或拖拽文件到此处上传，或
                        {/* <Text
                        display="inline"
                        color="primary.500"
                        cursor="pointer"
                        onClick={(event) => handleSelectFromCloud(event)}
                      >
                        数据空间选择
                      </Text> */}
                      </p>
                      <p style={{ fontSize: '12px', color: '#86909c', paddingTop: '8px ' }}>
                        仅支持上传 docx 格式，文件大小不超过10.00M
                      </p>
                      <Flex
                        alignItems="center"
                        mt="6px"
                        p="8px 13px"
                        borderRadius="8px"
                        border="1px solid #7d4dff"
                        bg="white"
                        cursor="pointer"
                        width="auto"
                        display="inline-flex"
                      >
                        <SvgIcon name="taskDownload" w={rpxDim(14)} h={rpxDim(14)} />
                        <Box
                          color="#7d4dff"
                          fontSize="14px"
                          fontWeight="500"
                          ml="8px"
                          onClick={(event) => handleSelectFromCloud(event)}
                        >
                          从数据空间导入
                        </Box>
                      </Flex>
                    </Dragger>
                  )}
                  {fileList?.map((file: any) => (
                    <Flex
                      onMouseEnter={() => (file.showDelete = true)}
                      onMouseLeave={() => (file.showDelete = false)}
                      key={file.uid}
                      border="1px dashed #E5E6EB"
                      borderRadius="8px"
                      p="16px"
                      alignItems="center"
                      justifyContent="center"
                      height={rpxDim(500)}
                      backgroundColor="#F9F8FC"
                      flexDirection="column"
                      textAlign="center"
                    >
                      <Box display="flex" flexDirection="column" alignItems="center">
                        <Box
                          // p={rpxDim(3)}
                          position="relative"
                          background="#fff"
                          style={{ borderRadius: '16px' }}
                        >
                          <FileIcon
                            fileType={file.fileType}
                            fileName={file.name}
                            w="80px"
                            h="80px"
                          />
                          <SvgIcon
                            name="circleClose"
                            position="absolute"
                            right="-12px"
                            top="-10px"
                            w="30px"
                            h="30px"
                            cursor="pointer"
                            onClick={() => handleRemoveFile(file)}
                          />
                        </Box>
                        <Box
                          color="#1D2129"
                          fontSize="15px"
                          mt="10px"
                          className={'textEllipsis'}
                          w="100%"
                          flex={'1 0 auto'}
                          textAlign="center"
                        >
                          {file.name}
                        </Box>
                        <Box color="#86909c" fontSize="12px" mt="5px" textAlign="center">
                          {`${(file.fileSize / (1024 * 1024)).toFixed(2)}MB`}
                        </Box>
                      </Box>
                    </Flex>
                  ))}
                </Box>
                <Box display="flex" justifyContent="right" mt={4} mr={18}>
                  <Box mr={4}>
                    <Button
                      onClick={() => {
                        setOpenFile(false);
                        setFileList([]);
                      }}
                    >
                      取消
                    </Button>
                  </Box>
                  <Button type="primary" onClick={() => handleConfirm()}>
                    确定
                  </Button>
                </Box>
              </Modal>
              {isChooserOpen && (
                <Chooser
                  mode={ChooserModeEnum.Self}
                  title="选择文件"
                  maxCount={1}
                  files={selectedFiles}
                  onSuccess={handleChooserSuccess}
                  onClose={handleChooserClose}
                />
              )}
            </Box>
          </Box>

          {/* 右侧AI栏 */}
          <RightPlatform appId={appId as string} />
        </Flex>
      </Box>
      <Files onSelect={onSelectFile} />
      {isUploading && <Loading zIndex={1000000} />}
      {enhanceContentVisible && (
        <Box
          ref={enhanceContentRef}
          display="flex"
          justifyContent="center"
          alignItems="center"
          style={{
            // zIndex: 1000,
            position: 'absolute',
            display: isScrolling ? 'none' : 'block',
            top:
              enhanceContentPosition?.type === 'top' ? enhanceContentPosition?.top + 10 : undefined,
            bottom:
              enhanceContentPosition?.type === 'bottom'
                ? `calc(120vh - ${enhanceContentPosition?.top}px)`
                : undefined,
            width: '100%',
            maxWidth: '661px',
            left: '49.8%',
            height: '10%',
            transform: 'translateX(-50%)'
          }}
        >
          <EnhanceContent
            labels={labels || ''}
            selectedText={selectedText}
            text={selectedText?.replace(/\s+/g, '')}
            operationType={selectedOperationType}
            onContentGenerated={handleContentGenerated}
            onSendingStatusChange={handleSendingStatusChange}
            editor1={editor1}
            enhanceContentType={enhanceContentType!}
            position={enhanceContentPosition}
            editorContent={editorContent}
            fullLatexText={fullLatexText}
            selectedLatexText={selectedLatexText}
          />
        </Box>
      )}

      <Modal
        width={rpxDim(964)}
        visible={isFormulaVisible}
        onCancel={() => onFormulaCancel()}
        onOk={() => getFormulas()}
        title="导入公式"
      >
        <LatexEasyEditor ref={LatexEasyRef} onFormulaChange={handleFormulaChange} />
      </Modal>
      <Modal
        visible={isSaveflag}
        onCancel={() => setIsSaveflag(false)}
        title="未保存数据"
        footer={null}
        centered
      >
        <Box textAlign="left" h={100} display="flex" alignItems="center" justifyContent="left">
          您编辑的内容已被暂存，但尚未保存至数据空间中，确认退出编辑？
        </Box>
        <Box display="flex" justifyContent="right" mt={4}>
          {SaveBtn()}
        </Box>
      </Modal>
      <Button
        id="top-button"
        ref={scrollTopButtonRef}
        onClick={scrollToTop}
        style={{
          position: 'fixed',
          bottom: '50px',
          left: '77%',
          zIndex: 1000,
          backgroundColor: '#fff',
          color: '#606266',
          borderRadius: '50%',
          width: '50px',
          height: '50px',
          display: 'none',
          justifyContent: 'center',
          alignItems: 'center',
          border: '1px solid #EDEDED',
          boxShadow: '0px 4px 4px 0px rgba(205,205,205,0.25)',
          lineHeight: '60px'
        }}
      >
        <SvgIcon name="chatUp" w="24px" h="24px" />
      </Button>
    </>
  );
};

export async function getServerSideProps(context: any) {
  const appId = context?.query?.appId;
  const appTaskTypeId = context?.query?.appTaskTypeId;
  return {
    props: {
      appId: appId || '',
      appTaskTypeId: appTaskTypeId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default EditorBox;
