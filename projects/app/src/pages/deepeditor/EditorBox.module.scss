.toolbar {
  display: flex;
  flex-wrap: nowrap;
  background-color: #fff;
  border-bottom: 1px solid #e5e7eb;
  justify-content: center;
}

.dropdown_del_border {
  border: none;
}
.dropdown_a {
  color: #52525b;
}
.dropdown_del_border:hover {
  display: flex;
}
.toolbar-select {
  display: flex;
  // margin: 0 10px;
  flex: 1;
}
.toolbar-icon {
  display: flex;
  flex: 1;
  margin-top: 6px;
  justify-content: flex-start;
  // 子元素居中
  svg {
    margin: 0 auto;
  }
}
.toolbar-icon-item {
  margin-right: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 3px;
  text-align: center;
  // 内容剧中显示
  svg {
    margin: 0 auto;
  }
  &:hover {
    background-color: #f3f4f6;
  }
}
.toolbarBox {
  display: flex;
  align-items: center;
}

.toolbar-box {
  padding: 0 19px;
  border-right: 1px solid #e5e5e5;
  margin: 14px 0;
}

.icon_btn {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  gap: 4px;
  font-size: 14px;
}

.toolbar-Undo-undo {
  padding: 8px;
  border-radius: 8px;

  flex: 1;
  &:hover {
    background-color: #f3f4f6;
  }
}

.toolbar-Undo {
  cursor: pointer;
  padding: 16px;
  display: flex;
  // gap: 16px;
  //垂直居中
  align-items: center;
  // &:hover {
  //   background-color: #f3f4f6;
  // }
}
.icon_btn_undo {
  padding: 16px;
}
// 选中样式
.selection {
  background-color: #f3f4f6;
}

.topTitleBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #fff;
  max-height: 68px;
  border-bottom: 1px solid var(--Colors-Gray-200, #e5e7eb);
}

.buttonBox {
  margin-right: 16px;
}
#editorContainer {
  width: 100%;
  /* or a specific height like 500px */
  // overflow-y: auto; /* allows vertical scrolling */
  background: #fff;
}

.content-container {
  display: flex;
  animation: contentEnter 0.2s ease;
}

.content-container.exit {
  animation: contentExit 0.2s ease;
}

@keyframes contentEnter {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes contentExit {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-10px);
  }
}
