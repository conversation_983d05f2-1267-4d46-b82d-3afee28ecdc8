import { Mo<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'antd';
import { useState } from 'react';
import { Image, Flex, Box } from '@chakra-ui/react';
import { useRouter } from 'next/router';
export default function HumanSettingModal({
  isOpen = false,
  onClose = () => {},
  refetchInstance = () => {},
  destroyInstance = () => {},
  updateHumanImg = () => {},
  humanSettings2d = null
}: {
  isOpen?: boolean;
  onClose?: () => void;
  refetchInstance?: () => void;
  destroyInstance?: () => void;
  updateHumanImg?: (humanSettings: any, humanSettings2d: any) => void;
  humanSettings2d?: any;
}) {
  const router = useRouter();
  // 智教46589,小安49438,小宝49439
  const avatars = ['46589', '49438', '49439'].includes(router.query.appId as string)
    ? [
        {
          url: '/imgs/humans/xb-mini.png',
          label: '小宝',
          value: '9be278fdc702418db9eb6b301f3ae25c'
        },
        {
          url: '/imgs/humans/bazj-mini.png',
          label: '宝安智教数字人',
          value: 'f6b0be5345e8414895f2f91c47bec5c1'
        },
        {
          url: '/imgs/humans/xa-mini.png',
          label: '小安',
          value: 'fae07be4e85f40b5991490519699ea3e'
        }
      ]
    : [
        {
          url: 'https://meta-studio-prd.cdn.bcebos.com/meta-studio-operation/scene/c2b58f9c-d829-4da1-b3af-513f5874dc81.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250314T024044Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604800&X-Amz-Credential=ALTAKT5AIuJnbbiGtJyROeVCSI%2F20250314%2Fgz%2Fs3%2Faws4_request&X-Amz-Signature=b5894a486103e5056dbfc5ebab6bd5f5fd097bfc66a6adece2b90752d37af685',
          label: '小宝',
          value: 'f6b0be5345e8414895f2f91c47bec5c1'
        },
        {
          url: 'https://meta-studio-prd.cdn.bcebos.com/meta-studio-operation/human/af2429da-7dc6-4848-a400-f0572e8a6615.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250314T012125Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604799&X-Amz-Credential=ALTAKT5AIuJnbbiGtJyROeVCSI%2F20250314%2Fgz%2Fs3%2Faws4_request&X-Amz-Signature=3144901f0b13576c240f180ab8dd76d9b4bc95870205d9d31a9f0c10c67f665a',
          label: '宝安智教数字人',
          value: '9be278fdc702418db9eb6b301f3ae25c'
        },
        {
          url: 'https://meta-studio-prd.cdn.bcebos.com/meta-studio-operation/scene/df5046f5-6b4c-44aa-bb99-05249ca05903.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250413T015142Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604800&X-Amz-Credential=ALTAKT5AIuJnbbiGtJyROeVCSI%2F20250413%2Fgz%2Fs3%2Faws4_request&X-Amz-Signature=fcff6511af02d1438dfe231b8b7977290c757d90c04f6bf97aed74a9ea20967e',
          label: '小安',
          value: 'fae07be4e85f40b5991490519699ea3e'
        }
      ];

  // 宝教通2d版本把人物顺序都搞乱了,导致我需要写这个条件处理调换顺序
  const voices = ['46589', '49438', '49439'].includes(router.query.appId as string)
    ? // 宝教通版本
      [
        {
          label: '小宝',
          value: '18dd4c1c085e427484a76e69971e4970'
        },
        {
          label: '宝安智教数字人',
          value: 'cd9440f56fcc432bbb8ab2d7fa338323'
        },
        {
          label: '小安',
          value: '7522ca7273ea43d7b8d92c541dd11be2'
        }
        // 正常版本
      ]
    : [
        {
          label: '小宝',
          // 正常版本下的好听女生
          value: 'cd9440f56fcc432bbb8ab2d7fa338323'
        },
        {
          label: '宝安智教数字人',
          // 正常版本下的男生
          value: '18dd4c1c085e427484a76e69971e4970'
        },
        {
          label: '小安',
          // 正常版本下的普通女生
          value: '7522ca7273ea43d7b8d92c541dd11be2'
        }
      ];

  const [selectedAvatar, setSelectedAvatar] = useState(() => {
    // 智教46589,小安49438,小宝49439
    if (['46589', '49438', '49439'].includes(router.query.appId as string)) {
      // modal弹窗人物的顺序是小宝,智教,小安
      // 获取当前appId所在的index
      const index = ['49439', '46589', '49438'].findIndex((appId) => appId === router.query.appId);
      // 优先使用父组件传入的设置
      if (humanSettings2d?.sceneId) {
        return humanSettings2d.sceneId;
      }
      return avatars[index].value;
    } else {
      const saved = localStorage.getItem('humanSettings');
      if (saved) {
        const settings = JSON.parse(saved);
        return settings.sceneId || avatars[0].value;
      }
      return avatars[0].value;
    }
  });

  const [selectedVoice, setSelectedVoice] = useState(() => {
    // 智教46589,小安49438,小宝49439
    if (['46589', '49438', '49439'].includes(router.query.appId as string)) {
      // 优先使用父组件传入的设置
      if (humanSettings2d?.timbreId) {
        return humanSettings2d.timbreId;
      }

      // 获取当前appId所在的index
      const index = ['49439', '46589', '49438'].findIndex((appId) => appId === router.query.appId);
      return voices[index].value;
    } else {
      const saved = localStorage.getItem('humanSettings');
      if (saved) {
        const settings = JSON.parse(saved);
        return settings.timbreId || voices[0].value;
      }
      return voices[0].value;
    }
  });

  const handleSave = () => {
    if (['46589', '49438', '49439'].includes(router.query.appId as string)) {
      const avatarUrl = avatars.find((avatar) => avatar.value === selectedAvatar)?.url;
      const humanSettings2d = {
        sceneId: selectedAvatar,
        timbreId: selectedVoice
      };
      updateHumanImg(avatarUrl, humanSettings2d);
      onClose();
    } else {
      const settings = {
        sceneId: selectedAvatar,
        timbreId: selectedVoice
      };
      localStorage.setItem('humanSettings', JSON.stringify(settings));
      onClose();

      // 调用父组件的销毁数字人视频流方法
      destroyInstance && destroyInstance();
      // 调用父组件的创建数字人视频流方法
      refetchInstance && refetchInstance();
      // 销毁播放器
      window.VePlayer?.destroy();
    }
  };

  return (
    <Modal
      title="数字人设置"
      open={isOpen}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存
        </Button>
      ]}
    >
      <Box>
        <Box>
          <Box mt="20px" fontSize="14px" fontWeight="500" color="#1d2129">
            音色选择
          </Box>
          <Box mt="8px">
            <Select
              style={{ width: '100%' }}
              placeholder="请选择音色"
              value={selectedVoice}
              onChange={(value) => setSelectedVoice(value)}
              options={voices}
            />
          </Box>
        </Box>

        <Box>
          <Box mt="20px" fontSize="14px" fontWeight="500" color="#1d2129">
            形象选择
          </Box>
          <Flex flexWrap="wrap" gap="16px" mt="9px">
            {avatars.map((avatar, index) => (
              <Col key={index} xs={12} sm={8} md={6} lg={4}>
                <Box
                  border={
                    selectedAvatar === avatar.value ? '1px solid #4d36f5' : '1px solid transparent'
                  }
                  borderRadius="10px"
                  onClick={() => setSelectedAvatar(avatar.value)}
                  p="4px"
                  userSelect="none"
                  cursor="pointer"
                >
                  <Image src={avatar.url} alt={avatar.label} draggable={false} />
                </Box>
                <Flex justifyContent="center" mt="15px" fontSize="14px" color="#000">
                  {avatar.label}
                </Flex>
              </Col>
            ))}
          </Flex>
        </Box>
      </Box>
    </Modal>
  );
}
