import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Flex, Input, Image } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { digitalhumanV1Instance } from '@/api/human';
import { useQuery } from '@tanstack/react-query';
import Head from 'next/head';
import ChatPanelHuman from '@/components/ChatPanelHuman';
import { useRouter } from 'next/router';
import { getToken } from '@/utils/humanAuth';
import HumanSettingModal from './components/HumanSettingModal';
import {
  timbresList,
  imageList,
  isDestroyInstance,
  eraHumansStream,
  eraTimbresStream,
  nlsToken
} from '@/api/human';
import Loading from '@/components/Loading';
import { useSystemStore } from '@/store/useSystemStore';
import dayjs from 'dayjs';
import { useSpeech } from '@/hooks/useSpeech';
import { EventNameEnum, eventBus, multiEventBus } from '@/utils/eventbus';
import { getPromptCenterList } from '@/api/prompt';
import { setHumanInfo } from '@/utils/humanAuth';
import { eraLogin } from '@/api/human';
import { useToast } from '@/hooks/useToast';
import useVoiceRecognition from '@/hooks/useVoiceRecognition';
import { flushSync } from 'react-dom';

const Humans = ({ sceneId }: { sceneId?: string }) => {
  const [lastUuid, setLastUuid] = useState<any>();
  const [inputText, setInputText] = useState<any>();
  const [answerStr, setAnswerStr] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [recommendQuestions, setRecommendQuestions] = useState<any[]>([]);
  const webSocketObjRef = useRef<any>();
  const veplayerRef = useRef<any>();
  const router = useRouter();
  const { isPc } = useSystemStore();
  const [isHumanSettingModal, setIsHumanSettingModal] = useState(false);
  const destroyInstanceTimer = useRef<NodeJS.Timeout>();
  const [speakResult, setSpeakResult] = useState<string>('');
  // 是否处于应发送消息状态
  const [isShouldSpeak, setIsShouldSpeak] = useState(false);
  const { toast } = useToast();
  const [nlsTokenData, setNlsTokenData] = useState<any>();
  const [selectedHumanImg, setSelectedHumanImg] = useState<any>();
  const [humanSettings2d, setHumanSettings2d] = useState<any>();
  // 获取阿里云语音转文字的token
  const { refetch: refetchNlsToken } = useQuery<any>(['nlsToken'], () => nlsToken({}), {
    enabled: false,
    onSuccess: (data) => {
      setNlsTokenData(data);
    }
  });

  useEffect(() => {
    refetchNlsToken();
  }, []);

  const { isSpeaking, stopSpeak, startSpeak, isTranscription } = useVoiceRecognition({
    // appkey: 'EAd9LoLQEm6p2pBF',
    // token: '8e029a98bd9048ce85b361ede4af0270',
    appkey: nlsTokenData?.appKey,
    token: nlsTokenData?.token,
    onResult: (text: string) => {
      console.log('text', text);
      // 存起来,并且叠加
      setSpeakResult((prev: string) => (prev || '') + text);
    }
  });

  // 推荐问题(取快捷指令接口前10条)
  const { refetch: refetchPrompts } = useQuery<any>(
    ['promptCenterList', router.query.appId],
    () =>
      getPromptCenterList({
        permission: 0,
        tenantAppId: router.query.appId as string
      }),
    {
      enabled: !!router.query.appId,
      onSuccess: (data) => {
        // 测试-造数据 Start
        // const newData = [];
        // for (let i = 0; i < 11; i++) {
        //   newData.push(...data);
        // }
        // setRecommendQuestions(newData.slice(0, 10).map((item: any) => item.inputContent));
        // 测试-造数据 End
        setRecommendQuestions(data?.slice(0, 10).map((item: any) => item.inputContent));
      }
    }
  );

  useEffect(() => {
    // 数字人登录
    eraLogin({}).then((res) => {
      setHumanInfo(res);
    });

    // // 数字人形象列表
    // eraHumansStream({}).then((res) => {
    //   console.log('eraHumansStream', res);
    // });

    // // 数字人音色列表
    // eraTimbresStream({}).then((res) => {
    //   console.log('eraTimbresStream', res);
    // });
  }, []);

  // 主动销毁处理(防止并发1问题)
  useEffect(() => {
    // 如果有hack参数(hackTimestamp等于当前分钟数,如2025-03-18-16:22),不轮询
    if (
      !router.query.hackTimestamp ||
      router.query.hackTimestamp !== dayjs().format('YYYY-MM-DD-HH:mm')
    ) {
      destroyInstanceTimer.current = setInterval(() => {
        isDestroyInstance({}).then((res) => {
          if (res) {
            webSocketObjRef?.current?.send(
              JSON.stringify({
                type: 20002
              })
            );
          }
        });
      }, 10000);
    }
  }, []);

  // 如果localStorage中没有数字人音色、形象列表,则获取列表,把第一项存到localStorage
  useEffect(() => {
    if (!localStorage.getItem('humanSettings')) {
      Promise.all([timbresList(), imageList()]).then(([timbresRes, imageRes]) => {
        let humanSettings = {
          timbreId: timbresRes[0]?.voiceId,
          sceneId: imageRes[0]?.sceneId
        };
        localStorage.setItem('humanSettings', JSON.stringify(humanSettings));
        refetchInstance();
      });
    } else {
      refetchInstance();
    }
  }, []);

  // 创建数字人接口
  const { data: instanceData, refetch: refetchInstance } = useQuery<any>(
    ['instance'],
    () => {
      setIsLoading(true);
      // 根据当前的appId,选择默认的音色
      let timbreId = '';
      if (['46589'].includes(router.query.appId as string)) {
        timbreId = 'cd9440f56fcc432bbb8ab2d7fa338323';
      } else if (['49439'].includes(router.query.appId as string)) {
        timbreId = '18dd4c1c085e427484a76e69971e4970';
      } else if (['49438'].includes(router.query.appId as string)) {
        timbreId = '7522ca7273ea43d7b8d92c541dd11be2';
      } else {
        timbreId = JSON.parse(localStorage.getItem('humanSettings') || '{}')?.timbreId;
      }

      return digitalhumanV1Instance({
        video: {
          // width和height的值调换可以调转方向
          width: 1920,
          height: 1080,
          bitrate: 2000
        },
        position: {
          width: 1300,
          height: 2500,
          auto: false,
          left: 334,
          top: 0
        },

        // 读取本地存储的sceneId和timbreId
        sceneId: JSON.parse(localStorage.getItem('humanSettings') || '{}')?.sceneId,
        timbreId: selectedHumanImg ? humanSettings2d?.timbreId : timbreId,

        mode: 'interact',
        // 透明图片行不通,视频背景会变黑色
        background: {
          // "type": "color",
          // "color": "#e8f4fe"

          type: 'image',
          imageUrl:
            'https://huayun-ai-obs-public.huayuntiantu.com/d72ee4fab33f38c8bdd77c87f3edff6f.png'
        },
        stopAfter: 2 // 单位：秒。超过此时间没有收到全双工信息，将停止推流。
      });
    },
    {
      enabled: false, // 禁用自动运行
      onSuccess: (data) => {
        setLastUuid(data.uuid);
        setIsLoading(false);
      },
      onError: (error) => {
        toast({
          title: '遇到错误，暂时无法加载数字人',
          status: 'error'
        });
        setIsLoading(false);
      }
    }
  );

  useEffect(() => {
    if (isTranscription) {
      // 提示转录中
      // toast({
      //   title: '正在解析语音，请稍候...',
      //   status: 'info'
      // });
    }
  }, [isTranscription]);

  // 离开当前页面时销毁数字人视频流
  useEffect(() => {
    return () => {
      // 销毁数字人视频流
      if (webSocketObjRef?.current?.readyState) {
        destroyInstance();
      }
      // 清除销毁处理计时器
      clearInterval(destroyInstanceTimer.current);
    };
  }, []);

  useEffect(() => {
    return () => {
      // 销毁语音识别的WebSocket连接
      if (isSpeaking) {
        stopSpeak();
      }
    };
  }, [isSpeaking]);

  // 销毁数字人视频流
  const destroyInstance = () => {
    webSocketObjRef?.current?.send(
      JSON.stringify({
        type: 20002
      })
    );

    // 移除所有事件监听
    if (webSocketObjRef?.current) {
      webSocketObjRef.current.onclose = null;
      webSocketObjRef.current.onerror = null;
      webSocketObjRef.current.onmessage = null;
      webSocketObjRef.current.onopen = null;
    }

    // 如果连接还是开启状态，则关闭连接
    if (webSocketObjRef?.current?.readyState === WebSocket.OPEN) {
      webSocketObjRef.current.close(1000, '正常关闭');
      // 清空引用
      webSocketObjRef.current = null;
    }
  };

  // 播报文字
  const sendText = useCallback(
    (sendText: string) => {
      // 如果文本为空或只包含空格,则return
      if (!sendText || sendText.trim() === '') {
        return;
      }

      // 判断是否至少包含数字、字母、中文其中之一
      const validContentRegex = /[\u4e00-\u9fa5a-zA-Z0-9]/;
      // 如果不包含任何有效内容,则return
      if (!validContentRegex.test(sendText)) {
        return;
      }

      // 播报前的字符替换 Start
      const symbolMap: { [key: string]: string } = {
        // '\\': '杠',
        // '/': '杠',
        // '@': '艾特',
        // '#': '井号',
        // '$': '美元',
        // '%': '百分号',
        // '&': '和',
        // '*': '星号',
        // '。': '',  // 中文句号
        // '．': '',  // 全角点
        // '.': '',   // 英文句号
        // '1': '一',
        // '2': '二',
        // '3': '三',
        // '4': '四',
        // '5': '五',
        // '6': '六',
        // '7': '七',
        // '8': '八',
        // '9': '九',
        // '0': '零'
        '\n': '，',
        '|': '，',
        '-': '，',
        '——': '，',
        '……': '，',
        '：': '，',
        '*': '，',
        '"': ''
        // '\\': '',  // 这样是去除不了\的
      };

      Object.keys(symbolMap).forEach((symbol) => {
        sendText = sendText.split(symbol).join(symbolMap[symbol]);
      });

      // 手动逐字符处理处理字符串，移除所有反斜杠
      let result = '';
      for (let i = 0; i < sendText.length; i++) {
        if (sendText[i] !== '\\') {
          result += sendText[i];
        }
      }
      sendText = result;

      // 播报前的字符替换 End

      // 去除sendText中的空格
      sendText = sendText.replace(/\s+/g, '');
      // 如果sendText中有重复超过10个的字符(只匹配符号的连续重复),则只保留一个(因为发现在,号太多时数字人不会说话)
      sendText = sendText.replace(/([^\w\u4e00-\u9fa5])\1{10,}/g, '$1');

      // 将根据发送的文本推送合成视频
      webSocketObjRef?.current?.send(
        JSON.stringify({
          type: 20003,
          data: `{"text":"${sendText}","volume":100,"speechRate":1.1,"pitchRate":1}`
        })
      );
    },
    [webSocketObjRef, instanceData]
  );

  const answerCallback = (value: string, isAnswerDone?: boolean) => {
    console.log('answerCallback', value, isAnswerDone);
    // 累加
    setAnswerStr((prev: string) => (prev || '') + value);
    // const punctuations = ['，', ',', '。', '.', '！', '!', '？', '?'];
    // 感叹号不能拿来断句,因为要把感叹号完整的留下来然后匹配![xxx](xxx)然后过滤掉
    // const punctuations = ['，', ',', '。', '？', '?'];
    const punctuations = ['，', ',', '。', '！', '!', '？', '?'];

    // 遇到这些符号,或当前回答流结束,并且当前麦克风没有开启,则调用文字播报
    if ((punctuations.includes(value) || isAnswerDone) && !isSpeaking) {
      // 使用函数式更新来确保拿到最新值
      setAnswerStr((prev: string) => {
        // 过滤掉格式为![xxx](xxx)的数据
        // const imageRegex = /!\[(.*?)\]\((.*?)\)/g; // 使用全局匹配
        // 过滤掉格式为[xxx](xxx)的数据
        const imageRegex = /\[(.*?)\]\((.*?)\)/g; // 使用全局匹配
        prev = prev.replace(imageRegex, ''); // 直接替换为空字符串
        console.log('当前累积的文本:', prev);
        // 调用文字播报
        sendText(prev);
        return '';
      });
    } else {
    }
  };

  useEffect(() => {
    // 确保已收到识别结果内容
    if (speakResult && isShouldSpeak) {
      // 调用数字人说话
      eventBus.emit(EventNameEnum.sendQuestion, { text: speakResult });
      // 初始化存储的语音识别结果内容
      setSpeakResult('');
      setIsShouldSpeak(false);
    }
  }, [speakResult, isShouldSpeak]);

  const clickStopSpeak = () => {
    webSocketObjRef?.current?.send(
      JSON.stringify({
        type: 20001
      })
    );
  };

  const updateHumanImg = (humanSettings: any, humanSettings2d: any) => {
    // 确保状态更新完成后再执行后续操作
    flushSync(() => {
      setSelectedHumanImg(humanSettings);
      if (humanSettings2d) {
        setHumanSettings2d(humanSettings2d);
      }
    });
    // 状态已同步更新，可以安全执行后续操作
    destroyInstance();
    refetchInstance();
    window.VePlayer?.destroy();
  };

  // eventBus监听停止数字人说话
  useEffect(() => {
    multiEventBus.on(EventNameEnum.stopSpeak, () => {
      clickStopSpeak();
    });
  }, []);

  useEffect(() => {
    let heartbeatTimer: NodeJS.Timeout;

    if (instanceData) {
      const socket: any = new WebSocket(
        `wss://api-mp.x-era.com/v1/instance/${instanceData.uuid}/control`
      );
      webSocketObjRef.current = socket;

      socket?.addEventListener('open', function () {
        console.log('WebSocket is open now.');
        // 轮询间隔1000毫秒,超过1500毫秒就有可能断连
        heartbeatTimer = setInterval(() => {
          socket?.send(
            JSON.stringify({
              type: 10001
            })
          );
        }, 1000);

        const script = document.createElement('script');
        // script.src =
        //   'https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/2.1.0/umd/veplayer.production.js';
        script.src = '/js/veplayer.production.js';
        script.onload = () => {
          if (window.VePlayer) {
            window.VePlayer.createLivePlayer({
              width: '100%',
              height: '100%',
              el: document.getElementById('veplayerId'), // 容器
              lang: 'zh',
              controls: false, // 是否添加控制条
              url: instanceData?.pullStreamUrl?.[0], // https://*.sdp
              mobile: {
                gradient: 'none', // 去除 H5 上下阴影遮罩效果
                disableGesture: 1 // 去除手势操作
              },
              autoplay: ['46589', '49438', '49439'].includes(router.query.appId as string)
                ? true
                : { muted: true }, // 宝教通:有声自动播放,其他:静音自动播放
              closeVideoClick: true, // 是否关闭 PC 端单击播放区域切换播放/暂停的能力，开启时，点击播放器区域可实现播放或暂停。
              logger: {
                appId: '03ec65f2-d197-4577-8855-5c898db43684',
                userId: '',
                deviceId: ''
              }
            })
              .then(function (veplayer: any) {
                window.VePlayer = veplayer;
              })
              .catch(function (err: any) {
                console.log('创建播放器失败:', err);
              });
          }
        };
        document.body.appendChild(script);

        socket?.send(
          JSON.stringify({
            type: 10003,
            data: getToken()
          })
        );
      });
    }

    // 销毁心跳定时器
    return () => {
      if (heartbeatTimer) {
        clearInterval(heartbeatTimer);
      }
    };
  }, [instanceData]);

  return (
    <Flex
      h="100%"
      pt={respDims(46)}
      pl={respDims(46)}
      pr={respDims(46)}
      alignItems="center"
      overflow="hidden"
      pos="relative"
      bgColor="white"
      backgroundImage="/imgs/humans/humanPageBg.png"
      backgroundRepeat="no-repeat"
      backgroundSize="100% 100%"
    >
      {isLoading && <Loading zIndex={1000000} text="正在生成数字人..." />}
      {/* VePlayer文档 https://www.volcengine.com/docs/6469/1155423 */}
      <Head>
        {/* <link
          rel="stylesheet"
          href="https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/2.1.0/umd/veplayer.production.css"
        /> */}
        <link rel="stylesheet" href="/css/veplayer.production.css" />
      </Head>
      <div
        ref={veplayerRef}
        id="veplayerId"
        style={{
          visibility: ['46589', '49438', '49439'].includes(router.query.appId as string)
            ? 'hidden'
            : 'visible'
        }}
      ></div>
      {selectedHumanImg && (
        <Image
          pos="fixed"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          // 去除selectedHumanImg图片地址中的-mini字符
          src={selectedHumanImg?.replace('-mini', '')}
          w="272px"
          h="364px"
        />
      )}
      {!selectedHumanImg && ['46589'].includes(router.query.appId as string) && (
        <Image
          pos="fixed"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          // 去除selectedHumanImg图片地址中的-mini字符
          src={'/imgs/humans/bazj.png'}
          w="272px"
          h="364px"
        />
      )}
      {!selectedHumanImg && ['49438'].includes(router.query.appId as string) && (
        <Image
          pos="fixed"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          src={'/imgs/humans/xa.png'}
          w="273px"
          h="402px"
        />
      )}
      {!selectedHumanImg && ['49439'].includes(router.query.appId as string) && (
        <Image
          pos="fixed"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          src={'/imgs/humans/xb.png'}
          w="297px"
          h="381px"
        />
      )}
      {/* humanSettingModal */}
      {isHumanSettingModal && (
        <HumanSettingModal
          isOpen={isHumanSettingModal}
          onClose={() => setIsHumanSettingModal(false)}
          refetchInstance={refetchInstance}
          destroyInstance={destroyInstance}
          updateHumanImg={updateHumanImg}
          humanSettings2d={humanSettings2d}
        />
      )}
      {/* top */}
      <Flex
        pos="fixed"
        top="24px"
        left="50%"
        transform="translateX(-50%)"
        w="calc(100% - 88px)"
        h="65px"
        lineHeight="65px"
        borderRadius="18px"
        bg="rgba(255, 255, 255, 0.21)"
        boxShadow="0px 0px 11.1px 0px rgba(112, 134, 156, 0.11)"
        backdropFilter="blur(14.050000190734863px)"
        alignItems="center"
        textAlign="center"
      >
        <Flex
          w="40px"
          h="40px"
          borderRadius="50%"
          bg="white"
          justifyContent="center"
          alignItems="center"
          pos="absolute"
          left="29px"
          userSelect="none"
          cursor="pointer"
          onClick={() => {
            router.back();
          }}
        >
          <Image src="/imgs/humans/humanDown.svg" w="14px" h="14px" />
        </Flex>
        <Box m="0 auto" fontSize="16px" fontWeight="500" color="#030712" userSelect="none">
          {router.query.appName}
        </Box>
        <Flex
          w="40px"
          h="40px"
          borderRadius="50%"
          bg="white"
          justifyContent="center"
          alignItems="center"
          pos="absolute"
          right="24px"
          userSelect="none"
          cursor="pointer"
          onClick={() => {
            setIsHumanSettingModal(true);
          }}
        >
          <Image src="/imgs/humans/humanSetting.svg" w="18px" h="18px" />
        </Flex>
      </Flex>
      <Flex
        pos="fixed"
        bottom="0px"
        right="44px"
        overflowY="auto"
        minW="340px" // 移动端布局下的兼容
        maxW="720px"
        w="38%"
        height="calc(100vh - 100px)"
      >
        {/* chat对话框 */}
        <ChatPanelHuman
          chatId={router.query.appId as string}
          activeRoute={''}
          answerCallback={answerCallback}
        />
      </Flex>
      {/* 开始识别麦克风说话内容 */}
      {!isSpeaking && (
        <Flex
          w="60px"
          h="60px"
          bg="linear-gradient(120deg, #DC7EFF -9.5%, #601CFF 87.77%)"
          borderRadius="50%"
          pos="fixed"
          bottom={isPc ? '47px' : '100px'}
          left="50%"
          transform="translateX(-50%)"
          justifyContent="center"
          alignItems="center"
          userSelect="none"
          cursor="pointer"
          onClick={() => {
            // 初始化存储的语音识别内容
            setSpeakResult('');
            setIsShouldSpeak(false);
            // 停止数字人说话
            multiEventBus.emit(EventNameEnum.stopSpeak);
            startSpeak();
          }}
        >
          <Image src="/imgs/humans/humanMicrophone.svg" w="32px" h="32px" />
        </Flex>
      )}
      {/* 停止识别麦克风说话内容 */}
      {isSpeaking && (
        <Flex
          w="60px"
          h="60px"
          borderRadius="50%"
          bg="linear-gradient(120deg, #DC7EFF -9.5%, #601CFF 87.77%)"
          pos="fixed"
          bottom={isPc ? '47px' : '100px'}
          left="50%"
          transform="translateX(-50%)"
          justifyContent="center"
          alignItems="center"
          userSelect="none"
          cursor="pointer"
          onClick={() => {
            stopSpeak();
            setIsShouldSpeak(true);
          }}
        >
          <Image src="/imgs/humans/humanSpeakStop.svg" w="32px" h="32px" />
        </Flex>
      )}
      {/* 推荐问题 */}
      {isPc && recommendQuestions?.length > 0 && (
        <Flex pos="fixed" bottom="47px" left="44px" flexDir="column" userSelect="none">
          <Box
            fontSize="18px"
            fontWeight={600}
            color="#000"
            onClick={() => {
              console.log('stopSpeak');
            }}
          >
            你可以对我说：
          </Box>
          <Flex mt="10px" flexDir="column" minH="586px">
            {recommendQuestions.map((item) => (
              <Box
                maxW="446px"
                overflow="hidden"
                textOverflow="ellipsis"
                whiteSpace="nowrap"
                borderRadius="50px 0px 0px 50px"
                color="white"
                lineHeight="46px"
                padding="0px 13px"
                background="linear-gradient(90deg, #9360FF 0%, rgba(106, 36, 255, 0.01) 100%)"
                cursor="pointer"
                _notFirst={{
                  marginTop: '14px'
                }}
                key={item}
                onClick={() => {
                  // 停止数字人说话
                  multiEventBus.emit(EventNameEnum.stopSpeak);
                  // 调用数字人说话
                  eventBus.emit(EventNameEnum.sendQuestion, { text: item });
                }}
              >
                {item}
              </Box>
            ))}
          </Flex>
        </Flex>
      )}

      {/* 数字人-调试测试-输入区域 Start */}
      {/* <Flex pos="fixed" left="0" top="0">
        <Input
          borderRadius="50px"
          placeholder="请输入文字"
          value={inputText}
          onChange={(e) => {
            setInputText(e.target.value);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              sendText(inputText);
            }
          }}
        ></Input>
        <Flex whiteSpace="nowrap">
          <Box onClick={() => sendText(inputText)}>发送</Box>
          <Box onClick={clickStopSpeak}>&nbsp;停止说话</Box>
        </Flex>
      </Flex> */}
      {/* 数字人-调试测试-输入区域 End */}
    </Flex>
  );
};

export default Humans;
