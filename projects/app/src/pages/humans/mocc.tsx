import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Flex, Input, Image } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { digitalhumanV1Instance } from '@/api/human';
import { useQuery } from '@tanstack/react-query';
import Head from 'next/head';
import ChatPanelHumanMooc from '@/components/ChatPanelHumanMooc';
import { useRouter } from 'next/router';
import { getToken } from '@/utils/humanAuth';
import HumanSettingModal from './components/HumanSettingModal';
import { timbresList, imageList, isDestroyInstance, activityGetUsing, nlsToken } from '@/api/human';
import Loading from '@/components/Loading';
import dayjs from 'dayjs';
import { EventNameEnum, eventBus, multiEventBus } from '@/utils/eventbus';
import { setHumanInfo } from '@/utils/humanAuth';
import { eraLogin } from '@/api/human';
import { useToast } from '@/hooks/useToast';
import useVoiceRecognition from '@/hooks/useVoiceRecognition';
import { nanoid } from 'nanoid';

const Humans = ({ sceneId }: { sceneId?: string }) => {
  const [inputText, setInputText] = useState<any>();
  const [answerStr, setAnswerStr] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const webSocketObjRef = useRef<any>();
  const veplayerRef = useRef<any>();
  const router = useRouter();
  const [isHumanSettingModal, setIsHumanSettingModal] = useState(false);
  const destroyInstanceTimer = useRef<NodeJS.Timeout>();
  // 是否处于应发送消息状态
  const [curGroupId, setCurGroupId] = useState<number>(1);
  const { toast } = useToast();
  const [isHumanLoaded, setIsHumanLoaded] = useState(false);
  const [nlsTokenData, setNlsTokenData] = useState<any>();
  const [chatId] = useState(() => nanoid());

  // 获取阿里云语音转文字的token
  const { refetch: refetchNlsToken } = useQuery<any>(['nlsToken'], () => nlsToken({}), {
    enabled: false,
    onSuccess: (data) => {
      setNlsTokenData(data);
    }
  });

  useEffect(() => {
    refetchNlsToken();
  }, []);

  const { isSpeaking, stopSpeak, startSpeak, isTranscription } = useVoiceRecognition({
    appkey: nlsTokenData?.appKey,
    token: nlsTokenData?.token,
    onResult: (text: string) => {
      console.log('最终发送给数字人的数据', text);
      if (text) {
        // 发送问题并调用数字人播报回答
        eventBus.emit(EventNameEnum.sendQuestion, { text: text });
      }
    }
  });

  useEffect(() => {
    if (nlsTokenData?.token) {
      startSpeak(true, ['你好']);
    }
  }, [nlsTokenData?.token]);

  // 1秒一次轮询获取当前环节对应的智能体ID
  const { data: activityUsingData } = useQuery<any>(
    ['activityUsingData'],
    () => activityGetUsing({}),
    {
      refetchInterval: 1000,
      refetchIntervalInBackground: true,
      onSuccess: (data) => {}
    }
  );

  useEffect(() => {
    // 数字人登录
    eraLogin({}).then((res) => {
      setHumanInfo(res);
    });
  }, []);

  // 主动销毁处理(防止并发1问题)
  useEffect(() => {
    // 如果有hack参数(hackTimestamp等于当前分钟数,如2025-03-18-16:22),不轮询
    if (
      !router.query.hackTimestamp ||
      router.query.hackTimestamp !== dayjs().format('YYYY-MM-DD-HH:mm')
    ) {
      destroyInstanceTimer.current = setInterval(() => {
        isDestroyInstance({}).then((res) => {
          if (res) {
            webSocketObjRef?.current?.send(
              JSON.stringify({
                type: 20002
              })
            );
          }
        });
      }, 10000);
    }
  }, []);

  // 如果localStorage中没有数字人音色、形象列表,则获取列表,把第一项存到localStorage
  useEffect(() => {
    if (!localStorage.getItem('humanSettings')) {
      Promise.all([timbresList(), imageList()]).then(([timbresRes, imageRes]) => {
        let humanSettings = {
          timbreId: timbresRes[0]?.voiceId,
          sceneId: imageRes[0]?.sceneId
        };
        localStorage.setItem('humanSettings', JSON.stringify(humanSettings));
        refetchInstance();
      });
    } else {
      refetchInstance();
    }
  }, []);

  // 创建数字人接口
  const { data: instanceData, refetch: refetchInstance } = useQuery<any>(
    ['instance'],
    () => {
      setIsLoading(true);
      return digitalhumanV1Instance({
        video: {
          // width和height的值调换可以调转方向
          width: 1080,
          height: 1920,
          bitrate: 2000
        },
        position: {
          width: 1080,
          height: 1920,
          auto: false,
          left: 14,
          top: 104
        },

        // 读取本地存储的sceneId和timbreId
        sceneId: JSON.parse(localStorage.getItem('humanSettings') || '{}')?.sceneId,
        timbreId: JSON.parse(localStorage.getItem('humanSettings') || '{}')?.timbreId,

        mode: 'interact',
        // 透明图片行不通,视频背景会变黑色
        background: {
          type: 'image',
          imageUrl:
            'https://huayun-ai-obs-public.huayuntiantu.com/5a5d2b3bb2e678ce5d12d2fc637477b6.png'
        },
        volume: 3.0,
        stopAfter: 2 // 单位：秒。超过此时间没有收到全双工信息，将停止推流。
      });
    },
    {
      enabled: false, // 禁用自动运行
      onSuccess: (data) => {
        setIsLoading(false);
        setIsHumanLoaded(true);
      },
      onError: (error) => {
        toast({
          title: '遇到错误，请重新加载数字人',
          status: 'error'
        });
        setIsLoading(false);
      }
    }
  );

  useEffect(() => {
    if (isTranscription) {
      console.log('正在解析语音，请稍候...');
    }
  }, [isTranscription]);

  // 离开当前页面时销毁数字人视频流
  useEffect(() => {
    return () => {
      // 销毁数字人视频流
      if (webSocketObjRef?.current?.readyState) {
        destroyInstance();
      }
      // 清除销毁处理计时器
      clearInterval(destroyInstanceTimer.current);
    };
  }, []);

  useEffect(() => {
    return () => {
      // 销毁语音识别的WebSocket连接
      if (isSpeaking) {
        stopSpeak();
      }
    };
  }, [isSpeaking]);

  // 销毁数字人视频流
  const destroyInstance = () => {
    webSocketObjRef?.current?.send(
      JSON.stringify({
        type: 20002
      })
    );

    // 移除所有事件监听
    if (webSocketObjRef?.current) {
      webSocketObjRef.current.onclose = null;
      webSocketObjRef.current.onerror = null;
      webSocketObjRef.current.onmessage = null;
      webSocketObjRef.current.onopen = null;
    }

    // 如果连接还是开启状态，则关闭连接
    if (webSocketObjRef?.current?.readyState === WebSocket.OPEN) {
      webSocketObjRef.current.close(1000, '正常关闭');
      // 清空引用
      webSocketObjRef.current = null;
    }
  };

  // 播报文字
  const sendText = useCallback(
    (sendText: string) => {
      // 如果文本为空或只包含空格,则return
      if (!sendText || sendText.trim() === '') {
        return;
      }

      // 判断是否至少包含数字、字母、中文其中之一
      const validContentRegex = /[\u4e00-\u9fa5a-zA-Z0-9]/;
      // 如果不包含任何有效内容,则return
      if (!validContentRegex.test(sendText)) {
        return;
      }

      // 播报前的字符替换 Start
      const symbolMap: { [key: string]: string } = {
        // '\\': '杠',
        // '/': '杠',
        // '@': '艾特',
        // '#': '井号',
        // '$': '美元',
        // '%': '百分号',
        // '&': '和',
        // '*': '星号',
        // '。': '',  // 中文句号
        // '．': '',  // 全角点
        // '.': '',   // 英文句号
        // '1': '一',
        // '2': '二',
        // '3': '三',
        // '4': '四',
        // '5': '五',
        // '6': '六',
        // '7': '七',
        // '8': '八',
        // '9': '九',
        // '0': '零'
        '\n': '，',
        '|': '，',
        '-': '，',
        '——': '，',
        '……': '，',
        '：': '，',
        '*': '，',
        '"': ''
        // '\\': '',  // 这样是去除不了\的
      };

      Object.keys(symbolMap).forEach((symbol) => {
        sendText = sendText.split(symbol).join(symbolMap[symbol]);
      });

      // 手动逐字符处理处理字符串，移除所有反斜杠
      let result = '';
      for (let i = 0; i < sendText.length; i++) {
        if (sendText[i] !== '\\') {
          result += sendText[i];
        }
      }
      sendText = result;

      // 播报前的字符替换 End

      // 去除sendText中的空格
      sendText = sendText.replace(/\s+/g, '');
      // 如果sendText中有重复超过10个的字符(只匹配符号的连续重复),则只保留一个(因为发现在,号太多时数字人不会说话)
      sendText = sendText.replace(/([^\w\u4e00-\u9fa5])\1{10,}/g, '$1');

      // 将根据发送的文本推送合成视频
      webSocketObjRef?.current?.send(
        JSON.stringify({
          type: 20003,
          data: `{"text":"${sendText}","volume":100,"speechRate":1.1,"pitchRate":1}`
        })
      );
    },
    [webSocketObjRef, instanceData]
  );

  const answerCallback = (value: string, isAnswerDone?: boolean) => {
    console.log('answerCallback', value, isAnswerDone);
    // 累加
    setAnswerStr((prev: string) => (prev || '') + value);
    // const punctuations = ['，', ',', '。', '.', '！', '!', '？', '?'];
    // 感叹号不能拿来断句,因为要把感叹号完整的留下来然后匹配![xxx](xxx)然后过滤掉
    // const punctuations = ['，', ',', '。', '？', '?'];
    const punctuations = ['，', ',', '。', '！', '!', '？', '?'];

    // 遇到这些符号,或当前回答流结束,并且当前麦克风没有开启,则调用文字播报
    if (punctuations.includes(value) || isAnswerDone) {
      // 使用函数式更新来确保拿到最新值
      setAnswerStr((prev: string) => {
        // 过滤掉格式为![xxx](xxx)的数据
        // const imageRegex = /!\[(.*?)\]\((.*?)\)/g; // 使用全局匹配
        // 过滤掉格式为[xxx](xxx)的数据
        const imageRegex = /\[(.*?)\]\((.*?)\)/g; // 使用全局匹配
        prev = prev.replace(imageRegex, ''); // 直接替换为空字符串
        console.log('当前累积的文本:', prev);
        // 调用文字播报
        sendText(prev);
        return '';
      });
    } else {
    }
  };

  const clickStopSpeak = () => {
    webSocketObjRef?.current?.send(
      JSON.stringify({
        type: 20001
      })
    );
  };

  useEffect(() => {
    multiEventBus.on(EventNameEnum.stopSpeak, () => {
      clickStopSpeak();
    });
  }, []);

  useEffect(() => {
    let heartbeatTimer: NodeJS.Timeout;

    if (instanceData) {
      const socket: any = new WebSocket(
        `wss://api-mp.x-era.com/v1/instance/${instanceData.uuid}/control`
      );
      webSocketObjRef.current = socket;

      socket?.addEventListener('open', function () {
        console.log('WebSocket is open now.');
        // 轮询间隔1000毫秒,超过1500毫秒就有可能断连
        heartbeatTimer = setInterval(() => {
          socket?.send(
            JSON.stringify({
              type: 10001
            })
          );
        }, 1000);

        const script = document.createElement('script');
        // script.src =
        //   'https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/2.1.0/umd/veplayer.production.js';
        script.src = '/js/veplayer.production.js';
        script.onload = () => {
          if (window.VePlayer) {
            window.VePlayer.createLivePlayer({
              width: '100%',
              height: '100%',
              el: document.getElementById('veplayerIdMocc'), // 容器
              lang: 'zh',
              controls: false, // 是否添加控制条
              url: instanceData?.pullStreamUrl?.[0], // https://*.sdp
              mobile: {
                gradient: 'none', // 去除 H5 上下阴影遮罩效果
                disableGesture: 1 // 去除手势操作
              },
              autoplay: { muted: true }, // 是否自动播放,类型：boolean | { muted?: boolean; }
              closeVideoClick: true, // 是否关闭 PC 端单击播放区域切换播放/暂停的能力，开启时，点击播放器区域可实现播放或暂停。
              logger: {
                appId: '03ec65f2-d197-4577-8855-5c898db43684',
                userId: '',
                deviceId: ''
              }
            })
              .then(function (veplayer: any) {
                window.VePlayer = veplayer;

                /** 大屏调试,打印尺寸用 Start
                // 使用setTimeout确保video元素已完全加载
                setTimeout(() => {
                  const playerContainer = document.getElementById('veplayerIdMocc');
                  if (playerContainer) {
                    const videoElement = playerContainer.querySelector('video');
                    if (videoElement) {
                      console.log('Video 元素宽度:', videoElement.offsetWidth);
                      console.log('Video 元素高度:', videoElement.offsetHeight);

                      // 获取更详细的尺寸信息
                      const rect = videoElement.getBoundingClientRect();
                      console.log('Video 元素详细尺寸:', {
                        width: rect.width,
                        height: rect.height,
                        top: rect.top,
                        left: rect.left,
                        right: rect.right,
                        bottom: rect.bottom
                      });

                      // 获取计算样式
                      const computedStyle = window.getComputedStyle(videoElement);
                      console.log('Video 计算样式宽度:', computedStyle.width);
                      console.log('Video 计算样式高度:', computedStyle.height);

                      // 打印video的class和id
                      console.log('Video 类名:', videoElement.className);
                      console.log('Video ID:', videoElement.id);

                      // 打印当前全屏的宽高
                      console.log('当前全屏的宽高:', window.innerWidth, window.innerHeight);
                    } else {
                      console.log('未找到video元素');
                    }
                  } else {
                    console.log('未找到播放器容器');
                  }
                }, 500); // 延迟500ms确保元素已渲染
                /** 大屏调试,打印尺寸用 End */
              })
              .catch(function (err: any) {
                console.log('创建播放器失败:', err);
              });
          }
        };
        document.body.appendChild(script);

        socket?.send(
          JSON.stringify({
            type: 10003,
            data: getToken()
          })
        );
      });
    }

    // 销毁心跳定时器
    return () => {
      if (heartbeatTimer) {
        clearInterval(heartbeatTimer);
      }
    };
  }, [instanceData]);

  return (
    <Flex
      h="100%"
      pt={respDims(46)}
      pl={respDims(46)}
      pr={respDims(46)}
      alignItems="center"
      overflow="hidden"
      pos="relative"
      style={{
        cursor: 'pointer'
      }}
      onClick={() => {
        // 打断数字人
        multiEventBus.emit(EventNameEnum.stopSpeak);
      }}
    >
      {isLoading && <Loading zIndex={1000000} text="正在生成数字人..." />}
      {/* VePlayer文档 https://www.volcengine.com/docs/6469/1155423 */}
      <Head>
        {/* <link
          rel="stylesheet"
          href="https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/2.1.0/umd/veplayer.production.css"
        /> */}
        <link rel="stylesheet" href="/css/veplayer.production.css" />
      </Head>
      <div ref={veplayerRef} id="veplayerIdMocc"></div>
      {/* humanSettingModal */}
      {isHumanSettingModal && (
        <HumanSettingModal
          isOpen={isHumanSettingModal}
          onClose={() => setIsHumanSettingModal(false)}
          refetchInstance={refetchInstance}
          destroyInstance={destroyInstance}
        />
      )}
      {/* AI助教 */}
      <Flex pos="fixed" top="24px" left="50%" transform="translateX(-50%)" justifyContent="center">
        <Image src="/imgs/humans/humanTitle.png" w="70%" h="70%" />
      </Flex>
      <Flex
        pos="fixed"
        bottom="226px"
        left="50%"
        transform="translateX(-50%)"
        overflowY="auto"
        minW="340px" // 移动端布局下的兼容
        maxW="720px"
        w="96%"
        height="372px"
        visibility="hidden"
      >
        {/* chat对话框,设置了不显示但不能删除,因为需要保留对话功能 */}
        {isHumanLoaded && (
          <ChatPanelHumanMooc
            appId={activityUsingData?.tenantAppId}
            chatId={chatId}
            activeRoute={''}
            answerCallback={answerCallback}
            groupId={curGroupId.toString()}
          />
        )}
      </Flex>

      {/* 数字人-调试测试-输入区域 Start */}
      {/* <Flex pos="fixed" left="0" top="0">
        <Input
          borderRadius="50px"
          placeholder="请输入文字"
          value={inputText}
          onChange={(e) => {
            setInputText(e.target.value);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              sendText(inputText);
            }
          }}
        ></Input>
        <Flex whiteSpace="nowrap">
          <Box onClick={() => sendText(inputText)}>发送</Box>
          <Box onClick={clickStopSpeak}>&nbsp;停止说话</Box>
        </Flex>
      </Flex> */}

      {/* 发送提问并调用数字人播报回答 Start */}
      {/* <Flex pos="fixed" top="100px" left="50%" transform="translateX(-50%)" alignItems="center">
        <Input
          value={inputText}
          onChange={(e) => {
            setInputText(e.target.value);
          }}
          placeholder="#调试#发送提问,调用数字人播报回答"
        ></Input>
        <Flex
          color="white"
          onClick={() => {
            eventBus.emit(EventNameEnum.sendQuestion, { text: inputText });
          }}
          whiteSpace="nowrap"
          ml="2px"
        >
          发送
        </Flex>
      </Flex> */}
      {/* 发送提问并调用数字人播报回答 End */}
      {/* 数字人-调试测试-输入区域 End */}
    </Flex>
  );
};

export default Humans;
