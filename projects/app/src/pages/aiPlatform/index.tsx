import {
  Box,
  Heading,
  SimpleGrid,
  Input,
  InputGroup,
  Flex,
  InputRightElement
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import Sidebar from './components/Sidebar';
import AICard from './components/AICard';
import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { AiAccountType } from '@/types/api/aiPlatform';
import { getAiAccountList } from '@/api/aiPlatform';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { serviceSideProps } from '@/utils/i18n';
import { useQuery } from '@tanstack/react-query';
import { useDebounce } from 'use-debounce';
import { useRoutes } from '@/hooks/useRoutes';
import { useTenantStore } from '@/store/useTenantStore';

const AiPlatform = () => {
  const [aiAccounts, setAiAccounts] = useState<AiAccountType[]>([]);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [allAiAccounts, setAllAiAccounts] = useState<AiAccountType[]>([]);
  const contentRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const rightContentRef = useRef<HTMLDivElement | null>(null);
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);
  const { tenant } = useTenantStore();

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['aiAccounts'],
    queryFn: () => getAiAccountList({ nameOrIntro: '' })
  });

  useEffect(() => {
    if (data) {
      setAllAiAccounts(data);
      setAiAccounts(data);
      if (data.length > 0) {
        setActiveCategory(data[0].categoryName);
      }
    }
  }, [data]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const categoryName = entry.target.getAttribute('data-category-name');
            if (categoryName) {
              setActiveCategory(categoryName);
            }
          }
        });
      },
      { threshold: 0.5 }
    );

    Object.values(contentRefs.current).forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [contentRefs]);

  const handleSearch = useCallback(() => {
    if (debouncedSearchTerm) {
      getAiAccountList({ nameOrIntro: debouncedSearchTerm }).then((data) => {
        setAiAccounts(data);
        if (data.length > 0) {
          setActiveCategory(data[0].categoryName);
        }
      });
    } else {
      setAiAccounts(allAiAccounts);
    }
  }, [debouncedSearchTerm, allAiAccounts]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        handleSearch();
      }
    },
    [handleSearch]
  );

  const getVisibleCategory = () => {
    const categories = document.querySelectorAll('[data-category-name]');
    let visibleCategory: string | null = activeCategory;

    categories.forEach((category) => {
      const rect = category.getBoundingClientRect();
      if (rect.top >= 0 && rect.top <= window.innerHeight / 5) {
        visibleCategory = category.getAttribute('data-category-name') || visibleCategory;
      }
    });

    return visibleCategory;
  };

  useEffect(() => {
    const handleScroll = () => {
      const visibleCategory = getVisibleCategory();
      if (visibleCategory && visibleCategory !== activeCategory) {
        setActiveCategory(visibleCategory);
      }
    };

    rightContentRef.current?.addEventListener('scroll', handleScroll);
    return () => {
      rightContentRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [activeCategory]);

  const handleCategoryClick = (categoryName: string) => {
    setActiveCategory(categoryName);
    const contentRef = contentRefs.current[categoryName];
    if (contentRef) {
      contentRef.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const memoizedCategories = useMemo(() => allAiAccounts, [allAiAccounts]);

  const renderLoadingState = () => (
    <Box position="absolute" top="50%" left="50%" transform="translate(-50%, -50%)" color="#a9a9ac">
      加载中...
    </Box>
  );

  const renderEmptyState = () => (
    <Box position="absolute" top="50%" left="50%" transform="translate(-50%, -50%)" color="#a9a9ac">
      暂无数据
    </Box>
  );

  return (
    <Flex
      bgImage={
        tenant?.functionBackgroundImgUrl
          ? tenant.functionBackgroundImgUrl
          : '/imgs/common/ai_nav_bg.png'
      }
      bgSize="cover"
      bgPosition="center"
      bgRepeat="no-repeat"
      w="100%"
      h="100%"
      justifyContent="space-between"
      p="28px 24px 0px 16px"
    >
      <Sidebar
        categories={memoizedCategories}
        onCategoryClick={handleCategoryClick}
        activeCategory={activeCategory || ''}
      />
      <Flex
        flexDir="column"
        p="0 17px 0px 0"
        flex="1"
        position="relative"
        h="100%"
        overflow="hidden"
      >
        <Box mb={8} pl="16px">
          <InputGroup maxW="532px">
            <Input
              placeholder="搜索"
              bg="rgba(0, 0, 0, 0.04);"
              borderRadius="8px"
              fontSize="15px"
              _placeholder={{
                fontSize: '14px'
              }}
              fontWeight="400"
              pr="60px"
              onChange={(e) => setSearchTerm(e.target.value)}
              value={searchTerm}
              onKeyDown={handleKeyDown}
            />
            <InputRightElement>
              {searchTerm && (
                <SvgIcon
                  name="close"
                  w={respDims('14fpx')}
                  h={respDims('14fpx')}
                  cursor="pointer"
                  onClick={() => {
                    setSearchTerm('');
                    setAiAccounts(allAiAccounts);
                  }}
                  color="#4E5969"
                />
              )}
              <SearchIcon color="gray.400" m="0 20px 0 10px" onClick={handleSearch} />
            </InputRightElement>
          </InputGroup>
        </Box>

        {isLoading ? (
          renderLoadingState()
        ) : (
          <Box
            flex="1"
            position="relative"
            ref={rightContentRef}
            overflowY="auto"
            overflowX="hidden"
          >
            {aiAccounts.length > 0
              ? aiAccounts.map((account) => (
                  <Box
                    key={account.categoryName}
                    mb={respDims(32)}
                    ref={(el) => (contentRefs.current[account.categoryName] = el)}
                    data-category-name={account.categoryName}
                  >
                    <Heading
                      mb={respDims(20)}
                      fontSize={respDims(24)}
                      fontWeight="500"
                      color="#303133"
                      pl="16px"
                    >
                      {account.categoryName}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing={6} pl="16px">
                      {account.clientUserThirdPartyAccountResponses.map((assistant, index) => (
                        <AICard key={index} {...assistant} />
                      ))}
                    </SimpleGrid>
                  </Box>
                ))
              : renderEmptyState()}
          </Box>
        )}
      </Flex>
    </Flex>
  );
};

export default AiPlatform;

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}
