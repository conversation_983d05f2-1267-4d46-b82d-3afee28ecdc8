import { Box, Container, HStack, VStack, Text, Button, Avatar, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
const Guide = () => {
  const router = useRouter();
  const { platformName, intro, link, logo, instructions } = router.query;
  return (
    <Box
      bgImage="url('/imgs/common/ai_nav_bg.png')"
      bgSize="cover"
      bgPosition="center"
      bgRepeat="no-repeat"
      minH="100%"
      w="100%"
    >
      {/* Header */}
      <Box position="sticky" top={0} zIndex={100}>
        <Container maxW="container.xl" py={4} w="100%">
          <HStack align="center" w="100%">
            <Box
              w={respDims(100)}
              h={respDims(34)}
              background="#FFFFFF"
              _hover={{
                background: '#764ff6',
                color: '#fff'
              }}
              borderRadius="50px"
              border="1px solid #D9CCFF"
              display="flex"
              justifyContent="center"
              alignItems="center"
              cursor="pointer"
              onClick={() => router.back()}
            >
              <SvgIcon name="chevronLeft" w={respDims(18)} h={respDims(19)} mr={respDims(2)} /> 返回
            </Box>
            <HStack spacing={4} justifyContent="center" w="100%">
              <Text textAlign="center" fontSize="18px" fontWeight="500" color="#030712">
                使用指南
              </Text>
            </HStack>
          </HStack>
        </Container>
      </Box>

      {/* Main Content */}
      <Container maxW="container.xl" pt="2px" h="100%">
        {/* AI Info */}
        <HStack
          direction="row"
          justify="space-between"
          bg="white"
          p={6}
          borderRadius="lg"
          mb="20px"
        >
          <HStack spacing={4}>
            <Avatar size="lg" src={logo as string} />
            <VStack align="start" spacing={1}>
              <Text fontSize="xl" fontWeight="bold">
                {platformName}
              </Text>
              <Text color="gray.600">{intro}</Text>
            </VStack>
          </HStack>
          <Button
            colorScheme="purple"
            onClick={() => {
              window.open(link as string, '_blank');
            }}
          >
            立即访问
          </Button>
        </HStack>

        {/* Guide Content */}
        <Box
          bg="white"
          p={8}
          borderRadius="lg"
          h="calc(100vh - 150px)"
          overflow="auto"
          minH="calc(94vh - 150px)"
        >
          <Flex mb="24px" alignItems="flex-start" direction="column">
            <Text fontSize="20px" color="#000" fontWeight="500">
              使用指南
            </Text>
            <SvgIcon width="60px" mt="-10px" height="17" name="aiNav" />
          </Flex>
          <Text
            mb={6}
            dangerouslySetInnerHTML={{
              __html: instructions ? instructions : ''
            }}
          />
        </Box>
      </Container>
    </Box>
  );
};

export default Guide;
