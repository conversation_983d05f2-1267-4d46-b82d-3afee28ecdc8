import { VStack, Text, Box, Flex } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { AiAccountType } from '@/types/api/aiPlatform';
import { useEffect, useState } from 'react';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { useRoutes } from '@/hooks/useRoutes';

const Sidebar = ({
  categories,
  onCategoryClick,
  activeCategory
}: {
  categories: AiAccountType[];
  onCategoryClick: (categoryName: string) => void;
  activeCategory: string;
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const { routeGroupMap } = useRoutes();
  const routeGroup = routeGroupMap[RouteGroupTypeEnum.Chat];

  useEffect(() => {
    if (categories.length > 0) {
      setSelectedCategory(categories[0].categoryName); // 页面加载时选中第一个类别
      onCategoryClick(categories[0].categoryName); // 调用回调函数
    }
  }, [categories]);

  return (
    <Box
      as="aside"
      w="144px"
      h="98%"
      borderRadius="21px"
      border="1px solid #FFF"
      background="rgba(255, 255, 255, 0.50)"
      boxShadow="0px 1px 4px 0px #F6F2FD"
      py="16px"
      position="relative"
      overflowY="auto"
      overflowX="hidden"
    >
      <VStack spacing={2} align="stretch">
        <Flex flexDirection="column" alignItems="center" mb="12px">
          <Text
            fontSize="18px"
            fontWeight="500"
            color="#030712"
            px={4}
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: '1',
              whiteSpace: 'normal',
              wordBreak: 'break-word',
              wordWrap: 'break-word'
            }}
          >
            {routeGroup.authActiveRoute?.name || ''}
          </Text>
          <SvgIcon mt="-10px" width="60" height="17" name="aiNav" />
        </Flex>
        {categories.length > 0 ? (
          categories.map((category, index) => (
            <Box
              cursor="pointer"
              key={index}
              m="0 10px"
              p="6px 0"
              textAlign="center"
              fontSize="15px"
              fontWeight={activeCategory === category.categoryName ? 'bold' : '500'}
              borderRadius="8px"
              color={activeCategory === category.categoryName ? '#7D4DFF' : '#606266'}
              onClick={() => onCategoryClick(category.categoryName)}
              bg={activeCategory === category.categoryName ? '#fff' : 'transparent'}
            >
              <Text>{category.categoryName}</Text>
            </Box>
          ))
        ) : (
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            color="#a9a9ac"
          >
            暂无数据
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default Sidebar;
