import { Box, Text, Button, Avatar, Tooltip, <PERSON>lex, Grid, Divider } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { UserThirdPartyAccountResponseType } from '@/types/api/aiPlatform';
import { HasPersonalAccountEnum } from '@/constants/api/aiPlatform';
import { respDims } from '@/utils/chakra';
import { useMemo } from 'react';
import { MessageBox } from '@/utils/ui/messageBox';

const AICard = ({
  platformName,
  intro,
  link,
  logo,
  videoUrl,
  accounts,
  hasPersonalAccount,
  instructions
}: UserThirdPartyAccountResponseType) => {
  const router = useRouter();

  // 使用 memoization 来减少不必要的重复渲染
  const accountTooltipContent = useMemo(() => {
    return accounts
      .map((account) => `账号: ${account.account}, 密码: ${account.password}`)
      .join('\n');
  }, [accounts]);

  const onRedirect = (id: string) => {
    MessageBox.confirm({
      title: '提示',
      content: '将跳转到外部环境，是否确认继续访问',
      okText: '继续访问',
      onOk: () => {
        window.open(link, '_blank');
      }
    });
  };

  return (
    <Box
      bg="white"
      p={respDims(20)}
      borderRadius="16px"
      shadow="sm"
      boxShadow="0px 0px 12px 0px rgba(105,105,105,0.07)"
      transition="all 0.3s ease-in-out"
      _hover={{
        transform: 'translateY(-5px)',
        boxShadow: '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)'
      }}
      overflow="hidden"
    >
      <Grid gridTemplateColumns={['repeat(1,2fr)']} gridGap={respDims(20)}>
        <Flex alignItems="center" mb={respDims(16)} onClick={() => window.open(link, '_blank')}>
          <Avatar
            src={logo}
            name={platformName}
            w={respDims(64)}
            h={respDims(64)}
            borderRadius="50%"
            mr="20px"
          />
          <Flex flexDirection="column">
            <Box
              color="#000000"
              fontSize={respDims(18)}
              fontWeight="bold"
              mb="8px"
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitBoxOrient: 'vertical',
                WebkitLineClamp: '1',
                whiteSpace: 'normal',
                wordBreak: 'break-word',
                wordWrap: 'break-word'
              }}
            >
              {platformName}
            </Box>
            <Tooltip bg="#333" p="10px" label={intro} aria-label="space-names-tooltip">
              <Flex
                color="#606266"
                fontSize={respDims(15)}
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  WebkitLineClamp: '1',
                  whiteSpace: 'normal',
                  wordBreak: 'break-word',
                  wordWrap: 'break-word'
                }}
                _hover={{ cursor: 'pointer' }}
              >
                {intro}
              </Flex>
            </Tooltip>
          </Flex>
        </Flex>
      </Grid>

      {hasPersonalAccount === HasPersonalAccountEnum.Yes &&
        (accounts.length > 1 ? (
          <Tooltip label={accountTooltipContent} placement="bottom-start" hasArrow>
            <Flex
              flexDirection="row"
              justifyContent="space-around"
              mb={4}
              fontSize="sm"
              color="gray.500"
              key={accounts[0].account}
            >
              <Text ml="-28px">账号: {accounts[0].account}</Text>
              <Text>密码: {accounts[0].password}</Text>
            </Flex>
          </Tooltip>
        ) : (
          accounts.map((account) => (
            <Flex
              flexDirection="row"
              justifyContent="space-around"
              mb={hasPersonalAccount === HasPersonalAccountEnum.Yes ? '12px' : 0}
              pl="8px"
              fontSize="sm"
              color="gray.500"
              key={account.account}
            >
              <Text ml="-28px">账号: {account.account}</Text>
              <Text>密码: {account.password}</Text>
            </Flex>
          ))
        ))}
      <Divider orientation="horizontal" />

      <Flex alignItems="center" justifyContent="flex-end" gap="10px" mt="12px">
        {instructions && (
          <Button
            size="sm"
            variant="outline"
            colorScheme="gray"
            onClick={() =>
              router.push({
                pathname: '/aiPlatform/guide',
                query: {
                  platformName,
                  intro,
                  link,
                  logo,
                  videoUrl,
                  instructions,
                  accounts: JSON.stringify(
                    accounts.map((account) => ({
                      account: account.account,
                      password: account.password
                    }))
                  ),
                  hasPersonalAccount
                }
              })
            }
          >
            使用指南
          </Button>
        )}
        {videoUrl && (
          <Button
            size="sm"
            variant="outline"
            colorScheme="gray"
            onClick={() => window.open(videoUrl, '_blank')}
          >
            视频课程
          </Button>
        )}
        <Button
          border="1px solid var(--purple-500, #7D4DFF)"
          color="#7D4DFF"
          size="sm"
          onClick={() => onRedirect(link)}
          bgColor="#fff"
          _hover={{ color: '#fff', bgColor: '#7D4DFF' }}
        >
          立即访问
        </Button>
      </Flex>
    </Box>
  );
};

export default AICard;
