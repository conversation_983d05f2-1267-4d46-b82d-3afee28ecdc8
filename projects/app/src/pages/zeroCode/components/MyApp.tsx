import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Flex,
  Button,
  Image,
  MenuButton,
  Center,
  Tooltip,
  Input,
  InputGroup,
  InputLeftElement
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useAppStore } from '@/store/useAppStore';
import { deleteClientApp } from '@/api/app';
import { ZeroCodeProps } from '..';
import SvgIcon from '@/components/SvgIcon';
import MyMenu from '@/components/MyMenu';
import { APP_ICON, DataSource } from '@/constants/common';
import { AppListItemType } from '@/types/api/app';
import { Toast } from '@/utils/ui/toast';
import { MessageBox } from '@/utils/ui/messageBox';
import { ModeTypeEnum } from '@/constants/api/app';
import { getSampleAppInfo } from '@/utils/app';
import AdvancedModal from '@/components/AppModal/advanced';
import SimpleModal from '@/components/AppModal/simple';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { respDims } from '@/utils/chakra';
import ZeroSpaceTour from '@/components/CustomTour/ZeroSpaceTour';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';

const MyApp = ({ currentTab, TabRender }: ZeroCodeProps) => {
  const router = useRouter();
  const { myApps, loadMyApps } = useAppStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const { openOverlay } = useOverlayManager();
  const [searchTerm, setSearchTerm] = useState('');
  useEffect(() => {
    loadMyApps(true);
  }, []);

  const onAddAdvanced = () => {
    openOverlay({
      Overlay: AdvancedModal,
      props: {
        isCopy: false,
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onEditAdvanced = (app: AppListItemType) => {
    openOverlay({
      Overlay: AdvancedModal,
      props: {
        isCopy: false,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onAddSimple = () => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: false,
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onEditSimple = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: false,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const deleteApp = async (app: AppListItemType) => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      MessageBox.confirm({
        title: '删除',
        content: '确认删除该应用所有信息？',
        onOk: async () => {
          try {
            await deleteClientApp({ id: app.id, tmbId: app.tmbId });
            Toast.success('删除成功');
            loadMyApps(true);
          } catch (err: any) {
            Toast.error(err?.message || '删除失败');
          } finally {
            setIsProcessing(false);
          }
        },
        onCancel: () => setIsProcessing(false)
      });
    } catch (error) {
      Toast.error('操作失败');
      setIsProcessing(false);
    }
  };

  const onCopySimple = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: true,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => {
          loadMyApps(true);
        }
      }
    });
  };

  const onCopyAdvanced = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: true,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => {
          loadMyApps(true);
        }
      }
    });
  };

  const filteredApps = myApps.filter(
    (app) =>
      app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.intro.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Flex w="100%" h="100%" flexDir="column" overflow="hidden">
      <Flex
        w="100%"
        pb={respDims(4, GLOBAL_DIMS_MIN_SCALE)}
        alignItems="center"
        justifyContent="space-between"
        borderBottom="1px solid #E0D5FF"
      >
        <TabRender />
        <InputGroup
          w={respDims(300, GLOBAL_DIMS_MIN_SCALE)}
          mr={respDims(1, GLOBAL_DIMS_MIN_SCALE)}
        >
          <InputLeftElement alignItems="center">
            <SvgIcon
              name="search"
              w={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
              h={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
              color="#4E5969"
            />
          </InputLeftElement>
          <Input
            placeholder="输入名称或者简介搜索"
            border="none"
            bg="rgba(0, 0, 0, 0.04)"
            _placeholder={{
              fontSize: respDims(14, GLOBAL_DIMS_MIN_SCALE),
              color: '#606266'
            }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            width={respDims(300, GLOBAL_DIMS_MIN_SCALE)}
          />
        </InputGroup>
      </Flex>

      <Box
        p={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
        px={respDims(0, GLOBAL_DIMS_MIN_SCALE)}
        mt={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
        h="100%"
        overflow="auto"
        flex="1"
      >
        <Grid
          gridTemplateColumns={['repeat(1,1fr)', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
          gap={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
        >
          <MyMenu
            isFullHeight={true}
            trigger="hover"
            offset={[0, 12]}
            menuListStyles={{
              width: respDims(443, GLOBAL_DIMS_MIN_SCALE)
            }}
            Button={
              <Flex
                p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                h={'100%'}
                minH={respDims(150, GLOBAL_DIMS_MIN_SCALE)}
                flexDir="column"
                color="primary.500"
                position="relative"
                cursor="pointer"
                userSelect="none"
                borderRadius={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                bgColor="#fff"
                border="1px dashed"
                borderColor="primary.500"
                transition="all 0.3s ease-in-out"
                _hover={{
                  transform: 'translateY(-5px)',
                  zIndex: '9',
                  boxShadow:
                    '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                  '& .app-menu': {
                    display: 'flex'
                  }
                }}
              >
                <Flex flex="1" justifyContent="center" alignItems="center">
                  <SvgIcon
                    name="plus"
                    w={respDims(28, GLOBAL_DIMS_MIN_SCALE)}
                    h={respDims(28, GLOBAL_DIMS_MIN_SCALE)}
                    mr={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
                  />
                  <Box
                    fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                    color="primary.500"
                    fontWeight="600"
                  >
                    创建应用
                  </Box>
                </Flex>
              </Flex>
            }
            menuList={[
              {
                label: (
                  <Flex flexDir="column" w="100%">
                    <Box
                      fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                      color="#303133"
                      fontWeight="400"
                      w="100%"
                    >
                      简易应用
                    </Box>
                    <Box
                      fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                      color="#909399"
                      fontWeight="400"
                      w="100%"
                    >
                      通过填表单形式,创建简单的AI应用,适合新手
                    </Box>
                  </Flex>
                ),
                icon: (
                  <SvgIcon
                    name="appSimpleAgent"
                    w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                    h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                  />
                ),
                onClick: () => onAddSimple()
              },
              {
                label: (
                  <Flex flexDir="column" w="100%">
                    <Box
                      fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                      color="#303133"
                      fontWeight="400"
                    >
                      高阶应用
                    </Box>
                    <Box
                      fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                      color="#909399"
                      fontWeight="400"
                    >
                      <Box>通过可视化编程的方式,构建逻辑复杂的多轮</Box>
                      <Box>对话AI应用,推荐高级玩家使用</Box>
                    </Box>
                  </Flex>
                ),
                icon: (
                  <SvgIcon
                    name="appAdvancedAgent"
                    w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                    h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                  />
                ),
                onClick: () => onAddAdvanced()
              }
            ]}
          />

          {filteredApps
            .filter(({ source }) => source === DataSource.Personal)
            .map((app) => (
              <Flex
                key={app.id}
                p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                flexDir="column"
                position="relative"
                cursor="pointer"
                userSelect="none"
                borderRadius={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                bgColor="#ffffff"
                transition="all 0.3s ease-in-out"
                _hover={{
                  transform: 'translateY(-5px)',
                  zIndex: '9',
                  boxShadow:
                    '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                  '& .app-menu': {
                    display: 'flex'
                  }
                }}
                onClick={() => {
                  router.push({
                    pathname: '/app/detail',
                    query: {
                      appType: app.type,
                      finalAppId: app.finalAppId,
                      isAdmin: '0',
                      appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(app)))
                    }
                  });
                }}
              >
                <Flex justifyContent="space-between">
                  <Flex alignItems="center">
                    <Image
                      w={respDims(64, GLOBAL_DIMS_MIN_SCALE)}
                      h={respDims(64, GLOBAL_DIMS_MIN_SCALE)}
                      src={app.avatarUrl || APP_ICON}
                      alt=""
                      borderRadius="50%"
                    />
                    <Flex flex="1" flexDir="column" ml={respDims(16, GLOBAL_DIMS_MIN_SCALE)}>
                      <Box
                        flex="0 0 auto"
                        color="#000000"
                        wordBreak="break-all"
                        fontSize={respDims(18, GLOBAL_DIMS_MIN_SCALE)}
                        fontWeight="bold"
                      >
                        {app.name}
                      </Box>
                      <Flex alignItems="center" mt={respDims(12, GLOBAL_DIMS_MIN_SCALE)}>
                        <SvgIcon
                          color="#c1c1c3"
                          mr={respDims(6, GLOBAL_DIMS_MIN_SCALE)}
                          name="user"
                          w={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                          h={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                        />
                        <Box
                          ml={respDims(2, GLOBAL_DIMS_MIN_SCALE)}
                          fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                          color="#909399"
                          fontWeight="400"
                        >
                          个人
                        </Box>
                      </Flex>
                    </Flex>
                  </Flex>

                  <MyMenu
                    trigger="hover"
                    offset={[20, 0]}
                    width={20}
                    Button={
                      <MenuButton
                        className="app-menu"
                        display="none"
                        position="absolute"
                        top="0"
                        right="0"
                        w={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                        h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                        _hover={{
                          bg: 'myWhite.600',
                          borderRadius: respDims(2, GLOBAL_DIMS_MIN_SCALE)
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Center>
                          <SvgIcon
                            name="more"
                            w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                            h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                          />
                        </Center>
                      </MenuButton>
                    }
                    menuList={[
                      {
                        label: '立即对话',
                        icon: (
                          <SvgIcon
                            name="chat"
                            w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                            h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                          />
                        ),
                        onClick: () => {
                          router.push({
                            pathname: '/home',
                            query: {
                              appId: app.id,
                              sceneId: app.sceneId
                            }
                          });
                        }
                      },
                      {
                        label: app.mode === ModeTypeEnum.simple ? '简易配置' : '高级编排',
                        icon: (
                          <SvgIcon
                            name="appArrange"
                            w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                            h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                          />
                        ),
                        onClick: () => {
                          router.push({
                            pathname: '/app/detail',
                            query: {
                              appType: app.type,
                              finalAppId: app.finalAppId,
                              isAdmin: '0',
                              appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(app)))
                            }
                          });
                        }
                      },
                      {
                        label: '复制',
                        icon: (
                          <SvgIcon
                            name="copy"
                            w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                            h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                          />
                        ),
                        onClick: () => (app.mode === 1 ? onCopySimple(app) : onCopyAdvanced(app))
                      },
                      {
                        label: '编辑',
                        icon: (
                          <SvgIcon
                            name="edit"
                            w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                            h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                          />
                        ),
                        onClick: () => {
                          if (app.mode === ModeTypeEnum.simple) {
                            onEditSimple(app);
                          } else {
                            onEditAdvanced(app);
                          }
                        }
                      },
                      {
                        label: '删除',
                        icon: (
                          <SvgIcon
                            name="trash"
                            w={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                            h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                          />
                        ),
                        onClick: () => deleteApp(app)
                      }
                    ]}
                  />
                </Flex>

                <Tooltip bg="#333" p="10px" label={app.intro} aria-label="app-intro-tooltip">
                  <Box
                    mt="13px"
                    color="#606266"
                    fontSize="15px"
                    minH="38px"
                    style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitBoxOrient: 'vertical',
                      WebkitLineClamp: '2',
                      whiteSpace: 'normal',
                      wordBreak: 'break-word',
                      wordWrap: 'break-word'
                    }}
                  >
                    {app.intro || '暂无介绍'}
                  </Box>
                </Tooltip>
              </Flex>
            ))}
        </Grid>
      </Box>
    </Flex>
  );
};

export default MyApp;
