import React, { useEffect, useMemo, useState } from 'react';
import {
  Box,
  Flex,
  Grid,
  Button,
  Image,
  Input,
  InputGroup,
  InputRightElement,
  InputLeftElement
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import Avatar from '@/components/Avatar';
import MyIcon from '@/components/LegacyIcon';
import MyMenu from '@/components/MyMenu';
import { useConfirm } from '@/hooks/useConfirm';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';
import DatasetTypeTag from '@/pages/dataset/component/DatasetTypeTag';
import { getDatasetsPage, putDatasetById, delDatasetById } from '@/api/tenant/dataset';
import { DatasetItemType, UpdateDatasetProps } from '@/types/api/tenant/dataset';
import { respDims, rpxDim } from '@/utils/chakra';
import { CollaborationTypeEnum, DatasetTypeEnum, DatasetTypeMap } from '@/constants/api/dataset';
import { PermissionTypeEnum } from '@/constants/permission';
import { useToast } from '@/hooks/useToast';
import { DataSource } from '@/constants/common';
import { ZeroCodeProps } from '..';
import CreateModal from './CreateModal';
import { Pagination } from 'antd';
import { CreateDatasetProps } from '@/types/api/dataset';
import ZeroSpaceTour from '@/components/CustomTour/ZeroSpaceTour';
import KnowledgePermissionModal from '@/pages/dataset/list/component/CollaborateManage';
import { useDatasetStore } from '@/store/useDatasetStore';

const Dataset = ({ currentTab, TabRender }: ZeroCodeProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { openConfirm, ConfirmModal } = useConfirm({ type: 'delete' });
  const { openOverlay } = useOverlayManager();
  const [createDataset, setCreateDataset] = useState<CreateDatasetProps>();
  const [filterType, setFilterType] = useState<PermissionTypeEnum>();
  const [current, setCurrent] = useState(1);
  const [size, setSize] = useState(999);
  const { toast } = useToast();
  const { parentId, finalParentId } = router.query as {
    parentId: string;
    finalParentId: string;
  };
  const deleteTipsMap: Record<DatasetTypeEnum, string> = {
    [DatasetTypeEnum.Dataset]: '确认删除该知识库？删除后数据无法恢复，请确认！',
    [DatasetTypeEnum.WebsiteDataset]: '确认删除该知识库？删除后数据无法恢复，请确认！'
  };
  const { myDatasets, loadDatasets, updateDataset, delDatasetById } = useDatasetStore();
  const [searchTerm, setSearchTerm] = useState('');

  const { data, refetch, isFetching } = useQuery(['loadDataset', parentId], () => {
    return Promise.all([loadDatasets(parentId)]);
  });

  useEffect(() => {
    if (data) {
      if (createDataset) {
        const index = data[0].findIndex(
          (item: DatasetItemType) => item.name === createDataset.name
        );
        if (index !== -1) {
          router.push({
            pathname: '/dataset/detail',
            query: {
              finalDatasetId: data[0][index].finalDatasetId
            }
          });
        }
        setCreateDataset(undefined);
      }
    }
  }, [data]);

  const presentDatasets = useMemo(() => {
    const datasets =
      filterType === undefined
        ? myDatasets
        : myDatasets.filter((it) => it.permission === filterType);
    return datasets
      .filter(
        (item) =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.intro.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .map((item) => {
        return {
          ...item,
          label: DatasetTypeMap[item.type]?.label,
          icon: DatasetTypeMap[item.type]?.icon
        };
      });
  }, [filterType, myDatasets, searchTerm]);

  const updateDatasetMutation = useMutation(putDatasetById, {
    onSuccess: () => {
      queryClient.invalidateQueries(['loadDataset', current, size]);
    }
  });

  const deleteDatasetMutation = useMutation(delDatasetById, {
    onSuccess: () => {
      toast({
        title: '删除成功',
        status: 'success'
      });
      queryClient.invalidateQueries(['loadDataset', current, size]);
    }
  });

  const onEditDatasetModal = (dataset: DatasetItemType) => {
    openOverlay({
      Overlay: CreateModal,
      props: {
        type: 'edit',
        dataset,
        onSuccess: () => refetch()
      }
    });
  };

  const onCreateDatasetModal = () => {
    openOverlay({
      Overlay: CreateModal,
      props: {
        type: 'add',
        onSuccess: (values) => {
          setCreateDataset(values);
          refetch();
        }
      }
    });
  };

  const onOpenCollaborateManage = (dataset: DatasetItemType) => {
    openOverlay({
      Overlay: KnowledgePermissionModal,
      props: {
        dataset,
        onSuccess: () => refetch()
      }
    });
  };

  return (
    <Flex w="100%" h="100%" flexDir="column" overflow="hidden">
      <Flex
        w="100%"
        pb="4px"
        alignItems="center"
        justifyContent="space-between"
        borderBottom="1px solid #E0D5FF"
      >
        <TabRender />
        <InputGroup w="300px" mr={'1px'}>
          <InputLeftElement alignItems="center">
            <SvgIcon name="search" w={respDims('20fpx')} h={respDims('20fpx')} color="#4E5969" />
          </InputLeftElement>
          <Input
            placeholder="输入名称或者简介搜索"
            border="none"
            bg="rgba(0, 0, 0, 0.04)"
            _placeholder={{
              fontSize: '14px',
              color: '#606266'
            }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            width="300px"
          />
        </InputGroup>
      </Flex>
      <Box flex="1" overflow="auto" p="8px" px="0px">
        <Grid
          py={5}
          gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
          gridGap={5}
          userSelect={'none'}
        >
          <Flex
            id="zero_code_space3"
            display={'flex'}
            flexDirection={'row'}
            py={3}
            px={5}
            cursor={'pointer'}
            borderWidth={1.5}
            borderRadius={'14px'}
            bgColor="#fff"
            border="1px dashed"
            borderColor="primary.500"
            minH={'130px'}
            position={'relative'}
            transition="all 0.3s ease-in-out"
            _hover={{
              transform: 'translateY(-5px)',
              zIndex: '9',
              boxShadow: '1.5'
            }}
            onClick={() => {
              openOverlay({
                Overlay: CreateModal,
                props: {
                  type: 'add',
                  onSuccess: () => refetch()
                }
              });
            }}
          >
            <Flex flex="1" justifyContent="center" alignItems="center">
              <SvgIcon name="plus" w="28px" h="28px" mr="8px" color="primary.500" />
              <Box fontSize="16px" color="primary.500" fontWeight="600">
                创建知识库
              </Box>
            </Flex>
          </Flex>

          {presentDatasets.map((dataset) => (
            <Flex
              key={dataset.id}
              minH={respDims(100, 100)}
              p="20px"
              position="relative"
              cursor="pointer"
              userSelect="none"
              borderRadius="14px"
              bgColor="#ffffff"
              flexDirection="column"
              transition="all 0.3s ease-in-out"
              _hover={{
                transform: 'translateY(-5px)',
                zIndex: '9',
                boxShadow:
                  '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                '& .app-menu': {
                  display: 'flex'
                },
                '& .icon': {
                  display: 'block'
                }
              }}
              draggable
              onClick={() => {
                router.push({
                  pathname: '/dataset/detail',
                  query: {
                    finalDatasetId: dataset.finalDatasetId
                  }
                });
              }}
            >
              <Flex justifyContent="space-between" alignItems="center">
                <Avatar src={dataset.avatarUrl} borderRadius={'md'} w={'50px'} h={'50px'} mr={4} />
                <Box flex={1} display={'flex'} flexDirection={'column'}>
                  <Flex justifyContent={'space-between'} alignItems={'center'}>
                    <Box className="textEllipsis3" fontWeight={'bold'} fontSize={'lg'}>
                      {dataset.name}
                    </Box>
                    <Box
                      borderRadius={'md'}
                      _hover={{
                        color: 'primary.500',
                        '& .icon': {
                          bg: 'myGray.100'
                        }
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    >
                      <MyMenu
                        width={100}
                        Button={
                          <Box w={'22px'} h={'22px'}>
                            <MyIcon
                              className="icon"
                              name={'more'}
                              h={'16px'}
                              w={'16px'}
                              px={1}
                              py={1}
                              borderRadius={'md'}
                              display={'none'}
                              cursor={'pointer'}
                            />
                          </Box>
                        }
                        menuList={[
                          {
                            label: '编辑信息',
                            onClick: () => onEditDatasetModal(dataset),
                            icon: <SvgIcon name="edit" w={respDims(20)} h={respDims(20)} />
                          },
                          {
                            label: '权限管理',
                            onClick: () => onOpenCollaborateManage(dataset),
                            icon: <SvgIcon name="datasetGroup" w={respDims(20)} h={respDims(20)} />
                          },
                          {
                            label: t('common.Delete'),
                            onClick: () => {
                              openConfirm(
                                () => deleteDatasetMutation.mutate(dataset.id),
                                undefined,
                                deleteTipsMap[dataset.type]
                              )();
                            },
                            icon: <SvgIcon name="trash" w={respDims(20)} h={respDims(20)} />
                          }
                        ]}
                      />
                    </Box>
                  </Flex>
                </Box>
              </Flex>
              <Box
                className={'textEllipsis2'}
                pt={respDims(16)}
                wordBreak={'break-all'}
                fontSize={respDims(14, 12)}
                color={'myGray.500'}
              >
                {dataset.intro || t('core.dataset.Intro Placeholder')}
              </Box>
            </Flex>
          ))}
        </Grid>

        {myDatasets.length === 0 && (
          <Flex mt={'35vh'} flexDirection={'column'} alignItems={'center'}>
            <MyIcon name="empty" w={'48px'} h={'48px'} color={'transparent'} />
            <Box mt={2} color={'myGray.500'}>
              {t('core.dataset.Empty Dataset Tips')}
            </Box>
          </Flex>
        )}
      </Box>

      <ConfirmModal />
    </Flex>
  );
};

export default Dataset;
