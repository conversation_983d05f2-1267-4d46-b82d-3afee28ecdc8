import { serviceSideProps } from '@/utils/i18n';
import { AuthTypeEnum, PageTypeEnum } from '@/components/FastGPT/constants';
import FastGPTWrapper from '@/components/FastGPTWrapper';
import { getToken } from '@/utils/auth';
const DatasetDetail = ({ finalDatasetId, query }: { finalDatasetId: string; query: string }) => {
  const huayunToken = getToken();
  return (
    <FastGPTWrapper
      options={{
        pageType: PageTypeEnum.datasetDetail,
        authType: AuthTypeEnum.user,
        datasetId: finalDatasetId,
        query: query || '',
        huayunToken: huayunToken
      }}
    />
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      finalDatasetId: context.query?.finalDatasetId || '',
      query: context.query?.query || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default DatasetDetail;
