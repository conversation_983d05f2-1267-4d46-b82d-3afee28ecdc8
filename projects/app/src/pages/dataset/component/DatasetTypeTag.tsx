import { DatasetTypeEnum, DatasetTypeMap } from '@/constants/api/dataset';
import { Box, Flex, FlexProps } from '@chakra-ui/react';
import React from 'react';
import { respDims } from '@/utils/chakra';

const DatasetTypeTag = ({ type, ...props }: { type: `${DatasetTypeEnum}` } & FlexProps) => {
  const item = DatasetTypeMap[type];

  if (!item) {
    return null;
  }

  return (
    <Flex
      bgColor="#F8F8FA"
      borderRadius={respDims(8)}
      fontSize={respDims('13fpx')}
      lineHeight={respDims('20fpx')}
      color="#909399"
      py={respDims(2)}
      px={respDims(18)}
      border="1px solid #ECECEC"
      {...props}
    >
      <Box>{item.label}</Box>
    </Flex>
  );
};

export default DatasetTypeTag;
