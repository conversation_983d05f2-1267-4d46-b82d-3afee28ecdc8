import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Box,
  Flex,
  Grid,
  useDisclosure,
  Image,
  Button,
  Center,
  MenuButton
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useDatasetStore } from '@/store/useDatasetStore';
import { useConfirm } from '@/hooks/useConfirm';
import { AddIcon } from '@chakra-ui/icons';
import { useQuery } from '@tanstack/react-query';
import { putDatasetById, postCreateDataset, getDatasetPaths } from '@/api/dataset';
import { useTranslation } from 'next-i18next';
import Avatar from '@/components/Avatar';
import MyIcon from '@/components/LegacyIcon';
import { serviceSideProps } from '@/utils/i18n';
import dynamic from 'next/dynamic';
import MyMenu from '@/components/MyMenu';
import { useRequest } from '@/hooks/useRequest';
import { useSystemStore } from '@/store/useSystemStore';
import { useDrag } from '@/hooks/useDrag';
import DatasetTypeTag from '../component/DatasetTypeTag';

import PermissionIconText from '@/components/support/permission/IconText';
import ParentPaths from '@/components/common/ParentPaths';
import { respDims } from '@/utils/chakra';
import { CollaborationTypeEnum, DatasetTypeEnum, DatasetTypeMap } from '@/constants/api/dataset';
import { PermissionTypeEnum } from '@/constants/permission';
import EditFolderModal, { useEditFolder } from '../component/EditFolderModal';

import { useUserStore } from '@/store/useUserStore';
import { DatasetItemType } from '@/types/api/dataset';
import CollaborateManage from './component/CollaborateManage';
import CreateModal from './component/CreateModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';
import MyTooltip from '@/components/MyTooltip';

const DatasetList = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { userInfo } = useUserStore();
  const { parentId, finalParentId } = router.query as {
    parentId: string;
    finalParentId: string;
  };
  const { setLoading } = useSystemStore();

  const deleteTipsMap: Record<DatasetTypeEnum, string> = {
    [DatasetTypeEnum.Dataset]: '确认删除该知识库？删除后数据无法恢复，请确认！',
    [DatasetTypeEnum.WebsiteDataset]: '确认删除该知识库？删除后数据无法恢复，请确认！'
  };

  const { openConfirm, ConfirmModal } = useConfirm({
    type: 'delete'
  });

  const { myDatasets, loadDatasets, updateDataset, delDatasetById } = useDatasetStore();
  const [filterType, setFilterType] = useState<PermissionTypeEnum>();

  const { moveDataId, setMoveDataId, dragStartId, setDragStartId, dragTargetId, setDragTargetId } =
    useDrag();
  const { openOverlay } = useOverlayManager();

  /* 点击删除 */
  const { mutate: onclickDelDataset } = useRequest({
    mutationFn: async (id: string) => {
      setLoading(true);
      await delDatasetById(id);
      return id;
    },
    onSettled() {
      setLoading(false);
    },
    successToast: t('common.Delete Success')
  });

  const { data, refetch, isFetching } = useQuery(['loadDataset', parentId], () => {
    return Promise.all([loadDatasets(parentId)]);
  });

  const presentDatasets = useMemo(() => {
    const datasets =
      filterType === undefined
        ? myDatasets
        : myDatasets.filter((it) => it.permission === filterType);
    return datasets.map((item) => {
      return {
        ...item,
        label: DatasetTypeMap[item.type]?.label,
        icon: DatasetTypeMap[item.type]?.icon
      };
    });
  }, [filterType, myDatasets]);

  const onOpenCollaborateManage = (dataset: DatasetItemType) => {
    openOverlay({
      Overlay: CollaborateManage,
      props: {
        dataset,
        onSuccess: () => refetch()
      }
    });
  };

  const onEditDatasetModal = (dataset: DatasetItemType) => {
    openOverlay({
      Overlay: CreateModal,
      props: {
        type: 'edit',
        dataset,
        onSuccess: () => refetch()
      }
    });
  };

  const onCreateDatasetModal = () => {
    openOverlay({
      Overlay: CreateModal,
      props: {
        type: 'add',
        parentId,
        onSuccess: () => refetch()
      }
    });
  };

  return (
    <Flex flexDir="column" h="100%" p={respDims(46)}>
      <Flex alignItems={'center'} justifyContent={'space-between'}>
        {/* url path */}
        <Box fontSize={respDims(24)} fontWeight={500} color="#030712">
          欢迎来到，知识库
        </Box>
        <Button onClick={onCreateDatasetModal}>
          <SvgIcon name="plus" mr={respDims(4)}></SvgIcon>
          {t('创建知识库')}
        </Button>
      </Flex>

      <Box
        flex="1"
        overflow="auto"
        mt={respDims(26)}
        p={respDims(8)}
        mx={respDims(-8)}
        mb={respDims(-8)}
      >
        <Grid
          gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
          gridGap={respDims(16)}
          userSelect={'none'}
        >
          {presentDatasets.map((dataset) => (
            <Box
              display={'flex'}
              flexDirection={'row'}
              key={dataset.id}
              py={3}
              px={5}
              cursor={'pointer'}
              bg={'white'}
              borderRadius={respDims(14)}
              minH={'105px'}
              position={'relative'}
              onDragStart={(e) => {
                setDragStartId(dataset.id);
              }}
              onDragOver={(e) => {
                e.preventDefault();
                const targetId = e.currentTarget.getAttribute('data-drag-id');
                if (!targetId) return;
                setDragTargetId(targetId);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                setDragTargetId(undefined);
              }}
              onDrop={async (e) => {
                e.preventDefault();
                if (!dragTargetId || !dragStartId || dragTargetId === dragStartId) return;
                // update parentId
                try {
                  await putDatasetById({
                    id: dragStartId,
                    parentId: dragTargetId
                  });
                  refetch();
                } catch (error) {}
                setDragTargetId(undefined);
              }}
              boxShadow="0px 0px 9px 0px rgba(105,105,105,0.07)"
              _hover={{
                boxShadow:
                  '0px 2px 4.3px 0px rgba(75, 86, 115, 0.07), 0px 0px 15.1px 0px rgba(92, 92, 92, 0.09)',
                '& .delete': {
                  display: 'block'
                },
                '& .app-menu': {
                  display: 'flex'
                }
              }}
              onClick={() => {
                router.push({
                  pathname: '/dataset/detail',
                  query: {
                    finalDatasetId: dataset.finalDatasetId
                  }
                });
              }}
            >
              <Avatar src={dataset.avatarUrl} borderRadius={'md'} w={'50px'} h={'50px'} mr={4} />
              <Box flex={1} display={'flex'} flexDirection={'column'}>
                <Flex justifyContent={'space-between'} alignItems={'center'}>
                  <Box className="textEllipsis3" fontWeight={'bold'} fontSize={'lg'}>
                    {dataset.name}
                  </Box>
                </Flex>
                <MyTooltip overflowOnly label={dataset.intro}>
                  <Box
                    flex={1}
                    className={'textEllipsis2'}
                    py={1}
                    wordBreak={'break-all'}
                    fontSize={respDims(14, 12)}
                    color={'myGray.500'}
                  >
                    {dataset.intro || t('core.dataset.Intro Placeholder')}
                  </Box>
                </MyTooltip>
                <Flex alignItems={'center'} fontSize={'sm'} mt={respDims(16)}>
                  <Box flex={1}>
                    {dataset.collaborationType == CollaborationTypeEnum.Collaboration && (
                      <SvgIcon
                        name="datasetGroup"
                        color="#909399"
                        w={respDims(20, 16)}
                        h={respDims(20, 16)}
                      ></SvgIcon>
                    )}
                  </Box>
                  <DatasetTypeTag type={dataset.type} />
                </Flex>
              </Box>
              <MyMenu
                width={120}
                trigger="hover"
                Button={
                  <MenuButton
                    className="app-menu"
                    display="none"
                    position="absolute"
                    top="0"
                    right="0"
                    w={respDims(30)}
                    h={respDims(30)}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Center padding={respDims(6)} bg="#f2f5f7" borderRadius="2px">
                      <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                    </Center>
                  </MenuButton>
                }
                menuList={[
                  {
                    label: (
                      <Flex alignItems={'center'} justifyContent="center" color="#303133">
                        编辑信息
                      </Flex>
                    ),
                    onClick: () => onEditDatasetModal(dataset),
                    icon: (
                      <SvgIcon name={'edit'} w={respDims(20)} h={respDims(20)} color="#303133" />
                    )
                  },
                  {
                    label: (
                      <Flex alignItems={'center'} justifyContent="center" color="#303133">
                        权限管理
                      </Flex>
                    ),
                    onClick: () => onOpenCollaborateManage(dataset),
                    icon: (
                      <SvgIcon
                        name="datasetGroup"
                        w={respDims(20)}
                        h={respDims(20)}
                        color="#303133"
                      />
                    )
                  },
                  {
                    label: (
                      <Flex alignItems={'center'} justifyContent="center" color="#303133">
                        {t('common.Delete')}
                      </Flex>
                    ),
                    icon: (
                      <SvgIcon name="trash" w={respDims(20)} h={respDims(20)} color="#303133" />
                    ),

                    onClick: () => {
                      openConfirm(
                        () => onclickDelDataset(dataset.id),
                        undefined,
                        deleteTipsMap[dataset.type]
                      )();
                    }
                  }
                ]}
              />
            </Box>
          ))}
        </Grid>

        {myDatasets.length === 0 && (
          <Flex mt={'35vh'} flexDirection={'column'} alignItems={'center'}>
            <MyIcon name="empty" w={'48px'} h={'48px'} color={'transparent'} />
            <Box mt={2} color={'myGray.500'}>
              {t('core.dataset.Empty Dataset Tips')}
            </Box>
          </Flex>
        )}
      </Box>

      <ConfirmModal />
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default DatasetList;
