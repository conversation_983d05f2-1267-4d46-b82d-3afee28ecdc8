import { Flex, Tag } from '@chakra-ui/react';
import React, { useMemo } from 'react';
import { PermissionValueType } from '@/types/api/dataset';
import { PermissionList, PermissionKeyEnum } from '@/constants/api/dataset';

export type PermissionTagsProp = {
  permission: PermissionValueType;
};

// 模拟的 getPerLabelList 函数
const getPerLabelList = (permission: PermissionValueType) => {
  const labels = [];
  if (permission & PermissionList[PermissionKeyEnum.ReadWrite].value)
    labels.push(PermissionList[PermissionKeyEnum.ReadWrite].name);
  if (permission & PermissionList[PermissionKeyEnum.Manange].value)
    labels.push(PermissionList[PermissionKeyEnum.Manange].name);
  return labels;
};

function PermissionTags({ permission }: PermissionTagsProp) {
  const perTagList = useMemo(() => getPerLabelList(permission), [permission]);

  return (
    <Flex gap="2" alignItems="center">
      {perTagList.map((item) => (
        <Tag mixBlendMode={'multiply'} key={item} border="none" py={2} px={3} fontSize={'xs'}>
          {item}
        </Tag>
      ))}
    </Flex>
  );
}

export default PermissionTags;
