import {
  Flex,
  Box,
  ModalBody,
  InputGroup,
  InputLeftElement,
  Input,
  Checkbox,
  ModalFooter,
  Button,
  Avatar
} from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import PermissionSelect from './PermissionSelect';
import { createTenantDatasetUser, getTenantUserPage } from '@/api/dataset';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import MyBox from '@/components/common/MyBox';
import { useUserStore } from '@/store/useUserStore';
import SvgIcon from '@/components/SvgIcon';
import MyModal from '@/components/MyModal';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { DatasetItemType, PermissionValueType, TenantDatasetUsersVO } from '@/types/api/dataset';
import { PermissionKeyEnum, PermissionList } from '@/constants/api/dataset';
import { respDims } from '@/utils/chakra';

export type AddModalPropsType = {
  onClose: () => void;
  onSuccess: () => void;
  dataset: DatasetItemType;
  collaboratorList: TenantDatasetUsersVO[];
};

function AddMemberModal({ onClose, onSuccess, dataset, collaboratorList }: AddModalPropsType) {
  const { t } = useTranslation();
  const { userInfo } = useUserStore();

  const [searchText, setSearchText] = useState<string>('');
  const [selectedMemberIdList, setSelectedMembers] = useState<string[]>([]);
  const [selectedPermission, setSelectedPermission] = useState<PermissionKeyEnum>(
    PermissionKeyEnum.ReadWrite
  );

  const { data: members = [], isLoading: loadingMembers } = useQuery(
    ['tenantUsers', userInfo?.tenantId],
    async () => {
      const response = await getTenantUserPage({
        tenantId: userInfo?.tenantId,
        size: 9999
      });
      return response.records;
    },
    {
      enabled: !!userInfo?.tenantId
    }
  );

  const filterMembers = useMemo(() => {
    const collaboratorIds = collaboratorList.map((item) => item.tmbId);

    return members.filter(
      (item) =>
        !collaboratorIds?.some((tmbId) => tmbId == item.id) && item.username.includes(searchText)
    );
  }, [members, collaboratorList, searchText]);
  const perLabel = useMemo(() => {
    const labels: string[] = [];

    labels.push(PermissionList[selectedPermission].name);

    Object.values(PermissionList).forEach((item) => {
      if (item.checkBoxType === 'multiple') {
        if (selectedPermission == item.value) {
          labels.push(item.name);
        }
      }
    });

    return labels.join('、');
  }, [selectedPermission]);

  const { mutate: onConfirm, isLoading: isUpdating } = useRequest({
    mutationFn: () => {
      return createTenantDatasetUser({
        tenantDatasetId: dataset.id,
        tmbIds: selectedMemberIdList,
        authority: selectedPermission
      });
    },
    successToast: t('添加成功'),
    onSuccess() {
      onClose();
      onSuccess();
    }
  });

  return (
    <MyModal isOpen onClose={onClose} iconSrc="modal/AddClb" title="添加协作者" minW="800px">
      <ModalBody>
        <MyBox
          isLoading={loadingMembers}
          display={'grid'}
          minH="400px"
          maxH="500px"
          border="1px solid"
          borderColor="myGray.200"
          borderRadius="0.5rem"
          gridTemplateColumns="55% 45%"
          fontSize={'sm'}
        >
          <Flex
            flexDirection="column"
            borderRight="1px solid"
            borderColor="myGray.200"
            p="4"
            minH="200px"
            overflowY="auto"
          >
            <InputGroup alignItems="center" size="sm">
              <InputLeftElement>
                <SvgIcon name="search" w="16px" color={'myGray.500'} />
              </InputLeftElement>
              <Input
                placeholder="搜索用户名"
                bgColor="myGray.50"
                onChange={(e) => setSearchText(e.target.value)}
              />
            </InputGroup>
            <Flex flexDirection="column" mt="2" overflowY="auto" maxH="350px">
              {filterMembers.map((member) => {
                const onChange = () => {
                  if (selectedMemberIdList.includes(member.id)) {
                    setSelectedMembers(selectedMemberIdList.filter((v) => v !== member.id));
                  } else {
                    setSelectedMembers([...selectedMemberIdList, member.id]);
                  }
                };
                return (
                  <Flex
                    key={member.id}
                    mt="1"
                    py="1"
                    px="3"
                    borderRadius="sm"
                    alignItems="center"
                    _hover={{
                      bgColor: 'myGray.50',
                      cursor: 'pointer'
                    }}
                  >
                    <Checkbox
                      mr="3"
                      isChecked={selectedMemberIdList.includes(member.id)}
                      onChange={onChange}
                      variant={'primary'}
                    />
                    <Flex
                      flexDirection="row"
                      onClick={onChange}
                      w="full"
                      justifyContent="space-between"
                    >
                      <Flex flexDirection="row" alignItems="center">
                        <Avatar src={member.avatar} w={respDims(24)} h={respDims(24)} />
                        <Box ml={respDims(10)}>{member.username}</Box>
                      </Flex>
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <Flex p="4" flexDirection="column">
            <Box>已选: {selectedMemberIdList.length}</Box>
            <Flex flexDirection="column" mt="2">
              {selectedMemberIdList.map((id) => {
                const member = members.find((v) => v.id === id);
                return member ? (
                  <Flex
                    key={id}
                    alignItems="center"
                    justifyContent="space-between"
                    py="2"
                    px={3}
                    borderRadius={'md'}
                    _hover={{ bg: 'myGray.50' }}
                    _notLast={{ mb: 2 }}
                  >
                    <Avatar src={member.avatar} w={respDims(24)} h={respDims(24)} />
                    <Box w="full" ml={respDims(10)}>
                      {member.username}
                    </Box>
                    <SvgIcon
                      name="close"
                      w="16px"
                      cursor={'pointer'}
                      _hover={{
                        color: 'red.600'
                      }}
                      onClick={() =>
                        setSelectedMembers(selectedMemberIdList.filter((v) => v !== id))
                      }
                    />
                  </Flex>
                ) : null;
              })}
            </Flex>
          </Flex>
        </MyBox>
      </ModalBody>
      <ModalFooter>
        <PermissionSelect
          value={selectedPermission}
          Button={
            <Flex
              alignItems={'center'}
              bg={'myGray.50'}
              border="base"
              fontSize={'sm'}
              px={3}
              borderRadius={'md'}
              h={'32px'}
            >
              {perLabel}
              <ChevronDownIcon fontSize={'md'} />
            </Flex>
          }
          onChange={(v) => setSelectedPermission(v)}
        />
        <Button isLoading={isUpdating} ml="4" h={'32px'} onClick={onConfirm}>
          确认
        </Button>
      </ModalFooter>
    </MyModal>
  );
}

export default AddMemberModal;
