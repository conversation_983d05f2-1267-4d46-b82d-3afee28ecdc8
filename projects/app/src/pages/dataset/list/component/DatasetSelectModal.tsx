import React, { useMemo, useRef, useState } from 'react';
import {
  Card,
  Flex,
  Box,
  Button,
  ModalBody,
  ModalFooter,
  useTheme,
  Grid,
  Divider,
  Avatar,
  Text,
  HStack,
  VStack,
  Image
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { AppListItemType, SelectedDatasetType } from '@/types/api/app';
import { useToast } from '@/hooks/useToast';
import MyModal from '@/components/MyModal';
import SvgIcon from '@/components/SvgIcon';
import Loading from '@/components/Loading';
import DatasetTypeTag from '../../component/DatasetTypeTag';
import { respDims } from '@/utils/chakra';
import MyIcon from '@/components/LegacyIcon';
import { useQuery } from '@tanstack/react-query';
import { getDatasetsList } from '@/api/dataset';
import { update2Dataset } from '@/api/cloud';
import { DatasetSourceMap } from '@/constants/api/dataset';
import { DatasetItemType, TenantDatasetResp } from '@/types/api/dataset';
import { DataSource } from '@/constants/common';
import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import { UserRoleEnum } from '@/constants/api/auth';
import { useRequest } from '@/hooks/useRequest';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';
// 应用类型
type DatasetTabType = {
  name: string;
  value: DataSource;
};

export const DatasetSelectModal = ({
  defaultSelectedDatasets = [],
  onChange,
  onClose,
  resolve,
  reject,
  isImport = false,
  cloudFileIds = [],
  cloudFileId = null,
  onRefresh = () => {}
}: {
  defaultSelectedDatasets: { id: string }[];
  onChange: (e: DatasetItemType) => void;
  onClose: () => void;
  sourceKey?: DataSource;
  resolve?: (res: any) => void;
  reject?: (rej?: any) => void;
  isImport?: boolean;
  cloudFileIds?: number[];
  cloudFileId?: number | null;
  onRefresh: () => void;
}) => {
  const router = useRouter();
  const appDetail = useMemo(() => {
    try {
      return JSON.parse(decodeURIComponent(router.query.appDetail as string)) as AppListItemType;
    } catch (error) {
      return {} as AppListItemType;
    }
  }, [router.query]);
  const sourceKey = useMemo(() => Number(appDetail.source) as DataSource, [appDetail]);
  const tmbId = useMemo(() => (appDetail.tmbId as string) || undefined, [appDetail]);
  const { t } = useTranslation();
  const theme = useTheme();
  const { toast } = useToast();
  const { userInfo } = useUserStore();
  const isAdmin = isImport
    ? userInfo?.roleType === UserRoleEnum.Admin
    : useMemo(() => (router.query.isAdmin as string) === '1', [router.query]);

  let currentTabData: DataSource;
  if (isImport) {
    currentTabData = isAdmin ? DataSource.Tenant : DataSource.Personal;
  } else {
    currentTabData = !isAdmin ? DataSource.Personal : DataSource.Tenant;
  }
  const [currentTab, setCurrentTab] = useState<DataSource>(currentTabData);
  const selectedRef = useRef<HTMLDivElement>(null);
  const [curItemIndex, setCurItemIndex] = useState<Number>();
  const [curItemObj, setCurItemObj] = useState({ id: '' });
  const [isOpen, setIsOpen] = useState(true);

  const { mutate: onClickUpdate, isLoading: isUpdate2DatasetLoading } = useRequest({
    mutationFn: (data) => {
      return update2Dataset({
        tenantDatasetId: data.tenantDatasetId,
        cloudFileId: data.cloudFileId
      });
    },
    onSuccess(res) {
      window.open(`/dataset/detail?finalDatasetId=${res}`, '_blank');
      setIsOpen(false);
      // 重新调用列表接口,更新已导入知识库列数据
      onRefresh?.();
    }
  });

  const getTabs = (sourceKey: DataSource): DatasetTabType[] => {
    let tabs;
    if (isImport) {
      isAdmin
        ? (tabs = [
            {
              name: DatasetSourceMap[DataSource.Offical].tabName,
              value: DataSource.Offical,
              show: false
            },
            {
              name: DatasetSourceMap[DataSource.Tenant].tabName,
              value: DataSource.Tenant,
              show: true
            },
            {
              name: DatasetSourceMap[DataSource.Personal].tabName,
              value: DataSource.Personal,
              show: true
            }
          ])
        : (tabs = [
            {
              name: DatasetSourceMap[DataSource.Personal].tabName,
              value: DataSource.Personal,
              show: true
            },
            {
              name: DatasetSourceMap[DataSource.Tenant].tabName,
              value: DataSource.Tenant,
              show: false
            },
            {
              name: DatasetSourceMap[DataSource.Offical].tabName,
              value: DataSource.Offical,
              show: false
            }
          ]);
    } else {
      tabs = [
        {
          name: DatasetSourceMap[DataSource.Personal].tabName,
          value: DataSource.Personal,
          show: sourceKey === DataSource.Personal
        },
        {
          name: DatasetSourceMap[DataSource.Tenant].tabName,
          value: DataSource.Tenant,
          show: sourceKey === DataSource.Personal || sourceKey === DataSource.Tenant
        },
        {
          name: DatasetSourceMap[DataSource.Offical].tabName,
          value: DataSource.Offical,
          show: true
        }
      ];
    }
    return tabs.filter((item) => item.show);
  };

  const tabs = getTabs(sourceKey);

  const {
    data: allDatasets,
    isLoading,
    isError
  } = useQuery(
    ['tenantDatasetList', tabs],
    async () => {
      const requests = tabs.map((tab) =>
        getDatasetsList({ source: tab.value, tmbId }, sourceKey, isAdmin).then((res) => {
          res.forEach((item) => {
            item.source = tab.value;
          });
          return res;
        })
      );
      const results = await Promise.all(requests);
      return results.flat();
    },
    {
      onSuccess: (data) => {
        setSelectedDatasets(
          data?.filter((dataset) => {
            return defaultSelectedDatasets?.some(
              (selected) => selected.id === dataset.finalDatasetId
            );
          }) || []
        );
      }
    }
  );

  const [selectedDatasets, setSelectedDatasets] = useState<DatasetItemType[]>(
    allDatasets?.filter((dataset) => {
      return defaultSelectedDatasets?.some((selected) => selected.id === dataset.finalDatasetId);
    }) || []
  );

  const filterDatasets = useMemo(() => {
    const filteredByTab = allDatasets?.filter((item) => item.source === currentTab) || [];
    return {
      selected: filteredByTab.filter((item) =>
        selectedDatasets.find((dataset) => dataset.finalDatasetId === item.finalDatasetId)
      ),
      unSelected: filteredByTab.filter(
        (item) =>
          !selectedDatasets.find((dataset) => dataset.finalDatasetId === item.finalDatasetId)
      )
    };
  }, [allDatasets, selectedDatasets, currentTab]);

  return (
    <MyModal
      title="选择知识库"
      isOpen={isOpen}
      onClose={() => {
        reject && reject();
        onClose();
      }}
      minW={respDims(814, GLOBAL_DIMS_MIN_SCALE)}
      borderRadius="14px"
      bgImage="/imgs/v2/gradient_bg7.png"
      bgSize="100% auto"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent',
        borderBottom: '1px solid #E7E7E7'
      }}
    >
      {isLoading && <Loading fixed={false} />}

      <Flex
        flexDirection="column"
        position="relative"
        overflow="hidden"
        px={respDims(32, GLOBAL_DIMS_MIN_SCALE)}
      >
        {/* 顶部标签栏 */}
        <Flex
          mt={respDims(23, GLOBAL_DIMS_MIN_SCALE)}
          alignItems="center"
          gap={respDims(12, GLOBAL_DIMS_MIN_SCALE)}
        >
          {tabs.map((tab, index) => (
            <Box
              key={tab.value}
              h={respDims(36, GLOBAL_DIMS_MIN_SCALE)}
              px={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
              py={respDims(3, GLOBAL_DIMS_MIN_SCALE)}
              borderRadius="10px"
              display="inline-flex"
              alignItems="center"
              justifyContent="center"
              cursor="pointer"
              onClick={() => setCurrentTab(tab.value)}
              {...(tab.value === currentTab
                ? {
                    bgGradient: 'linear(90deg, rgba(220,126,255,1) 0%, rgba(96,28,255,1) 100%)',
                    color: 'white',
                    fontWeight: 'semibold'
                  }
                : {
                    bg: 'rgba(0,0,0,0.03)',
                    color: '#3E3E3E',
                    fontWeight: 'normal'
                  })}
            >
              <Text
                fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                lineHeight={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
                textAlign="center"
              >
                {tab.name}
              </Text>
            </Box>
          ))}
        </Flex>

        {/* 未选择的知识库列表 */}
        <Box
          mt={respDims(17, GLOBAL_DIMS_MIN_SCALE)}
          h={['200px', '200px', '300px']}
          overflowY="auto"
          overflowX="hidden"
        >
          <Grid templateColumns="repeat(2, 1fr)" gap={respDims(16, GLOBAL_DIMS_MIN_SCALE)}>
            {filterDatasets.unSelected.map((dataset, index) => (
              <Box
                key={dataset.finalDatasetId}
                w="100%"
                h={respDims(97, '.5ms')}
                bg="white"
                borderRadius="14px"
                border="1px solid"
                borderColor="#E7E7E7"
                position="relative"
                cursor="pointer"
                onClick={() => {
                  if (isImport) {
                    setCurItemObj(dataset);
                    setCurItemIndex(index);
                  } else {
                    const vectorModel = allDatasets?.find(
                      (dataset) => dataset.finalDatasetId === selectedDatasets[0]?.finalDatasetId
                    )?.vectorModel;

                    if (vectorModel && vectorModel !== dataset.vectorModel) {
                      return toast({
                        status: 'warning',
                        title: t('common:dataset.Select Dataset Tips')
                      });
                    }
                    setSelectedDatasets((state) => [...state, { ...dataset }]);
                    setTimeout(() => {
                      selectedRef.current?.scrollTo({
                        top: selectedRef.current.scrollHeight,
                        behavior: 'smooth'
                      });
                    }, 100);
                  }
                }}
              >
                <Flex
                  w="100%"
                  h={'100%'}
                  alignItems="center"
                  gap={respDims(16, '.5ms')}
                  justifyContent={'center'}
                  ml={respDims(20, '.5ms')}
                >
                  <Avatar
                    src={dataset.avatarUrl}
                    borderRadius="md"
                    w={respDims(50, GLOBAL_DIMS_MIN_SCALE)}
                    h={respDims(50, GLOBAL_DIMS_MIN_SCALE)}
                  />
                  <Flex flexDirection="column" w="100%" gap={respDims(10, GLOBAL_DIMS_MIN_SCALE)}>
                    <Box h={respDims(24, GLOBAL_DIMS_MIN_SCALE)} overflow="hidden">
                      <Text
                        fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                        fontWeight="medium"
                        lineHeight={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
                        noOfLines={1}
                      >
                        {dataset.name}
                      </Text>
                    </Box>
                    <Text
                      fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                      color="#606266"
                      lineHeight={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                      noOfLines={1}
                      w={respDims(200, GLOBAL_DIMS_MIN_SCALE)}
                    >
                      {dataset.intro || t('core.dataset.Intro Placeholder')}
                    </Text>
                  </Flex>
                </Flex>
              </Box>
            ))}
          </Grid>
        </Box>

        {/* 分隔线 */}
        <Box position="relative" mt={respDims(12, GLOBAL_DIMS_MIN_SCALE)}>
          <Divider borderColor="#E7E7E7" />
        </Box>

        {/* 已选择的知识库 */}
        <Box mt={respDims(9, GLOBAL_DIMS_MIN_SCALE)}>
          <Text
            fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            fontWeight="medium"
            color="black"
            lineHeight={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
          >
            已选择
          </Text>

          <Box
            mt={respDims(12, GLOBAL_DIMS_MIN_SCALE)}
            h={respDims(160, '.5ms')}
            w="100%"
            pb={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
            overflow="auto"
            overflowX="hidden"
          >
            <Grid templateColumns="repeat(3, 1fr)" gap={respDims(11, GLOBAL_DIMS_MIN_SCALE)}>
              {selectedDatasets.map((item) => (
                <Flex
                  key={item.finalDatasetId}
                  w="100%"
                  h={respDims(62, GLOBAL_DIMS_MIN_SCALE)}
                  bg="rgba(249, 250, 251, 1)"
                  borderRadius="14px"
                  border="1px solid"
                  borderColor="#E7E7E7"
                  alignItems="center"
                  px={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                  py={respDims(19, GLOBAL_DIMS_MIN_SCALE)}
                  gap={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
                >
                  <Avatar
                    src={item.avatarUrl}
                    w={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                    h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                    borderRadius="md"
                  />
                  <Text
                    flex="1"
                    fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                    fontWeight="medium"
                    noOfLines={1}
                    lineHeight={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
                  >
                    {item.name}
                  </Text>
                  <Box
                    cursor="pointer"
                    w="18px"
                    h="18px"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedDatasets((state) =>
                        state.filter((dataset) => dataset.finalDatasetId !== item.finalDatasetId)
                      );
                    }}
                  >
                    <SvgIcon name="trash" w="18px" h="18px" _hover={{ color: 'red.500' }} />
                  </Box>
                </Flex>
              ))}
            </Grid>
          </Box>
        </Box>

        {/* 底部按钮 */}
        <Flex
          height={respDims(50, GLOBAL_DIMS_MIN_SCALE)}
          gap={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
          justifyContent="flex-end"
          mb={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
        >
          <Button
            variant="grayBase"
            fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            px={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            py={respDims(7, GLOBAL_DIMS_MIN_SCALE)}
            fontWeight="normal"
            borderRadius="8px"
            onClick={() => {
              reject && reject();
              onClose();
            }}
          >
            取消
          </Button>
          <Button
            variant="primary"
            fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            px={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            py={respDims(7, GLOBAL_DIMS_MIN_SCALE)}
            fontWeight="normal"
            borderRadius="8px"
            isLoading={isUpdate2DatasetLoading}
            onClick={() => {
              if (isImport) {
                if (curItemObj?.id) {
                  if (cloudFileIds?.length) {
                    cloudFileIds.map((v) => {
                      onClickUpdate({
                        tenantDatasetId: curItemObj?.id,
                        cloudFileId: v
                      });
                    });
                  } else {
                    onClickUpdate({
                      tenantDatasetId: curItemObj?.id,
                      cloudFileId: cloudFileId
                    });
                  }
                } else {
                  return toast({
                    status: 'warning',
                    title: t('请选择一个知识库')
                  });
                }
              } else {
                // filter out the dataset that is not in the kList
                const filterDatasets = selectedDatasets.filter((dataset) => {
                  return allDatasets?.find(
                    (item) => item.finalDatasetId === dataset.finalDatasetId
                  );
                });
                resolve && resolve(filterDatasets || []);
                onClose();
              }
            }}
          >
            {isImport ? t('common:common.CopyToDataset') : '完成'}
          </Button>
        </Flex>
      </Flex>
    </MyModal>
  );
};

export default DatasetSelectModal;
