import {
  ButtonProps,
  Flex,
  Menu,
  MenuButton,
  MenuList,
  Box,
  Radio,
  useOutsideClick,
  HStack
} from '@chakra-ui/react';
import React, { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'next-i18next';
import MyDivider from '@/components/MyDivider';
import SvgIcon from '@/components/SvgIcon';
import { PermissionValueType } from '@/types/api/dataset';
import { PermissionList, PermissionKeyEnum } from '@/constants/api/dataset';

export type PermissionSelectProps = {
  value?: PermissionValueType;
  onChange: (value: PermissionValueType) => void;
  trigger?: 'hover' | 'click';
  offset?: [number, number];
  Button: React.ReactNode;
  onDelete?: () => void;
} & Omit<ButtonProps, 'onChange' | 'value'>;

const MenuStyle = {
  py: 2,
  px: 3,
  _hover: {
    bg: 'myGray.50'
  },
  borderRadius: 'md',
  cursor: 'pointer',
  fontSize: 'sm'
};

function PermissionSelect({
  value,
  onChange,
  trigger = 'click',
  offset = [0, 5],
  Button,
  width = 'auto',
  onDelete,
  ...props
}: PermissionSelectProps) {
  const { t } = useTranslation();
  const ref = useRef<HTMLDivElement>(null);
  const closeTimer = useRef<any>();

  const [isOpen, setIsOpen] = useState(false);

  const permissionSelectList = useMemo(() => {
    const list = Object.entries(PermissionList).map(([key, value]) => {
      return {
        name: value.name,
        value: value.value,
        description: value.description,
        checkBoxType: value.checkBoxType
      };
    });

    return {
      singleCheckBoxList: list.filter((item) => item.checkBoxType === 'single'),
      multipleCheckBoxList: list.filter((item) => item.checkBoxType === 'multiple')
    };
  }, []);

  const selectedSingleValue = useMemo(() => {
    return PermissionList[value as PermissionKeyEnum].value;
  }, [value]);

  const onSelectPer = (per: PermissionValueType) => {
    if (per === value) return;
    onChange(per);
    setIsOpen(false);
  };

  useOutsideClick({
    ref: ref,
    handler: () => {
      setIsOpen(false);
    }
  });

  return (
    <Menu offset={offset} isOpen={isOpen} autoSelect={false} direction={'ltr'}>
      <Box
        ref={ref}
        onMouseEnter={() => {
          if (trigger === 'hover') {
            setIsOpen(true);
          }
          clearTimeout(closeTimer.current);
        }}
        onMouseLeave={() => {
          if (trigger === 'hover') {
            closeTimer.current = setTimeout(() => {
              setIsOpen(false);
            }, 100);
          }
        }}
      >
        <Box
          position={'relative'}
          onClickCapture={() => {
            if (trigger === 'click') {
              setIsOpen(!isOpen);
            }
          }}
        >
          <MenuButton
            w={'100%'}
            h={'100%'}
            position={'absolute'}
            top={0}
            right={0}
            bottom={0}
            left={0}
          />
          <Flex
            alignItems={'center'}
            justifyContent={'center'}
            position={'relative'}
            cursor={'pointer'}
            userSelect={'none'}
          >
            {Button}
          </Flex>
        </Box>
        <MenuList
          minW={isOpen ? `${width}px !important` : 0}
          p="3"
          border={'1px solid #fff'}
          boxShadow={
            '0px 2px 4px rgba(161, 167, 179, 0.25), 0px 0px 1px rgba(121, 141, 159, 0.25);'
          }
          zIndex={99}
          overflowY={'auto'}
          whiteSpace={'pre-wrap'}
        >
          {/* The list of single select permissions */}
          {permissionSelectList.singleCheckBoxList.map((item) => {
            const change = () => {
              onSelectPer(item.value as PermissionKeyEnum);
            };

            return (
              <HStack
                key={item.value}
                {...(selectedSingleValue === item.value
                  ? {
                      color: 'primary.600'
                    }
                  : {})}
                {...MenuStyle}
                maxW={['70vw', '260px']}
                onClick={(event) => {
                  event.stopPropagation();
                  change();
                }}
              >
                {/* <Radio isChecked={selectedSingleValue === item.value} variant="primary" /> */}
                <Box>
                  <Box>{item.name}</Box>
                  <Box color={'myGray.500'} fontSize={'mini'}>
                    {t(item.description as any)}
                  </Box>
                </Box>
              </HStack>
            );
          })}

          {onDelete && (
            <>
              <MyDivider my={2} h={'2px'} borderColor={'myGray.200'} />
              <HStack
                {...MenuStyle}
                onClick={() => {
                  onDelete();
                  setIsOpen(false);
                }}
              >
                <SvgIcon name="trash" w="20px" color="red.600" />
                <Box color="red.600">{t('删除')}</Box>
              </HStack>
            </>
          )}
        </MenuList>
      </Box>
    </Menu>
  );
}

export default React.memo(PermissionSelect);
