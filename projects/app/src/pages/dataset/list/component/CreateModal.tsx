import React, { useCallback, useState, useEffect } from 'react';
import { Box, Flex, Button, ModalFooter, ModalBody, Input, Textarea } from '@chakra-ui/react';
import { useSelectFile } from '@/hooks/useSelectFile';
import { useForm } from 'react-hook-form';
import { getErrText } from '@/utils/string';
import { useToast } from '@/hooks/useToast';
import { useSystemStore } from '@/store/useSystemStore';
import { useRequest } from '@/hooks/useRequest';
import Avatar from '@/components/Avatar';
import MyTooltip from '@/components/MyTooltip';
import MyModal from '@/components/MyModal';
import { postCreateDataset, putDatasetById } from '@/api/dataset';
import MySelect from '@/components/MySelect';
import { useTranslation } from 'next-i18next';
import MyRadio from '@/components/common/MyRadio';
import { DatasetTypeEnum } from '@/constants/api/dataset';
import { QuestionOutlineIcon } from '@chakra-ui/icons';
import { CreateDatasetProps, DatasetItemType } from '@/types/api/dataset';
import { uploadImage } from '@/utils/file';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';

import { DATASET_ICON } from '@/constants/common';

const CreateModal = ({
  onClose,
  onSuccess,
  parentId,
  finalParentId,
  type,
  dataset
}: {
  onClose: () => void;
  onSuccess: () => void;
  parentId?: string;
  finalParentId?: string;
  type: 'add' | 'edit';
  dataset?: DatasetItemType;
}) => {
  const { t } = useTranslation();
  const [refresh, setRefresh] = useState(false);
  const { toast } = useToast();
  const { isPc, feConfigs, embeddingModelList, datasetModelList } = useSystemStore();

  const filterNotHiddenVectorModelList = embeddingModelList.filter((item) => !item.hidden);

  const { register, setValue, getValues, handleSubmit } = useForm<CreateDatasetProps>({
    defaultValues: {
      parentId,
      finalParentId,
      type: dataset?.type || DatasetTypeEnum.Dataset,
      avatarUrl: dataset?.avatarUrl || DATASET_ICON,
      name: dataset?.name || '',
      intro: dataset?.intro || '',
      vectorModel: dataset?.vectorModel || filterNotHiddenVectorModelList[0].model,
      agentModel: dataset?.agentModel || datasetModelList[0].model
    }
  });

  useEffect(() => {
    if (dataset) {
      setValue('name', dataset.name);
      setValue('intro', dataset.intro);
      setValue('avatarUrl', dataset.avatarUrl);
      setValue('vectorModel', dataset.vectorModel);
      setValue('agentModel', dataset.agentModel);
    }
  }, [dataset, setValue]);

  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false
  });

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300
        });
        setValue('avatarUrl', data.fileUrl);
        setRefresh((state) => !state);
      } catch (err: any) {
        toast({
          title: getErrText(err, t('common.avatar.Select Failed')),
          status: 'warning'
        });
      }
    },
    [setValue, t, toast]
  );

  const { mutate: onSubmit, isLoading: submitting } = useRequest({
    mutationFn: async (data: CreateDatasetProps) => {
      if (type === 'add') {
        return await postCreateDataset(data);
      } else if (type === 'edit') {
        return await putDatasetById({ id: dataset?.id as string, ...data });
      }
    },
    successToast: type === 'add' ? t('common.Create Success') : t('common.Update Success'),
    onSuccess() {
      onSuccess();
      onClose();
    }
  });

  return (
    <MyModal
      title={type === 'add' ? t('core.dataset.Create dataset') : t('编辑知识库')}
      isOpen
      onClose={onClose}
      isCentered
      w={respDims(500, 450)}
      bgImage="/imgs/dataset/createModalBg.svg"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent',
        borderBottom: '1px solid #E7E7E7',
        padding: '20px 32px',
        marginBottom: '18px'
      }}
      hideCloseButton
    >
      <ModalBody>
        <Box mb={respDims(20)}>
          <Box
            color="#4E5969"
            fontSize={respDims('14fpx')}
            fontWeight={'400'}
            _after={{
              content: '"*"',
              paddingLeft: '5px',
              color: '#F53F3F'
            }}
            mb={respDims(8)}
          >
            {t('知识库图标与名称')}
          </Box>
          <Flex alignItems={'center'}>
            <MyTooltip label={t('请输入知识库名称')}>
              <Avatar
                flexShrink={0}
                src={getValues('avatarUrl')}
                w={respDims(36, 30)}
                h={respDims(36, 30)}
                cursor={'pointer'}
                borderRadius={'0'}
                onClick={onOpenSelectFile}
              />
            </MyTooltip>
            <Input
              ml={3}
              flex={1}
              autoFocus
              bg={'#f5f5f5'}
              placeholder={t('请输入知识库名称，如“教研活动案例“')}
              _placeholder={{
                fontSize: '14px'
              }}
              maxLength={30}
              {...register('name', {
                required: true
              })}
            />
          </Flex>
        </Box>
        <Box mb={respDims(20)}>
          <Box color="#4E5969" fontSize={respDims('14fpx')} fontWeight={'400'} mb={respDims(8)}>
            {t('知识库介绍')}
          </Box>
          <Textarea
            placeholder={
              t('请输入知识库介绍，如“根据用户需求，为文创作品项目推荐合适的遗址”') || ''
            }
            _placeholder={{
              fontSize: '14px'
            }}
            bg={'#f5f5f5'}
            maxLength={200}
            {...register('intro', {})}
          />
        </Box>
        {type === 'add' && (
          <>
            <Box mb={respDims(20)}>
              <Box
                color="#4E5969"
                fontSize={respDims('14fpx')}
                fontWeight={'400'}
                _after={{
                  content: '"*"',
                  paddingLeft: '5px',
                  color: '#F53F3F'
                }}
                mb={respDims(8)}
              >
                {/* 知识库类型 */}
                {t('core.dataset.Dataset Type')}
              </Box>
              <MyRadio
                gridGap={2}
                hiddenCircle={true}
                gridTemplateColumns={`repeat(${1 + (feConfigs.isPlus ? 4 : 0)},1fr)`}
                list={[
                  {
                    title: (
                      <Flex alignItems="center" mb={respDims(8)}>
                        <SvgIcon
                          name="datasetFolderStar"
                          mr={respDims(8)}
                          w={respDims(24)}
                        ></SvgIcon>
                        {t('core.dataset.Common Dataset')}
                      </Flex>
                    ),
                    value: DatasetTypeEnum.Dataset,
                    icon: '',
                    desc: t('core.dataset.Common Dataset Desc')
                  },
                  ...(feConfigs.isPlus
                    ? [
                        {
                          title: (
                            <Flex alignItems="center" mb={respDims(8)}>
                              <SvgIcon
                                name="datasetWebLine"
                                mr={respDims(8)}
                                w={respDims(24)}
                              ></SvgIcon>
                              {t('core.dataset.Website Dataset')}
                            </Flex>
                          ),
                          value: DatasetTypeEnum.WebsiteDataset,
                          icon: '',
                          desc: t('core.dataset.Website Dataset Desc')
                        }
                      ]
                    : [])
                ]}
                value={getValues('type')}
                onChange={(e) => {
                  setValue('type', e as `${DatasetTypeEnum}`);
                  setRefresh(!refresh);
                }}
              />
            </Box>

            {filterNotHiddenVectorModelList.length > 1 && (
              <Box mb={respDims(20)}>
                <Flex
                  mb={respDims(8)}
                  color="#4E5969"
                  fontSize={respDims('14fpx')}
                  alignItems={'center'}
                  flex={'0 0 100px'}
                  _after={{
                    content: '"*"',
                    paddingLeft: '5px',
                    color: '#F53F3F'
                  }}
                >
                  {t('core.ai.model.Vector Model')}
                  <MyTooltip label={t('core.dataset.embedding model tip')}>
                    <QuestionOutlineIcon ml={1} />
                  </MyTooltip>
                </Flex>
                <Box>
                  <MySelect
                    w={'100%'}
                    bg={'#f5f5f5'}
                    value={getValues('vectorModel')}
                    list={filterNotHiddenVectorModelList.map((item) => ({
                      label: item.name,
                      value: item.model
                    }))}
                    onchange={(e) => {
                      setValue('vectorModel', e);
                      setRefresh((state) => !state);
                    }}
                  />
                </Box>
              </Box>
            )}
            {datasetModelList.length > 1 && (
              <Box mb={respDims(20)}>
                <Box flex={'0 0 100px'} color="#4E5969" fontSize={respDims(14)} mb={respDims(8)}>
                  {t('core.ai.model.Dataset Agent Model')}
                </Box>
                <Box>
                  <MySelect
                    w={'100%'}
                    bg={'#f5f5f5'}
                    value={getValues('agentModel')}
                    list={datasetModelList.map((item) => ({
                      label: item.name,
                      value: item.model
                    }))}
                    onchange={(e) => {
                      setValue('agentModel', e);
                      setRefresh((state) => !state);
                    }}
                  />
                </Box>
              </Box>
            )}
          </>
        )}
      </ModalBody>

      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          {t('取消')}
        </Button>
        <Button isLoading={submitting} onClick={handleSubmit((data) => onSubmit(data))}>
          {t('完成')}
        </Button>
      </ModalFooter>

      <File onSelect={onSelectFile} />
    </MyModal>
  );
};

export default CreateModal;
