import {
  ModalBody,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Flex,
  Avatar,
  Box,
  Button,
  Input,
  InputGroup,
  InputRightElement
} from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRequest } from '@/hooks/useRequest';
import {
  deleteTenantDatasetUser,
  updateTenantDatasetUserAuthority,
  getTenantDatasetUserPage
} from '@/api/dataset';
import Loading from '@/components/Loading';
import EmptyTip from '@/components/EmptyTip';
import PermissionSelect from './PermissionSelect';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import AddMemberModal from './AddMemberModal';
import { useUserStore } from '@/store/useUserStore';
import { DatasetItemType, PermissionValueType } from '@/types/api/dataset';
import { PermissionKeyEnum, PermissionList } from '@/constants/api/dataset';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { respDims } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';

type MemberManage = {
  onClose?: () => void;
  onSuccess?: () => void;
  dataset: DatasetItemType;
};

const KnowledgePermissionModal: React.FC<MemberManage> = ({ onClose, onSuccess, dataset }) => {
  const { userInfo } = useUserStore();
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const [searchTerm, setSearchTerm] = useState('');

  const {
    data: collaboratorList = [],
    isLoading: isLoadingCollaborators,
    refetch
  } = useQuery(['collaboratorList', dataset.id], () =>
    getTenantDatasetUserPage({
      tenantDatasetId: dataset.id,
      searchKey: searchTerm,
      size: 9999,
      current: 1
    }).then((res) => res.records)
  );

  const { mutate: onDelete, isLoading: isRemoving } = useRequest({
    mutationFn: (tmbId: string) => deleteTenantDatasetUser({ id: tmbId }),
    successToast: '删除成功',
    onSuccess() {
      refetch();
      onSuccess && onSuccess();
    }
  });

  const { mutate: onUpdate, isLoading: isUpdating } = useRequest({
    mutationFn: ({ tmbId, per }: { tmbId: string; per: PermissionValueType }) => {
      return updateTenantDatasetUserAuthority({
        id: tmbId,
        authority: per
      });
    },
    successToast: '更新成功',
    onSuccess() {
      refetch();
    }
  });

  const loading = isRemoving || isUpdating || isLoadingCollaborators;

  const onAddMember = () => {
    openOverlay({
      Overlay: AddMemberModal,
      props: {
        dataset,
        collaboratorList,
        onClose: () => {},
        onSuccess() {
          refetch();
          onSuccess && onSuccess();
        }
      }
    });
  };

  const filteredCollaboratorList = collaboratorList.filter((item) =>
    item.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getPerLabelList = (per: PermissionKeyEnum) => {
    const labels: string[] = [];

    labels.push(PermissionList[per].name);

    Object.values(PermissionList).forEach((item) => {
      if (item.checkBoxType === 'multiple' && per & item.value) {
        labels.push(item.name);
      }
    });

    return labels;
  };

  return (
    <MyModal isOpen onClose={onClose} minW="600px" title="知识库权限管理" isCentered>
      <ModalBody>
        <Flex alignItems="center" justifyContent="space-between" my={respDims(10)}>
          <InputGroup>
            <Input
              bg={'#f5f5f5'}
              fontSize={respDims(14, 12)}
              _placeholder={{
                fontSize: '14px'
              }}
              placeholder="搜索知识库成员"
              value={searchTerm}
              flex={1}
              mr={respDims(10)}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <InputRightElement>
              <SvgIcon name="search" mr={respDims(20)}></SvgIcon>
            </InputRightElement>
          </InputGroup>

          <Button ml="10px" onClick={onAddMember} px={respDims(30)}>
            <SvgIcon name="plus" w="12px" h="12px" />
            <Box ml="8px">添加</Box>
          </Button>
        </Flex>
        <Box fontWeight={600} color="#4E5969" mt={respDims(20)} mb={respDims(8)}>
          知识库成员
        </Box>
        <TableContainer
          borderRadius="md"
          minH="400px"
          overflow="hidden"
          borderBottomRadius="md"
          border="1px solid #E5E6EB"
        >
          <Table>
            <Thead bg="myGray.100">
              <Tr>
                <Th border="none" color="#606266" pl={respDims(16)}>
                  名称
                </Th>
                <Th border="none" w={'40px'} textAlign="right" color="#606266" pr={respDims(16)}>
                  操作
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredCollaboratorList.map((item) => (
                <Tr
                  key={item.id}
                  _hover={{
                    bg: 'myGray.50'
                  }}
                  borderBottom="1px solid #E5E6EB"
                >
                  <Td border="none" px={respDims(16)} py={respDims(8)}>
                    <Flex alignItems="center">
                      <Avatar
                        src={item.avatar}
                        w={respDims(24)}
                        h={respDims(24)}
                        mr={respDims(8)}
                      />
                      {item.username}
                    </Flex>
                  </Td>

                  <Td border="none" px={respDims(16)} py={respDims(8)}>
                    <Flex alignItems="center">
                      <PermissionSelect
                        Button={
                          <Box color="primary.500" w={respDims(80)}>
                            {getPerLabelList(item.authority).join('、')}
                            <ChevronDownIcon fontSize={'md'} />
                          </Box>
                        }
                        value={item.authority}
                        onChange={(per) => {
                          onUpdate({
                            tmbId: item.id,
                            per
                          });
                        }}
                      ></PermissionSelect>

                      <Box
                        ml={respDims(12, 10)}
                        onClick={() =>
                          MessageBox.confirm({
                            title: '移除成员权限',
                            content: `确定要移除${item.username}的权限？`,
                            onOk: async () => {
                              onDelete(item.id);
                            }
                          })
                        }
                        fontSize={respDims(14, 12)}
                        sx={{
                          fontFamily: 'PingFang SC, PingFang SC',
                          fontWeight: '400',
                          color: '#F53F3F',
                          textAlign: 'left',
                          fontStyle: 'normal',
                          textTransform: 'none',
                          cursor: 'pointer',
                          _hover: {
                            opacity: 0.8
                          }
                        }}
                      >
                        移除
                      </Box>
                    </Flex>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
          {filteredCollaboratorList.length === 0 && <EmptyTip text={'暂无协作者'} />}
        </TableContainer>
        {loading && <Loading fixed={false} />}
        <OverlayContainer></OverlayContainer>
      </ModalBody>
    </MyModal>
  );
};

export default KnowledgePermissionModal;
