import { useOverlayManager } from '@/hooks/useOverlayManager';
import DatasetSelectModal from '../component/DatasetSelectModal';
import { selectDatasetParams } from '@/types/pages/dataset';

export default function useDatasetSelect() {
  const { openOverlay } = useOverlayManager();

  const open = ({
    defaultSelectedDatasets,
    sourceKey,
    cloudFileIds,
    cloudFileId,
    isImport,
    onRefresh = () => {}
  }: selectDatasetParams) =>
    openOverlay({
      Overlay: DatasetSelectModal,
      props: {
        onChange: () => {},
        onClose: () => {},
        defaultSelectedDatasets,
        sourceKey,
        isImport,
        cloudFileIds,
        cloudFileId,
        onRefresh
      }
    });

  return {
    open
  };
}
