import { Box, Center, Spinner } from '@chakra-ui/react';
import { useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { serviceSideProps } from '@/utils/i18n';
import { getExaminationToken } from '@/api/system';
import { activityReportExamination } from '@/api/ai/correction/correction';
import { getToken } from '@/utils/auth';

const AiExam = ({ ...props }) => {
  const iFrameRef = useRef<HTMLIFrameElement | null>(null);

  useEffect(() => {
    activityReportExamination().then((res) => {});
  }, []);

  const {
    data: token,
    isLoading,
    error
  } = useQuery({
    queryKey: ['examinationToken'],
    queryFn: getExaminationToken,
    staleTime: 1000 * 60 * 5,
    retry: 2
  });

  if (isLoading) {
    return (
      <Center w="100%" h="100%">
        <Spinner size="xl" color="primary.500" />
      </Center>
    );
  }

  if (error) {
    return <Box>Error: {(error as Error).message}</Box>;
  }

  const iframeSrc = `https://ib.hwzxs.com/#/login${
    token
      ? `?token=${encodeURIComponent(token.token)}&userToken=${encodeURIComponent(getToken())}`
      : `?userToken=${encodeURIComponent(getToken())}`
  }&_=${new Date().getTime()}`;

  return (
    <Box
      w="100%"
      h="100%"
      {...props}
      ref={iFrameRef}
      as="iframe"
      css={{
        '.AILayout-bg': {
          background: 'none!important'
        }
      }}
      src={iframeSrc}
      onLoad={() => {}}
    />
  );
};

export default AiExam;

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}
