import { Flex, useDisclosure } from '@chakra-ui/react';
import React, { useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import { useSystemStore } from '@/store/useSystemStore';
import ChatPanel from './ChatPanel';
import PageLayout from '@/components/PageLayout';
import HomeNavbar from '@/pages/home/<USER>/HomeNavbar';
import { useChatStore } from '@/store/useChatStore';

// 生成设备唯一标识
const generateDeviceId = (): string => {
  const storageKey = 'device_unique_id';

  // 尝试从localStorage获取已存在的deviceId
  if (typeof window !== 'undefined') {
    const existingId = localStorage.getItem(storageKey);
    if (existingId) {
      return existingId;
    }
  }

  // 生成新的UUID
  const generateUUID = (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const newDeviceId = generateUUID();

  // 存储到localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem(storageKey, newDeviceId);
  }

  return newDeviceId;
};

const Home = ({ chatId = '', activeRoute = '' }: { chatId?: string; activeRoute?: string }) => {
  const router = useRouter();
  const { isPc } = useSystemStore();

  // 从路由参数中获取appId
  const appId = useMemo(() => {
    return (router.query.appId as string) || '';
  }, [router.query.appId]);

  // 获取当前设备唯一标识
  const deviceId = useMemo(() => {
    return generateDeviceId();
  }, []);

  const { chatData, setClientAppFormDetail, setQuestionFormGuideStep } = useChatStore();

  const { isOpen: isOpenApplist, onOpen: onOpenAppList, onClose: onCloseAppList } = useDisclosure();

  useEffect(() => {
    if (!chatData.appId && (chatData.app as any)?.type !== 'simple') {
      return;
    }

    // if (chatData.appId) {
    //   getClientAppFormDetail(chatData.appId)
    //     .then((res) => {
    //       setClientAppFormDetail(res as ClientAppFormDetailType);
    //       setQuestionFormGuideStep(1);
    //     })
    //     .catch((error) => {});
    // }
  }, [chatData, setClientAppFormDetail, setQuestionFormGuideStep]);

  // 调试信息：输出appId和deviceId
  useEffect(() => {
    if (appId || deviceId) {
      console.log('temp_chat组件 - appId:', appId);
      console.log('temp_chat组件 - deviceId:', deviceId);
    }
  }, [appId, deviceId]);

  return (
    <PageLayout
      {...(!isPc && {
        bgImage: 'url(/imgs/v2/gradient_bg4.png)',
        bgRepeat: 'no-repeat',
        bgSize: '100% auto',
        navbar: (
          <>
            <HomeNavbar onOpenAppList={onOpenAppList} />
          </>
        )
      })}
    >
      <Flex flexDir="column" w="100%" h="100%">
        <Flex h="100%" w="100%" alignItems="stretch">
          <Flex flex="1 0 0" overflow="hidden" pos="relative">
            {/* 
              ChatPanel组件接收以下参数：
              - appId: 从路由参数获取的应用ID
              - chatId: 聊天会话ID
              - activeRoute: 当前活动路由
              - deviceId: 从路由参数获取的设备唯一标识
            */}
            <ChatPanel
              deviceId={deviceId}
              appId={appId}
              chatId={chatId}
              activeRoute={activeRoute}
              flex="1"
            />
          </Flex>
        </Flex>
      </Flex>
    </PageLayout>
  );
};

export default Home;
