.file-table {
  :global {
    .ant-table-thead {
      .ant-table-cell {
        color: rgba(0, 0, 0, 0.9) !important;
        background-color: #fafaff !important;

        &::before {
          display: none;
        }
      }
    }

    .ant-table-tbody {
      background-color: #fff !important;
      .selected-row {
        background-color: #fff !important; // 你可以根据需要调整颜色
      }
      &:hover .ant-table-placeholder {
        background-color: #fff !important;
      }

      .ant-table-placeholder {
        background-color: #fff !important;
        .ant-table-cell {
          border: none !important;
        }
      }

      .ant-table-row {
        .ant-table-cell {
          border-top: 2px solid transparent !important;
          border-bottom: 2px solid transparent !important;
          background-color: transparent;
          background-clip: padding-box;
          &:first-child {
            border-radius: 8px 0 0 8px;
          }
          &:last-child {
            border-radius: 0 8px 8px 0;
          }
        }

        &:hover .ant-table-cell {
          background-color: #f8fafc !important;
        }

        &.ant-table-row-selected {
          .ant-table-cell {
            background-color: #f9f7ff !important;
          }
        }

        &.dragging {
          opacity: 0.5;
        }

        &.drag-over {
          position: relative;
          .ant-table-cell:last-child {
            position: static !important;
            &::after {
              content: ' ';
              position: absolute;
              left: 0;
              top: 0;
              right: 0;
              bottom: 0;
              border: 1px solid #3366ff;
              border-radius: 8px;
              pointer-events: none;
            }
          }
          &:hover {
            background-color: #f3f4f6 !important;
          }
        }
      }
    }
  }
}

.file-table2 {
  :global {
    .ant-table-thead {
      .ant-table-cell {
        color: rgba(0, 0, 0, 0.9) !important;
        background-color: #f9fafc !important;

        &::before {
          display: none;
        }
      }
    }

    .ant-table-tbody {
      background-color: #fff !important;
      .selected-row {
        background-color: #fff !important; // 你可以根据需要调整颜色
      }
      .ant-table-row {
        .ant-table-cell {
          border-top: 2px solid transparent !important;
          border-bottom: 2px solid transparent !important;
          background-color: transparent;
          background-clip: padding-box;
          &:first-child {
            border-radius: 8px 0 0 8px;
          }
          &:last-child {
            border-radius: 0 8px 8px 0;
          }
        }

        &:hover .ant-table-cell {
          background-color: #f9fafc !important;
        }

        &.ant-table-row-selected {
          .ant-table-cell {
            background-color: #f9fafc !important;
          }
        }

        &.dragging {
          opacity: 0.5;
        }

        &.drag-over {
          position: relative;
          .ant-table-cell:last-child {
            position: static !important;
            &::after {
              content: ' ';
              position: absolute;
              left: 0;
              top: 0;
              right: 0;
              bottom: 0;
              border: 1px solid #3366ff;
              border-radius: 8px;
              pointer-events: none;
            }
          }
        }
      }
    }
  }
}

.space-tree {
  min-width: 100%;
  background-color: transparent;

  :global {
    .ant-tree-treenode {
      position: relative;
      align-items: center;
      padding: 0 8px 0 16px !important;
      border: 1px solid transparent;
      border-radius: 8px;

      .ant-tree-drop-indicator {
        display: none;
      }

      &.drag-over {
        border: 1px solid #3366ff;
      }

      &.drag-over-gap-bottom {
        &::after {
          content: ' ';
          height: 2px;
          background-color: #3366ff;
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
        }
      }

      &.dragging {
        opacity: 0.5;
        &::after {
          display: none;
        }
      }

      .ant-tree-draggable-icon {
        display: none;
        width: 0;
      }

      &:hover {
        background-color: #f8fafc !important;

        .ant-tree-draggable-icon {
          display: block;
        }
      }

      .ant-tree-switcher {
        display: flex;
        justify-content: center;
        align-items: center;
        align-self: center;
        width: 16px;
        height: 16px;
      }

      .ant-tree-indent-unit {
        width: 6px;
      }
    }

    .ant-tree-treenode-selected {
      background-color: #f2f3ff !important;
      color: #7d4dff !important;

      &:hover {
        background-color: #f2f3ff !important;
      }

      .ant-tree-node-content-wrapper {
        background-color: transparent;
      }
    }

    .ant-tree-node-content-wrapper {
      padding-left: 0px;
      &:hover {
        background-color: transparent !important;
      }
    }

    .ant-tree-title {
      white-space: nowrap;
    }
  }
}
.tree-transfer {
  :global {
    .ant-transfer-list-header-selected {
      display: none !important;
    }
    .ant-transfer-list-header-title {
      text-align: left;
    }
    .ant-btn {
      display: none !important;
    }
  }
}

.custom-select {
  :global {
    .ant-select-selector {
      background-color: #f7f7f7 !important;
    }
  }
}

.tenant-select {
  :global {
    .ant-select-selector {
      border: 1px solid #e9ebf0 !important;
      font-size: 12px !important;
    }
  }
}

.ground-select {
  :global {
    .ant-select-selector {
      background-color: #f6f6f6 !important;
    }
  }
}

.ant-tabs-tab {
  :global {
    .ant-tabs-tab-btn {
      font-size: 16px !important;
    }
  }
}
.select-content {
  :global {
    .ant-select-selector {
      border: none !important;
      background-color: #fafafa !important;
    }
    .ant-select-selection-item {
      color: #3366ff;
    }
    .ant-select-arrow {
      display: none;
    }
  }
}

.allSearch-select {
  :global {
    .ant-select-selector {
      border: none !important;
      background-color: #fff !important;
    }
  }
}

.ant-tree {
  :global {
    .ant-tree-checkbox {
      order: 1;
      align-self: center;
      margin-top: 0;
      margin-left: auto;
      position: relative;
      left: 0;
    }
    .ant-tree-checkbox + span:hover {
      background: transparent !important;
    }
    .ant-tree-node-content-wrapper {
      flex-grow: 1;
    }
    .ant-tree-switcher {
      align-self: center;
    }
    .ant-tree-treenode {
      width: 100%;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e5e7eb;
      padding: 13px;
    }
  }
}
