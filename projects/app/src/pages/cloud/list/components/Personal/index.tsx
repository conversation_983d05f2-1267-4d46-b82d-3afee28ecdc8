import MyTable from '@/components/MyTable';
import { Box, Center, Flex, Text } from '@chakra-ui/react';
import Header from './Header';
import { RowProps, Table, TableProps } from 'antd';
import Footer from './Footer';
import { copyFile, getMyFilePage, removeMyFileBatch, reportCloudFileDownload } from '@/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { TableRowSelection } from 'antd/es/table/interface';
import { BizTypeEnum, FileTypeEnum, PrivilegeEnum } from '@/constants/api/cloud';
import { formatFileSize } from '@/utils/tools';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import MyMenu from '@/components/MyMenu';
import styles from '../../../cloud.module.scss';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { MessageBox } from '@/utils/ui/messageBox';
import { FileType, locationPathType } from '@/types/api/cloud';
import { FileType as MessageFileType } from '@/components/ChatBox/MessageInputMini';
import { AccessType, FilePathType, PathItemType } from '@/types/cloud';
import { FooterComponentProps, MyTableRef, SearchBarProps } from '@/components/MyTable/types';
import MyTooltip from '@/components/MyTooltip';
import { FileParseStatusEnum, PathItemTypeEnum, myRootFolder } from '@/constants/cloud';
import { useCloudStore } from '@/store/useCloudStore';
import useFilePreview from '@/hooks/useFilePreview';
import FolderModal from '@/pages/cloud/components/FolderModal';
import useUploadRefresh from '@/pages/cloud/hooks/useUploadRefresh';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { Toast } from '@/utils/ui/toast';
import { useNotificationStore } from '@/store/useTificationContext';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';
import DndHandle from '@/pages/cloud/components/DndWrapper/DndHandle';
import DndRow from '@/pages/cloud/components/DndWrapper/DndRow';
import { useUserStore } from '@/store/useUserStore';
import { UserRoleEnum } from '@/constants/api/auth';
import MoveModal from '../Tenant/MoveModal';
import useDatasetSelect from '@/pages/dataset/list/hooks/useDatasetSelect';
import { useDatasetStore } from '@/store/useDatasetStore';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { DataSource } from '@/constants/common';
import { useChatStore } from '@/store/useChatStore';
import { useAppStore } from '@/store/useAppStore';
import { useQuery } from '@tanstack/react-query';
import { appointedTypeEnum } from '@/constants/api/app';
import { fileTypeInfos, UploadStatusEnum } from '@/components/ChatBox/MessageInput';
import AddFile from '@/pages/cloud/components/AddFile';
import { Modal } from 'antd';
const Personal = ({
  value,
  access,
  onGetLocationPath
}: {
  value: AppSimpleEditFormTypeMegre;
  onGetLocationPath: (data: locationPathType[], searchContent: string) => void;
  access: AccessType;
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<FileType[]>([]);
  const { userInfo } = useUserStore();
  const [sortOrder, setSortOrder] = useState<number>();
  const [showMenu, setShowMenu] = useState(false);
  const tableRef = useRef<MyTableRef>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { setInitChatInputs } = useChatStore();
  const isAdmin = userInfo?.roleType === UserRoleEnum.Admin;
  const isSpaceAdmin = userInfo?.roleType === UserRoleEnum.SpaceAdmin;

  const { previewFile } = useFilePreview();

  const { addDownload } = useCloudDownloader();

  const { fetchUnreadCount } = useNotificationStore();

  const { openOverlay } = useOverlayManager();
  const { selectFile, setSelectFile, setShowFileInfo, showFileInfo } = useCloudStore(); // 使用 store
  const [selectedMenuItemId, setSelectedMenuItemId] = useState<string | null>(null);
  const { specialAppList, loadSpecialAppList } = useAppStore();
  const { mindMapApp, docApp } = useMemo(() => {
    return {
      mindMapApp: specialAppList.find((app) => app.appointedType === appointedTypeEnum.MindMap),
      docApp: specialAppList.find((app) => app.appointedType === appointedTypeEnum.Document)
    };
  }, [specialAppList]);

  useQuery(['loadSpecialAppList'], () => loadSpecialAppList());
  const { open: openSelectDatasetModal } = useDatasetSelect();
  const { allDatasets } = useDatasetStore();
  const [path, setPath] = useState<FilePathType>([
    {
      type: PathItemTypeEnum.file,
      file: myRootFolder
    }
  ]);
  const selectDatasets = useMemo(() => {
    if (!value) return [];
    return allDatasets.filter((item) =>
      value.dataset?.datasets.find((dataset) => dataset.datasetId === item._id)
    );
  }, [allDatasets, value?.dataset?.datasets]);

  const MenuItem = ({ id, label }: { id: string; label: string }) => (
    <Flex
      alignItems="center"
      p={2}
      cursor="pointer"
      onClick={() => {
        setSortOrder(Number(id));
        setShowMenu(false);
        setSelectedMenuItemId(id);
      }}
      _hover={{ bg: '#F2F3F5' }}
      bg={selectedMenuItemId === id ? '#F2F3F5' : 'transparent'}
    >
      <Text fontSize="14px" pl="6px" lineHeight="22px" color="#1D2129" fontWeight="400">
        {label}
      </Text>
    </Flex>
  );

  const { privilege } = useMemo(() => {
    return {
      privilege:
        isAdmin || isSpaceAdmin
          ? PrivilegeEnum.Owner
          : path.reduce(
              (max, item) =>
                item.file.privileges?.reduce((max, cur) => (cur > max ? cur : max), max) ?? max,
              PrivilegeEnum.View
            )
    };
  }, [path, isAdmin, isSpaceAdmin]);

  const parentId = path[path.length - 1].file.id;

  useUploadRefresh({ parentId, onRefresh: () => tableRef.current?.reload() });

  const onIntoDataset = async (folder: FileType) => {
    if (
      folder.fileSize === 0 ||
      (folder.fileSize && Number((folder.fileSize / 1024 / 1024).toFixed(2)) < 500)
    ) {
      openSelectDatasetModal({
        defaultSelectedDatasets: selectDatasets.map((item) => {
          return { id: item._id };
        }),
        sourceKey: DataSource.Personal,
        cloudFileId: Number(folder.id),
        isImport: true,
        onRefresh: () => {
          tableRef.current?.reload();
        }
      });
    } else {
      Toast.warning('文件大小超过500MB限制');
    }
  };

  const onEditFolder = (folder: FileType) => {
    openOverlay({
      Overlay: FolderModal,
      props: {
        bizType: BizTypeEnum.MyLibrary,
        folder,
        fileType: folder.fileType,
        onSuccess: () => tableRef.current?.reload()
      }
    });
  };

  const onRemoveFile = (file: FileType) => {
    MessageBox.delete({
      content:
        file.fileType === FileTypeEnum.Folder
          ? '删除文件夹 则文件夹下的文件会全部删除，删除的全部内容将进入回收站，30天后自动彻底删除。'
          : '删除文件全部内容将进入回收站，30天后自动彻底删除。',

      onOk: () => {
        removeMyFileBatch({ list: [{ id: file.id, fileType: file.fileType! }] }).then(() => {
          tableRef.current?.reload();
          fetchUnreadCount();
        });
      }
    });
  };

  const onClickFile = async (file: FileType) => {
    const formattedSize = file.fileSize ? formatFileSize(file.fileSize) : '0';
    const sizeMatch = formattedSize.match(/^([\d.]+)/);
    // 拿到数字部分
    const fileSize = sizeMatch ? parseFloat(sizeMatch[1]) : 0;
    // 拿到单位部分
    const fileUnit = formattedSize.match(/([a-zA-Z]+)$/)?.[0];

    if (
      file.fileType === FileTypeEnum.File &&
      fileUnit !== 'B' &&
      fileUnit !== 'KB' &&
      fileSize > 80
    ) {
      // 防止重复点击
      if (isModalOpen) return;
      setIsModalOpen(true);

      // 取消和下载两个按钮
      Modal.confirm({
        title: '提示',
        content: '当前文件超过80M，在线预览过慢，请下载查看~',
        okText: '下载',
        cancelText: '取消',
        onOk: () => {
          onDownloadFile(file);
          setIsModalOpen(false);
        },
        maskClosable: true,
        onCancel: () => {
          setIsModalOpen(false);
        }
      });
      return;
    }

    const previewSuccess = await previewFile({
      fileUrl: file.file?.fileUrl ?? '',
      fileType: file.fileType,
      fileKey: file.file?.fileKey
    });
    if (previewSuccess) {
      return;
    }
    if (file.fileType === FileTypeEnum.Folder) {
      setPath((state) => [...state, { type: PathItemTypeEnum.file, file }]);
    }
  };

  const onBackToParent = useCallback(() => {
    setPath((state) => state.slice(0, state.length - 1));
  }, []);

  const onClickPathItem = useCallback((pathItem: PathItemType) => {
    if (pathItem.type === PathItemTypeEnum.space) {
      setPath([]);
    } else {
      setPath((state) => {
        const index = state.findIndex((item) => item.file.id === pathItem.file.id);
        return index >= 0 ? state.slice(0, index + 1) : state;
      });
    }
  }, []);

  const onDownloadFile = (file: FileType) => {
    reportCloudFileDownload();
    addDownload(
      file.fileType === FileTypeEnum.File
        ? {
            bizType: BizTypeEnum.MyLibrary,
            type: DownloadTypeEnum.File,
            fileId: file.id,
            fileKey: file.fileKey!
          }
        : {
            bizType: BizTypeEnum.MyLibrary,
            type: DownloadTypeEnum.Folder,
            source: DownloadSourceEnum.Normal,
            folderId: file.id
          }
    ).then(() => {
      Toast.success('已添加到下载队列中');
    });
  };

  const onMoveFile = (folder: FileType) => {
    openOverlay({
      Overlay: MoveModal,
      props: {
        bizType: BizTypeEnum.MyLibrary,
        folder,
        isBatch: false,
        onSuccess: () => tableRef.current?.reload()
      }
    });
  };

  const onCopyFile = (file: FileType) => {
    MessageBox.info({
      title: '复制提示',
      content: '复制文件将在当前列表显示，确定复制一份文件？',
      okCancel: true,
      okText: '确定复制',
      onOk: () => {
        copyFile(file.id).then(() => {
          tableRef.current?.reload();
          Toast.success('复制成功');
        });
      }
    });
  };

  const onFolderPath = useCallback(
    (path: locationPathType[], searchContent: string) => {
      setTimeout(() => {
        const fileItems: FilePathType = [];
        path.forEach((item) => {
          fileItems.push({
            type: PathItemTypeEnum.file,
            file: {
              id: item.id,
              parentId: item.parentId,
              fileName: item.name
            } as FileType
          });
        });

        setPath((state) => [...state, ...fileItems]);

        setTimeout(() => {
          tableRef.current?.setQuery({
            ...tableRef.current?.query,
            searchKey: searchContent
          });
        }, 300);
      }, 300);
      onGetLocationPath(path, searchContent);
    },
    [parentId, path]
  );

  useEffect(() => {
    setSelectedFiles([]);
    setSelectedRowKeys([]);
    tableRef.current?.setCurrent?.(1);
  }, [parentId]);

  const TableHeader = useCallback(
    ({ onSearch, ...props }: SearchBarProps) => {
      return (
        <Header
          {...props}
          path={path}
          onSearch={(e) => {
            setSelectedFiles([]);
            setSelectedRowKeys([]);
            onSearch?.(e);
          }}
          onBackToParent={onBackToParent}
          onClickPathItem={onClickPathItem}
          onClickFolderPath={onFolderPath}
        />
      );
    },
    [path, onBackToParent, onClickPathItem]
  );

  const TableFooter = useCallback(
    (props: FooterComponentProps) => {
      return (
        <Footer
          {...props}
          selectedFiles={selectedFiles}
          parentId={parentId}
          onRefresh={() => tableRef.current?.reload()}
          onClearSelectedFiles={() => {
            setSelectedRowKeys([]);
            setSelectedFiles([]);
          }}
        />
      );
    },
    [selectedFiles, parentId]
  );

  const onMindMap = (file: FileType) => {
    const type = file.fileName.substring(file.fileName.lastIndexOf('.') + 1).toLocaleLowerCase();
    const info = fileTypeInfos.find((it) => it.name === type);
    if (!info) {
      return null;
    }
    const fileData: MessageFileType = {
      ...file,
      type,
      sizeText: formatFileSize(file.fileSize!),
      rawFile: new File([], file.fileName),
      key: file.fileKey!,
      uploadStatus: UploadStatusEnum.success,
      percent: 100,
      sort: 0,
      isCallOcr: false,
      fileUrl: file.file?.fileUrl ?? '',
      fileKey: file.fileKey!,
      name: file.fileName,
      svgIcon: info.svgIcon
    };
    setInitChatInputs({
      inputVal: '生成思维导图',
      files: [fileData]
    });

    setTimeout(() => {
      window.open(`/home?appId=${mindMapApp?.id}&init=1`, '_blank');
    }, 100);
  };

  const onDocScreenshot = (file: FileType) => {
    const type = file.fileName.substring(file.fileName.lastIndexOf('.') + 1).toLocaleLowerCase();
    const info = fileTypeInfos.find((it) => it.name === type);
    if (!info) {
      return null;
    }
    const fileData: MessageFileType = {
      ...file,
      type,
      sizeText: formatFileSize(file.fileSize!),
      rawFile: new File([], file.fileName),
      key: file.fileKey!,
      uploadStatus: UploadStatusEnum.success,
      percent: 100,
      sort: 0,
      isCallOcr: false,
      fileUrl: file.file?.fileUrl ?? '',
      fileKey: file.fileKey!,
      name: file.fileName,
      svgIcon: info.svgIcon
    };
    setInitChatInputs({
      inputVal: '文档解读',
      files: [fileData]
    });
    window.open(`/home?appId=${docApp?.id}&init=1`, '_blank');
  };

  const rowSelection: TableRowSelection<FileType> = {
    type: 'checkbox',
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange(selectedRowKeys, selectedRows, info) {
      setSelectedRowKeys(selectedRowKeys as string[]);
      setSelectedFiles((state) => [
        ...state.filter(
          (item) =>
            selectedRowKeys.includes(item.rowKey!) &&
            !selectedRows.some((row) => row.rowKey === item.rowKey)
        ),
        ...selectedRows
      ]);
    }
  };

  const columns: TableProps<FileType>['columns'] = [
    ...(privilege >= PrivilegeEnum.Owner
      ? [
          {
            title: '',
            key: 'handle',
            className: 'dnd-handle',
            render: () => <DndHandle />,
            width: 0
          }
        ]
      : []),
    Table.SELECTION_COLUMN,
    {
      title: '文件名称',
      key: 'folderName',
      render: (_, record) => {
        const { fileType, fileName } = record;
        return (
          <Flex
            whiteSpace="nowrap"
            alignItems="center"
            cursor="pointer"
            onClick={() => onClickFile(record)}
          >
            <FileIcon flexShrink="0" {...record} />
            <MyTooltip overflowOnly>
              <Box
                ml={respDims(12)}
                fontSize={respDims('14fpx')}
                lineHeight="1.5em"
                maxH="3em"
                wordBreak="break-all"
                overflow="hidden"
                textOverflow="ellipsis"
                maxW={respDims(200, 30)}
              >
                {fileName}
              </Box>
            </MyTooltip>
          </Flex>
        );
      }
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      render: (_, { fileType: type, fileSize }) => {
        return (
          <Box whiteSpace="nowrap">
            {type === FileTypeEnum.Folder ? '' : formatFileSize(fileSize!)}
          </Box>
        );
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      render: (value) => {
        return <Box whiteSpace="nowrap">{dayjs(value).format('YYYY-MM-DD HH:mm')}</Box>;
      }
    },
    // {
    //   title: '已导入知识库',
    //   dataIndex: 'cloudFileDatasetList',
    //   render: (value) => {
    //     return (
    //       <MyTooltip
    //         overflowOnly
    //         label={value?.map((item: any) => item.tenantDatasetName).join(',')}
    //       >
    //         <Box
    //           ml={respDims(12)}
    //           fontSize={respDims('14fpx')}
    //           lineHeight="1.5em"
    //           maxH="3em"
    //           wordBreak="break-all"
    //           whiteSpace="nowrap"
    //           overflow="hidden"
    //           textOverflow="ellipsis"
    //           maxW="100px"
    //         >
    //           {value?.length === 0
    //             ? '-'
    //             : value?.map((v: any, index: number) => (
    //                 <>
    //                   {index !== 0 && '，'}
    //                   {v?.tenantDatasetName}
    //                 </>
    //               ))}
    //         </Box>
    //       </MyTooltip>
    //     );
    //   }
    // },
    {
      title: (
        <Flex justify="flex-end" position="relative">
          <MyTooltip label="排序" aria-label="排序">
            <Flex
              alignItems="center"
              justifyContent="center"
              _hover={{
                bgColor: '#F2F2F2',
                borderRadius: '4px'
              }}
              padding={respDims(4)}
              mr={respDims(16)}
            >
              <SvgIcon
                cursor="pointer"
                name="dataSpaceSort"
                color={showMenu ? 'primary.500' : '#1D2129'}
                w={respDims(22, 18)}
                h={respDims(22, 18)}
                onClick={() => {
                  setShowMenu((prev) => !prev);
                }}
              />
            </Flex>
          </MyTooltip>
          {showMenu && (
            <Box
              bg="white"
              borderRadius="md"
              boxShadow="md"
              pt="4px"
              w="146px"
              pos="absolute"
              top="50px"
              right="42px"
            >
              <MenuItem id="1" label="文件名A-Z" />
              <MenuItem id="2" label="文件名Z-A" />
              <MenuItem id="3" label="新文件优先" />
              <MenuItem id="4" label="旧文件优先" />
              <MenuItem id="5" label="大文件优先" />
              <MenuItem id="6" label="小文件优先" />
            </Box>
          )}
          <MyTooltip label="文件信息" aria-label="文件信息">
            <Flex
              alignItems="center"
              justifyContent="center"
              _hover={{
                bgColor: '#F2F2F2',
                borderRadius: '4px'
              }}
              padding={respDims(4)}
            >
              <SvgIcon
                cursor="pointer"
                name="file2Info"
                w={respDims(22, 18)}
                h={respDims(22, 18)}
                color={showFileInfo ? 'primary.500' : '#1D2129'}
                onClick={() => {
                  setShowFileInfo(!showFileInfo);
                }}
              />
            </Flex>
          </MyTooltip>
          {/* <MyTooltip label="文件动态" aria-label="文件动态">
            <SvgIcon
              name="file2Dynamic"
              w={respDims(20)}
              h={respDims(20)}
              onClick={onToggleFileDynamics}
              cursor="pointer"
            />
          </MyTooltip> */}
        </Flex>
      ),
      key: 'action',
      render: (_, record) => (
        <Flex justify="flex-end">
          <MyMenu
            trigger="hover"
            width={100}
            Button={
              <Center
                w={respDims('24fpx')}
                h={respDims('24fpx')}
                borderRadius={respDims(4)}
                cursor="pointer"
                _hover={{
                  bgColor: '#EFEFEF'
                }}
              >
                <SvgIcon name="more" w={respDims('14fpx')} h={respDims('14fpx')} />
              </Center>
            }
            menuList={[
              ...(record.fileParseStatus === FileParseStatusEnum.success
                ? [
                    ...(mindMapApp
                      ? [
                          {
                            label: '思维导图',
                            icon: <SvgIcon name="cloudMindMap" />,
                            onClick: () => onMindMap(record)
                          }
                        ]
                      : []),
                    ...(docApp
                      ? [
                          {
                            label: '文档解读',
                            icon: <SvgIcon name="cloudDocScreenShot" />,
                            onClick: () => onDocScreenshot(record)
                          }
                        ]
                      : [])
                  ]
                : []),
              // ...(record.fileType === FileTypeEnum.File
              //   ? [
              //       {
              //         label: '导入知识库',
              //         icon: <SvgIcon name="file_import_line" />,
              //         onClick: () => onIntoDataset(record)
              //       }
              //     ]
              //   : []),
              {
                label: '重命名',
                icon: <SvgIcon name="edit" />,
                onClick: () => onEditFolder(record)
              },
              {
                label: '下载',
                icon: <SvgIcon name="download" />,
                onClick: () => onDownloadFile(record)
              },
              {
                label: '移动',
                icon: <SvgIcon name="drag" />,
                onClick: () => onMoveFile(record)
              },
              ...(record.fileType === FileTypeEnum.File
                ? [
                    {
                      label: '复制',
                      icon: <SvgIcon name="copy" />,
                      onClick: () => onCopyFile(record)
                    }
                  ]
                : []),

              {
                label: '删除',
                icon: <SvgIcon name="trash" />,
                onClick: () => onRemoveFile(record)
              }
            ]}
            setShowMenu={setShowMenu}
          />
        </Flex>
      )
    }
  ];

  const DndTableRow = useCallback(({ record, ...props }: { record: FileType } & RowProps) => {
    return (
      <DndRow
        {...props}
        data={{
          type: 'file',
          file: {
            ...record,
            bizType: BizTypeEnum.MyLibrary
          },
          onDragEnd: () => tableRef.current?.reload()
        }}
      />
    );
  }, []);

  return (
    <Box
      w="100%"
      h="100%"
      flexDir="column"
      css={{
        '& .dnd-handle': {
          paddingLeft: '0 !important',
          paddingRight: '0 !important'
        }
      }}
    >
      <MyTable
        ref={tableRef}
        rowKey="rowKey"
        className={styles['file-table']}
        columns={columns}
        components={{
          body: {
            row: privilege >= PrivilegeEnum.Edit ? DndTableRow : undefined
          }
        }}
        rowSelection={rowSelection}
        api={getMyFilePage}
        defaultQuery={{ parentId, sortOrder }}
        boxStyle={{ px: 0, py: 0, overflow: 'visible' }}
        tableStyle={{
          paddingLeft: '20px',
          paddingRight: '20px'
        }}
        headerConfig={{ HeaderComponent: TableHeader, showHeader: true, showIfEmpty: true }}
        emptyConfig={{
          EmptyComponent: () => <AddFile path={path} isPersonal={true} />
        }}
        FooterComponent={TableFooter}
        onRow={(record) => ({
          record,
          onClick: () => {
            setSelectFile({ ...record, bizType: BizTypeEnum.MyLibrary });
          }
        })}
        rowClassName={(record) => (record.id === selectFile?.id ? 'selected-row' : '')}
      />
    </Box>
  );
};

export default Personal;
