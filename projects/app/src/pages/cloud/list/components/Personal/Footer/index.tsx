import { copyFileBatch, removeMyFileBatch } from '@/api/cloud';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { FooterComponentProps } from '@/components/MyTable/types';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import ToolBar from '@/pages/cloud/components/ToolBar';
import { FileType } from '@/types/api/cloud';
import { respDims, rpxDim } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex } from '@chakra-ui/react';
import { useNotificationStore } from '@/store/useTificationContext';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';
import useDatasetSelect from '@/pages/dataset/list/hooks/useDatasetSelect';
import { DataSource } from '@/constants/common';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import MoveModal from '../../Tenant/MoveModal';

const Footer = ({
  selectedFiles = [],
  parentId,
  total,
  PageRender,
  onRefresh,
  onClearSelectedFiles
}: {
  selectedFiles?: FileType[];
  parentId?: string;
  onRefresh?: () => void;
  onClearSelectedFiles?: () => void;
} & FooterComponentProps) => {
  const { addDownload } = useCloudDownloader();

  const { fetchUnreadCount } = useNotificationStore();
  const { openOverlay } = useOverlayManager();

  const { open: openSelectDatasetModal } = useDatasetSelect();

  const onMoveFiles = () => {
    openOverlay({
      Overlay: MoveModal,
      props: {
        bizType: BizTypeEnum.MyLibrary,
        folders: selectedFiles,
        isBatch: true,
        onSuccess: () => {
          onRefresh?.();
          onClearSelectedFiles?.();
        }
      }
    });
  };
  const onRemoveFiles = () => {
    selectedFiles?.length &&
      MessageBox.delete({
        content: selectedFiles.some((it) => it.fileType === FileTypeEnum.Folder)
          ? '删除文件夹 则文件夹下的文件会全部删除，删除的全部内容将进入回收站，30天后自动彻底删除。'
          : '删除文件全部内容将进入回收站，30天后自动彻底删除。',
        onOk: () => {
          removeMyFileBatch({
            list: selectedFiles.map((it) => ({ id: it.id, fileType: it.fileType! }))
          }).then(() => {
            onRefresh?.();
            fetchUnreadCount();
            onClearSelectedFiles?.();
          });
        }
      });
  };

  const onDownloadFiles = () => {
    selectedFiles?.length &&
      addDownload({
        bizType: BizTypeEnum.MyLibrary,
        type: DownloadTypeEnum.Batch,
        source: DownloadSourceEnum.Normal,
        parentId: parentId!,
        folderIds: selectedFiles
          .filter((it) => it.fileType === FileTypeEnum.Folder)
          .map((it) => it.id),
        fileIds: selectedFiles.filter((it) => it.fileType === FileTypeEnum.File).map((it) => it.id)
      }).then(() => {
        Toast.success('已添加到下载队列中');
      });
  };

  const onCopyFiles = () => {
    const ids = selectedFiles?.filter((it) => it.fileType === FileTypeEnum.File).map((it) => it.id);
    ids?.length &&
      MessageBox.info({
        title: '复制提示',
        content: '复制文件将在当前列表显示，确定复制选中文件？',
        okCancel: true,
        okText: '确定复制',
        onOk: () => {
          copyFileBatch(ids).then(() => {
            Toast.success('复制成功');
            onRefresh?.();
          });
        }
      });
  };

  const onIntoDataset = async () => {
    const exceedSizeFile = selectedFiles.find(
      (v) => v.fileSize && Number((v.fileSize / 1024 / 1024).toFixed(2)) > 500
    );
    if (selectedFiles?.length > 1000) {
      Toast.warning(`文件数量超过1000个，请取消部分文件再导入`);
    } else if (exceedSizeFile) {
      Toast.warning(`${exceedSizeFile.fileName}大小超过500MB限制`);
    } else {
      openSelectDatasetModal({
        defaultSelectedDatasets: selectedFiles?.map((item) => {
          return { id: item.id };
        }),
        sourceKey: DataSource.Personal,
        cloudFileIds: selectedFiles.map((v) => Number(v.id)),
        isImport: true,
        onRefresh
      });
    }
  };

  return (
    <Flex
      w="100%"
      alignItems="center"
      bgColor="#fff"
      borderRadius="0 0 8px 8px"
      pt={rpxDim(23)}
      pr={rpxDim(24)}
      pb={rpxDim(20)}
    >
      {selectedFiles && selectedFiles?.length > 0 && (
        <ToolBar
          buttons={[
            {
              label: '下载',
              icon: 'download',
              onClick: onDownloadFiles
            },

            {
              label: '复制',
              icon: 'copy',
              disabled: selectedFiles.some((it) => it.fileType === FileTypeEnum.Folder),
              onClick: onCopyFiles
            },
            {
              label: '移动',
              icon: 'drag',
              onClick: onMoveFiles
            },
            {
              label: '删除',
              icon: 'trash',
              onClick: onRemoveFiles
            }
            // v1.3.7暂不开放批量导入
            // {
            //   label: '导入知识库',
            //   icon: 'file_import_line',
            //   disabled: selectedFiles.some((it) => it.fileType === FileTypeEnum.Folder),
            //   onClick: onIntoDataset
            // }
          ]}
        />
      )}

      <Box ml="auto" mr={respDims(16)} whiteSpace="nowrap">
        共{total}项数据
      </Box>
      {PageRender && <PageRender />}
    </Flex>
  );
};

export default Footer;
