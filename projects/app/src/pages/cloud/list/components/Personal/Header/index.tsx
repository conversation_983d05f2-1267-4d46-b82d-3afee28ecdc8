import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import Breadcrumb from '@/pages/cloud/components/Breadcrumb';
import SearchInput from '@/pages/cloud/components/SearchInput';
import { PathItemTypeEnum, SearchTypeEnum } from '@/constants/cloud';
import { respDims, rpxDim } from '@/utils/chakra';
import {
  Box,
  Button,
  Flex,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from '@chakra-ui/react';
import TransferButton from '../../../../components/Transfer/Button';
import { useMemo, useState } from 'react';
import { BreadcrumbItemType, FilePathType, PathItemType } from '@/types/cloud';
import { SearchBarProps } from '@/components/MyTable/types';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import LocalUploadModal from '@/pages/cloud/components/LocalUploadModal';
import FolderModal from '@/pages/cloud/components/FolderModal';
import AllSearchModal from '../../Tenant/AllSearchModal';
import { locationPathType } from '@/types/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import { reportCloudFileUpload } from '@/api/scene';

type QueryType = {
  name: string;
};

const Header = ({
  path,
  query,
  tableInstance,
  onSearch,
  onBackToParent,
  onClickPathItem,
  onClickFolderPath
}: {
  path?: FilePathType;
  onSearch?: (query: QueryType) => void;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
  onClickFolderPath?: (path: locationPathType[], searchContent: string) => void;
} & SearchBarProps) => {
  const { openOverlay } = useOverlayManager();
  const [searchContent, setSearchContent] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [folderPathData, setFolderPathData] = useState<locationPathType[]>([]);
  const { addUpload } = useCloudUploader();

  const breadcrumbList = useMemo(() => {
    const list: (BreadcrumbItemType & { pathItem?: PathItemType })[] = [];

    const folderPath = path?.filter(
      (it: PathItemType) => it.type === PathItemTypeEnum.file
    ) as FilePathType;

    if (folderPath?.length > 1) {
      list.push({
        label: '返回上一级',
        isBack: true
      });
    }

    folderPath?.forEach((pathItem, index) => {
      list.push({
        label: pathItem.file.fileName!,
        pathItem,
        clickable: index < folderPath.length - 1
      });
    });
    return list;
  }, [path]);

  const onAddFolder = () => {
    path &&
      openOverlay({
        Overlay: FolderModal,
        props: {
          bizType: BizTypeEnum.MyLibrary,
          path,
          onSuccess: () => tableInstance.reload()
        }
      });
  };
  console.log(path, 'path');

  const onLocalUpload = (fileType: FileTypeEnum) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    if (fileType === FileTypeEnum.Folder) {
      input.webkitdirectory = true;
    }
    input.onchange = async (event) => {
      const files = Array.from((event.target as HTMLInputElement).files || []);
      // 过滤出非.开头的文件夹/文件
      const filteredFiles = files.filter((file) => {
        // 获取所有文件夹名和文件名
        const pathParts = file.webkitRelativePath
          ? file.webkitRelativePath.split('/')
          : [file.name];
        // 过滤出非.开头的文件夹/文件
        return !pathParts.some((part) => part.startsWith('.'));
      });

      if (filteredFiles.length === 0) {
        Toast.warning('没有可上传的文件');
        return;
      }

      if (files.length > filteredFiles.length) {
        Toast.warning(`已过滤${files.length - filteredFiles.length}个隐藏文件`);
      }

      if (filteredFiles.length && path?.length) {
        try {
          const parent = path[path.length - 1];
          const pathNames = path.map((it) =>
            it.type === PathItemTypeEnum.file ? it.file.fileName : ''
          );
          const parentId = parent.type === PathItemTypeEnum.file ? parent.file.id : '';
          reportCloudFileUpload();

          addUpload({
            path: pathNames,
            parentId,
            files: filteredFiles,
            bizType: BizTypeEnum.MyLibrary,
            oldId: undefined
          });

          Toast.success('已添加到上传队列');
        } catch (error) {
          console.error('Upload failed:', error);
          Toast.error('上传失败');
        }
      }
    };
    input.click();
  };

  const onAllSearch = () => {
    openOverlay({
      Overlay: AllSearchModal,
      props: {
        searchContent: searchContent,
        bizType: BizTypeEnum.MyLibrary,
        onSearchContent: (content) => {
          onSearch?.({ ...query, searchKey: content });
          setSearchContent(content);
        },
        onFolderPath: (data, searchContent) => {
          setFolderPathData(data);
          onClickFolderPath?.(data, searchContent);
        },
        onClose: () => {
          onClose;
        }
      }
    });
  };

  return (
    <Box w="100%">
      <Flex alignItems="center" w="100%">
        <Flex mr="auto" alignItems="center">
          <SearchInput
            border="1px solid #fff"
            bgColor="#eeeefe"
            searchIconDirect="left"
            isShowCloseIcon
            mr="auto"
            type={SearchTypeEnum.file}
            placeholder="通过文件名、正文搜索文档"
            w={respDims(695)}
            onChange={(e) => setSearchContent(e)}
            onClearSearch={() => {
              onSearch?.({ ...query, searchKey: '' }), setSearchContent('');
            }}
            onSearch={(e) => onSearch?.({ ...query, searchKey: e })}
            searchBgColor="#eeeefe"
          />
          {searchContent && (
            <>
              <Box
                fontWeight="400"
                fontSize="15px"
                color="#000000"
                marginRight={respDims(4)}
                marginLeft={respDims(13)}
              >
                无想要结果？
              </Box>
              <Popover
                trigger="click"
                placement="bottom"
                isOpen={isOpen}
                onOpen={onOpen}
                onClose={onClose}
              >
                <PopoverTrigger>
                  <Box
                    fontWeight="500"
                    fontSize="15px"
                    color="primary.500"
                    cursor="pointer"
                    onClick={() => {
                      onAllSearch();
                    }}
                  >
                    开始全局搜索
                  </Box>
                </PopoverTrigger>

                <PopoverContent
                  w={respDims('697fpx')}
                  h={respDims('688fpx')}
                  mt="13px"
                  mr="118px"
                  boxShadow="0px 3px 10px 0px rgba(0,0,0,0.11)"
                >
                  {isOpen && (
                    <AllSearchModal
                      onClose={onClose}
                      bizType={BizTypeEnum.MyLibrary}
                      onSearchContent={(content) => {
                        onSearch?.({ ...query, searchKey: content }), setSearchContent(content);
                      }}
                      onFolderPath={(data, searchContent) => {
                        setFolderPathData(data);
                        setSearchContent(searchContent);
                        onClickFolderPath?.(data, searchContent);
                      }}
                      searchContent={searchContent}
                    />
                  )}
                </PopoverContent>
              </Popover>
            </>
          )}
        </Flex>

        <Box>
          <MyMenu
            Button={
              <Button
                variant="solid"
                colorScheme="primary"
                px={respDims(20)}
                fontSize={respDims('14fpx')}
                borderRadius={respDims(8)}
                h={respDims(36)}
              >
                <SvgIcon name="upload" w={respDims('14fpx')} h={respDims('14fpx')} />
                <Box ml={respDims(8)}>上传</Box>
              </Button>
            }
            menuList={[
              {
                label: '文件上传',
                onClick: () => onLocalUpload(FileTypeEnum.File)
              },
              {
                label: '文件夹上传',
                onClick: () => onLocalUpload(FileTypeEnum.Folder)
              }
            ]}
          />
        </Box>
        <Button
          ml={respDims(13)}
          variant="outline"
          colorScheme="primary"
          px={respDims(20)}
          fontSize={respDims('14fpx')}
          borderRadius={respDims(8)}
          onClick={onAddFolder}
          h={respDims(36)}
          bgColor="#fff"
        >
          <SvgIcon name="folder" w={respDims('14fpx')} h={respDims('14fpx')} />
          <Box ml={respDims(8)}>新建文件夹</Box>
        </Button>
        <TransferButton ml={respDims(10)} />
      </Flex>

      <Flex
        borderRadius="8px 8px 0 0"
        w="100%"
        mt={rpxDim(22)}
        pt={rpxDim(22)}
        pl={rpxDim(20)}
        pb={rpxDim(12)}
        bgColor="#fff"
      >
        <Breadcrumb
          list={breadcrumbList}
          onClickItem={(item) => item.pathItem && onClickPathItem?.(item.pathItem)}
          onBack={onBackToParent}
        />
      </Flex>
    </Box>
  );
};

export default Header;
