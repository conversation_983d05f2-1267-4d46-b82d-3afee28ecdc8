import { respDims } from '@/utils/chakra';
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  Box,
  Text,
  useDisclosure,
  PopoverProps,
  Portal
} from '@chakra-ui/react';
import { ReactNode, useEffect } from 'react';

interface LkTooltipProps extends Omit<PopoverProps, 'children'> {
  children: ReactNode;
  header?: string;
  content: string | ReactNode;
  width?: string | number | string[];
  trigger?: 'hover' | 'click';
  contentBg?: string;
  onOpened?: () => void;
}

export const LkTooltip = ({
  children,
  header,
  content,
  width = respDims(406),
  trigger = 'hover',
  placement = 'right',
  contentBg = '#fff',
  onOpened,
  ...props
}: LkTooltipProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  useEffect(() => {
    if (isOpen) {
      onOpened?.();
    }
  }, [isOpen]);

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      trigger={trigger}
      placement={placement}
      {...props}
    >
      <PopoverTrigger>{children}</PopoverTrigger>
      <Portal>
        <PopoverContent
          maxWidth={width}
          width="auto"
          boxShadow="0px 2px 8px rgba(0, 0, 0, 0.15)"
          border="1px solid #E2E8F0"
          borderRadius="8px"
          p={0}
        >
          <PopoverBody p={0}>
            {header && (
              <Box borderBottom="1px solid #F3F4F6" p={respDims(16)}>
                <Text fontWeight="500" fontSize={respDims('16fpx')}>
                  {header}
                </Text>
              </Box>
            )}
            <Box
              p={respDims(12)}
              fontSize={respDims('14fpx')}
              lineHeight="1.5"
              borderRadius={8}
              bg={contentBg}
            >
              {content}
            </Box>
          </PopoverBody>
        </PopoverContent>
      </Portal>
    </Popover>
  );
};

export default LkTooltip;
