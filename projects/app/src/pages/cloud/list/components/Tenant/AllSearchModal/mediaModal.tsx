import { Box, Center, Flex, Input, InputGroup, InputRightElement } from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import LayoutOverlay from '@/components/LayoutOverlay';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { fileSearchMediaWord } from '@/api/cloud';
import { FileSearchMediaWordType } from '@/types/api/cloud';
import { StepProps, Steps } from 'antd';

const useDebounce = (callback: (...args: any[]) => void, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedCallback = (...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
};

const supportedFormats = [
  'mp3',
  'wav',
  'ogg',
  'oga',
  'flac',
  'aac',
  'm4a', // 音频格式
  'mp4',
  'webm',
  'ogv',
  'mov',
  'avi',
  'mkv' // 视频格式
];

const MediaModal = ({
  type,
  iframeSrc,
  fileId,
  searchKey,
  onClose
}: {
  type: string;
  iframeSrc: string;
  searchKey?: string;
  fileId?: string;
  onClose: () => void;
}) => {
  const iFrameRef = useRef<HTMLIFrameElement | null>(null);
  const [searchMediaWord, setSearchMediaWord] = useState<FileSearchMediaWordType>();
  const [searchContent, setSearchContent] = useState('');
  const [current, setCurrent] = useState<number | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60000);
    const seconds = ((time % 60000) / 1000).toFixed(0);
    return `${minutes}:${seconds.padStart(2, '0')}`;
  };

  const highlightText = (text: string, search: string) => {
    if (!search) return text;
    const regex = new RegExp(`(${search})`, 'gi');
    return text.replace(regex, '<Box style="color: #9497f2;font-weight:600">$1</Box>');
  };

  const onChange = (value: number) => {
    setCurrent(value);
    // 定位到视频的相应时间点
    if (searchMediaWord?.matchedWords[value] && videoRef.current) {
      const startTime = searchMediaWord.matchedWords[value].start_time;
      if (videoRef.current) {
        videoRef.current.currentTime = startTime / 1000; // 转换为秒
      }
      if (audioRef.current) {
        audioRef.current.currentTime = startTime / 1000; // 转换为秒
      }
    }
  };

  const generateSteps = (): StepProps[] => {
    return (
      searchMediaWord?.matchedWords.map((word, index) => ({
        title: `${formatTime(word.start_time)}: ${word.word}`,
        status: index < current! ? 'finish' : index === current ? 'process' : 'wait'
      })) || []
    );
  };

  const isAudioFormat = (url: string) => {
    const extension = getFileExtension(url);
    return ['mp3', 'wav', 'ogg', 'oga', 'flac', 'aac', 'm4a'].includes(extension);
  };

  const getFileExtension = (url: string) => {
    return url.split('?')[0].split('.').pop()?.toLowerCase() ?? '';
  };

  const isSupportedFormat = (url: string) => {
    const extension = getFileExtension(url);
    return supportedFormats.includes(extension);
  };

  const debouncedSearch = useDebounce((searchKey: string) => {
    if (searchKey && type === 'inside' && fileId) {
      fileSearchMediaWord({ fileId, searchKey })
        .then((data) => {
          setSearchMediaWord(data);
        })
        .catch((err) => {
          setSearchMediaWord(undefined);
        });
    }
  }, 500);

  useEffect(() => {
    if (type === 'inside') {
      fileSearchMediaWord({ fileId: fileId!, searchKey: searchKey! })
        .then((data) => {
          setSearchMediaWord(data);
        })
        .catch((err) => {
          setSearchMediaWord(undefined);
        });
    }
    if (searchKey) {
      setSearchContent(searchKey);
    }
  }, [type, searchKey]);

  useEffect(() => {
    debouncedSearch(searchContent);
  }, [searchContent]);
  return (
    <LayoutOverlay onClose={onClose}>
      <Flex w="100%" h="100%" position="relative" bgColor="#F8FAFC">
        <Box
          w={respDims(36)}
          h={respDims(36)}
          bg="#CC525F"
          borderRadius="8px"
          position="absolute"
          top="12px"
          right="24px"
          display="flex"
          zIndex="999"
          justifyContent="center"
          alignItems="center"
          cursor="pointer"
          onClick={onClose}
        >
          <SvgIcon name="close" w="12px" h="12px" color="#fff"></SvgIcon>
        </Box>
        {type === 'inside' ? (
          <Flex ml="190px" w="100%" marginTop="24px">
            {searchMediaWord?.file?.fileUrl && (
              <Flex w="100%" maxW="916px" p="19px 35px" bgColor="#fff" mr="18px" flexDir="column">
                {isSupportedFormat(searchMediaWord.file.fileUrl) &&
                  (isAudioFormat(searchMediaWord.file.fileUrl) ? (
                    <audio
                      controls
                      ref={audioRef}
                      src={searchMediaWord.file.fileUrl}
                      style={{
                        width: '100%',
                        minHeight: '80px',
                        maxHeight: '500px',
                        marginBottom: '40px'
                      }}
                    />
                  ) : (
                    <video
                      controls
                      ref={videoRef}
                      src={searchMediaWord.file.fileUrl}
                      style={{
                        width: '100%',
                        minHeight: '80px',
                        maxHeight: '500px',
                        marginBottom: '40px'
                      }}
                    />
                  ))}
                <Box
                  overflow="auto"
                  dangerouslySetInnerHTML={{
                    __html: highlightText(searchMediaWord?.file_content || '', searchContent)
                  }}
                />
              </Flex>
            )}

            <Flex maxW="302px" w="100%" flexDir="column">
              <Box bgColor="#fff" p="17px 20px 33px 16px">
                <InputGroup mr={respDims(10)} mb="30px">
                  <Input
                    bgColor="rgba(0,0,0,0.03)"
                    borderRadius="8px"
                    placeholder="搜索关键字"
                    value={searchContent}
                    h={respDims(36, 34)}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSearchContent(value);
                      if (!value) {
                        setSearchMediaWord((prev) => {
                          if (prev) {
                            return { ...prev, matchedWords: [] };
                          }
                          return prev;
                        });
                        setCurrent(null); // 清空当前步骤
                      }
                    }}
                  />
                  <InputRightElement h="100%">
                    <SvgIcon
                      color="#909399"
                      name="close"
                      w={respDims(20)}
                      h={respDims(20)}
                      onClick={() => setSearchContent('')}
                    />
                  </InputRightElement>
                </InputGroup>

                {searchContent ? (
                  <Steps
                    current={current!}
                    onChange={onChange}
                    size="small"
                    style={{ marginLeft: '6px' }}
                    direction="vertical"
                    items={generateSteps()}
                  />
                ) : (
                  <Center color="#a9a9ac">暂无数据</Center>
                )}
              </Box>
            </Flex>
          </Flex>
        ) : (
          <Flex
            w="100%"
            h="100%"
            transform="translate(-50%, -50%)"
            justifyContent="center"
            alignItems="center"
            position="absolute"
            top="50%"
            left="50%"
            overflow="hidden"
          >
            <Box
              ref={iFrameRef}
              as="iframe"
              w="90%"
              h="90%"
              css={{
                '.AILayout-bg': {
                  background: 'none!important'
                }
              }}
              src={iframeSrc}
            />
          </Flex>
        )}
      </Flex>
    </LayoutOverlay>
  );
};

export default MediaModal;

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}
