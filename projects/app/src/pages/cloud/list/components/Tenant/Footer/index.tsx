import { copyFileBatch, removeSpaceFileBatch } from '@/api/cloud';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { FooterComponentProps } from '@/components/MyTable/types';
import { BizTypeEnum, FileTypeEnum, PrivilegeEnum } from '@/constants/api/cloud';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import ToolBar from '@/pages/cloud/components/ToolBar';
import { FileType } from '@/types/api/cloud';
import { respDims, rpxDim } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex } from '@chakra-ui/react';
import BatchShareModal from '../BatchShareModal/index';
import { useMemo } from 'react';
import { useNotificationStore } from '@/store/useTificationContext';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';
import useDatasetSelect from '@/pages/dataset/list/hooks/useDatasetSelect';
import { DataSource } from '@/constants/common';
import MoveModal from '../MoveModal';
import { useUserStore } from '@/store/useUserStore';

const Footer = ({
  selectedFiles = [],
  parentId,
  privilege,
  total,
  PageRender,
  onRefresh,
  onClearSelectedFiles
}: {
  selectedFiles?: FileType[];
  parentId?: string;
  privilege: PrivilegeEnum;
  onRefresh?: () => void;
  onClearSelectedFiles?: () => void;
} & FooterComponentProps) => {
  const { openOverlay } = useOverlayManager();

  const { addDownload } = useCloudDownloader();

  const { fetchUnreadCount } = useNotificationStore();

  const { open: openSelectDatasetModal } = useDatasetSelect();

  const { userInfo } = useUserStore();

  const selectedPrivilege = useMemo(() => {
    if (!selectedFiles?.length) {
      return PrivilegeEnum.View;
    }
    if (privilege >= PrivilegeEnum.Manage) {
      return privilege;
    }
    const commonPrivilege = selectedFiles.reduce((min: PrivilegeEnum | undefined, file) => {
      if (!file.privileges?.length) {
        return PrivilegeEnum.View;
      }
      const filePrivilege = file.privileges.reduce(
        (max, cur) => (cur > max ? cur : max),
        PrivilegeEnum.View
      );
      return min === undefined || filePrivilege < min ? filePrivilege : min;
    }, undefined);
    return commonPrivilege !== undefined && commonPrivilege > privilege
      ? commonPrivilege
      : privilege;
  }, [selectedFiles, privilege]);

  const onMoveFiles = () => {
    openOverlay({
      Overlay: MoveModal,
      props: {
        bizType: BizTypeEnum.TenantLibrary,
        folders: selectedFiles,
        isBatch: true,
        onSuccess: () => {
          onRefresh?.();
          onClearSelectedFiles?.();
        }
      }
    });
  };
  const onRemoveFiles = () => {
    selectedFiles?.length &&
      MessageBox.delete({
        content: selectedFiles.some((it) => it.fileType === FileTypeEnum.Folder)
          ? '删除文件夹 则文件夹下的文件会全部删除，删除的全部内容将进入回收站，30天后自动彻底删除。'
          : '删除文件全部内容将进入回收站，30天后自动彻底删除。',
        tip: '提示：如果你删除的内容中有属于他人的，其所有者将收到通知',
        onOk: () => {
          removeSpaceFileBatch({
            list: selectedFiles.map((it) => ({ id: it.id, fileType: it.fileType! }))
          }).then(() => {
            onRefresh?.();
            fetchUnreadCount();
            onClearSelectedFiles?.();
          });
        }
      });
  };

  const onCopyFiles = () => {
    const ids = selectedFiles?.filter((it) => it.fileType === FileTypeEnum.File).map((it) => it.id);
    ids?.length &&
      MessageBox.info({
        title: '复制提示',
        content: '复制文件将在当前列表显示，确定复制选中文件？',
        okCancel: true,
        okText: '确定复制',
        onOk: () => {
          copyFileBatch(ids).then(() => {
            Toast.success('复制成功');
            onRefresh?.();
          });
        }
      });
  };

  const onDownloadFiles = () => {
    selectedFiles?.length &&
      addDownload({
        bizType: BizTypeEnum.TenantLibrary,
        type: DownloadTypeEnum.Batch,
        source: DownloadSourceEnum.Normal,
        parentId: parentId!,
        folderIds: selectedFiles
          .filter((it) => it.fileType === FileTypeEnum.Folder)
          .map((it) => it.id),
        fileIds: selectedFiles.filter((it) => it.fileType === FileTypeEnum.File).map((it) => it.id)
      }).then(() => {
        Toast.success('已添加到下载队列中');
      });
  };

  const onShareFile = () => {
    openOverlay({
      Overlay: BatchShareModal,
      props: {
        onSuccess: () => {
          onRefresh?.();
          onClearSelectedFiles?.();
        },
        onClose() {},
        selectedData: selectedFiles!
      }
    });
  };

  const onIntoDataset = async () => {
    const exceedSizeFile = selectedFiles.find(
      (v) => v.fileSize && Number((v.fileSize / 1024 / 1024).toFixed(2)) > 500
    );
    if (selectedFiles?.length > 1000) {
      Toast.warning(`文件数量超过1000个，请取消部分文件再导入`);
    } else if (exceedSizeFile) {
      Toast.warning(`${exceedSizeFile.fileName}大小超过500MB限制`);
    } else {
      openSelectDatasetModal({
        defaultSelectedDatasets: selectedFiles?.map((item) => {
          return { id: item.id };
        }),
        sourceKey: DataSource.Tenant,
        cloudFileIds: selectedFiles.map((v) => Number(v.id)),
        isImport: true,
        onRefresh
      });
    }
  };

  return (
    <Flex
      w="100%"
      alignItems="center"
      bgColor="#fff"
      borderRadius="0 0 8px 8px"
      pl={rpxDim(12)}
      pt={rpxDim(23)}
      pr={rpxDim(12)}
      pb={rpxDim(20)}
    >
      {!!selectedFiles?.length && (
        <ToolBar
          buttons={[
            {
              label: '下载',
              icon: 'download',
              onClick: onDownloadFiles
            },
            {
              label: '共享',
              icon: 'share2',
              disabled: selectedPrivilege < PrivilegeEnum.Manage || !selectedFiles?.length,
              onClick: onShareFile
            },
            {
              label: '复制',
              icon: 'copy',
              disabled:
                selectedPrivilege < PrivilegeEnum.Owner ||
                !selectedFiles?.length ||
                selectedFiles.some((it) => it.fileType === FileTypeEnum.Folder),
              onClick: onCopyFiles
            },
            {
              label: '移动',
              icon: 'drag',
              disabled: selectedPrivilege < PrivilegeEnum.Owner || !selectedFiles?.length,
              onClick: onMoveFiles
            },
            {
              label: '删除',
              icon: 'trash',
              disabled:
                (selectedPrivilege < PrivilegeEnum.Owner || !selectedFiles?.length) &&
                selectedFiles.some((file) => file?.tmbId != userInfo?.tmbId),
              onClick: onRemoveFiles
            }
            // v1.3.7暂不开放批量导入
            // {
            //   label: '导入知识库',
            //   icon: 'file_import_line',
            //   disabled: selectedFiles.some((it) => it.fileType === FileTypeEnum.Folder),
            //   onClick: onIntoDataset
            // }
          ]}
        />
      )}
      <Box ml="auto" mr={respDims(16)} whiteSpace="nowrap">
        共{total}项数据
      </Box>
      {PageRender && <PageRender />}
    </Flex>
  );
};

export default Footer;
