import { useOverlayManager } from '@/hooks/useOverlayManager';
import { Box, Button, Flex, Switch, Tooltip } from '@chakra-ui/react';
import AuditModal from '../AuditModal';
import MyTable from '@/components/MyTable';
import SearchInput from '@/pages/cloud/components/SearchInput';
import {
  cloudSpaceAuditSwitchDetail,
  getSpaceAuditorList,
  spaceAuditSwitchUpdate
} from '@/api/cloud';
import { TableProps } from 'antd';
import dayjs from 'dayjs';
import SvgIcon from '@/components/SvgIcon';
import { MyTableRef } from '@/components/MyTable/types';
import { useRef, useState } from 'react';
import { respDims, rpxDim } from '@/utils/chakra';
import { useQuery } from '@tanstack/react-query';
import { CloudSpaceAuditSwitchDetailType } from '@/types/api/cloud';
import { StatusEnum } from '@/constants/api/cloud';
import MyTooltip from '@/components/MyTooltip';
import { useTenantStore } from '@/store/useTenantStore';

type QueryType = {
  searchKey: string;
};

const AuditPanel = () => {
  const { openOverlay, OverlayContainer } = useOverlayManager();

  const { industryAlias } = useTenantStore();

  const tableRef = useRef<MyTableRef>(null);
  const [searchKey, setSearchKey] = useState('');
  const [isOpenChecked, setIsOpenedChecked] = useState(false);
  const [auditSwitchDetail, setAuditSwitchDetail] = useState<CloudSpaceAuditSwitchDetailType>();

  const onAddAudit = () => {
    openOverlay({
      Overlay: AuditModal,
      props: {
        isAdd: true,
        onSuccess: () => tableRef.current?.reload(),
        onClose() {}
      }
    });
  };

  useQuery(['auditPanelDetail'], () => cloudSpaceAuditSwitchDetail(), {
    onSuccess: (data) => {
      if (data) {
        setAuditSwitchDetail(data);
        setIsOpenedChecked(Number(data.status) === StatusEnum.Active ? true : false);
      }
    }
  });

  const columns: TableProps['columns'] = [
    {
      title: '审核人',
      width: 120,
      key: 'userName',
      dataIndex: 'userName'
    },
    {
      title: '审核空间',
      dataIndex: 'spaceNames',
      key: 'spaceNames',
      width: 140,
      render: (value) => (
        <Tooltip label={value.join(',')} aria-label="space-names-tooltip">
          <Box whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis" maxWidth="160px">
            {value.join(',')}
          </Box>
        </Tooltip>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 120,
      render: (value) => <>{value ? dayjs(value).format('YYYY/MM/DD HH:mm') : '-'}</>
    }
  ];

  const onSearch = (val: QueryType) => {
    setSearchKey(val.searchKey);
  };

  return (
    <Flex w="100%" h="100%" flexDir="column">
      <Flex
        m="2px 0 17px 0"
        justifyContent="center"
        bgColor="#f9fafc"
        p="12px 0 8px 0"
        borderRadius="8px"
      >
        <Box mr="10px" fontSize="15px" color="#303133" fontWeight="500">
          {industryAlias}空间资料审核开关
        </Box>
        <MyTooltip label={!isOpenChecked ? '关闭审核' : '开启审核'}>
          <Flex
            borderRadius={rpxDim(8)}
            style={{ position: 'relative' }} // 确保父容器相对定位
          >
            <Box
              style={{
                cursor: 'pointer',
                transition: 'background-color 0.3s',
                borderRadius: '8px', // 设置圆角
                padding: '8px', // 增加点击区域
                width: '32px', // 确保背景不超过 32px
                height: '32px', // 确保背景不超过 32px
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'transparent' // 初始背景色设置为透明
              }}
            >
              <Switch
                pb="10px"
                isChecked={isOpenChecked}
                onChange={(e) => {
                  spaceAuditSwitchUpdate({
                    id: auditSwitchDetail?.id!,
                    status: e.target.checked ? StatusEnum.Active : StatusEnum.Inactive
                  });
                  setIsOpenedChecked(e.target.checked);
                }}
              />
            </Box>
          </Flex>
        </MyTooltip>
      </Flex>
      <Box borderBottom="1px solid #E7E7E7" w="100%" m="2px 0 20px 0"></Box>
      <Flex>
        <SearchInput
          bgColor="#f7f7f7"
          borderRadius="8px"
          border="none"
          h={respDims(43)}
          flex="1"
          onSearch={(e) => onSearch?.({ searchKey: e })}
          placeholder="请输入审核人名称"
        />
        {/* <Button ml="16px" onClick={onAddAudit}>
          <SvgIcon name="settings" w="12px" h="12px" />
          <Box ml="8px">新增资料审核</Box>
        </Button> */}
      </Flex>
      <Box flex="1 0 0" overflow="hidden">
        <MyTable
          ref={tableRef}
          rowKey="rowKey"
          api={getSpaceAuditorList}
          pageConfig={{ showPaginate: false }}
          defaultQuery={{ searchKey }}
          columns={columns}
          boxStyle={{ px: 0, py: 0 }}
        />
      </Box>
      <OverlayContainer></OverlayContainer>
    </Flex>
  );
};

export default AuditPanel;
