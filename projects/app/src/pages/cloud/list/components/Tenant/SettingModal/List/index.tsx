import { removeSpace } from '@/api/cloud';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import { SpaceType } from '@/types/api/cloud';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex } from '@chakra-ui/react';
import { navPersonal, navTenant } from '@/constants/cloud';
import { NavType } from '@/types/cloud';
const List = ({
  settingData,
  onAdd,
  space,
  onClose,
  onCloseModal,
  EditSpaceSuccess,
  onNavChange
}: {
  settingData?: { id: string; name: string };
  onAdd: () => void;
  space?: SpaceType;
  onClose?: () => void;
  EditSpaceSuccess?: () => void;
  onCloseModal?: () => void;
  onNavChange?: (nav?: NavType) => void;
}) => {
  const onRemoveSpace = useCallbackRef(() => {
    MessageBox.delete({
      title: '删除提示',
      content:
        '删除空间 则空间下的文件会全部删除，删除的全部内容将进入回收站，30天后自动彻底删除。',
      tip: '提示：如果你删除的内容中有属于他人的，其所有者将收到通知',
      okButtonProps: {
        style: { backgroundColor: '#7D4DFF', borderColor: '#7D4DFF' }
      },
      cancelButtonProps: {
        onMouseEnter: (e) => {
          e.currentTarget.style.borderColor = '#7D4DFF';
          e.currentTarget.style.color = '#7D4DFF';
        },
        onMouseLeave: (e) => {
          e.currentTarget.style.borderColor = '#d9d9d9';
          e.currentTarget.style.color = 'rgba(0, 0, 0, 0.88)';
        }
      },
      onOk: () => {
        removeSpace(space?.id!).then(() => {
          Toast.success('删除成功');
          onNavChange?.(navPersonal);
          setTimeout(() => {
            onNavChange?.(navTenant);
          }, 20);
          EditSpaceSuccess?.();
          onCloseModal?.();
        });
      }
    });
  });

  return (
    <Flex direction="column" p="24px 28px" pb={0}>
      <Flex w="100%" mt="16px" direction="column">
        <Flex
          direction="column"
          p="14px 17px 16px 16px"
          border="1px solid #E5E7EB"
          borderRadius="8px"
          mr="11px"
          w="100%"
          mb="14px"
          _hover={{ bgColor: '#F6F6F6' }}
          onClick={() => {
            onAdd();
          }}
          cursor="pointer"
        >
          <Box fontSize="15px" color="#1D2129" fontWeight="500">
            转让空间
          </Box>
          <Box fontSize="13px" color="#606266" mt="8px" fontWeight="400">
            转让空间后，您将转变为空间成员
          </Box>
        </Flex>
        <Flex
          w="100%"
          direction="column"
          p="14px 17px 16px 16px"
          border="1px solid #E5E7EB"
          borderRadius="8px"
          cursor="pointer"
          _hover={{ bgColor: '#F6F6F6' }}
          onClick={() => onRemoveSpace()}
        >
          <Box fontSize="15px" color="#F53F3F" fontWeight="500">
            删除空间
          </Box>
          <Box fontSize="13px" color="#606266" mt="8px" fontWeight="400">
            删除空间后，成员将无法访问
          </Box>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default List;
