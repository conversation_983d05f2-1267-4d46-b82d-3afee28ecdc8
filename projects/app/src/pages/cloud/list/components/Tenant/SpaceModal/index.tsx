import { addSpace, spaceShareParentCloudSpaceList, updateSpace } from '@/api/cloud';
import { getTenantUserPage } from '@/api/tenant';
import { tenantEvaluateIconList } from '@/api/tenant/evaluate/rule';
import MyModal from '@/components/MyModal';
import { TenantMemberStatusEnum } from '@/constants/api/tenant';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { SpaceShareParentCloudSpaceListType, SpaceType } from '@/types/api/cloud';
import { IconListType } from '@/types/api/tenant/evaluate/rule';
import { BaseModalProps } from '@/types/cloud';
import { getListFromPage } from '@/utils/api';
import { respDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex, Grid } from '@chakra-ui/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Button, Form, Input } from 'antd';
import { useEffect, useState } from 'react';
import UserNameModal from '../UserNameModal';
import { SharedPersonnelTypeParams } from '@/constants/api/cloud';

type FormType = {
  spaceName: string;
  description?: string;
};

const colors = ['#9acab5', '#9ea7bb', '#4f7af7', '#f19f68', '#c8ca73'];
const SpaceModal = ({
  parentId,
  space,
  onClose,
  onSuccess
}: {
  parentId?: string;
  space?: SpaceType;
  onClose?: () => void;
  onSuccess: (data: { spaceName: string }) => void;
} & Omit<BaseModalProps, 'onSuccess'>) => {
  const [form] = Form.useForm<FormType>();
  const [selectedUserName, setSelectedUserName] = useState<string | null>(null);
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const [initialValues] = useState<FormType>({
    spaceName: space?.spaceName || '',
    description: space?.description || ''
  });
  const [memberList, setMemberList] = useState<IconListType[]>([]);
  const [spaceShareParentCloudSpaceData, setSpaceShareParentCloudSpaceData] = useState<
    SpaceShareParentCloudSpaceListType[]
  >([]);

  const { data: tenantUsers } = useQuery(['tenantUsers'], () =>
    getListFromPage(getTenantUserPage).then((res) =>
      res.filter((it) => it.status !== TenantMemberStatusEnum.Forbidden)
    )
  );
  const fetchSpaceShareParentCloudSpaceData = useQuery(
    ['spaceShareParentCloudSpaceData'],
    () => spaceShareParentCloudSpaceList({ spaceId: parentId! }),
    {
      onSuccess: (data) => {
        setSpaceShareParentCloudSpaceData(data || []);
      }
    }
  );

  const { refetch } = useQuery(['iconList'], () => tenantEvaluateIconList(), {
    onSuccess(data) {
      setMemberList(data || []);
    }
  });

  const onOpenSelectUserName = () => {
    openOverlay({
      Overlay: UserNameModal,
      props: {
        selectSpaceData: spaceShareParentCloudSpaceData,
        targetKeysData: (data) => {
          const newSpaceShareParentCloudSpaceData = [
            ...data.map((item) => ({
              id: item.key,
              tmbId: item.type === 1 ? item.key.split('-')[1] : 0,
              deptId: item.type === 2 ? item.key.split('-')[1] : 0,
              objectType: item.type,
              privilege: Number(item.privilege!),
              userName: item.title
            }))
          ];
          setSpaceShareParentCloudSpaceData(newSpaceShareParentCloudSpaceData);
        },
        onClose: () => {}
      }
    });
  };

  const onShareParent = (id: string) => {
    if (selectedUserName === id) {
      setSelectedUserName(null);
    } else {
      setSelectedUserName(id);
    }
  };

  useEffect(() => {
    refetch();
  }, [refetch]);

  const { mutate: onSubmit } = useMutation({
    mutationFn: (data: FormType) => {
      const shareObjects = spaceShareParentCloudSpaceData?.map((it) => ({
        tmbId: it.objectType === SharedPersonnelTypeParams.person ? Number(it.tmbId) : undefined,
        deptId: it.objectType === SharedPersonnelTypeParams.dept ? Number(it.deptId) : undefined,
        objectType: it.objectType,
        privilege: it.privilege
      }));
      if (spaceShareParentCloudSpaceData?.length === 0) {
        Toast.info('请选择空间成员');
        return Promise.reject();
      }
      if (space) {
        return updateSpace({ id: space.id, ...data }).then(() => data);
      } else if (parentId) {
        return addSpace(Object.assign({ parentId, ...data, shareObjects })).then(() => data);
      }
      return Promise.reject();
    },
    onSuccess: (data) => {
      Toast.success('操作成功');
      onSuccess?.({ ...data });
      onClose?.();
    }
  });

  return (
    <MyModal
      title={space ? '编辑空间' : '添加空间'}
      isOpen
      isCentered
      hideCloseButton
      onClose={onClose}
    >
      <Box
        w={respDims(662)}
        px={respDims(34)}
        py={respDims(24)}
        overflow="hidden"
        css={{
          '& .ant-form-item-label': {
            width: '100px !important'
          }
        }}
      >
        <Form
          layout="vertical"
          form={form}
          initialValues={initialValues}
          colon={false}
          onFinish={onSubmit}
        >
          <Form.Item
            name="spaceName"
            label="空间名称"
            rules={[
              { required: true, message: '请输入空间名称' },
              { min: 2, message: '空间名称至少需要两个字符' },
              { max: 10, message: '空间名称最多可以输入十个字符' }
            ]}
          >
            <Input
              style={{
                backgroundColor: '#F6F6F6'
              }}
              placeholder="请输入空间名称"
              autoComplete="off"
              autoFocus
              maxLength={10}
            />
          </Form.Item>

          <Form.Item name="description" label="空间介绍">
            <Input.TextArea
              style={{ minHeight: '60px', backgroundColor: '#F6F6F6' }}
              placeholder="请输入空间介绍"
              maxLength={200}
              minLength={2}
            />
          </Form.Item>

          {!space?.id && (
            <Form.Item label="空间成员">
              <Grid templateColumns="repeat(9, 1fr)" gap={2}>
                <Box
                  as="button"
                  type="button"
                  w={['40px']}
                  h={['14px']}
                  borderRadius="50%"
                  border="1px dashed #764ff6"
                  color="#764ff6"
                  cursor="pointer"
                  fontSize="24px"
                  mt="1px"
                  pb="40px"
                  onClick={() => {
                    onOpenSelectUserName();
                  }}
                >
                  +
                </Box>

                {spaceShareParentCloudSpaceData.length > 0 ? (
                  <Flex alignItems="center">
                    {spaceShareParentCloudSpaceData.slice(0, 6).map((it, index) => (
                      <Flex
                        w="40px"
                        h="40px"
                        key={it.id}
                        onClick={() => onShareParent(it.id)}
                        borderRadius="full"
                        overflow="hidden"
                        cursor="pointer"
                        mr="7px"
                        alignItems="center"
                        p="4px"
                        justifyContent="center"
                        bgColor={colors[index % colors.length]}
                      >
                        <Box
                          color="#fff"
                          fontSize="11px"
                          overflow="hidden"
                          whiteSpace="nowrap"
                          maxWidth="100%"
                        >
                          {it.userName && it.userName.length > 3
                            ? it.userName.slice(0, 3)
                            : it.userName}
                        </Box>
                      </Flex>
                    ))}
                    <Flex w="50px" fontSize="14px" color="#303133" fontWeight="400">
                      {spaceShareParentCloudSpaceData.length}人
                    </Flex>
                  </Flex>
                ) : (
                  <></>
                )}
              </Grid>
            </Form.Item>
          )}

          <Flex justify="end">
            <Button htmlType="button" onClick={onClose}>
              取消
            </Button>
            <Button style={{ marginLeft: '16px' }} type="primary" htmlType="submit">
              确定
            </Button>
          </Flex>
        </Form>
      </Box>
      <OverlayContainer />
    </MyModal>
  );
};

export default SpaceModal;
