import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import Breadcrumb from '@/pages/cloud/components/Breadcrumb';
import SearchInput from '@/pages/cloud/components/SearchInput';
import { navPersonal, NavTypeEnum, PathItemTypeEnum, SearchTypeEnum } from '@/constants/cloud';
import useSpaceTree, { SpaceTreeDataType } from '@/pages/cloud/hooks/useSpaceTree';
import { respDims, rpxDim } from '@/utils/chakra';
import {
  Box,
  Button,
  Flex,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from '@chakra-ui/react';
import { Badge } from 'antd';
import FolderModal from '../../../../components/FolderModal';
import TransferButton from '../../../../components/Transfer/Button';
import SettingsModal from '../SettingsModal';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { LkTooltip } from './LkTooltip';
import {
  BreadcrumbItemType,
  FilePathType,
  PathType,
  PathSpaceType,
  PathItemType,
  NavType,
  PathFileType
} from '@/types/cloud';
import { SearchBarProps } from '@/components/MyTable/types';
import { BizTypeEnum, FileTypeEnum, PrivilegeEnum } from '@/constants/api/cloud';
import { useQuery } from '@tanstack/react-query';
import { getAuditCount, getSpaceList, getSpaceAuditInfo } from '@/api/cloud';
import LocalUploadModal from '@/pages/cloud/components/LocalUploadModal';
import CloudUploadModal from '@/pages/cloud/components/CloudUploadModal';
import { useUserStore } from '@/store/useUserStore';
import { UserRoleEnum } from '@/constants/api/auth';
import AllSearchModal from '../AllSearchModal';
import { locationPathType, SpaceType } from '@/types/api/cloud';
import { Tooltip } from 'antd';
import MyTooltip from '@/components/MyTooltip';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import { Toast } from '@/utils/ui/toast';
import { useNotificationStore } from '@/store/useTificationContext';
import { useTenantStore } from '@/store/useTenantStore';

const Header = ({
  parentId,
  path,
  privilege,
  query,
  tableInstance,
  onSearch,
  onBackToParent,
  onClickPathItem,
  onClickFolderPath,
  onNavChange
}: {
  path?: PathType;
  privilege: PrivilegeEnum;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
  onClickFolderPath?: (path: locationPathType[], searchContent: string) => void;
  parentId: string;
  onNavChange?: (nav?: NavType) => void;
} & SearchBarProps) => {
  const router = useRouter();
  const { industryAlias } = useTenantStore();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [searchContent, setSearchContent] = useState('');
  const [folderPathData, setFolderPathData] = useState<locationPathType[]>([]);
  const [spaceData, setSpaceData] = useState<SpaceType[]>([]);
  const [moreSpaceData, setMoreSpaceData] = useState<SpaceType[]>([]);
  const [spaceAuditInfo, setSpaceAuditInfo] = useState<string>('');
  const [spaceShow, setSpaceShow] = useState(false);
  const { addUpload } = useCloudUploader();
  const { fetchUnreadCount } = useNotificationStore();

  const { userInfo } = useUserStore();

  const { openOverlay } = useOverlayManager();

  const [auditCount, setAuditCount] = useState(0);

  useQuery(['SpaceList', parentId], () => getSpaceList({ parentId: parentId || '0' }), {
    onSuccess: (data: SpaceType[]) => {
      if (data && data.length > 6) {
        setSpaceData(data.slice(0, 6));
        setMoreSpaceData(data.slice(6));
      } else {
        setSpaceData(data);
      }
    }
  });

  const { data: spaceAuditData, refetch: refetchSpaceAudit } = useQuery(
    ['SpaceAuditInfo', parentId],
    () => getSpaceAuditInfo({ id: parentId }),
    {
      onSuccess: (data) => {
        if (data && data.description) {
          setSpaceAuditInfo(data.description);
        }
      },
      enabled: !!parentId
    }
  );

  // 使用 useEffect 监听 spaceAuditData 的变化
  useEffect(() => {
    if (spaceAuditData?.description) {
      if (spaceAuditData.description.length > 0) {
        setSpaceShow(true);
      } else {
        setSpaceShow(false);
      }
      setSpaceAuditInfo(spaceAuditData.description);
    }
  }, [spaceAuditData]);

  const breadcrumbList = useMemo(() => {
    const list: (BreadcrumbItemType & { pathItem?: PathItemType })[] = [];

    if (!path || path.length === 0) {
      list.push({
        label: `${industryAlias}数据空间`,
        pathItem: {
          type: PathItemTypeEnum.space,
          space: {
            spaceName: `${industryAlias}数据空间`,
            id: 'school-space-id',
            parentId: '0',
            privileges: [PrivilegeEnum.Owner],
            sortNo: 0,
            shareType: 0
          }
        },
        clickable: true
      });
    } else {
      const spaceItem = path.reduce(
        (pre: PathItemType | undefined, it: PathItemType) =>
          it.type === PathItemTypeEnum.space ? it : pre,
        undefined
      ) as PathSpaceType;

      const folderPath = path.filter(
        (it: PathItemType) => it.type === PathItemTypeEnum.file
      ) as FilePathType;

      if (folderPath.length) {
        list.push({
          label: '返回上一级',
          isBack: true
        });
      }

      list.push({
        label: spaceItem?.space?.spaceName || '',
        pathItem: spaceItem,
        clickable: true,
        dndData:
          privilege >= PrivilegeEnum.Owner ? { type: 'space', space: spaceItem?.space } : undefined
      });

      folderPath.forEach((pathItem, index) => {
        list.push({
          label: pathItem.file.fileName!,
          pathItem,
          clickable: index < folderPath.length - 1,
          dndData:
            privilege >= PrivilegeEnum.Owner
              ? { type: 'file', file: { ...pathItem.file, bizType: BizTypeEnum.TenantLibrary } }
              : undefined
        });
      });
    }

    return list;
  }, [path, privilege]);

  const onAddFolder = () => {
    path &&
      openOverlay({
        Overlay: FolderModal,
        props: {
          bizType: BizTypeEnum.TenantLibrary,
          path,
          onSuccess: () => tableInstance.reload()
        }
      });
  };

  const onLocalUpload = (fileType: FileTypeEnum) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    if (fileType === FileTypeEnum.Folder) {
      input.webkitdirectory = true;
    }
    input.onchange = (event) => {
      const files = Array.from((event.target as HTMLInputElement).files || []);
      // 过滤出非.开头的文件夹/文件
      const filteredFiles = files.filter((file) => {
        // 获取所有文件夹名和文件名
        const pathParts = file.webkitRelativePath
          ? file.webkitRelativePath.split('/')
          : [file.name];
        // 过滤出非.开头的文件夹/文件
        return !pathParts.some((part) => part.startsWith('.'));
      });

      if (filteredFiles.length === 0) {
        Toast.warning('没有可上传的文件');
        return;
      }

      if (files.length > filteredFiles.length) {
        Toast.warning(`已过滤${files.length - filteredFiles.length}个隐藏文件`);
      }

      if (filteredFiles.length && path?.length) {
        const lastPathItem = path[path.length - 1];
        const parentId =
          lastPathItem.type === PathItemTypeEnum.space
            ? (lastPathItem as PathSpaceType).space.id
            : (lastPathItem as PathFileType).file.id;
        Toast.success('已添加到上传队列');
        addUpload({
          path: path.map((it) =>
            it.type === PathItemTypeEnum.space ? it.space.spaceName : it.file.fileName
          ),
          parentId,
          files: filteredFiles,
          bizType: BizTypeEnum.TenantLibrary
        });
      }
    };
    input.click();
  };

  const onCloudUpload = () => {
    path &&
      openOverlay({
        Overlay: CloudUploadModal,
        props: {
          path,
          onSuccess: (parentId) => {
            if (parentId === query?.['parentId']) {
              tableInstance.reload();
            }
          }
        }
      });
  };

  const onAudit = () => {
    router.push('/cloud/audit');
  };

  const onSettings = () => {
    openOverlay({
      Overlay: SettingsModal,
      props: {}
    });
  };

  const onAllSearch = () => {
    openOverlay({
      Overlay: AllSearchModal,
      props: {
        searchContent: searchContent,
        bizType: BizTypeEnum.TenantLibrary,
        onSearchContent: (content) => {
          onSearch?.({ ...query, searchKey: content });
          setSearchContent(content);
        },
        onFolderPath: (data, searchContent) => {
          setFolderPathData(data);
          onClickFolderPath?.(data, searchContent);
        },
        onClose: () => {
          onClose;
        }
      }
    });
  };

  useQuery(
    ['auditCount'],
    () =>
      getAuditCount({
        type:
          userInfo?.roleType === UserRoleEnum.Admin ||
          userInfo?.roleType === UserRoleEnum.SpaceAdmin
            ? undefined
            : 2
      }),
    {
      onSuccess(data) {
        setAuditCount(data.num);
      }
    }
  );

  const onNavChangeSpace = (space: SpaceType) => {
    onNavChange?.({
      type: NavTypeEnum.tenant,
      path: [{ type: PathItemTypeEnum.space, space }]
    });
  };

  return (
    <>
      <Box w="100%">
        <Flex alignItems="center" w="100%">
          <Flex mr="auto" alignItems="center">
            <SearchInput
              border="1px solid #fff"
              bgColor="#eeeefe"
              searchIconDirect="left"
              isShowCloseIcon
              placeholder="通过文件名、正文搜索文档"
              w={respDims(695)}
              mr={respDims(13)}
              type={SearchTypeEnum.file}
              value={query?.searchKey}
              onChange={(e) => setSearchContent(e)}
              onSearch={(e) => {
                onSearch?.({ ...query, searchKey: e }), setSearchContent(e);
              }}
              onClearSearch={() => {
                onSearch?.({ ...query, searchKey: '' }), setSearchContent('');
              }}
              searchBgColor="#eeeefe"
            />
            {searchContent && (
              <>
                <Box
                  fontWeight="400"
                  fontSize="15px"
                  color="#000000"
                  marginRight={respDims(4)}
                  marginLeft={respDims(13)}
                >
                  无想要结果？
                </Box>
                <Popover
                  trigger="click"
                  placement="bottom"
                  isOpen={isOpen}
                  onOpen={onOpen}
                  onClose={onClose}
                >
                  <PopoverTrigger>
                    <Box
                      fontWeight="500"
                      fontSize="15px"
                      color="primary.500"
                      cursor="pointer"
                      onClick={() => {
                        onAllSearch();
                      }}
                    >
                      开始全局搜索
                    </Box>
                  </PopoverTrigger>

                  <PopoverContent
                    w={respDims('697fpx')}
                    h={respDims('688fpx')}
                    mt="13px"
                    mr="420px"
                    boxShadow="0px 3px 10px 0px rgba(0,0,0,0.11)"
                  >
                    {isOpen && (
                      <AllSearchModal
                        onClose={onClose}
                        bizType={BizTypeEnum.TenantLibrary}
                        onSearchContent={(content) => {
                          onSearch?.({ ...query, searchKey: content }), setSearchContent(content);
                        }}
                        onFolderPath={(data, searchContent) => {
                          setFolderPathData(data);
                          setSearchContent(searchContent);
                          onClickFolderPath?.(data, searchContent);
                        }}
                        searchContent={searchContent}
                      />
                    )}
                  </PopoverContent>
                </Popover>
              </>
            )}
          </Flex>
          {path?.length !== 0 && (
            <Box>
              <MyMenu
                Button={
                  <Button
                    variant="solid"
                    colorScheme="primary"
                    px={respDims(20)}
                    fontSize={respDims('14fpx')}
                    borderRadius={respDims(8)}
                    h={respDims(36)}
                    ml={respDims(13)}
                  >
                    <SvgIcon name="upload" w={respDims('14fpx')} h={respDims('14fpx')} />
                    <Box ml={respDims(8)}>上传</Box>
                  </Button>
                }
                menuList={[
                  {
                    label: '文件上传',
                    onClick: () => onLocalUpload(FileTypeEnum.File)
                  },
                  {
                    label: '文件夹上传',
                    onClick: () => onLocalUpload(FileTypeEnum.Folder)
                  },
                  {
                    label: '添加云端文件',
                    onClick: () => onCloudUpload()
                  }
                ]}
              />
            </Box>
          )}
          {privilege >= PrivilegeEnum.Manage && path?.length !== 0 && (
            <Button
              ml={respDims(13)}
              variant="outline"
              colorScheme="primary"
              px={respDims(20)}
              fontSize={respDims('14fpx')}
              borderRadius={respDims(8)}
              onClick={onAddFolder}
              h={respDims(36)}
              bgColor="#fff"
            >
              <SvgIcon name="folder" w={respDims('14fpx')} h={respDims('14fpx')} />
              <Box ml={respDims(8)}>新建文件夹</Box>
            </Button>
          )}

          <Badge count={auditCount} size="small">
            <Button
              variant="outline"
              colorScheme="primary"
              px={respDims(20)}
              fontSize={respDims('14fpx')}
              borderRadius={respDims(8)}
              onClick={onAudit}
              h={respDims(36)}
              ml={respDims(13)}
              bgColor="#fff"
            >
              <SvgIcon name="file" w={respDims('14fpx')} h={respDims('14fpx')} />
              <Box ml={respDims(8)}>待审核资料</Box>
            </Button>
          </Badge>
          <Flex mr={respDims(10)} ml={respDims(10)}>
            <TransferButton ml="auto" />

            {privilege >= PrivilegeEnum.Manage && (
              <Button
                ml={respDims(10)}
                variant="ghost"
                p="0"
                minW="0"
                w={respDims(36)}
                h={respDims(36)}
                borderRadius={respDims(8)}
                border="1px solid #E5E7E8"
                onClick={onSettings}
                bgColor="#fff"
                _hover={{ bgColor: '#fff' }}
              >
                <SvgIcon name="settings2" w={respDims(14)} h={respDims(14)} color="#1D2129" />
              </Button>
            )}
          </Flex>
        </Flex>

        <Flex
          borderRadius="8px 8px 0 0"
          w="100%"
          mt={rpxDim(22)}
          pt={rpxDim(22)}
          pl={rpxDim(20)}
          pb={rpxDim(12)}
          bgColor="#fff"
        >
          <Breadcrumb
            list={breadcrumbList}
            {...(breadcrumbList.length === 1 && { fontSize: respDims('16fpx') })}
            onClickItem={(item) => item.pathItem && onClickPathItem?.(item.pathItem)}
            onBack={onBackToParent}
          />
          {spaceShow && (
            <LkTooltip
              header="空间介绍"
              onOpened={() => refetchSpaceAudit()}
              content={spaceAuditInfo}
              placement="bottom"
            >
              <Box
                ml={respDims(6)}
                display="flex"
                alignItems="center"
                sx={{
                  svg: {
                    cursor: 'pointer'
                  }
                }}
              >
                <SvgIcon
                  _hover={{
                    color: 'primary.500'
                  }}
                  name="informationLine"
                  w={respDims(24)}
                  h={respDims(24)}
                  color="#6b7280"
                />
              </Box>
            </LkTooltip>
          )}
        </Flex>

        {/* <Flex alignItems="center">
          {spaceData.map((item) => (
            <Flex
              key={item.id}
              bgColor="white"
              w={respDims(221)}
              h={respDims(56)}
              border="1px solid #E5E7E8"
              borderRadius={respDims(8)}
              alignItems="center"
              justifyContent="center"
              mr={respDims(14)}
              cursor="pointer"
              _hover={{
                bgColor: '#F3F4F6'
              }}
              onClick={() => {
                onNavChangeSpace(item);
              }}
            >
              <SvgIcon name="childSpace" w={respDims(32)} h={respDims(32)} />
              <Box ml={respDims(8)}>{item.spaceName}</Box>
            </Flex>
          ))}
          {spaceData.length >= 6 && (
            <Flex alignItems="center">
              <MyMenu
                Button={
                  <Button
                    variant="outline"
                    colorScheme="primary"
                    fontSize={respDims('14fpx')}
                    borderRadius={respDims(8)}
                    h={respDims(56)}
                    w={respDims(82)}
                    textAlign="center"
                  >
                    <Box>展开</Box>
                  </Button>
                }
                menuList={moreSpaceData.map((item) => ({
                  label: (
                    <Box
                      display="flex"
                      padding={`${respDims(7)} ${respDims(12)}`}
                      alignItems="center"
                      gap={respDims(8)}
                      alignSelf="stretch"
                    >
                      <SvgIcon name="smallChildSpace" w={respDims(16)} h={respDims(16)} />
                      <Box ml={respDims(8)}>{item.spaceName}</Box>
                    </Box>
                  ),
                  onClick: () => {
                    onNavChangeSpace(item);
                  }
                }))}
              />
            </Flex>
          )}
        </Flex> */}
      </Box>
    </>
  );
};

export default Header;
