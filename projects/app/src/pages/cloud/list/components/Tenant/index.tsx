import MyTable from '@/components/MyTable';
import { Box, Center, Flex, Text } from '@chakra-ui/react';
import Header from './Header';
import { RowProps, Table, TableProps } from 'antd';
import Footer from './Footer';
import {
  copyFile,
  getSpaceFilePages,
  getSpacePrimaryList,
  removeSpaceFileBatch
} from '@/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { TableRowSelection } from 'antd/es/table/interface';
import {
  AuditorStatusEnum,
  AuditorStatusMap,
  BizTypeEnum,
  FileTypeEnum,
  MoveTypeEnum,
  PrivilegeEnum
} from '@/constants/api/cloud';
import { formatFileSize } from '@/utils/tools';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import MyMenu from '@/components/MyMenu';
import styles from '../../../cloud.module.scss';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import FolderModal from '../../../components/FolderModal';
import ShareModal from './ShareModal';
import ShareFileModal from './ShareFileModal';
import { MessageBox } from '@/utils/ui/messageBox';
import { FileType, locationPathType } from '@/types/api/cloud';
import {
  FilePathType,
  NavType,
  PathFileType,
  PathItemType,
  PathSpaceType,
  TenantNavType
} from '@/types/cloud';
import { FooterComponentProps, MyTableRef, SearchBarProps } from '@/components/MyTable/types';
import MyTooltip from '@/components/MyTooltip';
import { FileParseStatusEnum, NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { useCloudStore } from '@/store/useCloudStore';
import useFilePreview from '@/hooks/useFilePreview';
import useUploadRefresh from '@/pages/cloud/hooks/useUploadRefresh';
import { Toast } from '@/utils/ui/toast';
import { useUserStore } from '@/store/useUserStore';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { useNotificationStore } from '@/store/useTificationContext';
import { UserRoleEnum } from '@/constants/api/auth';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';
import DndRow from '@/pages/cloud/components/DndWrapper/DndRow';
import DndHandle from '@/pages/cloud/components/DndWrapper/DndHandle';
import MoveModal from './MoveModal';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import useDatasetSelect from '@/pages/dataset/list/hooks/useDatasetSelect';
import { useDatasetStore } from '@/store/useDatasetStore';
import { DataSource } from '@/constants/common';
import EmptyCloud from '../../../components/EmptyCloud/index';
import AddFile from '../../../components/AddFile/index';
import { useChatStore } from '@/store/useChatStore';
import { useAppStore } from '@/store/useAppStore';
import { appointedTypeEnum } from '@/constants/api/app';
import { FileType as MessageFileType } from '@/components/ChatBox/MessageInputMini';
import { fileTypeInfos, UploadStatusEnum } from '@/components/ChatBox/MessageInput';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import SpaceModal from './SpaceModal';
import InfoSpaceModal from './InfoSpaceModal';
import GroupModal from './GroupModal';
import SettingModal from './SettingModal';
import { eventBus, EventNameEnum } from '@/utils/eventbus';
import { useQuery } from '@tanstack/react-query';
import FileSelect, { ItemType } from '../../../components/LocalUploadModal/FileSelect';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import { Modal } from 'antd';

const Tenant = ({
  value,
  nav,
  onGetLocationPath,
  onNavChange
}: {
  value: AppSimpleEditFormTypeMegre;
  nav: TenantNavType;
  onGetLocationPath: (data: locationPathType[], searchContent: string) => void;
  onNavChange: (nav?: NavType) => void;
}) => {
  const { userInfo } = useUserStore();
  const { specialAppList, loadSpecialAppList } = useAppStore();
  const { setInitChatInputs } = useChatStore();
  const { addUpload } = useCloudUploader();
  const { mindMapApp, docApp } = useMemo(() => {
    return {
      mindMapApp: specialAppList.find((app) => app.appointedType === appointedTypeEnum.MindMap),
      docApp: specialAppList.find((app) => app.appointedType === appointedTypeEnum.Document)
    };
  }, [specialAppList]);

  useQuery(['loadSpecialAppList'], () => loadSpecialAppList());
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const { data: spacePrimaryList } = useQuery(
    ['getSpacePrimaryList'],
    () =>
      getSpacePrimaryList({
        parentId: '0'
      }).then((res) => res.records),
    {
      enabled: nav?.path.length === 0
    }
  );
  const [selectedFiles, setSelectedFiles] = useState<FileType[]>([]);

  const { open: openSelectDatasetModal } = useDatasetSelect();
  const { allDatasets } = useDatasetStore();
  const tableRef = useRef<MyTableRef>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const { openOverlay } = useOverlayManager();

  const { previewFile } = useFilePreview();

  const { addDownload } = useCloudDownloader();

  const { fetchUnreadCount } = useNotificationStore();

  const { selectFile, setSelectFile, showFileInfo, setShowFileInfo } = useCloudStore(); // 使用 store

  const spaceId = (nav?.path && nav.path[nav.path.length - 1]?.space?.id) || '';

  const [folderPath, setFolderPath] = useState<FilePathType>([]);
  const selectDatasets = useMemo(() => {
    if (!value) return [];
    return allDatasets.filter((item) =>
      value.dataset.datasets.find((dataset) => dataset.datasetId === item._id)
    );
  }, [allDatasets, value?.dataset?.datasets]);
  const [showMenu, setShowMenu] = useState(false);
  const [sortOrder, setSortOrder] = useState<number>();
  const [selectedMenuItemId, setSelectedMenuItemId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const isAdmin = userInfo?.roleType === UserRoleEnum.Admin;
  const isSpaceAdmin = userInfo?.roleType === UserRoleEnum.SpaceAdmin;

  const { path, parentId, shareType, privilege } = useMemo(() => {
    const path = [...nav.path, ...folderPath];
    const item = path[path.length - 1];
    return {
      path,
      parentId: item ? (item.type === PathItemTypeEnum.space ? item.space.id : item.file.id) : '',
      shareType: item
        ? item.type === PathItemTypeEnum.space
          ? item.space.shareType
          : item.file.shareType
        : undefined,
      privilege:
        isAdmin || isSpaceAdmin
          ? PrivilegeEnum.Owner
          : folderPath.reduce(
              (max, item) =>
                item.file?.privileges?.reduce((max, cur) => (cur > max ? cur : max), max) ?? max,
              nav.path.reduce(
                (max, item) =>
                  item.space?.privileges?.reduce((max, cur) => (cur > max ? cur : max), max) ?? max,
                PrivilegeEnum.View
              )
            )
    };
  }, [nav.path, folderPath, isAdmin, isSpaceAdmin]);

  const MenuItem = ({ id, label }: { id: string; label: string }) => (
    <Flex
      alignItems="center"
      p={2}
      cursor="pointer"
      onClick={() => {
        setSortOrder(Number(id));
        setShowMenu(false);
        setSelectedMenuItemId(id);
      }}
      _hover={{ bg: '#F2F3F5' }}
      bg={selectedMenuItemId === id ? '#F2F3F5' : 'transparent'}
    >
      <Text fontSize="14px" pl="6px" lineHeight="22px" color="#1D2129" fontWeight="400">
        {label}
      </Text>
    </Flex>
  );

  useUploadRefresh({ parentId, onRefresh: () => tableRef.current?.reload() });

  const onIntoDataset = async (folder: FileType) => {
    if (
      folder.fileSize === 0 ||
      (folder.fileSize && Number((folder.fileSize / 1024 / 1024).toFixed(2)) < 500)
    ) {
      openSelectDatasetModal({
        defaultSelectedDatasets: selectDatasets.map((item) => {
          return { id: item._id };
        }),
        sourceKey: DataSource.Tenant,
        cloudFileId: Number(folder.id),
        isImport: true,
        onRefresh: () => {
          tableRef.current?.reload();
        }
      });
    } else {
      Toast.warning('文件大小超过500MB限制');
    }
  };

  const onEditFolder = (folder: FileType) => {
    openOverlay({
      Overlay: FolderModal,
      props: {
        bizType: BizTypeEnum.TenantLibrary,
        folder,
        fileType: folder.fileType,
        onSuccess: () => tableRef.current?.reload()
      }
    });
  };

  const onMoveFile = (folder: FileType) => {
    openOverlay({
      Overlay: MoveModal,
      props: {
        bizType: BizTypeEnum.TenantLibrary,
        folder,
        isBatch: false,
        onSuccess: () => tableRef.current?.reload()
      }
    });
  };

  const onDownloadFile = (file: FileType) => {
    addDownload(
      file.fileType === FileTypeEnum.File
        ? {
            bizType: BizTypeEnum.TenantLibrary,
            type: DownloadTypeEnum.File,
            fileId: file.id,
            fileKey: file.fileKey!
          }
        : {
            bizType: BizTypeEnum.TenantLibrary,
            type: DownloadTypeEnum.Folder,
            source: DownloadSourceEnum.Normal,
            folderId: file.id
          }
    ).then(() => {
      Toast.success('已添加到下载队列中');
    });
  };

  const onCopyFile = (file: FileType) => {
    MessageBox.info({
      title: '复制提示',
      content: '复制文件将在当前列表显示，确定复制一份文件？',
      okCancel: true,
      okText: '确定复制',
      onOk: () => {
        copyFile(file.id).then(() => {
          tableRef.current?.reload();
          Toast.success('复制成功');
        });
      }
    });
  };

  const onUpdateFile = (file: FileType) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.onchange = (event) => {
      const files = Array.from((event.target as HTMLInputElement).files || []);
      if (files.length && path?.length) {
        const lastPathItem = path[path.length - 1];
        const parentId =
          lastPathItem.type === PathItemTypeEnum.space
            ? (lastPathItem as PathSpaceType).space.id
            : (lastPathItem as PathFileType).file.id;
        Toast.success('已添加到上传队列');
        addUpload({
          path: path.map((it) =>
            it.type === PathItemTypeEnum.space ? it.space.spaceName : it.file.fileName
          ),
          parentId,
          files,
          bizType: BizTypeEnum.TenantLibrary,
          oldId: file.id
        });
      }
    };
    input.click();
  };

  const onShareFile = ({
    id,
    name,
    fileType,
    fileName
  }: {
    id: string;
    name: string;
    fileType: number;
    fileName: string;
  }) => {
    openOverlay({
      Overlay: fileType === FileTypeEnum.Folder ? ShareModal : ShareFileModal,
      props: {
        shareData: {
          id,
          name: fileType === FileTypeEnum.Folder ? name : fileName
        }
      }
    });
  };

  const onRemoveFile = (file: FileType) => {
    MessageBox.delete({
      content:
        file.fileType === FileTypeEnum.Folder
          ? '删除文件夹 则文件夹下的文件会全部删除，删除的全部内容将进入回收站，30天后自动彻底删除。'
          : '删除文件全部内容将进入回收站，30天后自动彻底删除。',
      tip: '提示：如果你删除的内容中有属于他人的，其所有者将收到通知',

      onOk: () => {
        removeSpaceFileBatch({ list: [{ id: file.id, fileType: file.fileType! }] }).then(() => {
          tableRef.current?.reload();
          fetchUnreadCount();
        });
      }
    });
  };

  const onClickFile = async (file: FileType) => {
    const fileExtension = file.file?.fileUrl.split('.').pop()?.toLowerCase() ?? '';
    const unsupportedFormats = ['log'];
    if (unsupportedFormats.includes(fileExtension)) {
      Toast.error('该文件类型不支持预览');
      return;
    }
    // 限制过大文件查看 Start
    const formattedSize = file.fileSize ? formatFileSize(file.fileSize) : '0';
    const sizeMatch = formattedSize.match(/^([\d.]+)/);
    // 拿到数字部分
    const fileSize = sizeMatch ? parseFloat(sizeMatch[1]) : 0;
    // 拿到单位部分
    const fileUnit = formattedSize.match(/([a-zA-Z]+)$/)?.[0];

    if (
      file.fileType === FileTypeEnum.File &&
      fileUnit !== 'B' &&
      fileUnit !== 'KB' &&
      fileSize > 80
    ) {
      // 防止重复点击
      if (isModalOpen) return;
      setIsModalOpen(true);

      // 取消和下载两个按钮
      Modal.confirm({
        title: '提示',
        content: '当前文件超过80M，在线预览过慢，请下载查看~',
        okText: '下载',
        cancelText: '取消',
        onOk: () => {
          onDownloadFile(file);
          setIsModalOpen(false);
        },
        maskClosable: true,
        onCancel: () => {
          setIsModalOpen(false);
        }
      });
      return;
    }
    // 限制过大文件查看 End

    const previewSuccess = await previewFile({
      fileUrl: file.file?.fileUrl ?? '',
      fileType: file.fileType,
      fileKey: file.file?.fileKey
    });
    if (previewSuccess) {
      return;
    }
    if (file.fileType === FileTypeEnum.Folder || file.fileType === FileTypeEnum.Space) {
      setFolderPath((state) => [...state, { type: PathItemTypeEnum.file, file }]);
    }
    if (file.type === MoveTypeEnum.Space) {
      nav.path = [...nav.path, { type: PathItemTypeEnum.space, space: file }] as any;
      await getSpaceFilePages({
        parentId: file.id,
        shareType,
        privilege,
        sortOrder
      });
    }
  };

  const onClickPathItem = useCallback((pathItem: PathItemType) => {
    if (pathItem.type === PathItemTypeEnum.space) {
      setFolderPath([]);
    } else {
      setFolderPath((state) => {
        const index = state.findIndex((item) => item.file.id === pathItem.file.id);
        return index >= 0 ? state.slice(0, index + 1) : state;
      });
    }
  }, []);

  const onBackToParent = useCallback(() => {
    setFolderPath((state) => state.slice(0, state.length - 1));
  }, []);

  const onFolderPath = useCallback(
    (path: locationPathType[], searchContent: string) => {
      setTimeout(() => {
        const fileItems: PathFileType[] = [];
        const spaceItems: PathSpaceType[] = [];

        path.forEach((item) => {
          if (item.fileType === 2) {
            fileItems.push({
              type: PathItemTypeEnum.file,
              file: {
                fileName: item.name,
                id: item.id,
                parentId: item.parentId
              }
            } as PathFileType);
          } else {
            spaceItems.push({
              type: PathItemTypeEnum.space,
              space: {
                spaceName: item.name,
                parentId: item.parentId,
                hasChildren: true,
                privileges: [],
                sortNo: 0,
                description: '',
                shareType: 0,
                children: [],
                id: item.id
              }
            } as PathSpaceType);
          }
        });

        setFolderPath(fileItems);

        nav.path = [...fileItems, ...spaceItems] as any;

        setTimeout(() => {
          tableRef.current?.setQuery({
            ...tableRef.current?.query,
            searchKey: searchContent
          });
        }, 300);
      }, 300);
      onGetLocationPath(path, searchContent);
    },
    [parentId, nav]
  );

  const onMindMap = (file: FileType) => {
    const type = file.fileName.substring(file.fileName.lastIndexOf('.') + 1).toLocaleLowerCase();
    const info = fileTypeInfos.find((it) => it.name === type);
    if (!info) {
      return null;
    }
    const fileData: MessageFileType = {
      ...file,
      type,
      sizeText: formatFileSize(file.fileSize!),
      rawFile: new File([], file.fileName),
      key: file.fileKey!,
      uploadStatus: UploadStatusEnum.success,
      percent: 100,
      sort: 0,
      isCallOcr: false,
      fileUrl: file.file?.fileUrl ?? '',
      fileKey: file.fileKey!,
      name: file.fileName,
      svgIcon: info.svgIcon
    };
    setInitChatInputs({
      inputVal: '生成思维导图',
      files: [fileData]
    });

    setTimeout(() => {
      window.open(`/home?appId=${mindMapApp?.id}&init=1`, '_blank');
    }, 100);
  };

  const onDocScreenshot = (file: FileType) => {
    const type = file.fileName.substring(file.fileName.lastIndexOf('.') + 1).toLocaleLowerCase();
    const info = fileTypeInfos.find((it) => it.name === type);
    if (!info) {
      return null;
    }
    const fileData: MessageFileType = {
      ...file,
      type,
      sizeText: formatFileSize(file.fileSize!),
      rawFile: new File([], file.fileName),
      key: file.fileKey!,
      uploadStatus: UploadStatusEnum.success,
      percent: 100,
      sort: 0,
      isCallOcr: false,
      fileUrl: file.file?.fileUrl ?? '',
      fileKey: file.fileKey!,
      name: file.fileName,
      svgIcon: info.svgIcon
    };
    setInitChatInputs({
      inputVal: '文档解读',
      files: [fileData]
    });
    window.open(`/home?appId=${docApp?.id}&init=1`, '_blank');
  };

  const onAddSpace = useCallbackRef((node: FileType) => {
    openOverlay({
      Overlay: SpaceModal,
      props: {
        parentId: node.id,
        onSuccess: () => {
          eventBus.emit(EventNameEnum.refreshTree, { parentId: node.id });
          tableRef.current?.reload();
        }
      }
    });
  });

  const onInfoSpace = useCallbackRef((node: FileType) => {
    openOverlay({
      Overlay: InfoSpaceModal,
      props: {
        space: {
          spaceName: node.fileName,
          description: node.description || '',
          id: node.id,
          parentId: node.parentId,
          privileges: [],
          sortNo: 0,
          shareType: 0,
          children: []
        },
        EditSpaceSuccess: () => {
          eventBus.emit(EventNameEnum.refreshTree, { parentId });
          tableRef.current?.reload();
        },
        onClose: () => {}
      }
    });
  });

  const onGroupSpace = useCallbackRef((node: FileType) => {
    openOverlay({
      Overlay: GroupModal,
      props: {
        groupData: {
          id: node.id,
          name: node.fileName
        },
        onSuccess: () => {
          eventBus.emit(EventNameEnum.refreshTree, { parentId });
          tableRef.current?.reload();
        }
      }
    });
  });

  const onSettingsSpace = useCallbackRef((node: FileType) => {
    openOverlay({
      Overlay: SettingModal,
      props: {
        settingData: {
          id: node.id,
          name: node.fileName
        },
        space: {
          spaceName: node.fileName,
          description: node.description || '',
          id: node.id,
          parentId: node.parentId,
          privileges: [],
          sortNo: 0,
          shareType: 0,
          children: []
        },
        EditSpaceSuccess: () => {
          eventBus.emit(EventNameEnum.refreshTree, { parentId });
          tableRef.current?.reload();
        },
        onClose: () => {},
        onNavChange
      }
    });
  });

  useEffect(() => {
    setSelectedFiles([]);
    setSelectedRowKeys([]);
    tableRef.current?.setCurrent?.(1);
  }, [parentId]);

  useEffect(() => {
    setFolderPath([]);
  }, [spaceId]);

  const prevNavRef = useRef<TenantNavType | undefined>();

  useEffect(() => {
    const isTenantNav = nav?.type === NavTypeEnum.tenant;
    const isPathEmpty = nav?.path.length === 0;
    const prevIsTenantNav = prevNavRef.current?.type === NavTypeEnum.tenant;
    const prevIsPathEmpty = prevNavRef.current?.path.length === 0;

    if (isTenantNav && isPathEmpty) {
      if (!prevIsTenantNav || !prevIsPathEmpty) {
        setIsLoading(true);
        getSpacePrimaryList({
          parentId: '0'
        }).then(() => {
          setIsDataLoaded(true);
          setIsLoading(false);
        });
      }
    } else if (
      prevNavRef.current?.path.length === nav.path.length &&
      prevNavRef.current?.path[prevNavRef.current?.path.length - 1]?.space?.id ===
        nav.path[nav.path.length - 1]?.space?.id &&
      nav.path.length !== 0 &&
      prevNavRef.current?.path[prevNavRef.current?.path.length - 1]?.space?.id !== '0'
    ) {
      tableRef.current?.reload();
    }

    prevNavRef.current = nav;
  }, [nav]);

  useEffect(() => {
    if (nav?.type !== NavTypeEnum.tenant && !isDataLoaded) {
      setIsLoading(true);
      getSpaceFilePages({
        parentId: '0'
      }).then(() => {
        setIsDataLoaded(true);
        setIsLoading(false);
      });
    }
  }, [nav, isDataLoaded]);

  const TableHeader = useCallback(
    ({ onSearch, ...props }: SearchBarProps) => {
      return (
        <>
          <Header
            {...props}
            onNavChange={onNavChange}
            parentId={parentId}
            path={path}
            privilege={privilege}
            onSearch={(e) => {
              setSelectedFiles([]);
              setSelectedRowKeys([]);
              onSearch?.(e);
            }}
            onBackToParent={onBackToParent}
            onClickPathItem={onClickPathItem}
            onClickFolderPath={onFolderPath}
          />
        </>
      );
    },
    [path, privilege, onBackToParent, onClickPathItem]
  );

  const TableFooter = useCallback(
    (props: FooterComponentProps) => {
      return (
        <Footer
          {...props}
          privilege={privilege}
          selectedFiles={selectedFiles}
          parentId={parentId}
          onRefresh={() => tableRef.current?.reload()}
          onClearSelectedFiles={() => {
            setSelectedRowKeys([]);
            setSelectedFiles([]);
          }}
        />
      );
    },
    [selectedFiles, parentId, privilege]
  );

  const rowSelection: TableRowSelection<FileType> = {
    type: 'checkbox',
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    columnWidth: 30,
    onChange(selectedRowKeys, selectedRows, info) {
      const filteredRows = selectedRows.filter((row) => row.type !== 1);
      setSelectedRowKeys(
        selectedRowKeys.filter((key, index) =>
          filteredRows.includes(selectedRows[index])
        ) as string[]
      );
      setSelectedFiles((state) => [
        ...state.filter(
          (item) =>
            selectedRowKeys.includes(item.rowKey!) &&
            !filteredRows.some((row) => row.rowKey === item.rowKey)
        ),
        ...filteredRows
      ]);
    },
    getCheckboxProps: (record) => ({
      disabled: record.type === 1 || record.fileType === FileTypeEnum.Space,
      style: {
        display: 'flex'
      }
    })
  };

  const fileTypes: Record<string, string[]> = {
    word: ['txt', 'doc', 'docx', 'dotx', 'dotm', 'md'],
    excel: ['xls', 'xlsx', 'csv'],
    pdf: ['pdf'],
    ppt: ['ppt', 'ppts', 'pot', 'ppa', 'pptx'],
    image: [
      'bmp',
      'jpg',
      'jpeg',
      'png',
      'tif',
      'gif',
      'pcx',
      'tga',
      'exif',
      'fpx',
      'svg',
      'psd',
      'cdr',
      'pcd',
      'dxf',
      'ufo',
      'eps',
      'ai',
      'raw',
      'WMF',
      'webp',
      'avif',
      'apng',
      'heic'
    ],
    video: [
      'wmv',
      'asf',
      'asx',
      'rm',
      'rmvb',
      'mpg',
      'mpeg',
      'mpe',
      '3gp',
      'mov',
      'mp4',
      'm4v',
      'avi',
      'dat',
      'mkv',
      'flv',
      'vob'
    ]
  };
  const columns: TableProps<FileType>['columns'] = [
    ...(privilege >= PrivilegeEnum.Edit
      ? [
          {
            title: '',
            key: 'handle',
            className: 'dnd-handle',
            render: () => <DndHandle />,
            width: 30
          }
        ]
      : []),
    Table.SELECTION_COLUMN,
    {
      title: <Box whiteSpace="nowrap">{path?.length ? '文件类型' : '文件名称'}</Box>,
      key: 'fileName',
      filters: path?.length
        ? [
            {
              text: '数据空间',
              value: '3'
            },
            {
              text: '文件夹',
              value: '2'
            },
            {
              text: 'Word',
              value: 'word'
            },
            {
              text: 'Excel',
              value: 'excel'
            },
            {
              text: 'PDF',
              value: 'pdf'
            },
            {
              text: 'PPT',
              value: 'ppt'
            },
            {
              text: '图片',
              value: 'image'
            },
            {
              text: '视频',
              value: 'video'
            },
            {
              text: '其他',
              value: 'other'
            }
          ]
        : undefined,
      onFilter: (value, record) => {
        const extension = record.fileName.split('.').pop()?.toLowerCase();
        if (record.fileType === 3 || record.fileType === 2) {
          return record.fileType.toString() === value;
        } else if (fileTypes[value as keyof typeof fileTypes]?.includes(extension as string)) {
          return true;
        } else if (value === 'other') {
          return (
            record.fileType?.toString() !== '3' &&
            record.fileType?.toString() !== '2' &&
            !fileTypes['word']?.includes(extension as string) &&
            !fileTypes['excel']?.includes(extension as string) &&
            !fileTypes['pdf']?.includes(extension as string) &&
            !fileTypes['ppt']?.includes(extension as string) &&
            !fileTypes['image']?.includes(extension as string) &&
            !fileTypes['video']?.includes(extension as string)
          );
        } else {
          return false;
        }
      },
      render: (_, record) => {
        const { fileName } = record;
        return (
          <Flex
            whiteSpace="nowrap"
            alignItems="center"
            cursor="pointer"
            onClick={() => onClickFile(record)}
          >
            {Number(record.type) === MoveTypeEnum.Space ||
            record.fileType === FileTypeEnum.Space ? (
              <SvgIcon name="box3" color="#4E5969" w={respDims(45)} h={respDims(45)} ml="-2px" />
            ) : (
              <FileIcon flexShrink="0" {...record} />
            )}

            <MyTooltip overflowOnly>
              <Box
                ml={respDims(12)}
                fontSize={respDims('14fpx')}
                lineHeight="1.5em"
                maxH="3em"
                wordBreak="break-all"
                overflow="hidden"
                textOverflow="ellipsis"
                maxW={respDims(200)}
              >
                {fileName}
              </Box>
            </MyTooltip>
          </Flex>
        );
      }
    },
    {
      title: <Box whiteSpace="nowrap">创建人</Box>,
      dataIndex: 'uploader',
      render: (value) => {
        return <Box whiteSpace="nowrap">{value}</Box>;
      }
    },
    {
      title: <Box whiteSpace="nowrap">审核人</Box>,
      dataIndex: 'auditor',
      render: (_, record) => {
        return (
          <Box whiteSpace="nowrap">{String(record.auditorId) !== '0' ? record.auditor : '/'}</Box>
        );
      }
    },

    {
      title: <Box whiteSpace="nowrap">更新时间</Box>,
      dataIndex: 'updateTime',
      render: (value) => {
        return <Box whiteSpace="nowrap">{dayjs(value).format('YYYY-MM-DD HH:mm')}</Box>;
      }
    },
    {
      title: <Box whiteSpace="nowrap">文件大小</Box>,
      dataIndex: 'size',
      render: (_, { fileType: type, fileSize, type: spaceType }) => {
        if (
          type === FileTypeEnum.Folder ||
          spaceType === MoveTypeEnum.Space ||
          type === FileTypeEnum.Space
        ) {
          return '';
        }
        return formatFileSize(fileSize!);
      }
    },
    // {
    //   title: <Box whiteSpace="nowrap">状态</Box>,
    //   dataIndex: 'auditorStatus',
    //   render: (value) => (
    //     <Box
    //       whiteSpace="nowrap"
    //       {...(value === AuditorStatusEnum.Approved && { color: '#2BA471' })}
    //     >
    //       {AuditorStatusMap[value as AuditorStatusEnum]?.label}
    //     </Box>
    //   )
    // },
    // {
    //   title: <Box whiteSpace="nowrap">已导入知识库</Box>,
    //   dataIndex: 'cloudFileDatasetList',
    //   render: (value) => {
    //     return (
    //       <MyTooltip
    //         overflowOnly
    //         label={value?.map((item: any) => item.tenantDatasetName).join(',')}
    //       >
    //         <Box
    //           ml={respDims(12)}
    //           fontSize={respDims('14fpx')}
    //           lineHeight="1.5em"
    //           maxH="3em"
    //           wordBreak="break-all"
    //           whiteSpace="nowrap"
    //           overflow="hidden"
    //           textOverflow="ellipsis"
    //           maxW="200px"
    //         >
    //           {value?.length
    //             ? value?.map((v: any, index: number) => (
    //                 <>
    //                   {index !== 0 && '，'}
    //                   {v?.tenantDatasetName}
    //                 </>
    //               ))
    //             : '-'}
    //         </Box>
    //       </MyTooltip>
    //     );
    //   }
    // },
    {
      title: (
        <Flex justify="flex-end" position="relative">
          <MyTooltip label="排序" aria-label="排序">
            <Flex
              alignItems="center"
              justifyContent="center"
              _hover={{
                bgColor: '#F2F2F2',
                borderRadius: '4px'
              }}
              padding={respDims(4)}
              mr={respDims(16)}
            >
              <SvgIcon
                cursor="pointer"
                name="dataSpaceSort"
                color={showMenu ? 'primary.500' : '#1D2129'}
                w={respDims(22, 18)}
                h={respDims(22, 18)}
                onClick={() => {
                  setShowMenu((prev) => !prev);
                }}
              />
            </Flex>
          </MyTooltip>
          {showMenu && (
            <Box
              bg="white"
              borderRadius="8px"
              w="146px"
              pos="absolute"
              top="50px"
              right="42px"
              bgColor="FFF"
              border="1px solid #E5E6EB"
              boxShadow="0px 4px 10px 0px rgba(0, 0, 0, 0.10)"
            >
              <MenuItem id="1" label="文件名A-Z" />
              <MenuItem id="2" label="文件名Z-A" />
              <MenuItem id="3" label="新文件优先" />
              <MenuItem id="4" label="旧文件优先" />
              <MenuItem id="5" label="大文件优先" />
              <MenuItem id="6" label="小文件优先" />
            </Box>
          )}
          <MyTooltip label="文件信息" aria-label="文件信息">
            <Flex
              alignItems="center"
              justifyContent="center"
              _hover={{
                bgColor: '#F2F2F2',
                borderRadius: '4px'
              }}
              padding={respDims(4)}
            >
              <SvgIcon
                cursor={
                  selectFile?.type === MoveTypeEnum.Space ||
                  selectFile?.fileType === FileTypeEnum.Space
                    ? 'not-allowed'
                    : 'pointer'
                }
                name="file2Info"
                color={showFileInfo ? 'primary.500' : '#1D2129'}
                w={respDims(22, 18)}
                h={respDims(22, 18)}
                onClick={() => {
                  if (
                    selectFile?.type === MoveTypeEnum.Space ||
                    selectFile?.fileType === FileTypeEnum.Space
                  ) {
                    return;
                  }
                  setShowFileInfo(!showFileInfo);
                }}
              />
            </Flex>
          </MyTooltip>
          {/* <MyTooltip label="文件动态" aria-label="文件动态">
            <SvgIcon
              name="file2Dynamic"
              w={respDims(20)}
              h={respDims(20)}
              onClick={onToggleFileDynamics}
              cursor="pointer"
            />
          </MyTooltip> */}
        </Flex>
      ),
      key: 'action',
      width: 80,
      render: (_, record) => {
        const menuList = [];
        if (record.type === 1 || record.fileType === FileTypeEnum.Space) {
          nav.path.length < 3 &&
            menuList.push({
              label: '添加空间',
              icon: <SvgIcon name="plus" />,
              onClick: () => onAddSpace(record)
            });
          menuList.push({
            label: '空间信息',
            icon: <SvgIcon name="cloudInfoSpace" />,
            onClick: () => onInfoSpace(record)
          });
          // 只有管理员或者拥有者才显示"成员及权限"菜单项
          if (
            record?.privileges?.length === 0 ||
            (record.privileges && record.privileges[0] >= PrivilegeEnum.Manage)
          ) {
            menuList.push({
              label: '成员及权限',
              icon: <SvgIcon name="group" />,
              onClick: () => onGroupSpace(record)
            });
            // 只有管理员或者拥有者才显示"空间设置"菜单项
            menuList.push({
              label: '空间设置',
              icon: <SvgIcon name="settings" />,
              onClick: () => onSettingsSpace(record)
            });
          }
        } else {
          const supExtensionArr = [
            'txt',
            'md',
            'pdf',
            'doc',
            'docx',
            'ppt',
            'pptx',
            'xls',
            'xlsx',
            'jpg',
            'jpeg',
            'png'
          ];
          const fileExtension = record.fileName.split('.').pop()?.toLowerCase() || '';
          if (
            supExtensionArr.includes(fileExtension) &&
            record.fileParseStatus === FileParseStatusEnum.success
          ) {
            if (mindMapApp) {
              menuList.push({
                label: '思维导图',
                icon: <SvgIcon name="cloudMindMap" />,
                onClick: () => onMindMap(record)
              });
            }

            if (docApp) {
              menuList.push({
                label: '文档解读',
                icon: <SvgIcon name="cloudDocScreenShot" />,
                onClick: () => onDocScreenshot(record)
              });
            }
          }
          const filePrivilege =
            privilege >= PrivilegeEnum.Manage
              ? privilege
              : record.privileges?.reduce((max, cur) => (cur > max ? cur : max), privilege) ??
                privilege;
          // if (filePrivilege >= PrivilegeEnum.Edit && record.fileType === FileTypeEnum.File) {
          //   menuList.push({
          //     label: '导入知识库',
          //     icon: <SvgIcon name="file_import_line" />,
          //     onClick: () => onIntoDataset(record)
          //   });
          // }

          if (filePrivilege >= PrivilegeEnum.Edit) {
            menuList.push({
              label: '重命名',
              icon: <SvgIcon name="edit" />,
              onClick: () => onEditFolder(record)
            });
          }

          menuList.push({
            label: '下载',
            icon: <SvgIcon name="download" />,
            onClick: () => onDownloadFile(record)
          });
          if (filePrivilege > PrivilegeEnum.View) {
            menuList.push({
              label: '移动',
              icon: <SvgIcon name="drag" />,
              onClick: () => onMoveFile(record)
            });
          }

          if (filePrivilege >= PrivilegeEnum.Edit && record.fileType === FileTypeEnum.File) {
            menuList.push({
              label: '复制',
              icon: <SvgIcon name="copy" />,
              onClick: () => onCopyFile(record)
            });
          }

          if (record.tmbId == userInfo?.tmbId) {
            menuList.push({
              label: '更新',
              icon: <SvgIcon name="copy" />,
              onClick: () => onUpdateFile(record)
            });
          }

          if (filePrivilege >= PrivilegeEnum.Edit) {
            menuList.push({
              label: '共享',
              icon: <SvgIcon name="share2" />,
              onClick: () => {
                onShareFile({
                  id: record.id,
                  name: record.fileName!,
                  fileType: record.fileType!,
                  fileName: record.fileName!
                });
              }
            });
          }
          if (record.tmbId == userInfo?.tmbId || filePrivilege >= PrivilegeEnum.Manage) {
            menuList.push({
              label: '删除',
              icon: <SvgIcon name="trash" />,
              onClick: () => onRemoveFile(record)
            });
          }
        }

        return (
          !!menuList.length && (
            <Flex justify="flex-end">
              <MyMenu
                trigger="hover"
                width={100}
                Button={
                  <Center
                    w={respDims('24fpx')}
                    h={respDims('24fpx')}
                    borderRadius={respDims(4)}
                    cursor="pointer"
                    _hover={{
                      bgColor: '#EFEFEF'
                    }}
                  >
                    <SvgIcon name="more" w={respDims('14fpx')} h={respDims('14fpx')} />
                  </Center>
                }
                menuList={menuList}
                setShowMenu={setShowMenu}
              />
            </Flex>
          )
        );
      }
    }
  ];

  const DndTableRow = useCallback(({ record, ...props }: { record: FileType } & RowProps) => {
    return (
      <DndRow
        {...props}
        data={{
          type: 'file',
          file: {
            ...record,
            bizType: BizTypeEnum.TenantLibrary
          },
          onDragEnd: () => tableRef.current?.reload()
        }}
      />
    );
  }, []);

  return (
    <Box
      w="100%"
      h="100%"
      flexDir="column"
      css={{
        '& .dnd-handle': {
          paddingLeft: '0 !important',
          paddingRight: '0 !important'
        }
      }}
    >
      {isLoading ? (
        <></>
      ) : nav?.path.length === 0 && spacePrimaryList?.length === 0 ? (
        <Flex flexDir="column" w="full" h="full">
          <TableHeader query={{}} tableInstance={tableRef.current as MyTableRef} />
          <EmptyCloud
            onNavChange={onNavChange}
            onConfirm={() => {
              eventBus.emit(EventNameEnum.refreshTree, { parentId: '0' });
              tableRef.current?.reload();
            }}
            isManage={privilege >= PrivilegeEnum.Manage}
          />
        </Flex>
      ) : (
        <MyTable
          ref={tableRef}
          rowKey="rowKey"
          className={styles['file-table']}
          columns={columns}
          components={{
            body: {
              row: privilege >= PrivilegeEnum.Edit ? DndTableRow : undefined
            }
          }}
          rowSelection={rowSelection}
          api={nav?.path.length === 0 ? getSpacePrimaryList : getSpaceFilePages}
          defaultQuery={{
            parentId: nav?.path.length === 0 ? '0' : parentId,
            shareType,
            privilege,
            sortOrder
          }}
          boxStyle={{
            px: 0,
            py: 0,
            overflow: 'visible'
          }}
          tableStyle={{
            paddingLeft: '20px',
            paddingRight: '20px'
          }}
          headerConfig={{ HeaderComponent: TableHeader, showHeader: true, showIfEmpty: true }}
          FooterComponent={TableFooter}
          emptyConfig={{
            EmptyComponent: () => <AddFile path={path} isPersonal={false} />
          }}
          onRow={(record) => ({
            record,
            onClick: () => {
              setSelectFile({ ...record, bizType: 1 });
            }
          })}
          rowClassName={(record) => (record.id === selectFile?.id ? 'selected-row' : '')}
        />
      )}
    </Box>
  );
};

export default Tenant;
