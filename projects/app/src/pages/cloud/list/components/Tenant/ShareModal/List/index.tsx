import {
  spaceShareD<PERSON><PERSON>,
  getSpaceShareList,
  getSpaceShareUpdatePrivilege,
  cloudBatchEditPrivilege
} from '@/api/cloud';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SvgIcon from '@/components/SvgIcon';
import { batchEditPrivilegeTypeEnum, PrivilegeEnum } from '@/constants/api/cloud';
import SearchInput from '@/pages/cloud/components/SearchInput';
import { CloudSpaceShareRequest, CloudSpaceShareResponse } from '@/types/api/cloud';
import { respDims } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import { Box, Button, Flex } from '@chakra-ui/react';
import { Select, TableProps } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import styles from '../../../../../cloud.module.scss';
import { useMemo, useRef, useState } from 'react';

interface RecordType {
  id: number;
  userName: string;
  deptName: string;
  updateTime: string;
  action: string;
}

type QueryType = {
  searchKey: string;
};

type PermissionType = {
  value: string;
  label: {
    shortLabel: string;
    detailedLabel: React.ReactNode;
  };
};
const List = ({
  onAdd,
  shareData
}: {
  onAdd: (data: CloudSpaceShareResponse[]) => void;
  shareData?: { id: string; name: string };
}) => {
  const [selectLoading, setSelectLoading] = useState(false);
  const tableRef = useRef<MyTableRef>(null);
  const [shareCount, setShareCount] = useState(0);
  const [searchKey, setSearchKey] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [buttonText, setButtonText] = useState('修改权限');

  const spaceId = useMemo(() => {
    return shareData!.id;
  }, [shareData]);

  const permissionOptions: PermissionType[] = [
    {
      value: '2',
      label: {
        shortLabel: '可编辑',
        detailedLabel: (
          <Flex flexDir="column">
            <Box fontWeight="500" color="#303133" mb="6px">
              可编辑
            </Box>
            <Box
              fontWeight="400"
              color="#575757"
              whiteSpace="nowrap"
              title="可查看/编辑/上传/下载/复制/移动/共享/审核"
              overflow="hidden"
              textOverflow="ellipsis"
              w={respDims(200)}
            >
              可查看/编辑/上传/下载/复制/移动/共享/审核
            </Box>
          </Flex>
        )
      }
    },
    {
      value: '1',
      label: {
        shortLabel: '可查看/下载',
        detailedLabel: (
          <Flex flexDir="column">
            <Box fontWeight="500" color="#303133" mb="6px">
              可查看/下载
            </Box>
            <Box fontWeight="400" color="#575757">
              可查看/上传/下载
            </Box>
          </Flex>
        )
      }
    },
    {
      value: '0',
      label: {
        shortLabel: '移除',
        detailedLabel: '移除'
      }
    }
  ];

  const getShortLabel = (value: string | undefined): string | undefined => {
    const option = permissionOptions.find((option) => option.value === value);
    return option?.label.shortLabel;
  };

  type PermissionValue = '2' | '1' | '0';

  const valueToPermission: { [key: number]: PermissionValue } = {
    2: '2',
    1: '1',
    0: '0'
  };

  const handleSelectChange = (value: string) => {
    setButtonText(value);
    if (value === '0') {
      MessageBox.warning({
        title: '移除提示',
        content: (
          <Box>
            <Box mt="10px" fontSize="12px">
              将用户移除当前空间，下级子空间同步被移除，确定移除成员？
            </Box>
          </Box>
        ),

        cancelText: '取消',
        okText: '确定',
        okCancel: true,
        onOk: () => {
          cloudBatchEditPrivilege({
            ids: selectedRowKeys,
            operateType: batchEditPrivilegeTypeEnum.Delete,
            privilege: Number(value)
          }).then((res) => {
            if (res) {
              setSelectedRowKeys([]);
              setTimeout(() => {
                tableRef.current?.reload();
              }, 300);
            }
          });
        }
      });
    } else {
      const params = {
        ids: selectedRowKeys,
        operateType: batchEditPrivilegeTypeEnum.Update,
        privilege: Number(value)
      };
      cloudBatchEditPrivilege(params).then((res) => {
        if (res) {
          tableRef.current?.reload();
        }
      });
    }
  };

  const getSpaceShareData = async (props: CloudSpaceShareRequest) => {
    let data = await getSpaceShareList(props);
    setShareCount(data.length);
    if (searchKey.trim() !== '') {
      data = data.filter((item) => item.userName.includes(searchKey.trim()));
    }

    return data;
  };

  const handlePermissionChange = async (value: string, record: RecordType) => {
    if (value === '0') {
      MessageBox.warning({
        title: '移除提示',
        content: (
          <Box>
            <Box mt="10px" fontSize="12px">
              将用户移除当前空间，下级子空间同步被移除，确定移除成员？
            </Box>
          </Box>
        ),

        cancelText: '取消',
        okText: '确定',
        okCancel: true,
        onOk: () => {
          spaceShareDelete({ id: String(record.id) }).then((res) => {
            if (res) {
              setSelectLoading(false);
              tableRef.current?.reload();
            }
          });
        }
      });
    } else {
      const params = {
        id: String(record.id),
        privilege: Number(value)
      };
      getSpaceShareUpdatePrivilege(params).then((res) => {
        if (res) {
          setSelectLoading(false);
          tableRef.current?.reload();
        }
      });
    }
    setButtonText('修改权限');
  };

  const rowSelection: TableRowSelection<CloudSpaceShareResponse> = {
    type: 'checkbox',
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    columnWidth: 30,
    onChange(selectedRowKeys, selectedRows, info) {
      setSelectedRowKeys(selectedRowKeys as string[]);
      setButtonText('修改权限');
    },
    getCheckboxProps: (record: CloudSpaceShareResponse) => {
      return {
        disabled: record.privilege === PrivilegeEnum.Owner
      };
    }
  };

  const columns: TableProps['columns'] = [
    {
      title: '全选',
      dataIndex: 'userName',
      width: 280,
      render: (text, record) => (
        <Flex flexDirection="column">
          <Flex alignItems="center" mb={respDims(8)}>
            {text && (
              <Box fontSize="15px" fontWeight="500" mr={respDims(4)} color="#303133">
                {text}
              </Box>
            )}
            <Box
              fontSize="14px"
              fontWeight="500"
              color="primary.5"
              p="2px 8px"
              bgColor="#E8F3FF"
              borderRadius="6px"
            >
              {record.deptName}
            </Box>
          </Flex>
          <Box fontSize="14px" fontWeight="400" color="#606266">
            继承下级空间权限
          </Box>
        </Flex>
      )
    },
    {
      title: (
        <Flex mt={respDims(10)} h="12px" w="100%" alignItems="center">
          {selectedRowKeys.length > 0 ? (
            <Select
              className={buttonText == '修改权限' ? styles['select-content'] : ''}
              value={
                buttonText === '修改权限'
                  ? '修改权限'
                  : getShortLabel(valueToPermission[Number(buttonText)])
              }
              onChange={handleSelectChange}
              style={{ width: '200px' }}
              dropdownStyle={{ zIndex: 2000 }}
              placeholder="请选择权限"
              loading={selectLoading}
            >
              {permissionOptions.map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label.detailedLabel}
                </Select.Option>
              ))}
            </Select>
          ) : (
            <Box h="12px"></Box>
          )}
        </Flex>
      ),
      dataIndex: 'privilege',
      render: (text, record) => (
        <Box>
          {text === PrivilegeEnum.Manage ? (
            <Box
              fontSize="14px"
              color="#303133"
              fontWeight="400"
              p="5px 12px"
              bgColor="#F6F6F6"
              borderRadius="8px"
              width="220px"
            >
              可管理
            </Box>
          ) : (
            <Select
              className={styles['ground-select']}
              value={valueToPermission[text] ? getShortLabel(valueToPermission[text]) : undefined}
              onChange={(value) => handlePermissionChange(value, record)}
              style={{ width: '220px' }}
              dropdownStyle={{ zIndex: 2000 }}
              placeholder="请选择权限"
              loading={selectLoading}
            >
              {permissionOptions.map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label.detailedLabel}
                </Select.Option>
              ))}
            </Select>
          )}
        </Box>
      )
    }
  ];

  const onSearch = (val: QueryType) => {
    setSearchKey(val.searchKey);
  };

  return (
    <Flex direction="column" p="24px 32px" h="600px">
      <MyTable
        headerConfig={{
          HeaderComponent: () => (
            <>
              <Box w="100%">
                <Flex alignItems="center" w="100%">
                  <SearchInput
                    bgColor="#F6F6F6"
                    border="none"
                    borderRadius="8px"
                    placeholder="请输入成员名称"
                    value={searchKey}
                    flex="1"
                    onSearch={(e) => onSearch?.({ searchKey: e })}
                  />
                  <Button w="120px" ml="10px" onClick={() => onAdd(tableRef.current?.data || [])}>
                    <SvgIcon name="plus" w="12px" h="12px" />

                    <Box ml="8px">添加成员</Box>
                  </Button>
                </Flex>
                <Box fontSize="14px" color="#606266" fontWeight="400" mt="10px">
                  {`授权列表(${shareCount || 0}人)`}
                </Box>
              </Box>
            </>
          )
        }}
        ref={tableRef}
        rowKey="id"
        rowSelection={rowSelection}
        api={getSpaceShareData}
        defaultQuery={{ spaceId, searchKey }}
        pageConfig={{ showPaginate: false }}
        columns={columns}
        boxStyle={{ px: 0, py: 0 }}
      />
    </Flex>
  );
};

export default List;
