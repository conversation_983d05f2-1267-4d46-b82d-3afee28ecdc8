import MyModal from '@/components/MyModal';
import { Box } from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';
import List from './List';
import SelectPersonnel from './Select';
import { BaseModalProps } from '@/types/cloud';
import { CloudSpaceShareResponse } from '@/types/api/cloud';

const GroupModal = ({ onClose, groupData }: BaseModalProps) => {
  const [current, setCurrent] = useState('list' as 'list' | 'select');
  const [selectSpaceData, setSelectSpaceData] = useState<CloudSpaceShareResponse[]>();

  const content = useMemo(() => {
    if (current === 'list') {
      return (
        <List
          groupData={groupData}
          onAdd={(data) => {
            setCurrent('select'), setSelectSpaceData(data);
          }}
        />
      );
    }
    return (
      <SelectPersonnel
        selectSpaceData={selectSpaceData!}
        groupData={groupData}
        onBack={() => setCurrent('list')}
      />
    );
  }, [current]);

  return (
    <MyModal
      title={current === 'list' ? groupData?.name : '添加成员'}
      isOpen
      isCentered
      onClose={onClose}
      minW="700px"
      minH="500px"
    >
      <Box w="100%" h="100%">
        {content}
      </Box>
    </MyModal>
  );
};

export default GroupModal;
