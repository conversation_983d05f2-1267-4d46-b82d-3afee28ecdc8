import { spaceAuditorAdd, spaceAuditorUpdate, getSpaceTree } from '@/api/cloud';
import MyModal from '@/components/MyModal';
import { BaseModalProps } from '@/types/cloud';
import { Toast } from '@/utils/ui/toast';
import { Button, Box, Flex, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Form, GetProp, Select, theme, Transfer, TransferProps, Tree, TreeDataNode } from 'antd';
import { SelectProps } from 'antd/lib';
import { useCallback, useEffect, useMemo, useState } from 'react';
import styles from '../../../../../cloud.module.scss';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { SpaceAuditorAddParams, SpaceAuditorAddType, SpaceType } from '@/types/api/cloud';
import { getListFromPage } from '@/utils/api';
import { getTenantUserPage } from '@/api/tenant';
import { TenantMemberStatusEnum } from '@/constants/api/tenant';

type FormType = {
  auditorIds: string[];
};

type TransferItem = GetProp<TransferProps, 'dataSource'>[number];

interface TreeTransferProps {
  dataSource: TreeDataNode[];
  targetKeys: TransferProps['targetKeys'];
  onChange: TransferProps['onChange'];
  oneWay?: boolean;
}

const isChecked = (selectedKeys: React.Key[], eventKey: React.Key) =>
  selectedKeys.includes(eventKey);

const generateTree = (
  treeNodes: TreeDataNode[] = [],
  checkedKeys: TreeTransferProps['targetKeys'] = []
): TreeDataNode[] =>
  treeNodes.map(({ children, ...props }) => ({
    ...props,
    children: generateTree(children, checkedKeys),
    checked: checkedKeys.includes(props.key as string)
  }));

const flattenTree = (treeNodes: TreeDataNode[] = []): string[] => {
  let keys: string[] = [];
  treeNodes.forEach(({ key, children }) => {
    keys.push(key as string);
    if (children) {
      keys = keys.concat(flattenTree(children));
    }
  });
  return keys;
};

const handleCheck = (checkedKeys: React.Key[], { node: { key, children } }: any) => {
  const allKeys = flattenTree(children);
  if (checkedKeys.includes(key)) {
    allKeys.push(key as string);
  } else {
    const index = allKeys.indexOf(key as string);
    if (index > -1) {
      allKeys.splice(index, 1);
    }
  }
  return allKeys;
};

const TreeTransfer: React.FC<TreeTransferProps> = ({
  dataSource,
  targetKeys = [],
  onChange,
  ...restProps
}) => {
  const { token } = theme.useToken();
  const [searchValue, setSearchValue] = useState('');

  const transferDataSource: TransferItem[] = [];
  function flatten(list: TreeDataNode[] = []) {
    list.forEach((item) => {
      transferDataSource.push(item as TransferItem);
      flatten(item.children);
    });
  }
  flatten(dataSource);

  const generateFilteredTree = (
    treeNodes: TreeDataNode[] = [],
    searchValue: string
  ): TreeDataNode[] => {
    return treeNodes
      .map((node) => {
        const children = generateFilteredTree(node.children, searchValue);
        if (
          String(node.title).toLowerCase().includes(searchValue.toLowerCase()) ||
          children.length
        ) {
          return {
            ...node,
            children
          };
        }
        return null;
      })
      .filter((node) => node !== null) as TreeDataNode[];
  };

  const filteredTreeData = generateFilteredTree(dataSource, searchValue);

  const filteredDataSource = transferDataSource.filter((item) =>
    item.title!.toLowerCase().includes(searchValue.toLowerCase())
  );

  return (
    <Transfer
      {...restProps}
      targetKeys={targetKeys}
      dataSource={filteredDataSource}
      className={styles['tree-transfer']}
      render={(item) => item.title!}
      showSelectAll={false}
      listStyle={{
        height: 420,
        overflow: 'auto'
      }}
      locale={{
        itemUnit: '',
        itemsUnit: '',
        notFoundContent: '暂无数据',
        titles: ['选择上传审核文件夹', `已选 ${targetKeys.length} 条数据`]
      }}
      onChange={onChange}
    >
      {({ direction, onItemSelect, selectedKeys }) => {
        if (direction === 'left') {
          const checkedKeys = [...selectedKeys, ...targetKeys];
          return (
            <div style={{ padding: token.paddingXS }}>
              <InputGroup
                boxSizing="border-box"
                overflow="hidden"
                border="1px solid #E5E7EB"
                bg="#f4f5f6"
                borderRadius={respDims(8)}
                w="100%"
                h={respDims('36fpx')}
                mb="10px"
              >
                <InputLeftElement
                  h="100%"
                  cursor="pointer"
                  _hover={{
                    bgColor: '#EFEFEF'
                  }}
                >
                  <SvgIcon
                    name="search"
                    w={respDims('14fpx')}
                    h={respDims('14fpx')}
                    color="#4E5969"
                  />
                </InputLeftElement>
                <Input
                  value={searchValue}
                  w="100%"
                  h="100%"
                  color="#303133"
                  bgColor="transparent"
                  fontSize={respDims('14fpx')}
                  border="none"
                  placeholder="请输入文件夹名称"
                  _focus={{
                    border: 'none',
                    outline: 'none',
                    boxShadow: 'none'
                  }}
                  onChange={(e) => setSearchValue(e.target.value)}
                  _placeholder={{
                    color: '#606266',
                    fontSize: 'inherit'
                  }}
                ></Input>
              </InputGroup>

              <Tree
                blockNode
                checkable
                defaultExpandAll
                checkedKeys={checkedKeys}
                treeData={generateTree(filteredTreeData, targetKeys)}
                onCheck={(checkedKeys, event) => {
                  const allKeys = handleCheck(checkedKeys as string[], event);
                  onItemSelect(
                    event.node.key as string,
                    !isChecked(checkedKeys as string[], event.node.key)
                  );
                  onChange?.([...new Set([...targetKeys, ...allKeys])], 'right', allKeys);
                }}
                onSelect={(selectedKeys, info) => {
                  const key = info.node.key as string;
                  const isCurrentlyChecked = isChecked(checkedKeys, key);
                  const newCheckedKeys = isCurrentlyChecked
                    ? checkedKeys.filter((k) => k !== key)
                    : [...checkedKeys, key];

                  const allKeys = handleCheck(newCheckedKeys, { node: info.node });
                  onItemSelect(key, !isCurrentlyChecked);
                  onChange?.([...new Set([...targetKeys, ...allKeys])], 'right', allKeys);
                }}
              />
            </div>
          );
        }
      }}
    </Transfer>
  );
};

const AuditModal = ({
  onClose,
  onSuccess,
  isAdd,
  auditPanelData
}: { isAdd?: boolean; auditPanelData?: SpaceAuditorAddType } & BaseModalProps) => {
  const [initialValues, setInitialValues] = useState<FormType>({
    auditorIds: []
  });
  const transformTreeData = (data: SpaceType[]): TreeDataNode[] => {
    return data.map((item) => ({
      key: item.id,
      title: item.spaceName,
      children: item.children ? transformTreeData(item.children) : []
    }));
  };

  const { data } = useQuery(['spaceTree'], async () => {
    const response = await getSpaceTree();
    return transformTreeData(response);
  });

  const [form] = Form.useForm<FormType>();

  const { data: tenantUsers } = useQuery(['tenantUsers'], () =>
    getListFromPage(getTenantUserPage).then((res) =>
      res.filter((it) => it.status !== TenantMemberStatusEnum.Forbidden)
    )
  );

  const auditors = useMemo(
    () =>
      tenantUsers?.map((it) => ({
        value: it.id,
        label: it.username,
        renderList: [it.username, it.deptName, it.roleName, it.phone]
      })) || [],
    [tenantUsers]
  );

  const [targetKeys, setTargetKeys] = useState<TreeTransferProps['targetKeys']>([]);
  const onChange: TreeTransferProps['onChange'] = (keys) => {
    setTargetKeys(keys);
  };

  const auditorOptionRender = useCallback<NonNullable<SelectProps['optionRender']>>(
    ({ data: { renderList } }) => (
      <Flex alignItems="center">
        {renderList.map((it: string, index: number) => (
          <Box
            lineHeight="2em"
            key={index}
            {...(index === 0 ? {} : { ml: '10px', color: 'rgba(0,0,0,0.6)', fontSize: '12px' })}
          >
            {it}
          </Box>
        ))}
      </Flex>
    ),
    []
  );

  const auditorFilterOption = useCallback(
    (input: string, option?: (typeof auditors)[number]) =>
      !!option && option.renderList.some((it) => it.includes(input)),
    []
  );

  const { mutate: onSubmit } = useMutation({
    mutationFn: (data: FormType) => {
      if (targetKeys!.length === 0 && isAdd) {
        Toast.error('请选择审核文件夹');
        return Promise.reject();
      }

      const appData: SpaceAuditorAddParams = {
        spaceIds: targetKeys!,
        auditorIds: data.auditorIds
      };
      return isAdd ? spaceAuditorAdd({ ...appData }) : spaceAuditorUpdate({ ...appData });
    },
    onSuccess: () => {
      Toast.success('操作成功');
      onSuccess?.();
      onClose?.();
    }
  });

  useEffect(() => {
    if (!isAdd) {
      setInitialValues({
        auditorIds: [auditPanelData!.tmbId]
      });
      setTargetKeys(auditPanelData!.spaceIds.map((item: number) => String(item)));
      form.setFieldValue('auditorIds', [String(auditPanelData!.tmbId)]);
    }
  }, [isAdd, auditPanelData, form]);

  return (
    <MyModal title="资料审核设置" isOpen isCentered onClose={onClose} minW="600px">
      <Box p="32px 32px 24px 32px">
        <Flex align="center" mb="16px">
          <Box
            width="24px"
            height="24px"
            bg="primary.500"
            borderRadius="50%"
            display="flex"
            justifyContent="center"
            alignItems="center"
            color="white"
            fontWeight="bold"
            marginRight="8px"
          >
            1
          </Box>
          <Box fontSize="16px" color="primary.500">
            选择文件夹
          </Box>
        </Flex>

        <Box m="16px 0" w="100%">
          <TreeTransfer dataSource={data!} targetKeys={targetKeys} onChange={onChange} oneWay />
        </Box>

        <Flex align="center" mb="16px">
          <Box
            width="24px"
            height="24px"
            bg="primary.500"
            borderRadius="50%"
            display="flex"
            justifyContent="center"
            alignItems="center"
            color="white"
            fontWeight="bold"
            marginRight="8px"
          >
            2
          </Box>
          <Box fontSize="16px" color="primary.500">
            设置审核人
          </Box>
        </Flex>

        <Form form={form} initialValues={initialValues} onFinish={onSubmit}>
          <Form.Item
            name="auditorIds"
            label="空间审核人"
            rules={[{ required: true, message: '请选择空间审核人' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择空间审核人"
              disabled={!isAdd}
              options={auditors}
              dropdownStyle={{ zIndex: 2000 }}
              popupMatchSelectWidth={false}
              optionRender={auditorOptionRender}
              filterOption={auditorFilterOption}
            />
          </Form.Item>
        </Form>
        <Box position="sticky" bottom={0} bg="white" p={4} mb="-50px">
          <Flex justify="end">
            <Button variant={'grayBase'} onClick={onClose}>
              取消
            </Button>
            <Button
              style={{ marginLeft: '16px' }}
              onClick={() => {
                form
                  .validateFields()
                  .then((values) => {
                    onSubmit(values);
                  })
                  .catch((info) => {
                    console.log('Validate Failed:', info);
                  });
              }}
            >
              确定
            </Button>
          </Flex>
        </Box>
      </Box>
    </MyModal>
  );
};

export default AuditModal;
