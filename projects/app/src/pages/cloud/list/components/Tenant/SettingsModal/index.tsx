import MyModal from '@/components/MyModal';
import { Box, Flex } from '@chakra-ui/react';
import AuditPanel from './AuditPanel';
import { useMemo, useState } from 'react';
import { respDims } from '@/utils/chakra';
import { BaseModalProps } from '@/types/cloud';

const settings = [
  {
    label: '资料审核设置',
    Component: AuditPanel
  }
];

const SettingsModal = ({ onClose }: {} & BaseModalProps) => {
  const [current, setCurrent] = useState(0);

  const content = useMemo(() => {
    const { Component } = settings[current];
    return <Component />;
  }, [current]);

  return (
    <MyModal title="设置" isOpen isCentered minW="750px" h={respDims(680)} onClose={onClose}>
      <Flex w="100%" h="100%">
        <Box
          w={respDims(162)}
          borderRight="1px solid #E7E7E7"
          flexShrink="0"
          px={respDims(14)}
          py={respDims(10)}
        >
          {settings.map((item, index) => (
            <Box
              key={item.label}
              py={respDims(8)}
              px={respDims(10)}
              color="#303133"
              bgColor={index === current ? '#F8FAFC' : 'transparent'}
              fontSize={respDims('15fpx')}
              lineHeight={respDims('18fpx')}
              borderRadius={respDims(8)}
              cursor="pointer"
            >
              {item.label}
            </Box>
          ))}
        </Box>
        <Box flex="1" p="10px 16px 10px 23px" overflow="hidden">
          {content}
        </Box>
      </Flex>
    </MyModal>
  );
};

export default SettingsModal;
