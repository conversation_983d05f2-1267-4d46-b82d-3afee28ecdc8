import MyModal from '@/components/MyModal';
import { BaseModalProps } from '@/types/cloud';
import {
  Button,
  Form,
  GetProp,
  Select,
  theme,
  Transfer,
  TransferProps,
  Tree,
  TreeDataNode
} from 'antd';
import { useState } from 'react';
import styles from '../../../../cloud.module.scss';
import { Box, Flex, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Toast } from '@/utils/ui/toast';
import { getClientTenantUserDeptUserTree } from '@/api/user';
import { getFileShareMixFolderShare } from '@/api/cloud';
import { FilesMixFolderShareParams, FileType } from '@/types/api/cloud';
import { useNotificationStore } from '@/store/useTificationContext';
import { PrivilegeEnum } from '@/constants/api/cloud';
import { CloseOutlined } from '@ant-design/icons';
import MyTooltip from '@/components/MyTooltip';

type FormType = {
  privilege: number;
};

type TransferItem = GetProp<TransferProps, 'dataSource'>[number];

interface TreeTransferProps {
  dataSource: TreeDataNode[];
  targetKeys: { key: string; type: number; privilege?: PrivilegeEnum }[];
  onChange: (keys: { key: string; type: number }[]) => void;
}

type PermissionType = {
  value: string;
  label: {
    shortLabel: string;
    detailedLabel: React.ReactNode;
  };
};

const permissionOptions: PermissionType[] = [
  {
    value: '3',
    label: {
      shortLabel: '可管理',
      detailedLabel: (
        <Flex flexDir="column">
          <Box fontWeight="500" color="#303133" mb="6px">
            可管理
          </Box>
          <Box fontWeight="400" color="#575757">
            拥有空间及文件夹所有权限
          </Box>
        </Flex>
      )
    }
  },
  {
    value: '2',
    label: {
      shortLabel: '可编辑',
      detailedLabel: (
        <Flex flexDir="column">
          <Box fontWeight="500" color="#303133" mb="6px">
            可编辑
          </Box>
          <Box
            fontWeight="400"
            color="#575757"
            whiteSpace="nowrap"
            title="可查看/编辑/上传/下载/复制/移动/共享/审核"
            overflow="hidden"
            textOverflow="ellipsis"
            w={respDims(170)}
          >
            可查看/编辑/上传/下载/复制/移动/共享/审核
          </Box>
        </Flex>
      )
    }
  },
  {
    value: '1',
    label: {
      shortLabel: '可查看/下载',
      detailedLabel: (
        <Flex flexDir="column">
          <Box fontWeight="500" color="#303133" mb="6px">
            可查看/下载
          </Box>
          <Box fontWeight="400" color="#575757">
            可查看/上传/下载
          </Box>
        </Flex>
      )
    }
  }
];

const getShortLabel = (value: string | undefined): string | undefined => {
  const option = permissionOptions.find((option) => option.value === value);
  return option?.label.shortLabel;
};

type PermissionValue = '3' | '2' | '1';

const valueToPermission: { [key: number]: PermissionValue } = {
  3: '3',
  2: '2',
  1: '1'
};

const generateTree = (
  treeNodes: TreeDataNode[] = [],
  checkedKeys: TreeTransferProps['targetKeys'] = []
): TreeDataNode[] =>
  treeNodes.map(({ children, ...props }) => ({
    ...props,
    children: generateTree(children, checkedKeys)
  }));

const TreeTransfer: React.FC<TreeTransferProps> = ({
  dataSource,
  targetKeys = [],
  onChange,
  ...restProps
}) => {
  const { token } = theme.useToken();
  const [searchValue, setSearchValue] = useState('');
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);

  const transferDataSource: TransferItem[] = [];
  function flatten(list: TreeDataNode[] = []) {
    list.forEach((item) => {
      transferDataSource.push({ ...item, type: (item as any).type } as TransferItem);
      flatten(item.children);
    });
  }
  flatten(dataSource);

  const generateFilteredTree = (
    treeNodes: TreeDataNode[] = [],
    searchValue: string
  ): TreeDataNode[] => {
    return treeNodes
      .map((node) => {
        const children = generateFilteredTree(node.children, searchValue);
        if (
          String(node.title).toLowerCase().includes(searchValue.toLowerCase()) ||
          children.length
        ) {
          return {
            ...node,
            children
          };
        }
        return null;
      })
      .filter((node) => node !== null) as TreeDataNode[];
  };

  const filteredTreeData = generateFilteredTree(dataSource, searchValue);

  const filteredDataSource = transferDataSource.filter((item) =>
    item.title!.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleCheck = (checkedKeys: any, info: any) => {
    const { node, checked } = info;
    let newTargetKeys = [...targetKeys];
    let newCheckedKeys = [...checkedKeys];

    const addKeys = (item: TreeDataNode) => {
      if (String(item.key).startsWith('user-') && !newCheckedKeys.includes(item.key)) {
        newCheckedKeys.push(item.key);
      }
      item.children?.forEach(addKeys);
    };

    const removeKeys = (item: TreeDataNode) => {
      newTargetKeys = newTargetKeys.filter((k) => k.key !== item.key);
      newCheckedKeys = newCheckedKeys.filter((k) => k !== item.key);
      item.children?.forEach(removeKeys);
    };

    if (checked) {
      if (String(node.key).startsWith('dept-')) {
        node.children?.forEach(removeKeys);
        newTargetKeys = newTargetKeys.filter(
          (k) => !k.key.startsWith('user-') && !k.key.startsWith('dept-')
        );
        if (!newTargetKeys.some((k) => k.key === node.key)) {
          newTargetKeys.push({ key: String(node.key), type: 2 });
        }
      } else {
        const parentKey = node.key.split('-')[0];
        newTargetKeys = newTargetKeys.filter((k) => k.key !== `dept-${parentKey}`);
        newTargetKeys.push({ key: String(node.key), type: 1 });
      }
    } else {
      removeKeys(node);
    }

    onChange(newTargetKeys);
    setCheckedKeys(newCheckedKeys);
  };

  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    const { node } = info;
    const key = String(node.key);
    const isCurrentlyChecked = selectedKeys.includes(key);
    handleCheck(
      isCurrentlyChecked ? selectedKeys.filter((k) => k !== key) : [...selectedKeys, key],
      {
        checked: !isCurrentlyChecked,
        node: node
      }
    );
  };

  return (
    <Transfer
      {...restProps}
      targetKeys={targetKeys.map((item) => item.key)}
      dataSource={filteredDataSource}
      className={styles['tree-transfer']}
      render={(item) => item.title!}
      showSelectAll={false}
      listStyle={{
        height: 420,
        overflow: 'auto'
      }}
      locale={{
        itemUnit: '',
        itemsUnit: '',
        notFoundContent: '暂无数据',
        titles: [
          <>
            <InputGroup
              boxSizing="border-box"
              overflow="hidden"
              borderRadius={respDims(4)}
              w={respDims('246fpx')}
              h={respDims('36fpx')}
            >
              <InputLeftElement
                h="100%"
                cursor="pointer"
                _hover={{
                  bgColor: '#EFEFEF'
                }}
              >
                <SvgIcon
                  name="search"
                  w={respDims('14fpx')}
                  h={respDims('14fpx')}
                  color="#4E5969"
                />
              </InputLeftElement>
              <Input
                value={searchValue}
                w="100%"
                h="100%"
                color="#303133"
                bgColor="transparent"
                fontSize={respDims('14fpx')}
                border="none"
                placeholder="请输入成员名称"
                _focus={{
                  border: 'none',
                  outline: 'none',
                  boxShadow: 'none'
                }}
                onChange={(e) => setSearchValue(e.target.value)}
                _placeholder={{
                  color: '#606266',
                  fontSize: 'inherit'
                }}
              />
            </InputGroup>
          </>,
          `已选 ${targetKeys.length} 个成员`
        ]
      }}
      onChange={(newTargetKeys) => {
        const newKeys = transferDataSource
          .filter((item) => newTargetKeys.includes(item.key))
          .map((item) => ({ key: item.key, type: item.type }));
        onChange(newKeys);
      }}
    >
      {({ direction, onItemSelect, selectedKeys }) => {
        if (direction === 'left') {
          const combinedCheckedKeys = [...checkedKeys, ...targetKeys.map((item) => item.key)];
          return (
            <div style={{ padding: token.paddingXS }}>
              <Tree
                checkable
                defaultExpandAll
                checkedKeys={combinedCheckedKeys}
                treeData={generateTree(filteredTreeData, targetKeys)}
                onCheck={handleCheck}
                onSelect={(_, info) => handleSelect(combinedCheckedKeys, info)}
              />
            </div>
          );
        } else if (direction === 'right') {
          return (
            <div style={{ padding: token.paddingXS }}>
              {targetKeys.map((item) => {
                const selectedItem = transferDataSource.find((data) => data.key === item.key);
                return (
                  <Flex
                    key={item.key}
                    bgColor="#F8FAFC"
                    borderRadius="8px"
                    alignItems="center"
                    p="15px"
                    mb="17px"
                    pos="relative"
                  >
                    <MyTooltip label={selectedItem?.title}>
                      <Box
                        whiteSpace="nowrap"
                        overflow="hidden"
                        textOverflow="ellipsis"
                        w={respDims(100)}
                      >
                        {selectedItem?.title}
                      </Box>
                    </MyTooltip>
                    {item.privilege === PrivilegeEnum.Owner ? (
                      <Box
                        fontSize="14px"
                        color="#303133"
                        fontWeight="400"
                        p="5px 12px"
                        bgColor="#F6F6F6"
                        borderRadius="8px"
                        width="170px"
                      >
                        拥有所有权
                      </Box>
                    ) : (
                      <Select
                        className={styles['ground-select']}
                        value={String(
                          getShortLabel(valueToPermission[Number(item.privilege)]) ||
                            getShortLabel(valueToPermission[Number(permissionOptions[2]?.value)])
                        )}
                        style={{ width: '170px' }}
                        dropdownStyle={{ zIndex: 2000 }}
                        placeholder="请选择权限"
                        onChange={(value) => {
                          const newTargetKeys = targetKeys.map((key) =>
                            key.key === item.key ? { ...key, privilege: String(value) } : key
                          );
                          onChange(newTargetKeys);
                        }}
                      >
                        {permissionOptions.map((option) => (
                          <Select.Option key={option.value} value={option.value}>
                            {option.label.detailedLabel}
                          </Select.Option>
                        ))}
                      </Select>
                    )}

                    {item.privilege !== PrivilegeEnum.Owner && (
                      <CloseOutlined
                        onClick={() => {
                          const newTargetKeys = targetKeys.filter((key) => key.key !== item.key);
                          onChange(newTargetKeys);

                          const removedItem = transferDataSource.find(
                            (data) => data.key === item.key
                          );
                          const removeKeysRecursively = (node: TreeDataNode) => {
                            setCheckedKeys((prev) => prev.filter((key) => key !== node.key));
                            node.children?.forEach(removeKeysRecursively);
                          };

                          if (removedItem && String(removedItem.key).startsWith('dept-')) {
                            const deptId = removedItem.key.split('-')[1];

                            const childKeys = transferDataSource
                              .filter(
                                (data) =>
                                  data.key.startsWith(`user-`) || data.key.startsWith(`dept-`)
                              )
                              .map((data) => data.key);

                            setCheckedKeys((prev) =>
                              prev.filter((key) => key !== item.key && !childKeys.includes(key))
                            );

                            const newCheckedKeys = checkedKeys.filter(
                              (key) => key !== item.key && !childKeys.includes(key)
                            );
                            setCheckedKeys(newCheckedKeys);

                            const node = dataSource.find((data) => data.key === removedItem.key);
                            if (node) {
                              removeKeysRecursively(node);
                            }
                          } else {
                            setCheckedKeys((prev) => prev.filter((key) => key !== item.key));
                          }
                        }}
                        style={{
                          marginLeft: '8px',
                          cursor: 'pointer',
                          color: '#606266',
                          fontSize: '10px',
                          backgroundColor: '#F8FAFC',
                          border: '1px solid #D1D5DB',
                          borderRadius: '50%',
                          padding: '4px',
                          position: 'absolute',
                          right: '-4px',
                          top: '-4px'
                        }}
                      />
                    )}
                  </Flex>
                );
              })}
            </div>
          );
        }
      }}
    </Transfer>
  );
};

const BatchShareModal = ({
  onSuccess,
  onClose,
  selectedData
}: {
  onSuccess?: () => void;
  onClose?: () => void;
  selectedData?: FileType[];
} & BaseModalProps) => {
  const [targetKeys, setTargetKeys] = useState<TreeTransferProps['targetKeys']>([]);
  const { fetchUnreadCount } = useNotificationStore();

  const transformTreeData = (data: any[]): TreeDataNode[] => {
    return data.map((item) => {
      const children = item.children ? transformTreeData(item.children) : [];
      const deptUsers = item.deptUsers
        ? item.deptUsers.map((user: any) => ({
            key: `user-${user.tmbId}`,
            title: user.userName,
            type: 1,
            isLeaf: true
          }))
        : [];
      return {
        key: `dept-${item.deptId}`,
        title: item.name,
        type: 2,
        children: [...children, ...deptUsers]
      };
    });
  };

  const { data } = useQuery(['shareTree'], async () => {
    const response = await getClientTenantUserDeptUserTree();
    return transformTreeData(response);
  });

  const onChange: TreeTransferProps['onChange'] = (keys) => {
    setTargetKeys(keys);
  };

  const generateTree = (
    treeNodes: TreeDataNode[] = [],
    checkedKeys: TreeTransferProps['targetKeys'] = []
  ): TreeDataNode[] =>
    treeNodes.map(({ children, ...props }) => ({
      ...props,
      children: generateTree(children, checkedKeys)
    }));

  const transferDataSource: TransferItem[] = [];
  function flatten(list: TreeDataNode[] = []) {
    list.forEach((item) => {
      transferDataSource.push({ ...item, type: (item as any).type } as TransferItem);
      flatten(item.children);
    });
  }
  flatten(data);

  const { mutate: onSubmit } = useMutation({
    mutationFn: (data: FormType) => {
      if (targetKeys!.length === 0) {
        Toast.error('请选择成员');
        return Promise.reject();
      }

      const shareObjects = targetKeys.map((item) => ({
        tmbId: item.type == 1 ? item.key.split('-')[1] || '' : undefined,
        deptId: item.type == 2 ? item.key.split('-')[1] || '' : undefined,
        privilege: item.privilege || Number(permissionOptions[2].value),
        objectType: item.type
      }));
      const files = selectedData?.map((item) => ({
        spaceId: item.id,
        fileType: item.fileType
      }));
      const appData: FilesMixFolderShareParams = {
        files: files!,
        shareObjects: shareObjects!
      };
      return getFileShareMixFolderShare({ ...appData });
    },
    onSuccess: () => {
      Toast.success('操作成功');
      onSuccess!();
      fetchUnreadCount();
      onClose!();
    }
  });

  return (
    <MyModal title="批量分享" isOpen isCentered onClose={onClose} minW="700px" minH="500px">
      <Flex direction="column" alignItems="self-start" p="24px 32px">
        <Box m="16px 0" w="100%">
          <TreeTransfer dataSource={data!} targetKeys={targetKeys} onChange={onChange} />
        </Box>

        <Box w="100%" h="90px">
          <Form onFinish={onSubmit}>
            <Form.Item>
              <Flex justify="end">
                <Button htmlType="button" onClick={onClose}>
                  取消
                </Button>
                <Button style={{ marginLeft: '16px' }} type="primary" htmlType="submit">
                  确定
                </Button>
              </Flex>
            </Form.Item>
          </Form>
        </Box>
      </Flex>
    </MyModal>
  );
};

export default BatchShareModal;
