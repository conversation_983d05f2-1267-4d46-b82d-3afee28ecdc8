import MyModal from '@/components/MyModal';
import { BaseModalProps } from '@/types/cloud';
import { Box, Center, Flex } from '@chakra-ui/react';
import { FileType, SpaceType } from '@/types/api/cloud';
import { Button } from 'antd';
import { respDims } from '@/utils/chakra';
import { useQuery } from '@tanstack/react-query';
import {
  getFolderList,
  getSpaceList,
  updateFileParent,
  updateFolderParent,
  updateSpaceParent,
  batchUpdateParent
} from '@/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { useState } from 'react';
import { BizTypeEnum, FileTypeEnum, MoveTypeEnum } from '@/constants/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import { useRequest } from '@/hooks/useRequest';
const MoveModal = ({
  folder,
  folders,
  isBatch,
  bizType,
  onClose,
  onSuccess
}: {
  bizType: BizTypeEnum;
  folders?: FileType[];
  isBatch?: boolean;
  folder?: FileType;
} & BaseModalProps) => {
  const [getSpaceData, setGetSpaceData] = useState<SpaceType[]>([]);
  const [parentId, setParentId] = useState<string>('0');
  const [navTitles, setNavTitles] = useState<string[]>(['全部']);
  const [pathStack, setPathStack] = useState<{ id: string; name: string }[]>([]);
  const [selectedSpace, setSelectedSpace] = useState<SpaceType | null>(null);
  const MAX_TITLE_LENGTH = 11;
  const calculateMaxLength = (totalTitles: number) => {
    const baseLength = 40; // 假设容器总宽度可以容纳40个字符
    const separatorLength = 3; // 分隔符 " / " 的长度
    const maxLength = Math.floor((baseLength - (totalTitles - 1) * separatorLength) / totalTitles);
    return maxLength;
  };

  const formatTitle = (title: string, maxLength: number) => {
    if (title.length <= maxLength) return title;
    return `${title.slice(0, maxLength)}...`;
  };

  const { isLoading, mutate: submitFn } = useRequest({
    mutationFn: async () => {
      if (!isBatch) {
        let params = {
          id: folder?.id || '',
          parentId: folder?.fileType === FileTypeEnum.Folder ? String(selectedSpace?.id) || '' : '',
          spaceId:
            bizType === BizTypeEnum.TenantLibrary && folder?.fileType === FileTypeEnum.File
              ? selectedSpace?.id
              : undefined,
          folderId:
            bizType === BizTypeEnum.MyLibrary && folder?.fileType === FileTypeEnum.File
              ? selectedSpace?.id
              : undefined
        };

        if (folder?.fileType === FileTypeEnum.Folder) {
          if (bizType === BizTypeEnum.TenantLibrary) {
            await updateSpaceParent(params);
          } else {
            await updateFolderParent(params);
          }
        } else {
          await updateFileParent(params);
        }
      } else {
        const params = {
          parentId: String(selectedSpace?.id),
          ids:
            folders?.filter((item) => item.fileType === FileTypeEnum.File).map((item) => item.id) ||
            [],
          folderIds:
            (bizType === BizTypeEnum.MyLibrary
              ? folders
                  ?.filter((item) => item.fileType === FileTypeEnum.Folder)
                  .map((item) => String(item.id))
              : []) || [],
          spaceIds:
            (bizType === BizTypeEnum.TenantLibrary
              ? folders
                  ?.filter((item) => item.fileType === FileTypeEnum.Folder)
                  .map((item) => String(item.id))
              : []) || []
        };
        await batchUpdateParent(params);
      }
    },
    onSuccess: async () => {
      Toast.success('操作成功');
      onSuccess?.();
      onClose?.();
    }
  });

  const fetchList = (parentId: string) => {
    return bizType === BizTypeEnum.TenantLibrary
      ? getSpaceList({ parentId: parentId || '0', isQuerySubdirectory: 1 })
      : getFolderList({ parentId, isQuerySubdirectory: 1 });
  };

  useQuery(['SpaceList', parentId], () => fetchList(parentId), {
    onSuccess: (data: SpaceType[]) => {
      if (data) {
        setGetSpaceData(data);
      }
    }
  });

  const handleSpaceClick = async (space: SpaceType) => {
    setParentId(space.id);
    setNavTitles((prevTitles) => [
      ...prevTitles,
      bizType === BizTypeEnum.TenantLibrary ? space.spaceName || '' : space.folderName || ''
    ]);
    setPathStack((prevStack) => [
      ...prevStack,
      {
        id: space.id,
        name: bizType === BizTypeEnum.TenantLibrary ? space.spaceName || '' : space.folderName || ''
      }
    ]);
    const data = await fetchList(space.id);
    setGetSpaceData(data);
    setSelectedSpace(space);
  };

  const handleBackClick = () => {
    if (pathStack.length > 0) {
      const newParentId = pathStack[pathStack.length - 2]?.id || '0';
      setParentId(newParentId);
      setNavTitles((prevTitles) => prevTitles.slice(0, -1));
      setPathStack((prevStack) => prevStack.slice(0, -1));
      fetchList(newParentId).then((data) => {
        setGetSpaceData(data);
      });
    }
  };

  return (
    <MyModal
      title="移动"
      isOpen
      isCentered
      hideCloseButton
      w="571px"
      minH="300px"
      onClose={onClose}
    >
      <Box p={respDims(24)}>
        <Flex bgColor="#F6F6F6" alignItems="center" h="38px" pl="16px">
          {navTitles.length === 1 && (
            <Box cursor="default" color="rgba(0,0,0,0.9)" fontSize="14px" fontWeight="400">
              全部
            </Box>
          )}
          {navTitles.length > 1 && (
            <Box
              cursor="pointer"
              color="#7D4DFF"
              fontSize="14px"
              fontWeight="400"
              onClick={handleBackClick}
            >
              返回上级
            </Box>
          )}
          {navTitles.map(
            (title, index) =>
              index > 0 && (
                <Flex alignItems="center" key={index} direction="row">
                  <Box
                    fontWeight={index === navTitles.length - 1 ? 'bold' : 'normal'}
                    color={index === navTitles.length - 1 ? '#303133' : '#606266'}
                    cursor={index === navTitles.length - 1 ? 'default' : 'pointer'}
                    onClick={() => {
                      const newParentId = pathStack[index - 1]?.id;
                      if (newParentId) {
                        setParentId(newParentId);
                        setNavTitles(navTitles.slice(0, index + 1));
                        setPathStack(pathStack.slice(0, index));
                        fetchList(newParentId).then((data) => {
                          setGetSpaceData(data);
                        });
                      }
                    }}
                    ml="10px"
                  >
                    <Box as="span" color="#606266">
                      {' / '}
                    </Box>
                    {formatTitle(title, calculateMaxLength(navTitles.length))}
                  </Box>
                </Flex>
              )
          )}
        </Flex>
        <Flex flexDir="column" m="16px 10px 16px 13px" maxH="400px" minH="400px" overflow="auto">
          {getSpaceData.length > 0 ? (
            <>
              {getSpaceData.map((space) => (
                <>
                  <Flex
                    alignItems="center"
                    key={space.id}
                    onClick={() => handleSpaceClick(space)}
                    cursor="pointer"
                  >
                    <SvgIcon
                      name={Number(space.type) === MoveTypeEnum.Space ? 'box' : 'file2Folder'}
                      color="#4E5969"
                      w={space.type === MoveTypeEnum.Space ? '14px' : '20px'}
                      h={space.type === MoveTypeEnum.Space ? '14px' : '20px'}
                      mr="10px"
                      mt="2px"
                    />
                    <Box
                      flex="1"
                      color="#303133"
                      fontWeight="400"
                      fontSize="15px"
                      lineHeight="22px"
                      overflow="hidden"
                      textOverflow="ellipsis"
                    >
                      {bizType === BizTypeEnum.TenantLibrary ? space.spaceName : space.folderName}
                    </Box>
                  </Flex>
                  <Box m="16px 0" border="1px solid #F3F4F6" w="100%" h="1px" />
                </>
              ))}
            </>
          ) : (
            <Center minH="240px" color="#a9a9ac">
              暂无数据
            </Center>
          )}
        </Flex>
        <Flex justify="flex-end">
          <Button htmlType="button" onClick={onClose}>
            取消
          </Button>
          <Button
            style={{ marginLeft: '16px' }}
            type="primary"
            disabled={navTitles.length < 2}
            loading={isLoading}
            htmlType="submit"
            onClick={() => {
              submitFn({});
            }}
          >
            移动到此
          </Button>
        </Flex>
      </Box>
    </MyModal>
  );
};

export default MoveModal;
