import MyModal from '@/components/MyModal';
import { Box } from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import List from './List';
import { BaseModalProps } from '@/types/cloud';
import SelectPersonnel from './Select';
import { CloudSpaceShareResponse } from '@/types/api/cloud';

const ShareFileModal = ({
  onClose,
  shareData
}: { shareData?: { id: string; name: string } } & BaseModalProps) => {
  const [current, setCurrent] = useState('list' as 'list' | 'select');
  const [selectSpaceData, setSelectSpaceData] = useState<CloudSpaceShareResponse[]>();

  const content = useMemo(() => {
    if (current === 'list') {
      return (
        <List
          shareData={shareData}
          onAdd={(data) => {
            setCurrent('select'), setSelectSpaceData(data);
          }}
        />
      );
    }
    return (
      <SelectPersonnel
        selectSpaceData={selectSpaceData!}
        shareData={shareData}
        onBack={() => setCurrent('list')}
      />
    );
  }, [current]);

  return (
    <MyModal title={shareData!.name} isOpen isCentered onClose={onClose} minW="700px" minH="500px">
      <Box w="100%" h="100%">
        {content}
      </Box>
    </MyModal>
  );
};

export default ShareFileModal;
