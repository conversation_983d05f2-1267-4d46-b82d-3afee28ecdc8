import MyModal from '@/components/MyModal';
import { Box } from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import List from './List';
import SelectPersonnel from './Select';
import { BaseModalProps } from '@/types/cloud';
import { NavType } from '@/types/cloud';

const SettingModal = ({
  onClose,
  settingData,
  space,
  EditSpaceSuccess,
  refetchSpaceAudit,
  onNavChange
}: BaseModalProps & {
  onNavChange?: (nav?: NavType) => void;
}) => {
  const [current, setCurrent] = useState('list' as 'list' | 'select');

  const content = useMemo(() => {
    if (current === 'list') {
      return (
        <List
          settingData={settingData}
          space={space}
          EditSpaceSuccess={() => {
            EditSpaceSuccess?.(), onClose?.();
            refetchSpaceAudit?.();
          }}
          onClose={() => {}}
          onAdd={() => setCurrent('select')}
          onCloseModal={() => onClose?.()}
          onNavChange={onNavChange}
        />
      );
    }
    return (
      <SelectPersonnel
        settingData={settingData}
        onSuccess={() => {
          EditSpaceSuccess?.(), onClose?.();
        }}
        onBack={() => {
          setCurrent('list');
        }}
      />
    );
  }, [current]);

  return (
    <MyModal
      title={current === 'list' ? '空间设置' : '转让空间权限'}
      isOpen
      isCentered
      onClose={onClose}
      minW="481px"
      minH="320px"
    >
      <Box w="100%" h="100%">
        {content}
      </Box>
    </MyModal>
  );
};

export default SettingModal;
