import { updateSpace } from '@/api/cloud';
import { SpaceType } from '@/types/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex } from '@chakra-ui/react';
import { Form, Input, Button } from 'antd';
import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import MyModal from '@/components/MyModal';

type FormType = {
  spaceName: string;
  description?: string;
};
const InfoSpaceModal = ({
  space,
  onClose,
  EditSpaceSuccess
}: {
  space?: SpaceType;
  EditSpaceSuccess?: () => void;
  onClose?: () => void;
}) => {
  const [form] = Form.useForm<FormType>();
  const [initialValues] = useState<FormType>({
    spaceName: space?.spaceName || '',
    description: space?.description || ''
  });

  const { mutate: onSubmit } = useMutation({
    mutationFn: (data: FormType) => {
      return updateSpace({ id: space?.id!, ...data });
    },
    onSuccess: (data) => {
      Toast.success('操作成功');
      EditSpaceSuccess?.();
      onClose?.();
    }
  });

  return (
    <MyModal title="空间信息" isOpen isCentered onClose={onClose} minW="600px" minH="400px">
      <Form form={form} initialValues={initialValues} colon={false} onFinish={onSubmit}>
        <Flex direction="column" p="24px 28px" pb={0}>
          <Box
            w="100%"
            color="rgba(0,0,0,0.9)"
            fontSize="14px"
            fontWeight="400"
            borderRadius="8px"
            mb="16px"
          >
            空间名称
          </Box>

          <Form.Item
            name="spaceName"
            rules={[
              { required: true, message: '请输入空间名称' },
              { min: 2, message: '空间名称至少需要两个字符' },
              { max: 10, message: '空间名称最多可以输入十个字符' }
            ]}
          >
            <Input
              placeholder="请输入空间名称"
              autoComplete="off"
              autoFocus
              style={{ background: '#F6F6F6', border: 'none' }}
              maxLength={10}
            />
          </Form.Item>
          <Box
            w="100%"
            color="rgba(0,0,0,0.9)"
            fontSize="14px"
            fontWeight="400"
            borderRadius="8px"
            mb="16px"
          >
            空间介绍
          </Box>
          <Form.Item name="description">
            <Input.TextArea
              style={{ background: '#F6F6F6', minHeight: '60px', border: 'none' }}
              placeholder="请输入空间介绍"
              maxLength={200}
              minLength={2}
            />
          </Form.Item>

          <Flex justify="right" mt="41px">
            <Button htmlType="button" onClick={() => onClose?.()}>
              取消
            </Button>
            <Button style={{ marginLeft: '16px' }} type="primary" htmlType="submit">
              确定
            </Button>
          </Flex>
        </Flex>
      </Form>
    </MyModal>
  );
};

export default InfoSpaceModal;
