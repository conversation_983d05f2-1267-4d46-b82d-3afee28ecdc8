import { Box, Center, Flex, HStack, Image, keyframes, Text } from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import MyTooltip from '@/components/MyTooltip';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { Select } from 'antd';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { fileClearSearchHistory, getFileGlobalListPage, getFileSearchHistory } from '@/api/cloud';
import { FileSearchHistoryType, FileType, locationPathType } from '@/types/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { BizTypeEnum, FileTypeEnum, SearchTypeParams } from '@/constants/api/cloud';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useRouter } from 'next/router';
import styles from '../../../../cloud.module.scss';

const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;
const AllSearchModal = ({
  onClose,
  searchContent,
  onSearchContent,
  onItemClick,
  bizType,
  onFolderPath
}: {
  onClose: () => void;
  onSearchContent: (content: string) => void;
  bizType: BizTypeEnum;
  searchContent: string;
  onFolderPath?: (path: locationPathType[], searchContent: string) => void;
  onItemClick?: (file: FileType) => void;
}) => {
  const { openOverlay } = useOverlayManager();
  const [animation, setAnimation] = useState(slideIn);
  const [viewId, setViewId] = useState(SearchTypeParams.fileName);
  const [searchHistoryCont, setSearchHistoryCont] = useState<string>('');
  const [historySearchList, setHistorySearchList] = useState<FileSearchHistoryType[]>([]);
  const [searchFileType, setSearchFileType] = useState<number>(0);
  const [updateTimeType, setUpdateTimeType] = useState<number>(0);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const router = useRouter();
  const typeOptions = [
    { value: 0, label: '不限类型' },
    { value: 1, label: 'word' },
    { value: 2, label: 'excel' },
    { value: 3, label: 'ppt' },
    { value: 4, label: 'pdf' },
    { value: 5, label: '视频' },
    { value: 6, label: '文件夹' },
    { value: 7, label: '其他' }
  ];
  const updateTimeOptions = [
    { value: 0, label: '全部时间' },
    { value: 1, label: '最近7天' },
    { value: 2, label: '最近1个月' },
    { value: 3, label: '最近2个月' },
    { value: 4, label: '最近3个月' }
  ];

  const tabs = [
    {
      id: 1,
      text: '文件名'
    },
    {
      id: 2,
      text: '正文'
    },
    {
      id: 3,
      text: '标签'
    }
  ];

  const onClearSearchHistory = () => {
    fileClearSearchHistory().then((data) => {
      if (data) {
        Toast.success('操作成功');
        refetchSearchHistory();
      }
    });
  };

  const { refetch: refetchSearchHistory } = useQuery(
    ['searchHistories'],
    () => getFileSearchHistory(),
    {
      onSuccess(data) {
        setHistorySearchList(data);
      }
    }
  );

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery(
    [
      'fileGlobalListPage',
      searchContent,
      viewId,
      searchFileType,
      updateTimeType,
      searchHistoryCont
    ],
    ({ pageParam = 1 }) =>
      getFileGlobalListPage({
        searchKey: searchHistoryCont || searchContent,
        searchType: viewId,
        bizType: bizType,
        searchFileType,
        updateTimeType,
        current: pageParam,
        size: 10
      }),
    {
      getNextPageParam: (lastPage, allPages) => {
        const nextPage = allPages.length + 1;
        return lastPage.records.length > 0 ? nextPage : undefined;
      },
      onSuccess: (data) => {
        const total = data.pages[0]?.total || 0;
        setTotalRecords(total);
      },
      enabled: !!(searchContent || searchHistoryCont)
    }
  );

  const hasRecords = data?.pages?.some((page) => page.records.length > 0);

  const handleSearchFileTypeChange = (value: number) => {
    setSearchFileType(value);
  };

  const handleUpdateTimeTypeChange = (value: number) => {
    setUpdateTimeType(value);
  };

  const onClickFile = async (file: FileType) => {
    if (onItemClick) {
      return onItemClick(file);
    }
    if (file.fileType === FileTypeEnum.Folder) {
      onClose();
      onFolderPath!(file.locationPath!, searchContent || searchHistoryCont);
    } else {
      const unsupportedFormats = [
        'zip',
        'rar',
        '7z',
        'tar',
        'gz',
        'exe',
        'dll',
        'bin',
        'iso',
        'class',
        'o',
        'so',
        'db',
        'sqlite',
        'mdb',
        'sys',
        'ini',
        'log',
        'psd',
        'ai',
        'dwg',
        'xmind'
      ];
      const fileExtension = file.file?.fileUrl.split('.').pop()?.toLowerCase() ?? '';
      if (unsupportedFormats.includes(fileExtension)) {
        Toast.error('该文件类型不支持预览');
      } else {
        const currentRoute = router.asPath;
        window.open(
          `${currentRoute}?action=preview&fileId=${file.id}&fileType=${file.fileType}&searchContent=${searchContent || searchHistoryCont}&fileUrl=${encodeURIComponent(file.file?.fileUrl ?? '')}&fileKey=${file.file?.fileKey}`,
          '_blank'
        );
      }
    }
  };

  const highlightText = (text: string, highlight: string) => {
    if (!highlight.trim()) {
      return <Box>{text}</Box>;
    }
    const parts = text.split(new RegExp(`(${highlight})`, 'gi'));
    return (
      <Flex>
        {parts.map((part, i) =>
          part.toLowerCase() === highlight.toLowerCase() ? (
            <Box key={i} style={{ color: '#3366FF', fontSize: '15px', fontWeight: 500 }}>
              {part}
            </Box>
          ) : (
            <Box key={i}>{part}</Box>
          )
        )}
      </Flex>
    );
  };

  const formatLocationPath = (locationPath: locationPathType[]) => {
    if (!locationPath || locationPath.length === 0) return '';
    return locationPath
      .map((item, index, arr) => (index === arr.length - 1 ? item.name : `${item.name}<`))
      .join('');
  };

  const parseEmTags = (content: string) => {
    return (
      <Box
        dangerouslySetInnerHTML={{
          __html: content.replace(
            /<em>(.*?)<\/em>/g,
            '<Box style="color: #3366FF; font-size: 15px; font-weight: 500;">$1</Box>'
          )
        }}
      />
    );
  };

  const listRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (listRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = listRef.current;
        if (scrollTop + clientHeight >= scrollHeight - 50 && hasNextPage && !isFetchingNextPage) {
          fetchNextPage();
        }
      }
    };

    if (listRef.current) {
      listRef.current.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (listRef.current) {
        listRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <Flex w="100%" h="100%" p="17px 16px 30px 16px" overflow="hidden" flexDirection="column">
      <Flex>
        <Flex
          flexDirection="row"
          alignItems="center"
          backgroundColor="#F9FAFB"
          borderRadius={respDims(50)}
          height={respDims('36fpx')}
          p="4px 6px"
          justifyContent="center"
          cursor="pointer"
          mr="auto"
        >
          {tabs.map((tab, index) => (
            <Flex
              key={tab.id}
              flexDirection="row"
              alignItems="center"
              justifyContent="center"
              fontSize="16px"
              onClick={() => {
                setViewId(tab.id);
                setAnimation(slideIn);
              }}
              cursor="pointer"
              {...(tab.id === viewId
                ? {
                    color: '#1D2129',
                    bgColor: '#ffffff',
                    fontWeight: '500'
                  }
                : {
                    color: '#4E5969',
                    fontWeight: '500'
                  })}
              borderRadius={respDims(46)}
              width={respDims('100fpx')}
              height="100%"
            >
              <Text fontSize={respDims('15fpx')} textAlign="center" whiteSpace="nowrap">
                {tab.text}
              </Text>
            </Flex>
          ))}
        </Flex>

        {(viewId === SearchTypeParams.fileName ||
          viewId === SearchTypeParams.text ||
          viewId === SearchTypeParams.tag) && (
          <HStack spacing={0.5}>
            <Select
              className={styles['allSearch-select']}
              style={{ width: '98px', borderRadius: '1px', height: '30px', marginLeft: '16px' }}
              placeholder="请选择类型"
              value={searchFileType}
              options={typeOptions}
              onChange={handleSearchFileTypeChange}
              allowClear
            />
            <Box w="1px" h="12px" bg="#dddddd"></Box>

            <Select
              className={styles['allSearch-select']}
              style={{ width: '98px', borderRadius: '1px', height: '30px' }}
              onChange={handleUpdateTimeTypeChange}
              placeholder="请选择更新时间"
              value={updateTimeType}
              options={updateTimeOptions}
              allowClear
            />
          </HStack>
        )}
      </Flex>
      {historySearchList.length > 0 && (
        <Flex m="16px 6px 9px 0">
          <Center mr="auto" fontWeight="500" fontSize="16px" color="#303133">
            搜索历史
          </Center>
          <MyTooltip label="清除搜索历史">
            <SvgIcon
              cursor="pointer"
              name="chatDelete"
              _hover={{
                borderRadius: '8px',
                bgColor: '#f6f6f6',
                p: '5px'
              }}
              w={respDims(32, 18)}
              h={respDims(32, 18)}
              p="5px"
              onClick={() => {
                onClearSearchHistory();
              }}
            />
          </MyTooltip>
        </Flex>
      )}

      {historySearchList.length > 0 && (
        <Flex flexWrap="wrap">
          {historySearchList.map((tab, index) => (
            <Box
              key={tab.searchContent + index}
              mb="10px"
              color="#4E5969"
              fontWeight="400"
              fontSize="14px"
              p="5px 16px"
              onClick={() => {
                onSearchContent(tab.searchContent);
                setSearchHistoryCont(tab.searchContent);
              }}
              backgroundColor="#F4F4F4"
              borderRadius="8px"
              mr="10px"
              cursor="pointer"
            >
              {tab.searchContent}
            </Box>
          ))}
        </Flex>
      )}

      <Box
        fontSize="16px"
        fontWeight="500"
        color="#303133"
        mt={historySearchList.length > 0 ? '4px' : '14px'}
        mb="16px"
      >
        搜索结果（{totalRecords || 0}）
      </Box>

      {hasRecords ? (
        <Flex flexDir="column" overflowY="auto" h="43vh" overflowX="hidden" ref={listRef}>
          {data &&
            data.pages.map((page) =>
              page.records.map((file) => (
                <Flex
                  key={file.id}
                  alignItems="center"
                  cursor="pointer"
                  onClick={() => {
                    onClickFile(file);
                  }}
                >
                  <Box mb={viewId === SearchTypeParams.fileName ? '28px' : '55px'}>
                    <FileIcon {...file} />
                  </Box>
                  <Flex flexDir="column" ml={respDims(20)}>
                    <MyTooltip overflowOnly>
                      <Box
                        fontSize={respDims('15fpx')}
                        lineHeight="1.5em"
                        maxH="3em"
                        color="rgba(0,0,0,0.9)"
                        wordBreak="break-all"
                        overflow="hidden"
                        textOverflow="ellipsis"
                        fontWeight="500"
                      >
                        {viewId === SearchTypeParams.fileName
                          ? highlightText(file.fileName, searchContent)
                          : file.fileName}
                      </Box>
                    </MyTooltip>
                    {file.highlight && file.highlight.fileContent.length > 0 && (
                      <Flex overflow="hidden">
                        <MyTooltip
                          overflowOnly
                          label={parseEmTags(file.highlight.fileContent.join(' '))}
                        >
                          <Flex pt="6px" maxW="520px" alignSelf="center" overflow="hidden">
                            {parseEmTags(file.highlight.fileContent.join(' '))}
                          </Flex>
                        </MyTooltip>
                      </Flex>
                    )}
                    {viewId === SearchTypeParams.tag && (
                      <Flex>
                        <Box
                          overflow="hidden"
                          color="#0F0F0F"
                          textOverflow="ellipsis"
                          fontFamily="PingFang SC"
                          fontSize="13px"
                          fontStyle="normal"
                          fontWeight="400"
                          lineHeight="22px"
                        >
                          标签：
                        </Box>
                        {file?.cloudLabel?.map((label) => (
                          <Box
                            display="flex"
                            padding="1px 8px"
                            alignItems="center"
                            fontSize="15px"
                            gap="4px"
                            borderRadius="2px"
                            background="var(--fill-2, #F2F3F5)"
                            key={label}
                            mr="7px"
                          >
                            {highlightText(label, searchContent)}
                          </Box>
                        ))}
                      </Flex>
                    )}
                    <Flex
                      alignItems="center"
                      fontSize="13px"
                      color="#909399"
                      fontWeight="400"
                      mb="24px"
                    >
                      <Box pt="4px">上传人：{file.uploader}</Box>
                      <MyTooltip label={formatLocationPath(file.locationPath!)}>
                        <Box
                          pt="2px"
                          style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: '1',
                            whiteSpace: 'normal'
                          }}
                          maxW="240px"
                          alignSelf="center"
                        >
                          &nbsp;| 位置：{formatLocationPath(file.locationPath!)}
                        </Box>
                      </MyTooltip>
                      <Box pt="1px"> &nbsp;| {file.updateTime}更新</Box>
                    </Flex>
                  </Flex>
                </Flex>
              ))
            )}
        </Flex>
      ) : (
        <Flex m="70px 0 4px 0" flexDir="column" alignItems="center">
          <Image w="80px" h="80px" src={'/imgs/cloud/folders_empty.png'} alt="" objectFit="cover" />
          <Box mt="23px" fontSize={respDims(15, 13)} color="#4E5969">
            未查询到相关结果
          </Box>
        </Flex>
      )}
    </Flex>
  );
};

export default AllSearchModal;
