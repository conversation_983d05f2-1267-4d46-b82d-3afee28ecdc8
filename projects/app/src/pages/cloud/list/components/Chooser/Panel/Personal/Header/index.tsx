import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import Breadcrumb from '@/pages/cloud/components/Breadcrumb';
import SearchInput from '@/pages/cloud/components/SearchInput';
import { PathItemTypeEnum, SearchTypeEnum } from '@/constants/cloud';
import { respDims } from '@/utils/chakra';
import {
  Box,
  Button,
  Flex,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from '@chakra-ui/react';
import TransferButton from '../../../../../../components/Transfer/Button';
import { useMemo, useState, useRef } from 'react';
import { BreadcrumbItemType, FilePathType, PathItemType } from '@/types/cloud';
import { SearchBarProps, MyTableRef } from '@/components/MyTable/types';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import LocalUploadModal from '@/pages/cloud/components/LocalUploadModal';
import FolderModal from '@/pages/cloud/components/FolderModal';
import AllSearchModal from '../../../../Tenant/AllSearchModal';
import { locationPathType } from '@/types/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import useUploadRefresh from '@/pages/cloud/hooks/useUploadRefresh';
import { useChooserContext } from '../../../ChooserContext';
import MyTooltip from '@/components/MyTooltip';

type QueryType = {
  name: string;
};

const Header = ({
  path,
  query,
  tableInstance,
  onSearch,
  onBackToParent,
  onClickPathItem,
  onClickFolderPath,
  onReload
}: {
  path?: FilePathType;
  onSearch?: (query: QueryType) => void;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
  onClickFolderPath?: (path: locationPathType[], searchContent: string) => void;
  onReload?: () => void;
} & SearchBarProps) => {
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const [searchContent, setSearchContent] = useState('');
  const [parentId, setParentId] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [folderPathData, setFolderPathData] = useState<locationPathType[]>([]);
  const { addUpload } = useCloudUploader();
  const tableRef = useRef<MyTableRef>(null);
  const { showCreateFolderBtn, showUploadBtn } = useChooserContext();

  const breadcrumbList = useMemo(() => {
    const list: (BreadcrumbItemType & { pathItem?: PathItemType })[] = [];

    const folderPath = path?.filter(
      (it: PathItemType) => it.type === PathItemTypeEnum.file
    ) as FilePathType;

    if (folderPath?.length > 1) {
      list.push({
        label: '返回上一级',
        isBack: true
      });
    }

    folderPath?.forEach((pathItem, index) => {
      list.push({
        label: pathItem.file.fileName!,
        pathItem,
        clickable: index < folderPath.length - 1
      });
    });
    return list;
  }, [path]);

  const onAddFolder = () => {
    path &&
      openOverlay({
        Overlay: FolderModal,
        props: {
          bizType: BizTypeEnum.MyLibrary,
          path,
          onSuccess: () => tableInstance.reload()
        }
      });
  };

  const onLocalUpload = (fileType: FileTypeEnum) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    if (fileType === FileTypeEnum.Folder) {
      input.webkitdirectory = true;
    }
    input.onchange = async (event) => {
      const files = (event.target as HTMLInputElement).files;
      if (files && path) {
        try {
          const parent = path[path.length - 1];
          const pathNames = path.map((it) =>
            it.type === PathItemTypeEnum.file ? it.file.fileName : ''
          );
          const parentId = parent.type === PathItemTypeEnum.file ? parent.file.id : '';
          setParentId(parentId);
          addUpload({
            path: pathNames,
            parentId,
            files: Array.from(files),
            bizType: BizTypeEnum.MyLibrary,
            oldId: undefined
          });

          Toast.success('已添加到上传队列');
        } catch (error) {
          console.error('Upload failed:', error);
          Toast.error('上传失败');
        }
      }
    };
    input.click();
  };

  useUploadRefresh({ parentId, onRefresh: () => onReload?.() });

  const onAllSearch = () => {
    openOverlay({
      Overlay: AllSearchModal,
      props: {
        searchContent: searchContent,
        bizType: BizTypeEnum.MyLibrary,
        onSearchContent: (content) => {
          onSearch?.({ ...query, searchKey: content });
          setSearchContent(content);
        },
        onFolderPath: (data, searchContent) => {
          setFolderPathData(data);
          onClickFolderPath?.(data, searchContent);
        },
        onClose: () => {
          onClose;
        }
      }
    });
  };

  return (
    <Box w="100%">
      <Flex justifyContent="space-between" alignItems="center" w="100%" p="10px 0 20px 0">
        <Box pl="2px">
          <Breadcrumb
            list={breadcrumbList}
            onClickItem={(item) => item.pathItem && onClickPathItem?.(item.pathItem)}
            onBack={onBackToParent}
          />
        </Box>

        <Flex pr="6px" alignItems="center">
          {showCreateFolderBtn && (
            <Button
              ml={respDims(13)}
              variant="outline"
              colorScheme="primary"
              px={respDims(20)}
              fontSize={respDims('14fpx')}
              borderRadius={respDims(8)}
              onClick={onAddFolder}
            >
              <SvgIcon name="folder" w={respDims('14fpx')} h={respDims('14fpx')} />
              <Box ml={respDims(8)}>新建文件夹</Box>
            </Button>
          )}
          {showUploadBtn && (
            <>
              <MyTooltip
                label="完成文件上传后，请在右侧的传输列表中查看您的上传进度详情。可从文件列表选择上传成功的文件进行内容生成操作。"
                placement="top"
              >
                <Button
                  variant="solid"
                  colorScheme="primary"
                  h={respDims(37)}
                  px={respDims(20)}
                  mr={respDims(10)}
                  fontSize={respDims('14fpx')}
                  borderRadius={respDims(8)}
                  onClick={() => onLocalUpload(FileTypeEnum.File)}
                >
                  <SvgIcon
                    color="#FFF"
                    name="cloudUpload"
                    w={respDims('14fpx')}
                    h={respDims('14fpx')}
                  />
                  <Box ml={respDims(8)}>上传文件</Box>
                </Button>
              </MyTooltip>

              <TransferButton ml="auto" showDownload={false} />
            </>
          )}
        </Flex>
      </Flex>

      <OverlayContainer></OverlayContainer>
    </Box>
  );
};

export default Header;
