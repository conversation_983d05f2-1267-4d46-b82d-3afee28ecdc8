import { FooterComponentProps } from '@/components/MyTable/types';
import { respDims } from '@/utils/chakra';
import { Flex } from '@chakra-ui/react';
import { Pagination } from 'antd';

const Footer = ({ tableInstance }: {} & FooterComponentProps) => {
  return (
    <Flex my={respDims(10)} w="100%" justify="flex-end">
      <Pagination
        size="small"
        total={tableInstance.total}
        pageSize={tableInstance.size}
        current={tableInstance.current}
        onChange={(page, size) => {
          tableInstance.setCurrent?.(page);
          tableInstance.setSize?.(size);
        }}
      />
    </Flex>
  );
};

export default Footer;
