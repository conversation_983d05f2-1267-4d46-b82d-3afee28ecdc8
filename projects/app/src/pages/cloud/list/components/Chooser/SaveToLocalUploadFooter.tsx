import MyModal from '@/components/MyModal';
import { Box, Flex, Text } from '@chakra-ui/react';
import Panel from './Panel';
import { respDims } from '@/utils/chakra';
import { Button, Form, Input } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AccessModeEnum, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { ChooserProvider, useChooserContext } from './ChooserContext';
import styles from '@/pages/index.module.scss';
import { uploadByHtmlStr, uploadFile } from '@/api/cloud';
import { NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { Toast } from '@/utils/ui/toast';
import { PathSpaceType, PathItemType } from '@/types/cloud';
import { useRequest } from '@/hooks/useRequest';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import htmlDocx from 'html-docx-js/dist/html-docx';
import { ChooserFileType } from '.';
import FileIcon from '@/pages/cloud/components/FileIcon';
import MyTooltip from '@/components/MyTooltip';
import SvgIcon from '@/components/SvgIcon';

const SaveToLocalUploadFooter = ({
  files,
  onSuccess,
  onClose,
  setTitle
}: {
  files?: ChooserFileType[];
  onSuccess?: (filename: string) => void;
  onClose?: () => void;
  setTitle?: (title: string) => void;
}) => {
  const { path, navType } = useChooserContext();
  const [selectedFiles, setSelectedFiles] = useState<ChooserFileType[]>([]);
  useEffect(() => {
    setSelectedFiles((state) =>
      state === files
        ? state
        : files?.map((it) => ({ ...it, rowKey: `${FileTypeEnum.File}-${it.fileId}` })) || []
    );
  }, [files]);
  const handleSave = async () => {
    const uploadFileList =
      selectedFiles?.map(async (file) => {
        const formData = new FormData();
        let folderId = '';
        if (path[path.length - 1].type == PathItemTypeEnum.space) {
          folderId = (path[path.length - 1] as PathSpaceType).space.id;
        } else {
          folderId = (path[path.length - 1] as PathSpaceType).file?.id || '';
        }

        const params = {
          bizType:
            navType == NavTypeEnum.personal ? BizTypeEnum.MyLibrary : BizTypeEnum.TenantLibrary,
          folderId: folderId,
          fileName: file.fileName,
          fileKey: file.fileKey,
          isAuditRoot: 1
        };
        Object.keys(params).forEach((key: any) => {
          formData.append(key, (params as any)[key]);
        });

        return uploadFile(formData);
      }) || [];

    await Promise.all(uploadFileList!);

    Toast.success('保存成功');
    onSuccess?.('');
    onClose?.();
    return;
  };

  return (
    <Box>
      <Box px={respDims(32)} pt={respDims(16)} fontSize={respDims(16, 14)} fontWeight="bold">
        <Text> 已选择文件：{selectedFiles.length}</Text>
      </Box>

      <Flex flexWrap="wrap" px={respDims(32)}>
        {selectedFiles.map((file) => (
          <Box
            key={file.rowKey}
            pt="7px"
            pr="7px"
            mr="7px"
            mt="7px"
            w={respDims('214fpx')}
            pos="relative"
          >
            <Flex
              px={respDims(12)}
              py={respDims(10)}
              bgColor="#F8FAFC"
              borderRadius={respDims(8)}
              border="1px solid #E5E7EB"
              align="center"
            >
              <FileIcon
                fileName={file.fileName}
                fileUrl={file.fileUrl}
                w={respDims(32)}
                h={respDims(32)}
              />
              <MyTooltip overflowOnly label={file.fileName}>
                <Box
                  ml={respDims(10)}
                  color="#1D2129"
                  fontSize={respDims('15fpx')}
                  whiteSpace="nowrap"
                  overflow="hidden"
                >
                  {file.fileName}
                </Box>
              </MyTooltip>
            </Flex>

            <SvgIcon
              pos="absolute"
              right="0"
              top="0"
              name="circleClose"
              cursor="pointer"
              onClick={() => {
                setSelectedFiles((state) => state.filter((it) => it.rowKey !== file.rowKey));
              }}
            />
          </Box>
        ))}
      </Flex>
      <Flex justify="flex-end" px={respDims(32)} py={respDims(24)} className={styles['my-form']}>
        <Box></Box>
        <Button htmlType="button" onClick={onClose}>
          取消
        </Button>
        <Button
          style={{ marginLeft: '16px' }}
          type="primary"
          htmlType="submit"
          onClick={() => handleSave()}
        >
          保存
        </Button>
      </Flex>
    </Box>
  );
};

export default SaveToLocalUploadFooter;
