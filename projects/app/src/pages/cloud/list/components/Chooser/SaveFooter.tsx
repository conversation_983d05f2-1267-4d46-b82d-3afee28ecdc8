import MyModal from '@/components/MyModal';
import { Flex } from '@chakra-ui/react';
import Panel from './Panel';
import { respDims } from '@/utils/chakra';
import { Button, Form, Input } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AccessModeEnum, BizTypeEnum } from '@/constants/api/cloud';
import { ChooserProvider, useChooserContext } from './ChooserContext';
import styles from '@/pages/index.module.scss';
import { uploadByHtmlStr, uploadFile } from '@/api/cloud';
import { NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { Toast } from '@/utils/ui/toast';
import { PathSpaceType, PathItemType } from '@/types/cloud';
import { useRequest } from '@/hooks/useRequest';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import htmlDocx from 'html-docx-js/dist/html-docx';
import { convertLatexImgToFormula } from '@/utils/export';
import { convertMdToHtml } from '@/api/chat';

const SaveFooter = ({
  filename,
  htmlStr,
  onSuccess,
  onClose,
  setTitle
}: {
  filename?: string;
  htmlStr?: string;
  onSuccess?: (filename: string) => void;
  onClose?: () => void;
  setTitle?: (title: string) => void;
}) => {
  const { path, navType } = useChooserContext();
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({ fileName: filename || '' });
  }, [filename, form]);

  const handleSaveText2Doc = useCallback(async () => {
    await form.validateFields();
    const formData = new FormData();
    let folderId = '';
    if (path[path.length - 1].type == PathItemTypeEnum.space) {
      folderId = (path[path.length - 1] as PathSpaceType).space.id;
    } else {
      folderId = (path[path.length - 1] as PathSpaceType).file?.id || '';
    }
    let htmlStr_ = htmlStr || '';
    htmlStr_ = convertLatexImgToFormula(htmlStr_ || '');
    htmlStr_ = htmlStr_.replace(/&amp;/g, '&');
    htmlStr_ = await convertMdToHtml({
      htmlStr: htmlStr_
    });
    const params = {
      bizType: navType == NavTypeEnum.personal ? BizTypeEnum.MyLibrary : BizTypeEnum.TenantLibrary,
      folderId: folderId,
      fileName: form.getFieldValue('fileName'),
      htmlStr: htmlStr_ || '<div>  </div>',
      isAuditRoot: 1
    };
    Object.keys(params).forEach((key: any) => {
      formData.append(key, (params as any)[key]);
    });
    await uploadByHtmlStr(formData);
  }, [path, navType, htmlStr, form]);

  const { isLoading, mutate: submitFn } = useRequest({
    mutationFn: handleSaveText2Doc,
    onSuccess() {
      Toast.success('保存成功');
      onSuccess?.(form.getFieldValue('fileName'));
      onClose?.();
    }
  });

  return (
    <>
      <Flex justify="flex-end" px={respDims(32)} py={respDims(24)} className={styles['my-form']}>
        <Form
          form={form}
          style={{ flex: 1, marginRight: '16px' }}
          initialValues={{ fileName: filename }}
        >
          <Form.Item
            name="fileName"
            rules={[{ required: true, message: '请输入文件名称' }]}
            style={{ width: '100%' }}
          >
            <Input
              placeholder="请输入文件名称"
              onChange={(e) => form.setFieldsValue({ fileName: e.target.value })}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
        <Button htmlType="button" onClick={onClose}>
          取消
        </Button>
        <Button
          style={{ marginLeft: '16px' }}
          type="primary"
          loading={isLoading}
          htmlType="submit"
          onClick={() => {
            submitFn({});
          }}
        >
          保存
        </Button>
      </Flex>
    </>
  );
};

export default SaveFooter;
