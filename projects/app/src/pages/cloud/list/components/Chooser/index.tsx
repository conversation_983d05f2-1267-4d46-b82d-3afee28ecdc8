import MyModal from '@/components/MyModal';
import { Flex } from '@chakra-ui/react';
import Panel from './Panel';
import { respDims } from '@/utils/chakra';
import { Button, Form, Input } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AccessModeEnum, BizTypeEnum } from '@/constants/api/cloud';
import { ChooserProvider, useChooserContext } from './ChooserContext';
import styles from '@/pages/index.module.scss';
import { getCloudChatReferenceFiles } from '@/api/cloud';
import { NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { Toast } from '@/utils/ui/toast';
import { NavType, PathItemType, PathSpaceType } from '@/types/cloud';
import { useRequest } from '@/hooks/useRequest';
import SaveFooter from './SaveFooter';
import SaveToLocalUploadFooter from './SaveToLocalUploadFooter';
import GetTargetFolderFooter from './GetTargetFolderFooter';
import { AuthorityTypeEnum } from '@/types/api/app';

export enum ChooserModeEnum {
  Self = 'Self',
  Tenant = 'Tenant',
  Member = 'Member'
}

export enum ModalModeEnum {
  Choose = 'Choose',
  Save = 'Save',
  SaveToLocalUpload = 'SaveToLocalUpload',
  GetTargetFolder = 'GetTargetFolder' // onSuccess 返回目录id
}

export type ChooserFileType = {
  fileId: string;
  fileName: string;
  fileKey: string;
  fileUrl?: string;
  rowKey?: string;
  fileType?: string;
  fileSize?: number;
  type?: number;
  authority?: AuthorityTypeEnum;
  fileParseStatus?: number | undefined;
};

export type SelectValidateFn = (props: {
  selectedFiles: ChooserFileType[];
  selectFiles: ChooserFileType[];
}) => boolean | Promise<boolean>;

export type CloudChooseProps = {
  setTitle?: (title: string) => void;
  title?: string;
  filename?: string;
  files?: ChooserFileType[];
  htmlStr?: string;
  modalMode?: ModalModeEnum;
  maxCount?: number;
  maxSize?: number;
  showCreateFolderBtn?: boolean;
  showFilenameInput?: boolean;
  accept?: string[];
  selectTips?: string;
  showUploadBtn?: boolean;
  onSuccess?: (files: ChooserFileType[], inputFileName?: string) => void;
  onSelectFolder?: (path: PathItemType[], navType: NavTypeEnum, filename?: string) => void;
  onClose?: () => void;
  onCloseCustom?: () => void;
  selectValidator?: SelectValidateFn;
};

const Chooser = (
  props: CloudChooseProps &
    (
      | { mode?: ChooserModeEnum.Self | ChooserModeEnum.Tenant }
      | { mode: ChooserModeEnum.Member; tmbId: string }
    )
) => {
  const {
    selectValidator,
    modalMode = ModalModeEnum.Choose,
    showCreateFolderBtn = false,
    accept,
    selectTips,
    maxSize,
    showUploadBtn = false,
    showFilenameInput = false
  } = props;
  return (
    <ChooserProvider
      modalMode={modalMode}
      showCreateFolderBtn={showCreateFolderBtn}
      accept={accept}
      selectValidator={selectValidator}
      maxSize={maxSize}
      selectTips={selectTips}
      showUploadBtn={showUploadBtn}
      showFilenameInput={showFilenameInput}
    >
      <ChooserContent {...props}></ChooserContent>
    </ChooserProvider>
  );
};

const ChooserContent = ({
  mode = ChooserModeEnum.Self,
  modalMode = ModalModeEnum.Choose,
  showCreateFolderBtn = false,
  title,
  files,
  filename,
  maxCount,
  htmlStr,
  onSuccess,
  onClose,
  setTitle,
  onCloseCustom,
  onSelectFolder,
  ...props
}: {
  title?: string;
  filename?: string;
  files?: ChooserFileType[];
  modalMode?: ModalModeEnum;
  showCreateFolderBtn?: boolean;
  maxCount?: number;
  htmlStr?: string;
  onSuccess?: (files: ChooserFileType[], inputFileName?: string) => void;
  onClose?: () => void;
  onCloseCustom?: () => void;
  setTitle?: (title: string) => void;
  onSelectFolder?: (path: PathItemType[], navType: NavTypeEnum, filename?: string) => void;
} & (
  | { mode?: ChooserModeEnum.Self | ChooserModeEnum.Tenant }
  | { mode: ChooserModeEnum.Member; tmbId: string }
)) => {
  const filesRef = useRef<ChooserFileType[]>(files || []);

  const PanelRender = useCallback(() => {
    return (
      <Panel
        flex="1"
        maxCount={maxCount}
        access={
          mode === ChooserModeEnum.Self
            ? {
                mode: AccessModeEnum.View
              }
            : mode === ChooserModeEnum.Tenant
              ? {
                  mode: AccessModeEnum.ViewTenant
                }
              : {
                  mode: AccessModeEnum.ViewMember,
                  tmbId: ((props as any).tmbId as string) || ''
                }
        }
        files={files}
        onFilesChange={(e) => {
          filesRef.current = e;
        }}
      />
    );
  }, [mode, props, files]);

  return (
    <MyModal
      title={<>{title || '从数据空间选择资料'}</>}
      isOpen
      onClose={() => {
        onClose?.();
        onCloseCustom?.();
      }}
      w={respDims(1090, '.8ms')}
      h={respDims(834, '.8ms')}
      maxW="95%"
      maxH="95%"
      isCentered
      closeOnOverlayClick={false}
      className="chooser-cloud-modal"
    >
      <Flex flexDir="column" h="100%" overflow="hidden">
        {PanelRender()}
        {modalMode === ModalModeEnum.Choose && (
          <Flex justify="flex-end" px={respDims(32)} py={respDims(24)}>
            <Button
              htmlType="button"
              onClick={() => {
                onClose?.();
                onCloseCustom?.();
              }}
            >
              关闭
            </Button>
            <Button
              style={{ marginLeft: '16px' }}
              type="primary"
              htmlType="submit"
              onClick={() => {
                getCloudChatReferenceFiles();
                onSuccess?.(filesRef.current);
                onClose?.();
              }}
            >
              完成
            </Button>
          </Flex>
        )}
        {modalMode === ModalModeEnum.Save && (
          <SaveFooter
            filename={filename}
            htmlStr={htmlStr}
            onSuccess={(filename) => {
              onSuccess?.(files || [], filename);
            }}
            onClose={onClose}
            setTitle={setTitle}
          ></SaveFooter>
        )}
        {modalMode === ModalModeEnum.SaveToLocalUpload && (
          <SaveToLocalUploadFooter
            files={files}
            onSuccess={(filename) => {
              onSuccess?.(files || [], filename);
            }}
            onClose={onClose}
            setTitle={setTitle}
          />
        )}
        {modalMode === ModalModeEnum.GetTargetFolder && (
          <GetTargetFolderFooter
            files={files}
            onConfirm={async (path, navType, filename) => {
              return onSelectFolder?.(path, navType, filename);
            }}
            filename={filename}
            onClose={onClose}
            setTitle={setTitle}
          />
        )}
      </Flex>
    </MyModal>
  );
};

export default Chooser;
