import MyTable from '@/components/MyTable';
import { Box, Flex } from '@chakra-ui/react';
import Header from './Header';
import { TableProps } from 'antd';
import Footer from './Footer';
import { getSpaceFilePage, getSpaceFileViewPage } from '@/api/cloud';

import { respDims } from '@/utils/chakra';
import { TableRowSelection } from 'antd/es/table/interface';
import { AccessModeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { formatFileSize } from '@/utils/tools';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import styles from '../../../../../cloud.module.scss';

import { FileType, GetSpaceFilePageProps } from '@/types/api/cloud';
import { AccessType, FilePathType, PathItemType, TenantNavType } from '@/types/cloud';
import { FooterComponentProps, MyTableRef, SearchBarProps } from '@/components/MyTable/types';

import { PathItemTypeEnum } from '@/constants/cloud';

import { useUserStore } from '@/store/useUserStore';

import FileIcon from '@/pages/cloud/components/FileIcon';
import { ChooserFileType, ModalModeEnum } from '../..';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import { CheckboxProps } from 'antd/lib';
import { useChooserContext } from '../../ChooserContext';

const Tenant = ({
  access,
  nav,
  searchKey,
  selectedFiles,
  onSelectedFilesChange
}: {
  access: AccessType;
  nav: TenantNavType;
  searchKey?: string;
  selectedFiles: ChooserFileType[];
  onSelectedFilesChange: (files: ChooserFileType[]) => void;
}) => {
  const { userInfo } = useUserStore();

  const tableRef = useRef<MyTableRef<GetSpaceFilePageProps, FileType>>(null);

  const spaceId = (nav?.path && nav.path[nav.path.length - 1]?.space?.id) || '';

  const { setContextPath, modalMode, accept, maxSize } = useChooserContext();

  const [folderPath, setFolderPath] = useState<FilePathType>([]);

  const { path, parentId, shareType } = useMemo(() => {
    const path = [...nav.path, ...folderPath];
    const item = path[path.length - 1];

    return {
      path,
      parentId: item ? (item.type === PathItemTypeEnum.space ? item.space.id : item.file.id) : '',
      shareType: item
        ? item.type === PathItemTypeEnum.space
          ? item.space.shareType
          : item.file.shareType
        : undefined
    };
  }, [nav.path, folderPath]);

  useEffect(() => {
    setContextPath(path);
  }, [path, setContextPath]);

  const selectedRowKeys = useMemo(() => selectedFiles.map((it) => it.rowKey!), [selectedFiles]);
  console.log(selectedRowKeys, 'selectedRowKeys', selectedFiles);

  const getSpaceFilePageApi = useCallbackRef((data: GetSpaceFilePageProps) =>
    access.mode === AccessModeEnum.View
      ? getSpaceFilePage(data)
      : getSpaceFileViewPage({
          ...data,
          isAdmin: access.mode === AccessModeEnum.ViewMember ? 0 : 1,
          tenantId: userInfo?.tenantId!,
          tmbId: access.mode === AccessModeEnum.ViewMember ? access.tmbId : undefined
        })
  );

  const onClickFile = (file: FileType) => {
    if (file.fileType === FileTypeEnum.Folder) {
      setFolderPath((state) => [...state, { type: PathItemTypeEnum.file, file }]);
    }
  };

  const onBackToParent = useCallback(() => {
    setFolderPath((state) => state.slice(0, state.length - 1));
  }, []);

  const onClickPathItem = useCallback((pathItem: PathItemType) => {
    if (pathItem.type === PathItemTypeEnum.space) {
      setFolderPath([]);
    } else {
      setFolderPath((state) => {
        const index = state.findIndex((item) => item.file.id === pathItem.file.id);
        return index >= 0 ? state.slice(0, index + 1) : state;
      });
    }
  }, []);

  useEffect(() => {
    setFolderPath([]);
  }, [spaceId]);

  const TableHeader = useCallback(
    ({ ...props }: SearchBarProps) => {
      return (
        <Header
          {...props}
          path={path}
          onBackToParent={onBackToParent}
          onClickPathItem={onClickPathItem}
          onReload={() => {
            setTimeout(() => {
              tableRef.current?.reload();
            }, 2000);
          }}
        />
      );
    },
    [path, onBackToParent, onClickPathItem]
  );

  const TableFooter = useCallback((props: FooterComponentProps) => {
    return <Footer {...props} />;
  }, []);

  const rowSelection: TableRowSelection<FileType> = {
    type: 'checkbox',
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    columnWidth: 70,
    getCheckboxProps: (
      record: FileType
    ): Partial<Omit<CheckboxProps, 'checked' | 'defaultChecked'>> => {
      const isFile = record.fileType === FileTypeEnum.File;
      const isOversize = Boolean(
        record.fileSize && record.fileSize > (maxSize ?? 100) * 1024 * 1024
      ); // 100MB in bytes
      let disabeld = !isFile || isOversize;
      disabeld = accept ? !accept.includes(record.fileName.split('.').pop() || '') : false;
      return disabeld ? { disabled: true, style: { display: 'none' } } : {};
    },
    onChange(selectedRowKeys) {
      const files: ChooserFileType[] = [];
      selectedRowKeys.forEach((rowKey) => {
        const file = selectedFiles.find((it) => it.rowKey === rowKey);
        if (file) {
          files.push(file);
          return;
        }
        const file2 = tableRef.current?.data.find((it) => it.rowKey === rowKey);
        if (file2) {
          files.push({
            fileId: file2.id,
            fileName: file2.fileName,
            fileKey: file2.fileKey!,
            fileUrl: file2.file?.fileUrl,
            rowKey: file2.rowKey,
            fileSize: file2.fileSize
          });
        }
      });
      onSelectedFilesChange(files);
    }
  };

  const columns: TableProps<FileType>['columns'] = [
    {
      title: '文件名称',
      key: 'fileName',
      width: 250,
      render: (_, record) => {
        const { fileName, fileType } = record;
        return (
          <Flex alignItems="center" cursor="pointer" onClick={() => onClickFile(record)}>
            <FileIcon
              fileType={fileType}
              fileName={fileName}
              fileUrl={record.file?.fileUrl}
              file={record.file}
            />
            <Box ml={respDims(12)}>{fileName}</Box>
          </Flex>
        );
      }
    },
    {
      title: '上传人',
      dataIndex: 'uploader'
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      render: (_, { fileType: type, fileSize }) =>
        type === FileTypeEnum.Folder ? '' : formatFileSize(fileSize!)
    }
  ];

  return (
    <Box w="100%" h="100%" flexDir="column">
      <MyTable
        ref={tableRef}
        rowKey="rowKey"
        className={styles['file-table2']}
        columns={columns}
        rowSelection={
          modalMode == ModalModeEnum.Save ||
          modalMode == ModalModeEnum.GetTargetFolder ||
          modalMode == ModalModeEnum.SaveToLocalUpload
            ? undefined
            : rowSelection
        }
        api={getSpaceFilePageApi}
        defaultQuery={{ parentId, shareType, searchKey }}
        boxStyle={{ px: 0, py: 0, overflow: 'visible' }}
        headerConfig={{ HeaderComponent: TableHeader }}
        FooterComponent={TableFooter}
      />
    </Box>
  );
};

export default Tenant;
