import React, { createContext, useContext, useState, ReactNode } from 'react';
import { NavTypeEnum } from '@/constants/cloud';
import { PathItemType } from '@/types/cloud';
import { ChooserFileType, ModalModeEnum, SelectValidateFn } from '.';

interface ChooserContextProps {
  showCreateFolderBtn: boolean;
  selectTips?: string;
  setShowCreateFolderBtn: (show: boolean) => void;
  modalMode: ModalModeEnum;
  setModalMode: (mode: ModalModeEnum) => void;
  navType: NavTypeEnum | null;
  setNavType: (type: NavTypeEnum | null) => void;
  path: PathItemType[];
  setContextPath: (path: PathItemType[]) => void;
  accept?: string[];
  selectValidator?: SelectValidateFn;
  showUploadBtn?: boolean;
  showFilenameInput?: boolean;
  maxSize?: number;
}

interface ChooserProviderProps {
  showCreateFolderBtn: boolean;
  modalMode: ModalModeEnum;

  children: ReactNode;
  accept?: string[];
  selectTips?: string;
  selectValidator?: SelectValidateFn;
  showUploadBtn?: boolean;
  showFilenameInput?: boolean;
  maxSize?: number;
}

const ChooserContext = createContext<ChooserContextProps | undefined>(undefined);

export const ChooserProvider: React.FC<ChooserProviderProps> = ({
  children,
  showCreateFolderBtn: initialShowCreateFolderBtn,
  modalMode: initialModalMode,
  selectValidator,
  accept,
  selectTips,
  showUploadBtn,
  maxSize = 100,
  showFilenameInput
}) => {
  const [showCreateFolderBtn, setShowCreateFolderBtn] = useState(initialShowCreateFolderBtn);
  const [modalMode, setModalMode] = useState(initialModalMode);
  const [navType, setNavType] = useState<NavTypeEnum | null>(null);
  const [path, setContextPath] = useState<PathItemType[]>([]);

  return (
    <ChooserContext.Provider
      value={{
        showCreateFolderBtn,
        selectTips,
        setShowCreateFolderBtn,
        modalMode,
        setModalMode,
        navType,
        setNavType,
        path,
        setContextPath,
        accept,
        selectValidator,
        showUploadBtn,
        showFilenameInput,
        maxSize
      }}
    >
      {children}
    </ChooserContext.Provider>
  );
};

export const useChooserContext = () => {
  const context = useContext(ChooserContext);
  if (!context) {
    throw new Error('useChooserContext must be used within a ChooserProvider');
  }
  return context;
};

export default ChooserProvider;
