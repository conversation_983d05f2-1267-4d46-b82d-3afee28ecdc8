import { ChakraProps } from '@chakra-ui/system';
import NavList from '../../Sidebar/NavList/Choose';
import {
  Box,
  Center,
  Flex,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Text,
  useDisclosure
} from '@chakra-ui/react';
import { AccessType, NavType } from '@/types/cloud';
import { useEffect, useState } from 'react';
import { respDims } from '@/utils/chakra';
import SearchInput from '@/pages/cloud/components/SearchInput';
import { NavTypeEnum, SearchTypeEnum } from '@/constants/cloud';
import Tenant from './Tenant';
import { Toast } from '@/utils/ui/toast';
import FileIcon from '@/pages/cloud/components/FileIcon';
import SvgIcon from '@/components/SvgIcon';
import MyTooltip from '@/components/MyTooltip';
import { ChooserFileType, ModalModeEnum } from '..';
import { AccessModeEnum, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import Personal from './Personal';
import { useChooserContext } from '../ChooserContext';
import AllSearchModal from '../../Tenant/AllSearchModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { nanoid } from 'nanoid';
import QuestionTip from '@/components/MyTooltip/QuestionTip';
import { isFunction } from 'lodash';

const Panel = ({
  access,
  maxCount,
  files,
  onFilesChange,
  ...props
}: {
  access: AccessType;
  files?: ChooserFileType[];
  maxCount?: number;

  onFilesChange: (files: ChooserFileType[]) => void;
} & ChakraProps) => {
  const [nav, setNav] = useState<NavType>();

  const [searchKey, setSearchKey] = useState('');

  const [selectedFiles, setSelectedFiles] = useState<ChooserFileType[]>([]);

  const { modalMode, setNavType, selectValidator, selectTips, accept } = useChooserContext();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { openOverlay } = useOverlayManager();

  useEffect(() => {
    setNavType(nav?.type || null);
  }, [nav, setNavType]);

  const onSelectedFilesChange = async (files: ChooserFileType[]) => {
    if (isFunction(selectValidator)) {
      if (
        !(await selectValidator({
          selectedFiles: selectedFiles,
          selectFiles: files
        }))
      ) {
        return;
      }
    } else if (maxCount && files.length > selectedFiles.length && files.length > maxCount) {
      Toast.warning(`选择数量不能超过${maxCount}个`);
      return;
    }
    setSelectedFiles(files);
    onFilesChange(files);
  };
  nav?.type;
  useEffect(() => {
    setSelectedFiles((state) =>
      state === files
        ? state
        : files?.map((it) => ({ ...it, rowKey: `${FileTypeEnum.File}-${it.fileId}` })) || []
    );
  }, [files]);

  return (
    <Flex flexDir="column" overflow="hidden" {...props}>
      <Flex w="100%" px={respDims(32)} py={respDims(16)}>
        <SearchInput
          type={SearchTypeEnum.file}
          placeholder="搜索文件，请输入文件名称"
          w="100%"
          onSearch={setSearchKey}
        />
        {searchKey && modalMode == ModalModeEnum.Choose && (
          <>
            <Center
              fontWeight="400"
              fontSize="15px"
              whiteSpace="nowrap"
              color="#000000"
              marginRight={respDims(4)}
              marginLeft={respDims(13)}
            >
              无想要结果？
            </Center>
            <Popover
              trigger="click"
              placement="bottom"
              isOpen={isOpen}
              onOpen={onOpen}
              onClose={onClose}
            >
              <PopoverTrigger>
                <Center
                  fontWeight="500"
                  fontSize="15px"
                  color="primary.500"
                  cursor="pointer"
                  whiteSpace="nowrap"
                  onClick={() => {
                    // onAllSearch();
                  }}
                >
                  开始全局搜索
                </Center>
              </PopoverTrigger>
              <PopoverContent
                w={respDims('697fpx')}
                h={respDims('688fpx')}
                mt="13px"
                mr="118px"
                boxShadow="0px 3px 10px 0px rgba(0,0,0,0.11)"
              >
                {isOpen && (
                  <AllSearchModal
                    onClose={onClose}
                    bizType={
                      nav?.type === NavTypeEnum.tenant
                        ? BizTypeEnum.TenantLibrary
                        : BizTypeEnum.MyLibrary
                    }
                    searchContent={searchKey}
                    onSearchContent={() => {}}
                    onFolderPath={() => {}}
                    onItemClick={(cloudFile) => {
                      console.log(cloudFile);

                      if (
                        accept?.length &&
                        !accept?.includes(cloudFile.fileName.split('.').pop() || '')
                      ) {
                        return Toast.info('只能选择' + accept.join('、') + '等格式');
                      }

                      if (cloudFile.fileType !== FileTypeEnum.File) {
                        return Toast.info('请选择文件');
                      }
                      if (modalMode == ModalModeEnum.Choose) {
                        const newItem = {
                          fileId: cloudFile.file?.id!,
                          fileName: cloudFile.file?.fileName!,
                          fileKey: cloudFile.file?.fileKey!,
                          fileUrl: cloudFile.file?.fileUrl!,
                          rowKey: `${FileTypeEnum.File}-${cloudFile.file?.fileKey}`,
                          fileType: cloudFile.file?.fileType,
                          fileSize: cloudFile.file?.fileSize
                        };
                        const newFiles: ChooserFileType[] = [
                          newItem,
                          ...(selectedFiles?.filter((item) => item.fileKey !== cloudFile.fileKey) ||
                            [])
                        ];
                        onSelectedFilesChange(newFiles);
                        onFilesChange(newFiles);
                      }
                      // if (modalMode == ModalModeEnum.Save) {
                      // }
                    }}
                  />
                )}
              </PopoverContent>
            </Popover>
          </>
        )}
      </Flex>

      <Flex
        flex="1 0 0"
        borderTop="1px solid #E5E7EB"
        borderBottom="1px solid #E5E7EB"
        overflow="hidden"
        align="stretch"
      >
        <NavList
          width={respDims(250, 220)}
          access={access}
          nav={nav}
          onNavChange={setNav}
          flexShrink="0"
          pl={respDims(32)}
          pr={respDims(10)}
          borderRight="1px solid #E5E7EB"
        />
        {/* <>{nav.path[1] && nav.path[1].space?.spaceName}</> */}
        <Box flex="1" ml={respDims(10)} mr={respDims(32)} overflow="hidden">
          {nav?.type === NavTypeEnum.tenant && (
            <Tenant
              access={access}
              nav={nav}
              searchKey={searchKey}
              selectedFiles={selectedFiles}
              onSelectedFilesChange={onSelectedFilesChange}
            />
          )}
          {nav?.type === NavTypeEnum.personal && (
            <Personal
              access={{ mode: AccessModeEnum.Manage }}
              searchKey={searchKey}
              selectedFiles={selectedFiles}
              onSelectedFilesChange={onSelectedFilesChange}
              onGetLocationPath={(e) => {}}
            />
          )}
        </Box>
      </Flex>

      {modalMode === ModalModeEnum.Choose && (
        <Box px={respDims(32)} pt={respDims(20)}>
          <Flex
            color="#000000"
            alignItems="center"
            fontSize={respDims('14fpx')}
            lineHeight={respDims('24fpx')}
            fontWeight="bold"
            mr={respDims(5)}
          >
            <Text mr={respDims(5)}>{`已选择文件:${selectedFiles.length}`}</Text>
            {selectTips && <QuestionTip label={selectTips}></QuestionTip>}
          </Flex>

          <Flex flexWrap="wrap" maxH={respDims('150fpx')} overflowY="auto">
            {selectedFiles.map((file) => (
              <Box
                key={file.rowKey}
                pt="7px"
                pr="7px"
                mr="7px"
                mt="7px"
                w={respDims('210fpx')}
                pos="relative"
              >
                <Flex
                  px={respDims(12)}
                  py={respDims(10)}
                  bgColor="#F8FAFC"
                  borderRadius={respDims(8)}
                  border="1px solid #E5E7EB"
                  align="center"
                >
                  <FileIcon
                    fileName={file.fileName}
                    fileUrl={file.fileUrl}
                    w={respDims(32)}
                    h={respDims(32)}
                  />
                  <MyTooltip overflowOnly label={file.fileName}>
                    <Box
                      ml={respDims(10)}
                      color="#1D2129"
                      fontSize={respDims('15fpx')}
                      whiteSpace="nowrap"
                      overflow="hidden"
                    >
                      {file.fileName}
                    </Box>
                  </MyTooltip>
                </Flex>

                <SvgIcon
                  pos="absolute"
                  right="0"
                  top="0"
                  name="circleClose"
                  cursor="pointer"
                  onClick={() => {
                    setSelectedFiles((state) => state.filter((it) => it.rowKey !== file.rowKey));
                    onFilesChange(selectedFiles?.filter((it) => it.rowKey !== file.rowKey) || []);
                  }}
                />
              </Box>
            ))}
          </Flex>
        </Box>
      )}
    </Flex>
  );
};

export default Panel;
