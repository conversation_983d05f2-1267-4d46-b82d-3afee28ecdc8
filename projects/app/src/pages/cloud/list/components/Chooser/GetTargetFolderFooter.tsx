import MyModal from '@/components/MyModal';
import { Box, Flex, Text } from '@chakra-ui/react';
import Panel from './Panel';
import { respDims } from '@/utils/chakra';
import { Button, Form, Input } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AccessModeEnum, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { ChooserProvider, useChooserContext } from './ChooserContext';
import styles from '@/pages/index.module.scss';
import { uploadByHtmlStr, uploadFile } from '@/api/cloud';
import { NavTypeEnum, PathItemTypeEnum } from '@/constants/cloud';
import { Toast } from '@/utils/ui/toast';
import { PathSpaceType, PathItemType, NavType } from '@/types/cloud';
import { useRequest } from '@/hooks/useRequest';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import htmlDocx from 'html-docx-js/dist/html-docx';
import { ChooserFileType } from '.';
import FileIcon from '@/pages/cloud/components/FileIcon';
import MyTooltip from '@/components/MyTooltip';
import SvgIcon from '@/components/SvgIcon';
import { useLoading } from '@/hooks/useLoading';

const GetTargetFolderFooter = ({
  files,
  onConfirm,
  onClose,
  filename,
  setTitle
}: {
  files?: ChooserFileType[];
  onConfirm?: (path: PathItemType[], navType: NavTypeEnum, filename: string) => void;
  onClose?: () => void;
  setTitle?: (title: string) => void;
  filename?: string;
}) => {
  const { path, navType, showFilenameInput } = useChooserContext();
  const { setIsLoading, isLoading } = useLoading();
  const [form] = Form.useForm();

  return (
    <Box>
      <Flex justify="flex-end" px={respDims(32)} py={respDims(24)} className={styles['my-form']}>
        {showFilenameInput ? (
          <Form
            form={form}
            style={{ flex: 1, marginRight: '16px' }}
            initialValues={{ filename: filename }}
          >
            <Form.Item
              name="filename"
              rules={[{ required: true, message: '请输入文件名称' }]}
              style={{ width: '100%', marginBottom: '0px' }}
            >
              <Input
                placeholder="请输入文件名称"
                onChange={(e) => form.setFieldsValue({ filename: e.target.value })}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Form>
        ) : (
          <></>
        )}
        <Button htmlType="button" onClick={onClose}>
          取消
        </Button>
        <Button
          loading={isLoading}
          style={{ marginLeft: '16px' }}
          type="primary"
          htmlType="submit"
          onClick={async () => {
            setIsLoading(true);
            await onConfirm?.(path, navType!, form.getFieldValue('filename'));
            setIsLoading(false);
          }}
        >
          保存
        </Button>
      </Flex>
    </Box>
  );
};

export default GetTargetFolderFooter;
