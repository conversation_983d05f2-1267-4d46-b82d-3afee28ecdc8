import MyTable from '@/components/MyTable';
import { Box, Center, Flex } from '@chakra-ui/react';
import Header from './Header';
import { RowProps, Table, TableProps } from 'antd';
import Footer from './Footer';
import { copyFile, getMyFilePage, removeMyFileBatch } from '@/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { TableRowSelection } from 'antd/es/table/interface';
import { BizTypeEnum, FileTypeEnum, PrivilegeEnum } from '@/constants/api/cloud';
import { formatFileSize } from '@/utils/tools';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import styles from '../../../../../cloud.module.scss';
import { FileType, locationPathType } from '@/types/api/cloud';
import { AccessType, FilePathType, PathItemType, PathSpaceType } from '@/types/cloud';
import { FooterComponentProps, MyTableRef, SearchBarProps } from '@/components/MyTable/types';
import { PathItemTypeEnum, myRootFolder } from '@/constants/cloud';
import { useCloudStore } from '@/store/useCloudStore';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { Toast } from '@/utils/ui/toast';
import { useNotificationStore } from '@/store/useTificationContext';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';
import { useUserStore } from '@/store/useUserStore';
import { UserRoleEnum } from '@/constants/api/auth';
import { ChooserFileType, ModalModeEnum } from '../..';
import { CheckboxProps } from 'antd/lib';
import { useChooserContext } from '../../ChooserContext';

const Personal = ({
  access,
  onGetLocationPath,
  searchKey,
  selectedFiles,
  onSelectedFilesChange
}: {
  onGetLocationPath: (data: locationPathType[], searchContent: string) => void;
  access: AccessType;
  searchKey?: string;
  selectedFiles: ChooserFileType[];
  onSelectedFilesChange: (files: ChooserFileType[]) => void;
}) => {
  const selectedRowKeys = useMemo(() => selectedFiles.map((it) => it.rowKey!), [selectedFiles]);
  const { userInfo } = useUserStore();
  const tableRef = useRef<MyTableRef>(null);

  const isAdmin = userInfo?.roleType === UserRoleEnum.Admin;

  const { selectFile, setSelectFile, setShowFileInfo, showFileInfo } = useCloudStore();

  const [path, setPath] = useState<FilePathType>([
    {
      type: PathItemTypeEnum.file,
      file: myRootFolder
    }
  ]);

  const { setContextPath, modalMode, accept, maxSize } = useChooserContext();
  useEffect(() => {
    setContextPath(path);
  }, [path, setContextPath]);

  const { privilege } = useMemo(() => {
    return {
      privilege: isAdmin
        ? PrivilegeEnum.Owner
        : path.reduce(
            (max, item) =>
              item.file.privileges?.reduce((max, cur) => (cur > max ? cur : max), max) ?? max,
            PrivilegeEnum.View
          )
    };
  }, [path, isAdmin]);

  const parentId = path[path.length - 1].file.id;

  const onClickFile = (file: FileType) => {
    if (file.fileType === FileTypeEnum.Folder) {
      setPath((state) => [...state, { type: PathItemTypeEnum.file, file }]);
    }
  };

  const onBackToParent = useCallback(() => {
    setPath((state) => state.slice(0, state.length - 1));
  }, []);

  const onClickPathItem = useCallback((pathItem: PathItemType) => {
    if (pathItem.type === PathItemTypeEnum.space) {
      setPath([]);
    } else {
      setPath((state) => {
        const index = state.findIndex((item) => item.file.id === pathItem.file.id);
        return index >= 0 ? state.slice(0, index + 1) : state;
      });
    }
  }, []);

  useEffect(() => {
    tableRef.current?.setCurrent?.(1);
  }, [parentId]);

  const TableHeader = useCallback(
    ({ ...props }: SearchBarProps) => {
      return (
        <Header
          {...props}
          path={path}
          onBackToParent={onBackToParent}
          onClickPathItem={onClickPathItem}
          onReload={() => tableRef.current?.reload()}
        />
      );
    },
    [path, onBackToParent, onClickPathItem]
  );

  const TableFooter = useCallback((props: FooterComponentProps) => {
    return <Footer {...props} />;
  }, []);

  const rowSelection: TableRowSelection<FileType> = {
    type: 'checkbox',
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    columnWidth: 70,
    getCheckboxProps: (
      record: FileType
    ): Partial<Omit<CheckboxProps, 'checked' | 'defaultChecked'>> => {
      const isFile = record.fileType === FileTypeEnum.File;
      const isOversize = Boolean(
        record.fileSize && record.fileSize > (maxSize ?? 100) * 1024 * 1024
      ); // 100MB in bytes

      let disabeld = accept ? !accept.includes(record.fileName.split('.').pop() || '') : false;
      disabeld = disabeld || !isFile || isOversize;
      return disabeld ? { disabled: true, style: { display: 'none' } } : {};
    },
    onChange(selectedRowKeys) {
      const files: ChooserFileType[] = [];
      selectedRowKeys.forEach((rowKey) => {
        const file = selectedFiles.find((it) => it.rowKey === rowKey);
        if (file) {
          files.push(file);
          return;
        }
        const file2 = tableRef.current?.data.find((it) => it.rowKey === rowKey);
        if (file2) {
          files.push({
            fileId: file2.id,
            fileName: file2.fileName,
            fileKey: file2.fileKey!,
            fileUrl: file2.file?.fileUrl,
            rowKey: file2.rowKey,
            fileSize: file2.fileSize
          });
        }
      });
      onSelectedFilesChange(files);
    }
  };

  const columns: TableProps<FileType>['columns'] = [
    {
      title: '文件名称',
      key: 'folderName',
      render: (_, record) => {
        const { fileType, fileName } = record;
        return (
          <Flex alignItems="center" cursor="pointer" onClick={() => onClickFile(record)}>
            <FileIcon
              fileType={fileType}
              fileName={fileName}
              fileUrl={record.file?.fileUrl}
              file={record.file}
            />
            <Box ml={respDims(12)}>{fileName}</Box>
          </Flex>
        );
      }
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      render: (_, { fileType: type, fileSize }) =>
        type === FileTypeEnum.Folder ? '' : formatFileSize(fileSize!)
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      render: (value) => dayjs(value).format('YYYY-MM-DD HH:mm')
    }
  ];

  return (
    <Box w="100%" h="100%" flexDir="column">
      {/* abcdefg */}
      <MyTable
        ref={tableRef}
        rowKey="rowKey"
        className={styles['file-table']}
        columns={columns}
        rowSelection={
          modalMode == ModalModeEnum.Save ||
          modalMode == ModalModeEnum.SaveToLocalUpload ||
          modalMode == ModalModeEnum.GetTargetFolder
            ? undefined
            : rowSelection
        }
        api={getMyFilePage}
        defaultQuery={{ parentId, searchKey }}
        boxStyle={{ px: 0, py: 0, overflow: 'visible' }}
        headerConfig={{ HeaderComponent: TableHeader }}
        FooterComponent={TableFooter}
      />
    </Box>
  );
};

export default Personal;
