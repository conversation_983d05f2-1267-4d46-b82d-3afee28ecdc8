import Breadcrumb from '@/pages/cloud/components/Breadcrumb';

import { PathItemTypeEnum } from '@/constants/cloud';
import { respDims } from '@/utils/chakra';
import { Box, Button, Flex } from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import {
  BreadcrumbItemType,
  FilePathType,
  PathType,
  PathSpaceType,
  PathItemType,
  PathFileType
} from '@/types/cloud';
import { SearchBarProps } from '@/components/MyTable/types';
import MyTooltip from '@/components/MyTooltip';
import { QuestionOutlineIcon } from '@chakra-ui/icons';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import FolderModal from '@/pages/cloud/components/FolderModal';
import { useChooserContext } from '../../../ChooserContext';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import LocalUploadModal from '@/pages/cloud/components/LocalUploadModal';
import AllSearchModal from '../../../../Tenant/AllSearchModal';
import { locationPathType } from '@/types/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import useUploadRefresh from '@/pages/cloud/hooks/useUploadRefresh';
import TransferButton from '@/pages/cloud/components/Transfer/Button';
const Header = ({
  path,
  tableInstance,
  onBackToParent,
  onClickPathItem,
  onReload
}: {
  path?: PathType;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
  onReload?: () => void;
} & SearchBarProps) => {
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const { showCreateFolderBtn, showUploadBtn } = useChooserContext();
  const [parentId, setParentId] = useState('');
  const { addUpload } = useCloudUploader();

  const breadcrumbList = useMemo(() => {
    const list: (BreadcrumbItemType & { pathItem?: PathItemType })[] = [];

    const spaceItem = path?.reduce(
      (pre: PathItemType | undefined, it: PathItemType) =>
        it.type === PathItemTypeEnum.space ? it : pre,
      undefined
    ) as PathSpaceType;

    const folderPath = path?.filter(
      (it: PathItemType) => it.type === PathItemTypeEnum.file
    ) as FilePathType;

    if (folderPath?.length) {
      list.push({
        label: '返回上一级',
        isBack: true
      });
    }

    list.push({
      label: spaceItem?.space?.spaceName || '',
      pathItem: spaceItem,
      clickable: true
    });

    folderPath?.forEach((pathItem, index) => {
      list.push({
        label: pathItem.file.fileName!,
        pathItem,
        clickable: index < folderPath.length - 1
      });
    });
    return list;
  }, [path]);

  const onAddFolder = () => {
    path &&
      openOverlay({
        Overlay: FolderModal,
        props: {
          bizType: BizTypeEnum.TenantLibrary,
          path,
          onSuccess: () => tableInstance.reload()
        }
      });
  };

  const onLocalUpload = (fileType: FileTypeEnum) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    if (fileType === FileTypeEnum.Folder) {
      input.webkitdirectory = true;
    }
    input.onchange = async (event) => {
      const files = (event.target as HTMLInputElement).files;
      if (files && path) {
        try {
          const lastPathItem = path[path.length - 1];

          const parentId =
            lastPathItem.type === PathItemTypeEnum.space
              ? (lastPathItem as PathSpaceType).space.id
              : (lastPathItem as PathFileType).file.id;
          setParentId(parentId);
          addUpload({
            path: path.map((it) =>
              it.type === PathItemTypeEnum.space ? it.space.spaceName : it.file.fileName
            ),
            parentId,
            files: Array.from(files),
            bizType: BizTypeEnum.TenantLibrary
          });

          Toast.success('已添加到上传队列');
        } catch (error) {
          console.error('Upload failed:', error);
          Toast.error('上传失败');
        }
      }
    };
    input.click();
  };

  useUploadRefresh({ parentId, onRefresh: () => onReload?.() });

  return (
    <Flex w="100%" mt={respDims(10)} alignItems="center" justifyContent="space-between">
      <Flex alignItems="center">
        <Breadcrumb
          list={breadcrumbList}
          {...(breadcrumbList.length === 1 && { fontSize: respDims('16fpx') })}
          onClickItem={(item) => item.pathItem && onClickPathItem?.(item.pathItem)}
          onBack={onBackToParent}
        />
        <Flex ml={2} alignItems="center">
          <MyTooltip label={'选择单个文件不能超过100MB'}>
            <QuestionOutlineIcon w={'0.9rem'} />
          </MyTooltip>
        </Flex>
      </Flex>
      {showCreateFolderBtn && (
        <Button
          ml={respDims(13)}
          variant="outline"
          colorScheme="primary"
          px={respDims(20)}
          fontSize={respDims('14fpx')}
          borderRadius={respDims(8)}
          onClick={onAddFolder}
        >
          <SvgIcon name="folder" w={respDims('14fpx')} h={respDims('14fpx')} />
          <Box ml={respDims(8)}>新建文件夹</Box>
        </Button>
      )}
      {showUploadBtn && (
        <Flex py={respDims(10)}>
          <Button
            variant="solid"
            colorScheme="primary"
            px={respDims(20)}
            mr={respDims(10)}
            fontSize={respDims('14fpx')}
            borderRadius={respDims(8)}
            onClick={() => onLocalUpload(FileTypeEnum.File)}
          >
            <SvgIcon color="#FFF" name="cloudUpload" w={respDims('14fpx')} h={respDims('14fpx')} />
            <Box ml={respDims(8)}>上传文件</Box>
          </Button>
          <TransferButton ml="auto" />
        </Flex>
      )}
      <OverlayContainer></OverlayContainer>
    </Flex>
  );
};

export default Header;
