import { respDims, rpxDim } from '@/utils/chakra';
import { Box, Button, Flex } from '@chakra-ui/react';
import { BreadcrumbItemType, PathType, PathItemType, PathSpaceType } from '@/types/cloud';
import { useMemo, useEffect } from 'react';
import { SearchBarProps } from '@/components/MyTable/types';
import Breadcrumb from '@/pages/cloud/components/Breadcrumb';
import SearchInput from '@/pages/cloud/components/SearchInput';
import { MessageBox } from '@/utils/ui/messageBox';
import { cloudRecycleClearAll } from '@/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { PathItemTypeEnum, SearchTypeEnum } from '@/constants/cloud';

type QueryType = {
  name: string;
};

const TableHeader = ({
  path,
  onSearch = () => {}, // 提供默认值
  onRefresh,
  onClickPathItem,
  onBackToParent,
  defaultQuery
}: {
  path?: PathType;
  onSearch?: (query: QueryType) => void;
  onRefresh?: () => void;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
} & SearchBarProps) => {
  const breadcrumbList = useMemo(() => {
    const list: (BreadcrumbItemType & { pathItem?: PathItemType })[] = [];

    const spaceItem = path?.reduce(
      (pre: PathItemType | undefined, it: PathItemType) =>
        it.type === PathItemTypeEnum.space ? it : pre,
      undefined
    ) as PathSpaceType;

    const folderPath = path?.filter(
      (it: PathItemType) => it.type === PathItemTypeEnum.file
    ) as PathType;

    if (folderPath?.length) {
      list.push({
        label: '返回上一级',
        isBack: true
      });
    }

    list.push({
      label: spaceItem?.space?.spaceName || '回收站',
      pathItem: spaceItem || { file: { id: 'recycleHome' }, type: ' ' },
      clickable: true
    });

    folderPath?.forEach((pathItem, index) => {
      const label: string = pathItem.file?.fileName || '';
      list.push({
        label,
        pathItem,
        clickable: index < folderPath.length - 1
      });
    });
    return list;
  }, [path]);

  const emptyRecycleBin = () => {
    MessageBox.confirm({
      title: '清空回收站提示',
      content: '清空回收站将不可找回文件，确定清空回收站文件？',
      onOk: () => {
        cloudRecycleClearAll().then((res) => {
          Toast.success('操作成功');
          if (onRefresh) {
            onRefresh();
          }
        });
      }
    });
  };

  return (
    <Box w="100%">
      <Flex alignItems="center">
        <SearchInput
          border="1px solid #fff"
          bgColor="#eeeefe"
          searchIconDirect="left"
          isShowCloseIcon
          mr="auto"
          type={SearchTypeEnum.file}
          placeholder="通过文件名、正文搜索文档"
          w={respDims(695)}
          onSearch={(e) =>
            onSearch?.({
              searchKey: e,
              id: defaultQuery?.id,
              bizType: defaultQuery?.bizType,
              fileType: defaultQuery?.fileType,
              parentId: defaultQuery?.parentId
            })
          }
          searchBgColor="#eeeefe"
        />

        <Button
          ml={respDims(13)}
          variant="outline"
          colorScheme="primary"
          px={respDims(20)}
          fontSize={respDims('14fpx')}
          borderRadius={respDims(8)}
          onClick={emptyRecycleBin}
          h={respDims(36)}
          bgColor="#fff"
        >
          清空回收站
        </Button>
      </Flex>
      <Flex
        borderRadius="8px 8px 0 0"
        w="100%"
        mt={rpxDim(22)}
        pt={rpxDim(22)}
        pl={rpxDim(20)}
        pb={rpxDim(12)}
        bgColor="#fff"
      >
        <Breadcrumb
          list={breadcrumbList}
          onClickItem={(item) => item.pathItem && onClickPathItem?.(item.pathItem)}
          onBack={onBackToParent}
        />
      </Flex>
    </Box>
  );
};

export default TableHeader;
