import React from 'react';
import ToolBar from '@/pages/cloud/components/ToolBar';
import { respDims, rpxDim } from '@/utils/chakra';
import { Box, Flex } from '@chakra-ui/react';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { cloudRecycleBatchRecovery, cloudRecycleBatchDelete } from '@/api/cloud';

interface FooterProps {
  total?: number;
  PageRender: () => JSX.Element;
  selectedRowKeys: string[];
  selectedParentSpaceId: string[];
  refreshTable: (parentSpaceId?: string) => void;
}

const Footer: React.FC<FooterProps> = ({
  total,
  PageRender,
  selectedRowKeys,
  selectedParentSpaceId,
  refreshTable
}) => {
  const onBatchRecoverFile = () => {
    const params = selectedRowKeys.map((item) => {
      return { id: item };
    });
    const uniqueParentSpaceIds = Array.from(new Set(selectedParentSpaceId));
    console.log('params', uniqueParentSpaceIds);
    MessageBox.confirm({
      title: '批量恢复提示',
      content: '恢复文件则在原删除文件位置显示，确定恢复文件？',
      onOk: () => {
        cloudRecycleBatchRecovery(params).then((res) => {
          Toast.success('操作成功');
          refreshTable();
          uniqueParentSpaceIds.forEach((parentSpaceId) => {
            refreshTable(parentSpaceId);
          });
        });
      }
    });
  };

  const onBatchRemoveFile = () => {
    const params = selectedRowKeys.map((item) => {
      return { id: item };
    });
    MessageBox.confirm({
      title: '批量彻底删除提示',
      content: '彻底删除文件将不可找回文件，确定删除文件？',
      onOk: () => {
        cloudRecycleBatchDelete(params).then((res) => {
          Toast.success('操作成功');
          refreshTable();
        });
      }
    });
  };

  return (
    <Flex
      w="100%"
      alignItems="center"
      bgColor="#fff"
      borderRadius="0 0 8px 8px"
      pt={rpxDim(23)}
      pr={rpxDim(24)}
      pb={rpxDim(20)}
    >
      <ToolBar
        buttons={[
          {
            label: '恢复',
            icon: 'redo',
            onClick: onBatchRecoverFile,
            disabled: !selectedRowKeys.length
          },
          {
            label: '彻底删除',
            icon: 'trash',
            onClick: onBatchRemoveFile,
            disabled: !selectedRowKeys.length
          }
        ]}
      />

      <Box ml="auto" mr={respDims(16)} whiteSpace="nowrap">
        共{total}项数据
      </Box>
      <PageRender />
    </Flex>
  );
};

export default Footer;
