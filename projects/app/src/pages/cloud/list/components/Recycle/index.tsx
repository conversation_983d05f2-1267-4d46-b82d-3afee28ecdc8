import React, {
  forwardRef,
  ForwardedRef,
  useImperativeHandle,
  useRef,
  useState,
  useCallback,
  useMemo
} from 'react';
import MyTable from '@/components/MyTable';
import { Box, Center, Flex, Image } from '@chakra-ui/react';
import Header from './Header';
import { TableProps } from 'antd';
import Footer from './Footer';
import { MyTableRef, SearchBarProps } from '@/components/MyTable/types';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { TableRowSelection } from 'antd/es/table/interface';
import { FileTypeEnum, MoveTypeEnum } from '@/constants/api/cloud';
import dayjs from 'dayjs';
import MyMenu from '@/components/MyMenu';
import styles from '../../../cloud.module.scss';
import { MessageBox } from '@/utils/ui/messageBox';
import { FooterComponentProps } from '@/components/MyTable/types';
import { Toast } from '@/utils/ui/toast';
import { RecycleNavType } from '@/types/cloud';
import { FilePathType } from '@/types/cloud';
import {
  getCloudRecyclePage,
  cloudRecycleDelete,
  cloudRecycleRecovery,
  getCloudRecycleSubListPage
} from '@/api/cloud';
import { PathItemTypeEnum } from '@/constants/cloud';
import { PathItemType } from '@/types/cloud';
import useFilePreview from '@/hooks/useFilePreview';
import { RecycleSubListPageType, RecyclePageType } from '@/types/api/cloud';
import FileIcon from '@/pages/cloud/components/FileIcon';
import MyTooltip from '@/components/MyTooltip';

interface RecycleProps {
  refreshTreeRef: (parentSpaceId?: string) => void;
  nav?: RecycleNavType;
}

interface RecycleRef {
  refresh: () => void;
}

interface FileState {
  id: string;
  tmbId: number;
  bizType: number;
  fileType: number;
  parentId: number;
}

const Recycle = ({ refreshTreeRef, nav }: RecycleProps, ref: ForwardedRef<RecycleRef>) => {
  const { previewFile } = useFilePreview();

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedParentSpaceId, setSelectedParentSpaceId] = useState<string[]>([]);
  const [fileState, setFileState] = useState<FileState>({
    id: '',
    tmbId: 0,
    bizType: 0,
    fileType: 0,
    parentId: 0
  });
  const [fileId, setFileId] = useState('');
  const [fileName, setFileName] = useState('');
  const [location, setLocation] = useState(''); //子文件夹接口无文件位置字段需要储存起来
  const [deleterName, setDeleterName] = useState(''); //子文件夹接口无删除者字段需要储存起来
  const [folderPath, setFolderPath] = useState<FilePathType>([]);

  const { path, cloudSpaceId } = useMemo(() => {
    const path = [...folderPath];
    const item = path[path.length - 1];
    return {
      path,
      cloudSpaceId: item ? item.file.id : ''
    };
  }, [folderPath]);

  const tableRef = useRef<MyTableRef>(null);

  useImperativeHandle(ref, () => ({
    refresh: () => {
      refreshTable();
    }
  }));

  const refreshTable = (parentSpaceId?: string) => {
    setFileId('');
    setFileName('');
    tableRef.current?.reload();
    refreshTreeRef(parentSpaceId);
  };

  const onRefresh = useCallback(() => {
    tableRef.current?.reload();
  }, []);

  const onBackToParent = useCallback(() => {
    setFolderPath((state) => state.slice(0, state.length - 1));

    if (folderPath.length > 1) {
      const lastElement = folderPath[folderPath.length - 2];
      const { fileType, id, bizId } = lastElement.file;

      if (folderPath.length === 2) {
        if (fileType && bizId) {
          updateFileState(fileType, bizId);
          tableRef.current?.reload();
        }
      } else {
        if (fileType && id) {
          updateFileState(fileType, Number(id));
          tableRef.current?.reload();
        }
      }
    } else if (folderPath.length === 1) {
      setFileId('');
      setFileState({
        id: '',
        tmbId: 0,
        bizType: 0,
        fileType: 0,
        parentId: 0
      });
      tableRef.current?.reload();
    }
  }, [folderPath]);

  const onClickPathItem = useCallback((pathItem: PathItemType) => {
    const { file } = pathItem;

    if (!file) {
      return;
    }

    const { id, bizId, bizType, fileType } = file;

    if (id === 'recycleHome') {
      setFileId('');
      setFileState({
        id: '',
        tmbId: 0,
        bizType: 0,
        fileType: 0,
        parentId: 0
      });
      tableRef.current?.reload();
      setFolderPath([]);
      return;
    }

    if (bizId) {
      setFileState({
        id,
        tmbId: 0,
        bizType: Number(bizType),
        fileType,
        parentId: bizId
      });
    } else {
      updateFileState(fileType, Number(id));
    }

    if (pathItem.type === PathItemTypeEnum.space) {
      setFolderPath([]);
    } else {
      setFolderPath((state) => {
        const index = state.findIndex((item) => item.file.id === id);
        return index >= 0 ? state.slice(0, index + 1) : state;
      });
    }
  }, []);

  const TableHeader = useCallback(
    (props: SearchBarProps) => {
      return (
        <Header
          path={path}
          {...props}
          onRefresh={onRefresh}
          onBackToParent={onBackToParent}
          onClickPathItem={onClickPathItem}
        />
      );
    },
    [path, onRefresh, onBackToParent, onClickPathItem]
  );

  const TableFooter = useCallback(
    (props: FooterComponentProps) => {
      return (
        <Footer
          {...props}
          selectedRowKeys={selectedRowKeys}
          selectedParentSpaceId={selectedParentSpaceId}
          refreshTable={refreshTable}
        />
      );
    },
    [selectedRowKeys, selectedParentSpaceId]
  );

  const rowSelection: TableRowSelection<RecycleSubListPageType> = {
    type: 'checkbox',
    onChange: (selectedRowKeys: React.Key[], selectedRows: RecycleSubListPageType[]) => {
      console.log('selectedRows', selectedRows);
      setSelectedRowKeys(selectedRowKeys as string[]);
      setSelectedParentSpaceId(selectedRows.map((item) => item.parentSpaceId ?? ''));
    }
  };

  const onRecoverFile = (id: string) => {
    MessageBox.confirm({
      title: '恢复提示',
      content: '恢复文件则在原删除文件位置显示，确定恢复文件？',
      onOk: () => {
        cloudRecycleRecovery(id).then((res) => {
          Toast.success('操作成功');
          refreshTable();
        });
      }
    });
  };

  const onRemoveFile = (id: string) => {
    MessageBox.confirm({
      title: '彻底删除提示',
      content: '彻底删除文件将不可找回文件，确定删除文件？',
      onOk: () => {
        cloudRecycleDelete(id).then((res) => {
          Toast.success('操作成功');
          refreshTable();
        });
      }
    });
  };

  const updateFileState = (fileType: number, parentId: number) => {
    setFileState((prevState) => ({
      ...prevState,
      fileType: fileType,
      parentId: parentId
    }));
  };

  const onClickFile = async (file: RecyclePageType) => {
    const previewSuccess = await previewFile({
      fileUrl: file.file?.fileUrl ?? '',
      fileType: file.fileType,
      fileKey: file.file?.fileKey
    });
    if (previewSuccess) {
      return;
    }

    setFileState({
      id: file.id,
      tmbId: file.tmbId,
      bizType: file.bizType,
      fileType: file.fileType,
      parentId: file.bizId
    });

    setLocation(file.location);

    setDeleterName(file.deleterName);

    setFileId(file.id);

    tableRef.current?.reload();

    if (file.fileType === FileTypeEnum.Folder) {
      setFolderPath((state) => [...state, { type: PathItemTypeEnum.file, file: file as any }]);
    }
  };

  const onChildClickFile = async (file: RecycleSubListPageType) => {
    const previewSuccess = await previewFile({
      fileUrl: file.file?.fileUrl ?? '',
      fileType: file.fileType,
      fileKey: file.file?.fileKey
    });
    if (previewSuccess) {
      return;
    }

    updateFileState(file.fileType, Number(file.id));
    if (file.fileType === FileTypeEnum.Folder) {
      setFolderPath((state) => [...state, { type: PathItemTypeEnum.file, file: file as any }]);
    }
  };

  const columns: TableProps<RecyclePageType>['columns'] = [
    {
      title: '文件名称',
      key: 'folderName',
      render: (_, record) => {
        const { fileName } = record;
        return (
          <Flex
            whiteSpace="nowrap"
            alignItems="center"
            cursor="pointer"
            onClick={() => onClickFile(record)}
          >
            {Number(record.type) === MoveTypeEnum.Space ||
            record.fileType === FileTypeEnum.Space ? (
              <SvgIcon name="box3" color="#4E5969" w={respDims(45)} h={respDims(45)} ml="-2px" />
            ) : (
              <FileIcon flexShrink="0" {...record} />
            )}
            <MyTooltip overflowOnly>
              <Box
                ml={respDims(12)}
                fontSize={respDims('14fpx')}
                lineHeight="1.5em"
                maxH="3em"
                wordBreak="break-all"
                overflow="hidden"
                textOverflow="ellipsis"
                maxW="170px"
              >
                {fileName}
              </Box>
            </MyTooltip>
          </Flex>
        );
      }
    },
    {
      title: '删除时间',
      dataIndex: 'createTime',
      render: (value) => {
        return <Box whiteSpace="nowrap">{dayjs(value).format('YYYY-MM-DD HH:mm')}</Box>;
      }
    },
    {
      title: '文件位置',
      dataIndex: 'location',
      render: (value) => {
        return <Box whiteSpace="nowrap">{value}</Box>;
      }
    },
    {
      title: '删除者',
      dataIndex: 'deleterName',
      render: (value) => {
        return <Box whiteSpace="nowrap">{value}</Box>;
      }
    },
    {
      title: '',
      key: 'action',
      render: (_, record) => (
        <Flex justify="flex-end">
          <MyMenu
            trigger="hover"
            width={100}
            Button={
              <Center
                w={respDims('24fpx')}
                h={respDims('24fpx')}
                borderRadius={respDims(4)}
                cursor="pointer"
                _hover={{
                  bgColor: '#EFEFEF'
                }}
              >
                <SvgIcon name="more" w={respDims('14fpx')} h={respDims('14fpx')} />
              </Center>
            }
            menuList={[
              {
                label: '恢复',
                icon: <SvgIcon name="redo" />,
                onClick: () => onRecoverFile(record.id)
              },
              {
                label: '彻底删除',
                icon: <SvgIcon name="trash" />,
                onClick: () => onRemoveFile(record.id)
              }
            ]}
          />
        </Flex>
      )
    }
  ];
  // 子文件夹字段显示
  const childColumns: TableProps<RecycleSubListPageType>['columns'] = [
    {
      title: '文件名称',
      key: 'folderName',
      render: (_, record) => {
        const { fileType, fileName } = record;
        return (
          <Flex alignItems="center" cursor="pointer" onClick={() => onChildClickFile(record)}>
            <FileIcon {...record} />
            {/* <Box ml={respDims(12)}>{fileName}</Box> */}
            <Box ml={respDims(12)}>
              {fileType === FileTypeEnum.Folder ? record.folderName : fileName}
            </Box>
          </Flex>
        );
      }
    },
    {
      title: '删除时间',
      dataIndex: 'deleteTime',
      render: (value) => dayjs(value).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '文件位置',
      dataIndex: 'location',
      render: (_) => (
        <Flex alignItems="center">
          <Box ml={respDims(12)}>{location}</Box>
        </Flex>
      )
    },
    {
      title: '删除者',
      dataIndex: 'deleterName',
      render: (_) => (
        <Flex alignItems="center">
          <Box ml={respDims(12)}>{deleterName}</Box>
        </Flex>
      )
    }
  ];
  return (
    <Box w="100%" h="100%" flexDir="column">
      <MyTable
        className={styles['file-table']}
        ref={tableRef}
        columns={!fileId ? columns : childColumns}
        defaultQuery={fileState}
        rowSelection={rowSelection}
        api={!fileId ? getCloudRecyclePage : getCloudRecycleSubListPage}
        boxStyle={{ px: 0, py: 0, overflow: 'visible' }}
        tableStyle={{
          paddingLeft: '20px',
          paddingRight: '20px'
        }}
        headerConfig={{ HeaderComponent: TableHeader }}
        FooterComponent={!fileId ? TableFooter : undefined}
        emptyConfig={{
          emptyStyle: {
            bgColor: 'white',
            h: '100%',
            borderRadius: '0 0 8px 8px'
          },
          EmptyComponent: () => (
            <>
              <Image src="/imgs/common/folder_empty.svg" w="150px" alt="" />
              <Box color="#909399" fontSize={respDims(14, 12)}>
                {'暂无数据'}
              </Box>
            </>
          )
        }}
      />
    </Box>
  );
};

export default forwardRef(Recycle);
