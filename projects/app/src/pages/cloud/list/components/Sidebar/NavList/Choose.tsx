import { ChakraProps, Flex } from '@chakra-ui/react';
import NavItem, { NavItemType } from './NavItem';
import SpaceTree, { SpaceTreeRef } from '../SpaceTree';
import { AccessType, NavType } from '@/types/cloud';
import { locationPathType, SpaceType } from '@/types/api/cloud';
import { NavTypeEnum, navPersonal, navRecycle } from '@/constants/cloud';
import { useEffect, useRef, useState, useMemo } from 'react';
import { AccessModeEnum } from '@/constants/api/cloud';
import { useTenantStore } from '@/store/useTenantStore';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import SpaceModal from '../../Tenant/SpaceModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { UserRoleEnum } from '@/constants/api/auth';
import { useUserStore } from '@/store/useUserStore';

const NavList = ({
  access,
  nav,
  folderPathData,
  spaceTreeRef,
  onNavChange,
  onSpaceRemove,
  ...props
}: {
  access: AccessType;
  nav?: NavType;
  folderPathData?: locationPathType[];
  spaceTreeRef?: React.Ref<SpaceTreeRef>;
  onNavChange?: (nav?: NavType) => void;
  onSpaceRemove?: (space: SpaceType) => void;
} & ChakraProps) => {
  const [expandedIndex, setExpandedIndex] = useState(0);
  const { openOverlay } = useOverlayManager();
  const { userInfo } = useUserStore();
  const hasInitialized = useRef(false);
  const [isAddSuccess, setIsAddSuccess] = useState(false);
  const isAdmin = userInfo?.roleType === UserRoleEnum.Admin;
  const { industryAlias } = useTenantStore();

  const onAddSpace = useCallbackRef(() => {
    openOverlay({
      Overlay: SpaceModal,
      props: {
        parentId: '0',
        onSuccess: () => {
          setIsAddSuccess(true);
        }
      }
    });
  });

  const navList = useMemo(() => {
    const list: NavItemType[] = [
      {
        label: (
          <Flex alignItems="center">
            {industryAlias}数据空间
            {isAdmin && (
              <SvgIcon
                p="3px"
                w="23px"
                h="22px"
                bgColor="#F8FAFC"
                borderRadius="4px"
                color="#4E5969"
                onClick={(e) => {
                  e.stopPropagation();
                  onAddSpace();
                }}
                ml={respDims(53)}
                name="plus"
              />
            )}
          </Flex>
        ),
        icon: 'navSchoolLine',
        activeIcon: 'navSchoolLine',
        content: (
          <SpaceTree
            ref={spaceTreeRef}
            access={access}
            folderPathData={folderPathData}
            isAddSuccess={isAddSuccess}
            path={nav?.type === NavTypeEnum.tenant ? nav.path : undefined}
            onPathChange={(path) => {
              onNavChange?.({ type: NavTypeEnum.tenant, path });
            }}
            onRemove={onSpaceRemove}
          />
        )
      }
    ];

    if (access.mode === AccessModeEnum.View || access.mode === AccessModeEnum.Manage) {
      list.push({
        label: '我的数据空间',
        icon: 'user',
        activeIcon: 'user',
        nav: navPersonal
      });
    }

    if (access.mode === AccessModeEnum.Manage) {
      list.push({
        label: '回收站',
        icon: 'package',
        activeIcon: 'package',
        nav: navRecycle
      });
    }

    return list;
  }, [
    access,
    industryAlias,
    isAdmin,
    isAddSuccess,
    nav,
    onAddSpace,
    onNavChange,
    onSpaceRemove,
    spaceTreeRef,
    folderPathData
  ]);

  useEffect(() => {
    if (
      !hasInitialized.current &&
      (access.mode !== AccessModeEnum.Manage || (!nav && access.mode === AccessModeEnum.Manage))
    ) {
      onNavChange?.(navPersonal);
      hasInitialized.current = true;
    }
  }, [nav, onNavChange, access.mode]);

  return (
    <Flex flexDir="column" overflowY="scroll" overflowX="hidden" {...props}>
      {navList.map((item, index) => (
        <NavItem
          key={index}
          {...item}
          isActive={item.nav?.type === nav?.type && !!nav}
          isExpanded={expandedIndex === index}
          content={item.content}
          onClick={() => {
            item.nav && onNavChange?.(item.nav);
            item.content && setExpandedIndex((state) => (state === index ? -1 : index));
          }}
        />
      ))}
    </Flex>
  );
};

export default NavList;
