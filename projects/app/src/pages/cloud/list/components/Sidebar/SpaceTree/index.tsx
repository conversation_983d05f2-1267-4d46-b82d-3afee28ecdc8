import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useCallback,
  useEffect,
  useState,
  ForwardedRef
} from 'react';
import { Tree } from 'antd';
import { Key } from 'antd/es/table/interface';
import { Box, Center, Flex } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import MyMenu from '@/components/MyMenu';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SpaceModal from '../../Tenant/SpaceModal';
import GroupModal from '../../Tenant/GroupModal';
import { MessageBox } from '@/utils/ui/messageBox';
import { getSpaceTree, sortSpace, updateSpaceParent } from '@/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { DataNode, EventDataNode, TreeProps } from 'antd/es/tree';
import { AccessType, SpacePathType, NavType } from '@/types/cloud';
import styles from '../../../../cloud.module.scss';
import useSpaceTree, { SpaceTreeDataType } from '@/pages/cloud/hooks/useSpaceTree';
import { locationPathType, SpaceType } from '@/types/api/cloud';
import { respDims } from '@/utils/chakra';
import { AccessModeEnum, PrivilegeEnum } from '@/constants/api/cloud';
import { useDroppable } from '@dnd-kit/core';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import SettingModal from '../../Tenant/SettingModal';
import InfoSpaceModal from '../../Tenant/InfoSpaceModal';
import { eventBus, EventNameEnum } from '@/utils/eventbus';
export interface SpaceTreeProps {
  access: AccessType;

  path?: SpacePathType;

  folderPathData?: locationPathType[];

  isAddSuccess?: boolean;

  onPathChange?: (path: SpacePathType, reason: 'select' | 'update') => void;

  onRemove?: (space: SpaceType) => void;

  onNavChange?: (nav?: NavType) => void;
}

export interface SpaceTreeRef {
  refreshTree: (parentId?: string, searchKey?: string) => Promise<void>;
}

export type InfoType = {
  selfLevel: number;
  leafLevel: number;
  bottomLevel: number;
};

const maxLevel = 3;

const SpaceTree = (
  {
    access,
    path,
    folderPathData,
    isAddSuccess,
    onPathChange,
    onRemove,
    onNavChange
  }: SpaceTreeProps,
  ref: ForwardedRef<SpaceTreeRef>
) => {
  const autoSelectRef = useRef(true);

  const prevFolderPathDataRef = useRef(folderPathData);

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);

  const { treeData, refreshTree, getPath } = useSpaceTree({ access });

  const infoMapRef = useRef<Record<string, InfoType>>();

  const [dragOverKey, setDragOverKey] = useState('');

  const { openOverlay } = useOverlayManager();

  const findAndSelectNode = (data: SpaceTreeDataType[], pathData: locationPathType[]) => {
    const allFileTypesNotEqualTo2 = pathData.every((item) => item.fileType !== 2);
    if (allFileTypesNotEqualTo2) return;

    if (pathData.length === 0 || !pathData[1]) return;

    const secondLevelId = pathData[1].id;
    let currentData = data; // 初始为顶级节点数组
    let secondLevelNode = null;

    for (const topLevelNode of currentData) {
      if (topLevelNode.children) {
        const foundNode = topLevelNode.children.find((child) => child.id === secondLevelId);
        if (foundNode) {
          secondLevelNode = foundNode; // 找到第二级节点
          break;
        }
      }
    }

    if (secondLevelNode) {
      autoSelectRef.current = false;
      setSelectedKeys([secondLevelNode.key]);
      onPathChange?.(getPath(secondLevelNode.id), 'update');
    }
  };

  const onAddSpace = useCallbackRef((node: SpaceTreeDataType) => {
    openOverlay({
      Overlay: SpaceModal,
      props: {
        parentId: node.id,
        onSuccess: () => {
          infoMapRef.current = undefined;
          setTimeout(() => {
            refreshTree(node.id);
            onPathChange?.(...[getPath(node.id)], 'select');
          }, 1000);
        }
      }
    });
  });

  const onGroupSpace = useCallbackRef((node: SpaceTreeDataType) => {
    openOverlay({
      Overlay: GroupModal,
      props: {
        groupData: {
          id: node.id,
          name: node.title
        },
        onSuccess: () => {
          infoMapRef.current = undefined;
          setTimeout(() => {
            refreshTree(node.id);
            onPathChange?.(...[getPath(node.id)], 'select');
          }, 1000);
        }
      }
    });
  });

  const onInfoSpace = useCallbackRef((node: SpaceTreeDataType) => {
    openOverlay({
      Overlay: InfoSpaceModal,
      props: {
        space: node.space,
        EditSpaceSuccess: () => {
          infoMapRef.current = undefined;
          setTimeout(() => {
            refreshTree(node.parent?.id || '0');
            onPathChange?.(...[getPath(node.parent?.id || '0')], 'select');
          }, 1000);
        },
        onClose: () => {}
      }
    });
  });

  const onSettingsSpace = useCallbackRef((node: SpaceTreeDataType) => {
    openOverlay({
      Overlay: SettingModal,
      props: {
        settingData: {
          id: node.id,
          name: node.title
        },
        space: node.space,
        EditSpaceSuccess: () => {
          infoMapRef.current = undefined;
          setTimeout(() => {
            refreshTree(node.parent?.id);
            onPathChange?.(...[getPath(node.parent?.id || '0')], 'select');
          }, 1000);
        },
        onClose: () => {},
        onNavChange
      }
    });
  });

  const onRemoveRef = useRef(onRemove);
  onRemoveRef.current = onRemove;

  // 只执行一遍,代替lodaData,因为lodaData会在每个节点首次展开时执行,和onExpand重复了
  const isFirstLoad = useRef(true);
  useEffect(() => {
    if (isFirstLoad.current && treeData.length > 0) {
      isFirstLoad.current = false;
      refreshTree(treeData[0].id);
    }
  }, [treeData]);

  // 每次展开时都获取节点最新数据,避免因拖拽而丢失节点数据
  const onExpand = (expandedKeys: Key[], { expanded, node }: any) => {
    setExpandedKeys(expandedKeys);
    if (expanded) {
      refreshTree(node.id);
    }
  };

  const getLevelMap = async () => {
    const list = await getSpaceTree();
    const map: Record<string, InfoType> = {};
    const tr = (list: SpaceType[], level: number): number => {
      let maxLeafLevel = 0;
      list.forEach((it) => {
        const leafLevel = it.children?.length ? tr(it.children, level + 1) : level;
        if (leafLevel > maxLeafLevel) {
          maxLeafLevel = leafLevel;
        }
        const info: InfoType = {
          selfLevel: level,
          leafLevel: leafLevel,
          bottomLevel: leafLevel - level
        };
        map[it.id] = info;
      });
      return maxLeafLevel;
    };
    tr(list, 0);
    return map;
  };

  const allowDrop: TreeProps['allowDrop'] = ({ dropNode, dropPosition }) => {
    // 一般情况下
    // 当 dropPosition === 0 时：表示将节点拖放到目标节点内部（成为其子节点）
    // 当 dropPosition === -1 时：表示将节点拖放到目标节点之前（成为兄弟节点）
    // 当 dropPosition === 1 时：表示将节点拖放到目标节点之后（成为兄弟节点）
    // antd tree组件onDrop事件中会强制内置如下语句计算dropPosition,所以有可能也会出现其他值的情况
    // const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const privilege =
      dropPosition === 0
        ? (dropNode as SpaceTreeDataType).privilege
        : (dropNode as SpaceTreeDataType).parent?.privilege;
    return (
      (dropNode as SpaceTreeDataType)?.space?.privileges?.length === 0 ||
      (privilege !== undefined && privilege >= PrivilegeEnum.Manage)
    );
  };

  // 添加一个状态来控制拖拽模式
  const [dragMode, setDragMode] = useState<'sort' | 'nest' | null>(null);
  const [dragOverNode, setDragOverNode] = useState<EventDataNode<SpaceTreeDataType> | null>(null);
  const [dragOverPosition, setDragOverPosition] = useState<number>(0);
  // 修改onDragStart函数，增加拖拽模式初始化
  const onDragStart: TreeProps['onDragStart'] = () => {
    if (!infoMapRef.current) {
      getLevelMap().then((res) => (infoMapRef.current = res));
    }
    setDragMode(null); // 重置拖拽模式
  };

  // 添加onDragOver函数，用于实时更新拖拽模式
  const onDragOver: TreeProps['onDragOver'] = (info) => {
    setDragOverNode(info.node as EventDataNode<SpaceTreeDataType>);
    // 获取鼠标当前Y坐标
    const mouseY = info.event.clientY;

    // 查找真正的树节点元素，而不是依赖event.target
    const targetElement = info.event.target as HTMLElement;
    const treeNodeElement = targetElement.closest('.ant-tree-treenode') as HTMLElement;

    if (!treeNodeElement) {
      return;
    }

    const nodeRect = treeNodeElement.getBoundingClientRect();
    const nodeHeight = nodeRect.height;
    const offsetY = mouseY - nodeRect.top;
    // 调整判断区域：上1/3为上方插入，下1/3为下方插入，中间1/3为嵌套
    if (offsetY < nodeHeight / 3) {
      // 上方1/3区域 - 在节点前插入
      setDragMode('sort');
      // 注意: 虽说只是设置拖拽时的图标,但设置为none时会无法拖动成功(因拖拽操作会被取消,底层的html5设计如此)
      info.event.dataTransfer.dropEffect = 'move';
      (info as any).dropPosition = -1;
    } else if (offsetY > (nodeHeight * 2) / 3) {
      // 下方1/3区域 - 在节点后插入
      setDragMode('sort');
      info.event.dataTransfer.dropEffect = 'move';
      (info as any).dropPosition = 1;
    } else {
      // 中间区域 - 嵌套到节点内
      setDragMode('nest');
      info.event.dataTransfer.dropEffect = 'copy';
      (info as any).dropPosition = 0;
    }
    setDragOverPosition((info as any).dropPosition);
  };

  // 一般情况下
  // 当 dropPosition === 0 时：表示将节点拖放到目标节点内部（成为其子节点）
  // 当 dropPosition === -1 时：表示将节点拖放到目标节点之前（成为兄弟节点）
  // 当 dropPosition === 1 时：表示将节点拖放到目标节点之后（成为兄弟节点）
  // antd tree组件onDrop事件中会强制内置如下语句计算dropPosition,所以有可能也会出现其他值的情况
  // const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
  // onDrop的dropPosition不准,落点在同级中的第一位时会被判定为父元素所在同级元素中的index,dragOverPosition是自己写的,能准确判定上中下区域
  const onDrop = async ({
    // 被拖拽元素
    dragNode,
    dropPosition,
    dropToGap,
    // 落点元素
    node
  }: {
    dragNode: EventDataNode<SpaceTreeDataType>;
    dropPosition: number;
    dropToGap: boolean;
    node: EventDataNode<SpaceTreeDataType>;
  }) => {
    if (!infoMapRef.current) {
      infoMapRef.current = await getLevelMap();
    }

    // 获取目标节点的父节点
    // const newParent = dropToGap ? node.parent?.space || node.space : node.space;
    const newParent = dragMode === 'sort' ? node.parent?.space || node.space : node.space;

    // 当前被拖拽的父节点
    const oldParent = dragNode.parent?.space;
    if (!newParent) {
      return;
    }

    const isNestOperation = dragMode === 'nest';
    let topLevel;
    // 移动到节点内部
    if (isNestOperation) {
      // 嵌套操作：使用目标节点的层级
      topLevel = infoMapRef.current[node.key]?.selfLevel;
      // 拖拽排序
    } else {
      // 排序操作：使用新父节点的层级
      // 如果新父节点是根节点，层级为0
      topLevel =
        Number(newParent?.parentId) === 0 ? 0 : infoMapRef.current[newParent.id]?.selfLevel;
    }

    const bottomLevel = infoMapRef.current[dragNode.key]?.bottomLevel;

    // 如果是同级排序（新旧父节点相同），则不需要检查层级
    const isSameLevelSort =
      !isNestOperation &&
      // 同一父节点下的排序
      (oldParent?.id === newParent.id ||
        // 或者都是顶级节点
        ((!oldParent || Number(oldParent.parentId) === 0) &&
          (!newParent || Number(newParent.parentId) === 0)));

    if (
      !isSameLevelSort &&
      bottomLevel !== undefined &&
      topLevel !== undefined &&
      topLevel + bottomLevel + 1 > maxLevel
    ) {
      Toast.error('最多支持' + maxLevel + '级目录');
      return;
    }

    // 移动到节点内部
    if (isNestOperation) {
      MessageBox.confirm({
        title: '移动空间',
        content: `确定要移动空间"${dragNode.space.spaceName}"到"${newParent.spaceName}"吗？`,
        maskClosable: true,
        onOk: async () => {
          infoMapRef.current = undefined;

          await updateSpaceParent({ id: dragNode.id, parentId: newParent.id });
          await refreshTree(oldParent?.id);
          await refreshTree(node.space.id);
          refreshTree(dragNode.id);

          // 获取被拖拽节点下的所有子节点
          const childrenKeys =
            dragNode.children?.filter((it) => it.isLeaf === false).map((it) => it.key as string) ||
            [];
          // 收起这些子节点
          setExpandedKeys(expandedKeys.filter((item) => !childrenKeys.includes(item as string)));
        }
      });
      return;
    }

    // 落点元素的父节点
    // onDrop的dropPosition不准,落点在同级中的第一位时会被判定为父元素所在同级元素中的index,dragOverPosition是自己写的,能准确判定上中下区域
    const parentSpace = dragOverNode?.parent?.space;

    const isTopLevelSort = !parentSpace;
    const spaceChildren = isTopLevelSort
      ? treeData.map((item) => item.space)
      : parentSpace.children || [];

    if (!spaceChildren.length) {
      return;
    }
    // 从子级往外移动到顶级目录下
    if (oldParent && isTopLevelSort) {
      MessageBox.confirm({
        title: '移动空间',
        content: `确定要移动空间"${dragNode.space.spaceName}"到顶级目录下吗？`,
        maskClosable: true,
        onOk: async () => {
          infoMapRef.current = undefined;

          // 更新父节点为顶级目录（parentId为0）
          await updateSpaceParent({ id: dragNode.id, parentId: '0' });

          // 获取目标位置
          const dropPos = dragOverNode?.pos?.split('-');
          const dropIndex = Number(dropPos?.[dropPos?.length - 1]);

          // 准备排序数据
          const topLevelSpaces = treeData.map((item) => item.space);
          const sortData = topLevelSpaces.map((it, index) => ({
            id: it.id,
            sortNo: index + 1
          }));

          // 在目标位置插入被拖拽元素
          let targetIndex;
          if (dragOverPosition === -1) {
            // 插入到目标节点之前
            targetIndex = dropIndex;
          } else if (dragOverPosition === 1) {
            // 插入到目标节点之后
            targetIndex = dropIndex + 1;
          } else {
            // 兜底处理
            targetIndex = dropPosition < 0 ? dropIndex - 1 : dropPosition;
          }

          // 确保目标索引不超出数组范围
          targetIndex = Math.max(0, Math.min(targetIndex, sortData.length));

          // 插入拖拽节点
          sortData.splice(targetIndex, 0, {
            id: dragNode.id,
            sortNo: targetIndex + 1
          });

          // 更新排序号
          sortData.forEach((it, index) => {
            it.sortNo = index + 1;
          });

          await sortSpace(sortData);

          // 刷新树
          await refreshTree(oldParent?.id);
          await refreshTree();
          refreshTree(dragNode.id);

          // 获取被拖拽节点下的所有子节点
          const childrenKeys =
            dragNode.children?.filter((it) => it.isLeaf === false).map((it) => it.key as string) ||
            [];
          // 收起这些子节点
          setExpandedKeys(expandedKeys.filter((item) => !childrenKeys.includes(item as string)));
        }
      });
      // 如果是拖拽到不同的父节点
      // 如果落点元素的父节点和被拖拽元素的父节点不同,则需要确认移动
    } else if (!isTopLevelSort && oldParent?.id !== parentSpace?.id) {
      MessageBox.confirm({
        title: '移动空间',
        content: `确定要移动空间"${dragNode.space.spaceName}"到"${parentSpace.spaceName}"吗？`,
        maskClosable: true,
        onOk: async () => {
          infoMapRef.current = undefined;

          // 先更新父节点
          await updateSpaceParent({ id: dragNode.id, parentId: parentSpace.id });

          // 获取目标位置
          // onDrop的dropPosition不准,落点在同级中的第一位时会被判定为父元素所在同级元素中的index,dragOverPosition是自己写的,能准确判定上中下区域
          // 在拖拽到不同的父节点的情况下,使用dragOverNode?.pos?.split('-')是准确的,不需要通过dragOverPosition判断
          const dropPos = dragOverNode?.pos?.split('-');
          const dropIndex = Number(dropPos[dropPos.length - 1]);

          // 准备排序数据
          const children = [...(parentSpace.children || [])];
          // 添加拖拽的节点到新的位置
          const insertIndex = dropIndex;

          // 构建排序数据
          const sortData = children.map((it, index) => ({
            id: it.id,
            sortNo: index + 1
          }));

          // 插入拖拽节点
          sortData.splice(insertIndex, 0, {
            id: dragNode.id,
            sortNo: insertIndex + 1
          });

          // 更新排序号
          sortData.forEach((it, index) => {
            it.sortNo = index + 1;
          });

          await sortSpace(sortData);

          // 刷新树
          await refreshTree(oldParent?.id);
          await refreshTree(parentSpace.id);
          refreshTree(dragNode.id);

          // 获取被拖拽节点下的所有子节点
          const childrenKeys = dragNode.children
            ?.filter((it) => it.isLeaf === false)
            .map((it) => it.key as string) || [''];
          // 收起这些子节点
          setExpandedKeys(expandedKeys.filter((item) => !childrenKeys.includes(item as string)));
        }
      });
    } else {
      // 同级排序
      // 获取当前被拖拽的元素在同级中的排序
      const dragIndex = spaceChildren.findIndex((item) => item.id === dragNode.space.id);

      // 获取目标位置
      // onDrop的dropPosition不准,落点在同级中的第一位时会被判定为父元素所在同级元素中的index,dragOverPosition是自己写的,能准确判定上中下区域
      const dropPos = dragOverNode?.pos?.split('-');
      const dropIndex = Number(dropPos?.[dropPos?.length - 1]);

      // 准备排序数据
      const sortData = spaceChildren.map((it, index) => ({
        id: it.id,
        sortNo: index + 1
      }));

      // 删除被拖拽元素后,取到被拖拽元素
      const dragItem = sortData.splice(dragIndex, 1)[0];

      // 使用更准确的 dragOverPosition 来确定目标位置
      let targetIndex;
      if (dragOverPosition === -1) {
        // 插入到目标节点之前
        targetIndex = dropIndex;
        // 如果拖拽元素在目标元素之前，则目标索引需要减1（因为已经移除了拖拽元素）
        if (dragIndex < dropIndex) {
          targetIndex--;
        }
      } else if (dragOverPosition === 1) {
        // 插入到目标节点之后
        targetIndex = dropIndex + 1;
        // 如果拖拽元素在目标元素之前，则目标索引需要减1（因为已经移除了拖拽元素）
        if (dragIndex < dropIndex) {
          targetIndex--;
        }
      } else {
        // 兜底处理，使用原来的逻辑
        targetIndex = dropPosition < 0 ? dropIndex - 1 : dropPosition;
      }

      // 确保目标索引不超出数组范围
      targetIndex = Math.max(0, Math.min(targetIndex, sortData.length));

      // 在目标位置插入被拖拽元素
      sortData.splice(targetIndex, 0, dragItem);

      // 更新排序号
      sortData.forEach((it, index) => {
        it.sortNo = index + 1;
      });
      await sortSpace(sortData);
      await refreshTree(isTopLevelSort ? undefined : parentSpace.id);
    }
  };

  useImperativeHandle(ref, () => ({
    refreshTree
  }));

  useEffect(() => {
    if (
      prevFolderPathDataRef.current !== folderPathData &&
      folderPathData &&
      folderPathData.length > 0
    ) {
      setTimeout(() => {
        findAndSelectNode(treeData, folderPathData!);
        prevFolderPathDataRef.current = folderPathData;
      }, 600);
    }
  }, [treeData, folderPathData, onPathChange, getPath]);

  const refreshTreeHandler = useCallback(
    ({ parentId }: { parentId?: string }) => {
      refreshTree(parentId);
    },
    [refreshTree]
  );

  useEffect(() => {
    eventBus.on(EventNameEnum.refreshTree, refreshTreeHandler);
    return () => {
      eventBus.off(EventNameEnum.refreshTree);
    };
  }, [refreshTreeHandler]);

  useEffect(() => {
    !path?.length && setSelectedKeys([]);
  }, [path]);

  const draggableConfig =
    access.mode === AccessModeEnum.Manage
      ? {
          icon: (
            <SvgIcon
              name="menu2"
              color="#D5D5D5"
              pos="absolute"
              left="4px"
              top="0"
              bottom="0"
              my="auto"
            />
          ),
          nodeDraggable: (node: DataNode) => {
            const { level, privilege, space } = node as SpaceTreeDataType;
            if (level === 0) {
              return space?.privileges?.length === 0 || privilege >= PrivilegeEnum.Owner;
            } else {
              return space?.privileges?.length === 0 || privilege >= PrivilegeEnum.Manage;
            }
          }
        }
      : undefined;

  const TreeTitle = useCallback(
    ({ node }: { node: SpaceTreeDataType }) => {
      const { setNodeRef, isOver } = useDroppable({
        id: node.key,
        data: { type: 'space', space: node.space },
        disabled: access.mode !== AccessModeEnum.Manage || node.privilege < PrivilegeEnum.Owner
      });

      useEffect(() => {
        setDragOverKey((state) => (isOver ? node.key : state === node.key ? '' : state));
        return () => setDragOverKey((state) => (state === node.key ? '' : state));
      }, [isOver, node.key]);

      return (
        <Flex
          ref={setNodeRef}
          h="100%"
          py="10px"
          key={node.key + '_wrap'}
          alignItems="center"
          _hover={{
            '.space-menu-button': {
              visibility: 'visible'
            }
          }}
          {...(isOver && { color: 'red' })}
        >
          <SvgIcon
            key={node.key + '_icon'}
            {...(selectedKeys.includes(node.key)
              ? {
                  name: 'box2',
                  color: '#7D4DFF'
                }
              : {
                  name: 'box',
                  color: '#4E5969'
                })}
            w="16px"
            h="16px"
          />

          <Box
            ml="4px"
            flex="1"
            key={node.key + '_title'}
            maxW="9em"
            color={selectedKeys.includes(node.key) ? '#7D4DFF' : '#4E5969'}
            fontSize={respDims('14fpx')}
            fontWeight="500"
            overflow="hidden"
            textOverflow="ellipsis"
          >
            {node.title as React.ReactNode}
          </Box>

          {(node?.space?.privileges?.length === 0 ||
            (access.mode === AccessModeEnum.Manage && node.privilege >= PrivilegeEnum.Edit)) && (
            <Box
              className="space-menu-button"
              visibility="hidden"
              w="24px"
              h="24px"
              pos="absolute"
              top="0"
              right="0"
              bottom="0"
              my="auto"
              borderRadius="4px"
              bgColor={selectedKeys.includes(node.key) ? 'primary.50' : '#F8FAFC'}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
              }}
            >
              <MyMenu
                trigger="hover"
                isUsePortal
                zIndex={2001}
                offset={[-100, 4]}
                Button={
                  <Center
                    w="24px"
                    h="24px"
                    borderRadius="4px"
                    cursor="pointer"
                    _hover={{
                      bgColor: '#e5dbff'
                    }}
                  >
                    <SvgIcon position="relative" name="more" w="14px" h="14px" />
                  </Center>
                }
                menuList={[
                  ...(node.level < maxLevel
                    ? [
                        {
                          label: '添加空间',
                          icon: <SvgIcon name="plus" />,
                          onClick: () => onAddSpace(node)
                        }
                      ]
                    : []),
                  {
                    label: '空间信息',
                    icon: <SvgIcon name="cloudInfoSpace" />,
                    onClick: () => onInfoSpace(node)
                  },
                  // 只有管理员或者拥有者才显示"成员及权限"菜单项
                  ...(node?.space?.privileges?.length === 0 ||
                  node?.space?.privileges[0] >= PrivilegeEnum.Manage
                    ? [
                        {
                          label: '成员及权限',
                          icon: <SvgIcon name="group" />,
                          onClick: () => onGroupSpace(node)
                        }
                      ]
                    : []),
                  // 只有管理员或者拥有者才显示"空间设置"菜单项
                  ...(node?.space?.privileges?.length === 0 ||
                  node?.space?.privileges[0] >= PrivilegeEnum.Manage
                    ? [
                        {
                          label: '空间设置',
                          icon: <SvgIcon name="settings" />,
                          onClick: () => onSettingsSpace(node)
                        }
                      ]
                    : [])
                ]}
              />
            </Box>
          )}
        </Flex>
      );
    },
    [onAddSpace, onGroupSpace, onSettingsSpace, selectedKeys, access]
  );

  const renderTreeNodes = (data: SpaceTreeDataType[]) =>
    data.map((item) => {
      return (
        <Tree.TreeNode
          {...item}
          title={<TreeTitle node={item} />}
          key={item.key}
          className={item.key === dragOverKey ? 'drag-over' : ''}
        >
          {!!item.children?.length && renderTreeNodes(item.children)}
        </Tree.TreeNode>
      );
    });

  return (
    <>
      {treeData.length > 0 && (
        <Tree<SpaceTreeDataType>
          expandedKeys={expandedKeys}
          onExpand={onExpand}
          draggable={draggableConfig}
          className={`${styles['space-tree']}`}
          defaultExpandedKeys={[treeData[0].key]}
          selectedKeys={selectedKeys}
          blockNode
          onDragStart={onDragStart}
          onDragOver={onDragOver}
          allowDrop={allowDrop}
          onDrop={onDrop}
          onSelect={(selectedKeys, info) => {
            if (info.selected) {
              setSelectedKeys(selectedKeys as string[]);
              onPathChange?.(getPath(selectedKeys[0] as string), 'select');
            }
          }}
        >
          {renderTreeNodes(treeData)}
        </Tree>
      )}
    </>
  );
};

export default forwardRef(SpaceTree);
