import { ChakraProps, Flex, Box } from '@chakra-ui/react';
import NavItem, { NavItemType } from './NavItem';
import SpaceTree, { SpaceTreeRef } from '../SpaceTree';
import { AccessType, NavType } from '@/types/cloud';
import { locationPathType, SpaceType } from '@/types/api/cloud';
import { NavTypeEnum, navPersonal, navRecycle } from '@/constants/cloud';
import { useEffect, useRef, useState, useMemo, MutableRefObject } from 'react';
import { AccessModeEnum } from '@/constants/api/cloud';
import { useTenantStore } from '@/store/useTenantStore';
import SvgIcon from '@/components/SvgIcon';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import SpaceModal from '../../Tenant/SpaceModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { UserRoleEnum } from '@/constants/api/auth';
import { useUserStore } from '@/store/useUserStore';
const NavList = ({
  access,
  nav,
  folderPathData,
  spaceTreeRef,
  onNavChange,
  onSpaceRemove,
  ...props
}: {
  access: AccessType;
  nav?: NavType;
  folderPathData?: locationPathType[];
  spaceTreeRef?: MutableRefObject<SpaceTreeRef | null>;
  onNavChange?: (nav?: NavType) => void;
  onSpaceRemove?: (space: SpaceType) => void;
} & ChakraProps) => {
  const [expandedIndex, setExpandedIndex] = useState<number[]>([0]);
  const { openOverlay } = useOverlayManager();
  const { userInfo } = useUserStore();
  const hasInitialized = useRef(false);
  const isAdmin = userInfo?.roleType === UserRoleEnum.Admin;
  const isSpaceAdmin = userInfo?.roleType === UserRoleEnum.SpaceAdmin;
  const { industryAlias } = useTenantStore();
  const [selectedIndex, setSelectedIndex] = useState<number | null>(0);
  const onAddSpace = useCallbackRef(() => {
    openOverlay({
      Overlay: SpaceModal,
      props: {
        parentId: '0',
        onSuccess: () => {
          spaceTreeRef?.current?.refreshTree('0');
        }
      }
    });
  });

  useEffect(() => {
    if (nav?.type === NavTypeEnum.personal) {
      setSelectedIndex(1);
    }
  }, [nav]);

  const handlePrimarySpaceClick = async () => {
    try {
      setSelectedIndex((prevIndex) => (prevIndex === 0 ? -1 : 0));
      onNavChange?.({ type: NavTypeEnum.tenant, path: [] });
    } catch (error) {}
  };

  useEffect(() => {
    handlePrimarySpaceClick();
  }, []);

  useEffect(() => {
    // 确保在组件加载时选中团队数据空间
    if (nav?.type === NavTypeEnum.tenant) {
      setSelectedIndex(0);
    }
  }, [nav]);

  const navList = useMemo(() => {
    const list: NavItemType[] = [
      {
        label: (
          <Flex
            justifyContent="space-between"
            alignItems="center"
            onClick={handlePrimarySpaceClick}
          >
            <Box whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
              {industryAlias}数据空间
            </Box>
            {(isAdmin || isSpaceAdmin) && (
              <SvgIcon
                p="3px"
                w="23px"
                h="22px"
                bgColor="#F8FAFC"
                borderRadius="4px"
                color="#4E5969"
                onClick={(e) => {
                  e.stopPropagation();
                  onAddSpace();
                }}
                name="plus"
                _hover={{
                  bgColor: '#e5dbff'
                }}
              />
            )}
          </Flex>
        ),
        icon: 'navSchoolLine',
        activeIcon: 'navSchoolLine',
        content: (
          <SpaceTree
            ref={spaceTreeRef}
            access={access}
            folderPathData={folderPathData}
            path={nav?.type === NavTypeEnum.tenant ? nav.path : undefined}
            onPathChange={(path) => {
              onNavChange?.({ type: NavTypeEnum.tenant, path });
            }}
            onNavChange={onNavChange}
            onRemove={onSpaceRemove}
          />
        )
      }
    ];

    if (access.mode === AccessModeEnum.View || access.mode === AccessModeEnum.Manage) {
      list.push({
        label: '我的数据空间',
        icon: 'user',
        activeIcon: 'user',
        nav: navPersonal
      });
    }

    if (access.mode === AccessModeEnum.Manage) {
      list.push({
        label: '回收站',
        icon: 'package',
        activeIcon: 'package',
        nav: navRecycle
      });
    }

    return list;
  }, [
    access,
    industryAlias,
    isAdmin,
    isSpaceAdmin,
    nav,
    onAddSpace,
    onNavChange,
    onSpaceRemove,
    spaceTreeRef,
    folderPathData
  ]);

  useEffect(() => {
    if (
      !hasInitialized.current &&
      (access.mode !== AccessModeEnum.Manage || (!nav && access.mode === AccessModeEnum.Manage))
    ) {
      onNavChange?.(navPersonal);
      hasInitialized.current = true;
    }
  }, [nav, onNavChange, access.mode]);

  return (
    <Flex flexDir="column" overflowY="scroll" overflowX="hidden" {...props}>
      {navList.map((item, index) => (
        <NavItem
          key={index}
          {...item}
          isActive={selectedIndex === index}
          isExpanded={expandedIndex.includes(index)}
          content={item.content}
          onClick={() => {
            item.nav && onNavChange?.(item.nav);
            setSelectedIndex(index);
          }}
          onToggleExpand={() => {
            setExpandedIndex((prevExpanded) =>
              prevExpanded.includes(index)
                ? prevExpanded.filter((i) => i !== index)
                : [...prevExpanded, index]
            );
          }}
        />
      ))}
    </Flex>
  );
};

export default NavList;
