import React, {
  useRef,
  ForwardedRef,
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback
} from 'react';
import { Box, ChakraProps, Flex, useToken } from '@chakra-ui/react';
import { AccessType, NavType } from '@/types/cloud';
import { respDims, rpxDim } from '@/utils/chakra';
import { locationPathType, SpaceType } from '@/types/api/cloud';
import { Progress } from 'antd';
import { useCloudStore } from '@/store/useCloudStore';
import { useQuery } from '@tanstack/react-query';
import { formatFileSize } from '@/utils/tools';
import { SpaceTreeRef } from './SpaceTree';
import NavList from './NavList';

interface SidebarProps extends ChakraProps {
  access: AccessType;

  nav?: NavType;
  folderPathData?: locationPathType[];

  onNavChange?: (nav?: NavType) => void;

  onSpaceRemove?: (space: SpaceType) => void;
  defaultWidth?: number;
  minWidth?: number;
  maxWidth?: number;
}

export interface SidebarRef {
  refreshSpaceTree: (parentSpaceId?: string) => void;
  isDragging?: boolean;
}

const Sidebar = (
  {
    access,
    nav,
    folderPathData,
    onNavChange,
    onSpaceRemove,
    defaultWidth = 247,
    minWidth = 205,
    maxWidth = 370,
    ...props
  }: SidebarProps,
  ref: ForwardedRef<SidebarRef>
) => {
  const spaceTreeRef = useRef<SpaceTreeRef>(null);

  const { usageStats, loadUsageStats } = useCloudStore();
  const [width, setWidth] = useState(defaultWidth);
  const [isDragging, setIsDragging] = useState(false);
  const dragStartX = useRef(0);
  const dragStartWidth = useRef(0);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      setIsDragging(true);
      dragStartX.current = e.clientX;
      dragStartWidth.current = width;
      e.preventDefault();
    },
    [width]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;
      setIsDragging(true);

      const deltaX = e.clientX - dragStartX.current;
      const newWidth = Math.min(Math.max(dragStartWidth.current + deltaX, minWidth), maxWidth);
      setWidth(newWidth);
    },
    [isDragging, minWidth, maxWidth]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  useImperativeHandle(ref, () => ({
    refreshSpaceTree: (parentId?: string, searchKey?: string) =>
      spaceTreeRef.current?.refreshTree(parentId, '123') || Promise.reject(),
    isDragging
  }));

  useQuery(['useageStats'], () => loadUsageStats(true));
  return (
    <Flex
      flexDir="column"
      {...props}
      w={`${width}px`}
      position="relative"
      transition={isDragging ? 'none' : 'width 0.1s ease'}
      userSelect={isDragging ? 'none' : 'auto'}
      flexShrink="0"
    >
      <Box
        pl={rpxDim(21)}
        pb={rpxDim(22)}
        pt={rpxDim(28)}
        color="#303133"
        fontSize={rpxDim(22)}
        fontWeight="bold"
        lineHeight={respDims(23)}
        borderBottom="1px solid #E5E7EB"
      >
        <Box ml="5px">数据空间</Box>
        <Box w="114px" h="13px" bgColor="#ae99f8" mt={rpxDim(-7)}></Box>
      </Box>
      <Flex
        borderRadius="8px"
        bgColor="#fff"
        flexDirection="column"
        overflow="hidden auto"
        flex="1"
        mb={rpxDim(16)}
      >
        <NavList
          flex="1"
          pt={respDims(10)}
          pl={respDims(16)}
          pr="3px"
          access={access}
          nav={nav}
          folderPathData={folderPathData}
          spaceTreeRef={spaceTreeRef}
          onNavChange={onNavChange}
          onSpaceRemove={onSpaceRemove}
          border="none"
        />

        {!!usageStats?.total && (
          <Box bgColor="#fff">
            <Progress
              strokeColor="#7d4dff"
              percent={(usageStats.used / usageStats.total) * 100}
              showInfo={false}
              strokeWidth={8}
              style={{ height: '8px', paddingLeft: '11px', paddingRight: '29px' }}
            />
            <Box
              ml={rpxDim(11)}
              mt={respDims(5)}
              mb={respDims(16)}
              color="rgba(0,0,0,0.6)"
              fontSize={respDims('12fpx')}
              lineHeight={respDims('22fpx')}
            >
              {`${formatFileSize(usageStats.used, true)} / ${formatFileSize(usageStats.total, true)}`}
            </Box>
          </Box>
        )}
      </Flex>
      <Box
        position="absolute"
        right="-10px"
        top="50%"
        transform="translateY(-50%)"
        bottom="0"
        width="10px"
        h="calc(100% - 16px - 22px)"
        cursor="col-resize"
        onMouseDown={handleMouseDown}
      >
        <Box
          position="absolute"
          right="4px"
          top="0"
          bottom="0"
          width="2px"
          cursor="col-resize"
          _hover={{ bg: 'rgba(0,0,0,0.1)' }}
          onMouseDown={handleMouseDown}
          style={{
            backgroundColor: isDragging ? '#05f' : 'transparent'
          }}
        ></Box>
      </Box>

      {isDragging && (
        <Box
          position="fixed"
          top="0"
          right="0"
          bottom="0"
          left="0"
          cursor="col-resize"
          zIndex={9999}
        />
      )}
    </Flex>
  );
};

export default forwardRef(Sidebar);
