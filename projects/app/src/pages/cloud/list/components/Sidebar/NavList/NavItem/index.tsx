import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { NavType } from '@/types/cloud';
import { respDims } from '@/utils/chakra';
import { CaretDownOutlined } from '@ant-design/icons';
import { Box, Center, Flex } from '@chakra-ui/react';
import { ReactNode } from 'react';

export type NavItemType = {
  label: React.ReactNode;
  icon: SvgIconNameType;
  activeIcon: SvgIconNameType;
  nav?: NavType;
  content?: ReactNode;
};

const NavItem = ({
  label,
  icon,
  activeIcon,
  isActive,
  isExpanded,
  content,
  onClick,
  onToggleExpand
}: {
  isActive?: boolean;
  isExpanded?: boolean;
  onClick?: () => void;
  onToggleExpand?: () => void;
} & NavItemType) => {
  return (
    <>
      <Flex
        mb="4px"
        key={icon}
        p="11px 8px"
        alignItems="center"
        {...(isActive
          ? {
              color: 'primary.500',
              bgColor: '#f2f3ff'
            }
          : {
              color: '#0a0a0a',
              _hover: {
                bgColor: '#f3f4f6'
              }
            })}
        fontSize={respDims('16fpx')}
        fontWeight="500"
        borderRadius="8px"
        cursor="pointer"
        pos="relative"
        userSelect="none"
        onClick={onClick}
      >
        <Center
          w="16px"
          h="16px"
          onClick={(e) => {
            e.stopPropagation();
            onToggleExpand?.();
          }}
        >
          {content && (
            <CaretDownOutlined
              style={{
                width: '10px',
                height: '10px',
                transform: `rotate(${isExpanded ? '0deg' : '-90deg'})`,
                transition: '0.3s ease-in-out'
              }}
            />
          )}
        </Center>

        <SvgIcon
          ml="4px"
          {...(isActive
            ? { name: activeIcon, color: '#33366ff' }
            : { name: icon, color: '#4E5969' })}
          w="20px"
          h="20px"
        />
        <Box ml="8px" overflow="hidden" flex="1">
          {label}
        </Box>
      </Flex>

      {content && <Box display={isExpanded ? 'block' : 'none'}>{content}</Box>}
    </>
  );
};

export default NavItem;
