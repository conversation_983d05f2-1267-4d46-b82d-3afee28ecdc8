import { Box, Flex } from '@chakra-ui/react';
import Sidebar, { SidebarRef } from './components/Sidebar';
import { useState, useRef, useCallback, useEffect } from 'react';
import { respDims, rpxDim } from '@/utils/chakra';
import Tenant from './components/Tenant';
import Personal from './components/Personal';
import FileInfo from '../components/FileInfo';
import Recycle from './components/Recycle';
import { useCloudStore } from '@/store/useCloudStore';
import { NavType } from '@/types/cloud';
import { NavTypeEnum } from '@/constants/cloud';
import DndWrapper from '../components/DndWrapper';
import { serviceSideProps } from '@/utils/i18n';
import { AccessModeEnum } from '@/constants/api/cloud';
import { locationPathType } from '@/types/api/cloud';
import { useRouter } from 'next/router';
import useFilePreview from '@/hooks/useFilePreview';
import { useContextSelector } from 'use-context-selector';
import { AppContext } from '@/components/AppDetail/components/context';
import { useTenantStore } from '@/store/useTenantStore';

interface RouterQuery {
  action: string;
  fileId: string;
  fileType: number;
  fileUrl: string;
  searchContent: string;
  fileKey: string;
}

const CloudList = () => {
  const [nav, setNav] = useState<NavType>();

  const [folderPathData, setFolderPathData] = useState<locationPathType[]>([]);

  const [showFileDynamics, setShowFileDynamics] = useState(false);

  const { selectFile, setSelectFile, showFileInfo, setShowFileInfo } = useCloudStore(); // 使用 store

  const sidebarRef = useRef<SidebarRef>(null);

  const recycleRef = useRef<{ refresh: () => void }>(null);

  const router = useRouter();

  const { appForm } = useContextSelector(AppContext, (v) => v);

  const { previewFile } = useFilePreview();

  const { tenant } = useTenantStore();

  const [previewParams, setPreviewParams] = useState<RouterQuery | null>(null);

  const handleNavChange = (nav: NavType) => {
    setNav(nav);
    setShowFileInfo(false);
    setShowFileDynamics(false);
    setSelectFile(null);
  };

  const handleToggleFileInfo = useCallback(() => {
    setShowFileInfo(!showFileInfo);
  }, [showFileInfo]);

  const handleToggleFileDynamics = () => {
    setShowFileDynamics((prev) => !prev);
  };

  const onSpaceRemove = () => {
    recycleRef.current?.refresh();
  };

  const refreshTreeRef = (parentSpaceId?: string) => {
    sidebarRef.current?.refreshSpaceTree(parentSpaceId);
  };

  useEffect(() => {
    const { action, fileId, fileType, fileUrl, searchContent, fileKey } =
      router.query as unknown as RouterQuery;

    if (action === 'preview') {
      setPreviewParams({
        action,
        fileId,
        fileType,
        fileUrl,
        searchContent,
        fileKey
      });

      router.replace(router.pathname, undefined, { shallow: true });

      setTimeout(() => {
        previewFile({
          fileUrl: decodeURIComponent(fileUrl as string) || '',
          fileType: Number(fileType),
          fileId,
          searchContent,
          fileKey: fileKey
          // fileKey: historyItem.files.fileKey
        });
      }, 2000);
    }
    handleNavChange({ type: NavTypeEnum.tenant, path: [] });
  }, [router.query]);

  return (
    <Flex
      alignItems="stretch"
      w="100%"
      h="100%"
      borderRadius="0"
      backgroundImage={
        tenant?.functionBackgroundImgUrl
          ? tenant.functionBackgroundImgUrl
          : '/imgs/app/app_center_bg3.png'
      }
      backgroundRepeat="no-repeat"
      backgroundSize="cover"
      pr={rpxDim(16)}
    >
      <DndWrapper>
        <Flex
          flex="1"
          alignItems="stretch"
          h="100%"
          pt="0"
          ml={rpxDim(16)}
          // 避免上传文件时,当前上传个数显示不全以及出现下侧、右侧滚动条的问题,当前上传个数适配2位用7px,适配3位用11px,11px缺点是页面右侧间距较大
          pr="7px"
          overflowX="auto"
          css={{
            '&::-webkit-scrollbar': {
              height: '18px !important'
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#d8d8d8 !important'
            },
            '&::-webkit-scrollbar-thumb:hover': {
              backgroundColor: '#d8d8d8 !important'
            }
          }}
        >
          <Sidebar
            defaultWidth={247}
            minWidth={205}
            maxWidth={370}
            access={{ mode: AccessModeEnum.Manage }}
            nav={nav}
            folderPathData={folderPathData}
            w={respDims(250, 220)}
            onNavChange={(event) => {
              setNav(event);
              setSelectFile(null);
            }}
            onSpaceRemove={onSpaceRemove}
            h="100vh"
          />

          <Box flex="1" pr="0" mb={rpxDim(16)} ml={rpxDim(10)} mt={rpxDim(22)}>
            {/* 租户数据空间 */}
            {nav?.type === NavTypeEnum.tenant && (
              <Tenant
                value={appForm}
                onNavChange={(event) => {
                  setNav(event);
                  setSelectFile(null);
                }}
                nav={nav}
                onGetLocationPath={(e) => {
                  setFolderPathData(e);
                }}
              />
            )}
            {/* 个人数据空间 */}
            {nav?.type === NavTypeEnum.personal && (
              <Personal
                value={appForm}
                access={{ mode: AccessModeEnum.Manage }}
                onGetLocationPath={(e) => {
                  setFolderPathData(e);
                }}
              />
            )}
            {/* 回收站 */}
            {nav?.type === NavTypeEnum.recycle && (
              <Recycle nav={nav} ref={recycleRef} refreshTreeRef={refreshTreeRef} />
            )}
          </Box>
        </Flex>
      </DndWrapper>
      {showFileInfo && <FileInfo pageType={nav?.type} />}
      {/* {showFileDynamics && <FileDynamics id={selectFile?.id} />} */}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default CloudList;
