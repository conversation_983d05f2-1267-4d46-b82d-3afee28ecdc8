import { UploadStatusEnum } from '@/components/CloudProvider/type';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import { useEffect, useRef } from 'react';

export default function useUploadRefresh({
  parentId,
  onRefresh
}: {
  parentId?: string;
  onRefresh?: () => void;
}) {
  // useCloudUploader引起此组件及父级以上全部组件都会重复加载
  const { uploadFiles } = useCloudUploader();

  const parentIdRef = useRef(parentId);

  const localIdsRef = useRef<string[]>([]);

  const onChangeRef = useRef(onRefresh);

  onChangeRef.current = onRefresh;

  useEffect(() => {
    const localIds = uploadFiles
      .filter(
        (file) =>
          file.remoteParentId === parentId &&
          [UploadStatusEnum.Waiting, UploadStatusEnum.Uploading].includes(file.status)
      )
      .map((it) => it.localId);

    if (parentId !== parentIdRef.current) {
      parentIdRef.current = parentId;
      localIdsRef.current = localIds;
      return;
    }

    if (
      localIdsRef.current.some((localId) =>
        uploadFiles.some(
          (file) => file.localId === localId && file.status === UploadStatusEnum.Uploaded
        )
      )
    ) {
      onChangeRef.current?.();
    }

    localIdsRef.current = localIds;
  }, [parentId, uploadFiles]);
}
