import { getMyFilePage, getSpaceFilePage, getSpaceList } from '@/api/cloud';
import { useCallback, useEffect, useRef, useState } from 'react';
import { getListFromPage } from '@/utils/api';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { PathItemTypeEnum, myRootFolder, virtualRootId } from '@/constants/cloud';
import { PathItemType, PathType } from '@/types/cloud';

export type PathTreeDataType = {
  id: string;
  key: string;
  value: string;
  title: string;
  parent?: PathTreeDataType;
  children?: PathTreeDataType[];
  isLeaf?: boolean;
  pathItem: PathItemType;
};

type TreePathItemType = {
  id: string;
  children?: TreePathItemType[];
  treeNode?: PathTreeDataType;
} & PathItemType;

export default function usePathTree({
  bizType,
  valueKey,
  titleKey,
  autoInitRoot = true
}: {
  bizType: BizTypeEnum;
  valueKey?: string;
  titleKey?: string;
  autoInitRoot?: boolean;
}) {
  const bizTypeRef = useRef(bizType);

  const treeRef = useRef<TreePathItemType[]>([]);

  const leafRef = useRef<Record<string, boolean>>({});

  const mapRef = useRef<Record<string, TreePathItemType>>({});

  const [treeData, setTreeData] = useState<PathTreeDataType[]>([]);

  const updateTreeData = useCallback(() => {
    const map: Record<string, TreePathItemType> = {};
    const trMap = (list: TreePathItemType[]): PathTreeDataType[] =>
      list.map((it) => {
        const node: PathTreeDataType = {
          id: it.id,
          key: it.id,
          value: it.id,
          title: it.type === PathItemTypeEnum.file ? it.file.fileName : it.space.spaceName,
          isLeaf: !!leafRef.current[it.id],
          pathItem: it
        };

        if (valueKey) {
          (node as any)[valueKey] = node.value;
        }

        if (titleKey) {
          (node as any)[titleKey] = node.title;
        }

        if (it.children?.length) {
          node.children = trMap(it.children);
          node.children.forEach((it) => {
            it.parent = node;
          });
        }

        it.treeNode = node;
        map[it.id] = it;
        return node;
      });

    mapRef.current = map;
    setTreeData(trMap(treeRef.current));
  }, [valueKey, titleKey]);

  const getRootList = useCallback(() => {
    if (bizTypeRef.current === BizTypeEnum.TenantLibrary) {
      return getSpaceList({ parentId: virtualRootId || '0' }).then((res) => {
        treeRef.current = res.map((it) => ({
          id: it.id,
          type: PathItemTypeEnum.space,
          space: it
        }));
        updateTreeData();
      });
    }

    treeRef.current = [
      {
        id: virtualRootId,
        type: PathItemTypeEnum.file,
        file: myRootFolder
      }
    ];
    updateTreeData();
    return Promise.resolve();
  }, [updateTreeData]);

  const refreshTree = useCallback(
    (parentId?: string) => {
      if (
        !parentId ||
        (parentId === virtualRootId && bizTypeRef.current === BizTypeEnum.TenantLibrary)
      ) {
        if (treeRef.current.length) {
          return Promise.resolve();
        }
        return getRootList();
      }

      const parent = mapRef.current[parentId];
      if (!parent) {
        return Promise.reject();
      }

      if (parent.children?.length) {
        return Promise.resolve();
      }

      if (parent.type === PathItemTypeEnum.space) {
        console.log(parent.space, 'parent.space');

        return Promise.all([
          getSpaceList({ parentId: parent.space.id || '0', shareType: parent.space.shareType }),
          getListFromPage(getSpaceFilePage, {
            parentId: parent.space.id,
            shareType: parent.space.shareType,
            fileType: FileTypeEnum.Folder
          })
        ]).then(([spaces, files]) => {
          parent.children = [
            ...spaces.map(
              (it) =>
                ({
                  id: it.id,
                  type: PathItemTypeEnum.space,
                  space: it
                }) as TreePathItemType
            ),
            ...files
              .filter((it) => it.fileType === FileTypeEnum.Folder)
              .map(
                (it) =>
                  ({
                    id: it.id,
                    type: PathItemTypeEnum.file,
                    file: it
                  }) as TreePathItemType
              )
          ];
          leafRef.current[parent.id] = !parent.children?.length;

          updateTreeData();
        });
      }

      return getListFromPage(
        bizTypeRef.current === BizTypeEnum.TenantLibrary ? getSpaceFilePage : getMyFilePage,
        {
          parentId: parent.file.id,
          shareType: parent.file.shareType,
          fileType: FileTypeEnum.Folder
        }
      ).then((res) => {
        parent.children = res
          .filter((it) => it.fileType === FileTypeEnum.Folder)
          .map(
            (it) =>
              ({
                id: it.id,
                type: PathItemTypeEnum.file,
                file: it
              }) as TreePathItemType
          );
        leafRef.current[parent.id] = !parent.children?.length;
        updateTreeData();
      });
    },
    [updateTreeData, getRootList]
  );

  const getPath = useCallback((id: string) => {
    const path: PathType = [];
    let item = mapRef.current[id];
    while (item) {
      path.unshift(item);
      item =
        mapRef.current[
          item.type === PathItemTypeEnum.space ? item.space.parentId : item.file.parentId
        ];
    }
    return path;
  }, []);

  const getPathItem = useCallback((id: string) => mapRef.current[id], []);

  const getTreeNode = useCallback((id: string) => mapRef.current[id]?.treeNode, []);

  useEffect(() => {
    autoInitRoot && getRootList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    treeData,
    initRoot: getRootList,
    refreshTree,
    getTreeNode,
    getPath,
    getPathItem
  };
}
