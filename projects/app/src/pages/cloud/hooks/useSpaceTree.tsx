import { getSpace<PERSON>ist, getSpaceViewList } from '@/api/cloud';
import { UserRoleEnum } from '@/constants/api/auth';
import { AccessModeEnum, PrivilegeEnum } from '@/constants/api/cloud';
import { PathItemTypeEnum, virtualRootId } from '@/constants/cloud';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import { useUserStore } from '@/store/useUserStore';
import { GetSpaceListProps, SpaceType } from '@/types/api/cloud';
import { AccessType, SpacePathType } from '@/types/cloud';
import { useCallback, useEffect, useRef, useState } from 'react';

export type SpaceTreeDataType = {
  id: string;
  key: string;
  value: string;
  title: string;
  parent?: SpaceTreeDataType;
  isLeaf?: boolean;
  children?: SpaceTreeDataType[];
  space: SpaceType;
  level: number;
  privilege: PrivilegeEnum;
};

type TreeSpaceType = { children?: TreeSpaceType[] } & SpaceType;

export default function useSpaceTree({ access }: { access: AccessType }) {
  const treeRef = useRef<TreeSpaceType[]>([]);

  const mapRef = useRef<Record<string, TreeSpaceType>>({});

  const [treeData, setTreeData] = useState<SpaceTreeDataType[]>([]);

  const { userInfo } = useUserStore();

  const isAdmin = userInfo?.roleType === UserRoleEnum.Admin;

  const getSpaceListApi = useCallbackRef((data: GetSpaceListProps) =>
    access.mode === AccessModeEnum.Manage || access.mode === AccessModeEnum.View
      ? getSpaceList(data)
      : getSpaceViewList({
          ...data,
          isAdmin: access.mode === AccessModeEnum.ViewTenant ? 1 : 0,
          tenantId: userInfo?.tenantId!,
          tmbId: access.mode === AccessModeEnum.ViewMember ? access.tmbId : undefined
        })
  );

  const updateTreeData = useCallback(() => {
    const map: Record<string, TreeSpaceType> = {};
    const trMap = (
      list: TreeSpaceType[],
      level: number,
      parentPrivilege: PrivilegeEnum
    ): SpaceTreeDataType[] =>
      list.map((it) => {
        map[it.id] = it;

        const privilege: PrivilegeEnum = isAdmin
          ? PrivilegeEnum.Owner
          : it.privileges.reduce((max, it) => (it > max ? it : max), parentPrivilege);

        const node: SpaceTreeDataType = {
          id: it.id,
          key: it.id,
          value: it.id,
          title: it.spaceName,
          isLeaf: !it.hasChildren,
          space: it,
          level,
          privilege
        };

        if (it.children?.length) {
          node.children = trMap(it.children, level + 1, privilege);
          node.children.forEach((it) => (it.parent = node));
        }

        return node;
      });

    mapRef.current = map;
    setTreeData(trMap(treeRef.current, 0, PrivilegeEnum.View));
  }, [isAdmin]);

  const refreshTree = useCallback(
    (parentId?: string) => {
      if (!parentId || parentId === '0') {
        return getSpaceListApi({ parentId: virtualRootId || '0' }).then((res) => {
          treeRef.current = res.map((it) => {
            const space: TreeSpaceType = { ...it };
            const old = treeRef.current.find((it) => it.id === space.id);
            if (old?.children) {
              space.children = old.children;
            }
            return space;
          });
          updateTreeData();
        });
      }

      const parent = mapRef.current[parentId];
      if (!parent) {
        return Promise.reject();
      }
      const maxPrivilege =
        parent.privileges.length > 0
          ? parent.privileges.reduce((max, it) => (it > max ? it : max), parent.privileges[0])
          : PrivilegeEnum.Owner;
      return getSpaceListApi({
        parentId: parent.id || '0',
        shareType: parent.shareType,
        privilege: isAdmin ? undefined : maxPrivilege
      }).then((res) => {
        parent.children = res.map((it) => {
          const space: TreeSpaceType = { ...it };
          const old = parent.children?.find((it) => it.id === space.id);
          if (old?.children) {
            space.children = old.children;
          }
          return space;
        });
        parent.hasChildren = res.length > 0;
        updateTreeData();
      });
    },
    [getSpaceListApi, updateTreeData]
  );

  const getPath = useCallback((id: string) => {
    const path: SpacePathType = [];
    let space = mapRef.current[id];
    while (space) {
      path.unshift({ type: PathItemTypeEnum.space, space });
      space = mapRef.current[space.parentId];
    }
    return path;
  }, []);

  useEffect(() => {
    getSpaceListApi({ parentId: virtualRootId || '0' }).then((res) => {
      treeRef.current = res.map((it) => ({ ...it }));
      updateTreeData();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    treeData,
    refreshTree,
    getPath
  };
}
