import MyModal from '@/components/MyModal';
import { BaseModalProps, PathType } from '@/types/cloud';
import { Box, Flex } from '@chakra-ui/react';
import { FileType } from '@/types/api/cloud';
import { Button, Form, Input } from 'antd';
import { respDims } from '@/utils/chakra';
import { useMutation } from '@tanstack/react-query';
import {
  addMyFolder,
  addSpaceFolder,
  cloudFileRename,
  renameMyFolder,
  updateSpaceFolder
} from '@/api/cloud';
import { PathItemTypeEnum } from '@/constants/cloud';
import { Toast } from '@/utils/ui/toast';
import { useState } from 'react';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import PathCascader from '@/pages/cloud/components/PathCascader';
import { useCloudStore } from '@/store/useCloudStore';
import { useNotificationStore } from '@/store/useTificationContext';

type FormType = {
  path?: PathType;
  folderName: string;
};

const FolderModal = ({
  bizType,
  fileType,
  folder,
  path,
  onClose,
  onSuccess
}: {
  bizType: BizTypeEnum;
  fileType?: FileTypeEnum;
  folder?: FileType;
  path?: PathType;
} & BaseModalProps) => {
  const [initialValues] = useState<FormType>({
    path,
    folderName: folder?.fileName || ''
  });
  const { setSelectFile, selectFile } = useCloudStore();

  const [form] = Form.useForm<FormType>();

  const { fetchUnreadCount } = useNotificationStore();

  const { mutate: onSubmit } = useMutation({
    mutationFn: async (data: FormType) => {
      if (folder) {
        if (bizType === BizTypeEnum.TenantLibrary && fileType === FileTypeEnum.Folder) {
          return updateSpaceFolder({ id: folder.id, spaceName: data.folderName });
        } else if (bizType === BizTypeEnum.MyLibrary && fileType === FileTypeEnum.Folder) {
          return renameMyFolder({ id: folder.id, folderName: data.folderName });
        } else if (fileType === FileTypeEnum.File) {
          return cloudFileRename({ id: folder!.id, fileName: data.folderName });
        }
      }
      if (!data.path?.length) {
        return Promise.reject();
      }

      const parent = data.path[data.path.length - 1];
      const parentId = parent.type === PathItemTypeEnum.space ? parent.space.id : parent.file.id;
      if (bizType === BizTypeEnum.TenantLibrary) {
        return addSpaceFolder({ parentId, spaceName: data.folderName });
      } else {
        return addMyFolder({ parentId, folderName: data.folderName });
      }
    },
    onSuccess: async () => {
      Toast.success('操作成功');
      selectFile?.id == String(folder?.id) && setSelectFile(selectFile ? { ...selectFile } : null); //刷新文件信息
      onSuccess?.();
      onClose?.();
      await fetchUnreadCount();
    }
  });

  return (
    <MyModal
      title={
        folder && fileType == FileTypeEnum.Folder
          ? '编辑文件夹'
          : folder && fileType == FileTypeEnum.File
            ? '编辑文件'
            : '添加文件夹'
      }
      isOpen
      isCentered
      hideCloseButton
      onClose={onClose}
    >
      <Box p={respDims(24)}>
        <Form form={form} initialValues={initialValues} labelCol={{ span: 7 }} onFinish={onSubmit}>
          <Form.Item
            name="folderName"
            label={fileType == FileTypeEnum.Folder ? '文件夹名称' : '文件名称'}
            rules={[
              {
                required: true,
                message: fileType == FileTypeEnum.Folder ? '请输入文件夹名称' : '请输入文件名称'
              }
            ]}
          >
            <Input
              placeholder={fileType == FileTypeEnum.Folder ? '文件夹名称' : '文件名称'}
              autoComplete="off"
              autoFocus
            />
          </Form.Item>

          {!folder && (
            <Form.Item
              name="path"
              label="所属位置"
              rules={[{ required: true, message: '请选择所属位置' }]}
            >
              <PathCascader bizType={bizType} defaultPath={path} />
            </Form.Item>
          )}

          <Flex justify="right">
            <Button htmlType="button" onClick={onClose}>
              取消
            </Button>
            <Button style={{ marginLeft: '16px' }} type="primary" htmlType="submit">
              确定
            </Button>
          </Flex>
        </Form>
      </Box>
    </MyModal>
  );
};

export default FolderModal;
