import { BizTypeEnum } from '@/constants/api/cloud';
import { Cascader } from 'antd';
import { ReactNode, useEffect, useState } from 'react';
import usePathTree, { PathTreeDataType } from '../../hooks/usePathTree';
import { PathType } from '@/types/cloud';
import { PathItemTypeEnum } from '@/constants/cloud';

const PathCascader = ({
  bizType,
  placeholder,
  value,
  defaultPath,
  disabled,
  onChange
}: {
  bizType: BizTypeEnum;
  placeholder?: ReactNode;
  value?: PathType;
  defaultPath?: PathType;
  disabled?: boolean;
  onChange?: (value: PathType) => void;
}) => {
  const [innerValue, setInnerValue] = useState([] as string[]);

  const {
    treeData: options,
    initRoot,
    refreshTree,
    getPath,
    getPathItem
  } = usePathTree({ bizType, autoInitRoot: false });

  const onLoadData = (options: PathTreeDataType[]) => options.forEach((it) => refreshTree(it.id));

  const displayRender = () => {
    const id = innerValue[innerValue.length - 1];
    const pathItem = id
      ? getPathItem(id) ||
        value?.find((it) =>
          it.type === PathItemTypeEnum.space ? it.space.id === id : it.file.id === id
        )
      : undefined;

    return pathItem ? (
      <>
        {pathItem.type === PathItemTypeEnum.space
          ? pathItem.space.spaceName
          : pathItem.file.fileName}
      </>
    ) : (
      <></>
    );
  };

  useEffect(() => {
    if (!value?.length) {
      return;
    }
    const ids = value.map((it) => (it.type === PathItemTypeEnum.space ? it.space.id : it.file.id));
    setInnerValue(ids);
    initRoot().then(() => {
      if (!ids.length) {
        return;
      }
      const next = (index: number) => {
        if (index >= ids.length - 1) {
          return;
        }
        refreshTree(ids[index]).then(() => next(index + 1));
      };
      next(0);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Cascader
      style={{ width: '100%' }}
      fieldNames={{ label: 'title' }}
      changeOnSelect
      allowClear={true}
      placeholder={placeholder}
      value={innerValue}
      options={options}
      dropdownStyle={{ overflowX: 'auto', zIndex: 2000 }}
      loadData={onLoadData}
      displayRender={displayRender}
      disabled={disabled}
      onChange={(e) => {
        const value = (e as string[]) ?? [];
        if (value?.length) {
          setInnerValue(value);
          onChange?.(value.length ? getPath(value[value.length - 1]) : []);
        } else {
          setInnerValue(
            defaultPath?.map((it) =>
              it.type === PathItemTypeEnum.space ? it.space.id : it.file.id
            ) || []
          );
          onChange?.(defaultPath || []);
        }
      }}
    />
  );
};

export default PathCascader;
