import { Box, ChakraProps, Flex, Text } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { FileTypeEnum } from '@/constants/api/cloud';
import { FileType } from '@/types/api/cloud';
import FileIcon from '../FileIcon';

// 模拟的静态数据
const fileData: FileType[] = [
  {
    fileType: FileTypeEnum.File,
    id: '1',
    parentId: '1',
    fileName: '一级文件夹名称>二级文件夹名称1111',
    fileSize: 1024,
    uploader: '张海丽',
    auditor: '张海丽',
    updateTime: '6-25 14:25'
  },
  {
    fileType: FileTypeEnum.File,
    id: '2',
    parentId: '1',

    fileName: '一级文件夹名称>二级文件夹名称',
    fileSize: 2048,
    uploader: '张海丽',
    auditor: '张海丽',
    updateTime: '6-25 14:25'
  },
  {
    fileType: FileTypeEnum.File,
    id: '3',
    parentId: '1',

    fileName: '一级文件夹名称>二级文件夹名称',
    fileSize: 512,
    uploader: '张海丽',
    auditor: '张海丽',
    updateTime: '6-25 14:25'
  },
  {
    fileType: FileTypeEnum.File,
    id: '4',
    parentId: '1',

    fileName: '一级文件夹名称>二级文件夹名称',
    fileSize: 1024,
    uploader: '张海丽',
    auditor: '张海丽',
    updateTime: '6-25 14:25'
  },
  {
    fileType: FileTypeEnum.File,
    id: '5',
    parentId: '1',

    fileName: '一级文件夹名称>二级文件夹名称',
    fileSize: 2048,
    uploader: '张海丽',
    auditor: '张海丽',
    updateTime: '6-25 14:25'
  }
];

const FileDynamics = ({ ml }: {} & ChakraProps) => {
  return (
    <Flex
      ml={ml ?? respDims(16)}
      flexDir="column"
      w={respDims(346)}
      h="100%"
      px={respDims(16)}
      py={respDims(24)}
      bgColor="#FFFFFF"
      borderRadius={respDims(20)}
    >
      <Box
        ml={respDims(8)}
        mb={respDims(11)}
        flexShrink="0"
        color="#030712"
        fontSize={respDims('17fpx')}
        lineHeight={respDims('28fpx')}
        fontWeight="bold"
      >
        文件动态
      </Box>

      <Box
        flex="1"
        py={respDims(16)}
        borderRadius={respDims(14)}
        px={respDims(16)}
        overflowY="auto"
        bgColor="#f7f9fb"
      >
        {fileData.map((file) => (
          <Box key={file.id} mb={respDims(14)}>
            <Flex justifyContent="flex-start" mb={respDims(6)}>
              <Text fontSize={respDims(14)} mr={respDims(10)} fontWeight="500" color="#303133">
                {file.uploader}
              </Text>
              <Text fontSize={respDims(14)} mr={respDims(10)} color="#606266">
                新增
              </Text>
              <Text fontSize={respDims(14)} color="#606266">
                {file.updateTime}
              </Text>
            </Flex>
            <Flex
              bgColor="#FFFFFF"
              borderRadius={respDims(8)}
              py={respDims(10)}
              px={respDims(14)}
              alignItems="center"
            >
              <FileIcon {...file} />
              <Box ml={respDims(12)} flex="1" w={0}>
                <Box fontSize={respDims(14)} mb={respDims(4)} fontWeight="bold" color="#000000">
                  {file.fileName}
                </Box>
                <Box
                  fontSize={respDims(12)}
                  color="#000000"
                  opacity="0.6"
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  位置：{file.fileName}
                </Box>
              </Box>
            </Flex>
          </Box>
        ))}
      </Box>
    </Flex>
  );
};

export default FileDynamics;
