import SvgIcon from '@/components/SvgIcon';
import { useCallbackRef } from '@/hooks/useCallbackRef';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { Box, Button, Heading, Text, SimpleGrid, VStack, Flex, Image } from '@chakra-ui/react';
import React from 'react';
import SpaceModal from '../../list/components/Tenant/SpaceModal';
import { navPersonal } from '@/constants/cloud';
import { NavType } from '@/types/cloud';
import { respDims } from '@/utils/chakra';
import { useTenantStore } from '@/store/useTenantStore';

interface FeatureCardProps {
  icon: React.ElementType;
  title: string;
  color: string;
  bgGradient: string;
}

const EmptyCloud: React.FC<FeatureCardProps> = ({ icon, title, color, bgGradient }) => (
  <VStack
    spacing={4}
    p={6}
    align="center"
    borderRadius="xl"
    transition="transform 0.2s"
    _hover={{ transform: 'translateY(-4px)' }}
    gap="0"
  >
    <Box borderRadius="full" mt={respDims(13)}>
      {React.createElement(icon)}
    </Box>
    <Text
      fontSize="18px"
      color="#1D2129"
      mt="21px"
      fontWeight="400"
      textAlign="center"
      whiteSpace="nowrap"
    >
      {title}
    </Text>
  </VStack>
);

const SchoolDataSpace: React.FC<{
  isManage?: boolean;
  onConfirm: () => void;
  onNavChange: (nav: NavType) => void;
}> = ({ isManage = true, onConfirm, onNavChange }) => {
  const { industryAlias } = useTenantStore();
  const { openOverlay } = useOverlayManager();

  const onAddSpace = useCallbackRef(() => {
    openOverlay({
      Overlay: SpaceModal,
      props: {
        parentId: '0',
        onSuccess: () => {
          onConfirm();
        }
      }
    });
  });

  const handleGoToPersonalSpace = () => {
    onNavChange(navPersonal);
  };

  const features = [
    {
      icon: () => <Image src="/icon/noSpace1.png" w={respDims(91)} h={respDims(91)} />,
      title: '大容量团队云空间',
      color: 'purple.500',
      bgGradient: 'linear(to-r, purple.100, purple.200)'
    },
    {
      icon: () => <Image src="/icon/noSpace2.png" w={respDims(91)} h={respDims(91)} />,
      title: '权限管理防泄露',
      color: 'blue.500',
      bgGradient: 'linear(to-r, blue.100, blue.200)'
    },
    {
      icon: () => <Image src="/icon/noSpace3.png" w={respDims(91)} h={respDims(91)} />,
      title: '智能搜索',
      color: 'blue.400',
      bgGradient: 'linear(to-r, blue.50, blue.100)'
    },
    {
      icon: () => <Image src="/icon/noSpace4.png" w={respDims(91)} h={respDims(91)} />,
      title: '共享文件',
      color: 'orange.400',
      bgGradient: 'linear(to-r, orange.100, orange.200)'
    }
  ];

  return (
    <Box
      bg="white"
      h="100%"
      w="100%"
      bgColor="#fff"
      p="22px 20px"
      borderTopLeftRadius="0"
      borderTopRightRadius="0"
      borderBottomLeftRadius="8px"
      borderBottomRightRadius="8px"
    >
      <Flex justifyContent="center" maxW="container.xl" h="100%" position="relative">
        <VStack w="full" top="50%" left="50%" position="absolute" transform="translate(-50%, -50%)">
          <VStack spacing={6} textAlign="center" w="full">
            <Heading fontSize="24px" fontWeight="600" color="#0A0A0A">
              {isManage
                ? `创建${industryAlias}数据空间开启高效工作`
                : `你暂无权限访问${industryAlias}数据空间`}
            </Heading>
            <Text fontSize="18px" fontWeight="400" color="#606266" maxW="2xl">
              {industryAlias}资料统一管理，沉淀{industryAlias}数字资产，文档安全更有保障
            </Text>
          </VStack>

          <SimpleGrid
            columns={{ base: 3, md: 4, lg: 4 }}
            spacing={8}
            w="full"
            maxW="container.lg"
            px={{ base: 4, md: 0 }}
          >
            {features.map((feature, index) => (
              <EmptyCloud key={index} {...feature} />
            ))}
          </SimpleGrid>

          <Button
            size="lg"
            colorScheme="purple"
            mt="40px"
            p="7px 20px"
            w="179.111px"
            borderRadius="8px"
            fontSize="md"
            onClick={() => {
              if (isManage) {
                onAddSpace();
              } else {
                handleGoToPersonalSpace();
              }
            }}
            _hover={{
              transform: 'translateY(-2px)',
              boxShadow: 'lg'
            }}
            transition="all 0.2s"
          >
            {isManage ? `添加${industryAlias}空间` : '去我的数据空间储存'}
          </Button>
        </VStack>
      </Flex>
    </Box>
  );
};

export default SchoolDataSpace;
