import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { respDims, rpxDim } from '@/utils/chakra';
import { Box, Center, Flex } from '@chakra-ui/react';
import { Fragment } from 'react';

const ToolBar = ({
  buttons
}: {
  buttons: {
    label: string;
    icon?: SvgIconNameType;
    onClick: () => void;
    disabled?: boolean;
  }[];
}) => {
  return (
    <Flex
      alignItems="center"
      h={respDims('40fpx')}
      boxSizing="border-box"
      borderRadius={respDims(8)}
      border="1px solid #E5E7EB"
      overflow="hidden"
      flexShrink="0"
      mr={rpxDim(10)}
      ml={rpxDim(20)}
    >
      {buttons.map((item, index) => (
        <Fragment key={item.label}>
          {index > 0 && <Box w="1px" h={respDims('22fpx')} bgColor="#E5E7EB" />}

          <Center
            px={respDims(20)}
            h="100%"
            color={item.disabled ? '#A0A0A0' : item.icon === 'trash' ? '#f53f3f' : '#1D2129'}
            fontSize={respDims('15fpx')}
            cursor={item.disabled ? 'not-allowed' : 'pointer'}
            _hover={{
              color: item.disabled ? '#A0A0A0' : item.icon === 'trash' ? '#f53f3f' : 'primary.500'
            }}
            onClick={!item.disabled ? item.onClick : undefined}
          >
            {item.icon && (
              <SvgIcon
                name={item.icon}
                w={respDims('18fpx')}
                h={respDims('18fpx')}
                mr={respDims(8)}
              />
            )}

            <Box whiteSpace="nowrap">{item.label}</Box>
          </Center>
        </Fragment>
      ))}
    </Flex>
  );
};

export default ToolBar;
