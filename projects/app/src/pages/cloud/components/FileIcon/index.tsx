import { FileTypeEnum } from '@/constants/api/cloud';
import { FileInfo } from '@/types/api/cloud';
import { ChakraProps } from '@chakra-ui/react';
import CommonFileIcon from '@/components/FileIcon';

const FileIcon = ({
  fileType,
  fileName,
  fileUrl,
  file,
  files,
  localFile,
  ...props
}: {
  fileType?: FileTypeEnum;
  fileName?: string;
  fileUrl?: string;
  file?: FileInfo;
  files?: FileInfo;
  localFile?: File;
} & ChakraProps) => {
  return (
    <CommonFileIcon
      isFolder={fileType === FileTypeEnum.Folder}
      fileName={fileName || file?.fileName || files?.fileName}
      fileUrl={fileUrl || file?.fileUrl || files?.fileUrl}
      localFile={localFile}
      {...props}
    />
  );
};

export default FileIcon;
