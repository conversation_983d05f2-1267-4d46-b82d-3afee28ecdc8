import { Center } from '@chakra-ui/react';
import { useContext } from 'react';
import { DndRowContext } from '../DndRow';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';

const DndHandle = () => {
  const { setDragNodeRef, attributes, listeners } = useContext(DndRowContext);

  return (
    <Center
      ref={setDragNodeRef}
      {...attributes}
      {...listeners}
      w={respDims(36)}
      h={respDims(36)}
      cursor="grab"
    >
      <SvgIcon name="menu2" color="#D5D5D5" />
    </Center>
  );
};

export default DndHandle;
