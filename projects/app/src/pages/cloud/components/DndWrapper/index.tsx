import { FileType } from '@/types/api/cloud';
import { DndDataType } from '@/types/cloud';
import { Box, Flex } from '@chakra-ui/react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import { ReactNode, useCallback, useState } from 'react';
import FileIcon from '../FileIcon';
import { respDims } from '@/utils/chakra';
import { updateFileParent, updateFolderParent, updateSpaceParent } from '@/api/cloud';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { MessageBox } from '@/utils/ui/messageBox';

const DndWrapper = ({ children }: { children: ReactNode }) => {
  const [file, setFile] = useState<FileType>();

  const onDragStart = useCallback((e: DragStartEvent) => {
    const data = e.active.data.current as DndDataType;

    setFile(data.type === 'file' ? data.file : undefined);
  }, []);

  const onDragEnd = useCallback((e: DragEndEvent) => {
    setFile(undefined);

    const active = e.active.data.current as DndDataType;
    const over = e.over?.data.current as DndDataType;
    if (!active || !over) {
      return;
    }

    let req: (() => Promise<any>) | undefined;
    let activeName: string | undefined;
    let overName: string | undefined;

    if (over.type === 'space') {
      if (
        active.type !== 'file' ||
        active.file.bizType !== BizTypeEnum.TenantLibrary ||
        active.file.parentId == over.space.id
      ) {
        return;
      }
      activeName = active.file.fileName;
      overName = over.space.spaceName;
      if (active.file.fileType === FileTypeEnum.File) {
        req = () =>
          updateFileParent({
            id: active.file.id,
            spaceId: active.file.bizType === BizTypeEnum.TenantLibrary ? over.space.id : undefined,
            folderId: active.file.bizType === BizTypeEnum.MyLibrary ? over.space.id : undefined
          });
      } else {
        const createUpdateParentRequest = (parentId: string) => {
          if (active.file.bizType === BizTypeEnum.TenantLibrary) {
            return updateSpaceParent({ id: active.file.id, parentId });
          }
          return updateFolderParent({ id: active.file.id, parentId });
        };
        req = () => createUpdateParentRequest(over.space.id);
      }
    } else if (
      (over.type === 'file' &&
        over.file.fileType === FileTypeEnum.Folder &&
        active.type === 'file' &&
        active.file.parentId != over.file.id) ||
      (over.type === 'file' &&
        over.file.fileType === FileTypeEnum.Space &&
        active.type === 'file' &&
        active.file.parentId != over.file.id)
    ) {
      activeName = active.file.fileName;
      overName = over.file.fileName;
      if (active.file.fileType === FileTypeEnum.File) {
        req = () =>
          updateFileParent({
            id: active.file.id,
            spaceId: active.file.bizType === BizTypeEnum.TenantLibrary ? over.file.id : undefined,
            folderId: active.file.bizType === BizTypeEnum.MyLibrary ? over.file.id : undefined
          });
      } else {
        const createUpdateParentRequest = (parentId: string) => {
          if (active.file.bizType === BizTypeEnum.TenantLibrary) {
            return updateSpaceParent({ id: active.file.id, parentId });
          }
          return updateFolderParent({ id: active.file.id, parentId });
        };
        req = () => createUpdateParentRequest(over.file.id);
      }
    }

    req &&
      MessageBox.confirm({
        content: `确定要将“${activeName}”移动到“${overName}”？`,
        onOk: () => {
          req().then(() => {
            active.onDragEnd?.();
            over.onDropEnd?.();
          });
        }
      });
  }, []);

  return (
    <DndContext onDragStart={onDragStart} onDragEnd={onDragEnd}>
      {children}
      <DragOverlay dropAnimation={null}>
        {file && (
          <Flex alignItems="center" cursor="pointer">
            <FileIcon {...file} />
            <Box ml={respDims(12)} whiteSpace="nowrap">
              {file.fileName}
            </Box>
          </Flex>
        )}
      </DragOverlay>
    </DndContext>
  );
};

export default DndWrapper;
