import { FileTypeEnum } from '@/constants/api/cloud';
import { DndDataType } from '@/types/cloud';
import { Box } from '@chakra-ui/react';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import { RowProps } from 'antd';
import React, { createContext } from 'react';

export type DndRowContextType = {
  attributes?: ReturnType<typeof useDraggable>['attributes'];
  listeners?: ReturnType<typeof useDraggable>['listeners'];
  setDragNodeRef: (node: HTMLElement | null) => void;
};

export const DndRowContext = createContext<DndRowContextType>({
  setDragNodeRef: () => {}
});

const DndRow = ({ data, ...props }: { data: DndDataType } & RowProps) => {
  const rowKey = (props as any)['data-row-key'];

  const {
    isDragging,
    attributes,
    listeners,
    setNodeRef: setDragNodeRef
  } = useDraggable({
    id: rowKey,
    data
  });

  const { isOver, setNodeRef: setDropNodeRef } = useDroppable({
    id: rowKey,
    data,
    disabled: isDragging || (data.type === 'file' && data.file.fileType === FileTypeEnum.File)
  });

  const rowClassName = `${props.className} ${isDragging ? 'dragging' : isOver ? 'drag-over' : ''}`;

  return (
    <DndRowContext.Provider value={{ attributes, listeners, setDragNodeRef: setDragNodeRef }}>
      <tr ref={setDropNodeRef} {...props} className={rowClassName}>
        {props.children}
      </tr>
    </DndRowContext.Provider>
  );
};

export default DndRow;
