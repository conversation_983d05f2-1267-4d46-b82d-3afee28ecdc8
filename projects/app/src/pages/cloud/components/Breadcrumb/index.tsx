import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { Fragment } from 'react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { useRouter } from 'next/router';
import { BreadcrumbItemType } from '@/types/cloud';
import Item from './Item';

const Breadcrumb = <T extends BreadcrumbItemType>({
  list,
  fontSize,
  lineHeight,
  onBack,
  onClickItem,
  ...props
}: {
  list: T[];
  onBack?: () => void;
  onClickItem?: (item: T) => void;
} & ChakraProps) => {
  const router = useRouter();

  const isSpace = list.length >= 6 && list.every((item) => item.label !== '我的数据空间');
  const isMySpace = list.length >= 7 && list.some((item) => item.label === '我的数据空间');

  return (
    <Flex
      alignItems="center"
      fontSize={fontSize ?? respDims('14fpx')}
      lineHeight={lineHeight ?? respDims('22fpx')}
      overflow="hidden"
      {...props}
    >
      {isSpace || isMySpace ? (
        <>
          {list.slice(0, isSpace ? 3 : 4).map((item, index) => (
            <Fragment key={`${index}-${item.label}`}>
              {index > 0 && (
                <SvgIcon
                  name="slash"
                  w={respDims('16fpx')}
                  h={respDims('16fpx')}
                  mx={respDims(4)}
                  color="#909399"
                />
              )}

              {index === 0 && item.isBack ? (
                <Flex
                  color="primary.500"
                  alignItems="center"
                  cursor="pointer"
                  onClick={() => (onBack ? onBack() : router.back())}
                >
                  <SvgIcon name="chevronLeft" w={respDims('16fpx')} h={respDims('16fpx')} />
                  <Item ml={respDims(4)} dndData={item.dndData}>
                    {item.label}
                  </Item>
                </Flex>
              ) : (
                <Item
                  dndData={item.dndData}
                  {...(index === list.length - 1
                    ? { color: '#303133', fontWeight: 'bold', flexShrink: '0' }
                    : { color: '#606266', overflow: 'hidden', textOverflow: 'ellipsis' })}
                  cursor={item.clickable ? 'pointer' : undefined}
                  onClick={item.clickable && onClickItem ? () => onClickItem(item) : undefined}
                >
                  {item.label}
                </Item>
              )}
            </Fragment>
          ))}

          {/* 只保留开头和倒数各2个目录,中间的目录合并改为... */}
          <SvgIcon
            name="slash"
            w={respDims('16fpx')}
            h={respDims('16fpx')}
            mx={respDims(4)}
            color="#909399"
          />
          <Item color="#606266" mx={respDims(4)}>
            ...
          </Item>

          {list.slice(-2).map((item, index) => (
            <Fragment key={`${list.length - 2 + index}-${item.label}`}>
              <SvgIcon
                name="slash"
                w={respDims('16fpx')}
                h={respDims('16fpx')}
                mx={respDims(4)}
                color="#909399"
              />
              <Item
                dndData={item.dndData}
                color={index === 1 ? '#303133' : '#606266'}
                fontWeight={index === 1 ? 'bold' : undefined}
                flexShrink={index === 1 ? '0' : undefined}
                cursor={item.clickable ? 'pointer' : undefined}
                onClick={item.clickable && onClickItem ? () => onClickItem(item) : undefined}
              >
                {item.label}
              </Item>
            </Fragment>
          ))}
        </>
      ) : (
        list.map((item, index) => {
          return (
            <Fragment key={`${index}-${item.label}`}>
              {index > 0 && (
                <SvgIcon
                  name="slash"
                  w={respDims('16fpx')}
                  h={respDims('16fpx')}
                  mx={respDims(4)}
                  color="#909399"
                />
              )}

              {index === 0 && item.isBack ? (
                <Flex
                  color="primary.500"
                  alignItems="center"
                  cursor="pointer"
                  onClick={() => (onBack ? onBack() : router.back())}
                >
                  <SvgIcon name="chevronLeft" w={respDims('16fpx')} h={respDims('16fpx')} />
                  <Item ml={respDims(4)} dndData={item.dndData}>
                    {item.label}
                  </Item>
                </Flex>
              ) : (
                <Item
                  dndData={item.dndData}
                  {...(index === list.length - 1
                    ? { color: '#303133', fontWeight: 'bold', flexShrink: '0' }
                    : { color: '#606266', overflow: 'hidden', textOverflow: 'ellipsis' })}
                  cursor={item.clickable ? 'pointer' : undefined}
                  onClick={item.clickable && onClickItem ? () => onClickItem(item) : undefined}
                >
                  {item.label}
                </Item>
              )}
            </Fragment>
          );
        })
      )}
    </Flex>
  );
};

export default Breadcrumb;
