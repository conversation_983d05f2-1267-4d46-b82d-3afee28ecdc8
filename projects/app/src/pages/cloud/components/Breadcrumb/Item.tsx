import { DndDataType } from '@/types/cloud';
import { Box, BoxProps } from '@chakra-ui/react';
import { useDroppable } from '@dnd-kit/core';
import { ReactNode } from 'react';

const Item = ({
  dndData,
  children,
  ...props
}: { dndData?: DndDataType; children?: ReactNode } & BoxProps) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `breadcrumb-${dndData?.type === 'file' ? dndData.file.id : dndData?.space?.id || ''}`,
    data: dndData,
    disabled: !dndData
  });

  return (
    <Box ref={setNodeRef} {...props} {...(isOver && { color: '#3366ff' })}>
      {children}
    </Box>
  );
};

export default Item;
