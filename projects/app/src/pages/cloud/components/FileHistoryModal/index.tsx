import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalBody, Text } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useQuery } from '@tanstack/react-query';
import { Toast } from '@/utils/ui/toast';
import { getErrText } from '@/utils/string';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon'; // 假设 SvgIcon 在这个路径
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { getFoldVersionFilePage } from '@/api/cloud';
import { PagingData } from '@/types';
import { CloudFileVersionResponse } from '@/types/api/cloud';
import { formatLocationPath } from '@/utils/business/cloud';
import useFilePreview from '@/hooks/useFilePreview';
import { useToast } from '@/hooks/useToast';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import FileIcon from '../FileIcon';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';

type FileHistoryModalProps = {
  onClose: () => void;
  bizType: BizTypeEnum;
  fileType: FileTypeEnum;
  historyItem: CloudFileVersionResponse;
};

const FileHistoryModal: React.FC<FileHistoryModalProps> = ({
  onClose,
  bizType,
  historyItem,
  fileType
}) => {
  const { addDownload } = useCloudDownloader();

  const {
    data: fileHistory,
    isLoading,
    error
  } = useQuery<PagingData<CloudFileVersionResponse>, Error>(
    ['fileHistory', bizType, fileType, historyItem],
    () => getFoldVersionFilePage({ id: historyItem.id as string, current: 1, size: 9999 }),
    {
      onError: (err) => {
        Toast.warning({
          title: getErrText(err, '获取文件历史版本失败')
        });
      }
    }
  );
  const { toast } = useToast();
  const { previewFile } = useFilePreview();
  const handlePreview = async (historyItem: CloudFileVersionResponse) => {
    const previewSuccess = await previewFile({
      fileUrl: historyItem.files?.fileUrl ?? '',
      fileType: FileTypeEnum.File,
      fileKey: historyItem.files.fileKey
    });

    if (previewSuccess) {
      return;
    } else {
      toast({
        status: 'warning',
        title: '文件类型不能预览'
      });
    }
  };

  const handleDownload = (file: CloudFileVersionResponse) => {
    addDownload({
      bizType,
      type: DownloadTypeEnum.File,
      fileId: file.id,
      fileKey: file.files.fileKey
    }).then(() => {
      Toast.success('已添加到下载队列中');
    });
  };

  const handleDownloadAll = () => {
    addDownload({
      bizType,
      type: DownloadTypeEnum.Batch,
      source: DownloadSourceEnum.History,
      parentId: historyItem.id
    }).then(() => {
      Toast.success('已添加到下载队列中');
    });
  };

  return (
    <MyModal title="文件历史版本" w={respDims(600)} isOpen onClose={onClose} isCentered>
      <ModalBody>
        <Box p={respDims(16)}>
          <Text fontSize={respDims(16)} fontWeight="500" mb={respDims(16)} color="#303133">
            {historyItem.fileName}
          </Text>
          <Text fontSize={respDims(14)} fontWeight="400" color="#606266">
            文件路径：{formatLocationPath(historyItem.locationPath)}
          </Text>
        </Box>
        {isLoading ? (
          <Text>加载中...</Text>
        ) : error ? (
          <Text>加载失败</Text>
        ) : (
          fileHistory?.records.map((file) => (
            <Flex
              key={file.id}
              bg="rgba(248, 250, 252, 1)"
              borderRadius={respDims(8)}
              p={respDims(12)}
              mb={respDims(12)}
              alignItems="center"
            >
              <Box w={respDims(40)} h={respDims(40)} mr={respDims(12)}>
                <FileIcon {...file} />
              </Box>
              <Text flex="1" fontSize={respDims(14)} color="rgba(0, 0, 0, 0.9)">
                {file.fileName}
              </Text>
              <Flex alignItems="center" onClick={() => handlePreview(file)} cursor="pointer">
                <SvgIcon name="file2Preview" w={respDims(14)} h={respDims(14)} mr={respDims(8)} />
                <Text fontSize={respDims(14)} color="rgba(29, 33, 41, 1)">
                  预览
                </Text>
              </Flex>
              <Box
                w={respDims(1)}
                h={respDims(22)}
                bg="#E5E7EB"
                mr={respDims(4)}
                mx={respDims(16)}
              ></Box>
              <Flex alignItems="center" cursor="pointer" onClick={() => handleDownload(file)}>
                <SvgIcon name="download" w={respDims(14)} h={respDims(14)} mr={respDims(8)} />
                <Text fontSize={respDims(14)} color="rgba(29, 33, 41, 1)">
                  下载
                </Text>
              </Flex>
            </Flex>
          ))
        )}
      </ModalBody>
      <ModalFooter>
        <Button variant={'grayBase'} mr={respDims(12)} onClick={onClose}>
          关闭
        </Button>
        <Button onClick={() => handleDownloadAll()}>下载文件</Button>
      </ModalFooter>
    </MyModal>
  );
};

export default FileHistoryModal;
