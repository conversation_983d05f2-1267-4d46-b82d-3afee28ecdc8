import React, { useState } from 'react';
import { Form, Input, Button, Radio } from 'antd';
import { Box, Stack } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { AuditRequestProps } from '@/types/api/cloud';
import { doAudit } from '@/api/cloud';
import { FileInfoProps } from '../type';
import { useCloudStore } from '@/store/useCloudStore';
import { useToast } from '@/hooks/useToast';
import { useNotificationStore } from '@/store/useTificationContext';
import { CloudContext } from '@/components/CloudProvider';
import { AuditorStatusEnum } from '@/constants/api/cloud';

interface AuditFormData {
  auditStatus: string;
  reason: string;
}

type AuditFormProps = {
  onSubmit?: (values: AuditFormData) => void;
  onCancel: () => void;
  onSuccess: () => void;
} & FileInfoProps;

const AuditForm: React.FC<AuditFormProps> = ({ onSubmit, onCancel, onSuccess, id, fileType }) => {
  const [form] = Form.useForm();
  const [refresh, setRefresh] = useState(false);

  const [showReason, setShowReason] = useState(false); // 控制原因表单的显示
  const { setShowFileInfo, setSelectFile, selectFile } = useCloudStore(); // 使用 store
  const { toast } = useToast();
  const { fetchUnreadCount } = useNotificationStore();
  const mutation = useMutation(doAudit, {
    onSuccess: () => {
      toast({
        title: '审核成功',
        status: 'success'
      });
      onSubmit && onSubmit(form.getFieldsValue());
      setSelectFile(
        selectFile ? { ...selectFile, auditorStatus: AuditorStatusEnum.Approved } : null
      );
      setShowFileInfo(false);
      fetchUnreadCount();
    },
    onError: () => {
      toast({
        title: '审核失败',
        status: 'error'
      });
      fetchUnreadCount();
    }
  });

  const handleRadioChange = (e: any) => {
    form.setFieldsValue({ auditStatus: e.target.value });
    setRefresh(!refresh);

    setShowReason(e.target.value !== 'pass'); // 当选择 "通过" 时隐藏原因表单
  };

  const handleSubmit = (values: AuditFormData) => {
    if (id && fileType) {
      const requestData: AuditRequestProps = {
        auditRemark: values.reason,
        auditorStatus: values.auditStatus === 'pass' ? 1 : 2,
        id: id,
        fileType: fileType
      };
      fetchUnreadCount();
      mutation.mutate(requestData);
    }
  };

  return (
    <Box as="div" p={4}>
      <Form form={form} onFinish={handleSubmit} initialValues={{ auditStatus: 'pass', reason: '' }}>
        <Form.Item name="auditStatus">
          <Radio.Group onChange={handleRadioChange}>
            <Radio value="pass">通过</Radio>
            <Radio value="fail">不通过</Radio>
          </Radio.Group>
        </Form.Item>
        {showReason && (
          <Form.Item name="reason">
            <Input.TextArea rows={4} maxLength={500} placeholder="请输入原因" />
          </Form.Item>
        )}
        <Stack direction="row" spacing={4} mt={4}>
          <Button type="primary" htmlType="submit" loading={mutation.isLoading}>
            提交
          </Button>
          <Button onClick={onCancel}>取消</Button>
        </Stack>
      </Form>
    </Box>
  );
};

export default AuditForm;
