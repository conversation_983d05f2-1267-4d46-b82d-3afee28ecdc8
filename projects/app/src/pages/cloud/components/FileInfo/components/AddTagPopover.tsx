import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  ChakraProps,
  Flex,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Text,
  useDisclosure
} from '@chakra-ui/react';
import { useToast } from '@/hooks/useToast';
import { Input } from 'antd';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { createLabel, listLabels, setLabel } from '@/api/cloud';
import { CreateLabelRequest, ListLabelsRequest, SetLabelRequest } from '@/types/api/cloud';
import { LabelItem } from '@/types/api/cloud'; // 假设 LabelItem 类型定义在这里
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';

interface AddTagPopoverProps extends ChakraProps {
  bizType: BizTypeEnum | undefined;
  fileType: FileTypeEnum | undefined;
  id: string | undefined | null;
  resetLabels: () => void;
  fileLabels: LabelItem[];
  children?: React.ReactNode;
}

const { Search } = Input;

const AddTagPopover: React.FC<AddTagPopoverProps> = ({
  bizType,
  fileType,
  id,
  fileLabels,
  ml,
  children,
  resetLabels
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedTagIds, setSelectedTagIds] = useState<number[]>([]);
  const [isCreatingTag, setIsCreatingTag] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [tags, setTags] = useState<LabelItem[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    fetchTags();
  }, [bizType, fileType, id]);

  useEffect(() => {
    // 初始化已选中的标签
    setSelectedTagIds(fileLabels.map((label) => label.id));
  }, [fileLabels]);

  const fetchTags = async () => {
    const requestData: ListLabelsRequest = { labelName: '' };
    const response = await listLabels(requestData);
    setTags(response);
    return response;
  };

  const handleTagClick = (tagId: number) => {
    setSelectedTagIds((prev) =>
      prev.includes(tagId) ? prev.filter((id) => id !== tagId) : [...prev, tagId]
    );
  };

  const handleCreateTag = async () => {
    const trimmedTagName = newTagName.trim();
    if (trimmedTagName !== '') {
      const existingTag = tags.find((tag) => tag.labelName === trimmedTagName);
      if (existingTag) {
        // 如果标签已存在，提示用户
        toast({
          title: '标签已存在',
          status: 'warning',
          duration: 3000
        });
        setSelectedTagIds((prev) => [...prev, existingTag.id]);
      } else {
        // 如果标签不存在，则添加并选中它
        const requestData: CreateLabelRequest = { labelName: trimmedTagName };
        await createLabel(requestData);
        const data = await fetchTags(); // 重新获取标签列表
        const newTag = data.find((tag) => tag.labelName === trimmedTagName);
        if (newTag) {
          setSelectedTagIds((prev) => [...prev, newTag.id]);
        }
        toast({
          title: '标签创建成功',
          status: 'success',
          duration: 3000
        });
      }
      setNewTagName('');
      setIsCreatingTag(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleCreateTag();
    }
  };

  const handleConfirm = async () => {
    const requestData: SetLabelRequest = {
      bizType: bizType!, // 根据实际业务类型设置
      fileType: fileType!, // 根据实际文件类型设置
      id: id as string,
      labelIds: selectedTagIds
    };
    await setLabel(requestData);
    toast({
      title: '设置成功',
      status: 'success',
      duration: 3000
    });
    resetLabels();
    onClose();
  };

  const filteredTags = tags.filter((tag) => tag.labelName.includes(searchTerm));

  return (
    <Popover
      trigger="click"
      placement="bottom-start"
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
    >
      <PopoverTrigger>
        <Flex
          bgColor="#FFFFFF"
          borderRadius={respDims(2)}
          py={respDims(10)}
          px={respDims(15)}
          alignItems="center"
          cursor="pointer"
        >
          <SvgIcon name="plus" w={respDims(12)} h={respDims(12)} mr={respDims(4)} />
          <Text fontSize={respDims(14)} fontWeight="500" color="rgba(29, 33, 41, 1)">
            添加标签
          </Text>
        </Flex>
      </PopoverTrigger>
      <PopoverContent
        w={respDims(282)}
        h={respDims(isCreatingTag ? 420 : 380)}
        boxShadow="0px 3px 10px 0px rgba(0, 0, 0, 0.11)"
        borderRadius="8px"
      >
        <Box p={respDims(16)}>
          <Button
            variant="outline"
            w="100%"
            h={respDims(36)}
            borderRadius="4px"
            border="1px solid rgba(209, 213, 219, 1)"
            mb={respDims(8)}
            onClick={() => setIsCreatingTag(true)}
          >
            <Flex alignItems="center" justifyContent="center">
              <SvgIcon name="plus" w={respDims(14)} h={respDims(14)} mr={respDims(4)} />
              <Text fontSize={respDims(14)} color="rgba(78, 89, 105, 1)">
                新建标签
              </Text>
            </Flex>
          </Button>
          {isCreatingTag && (
            <Box bg="rgba(242, 243, 245, 1)" borderRadius="4px" mb={respDims(8)}>
              <Input
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="新建标签名"
                onPressEnter={handleKeyPress}
                addonBefore={<SvgIcon name="file2Label" w={respDims(20)} h={respDims(20)} />}
                addonAfter={
                  <SvgIcon
                    name="check"
                    w={respDims(20)}
                    h={respDims(20)}
                    cursor="pointer"
                    onClick={handleCreateTag}
                  />
                }
              />
            </Box>
          )}
          <Search
            placeholder="搜索标签"
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ marginBottom: '8px' }}
          />
          <Box h={respDims(200)} overflowY="auto">
            {filteredTags.map((tag) => (
              <Flex
                key={tag.id}
                alignItems="center"
                justifyContent="space-between"
                p={respDims(8)}
                cursor="pointer"
                onClick={() => handleTagClick(tag.id)}
              >
                <Flex
                  alignItems="center"
                  color={selectedTagIds.includes(tag.id) ? '#1D2129' : '#606266'}
                  fontSize={respDims(14)}
                >
                  <SvgIcon
                    mr={respDims(10)}
                    name="file2Label"
                    fontSize={respDims(14)}
                    w={respDims(20)}
                    h={respDims(20)}
                  />
                  {tag.labelName}
                </Flex>
                {selectedTagIds.includes(tag.id) && (
                  <SvgIcon name="check" color="primary.5" w={respDims(20)} h={respDims(20)} />
                )}
              </Flex>
            ))}
          </Box>
          <Flex justifyContent="space-between" mt={respDims(12)}>
            <Button variant="outline" w="48%" h={respDims(36)} borderRadius="4px" onClick={onClose}>
              取消
            </Button>
            <Button
              w="48%"
              h={respDims(36)}
              borderRadius="4px"
              colorScheme="blue"
              onClick={handleConfirm}
            >
              确定
            </Button>
          </Flex>
        </Box>
      </PopoverContent>
    </Popover>
  );
};

export default AddTagPopover;
