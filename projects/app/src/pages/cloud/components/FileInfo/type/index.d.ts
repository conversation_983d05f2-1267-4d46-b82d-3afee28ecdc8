import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { ChakraProps } from '@chakra-ui/system';
export type PageType = 'auit' | 'personal' | 'tenant' | 'recycle';

export interface FileInfoProps extends ChakraProps {
  bizType?: BizTypeEnum | undefined;
  fileType?: FileTypeEnum | undefined;
  id?: string | undefined | null;
  pageType?: PageType;
  onReload?: () => void;
}
// const FileInfoType = () => {
//   return <></>;
// };

// export default FileInfoType;
