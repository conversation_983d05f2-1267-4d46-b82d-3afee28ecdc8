import React, { useState, useEffect } from 'react';
import { Box, Flex, Text, ChakraProps, Image } from '@chakra-ui/react';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { respDims, rpxDim } from '@/utils/chakra';
import FileHistoryModal from '../FileHistoryModal';
import SvgIcon from '@/components/SvgIcon';
import AuditForm from './components/AuditForm';
import AddTagPopover from './components/AddTagPopover';
import {
  getFileDetail,
  getFileVersionList,
  getFileShareList,
  getSetLabels,
  getSpaceShareList
} from '@/api/cloud';
import type {
  CloudFileDetailResponse,
  CloudFileVersionResponse,
  CloudFileShareResponse,
  LocationPathItem,
  GetSetLabelsRequest,
  LabelItem
} from '@/types/api/cloud';
import {
  AuditorStatusEnum,
  AuditorStatusMap,
  BizTypeEnum,
  FileTypeEnum,
  FileTypeMap
} from '@/constants/api/cloud';
import { useQuery } from '@tanstack/react-query';
import { formatFileSize } from '@/utils/tools';
import { FileInfoProps } from './type';
import dayjs from 'dayjs';
import { useCloudStore } from '@/store/useCloudStore';
import { formatLocationPath } from '@/utils/business/cloud';
import MyTooltip from '@/components/MyTooltip';
import ShareModal from '../../list/components/Tenant/ShareModal';
import ShareFileModal from '../../list/components/Tenant/ShareFileModal';
import useFilePreview from '@/hooks/useFilePreview';
import { useToast } from '@/hooks/useToast';
import FileIcon from '../FileIcon';

const FileInfo: React.FC<FileInfoProps> = (props) => {
  const { ml, pageType = 'auit', onReload } = props;
  const { openOverlay } = useOverlayManager();
  const { selectFile, setShowFileInfo } = useCloudStore(); // 使用 store
  const { bizType, fileType, id } = selectFile || {};
  const { previewFile } = useFilePreview();

  const fetchFileData = async () => {
    if (typeof id === 'string' && fileType && bizType) {
      const [fileDetail, fileVersions, fileShares, fileLabels] = await Promise.all([
        getFileDetail({ bizType, fileType, id }),
        getFileVersionList({ bizType, fileType, id }),
        selectFile?.fileType == FileTypeEnum.File
          ? getFileShareList({
              fileId: id
            })
          : getSpaceShareList({ spaceId: id }),
        getSetLabels({ bizType, fileType, id: Number(id) })
      ]);
      return { fileDetail, fileVersions, fileShares, fileLabels };
    }
    return { fileDetail: null, fileVersions: [], fileShares: [], fileLabels: [] };
  };
  useEffect(() => {
    !isFetching && refetch();
  }, [selectFile]);

  const { toast } = useToast();

  const { data, isFetching, error, refetch } = useQuery(
    ['init', bizType, fileType, id, selectFile],
    fetchFileData,
    {
      enabled: !!bizType && !!fileType && !!id
    }
  );

  const onOpenHistory = async (historyItem: CloudFileVersionResponse) => {
    if (fileType == FileTypeEnum.File) {
      const previewSuccess = await previewFile({
        fileUrl: historyItem.files?.fileUrl ?? '',
        fileType: FileTypeEnum.File,
        fileKey: historyItem.files.fileKey
      });
      if (previewSuccess) {
        return;
      } else {
        toast({
          status: 'warning',
          title: '文件类型不能预览'
        });
      }
    } else {
      openOverlay({
        Overlay: FileHistoryModal,
        props: {
          historyItem: historyItem as CloudFileVersionResponse,
          fileType: fileType!,
          bizType: bizType!
        }
      });
    }
  };

  const getFileLabel = () => {
    if (!data?.fileDetail) {
      return '';
    }

    const { fileType, fileName } = data.fileDetail;

    if (fileType === FileTypeEnum.Folder) {
      return FileTypeMap[fileType]?.label || '未知文件夹';
    } else if (fileName) {
      const fileExtension = fileName.split('.').pop();
      return fileExtension ? `.${fileExtension}` : '未知文件';
    }

    return '未知文件';
  };

  const onShareFile = () => {
    if (selectFile) {
      const { id, fileType, fileName } = selectFile;
      openOverlay({
        Overlay: fileType === FileTypeEnum.Folder ? ShareModal : ShareFileModal,
        props: {
          shareData: {
            id,
            name: fileName
          }
        }
      });
    }
  };

  const fileInfoFields = [
    {
      label: '所有者',
      key: 'tmbUserName',
      renderField: (field: { label: string; key: string }) => {
        return <> {data?.fileDetail?.tmbUserName} </>;
      }
    },
    {
      label: '更新时间',
      key: 'updateTime',
      renderField: (field: { label: string; key: string }) => {
        return (
          <>
            {data?.fileDetail?.createTime &&
              dayjs(data?.fileDetail?.createTime).format('M月D日 HH:mm')}
          </>
        );
      }
    },
    {
      label: '文件大小',
      key: 'fileSize',
      renderField: (field: { label: string; key: string }) => {
        return (
          <>
            {data?.fileDetail?.fileSize && data?.fileDetail?.fileSize != 0
              ? formatFileSize(data?.fileDetail?.fileSize)
              : 0}
          </>
        );
      }
    },
    {
      label: '文件类型',
      key: 'fileType',
      renderField: (field: { label: string; key: string }) => {
        return <> {getFileLabel()} </>;
      }
    },
    {
      label: '文件位置',
      key: 'locationPath',
      renderField: (field: { label: string; key: string }) => {
        let pre = '';
        if (data?.fileDetail?.bizType == BizTypeEnum.MyLibrary) {
          if (data.fileDetail?.locationPath.length == 0) {
            pre = pre + '我的数据空间';
          } else {
            pre = pre + '我的数据空间>';
          }
        }
        if (!data?.fileDetail) return null;
        if (Array.isArray(data.fileDetail[field.key as keyof CloudFileDetailResponse])) {
          let paths = pre + formatLocationPath(data.fileDetail?.locationPath || []);
          return (
            <MyTooltip label={paths}>
              {paths.length > 12 ? paths.slice(0, 12) + '...' : paths}
            </MyTooltip>
          );
        }
        return <>{data.fileDetail[field.key as keyof typeof data.fileDetail] || ''}</>;
      }
    }
  ];

  if (!id || isFetching || error) {
    return (
      <Flex
        ml="10px"
        flexDir="column"
        w={respDims(332)}
        h="100%"
        px={respDims(16)}
        py={respDims(24)}
        bgColor="#FFFFFF"
        justifyContent="center"
        alignItems="center"
        borderRadius={respDims(20)}
        // h={rpxDim(769)}
        // mt={rpxDim(76)}
        // mb={rpxDim(16)}
      >
        <>
          <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
          <Box color="#4E5969" fontSize={respDims(14, 12)}>
            {isFetching ? '文件信息加载中...' : error ? '请求错误' : '请选择文件查看信息'}
          </Box>
        </>
      </Flex>

      // <Flex flexDir="column" w="100%" h="100%">
      //   <Box
      //     visibility="hidden"
      //     pl={rpxDim(21)}
      //     pb={rpxDim(22)}
      //     pt={rpxDim(28)}
      //     color="#303133"
      //     fontSize={rpxDim(22)}
      //     fontWeight="bold"
      //     lineHeight={respDims(23)}
      //     borderBottom="1px solid #E5E7EB"
      //   >
      //     <Box ml="5px">数据空间</Box>
      //     <Box w="114px" h="13px" bgColor="#ae99f8" mt={rpxDim(-7)}></Box>
      //   </Box>

      //   <Flex
      //     ml="10px"
      //     flexDir="column"
      //     w={respDims(332)}
      //     px={respDims(16)}
      //     py={respDims(24)}
      //     bgColor="#FFFFFF"
      //     justifyContent="center"
      //     alignItems="center"
      //     borderRadius={respDims(20)}
      //     h="100%"
      //   >
      //     <>
      //       <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
      //       <Box color="#4E5969" fontSize={respDims(14, 12)}>
      //         {isFetching ? '文件信息加载中...' : error ? '请求错误' : '请选择文件查看信息'}
      //       </Box>
      //     </>
      //   </Flex>
      // </Flex>
    );
  }

  return (
    <Flex
      ml={ml ?? respDims(15)}
      flexDir="column"
      w={respDims(332)}
      h="100%"
      px={respDims(16)}
      py={respDims(24)}
      bgColor="#FFFFFF"
      borderRadius={respDims(20)}
    >
      {data?.fileDetail && (
        <>
          <Box
            ml={respDims(8)}
            flexShrink="0"
            color="#030712"
            fontSize={respDims(17)}
            lineHeight={respDims(28)}
            fontWeight="bold"
          >
            文件信息
          </Box>

          <Box
            flex="1"
            mt={respDims(11)}
            p={respDims(16)}
            bgColor="#F8FAFC"
            borderRadius={respDims(14)}
            overflowY="auto"
          >
            <Flex
              bgColor="#FFFFFF"
              borderRadius={respDims(8)}
              py={respDims(10)}
              px={respDims(14)}
              alignItems="center"
              justifyContent="flex-start"
              mb={respDims(16)}
            >
              <FileIcon
                fileType={data.fileDetail.fileType}
                fileName={data.fileDetail.fileName}
                w={respDims(40)}
                h={respDims(40)}
              ></FileIcon>
              <Text
                ml={respDims(12)}
                fontSize={respDims(14)}
                fontWeight="normal"
                color="rgba(0, 0, 0, 0.9)"
                whiteSpace="nowrap"
                overflow="hidden"
                textOverflow="ellipsis"
              >
                {data.fileDetail.fileName}
              </Text>
            </Flex>

            <Text
              fontSize={respDims(15)}
              fontWeight="500"
              color="rgba(48, 49, 51, 1)"
              mb={respDims(8)}
            >
              标签
            </Text>
            <Flex mb={respDims(16)} flexWrap="wrap">
              {data.fileLabels.map((tag, index) => (
                <Box
                  key={index}
                  display="flex"
                  p="5px 8px"
                  alignItems="center"
                  gap="4px"
                  borderRadius="2px"
                  bgColor="var(--bg-white, #FFF)"
                  mr={respDims(8)}
                >
                  <Text fontSize={respDims(14)} fontWeight="normal" color="rgba(29, 33, 41, 1)">
                    {tag.labelName}
                  </Text>
                </Box>
              ))}
              <AddTagPopover
                {...props}
                bizType={bizType}
                fileType={fileType}
                id={id}
                resetLabels={refetch}
                fileLabels={data.fileLabels}
              />
            </Flex>

            {bizType == BizTypeEnum.TenantLibrary && (
              <>
                <Flex justifyContent="space-between" alignItems="center" mb={respDims(16)}>
                  <Text fontSize={respDims(15)} fontWeight="500" color="rgba(48, 49, 51, 1)">
                    共享设置
                  </Text>
                </Flex>
                <Text
                  fontSize={respDims(14)}
                  fontWeight="normal"
                  color="rgba(96, 98, 102, 1)"
                  mb={respDims(16)}
                >
                  {data.fileShares.map((share) => share.userName).join('、')}
                </Text>
                <Flex mb={respDims(16)}>
                  <Flex
                    bgColor="#FFFFFF"
                    borderRadius={respDims(2)}
                    p={respDims(5)}
                    alignItems="center"
                    cursor="pointer"
                    onClick={() => onShareFile()}
                  >
                    <SvgIcon name="share2" w={respDims(14)} h={respDims(14)} mr={respDims(4)} />
                    <Text fontSize={respDims(14)} fontWeight="normal" color="rgba(29, 33, 41, 1)">
                      共享
                    </Text>
                  </Flex>
                </Flex>
                <Text
                  fontSize={respDims(15)}
                  fontWeight="500"
                  color="rgba(48, 49, 51, 1)"
                  mb={respDims(8)}
                >
                  历史版本
                </Text>
                <Flex
                  bgColor="#FFFFFF"
                  borderRadius={respDims(8)}
                  px={respDims(14)}
                  flexDir="column"
                  mb={respDims(16)}
                >
                  {data.fileVersions.map((item, index) => (
                    <Flex
                      key={index}
                      justifyContent="space-between"
                      alignItems="center"
                      borderBottom={
                        index < data.fileVersions.length - 1 ? '1px solid #EFEFEF' : 'none'
                      }
                      py={respDims(14)}
                    >
                      <Flex
                        flexDirection="column"
                        flex="1"
                        overflow="hidden"
                        w="100%"
                        whiteSpace="nowrap"
                      >
                        <MyTooltip overflowOnly>
                          <Box
                            width="100%"
                            textOverflow="ellipsis"
                            overflow="hidden"
                            mb={respDims(6)}
                          >
                            {item.fileName}
                          </Box>
                        </MyTooltip>

                        <Text
                          fontSize={respDims(14)}
                          fontWeight="normal"
                          color="rgba(0, 0, 0, 0.9)"
                        >
                          {item.createTime && dayjs(item.createTime).format('M月D日 HH:mm')}
                        </Text>
                      </Flex>

                      {
                        <SvgIcon
                          onClick={() => onOpenHistory(item)}
                          cursor="pointer"
                          name="file2Eye"
                          w={respDims(21)}
                          h={respDims(21)}
                        />
                      }
                    </Flex>
                  ))}
                </Flex>
              </>
            )}
            <Text
              fontSize={respDims(15)}
              fontWeight="500"
              color="rgba(48, 49, 51, 1)"
              mb={respDims(8)}
            >
              更多信息
            </Text>
            <Flex
              bgColor="#FFFFFF"
              borderRadius={respDims(8)}
              px={respDims(14)}
              py={respDims(7)}
              flexDir="column"
              mb={respDims(16)}
            >
              {fileInfoFields.map((field, index) => (
                <Flex
                  key={index}
                  justifyContent="space-between"
                  alignItems="center"
                  borderBottom={index < fileInfoFields.length - 1 ? '1px solid #EFEFEF' : 'none'}
                  py={respDims(13)}
                >
                  <Text
                    fontSize={respDims('14fpx')}
                    fontWeight="normal"
                    color="rgba(96, 98, 102, 1)"
                    w={respDims(80, 60)}
                  >
                    {field.label}:
                  </Text>
                  <Text
                    fontSize={respDims('14fpx')}
                    fontWeight="normal"
                    w={respDims(0)}
                    flex="1"
                    color="rgba(29, 33, 41, 1)"
                    whiteSpace="nowrap"
                    overflow="hidden"
                    textOverflow="ellipsis"
                  >
                    {field.renderField && field.renderField(field)}
                  </Text>
                </Flex>
              ))}
            </Flex>

            {pageType !== 'personal' && (
              <>
                <Text
                  fontSize={respDims(15)}
                  fontWeight="500"
                  color="rgba(48, 49, 51, 1)"
                  mb={respDims(8)}
                >
                  审核
                </Text>
                {data.fileDetail.auditorStatus === AuditorStatusEnum.Pending &&
                pageType == 'auit' ? (
                  <AuditForm
                    {...props}
                    bizType={bizType}
                    fileType={fileType}
                    id={id}
                    onSuccess={() => {
                      refetch();
                      onReload && onReload();
                    }}
                    onCancel={() => {
                      // 处理取消逻辑
                      setShowFileInfo(false);
                    }}
                  />
                ) : (
                  <Flex
                    bgColor="#FFFFFF"
                    borderRadius={respDims(8)}
                    px={respDims(14)}
                    flexDir="column"
                    mb={respDims(16)}
                  >
                    <Flex justifyContent="space-between" alignItems="center" py={respDims(14)}>
                      <Text fontSize={respDims(14)} fontWeight="normal" color="rgba(0, 0, 0, 0.9)">
                        {data.fileDetail.auditorName}
                      </Text>
                      <Text fontSize={respDims(14)} fontWeight="normal" color="rgba(0, 0, 0, 0.9)">
                        {data.fileDetail.auditTime &&
                          dayjs(data.fileDetail.auditTime).format('M月D日 HH:mm')}
                      </Text>

                      <Text fontSize={respDims(14)} fontWeight="normal" color="rgba(0, 0, 0, 0.9)">
                        {AuditorStatusMap[data.fileDetail.auditorStatus]?.label}
                      </Text>
                    </Flex>
                  </Flex>
                )}
              </>
            )}
          </Box>
        </>
      )}
    </Flex>
  );
};

export default FileInfo;
