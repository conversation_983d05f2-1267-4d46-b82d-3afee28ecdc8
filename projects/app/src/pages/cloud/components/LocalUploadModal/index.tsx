import MyModal from '@/components/MyModal';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { respDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex, background } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { Button, Form, Tooltip } from 'antd';
import { useMemo, useState } from 'react';
import PathCascader from '../PathCascader';
import FileSelect, { ItemType } from './FileSelect';
import { BaseModalProps, PathType } from '@/types/cloud';
import { PathItemTypeEnum } from '@/constants/cloud';
import { useNotificationStore } from '@/store/useTificationContext';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import MyTooltip from '@/components/MyTooltip';

type FormType = {
  path: PathType;
  list: ItemType[];
};

const LocalUploadModal = ({
  bizType,
  fileType,
  path,
  updateId,
  onClose,
  onSuccess
}: {
  bizType: BizTypeEnum;
  fileType: FileTypeEnum;
  path: PathType;
  updateId?: string;
} & BaseModalProps) => {
  const [initialValues] = useState<FormType>({
    path,
    list: []
  });

  const [form] = Form.useForm<FormType>();

  const description = useMemo(() => {
    let description = '';
    path.forEach((item) => {
      if (item.type === PathItemTypeEnum.space) {
        description = item.space.description || '';
      }
    });
    return `文件上传说明：${description || '请将文件上传到指定文件夹中归类'}`;
  }, [path]);

  const { addUpload } = useCloudUploader();

  const { fetchUnreadCount } = useNotificationStore();

  const { mutate: onSubmit } = useMutation({
    // 断点续传
    mutationFn: (data: FormType) => {
      const parent = data.path[data.path.length - 1];
      const path = data.path.map((it) =>
        it.type === PathItemTypeEnum.space ? it.space.spaceName : it.file.fileName
      );

      const parentId = parent.type === PathItemTypeEnum.space ? parent.space.id : parent.file.id;
      const files = data.list
        .filter((it) => it.type === FileTypeEnum.File)
        .flatMap((it) => it.files);

      if (files.length) {
        addUpload({
          path,
          parentId,
          files,
          bizType,
          oldId: updateId
        });
      }

      data.list.forEach((it) => {
        if (it.type === FileTypeEnum.Folder) {
          addUpload({
            path,
            parentId,
            files: it.files,
            bizType,
            oldId: updateId
          });
        }
      });

      return Promise.resolve();
    },
    onSuccess: async () => {
      Toast.success('已添加到上传队列');
      onSuccess?.();
      await fetchUnreadCount();
      onClose?.();
    }
  });

  return (
    <MyModal
      title={fileType === FileTypeEnum.File ? '上传文件' : '上传文件夹'}
      isOpen
      isCentered
      maxW="auto"
      hideCloseButton
      onClose={onClose}
    >
      <Box p={respDims(24)}>
        {bizType === BizTypeEnum.TenantLibrary && (
          <Box
            mb={respDims(21)}
            color="#606266"
            fontSize={respDims('14fpx')}
            lineHeight={respDims('28fpx')}
          >
            <MyTooltip
              label={description}
              placement="top"
              hasArrow
              bg="white"
              color="#606266"
              fontSize={respDims('14fpx')}
              p={respDims(12)}
              boxShadow="0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08), 0 9px 28px 8px rgba(0,0,0,.05)"
              borderRadius="4px"
            >
              <Box
                whiteSpace="nowrap"
                overflow="hidden"
                textOverflow="ellipsis"
                maxW={respDims(600)}
              >
                {description}
              </Box>
            </MyTooltip>
          </Box>
        )}

        <Form form={form} initialValues={initialValues} onFinish={onSubmit}>
          <Form.Item
            name="path"
            label="上传位置"
            rules={[{ required: true, message: '请选择上传位置' }]}
          >
            <PathCascader bizType={bizType} defaultPath={path} disabled={!!updateId} />
          </Form.Item>

          <Form.Item
            name="list"
            label="选择文件"
            rules={[
              {
                required: true,
                message: fileType === FileTypeEnum.File ? '请选择文件' : '请选择文件夹'
              }
            ]}
          >
            <FileSelect fileType={fileType} multiple={!updateId} />
          </Form.Item>

          <Flex justify="end">
            <Button htmlType="button" onClick={onClose}>
              取消
            </Button>
            <Button style={{ marginLeft: '16px' }} type="primary" htmlType="submit">
              确定上传
            </Button>
          </Flex>
        </Form>
      </Box>
    </MyModal>
  );
};

export default LocalUploadModal;
