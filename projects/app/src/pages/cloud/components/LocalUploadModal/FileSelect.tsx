import SvgIcon from '@/components/SvgIcon';
import { FileTypeEnum, MaxUploadFileSize } from '@/constants/api/cloud';
import { useSelectFile } from '@/hooks/useSelectFile';
import { respDims } from '@/utils/chakra';
import { formatFileSize } from '@/utils/tools';
import { Toast } from '@/utils/ui/toast';
import { Box, Center, Flex, Grid, GridItem } from '@chakra-ui/react';
import { Button } from 'antd';
import { nanoid } from 'nanoid';
import { useState } from 'react';
import FileIcon from '../FileIcon';

export type ItemType = {
  key: string;
  name: string;
  size: number;
  subtext: string;
  files: File[];
  type: FileTypeEnum;
};
// 上传组件

const FileSelect = ({
  fileType,
  value,
  multiple = true,
  onChange
}: {
  fileType: FileTypeEnum;
  value?: ItemType[];
  multiple?: boolean;
  onChange?: (value: ItemType[]) => void;
}) => {
  const [innerValue, setInnerValue] = useState<ItemType[]>(value || []);

  const { File, onOpen } = useSelectFile({
    multiple,
    directory: fileType === FileTypeEnum.Folder,
    maxCount: 10000
  });

  const onSelect = (files: File[]) => {
    const list: ItemType[] = [];

    let overSizeCount = 0;

    files.forEach((file) => {
      if (file.name.startsWith('.')) {
        return;
      }

      if (file.size > MaxUploadFileSize) {
        overSizeCount++;
        return;
      }

      if (!file.webkitRelativePath) {
        const dotIndex = file.name.lastIndexOf('.');
        const suffix = dotIndex > 0 ? file.name.substring(dotIndex + 1) : '';
        list.push({
          key: nanoid(),
          name: file.name,
          size: file.size,
          subtext: `${suffix}${suffix ? ', ' : ''}${formatFileSize(file.size)}`,
          files: [file],
          type: FileTypeEnum.File
        });
        return;
      }

      const name = file.webkitRelativePath.split('/')[0];
      const item = list.find((it) => it.name === name);
      if (item) {
        item.size += file.size;
        item.files.push(file);
      } else {
        list.push({
          key: nanoid(),
          name,
          size: file.size,
          subtext: '',
          files: [file],
          type: FileTypeEnum.Folder
        });
      }
    });

    list.forEach((it) => {
      if (!it.subtext) {
        it.subtext = formatFileSize(it.size);
      }
    });

    const newValue = multiple ? [...innerValue, ...list] : list.slice(0);
    setInnerValue(newValue);
    onChange?.(newValue);

    overSizeCount > 0 &&
      Toast.warning(`忽略了${overSizeCount}个大小超过${formatFileSize(MaxUploadFileSize)}的文件`);
  };

  return (
    <Box>
      <Button type="primary" ghost style={{ borderRadius: '4px' }} onClick={onOpen}>
        <Center>
          <SvgIcon name="plus" />
          <Box>{fileType === FileTypeEnum.File ? '选择文件' : '选择文件夹'}</Box>
        </Center>
      </Button>

      <Grid
        mt={respDims(5)}
        templateColumns="repeat(2, 1fr)"
        gap={respDims(12)}
        w={respDims(559)}
        maxH={respDims(200)}
        overflowY="scroll"
      >
        {innerValue.map((item) => (
          <GridItem
            key={item.key}
            w="100%"
            h="100%"
            pos="relative"
            pt={respDims(8)}
            pr={respDims(5)}
            overflow="hidden"
          >
            <Flex
              alignItems="center"
              minW={respDims(266)}
              h={respDims('68fpx')}
              px={respDims(12)}
              py={respDims(14)}
              bgColor="#F7F8FA"
              borderRadius={respDims(8)}
            >
              <FileIcon fileType={item.type} localFile={item.files[0]} />

              <Box flex="1" ml={respDims(12)} overflow="hidden">
                <Box
                  color="#1D2129"
                  fontSize={respDims('14fpx')}
                  lineHeight={respDims('22fpx')}
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  {item.name}
                </Box>
                <Box
                  mt={respDims(4)}
                  color="#909399"
                  fontSize={respDims('13fpx')}
                  lineHeight={respDims('22fpx')}
                >
                  {item.subtext}
                </Box>
              </Box>
            </Flex>

            <SvgIcon
              name="circleClose"
              w={respDims(24)}
              h={respDims(24)}
              pos="absolute"
              top="0"
              right="0"
              cursor="pointer"
              onClick={() => {
                const newValue = innerValue.filter(({ key }) => key !== item.key);
                setInnerValue(newValue);
                onChange?.(newValue);
              }}
            />
          </GridItem>
        ))}
      </Grid>

      <File onSelect={onSelect} />
    </Box>
  );
};

export default FileSelect;
