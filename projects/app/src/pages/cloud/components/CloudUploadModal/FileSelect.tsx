import { getMyFlatFilePage } from '@/api/cloud';
import MyTable from '@/components/MyTable';
import { MyTableRef, SearchBarProps } from '@/components/MyTable/types';
import { FileType, MyFlatFileFolderType, MyFlatFileType } from '@/types/api/cloud';
import { getListFromPage } from '@/utils/api';
import { respDims } from '@/utils/chakra';
import { formatFileSize } from '@/utils/tools';
import { Box, Flex } from '@chakra-ui/react';
import { TableProps } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';

import { useCallback, useMemo, useRef } from 'react';
import SearchInput from '../SearchInput';
import FileIcon from '../FileIcon';
import MyTooltip from '@/components/MyTooltip';

const getFolderNames = (folder?: MyFlatFileFolderType) => {
  const names: string[] = [];
  let child = folder;
  while (child) {
    names.push(child.folderName);
    child = child.children?.[0];
  }
  return names;
};

const FileSelect = ({
  value,
  onChange
}: {
  value?: FileType[];
  onChange?: (value: FileType[]) => void;
}) => {
  const tableRef = useRef<MyTableRef>(null);

  const selectedRowKeys = useMemo(() => {
    return value?.filter((it) => it !== undefined && it !== null).map((it) => it.id) || [];
  }, [value]);

  const rowSelection: TableRowSelection<FileType> = {
    type: 'checkbox',
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange(selectedRowKeys, selectedRows, info) {
      onChange?.(selectedRows);
    }
  };

  const columns: TableProps<MyFlatFileType>['columns'] = [
    {
      title: '文件名称',
      key: 'fileName',
      render: (_, record) => {
        const { fileName } = record;
        return (
          <Flex alignItems="center">
            <FileIcon {...record} />
            <Box ml={respDims(12)} w="280px" overflow="hidden">
              <MyTooltip overflowOnly>{fileName}</MyTooltip>
              <MyTooltip
                overflowOnly
                label={`位置 ${getFolderNames(record.folderNames?.[0]).join(' > ')}`}
              >
                <Box
                  maxW="100%"
                  color="rgba(0,0,0,0.4)"
                  fontSize="12px"
                  lineHeight={respDims('22fpx')}
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >{`位置 ${getFolderNames(record.folderNames?.[0]).join(' > ')}`}</Box>
              </MyTooltip>
            </Box>
          </Flex>
        );
      }
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      render: (_, { fileSize }) => (fileSize ? formatFileSize(fileSize) : '')
    }
  ];

  const getFiles = useCallback(
    (query: { searckKey?: string }) => getListFromPage(getMyFlatFilePage, query),
    []
  );

  const Header = useCallback(({ tableInstance }: SearchBarProps) => {
    return (
      <Flex w="100%" align="center" flexDir="column">
        <SearchInput
          placeholder="文件名搜索"
          w="100%"
          p="7px 16px"
          borderRadius="8px"
          bgColor="#F6F6F6"
          mb="14px"
          onSearch={(e) => tableInstance.setQuery({ searchKey: e })}
        />
      </Flex>
    );
  }, []);

  return (
    <Box
      w="500px"
      h="501px"
      overflow="hidden"
      flexDir="column"
      css={{
        'th.ant-table-cell': {
          padding: '6px 4px !important'
        },
        'td.ant-table-cell': {
          padding: '14px 4px !important'
        }
      }}
    >
      <MyTable
        ref={tableRef}
        rowKey="id"
        style={{ width: '100%' }}
        columns={columns}
        rowSelection={rowSelection}
        pageConfig={{ showPaginate: false }}
        headerConfig={{ HeaderComponent: Header }}
        api={getFiles}
        boxStyle={{ px: 0, py: 0, overflow: 'hidden' }}
      />
    </Box>
  );
};

export default FileSelect;
