import MyModal from '@/components/MyModal';
import { BizTypeEnum } from '@/constants/api/cloud';
import { respDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import { Box, Flex } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { Button, Form } from 'antd';
import { useMemo, useState } from 'react';
import PathCascader from '../PathCascader';
import FileSelect from './FileSelect';
import { BaseModalProps, PathType } from '@/types/cloud';
import { PathItemTypeEnum } from '@/constants/cloud';
import { FileType } from '@/types/api/cloud';
import { uploadMyFileToCloud } from '@/api/cloud';
import MyTooltip from '@/components/MyTooltip';

type FormType = {
  path: PathType;
  list: FileType[];
};

const CloudUploadModal = ({
  path,
  onClose,
  onSuccess
}: {
  path: PathType;
} & { onSuccess?: (parentId: string) => void } & Omit<BaseModalProps, 'onSuccess'>) => {
  const [initialValues] = useState<FormType>({
    path,
    list: []
  });

  const [form] = Form.useForm<FormType>();

  const description = useMemo(() => {
    let description = '';
    path.forEach((item) => {
      if (item.type === PathItemTypeEnum.space) {
        description = item.space.description || '';
      }
    });
    return `文件上传说明：${description || '请将文件上传到指定文件夹中归类'}`;
  }, [path]);

  const { mutate: onSubmit } = useMutation({
    mutationFn: (data: FormType) => {
      const parent = path[path.length - 1];
      const spaceId = parent.type === PathItemTypeEnum.space ? parent.space.id : parent.file.id;
      return uploadMyFileToCloud({
        spaceId,
        sourceFileIds: data.list.map((item) => item.id),
        isAuditRoot: 1
      }).then(() => spaceId);
    },
    onSuccess: (spaceId: string) => {
      Toast.success('操作成功');
      onSuccess?.(spaceId);
      onClose?.();
    }
  });

  return (
    <MyModal title="添加云端文件" isOpen isCentered w="571px" hideCloseButton onClose={onClose}>
      <Box p={respDims(32)}>
        <Form form={form} initialValues={initialValues} onFinish={onSubmit}>
          <Form.Item name="list">
            <FileSelect />
          </Form.Item>

          <Box
            position="sticky"
            bottom="0"
            bg="white"
            pb={respDims(24)}
            zIndex="1"
            mb={respDims(-24)}
          >
            <Flex justify="flex-end">
              <Button htmlType="button" onClick={onClose}>
                取消
              </Button>
              <Button style={{ marginLeft: '16px' }} type="primary" htmlType="submit">
                确定上传
              </Button>
            </Flex>
          </Box>
        </Form>
      </Box>
    </MyModal>
  );
};

export default CloudUploadModal;
