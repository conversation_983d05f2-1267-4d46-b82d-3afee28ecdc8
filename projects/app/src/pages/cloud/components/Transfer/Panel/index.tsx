import React from 'react';
import { respDims } from '@/utils/chakra';
import { Tab, Tab<PERSON>ist, TabPanel, TabPanels, Tabs } from '@chakra-ui/react';
import Upload from './Upload';
import Download from './Download';
import SvgIcon from '@/components/SvgIcon';

const TransferPanel = ({
  onClose,
  showDownload = true
}: {
  onClose?: () => void;
  showDownload?: boolean;
}) => {
  return (
    <Tabs w="100%" h="100%" colorScheme="primary" display="flex" flexDir="column">
      <TabList
        mt={respDims(11)}
        px={respDims(16)}
        fontSize={respDims('16fpx')}
        lineHeight={respDims('22fpx')}
        pos="relative"
        borderBottom="1px solid #E7E7E7"
        css={{
          '.chakra-tabs__tab': {
            padding: '9px 16px',
            position: 'relative',
            border: 'none',
            '&[aria-selected=true]::after': {
              content: '" "',
              position: 'absolute',
              width: '2em',
              bottom: '1px',
              left: 0,
              right: 0,
              margin: '0 auto',
              height: '2px',
              background: 'primary.500'
            }
          }
        }}
      >
        <Tab>上传</Tab>
        {showDownload && <Tab>下载</Tab>}

        <SvgIcon
          name="close"
          w={respDims(20)}
          h={respDims(20)}
          pos="absolute"
          right={respDims(32)}
          top="0"
          bottom="0"
          my="auto"
          cursor="pointer"
          onClick={onClose}
        />
      </TabList>

      <TabPanels flex="1" overflow="hidden">
        <TabPanel h="100%">
          <Upload />
        </TabPanel>
        {showDownload && (
          <TabPanel h="100%">
            <Download />
          </TabPanel>
        )}
      </TabPanels>
    </Tabs>
  );
};

export default TransferPanel;
