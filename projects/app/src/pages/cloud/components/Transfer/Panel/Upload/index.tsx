import { UploadFileType, UploadStatusEnum } from '@/components/CloudProvider/type';
import MyTooltip from '@/components/MyTooltip';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { respDims } from '@/utils/chakra';
import { formatFileSize } from '@/utils/tools';
import { Box, Flex, Image } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { Fragment, useMemo } from 'react';
import FileIcon from '../../../FileIcon';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import { Progress } from 'antd';

type StatusInfoType = {
  icon: SvgIconNameType;
  name: string;
  removable?: boolean;
  retryable?: boolean;
};

const statusInfoMap: Record<UploadStatusEnum, StatusInfoType> = {
  [UploadStatusEnum.Waiting]: {
    icon: 'transferWaiting',
    name: '等待中',
    removable: true
  },
  [UploadStatusEnum.Uploading]: {
    icon: 'transferUpload',
    name: '上传中',
    removable: true
  },
  [UploadStatusEnum.Uploaded]: { icon: 'transferSuccess', name: '成功' },
  [UploadStatusEnum.Failed]: { icon: 'transferError', name: '失败', retryable: true }
};

const Upload = () => {
  const { uploadFiles, removeUpload, retryUpload } = useCloudUploader();
  console.log('uploadFiles', uploadFiles);

  const files: ({
    sizeText: string;
    timeText: string;
    statusInfo: StatusInfoType;
  } & UploadFileType)[] = useMemo(
    () =>
      uploadFiles.map((it) => ({
        ...it,
        sizeText: formatFileSize(it.size, true),
        timeText: dayjs(it.createTime).format('MM-DD HH:mm'),
        statusInfo: statusInfoMap[it.status]
      })),
    [uploadFiles]
  );
  const progress = useMemo(() => {
    if (!files.length) return 0;

    return (
      files.reduce((acc, file) => {
        const fileProgress =
          file.status === UploadStatusEnum.Uploaded
            ? 100
            : ((file.loadedSize || 0) / file.size) * 100;
        return acc + fileProgress;
      }, 0) / files.length
    );
  }, [files]);

  return files.length ? (
    <Box w="100%" maxH="100%" overflowY="auto">
      {files.map((file) => (
        <Flex
          key={file.localId}
          alignItems="center"
          py={respDims(14)}
          _notLast={{
            borderBottom: '1px solid #E5E7EB'
          }}
          pos="relative"
        >
          <FileIcon fileType={file.type} fileName={file.name} />
          <Flex
            flex="1"
            ml={respDims(10)}
            color="#909399"
            fontSize={respDims('13fpx')}
            lineHeight={respDims('22fpx')}
            alignItems="flex-end"
            overflow="hidden"
          >
            <Box flex="1 1 0" overflow="hidden">
              <Box
                color="rgba(0,0,0,0.9)"
                fontSize={respDims('15fpx')}
                lineHeight={respDims('22fpx')}
                maxH={respDims('44fpx')}
                overflow="hidden"
                wordBreak="break-all"
                whiteSpace="wrap"
                textOverflow="ellipsis"
              >
                {file.name}
              </Box>

              <Flex>
                <Box w="4em">{file.sizeText}</Box>

                {!!file.remotePath?.length && (
                  <Flex flex="1 1 0" alignItems="center" overflow="hidden">
                    <Box mx={respDims(8)} w="1px" h={respDims('13fpx')} bgColor="#909399" />

                    <MyTooltip overflowOnly>
                      <Flex flex="1 1 0" alignItems="center" overflow="hidden" whiteSpace="nowrap">
                        {file.remotePath.map((item, index) => (
                          <Fragment key={index}>
                            {index > 0 && <SvgIcon name="chevronRight" />}
                            <Box>{item}</Box>
                            {file.status !== UploadStatusEnum.Uploaded && (
                              <Box>({file.progress?.toFixed(2)}%)</Box>
                            )}
                          </Fragment>
                        ))}
                      </Flex>
                    </MyTooltip>
                  </Flex>
                )}

                <Box ml={respDims(16)} w={respDims('100fpx')}>
                  {file.timeText}
                </Box>
              </Flex>
            </Box>

            {file.statusInfo && (
              <>
                <SvgIcon
                  ml={respDims(24)}
                  name={file.statusInfo.icon}
                  w={respDims('24fpx')}
                  h={respDims('24fpx')}
                />

                <Box ml={respDims(10)} w="2em">
                  {file.statusInfo.name}
                </Box>

                <Box w="5em" h={respDims('22fpx')} textAlign="center">
                  {file.statusInfo.removable && (
                    <SvgIcon
                      name="close"
                      w={respDims('22fpx')}
                      h={respDims('22fpx')}
                      cursor="pointer"
                      _hover={{ color: '#3366ff' }}
                      onClick={() => removeUpload(file)}
                    />
                  )}
                  {file.statusInfo.retryable && (
                    // {/* 重新上传 */}
                    <SvgIcon
                      name="transferWaiting"
                      w={respDims('24fpx')}
                      h={respDims('24fpx')}
                      cursor="pointer"
                      _hover={{ color: '#3366ff' }}
                      onClick={() => retryUpload(file)}
                    ></SvgIcon>
                  )}
                </Box>
              </>
            )}
          </Flex>
          {file.status === UploadStatusEnum.Uploading && (
            <Box pos="absolute" left="0" right="0" bottom="-5px">
              <Progress
                percent={(file.loadedSize / file.size) * 100}
                strokeWidth={4}
                style={{ height: '4px' }}
                showInfo={false}
              />
            </Box>
          )}
          {file.status === UploadStatusEnum.Failed && (
            <Box pos="absolute" left="0" right="0" bottom="-5px">
              <Progress
                percent={Math.max(((file.loadedSize || 0) / file.size) * 100, 1)}
                status="exception"
                strokeWidth={4}
                style={{ height: '4px' }}
                showInfo={false}
              />
            </Box>
          )}
        </Flex>
      ))}
    </Box>
  ) : (
    <Flex justifyContent="center" alignItems="center" h="100%" direction="column">
      <Image src="/imgs/common/folder_empty.svg" w="150px" alt="" />
      <Box color="#4E5969" fontSize={respDims(16, 12)}>
        暂无数据
      </Box>
    </Flex>
  );
};

export default Upload;
