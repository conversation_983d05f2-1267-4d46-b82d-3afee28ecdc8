import { DownloadFileType, DownloadStatusEnum } from '@/components/CloudProvider/type';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { respDims } from '@/utils/chakra';
import { formatFileSize } from '@/utils/tools';
import { Box, Flex, Image } from '@chakra-ui/react';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import FileIcon from '../../../FileIcon';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';
import { Progress } from 'antd';

type StatusInfoType = {
  icon: SvgIconNameType;
  name: string;
  removable?: boolean;
  retryable?: boolean;
};

const statusInfoMap: Record<DownloadStatusEnum, StatusInfoType> = {
  [DownloadStatusEnum.Waiting]: {
    icon: 'transferWaiting',
    name: '等待中',
    removable: true
  },
  [DownloadStatusEnum.Downloading]: {
    icon: 'transferDownload',
    name: '下载中',
    removable: true
  },
  [DownloadStatusEnum.Downloaded]: { icon: 'transferSuccess', name: '成功' },
  [DownloadStatusEnum.Failed]: { icon: 'transferError', name: '失败', retryable: false }
};

const Download = () => {
  const { downloadFiles, removeDownload } = useCloudDownloader();

  const files: ({
    sizeText: string;
    timeText: string;
    statusInfo: StatusInfoType;
  } & DownloadFileType)[] = useMemo(
    () =>
      downloadFiles.map((it) => ({
        ...it,
        sizeText: it.size ? formatFileSize(it.size) : '',
        timeText: dayjs(it.createTime).format('MM-DD HH:mm'),
        statusInfo: statusInfoMap[it.status]
      })),
    [downloadFiles]
  );

  return files.length ? (
    <Box w="100%" maxH="100%" overflowY="auto">
      {files.map((file) => (
        <Flex
          key={file.id}
          alignItems="center"
          py={respDims(14)}
          px={respDims(4)}
          _notLast={{
            borderBottom: '1px solid #E5E7EB'
          }}
          pos="relative"
        >
          <FileIcon fileName={file.name} fileUrl={file.url} />

          <Flex
            flex="1"
            ml={respDims(10)}
            color="#909399"
            fontSize={respDims('13fpx')}
            lineHeight={respDims('22fpx')}
            alignItems="flex-end"
            overflow="hidden"
          >
            <Box flex="1 1 0" overflow="hidden">
              <Box
                color="rgba(0,0,0,0.9)"
                fontSize={respDims('15fpx')}
                lineHeight={respDims('22fpx')}
                maxH={respDims('44fpx')}
                overflow="hidden"
                wordBreak="break-all"
                whiteSpace="wrap"
                textOverflow="ellipsis"
              >
                {file.name}
              </Box>

              <Flex>
                <Box w="4em">{file.sizeText}</Box>

                <Box ml="auto" w={respDims('100fpx')}>
                  {file.timeText}
                </Box>
              </Flex>
            </Box>

            {file.statusInfo && (
              <>
                <SvgIcon
                  ml={respDims(24)}
                  name={file.statusInfo.icon}
                  w={respDims('24fpx')}
                  h={respDims('24fpx')}
                />

                <Box ml={respDims(10)} w="4em">
                  {file.statusInfo.name}
                </Box>

                <Box w="5em" h={respDims('22fpx')} textAlign="center">
                  {file.statusInfo.removable && (
                    <SvgIcon
                      name="close"
                      w={respDims('22fpx')}
                      h={respDims('22fpx')}
                      cursor="pointer"
                      _hover={{ color: '#3366ff' }}
                      onClick={() => removeDownload(file)}
                    />
                  )}

                  {file.statusInfo.retryable && (
                    <Box cursor="pointer" _hover={{ color: '#3366ff' }} onClick={() => {}}>
                      重新下载
                    </Box>
                  )}
                </Box>
              </>
            )}
          </Flex>

          {file.status === DownloadStatusEnum.Downloading && (
            <Box pos="absolute" left="0" right="0" bottom="-5px">
              <Progress
                percent={(file.loadedSize / file.size) * 100}
                strokeWidth={4}
                style={{ height: '4px' }}
                showInfo={false}
              />
            </Box>
          )}
        </Flex>
      ))}
    </Box>
  ) : (
    <Flex justifyContent="center" alignItems="center" h="100%" direction="column">
      <Image src="/imgs/common/folder_empty.svg" w="150px" alt="" />
      <Box color="#4E5969" fontSize={respDims(16, 12)}>
        暂无数据
      </Box>
    </Flex>
  );
};

export default Download;
