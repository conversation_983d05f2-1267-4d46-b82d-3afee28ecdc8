import React from 'react';
import {
  Box,
  Button,
  ChakraProps,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { Badge } from 'antd';
import TransferPanel from '../Panel';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';

const TransferButton = ({
  children,
  showDownload = true,
  ...props
}: { children?: React.ReactNode; showDownload?: boolean } & ChakraProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const { uploadingCount } = useCloudUploader();

  const { downloadingCount } = useCloudDownloader();

  const badgeCount = uploadingCount + downloadingCount;

  return (
    <Popover
      trigger="hover"
      placement="bottom-end"
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
    >
      <PopoverTrigger>
        <Box {...props}>
          <Badge size="small" count={badgeCount} offset={[badgeCount > 9 ? -8 : 0, 0]}>
            {children ?? (
              <Button
                variant="ghost"
                p="0"
                minW="0"
                w={respDims(36)}
                h={respDims(36)}
                borderRadius={respDims(8)}
                border="1px solid #E5E7E8"
                bgColor="#fff"
                _hover={{ bgColor: '#fff' }}
              >
                <SvgIcon name="swap" w={respDims(14)} h={respDims(14)} color="#1D2129" />
              </Button>
            )}
          </Badge>
        </Box>
      </PopoverTrigger>

      <PopoverContent
        w={respDims('697fpx')}
        h={respDims('488fpx')}
        boxShadow="0px 3px 10px 0px rgba(0,0,0,0.11)"
      >
        {isOpen && <TransferPanel onClose={onClose} showDownload={showDownload} />}
      </PopoverContent>
    </Popover>
  );
};

export default TransferButton;
