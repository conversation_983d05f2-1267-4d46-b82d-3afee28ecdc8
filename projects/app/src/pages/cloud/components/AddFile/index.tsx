import SvgIcon from '@/components/SvgIcon';
import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { PathItemTypeEnum } from '@/constants/cloud';
import { useCloudUploader } from '@/hooks/useCloudUploader';
import { PathFileType, PathSpaceType, PathType } from '@/types/cloud';
import { respDims, rpxDim } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import { Box, Button, VStack, Text, Flex, Image } from '@chakra-ui/react';

const AddFile = ({ path, isPersonal }: { path: PathType; isPersonal: boolean }) => {
  const { addUpload } = useCloudUploader();

  const onLocalUpload = (fileType: FileTypeEnum) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    if (fileType === FileTypeEnum.Folder) {
      input.webkitdirectory = true;
    }
    input.onchange = (event) => {
      const files = Array.from((event.target as HTMLInputElement).files || []);
      if (files.length && path?.length) {
        const lastPathItem = path[path.length - 1];
        const parentId =
          lastPathItem.type === PathItemTypeEnum.space
            ? (lastPathItem as PathSpaceType).space.id
            : (lastPathItem as PathFileType).file.id;
        Toast.success('已添加到上传队列');
        addUpload({
          path: path.map((it) =>
            it.type === PathItemTypeEnum.space ? it.space.spaceName : it.file.fileName
          ),
          parentId,
          files,
          bizType: isPersonal ? BizTypeEnum.MyLibrary : BizTypeEnum.TenantLibrary
        });
      }
    };
    input.click();
  };

  return (
    <Flex maxW="container.xl" pt="200px">
      <VStack spacing={0} align="center">
        <Box w="80px" h="80px" position="relative" mb={4}>
          <Box position="absolute" />
          <Box
            position="absolute"
            inset={0}
            borderRadius="lg"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Image src="/icon/add_file.png" w={respDims(91)} h={respDims(91)} maxW="none" />
          </Box>
        </Box>

        <VStack spacing={3}>
          <Text fontSize="24px" fontWeight="600" color="#0A0A0A">
            请添加文件
          </Text>
          <Text fontSize="18px" color="#606266" fontWeight="400">
            可添加本地文件至空间中，或让同事共享文件
          </Text>
        </VStack>

        <Flex alignItems="center" gap={respDims(20)} mt={rpxDim(36)}>
          <Button
            variant="outline"
            colorScheme="primary"
            _hover={{
              bg: '#e3dcfc'
            }}
            p="7px 20px"
            fontSize={respDims('14fpx')}
            borderRadius={respDims(8)}
            onClick={() => onLocalUpload(FileTypeEnum.File)}
            h="40px"
            color="#7D4DFF"
          >
            {/* <SvgIcon name="cloudUpload" w={respDims('14fpx')} h={respDims('14fpx')} /> */}
            <Image
              src="/imgs/cloud/upload.svg"
              w={respDims('14fpx')}
              h={respDims('14fpx')}
              maxW="none"
            />
            <Box fontWeight="400" fontSize="14px" ml={respDims(8)}>
              上传文件
            </Box>
          </Button>

          <Button
            variant="solid"
            colorScheme="primary"
            p="7px 20px"
            fontSize={respDims('14fpx')}
            borderRadius={respDims(8)}
            h="40px"
            onClick={() => onLocalUpload(FileTypeEnum.Folder)}
          >
            {/* <SvgIcon name="cloudFolder" w={respDims('14fpx')} h={respDims('14fpx')} /> */}
            <Image
              src="/imgs/cloud/folder.svg"
              w={respDims('14fpx')}
              h={respDims('14fpx')}
              maxW="none"
            />
            <Box color="#fff" fontWeight="500" fontSize="14px" ml={respDims(8)}>
              上传文件夹
            </Box>
          </Button>
        </Flex>
      </VStack>
    </Flex>
  );
};

export default AddFile;
