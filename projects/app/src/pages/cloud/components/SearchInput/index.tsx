import SvgIcon from '@/components/SvgIcon';
import { SearchTypeEnum } from '@/constants/cloud';
import { respDims, rpxDim } from '@/utils/chakra';
import {
  Box,
  ChakraProps,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';

const typeConfigs: Record<
  SearchTypeEnum,
  {
    placeholder?: string;
    iconWidth: string[];
    iconContainerWidthLeft?: string[];
    iconContainerWidthRight: string[];
    inputPx: string[];
  } & ChakraProps
> = {
  [SearchTypeEnum.general]: {
    placeholder: '请输入',
    w: respDims('246fpx'),
    h: respDims('36fpx'),
    bgColor: '#FFFFFF',
    borderRadius: respDims(4),
    border: '1px solid #E5E7EB',
    iconWidth: respDims('14fpx'),
    iconContainerWidthRight: respDims('38fpx'),
    inputPx: respDims('12fpx')
  },
  [SearchTypeEnum.file]: {
    placeholder: '搜索文件',
    w: respDims('246fpx'),
    h: respDims('36fpx'),
    bgColor: 'rgba(0,0,0,0.03)',
    borderRadius: respDims(8),
    iconWidth: respDims('14fpx'),
    iconContainerWidthLeft: respDims('42fpx'),
    iconContainerWidthRight: respDims('46fpx'),
    inputPx: respDims('16fpx')
  }
};

const SearchInput = ({
  type = SearchTypeEnum.general,
  value = '',
  placeholder,
  w,
  h,
  width,
  height,
  bgColor,
  border,
  borderRadius,
  onChange,
  onSearch,
  onClearSearch,
  isShowCloseIcon = false,
  searchIconDirect = 'right',
  searchBgColor = '#EFEFEF',
  ...props
}: {
  type?: SearchTypeEnum;
  value?: string;
  placeholder?: string;
  onChange?: (text: string) => void;
  onSearch?: (text: string) => void;
  onClearSearch?: () => void;
  isShowCloseIcon?: boolean;
  searchIconDirect?: string;
  searchBgColor?: string;
} & ChakraProps) => {
  const [innerValue, setInnerValue] = useState(value);

  const config = typeConfigs[type];

  const showLeftIcon = type === SearchTypeEnum.file && false;

  useEffect(() => {
    setInnerValue(value);
  }, [value]);

  return (
    <InputGroup
      w={w ?? width ?? config.w}
      h={h ?? height ?? config.h}
      bgColor={bgColor ?? config.bgColor}
      border={border ?? config.border}
      borderRadius={borderRadius ?? config.borderRadius}
      boxSizing="border-box"
      overflow="hidden"
      {...props}
    >
      {showLeftIcon && (
        <InputLeftElement w={config.iconContainerWidthRight} h="100%">
          <SvgIcon name="microphone" w={respDims('14fpx')} h={respDims('14fpx')} color="#4E5969" />
        </InputLeftElement>
      )}

      {searchIconDirect === 'left' && (
        <InputLeftElement
          w={config.iconContainerWidthLeft}
          h="100%"
          cursor="pointer"
          _hover={{
            bgColor: searchBgColor
          }}
          onClick={() => {
            onSearch?.(innerValue);
          }}
        >
          <SvgIcon name="search" w={respDims('18fpx')} h={respDims('18fpx')} color="#4E5969" />
        </InputLeftElement>
      )}

      <Input
        value={innerValue}
        w="100%"
        h="100%"
        pl={searchIconDirect === 'left' ? config.iconContainerWidthLeft : config.inputPx}
        pr={config.iconContainerWidthRight}
        placeholder={placeholder ?? config.placeholder}
        color="#303133"
        bgColor="transparent"
        fontSize={respDims('16fpx')}
        border="none"
        _focus={{
          border: 'none',
          outline: 'none',
          boxShadow: 'none'
        }}
        _placeholder={{
          color: '#606266',
          fontSize: 'inherit'
        }}
        onChange={(e) => {
          setInnerValue(e.target.value);
          onChange?.(e.target.value);
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            e.stopPropagation();
            onSearch?.(innerValue);
          }
        }}
      />

      {searchIconDirect === 'right' && (
        <InputRightElement
          w={config.iconContainerWidthRight}
          h="100%"
          cursor="pointer"
          _hover={{
            bgColor: searchBgColor
          }}
          onClick={() => {
            onSearch?.(innerValue);
          }}
        >
          <SvgIcon name="search" w={respDims('14fpx')} h={respDims('14fpx')} color="#4E5969" />
        </InputRightElement>
      )}
      {innerValue && isShowCloseIcon && (
        <Box
          cursor="pointer"
          bgColor="#eeeefe"
          pos="absolute"
          top="50%"
          transform="translate(0,-50%)"
          zIndex="10"
          right={searchIconDirect === 'left' ? rpxDim(12) : respDims(46)}
          onClick={() => {
            setInnerValue('');
            onClearSearch?.();
          }}
          w={respDims('14fpx')}
          h={respDims('14fpx')}
        >
          <SvgIcon
            display="block"
            name="close"
            w={respDims('14fpx')}
            h={respDims('14fpx')}
            color="#4E5969"
          />
        </Box>
      )}
    </InputGroup>
  );
};

export default SearchInput;
