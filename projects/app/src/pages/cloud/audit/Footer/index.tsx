import ToolBar from '@/pages/cloud/components/ToolBar';
import { respDims } from '@/utils/chakra';
import { Flex } from '@chakra-ui/react';
import AuditModal from '../components/AuditModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { FileType } from '@/types/api/cloud';
import { MessageBox } from '@/utils/ui/messageBox';
import { folderMixBatchDelete } from '@/api/cloud';
import { Toast } from '@/utils/ui/toast';
import { AuditorStatusEnum, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';

const Footer = ({
  selectedData,
  parentId,
  currentTab,
  onSuccess
}: {
  selectedData?: FileType[];
  parentId?: string;
  currentTab?: string;
  onSuccess: () => void;
}) => {
  const { openOverlay } = useOverlayManager();

  const { addDownload } = useCloudDownloader();

  const onBatchAudit = () => {
    const list = selectedData?.filter((item) => item.auditorStatus === AuditorStatusEnum.Pending);

    if (list!.length) {
      openOverlay({
        Overlay: AuditModal,
        props: {
          onSuccess: () => {
            onSuccess();
          },
          onClose() {},
          selectedData: selectedData!
        }
      });
    } else {
      Toast.error('请选择待审核文件');
    }
  };

  const onRemoveFiles = () => {
    MessageBox.confirm({
      title: '删除文件',
      content: `确定要删除选中的文件吗？`,
      onOk: () => {
        const list = selectedData
          ?.filter((item) => item.auditorStatus === AuditorStatusEnum.Rejected)
          .map((it) => {
            return {
              id: it.id,
              fileType: it.fileType!
            };
          });
        if (list && list.length > 0) {
          folderMixBatchDelete({ list: list! }).then((res) => {
            if (res) {
              onSuccess();
              Toast.success('删除成功');
            }
          });
        } else {
          Toast.error('请选中审核未通过数据删除！');
        }
      }
    });
  };

  const onDownloadFiles = () => {
    selectedData?.length &&
      addDownload({
        bizType: BizTypeEnum.TenantLibrary,
        type: DownloadTypeEnum.Batch,
        source: DownloadSourceEnum.Audit,
        parentId: parentId || '0',
        folderIds: selectedData
          .filter((it) => it.fileType === FileTypeEnum.Folder)
          .map((it) => it.id),
        fileIds: selectedData.filter((it) => it.fileType === FileTypeEnum.File).map((it) => it.id)
      }).then(() => {
        Toast.success('已添加到下载队列中');
      });
  };

  return (
    <>
      {selectedData?.length ? (
        <Flex mb={respDims(5)} ml={respDims(10)} w="100%" alignItems="center">
          <ToolBar
            buttons={[
              {
                label: '审核',
                icon: 'file2Examine',
                disabled: currentTab !== '2',
                onClick: () => onBatchAudit()
              },
              {
                label: '下载',
                disabled: !selectedData?.length,
                icon: 'download',
                onClick: onDownloadFiles
              },
              {
                label: '删除',
                disabled: !selectedData?.length,
                icon: 'trash',
                onClick: () => onRemoveFiles()
              }
            ]}
          />
        </Flex>
      ) : (
        <></>
      )}
    </>
  );
};

export default Footer;
