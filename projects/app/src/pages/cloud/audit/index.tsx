import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import MyTable from '@/components/MyTable';
import { Box, Center, Flex } from '@chakra-ui/react';
import Header from './Header';
import { TableProps } from 'antd';
import Footer from './Footer';
import { FileType } from '@/types/api/cloud';
import { getAuditList, getAuditSubList, folderMixBatchDelete } from '@/api/cloud';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { TableRowSelection } from 'antd/es/table/interface';
import {
  AuditorStatusEnum,
  AuditorStatusMap,
  BizTypeEnum,
  FileTypeEnum,
  StatusEnum
} from '@/constants/api/cloud';
import { formatFileSize } from '@/utils/tools';
import dayjs from 'dayjs';
import MyMenu from '@/components/MyMenu';
import styles from '../cloud.module.scss';
import { MessageBox } from '@/utils/ui/messageBox';
import FileInfo from '../components/FileInfo';
import { MyTableRef, SearchBarProps } from '@/components/MyTable/types';
import { useCloudStore } from '@/store/useCloudStore';
import { Toast } from '@/utils/ui/toast';
import { FilePathType, NavType, PathItemType } from '@/types/cloud';
import { PathItemTypeEnum } from '@/constants/cloud';
import useFilePreview from '@/hooks/useFilePreview';
import { DownloadSourceEnum, DownloadTypeEnum } from '@/components/CloudProvider/type';
import FileIcon from '../components/FileIcon';
import MyTooltip from '@/components/MyTooltip';
import { useCloudDownloader } from '@/hooks/useCloudDownloader';
import { serviceSideProps } from '@/utils/i18n';
import { current } from 'immer';
const CloudAuditList = () => {
  const [nav, setNav] = useState<NavType>();
  const [currentTab, setCurrentTab] = useState('2');
  const { selectFile, setSelectFile, showFileInfo, setShowFileInfo } = useCloudStore();
  const [showFileDynamics, setShowFileDynamics] = useState(false);
  const [selectedRows, setSelectedRows] = useState<FileType[]>();
  const tableRef = useRef<MyTableRef>(null);

  const { previewFile } = useFilePreview();

  const { addDownload } = useCloudDownloader();

  useEffect(() => {
    setSelectFile(null);
    setShowFileInfo(false);
  }, []);

  useEffect(() => {
    // 根据selectFile的状态刷新

    let tableData = (tableRef.current?.data || []) as FileType[];
    let target = tableData.find((item) => {
      if (
        selectFile?.fileType == item.fileType &&
        selectFile?.id == item.id &&
        selectFile?.auditorStatus != item.auditorStatus
      ) {
        return true;
      }
    });

    if (target) {
      tableRef.current?.reload();
    }
  }, [selectFile]);

  const [folderPath, setFolderPath] = useState<FilePathType>([]);

  const { path, cloudSpaceId } = useMemo(() => {
    const path = [...folderPath];
    const item = path[path.length - 1];
    return {
      path,
      cloudSpaceId: item ? item.file.id : ''
    };
  }, [folderPath]);

  const onToggleFileDynamics = () => {
    setShowFileDynamics((prev) => !prev);
  };

  const onClickFile = async (file: FileType) => {
    const previewSuccess = await previewFile({
      fileUrl: file.files?.fileUrl ?? '',
      fileType: file.fileType,
      fileKey: file.fileKey
    });
    if (previewSuccess) {
      return;
    }
    if (file.fileType === FileTypeEnum.Folder) {
      setFolderPath((state) => [...state, { type: PathItemTypeEnum.file, file }]);
    }
  };

  const onRemoveFile = (file: FileType) => {
    MessageBox.confirm({
      title: '删除文件',
      content: `确定要删除文件${file.fileName}吗？`,
      onOk: () => {
        const list = [
          {
            id: file.id,
            fileType: file.fileType!
          }
        ];
        folderMixBatchDelete({ list }).then((res) => {
          if (res) {
            tableRef.current?.reload();
            Toast.success('删除成功');
          }
        });
      }
    });
  };

  const onRefresh = useCallback(() => {
    tableRef.current?.reload();
  }, []);

  const onBackToParent = useCallback(() => {
    setFolderPath((state) => state.slice(0, state.length - 1));
  }, []);

  const onClickPathItem = useCallback((pathItem: PathItemType) => {
    if (pathItem.type === PathItemTypeEnum.space) {
      setFolderPath([]);
    } else {
      setFolderPath((state) => {
        const index = state.findIndex((item) => item.file.id === pathItem.file.id);
        return index >= 0 ? state.slice(0, index + 1) : state;
      });
    }
  }, []);

  const onDownloadFile = (file: FileType) => {
    addDownload(
      file.fileType === FileTypeEnum.File
        ? {
            bizType: BizTypeEnum.TenantLibrary,
            type: DownloadTypeEnum.File,
            fileId: file.id,
            fileKey: file.fileKey!
          }
        : {
            bizType: BizTypeEnum.TenantLibrary,
            type: DownloadTypeEnum.Folder,
            source: DownloadSourceEnum.Audit,
            folderId: file.id
          }
    ).then(() => {
      Toast.success('已添加到下载队列中');
    });
  };

  const handleTabChange = (value: string) => {
    setCurrentTab(value);
  };

  const TableHeader = useCallback(
    (props: SearchBarProps) => {
      return (
        <Header
          path={path}
          {...props}
          cloudSpaceId={cloudSpaceId}
          onRefresh={onRefresh}
          onBackToParent={onBackToParent}
          onClickPathItem={onClickPathItem}
          onTabChange={handleTabChange}
        />
      );
    },
    [path, onRefresh, onBackToParent, onClickPathItem]
  );

  const rowSelection: TableRowSelection<FileType> = {
    type: 'checkbox',
    onChange: (selectedRowKeys: React.Key[], selectedRows: FileType[]) => {
      setSelectedRows(selectedRows);
    }
  };

  const columns: TableProps<FileType>['columns'] = [
    {
      title: '文件名称',
      key: 'fileName',
      width: 300,
      render: (_, record) => {
        const { fileName } = record;
        return (
          <Flex alignItems="center" cursor="pointer" onClick={() => onClickFile(record)}>
            <FileIcon {...record} />
            <Box ml={respDims(12)}>{fileName}</Box>
          </Flex>
        );
      }
    },
    {
      title: '上传人',
      ellipsis: true,
      width: 120,
      dataIndex: 'tmbUserName'
    },
    {
      title: '上传时间',
      ellipsis: true,
      width: 120,
      dataIndex: 'createTime',
      render: (value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm') : '')
    },
    {
      title: '文件大小',
      width: 120,
      dataIndex: 'fileSize',
      render: (_, { fileType: type, fileSize }) =>
        type === FileTypeEnum.Folder ? '' : formatFileSize(fileSize!)
    },
    {
      title: '审核人',
      ellipsis: true,
      width: 120,
      dataIndex: 'auditorName'
    },
    {
      title: '审核时间',
      ellipsis: true,
      width: 120,
      dataIndex: 'auditTime',
      render: (value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm') : '')
    },
    {
      title: '状态',
      ellipsis: true,
      width: 120,
      dataIndex: 'auditorStatus',
      render: (value, record) => {
        return (
          <Flex alignItems="center">
            <Box
              w="6px"
              h="6px"
              mr="8px"
              bgColor={value === 2 ? '#D54941' : 'rgba(0,0,0,0.6)'}
              borderRadius="50%"
            ></Box>
            <Box color={value === 2 ? '#D54941' : 'rgba(0,0,0,0.6)'}>
              {AuditorStatusMap[value as StatusEnum]?.label}
            </Box>
            {value === 2 && (
              <MyTooltip label={record.auditRemark}>
                <SvgIcon ml="11px" name="file2Query" w={respDims(16)} h={respDims(16)} />
              </MyTooltip>
            )}
          </Flex>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      render: (_, record) => (
        <Flex justify="flex-end">
          <MyMenu
            trigger="hover"
            width={100}
            Button={
              <Center
                w={respDims('24fpx')}
                h={respDims('24fpx')}
                borderRadius={respDims(4)}
                cursor="pointer"
                _hover={{
                  bgColor: '#EFEFEF'
                }}
              >
                <SvgIcon name="more" w={respDims('14fpx')} h={respDims('14fpx')} />
              </Center>
            }
            menuList={[
              {
                label: '下载',
                icon: <SvgIcon name="download" />,
                onClick: () => onDownloadFile(record)
              },
              ...(record.auditorStatus === AuditorStatusEnum.Pending &&
              !cloudSpaceId &&
              currentTab != '1'
                ? [
                    {
                      label: '审核',
                      icon: <SvgIcon name="file2Examine" />,
                      onClick: () => {
                        setSelectFile({ ...record, bizType: 1 });
                        setShowFileInfo(true);
                      }
                    }
                  ]
                : []),
              ...(record.auditorStatus !== AuditorStatusEnum.Approved
                ? [
                    {
                      label: '删除',
                      icon: <SvgIcon name="trash" />,
                      onClick: () => onRemoveFile(record)
                    }
                  ]
                : [])
            ]}
          />
        </Flex>
      )
    }
  ];

  useEffect(() => {
    if (cloudSpaceId) {
      tableRef.current?.setQuery({ cloudSpaceId, type: Number(tableRef.current.query.type) || 2 });
    }
  }, [cloudSpaceId]);

  useEffect(() => {
    setFolderPath([]);
  }, [nav]);

  return (
    <Flex
      w="100%"
      h="100%"
      px={respDims(24)}
      py={respDims(16)}
      borderRadius="0"
      backgroundImage="/imgs/app/app_center_bg3.png"
      backgroundRepeat="no-repeat"
      backgroundSize="cover"
    >
      <Box
        flex="1"
        h="100%"
        alignItems="stretch"
        px={respDims(24)}
        pt="0"
        pb={respDims(60)}
        bgColor="#FFFFFF"
        borderRadius={respDims(10)}
        overflow="hidden"
      >
        <MyTable
          ref={tableRef}
          className={styles['file-table']}
          columns={columns}
          rowSelection={rowSelection}
          api={!cloudSpaceId ? getAuditList : getAuditSubList}
          pageConfig={{
            showPaginate: false
          }}
          defaultQuery={{
            type: 2
          }}
          boxStyle={{ pl: '0px', pr: '0', py: 0 }}
          headerConfig={{ HeaderComponent: TableHeader }}
          onRow={(record) => ({
            onClick: () => {
              setSelectFile({ ...record, bizType: 1 });
            }
          })}
          rowClassName={(record) => (record.id === selectFile?.id ? 'selected-row' : '')}
        />
        <Footer
          selectedData={selectedRows}
          parentId={cloudSpaceId}
          currentTab={currentTab}
          onSuccess={() => {
            setSelectedRows([]);
            tableRef.current?.reload();
          }}
        />
      </Box>
      {showFileInfo && selectFile && (
        <FileInfo id={selectFile.id} bizType={selectFile.bizType} fileType={selectFile.fileType} />
      )}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default CloudAuditList;
