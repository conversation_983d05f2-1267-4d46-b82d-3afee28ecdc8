import React, { useState } from 'react';
import { Form, Input, Button, Radio } from 'antd';
import { Box, Stack } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { getAuditDoAuditBatch } from '@/api/cloud';
import { useCloudStore } from '@/store/useCloudStore';
import { useToast } from '@/hooks/useToast';
import MyModal from '@/components/MyModal';
import { FileType } from '@/types/api/cloud';
import { AuditorStatusEnum } from '@/constants/api/cloud';

interface AuditFormData {
  auditStatus: string;
  reason: string;
}

const AuditForm = ({
  onSuccess,
  onClose,
  selectedData
}: {
  onSuccess: () => void;
  onClose: () => void;
  selectedData: FileType[];
}) => {
  const [form] = Form.useForm();
  const [refresh, setRefresh] = useState(false);
  const { setShowFileInfo } = useCloudStore();
  const { toast } = useToast();
  const mutation = useMutation(getAuditDoAuditBatch, {
    onSuccess: () => {
      toast({
        title: '审核成功',
        status: 'success'
      });
      onSuccess();
      onClose();
      setShowFileInfo(false);
    },
    onError: () => {
      toast({
        title: '审核失败',
        status: 'error'
      });
    }
  });

  const handleRadioChange = (e: any) => {
    form.setFieldsValue({ auditStatus: e.target.value });
    setRefresh(!refresh);
  };

  const handleSubmit = (values: AuditFormData) => {
    const list = selectedData
      ?.filter((item) => item.auditorStatus === AuditorStatusEnum.Pending)
      .map((it) => {
        return {
          id: it.id,
          fileType: it.fileType!
        };
      });

    const requestData = {
      auditRemark: values.reason || '',
      auditorStatus: values.auditStatus === 'pass' ? 1 : 2,
      auditRequestList: list
    };
    mutation.mutate(requestData);
  };

  return (
    <MyModal title="批量审核" isOpen isCentered onClose={onClose}>
      <Box as="div" p={4} ml="12px">
        <Form
          form={form}
          onFinish={handleSubmit}
          initialValues={{ auditStatus: 'pass', reason: '' }}
        >
          <Form.Item name="auditStatus">
            <Radio.Group onChange={handleRadioChange}>
              <Radio value="pass">通过</Radio>
              <Radio value="fail">不通过</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.auditStatus !== currentValues.auditStatus
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('auditStatus') === 'fail' ? (
                <Form.Item name="reason" rules={[{ required: true, message: '请输入原因' }]}>
                  <Input.TextArea rows={4} maxLength={500} placeholder="请输入原因" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
          <Stack direction="row" justifyContent="end" spacing={4} mt={4}>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" htmlType="submit" loading={mutation.isLoading}>
              提交
            </Button>
          </Stack>
        </Form>
      </Box>
    </MyModal>
  );
};

export default AuditForm;
