import { SearchBarProps } from '@/components/MyTable/types';
import Breadcrumb from '@/pages/cloud/components/Breadcrumb';
import SearchInput from '@/pages/cloud/components/SearchInput';
import { respDims } from '@/utils/chakra';
import { Box, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import SvgIcon from '@/components/SvgIcon';
import { Select } from 'antd';
import styles from '../../cloud.module.scss';
import { PathItemTypeEnum, SearchTypeEnum } from '@/constants/cloud';
import {
  BreadcrumbItemType,
  FilePathType,
  PathType,
  PathSpaceType,
  PathItemType
} from '@/types/cloud';
import { UserRoleEnum } from '@/constants/api/auth';
import { useUserStore } from '@/store/useUserStore';

type QueryType = {
  name: string;
};

const stateOptions = [
  { value: '', label: '全部状态' },
  { value: '0', label: '待审核' },
  { value: '2', label: '审核未通过' }
];

const tabs: { name: string; value: string }[] = [
  {
    name: '我审核的',
    value: '2'
  },
  {
    name: '我上传的',
    value: '1'
  }
];
const Header = ({
  path,
  onSearch,
  onBackToParent,
  onClickPathItem,
  cloudSpaceId,
  query,
  onTabChange
}: {
  path?: PathType;
  cloudSpaceId?: string;
  onSearch?: (query: QueryType) => void;
  onRefresh?: () => void;
  onClickPathItem?: (item: PathItemType) => void;
  onBackToParent?: () => void;
  onTabChange?: (value: string) => void;
} & SearchBarProps) => {
  const router = useRouter();
  const [stateValue, setStateValue] = useState('');
  const [currentTab, setCurrentTab] = useState(query.type == 1 ? '1' : '2');
  const { userInfo } = useUserStore();

  const breadcrumbList = useMemo(() => {
    const list: (BreadcrumbItemType & { pathItem?: PathItemType })[] = [];

    const spaceItem = path?.reduce(
      (pre: PathItemType | undefined, it: PathItemType) =>
        it.type === PathItemTypeEnum.space ? it : pre,
      undefined
    ) as PathSpaceType;

    const folderPath = path?.filter(
      (it: PathItemType) => it.type === PathItemTypeEnum.file
    ) as FilePathType;

    if (folderPath?.length) {
      list.push({
        label: '返回上一级',
        isBack: true
      });
    }

    list.push({
      label: spaceItem?.space?.spaceName || '待审核资料',
      pathItem: spaceItem,
      clickable: true
    });

    folderPath?.forEach((pathItem, index) => {
      list.push({
        label: pathItem.file.fileName!,
        pathItem,
        clickable: index < folderPath.length - 1
      });
    });
    return list;
  }, [path]);

  const handleBreadcrumbClick = (item: BreadcrumbItemType & { pathItem?: PathItemType }) => {
    if (item.label === '待审核资料') {
      onBackToParent?.();
      onSearch && onSearch({ ...query, type: currentTab === '2' ? 1 : 2 });
    } else if (item.pathItem) {
      onClickPathItem?.(item.pathItem);
    }
  };

  const onAudit = () => {
    router.push('/cloud/list');
  };

  const onCurrentTab = (key: string) => {
    setCurrentTab(key);
    onTabChange!(key);
    onSearch && onSearch({ ...query, type: currentTab === '2' ? 1 : 2 });
  };

  return (
    <Box w="100%" mb={respDims(16)} ml={respDims(10)}>
      <Flex mt={respDims(24)} mb={respDims(16)} alignItems="center" w="100%">
        {breadcrumbList.length === 1 && (
          <Box
            onClick={onAudit}
            cursor="pointer"
            color="primary.500"
            display="flex"
            alignItems="center"
          >
            <SvgIcon name="chevronLeft" w={respDims('18fpx')} h={respDims('18fpx')} /> 返回上一级
            <SvgIcon
              name="slash"
              w={respDims('16fpx')}
              h={respDims('16fpx')}
              mx={respDims(4)}
              color="#909399"
            />
          </Box>
        )}
        <Breadcrumb
          list={breadcrumbList}
          onClickItem={handleBreadcrumbClick}
          onBack={onBackToParent}
        />
      </Flex>

      <Flex alignItems="stretch" flexShrink="0" ml={respDims(10)}>
        {tabs
          .filter((tab) => userInfo?.roleType !== UserRoleEnum.Admin || tab === tabs[0])
          .map((tab) => (
            <Box
              key={tab.value}
              pb="10px"
              m="10px 32px 16px 0"
              position="relative"
              {...(tab.value === currentTab
                ? {
                    color: 'primary.500',
                    _after: {
                      position: 'absolute',
                      content: '""',
                      left: '0',
                      right: '0',
                      bottom: '-1px',
                      w: '100%',
                      height: '2px',
                      bgColor: 'primary.500'
                    }
                  }
                : {
                    color: '#4E5969'
                  })}
              fontSize="14px"
              fontWeight="bold"
              cursor="pointer"
              onClick={() => onCurrentTab(tab.value)}
            >
              {tab.name}
            </Box>
          ))}
      </Flex>

      <Flex mt={respDims(10)} ml={respDims(10)}>
        <Select
          defaultValue={stateValue}
          style={{ width: '150px' }}
          className={styles['custom-select']}
          onChange={(e) =>
            onSearch?.({
              auditorStatus: e,
              cloudSpaceId: cloudSpaceId ? cloudSpaceId : undefined,
              type: currentTab === '2' ? 1 : 2
            })
          }
          dropdownStyle={{ zIndex: 2000 }}
          popupMatchSelectWidth={false}
          placeholder="请选择"
        >
          {stateOptions.map((option) => (
            <Select.Option key={option.value} value={option.value}>
              {option.label}
            </Select.Option>
          ))}
        </Select>

        <SearchInput
          mr="auto"
          ml="10px"
          type={SearchTypeEnum.file}
          onSearch={(e) =>
            onSearch?.({
              keyword: e,
              cloudSpaceId: cloudSpaceId ? cloudSpaceId : undefined,
              type: currentTab === '2' ? 1 : 2
            })
          }
        />
      </Flex>
    </Box>
  );
};

export default Header;
