import AppDetail from '@/components/AppDetail';
import { DataSource } from '@/constants/common';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { serviceSideProps } from '@/utils/i18n';

const ClientAppDetail = (props: {
  appType?: AppTypeEnum;
  finalAppId: string;
  isAdmin: '1' | '0';
}) => {
  return (
    <>
      <AppDetail {...props}></AppDetail>
    </>
  );
};

export async function getServerSideProps(context: any) {
  const { source, isAdmin, id } = context.query;

  return {
    props: {
      appType: context.query?.appType || '',
      finalAppId: context.query?.finalAppId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default ClientAppDetail;
