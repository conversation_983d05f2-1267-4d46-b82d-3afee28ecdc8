import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Flex,
  Button,
  InputGroup,
  Input,
  InputRightElement,
  Image,
  MenuButton,
  Center,
  Tooltip,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { deleteClientApp } from '@/api/app';
import { serviceSideProps } from '@/utils/i18n';
import MyIcon from '@/components/LegacyIcon';
import AdvancedModal from '@/components/AppModal/advanced';
import SimpleModal from '@/components/AppModal/simple';
import { useAppStore } from '@/store/useAppStore';
import { respDims } from '@/utils/chakra';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { APP_ICON, DataSource } from '@/constants/common';
import { AppListItemType } from '@/types/api/app';
import { Toast } from '@/utils/ui/toast';
import { throttle } from 'lodash';
import {
  getTenantSceneList,
  getCenterValidAppWorkflow,
  getAppCenterRecentlyUsedList,
  getAppCenterHomePageUsedList,
  getOtherAppList,
  reportAppsVisit,
  commonAppList,
  setCommonApp,
  rmCommonApp,
  addToNavbarCommonAppList,
  removeFromNavbarCommonAppList
} from '@/api/scene';
import { SceneType, CommonAppType } from '@/types/api/scene';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { MessageBox } from '@/utils/ui/messageBox';
import { motion } from 'framer-motion';
import { ModeTypeEnum } from '@/constants/api/app';
import { getSampleAppInfo } from '@/utils/app';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { ChatItemType } from '@/fastgpt/global/core/chat/type';
import { getToken } from '@/utils/auth';
import { PermissionTypeEnum } from '@/constants/permission';
import { useSystemStore } from '@/store/useSystemStore';
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';
import { useTenantStore } from '@/store/useTenantStore';
import SettingModal from './components/SettingModal';
import { EventNameEnum, eventBus } from '@/utils/eventbus';

type ConfirmDialogType = {
  destroy: () => void;
};

const AppListPc = ({ sceneId }: { sceneId?: string }) => {
  const router = useRouter();
  const { type } = router.query;
  const { myApps, loadMyApps } = useAppStore();
  const [scenesList, setScenesList] = useState<SceneType[]>([]);
  const [recentlyUsedList, setRecentlyUsedList] = useState<AppListItemType[]>([]);
  const [commonAppListData, setCommonAppListData] = useState<CommonAppType[]>([]);
  const [homePageUsedList, setHomePageUsedList] = useState<AppListItemType[]>([]);
  const [filterSceneId, setFilterSceneId] = useState(sceneId || 'recentlyUsed');
  const [filterText, setFilterText] = useState('');
  const gridRef = useRef<HTMLDivElement>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { setDeepEditChatItem } = useDeepEditStore();
  const [isDomain, setIsDomain] = useState(false);
  const { isPc } = useSystemStore();
  const { tenant } = useTenantStore();
  const [isSticky, setIsSticky] = useState(false);
  const stickyHeaderRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isBaoAnDomain()) {
      setIsDomain(true);
    } else {
      setIsDomain(false);
    }
  }, [window.location]);

  const { data: otherAppsData } = useQuery({
    queryKey: ['otherApps'],
    queryFn: getOtherAppList
  });
  const otherApps = otherAppsData || [];

  const presentScenes = useMemo(() => {
    const scenes = [
      { name: '我的应用', sceneId: 'personage' },
      ...scenesList.map((it) => ({ ...it, sceneId: it.id }))
    ];

    return scenes;
  }, [scenesList, isDomain]);

  const combinedList = useMemo(() => {
    return [
      {
        name: '我的应用',
        sceneId: 'personage',
        apps: myApps.filter(({ source }) => source === DataSource.Personal)
      },
      ...scenesList.map((scene) => {
        const apps = myApps.filter((item) =>
          item.labelList?.some((it) => String(it.tenantSceneId) === String(scene.id))
        );
        if (scene.name === 'AI评价' && otherApps.length > 0) {
          apps.push(...otherApps);
        }
        return {
          name: scene.name,
          sceneId: scene.id,
          apps
        };
      })
    ];
  }, [scenesList, myApps]);

  const { openOverlay } = useOverlayManager();

  const onCopySimple = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: true,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => {
          setFilterSceneId('personage');
          loadMyApps(true);
          router.push({
            pathname: '/zeroCode',
            query: {
              tab: 'my'
            }
          });
        }
      }
    });
  };

  const onCopyAdvanced = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: true,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => {
          setFilterSceneId('personage');
          loadMyApps(true);
          router.push({
            pathname: '/zeroCode',
            query: {
              tab: 'my'
            }
          });
        }
      }
    });
  };

  const scrollToElement = (id: string) => {
    const element = document.getElementById(id);
    if (element && scrollRef.current && stickyHeaderRef.current) {
      const containerTop = scrollRef.current.getBoundingClientRect().top;
      const elementTop = element.getBoundingClientRect().top;
      const offset =
        elementTop -
        containerTop +
        scrollRef.current.scrollTop -
        stickyHeaderRef.current.clientHeight;
      scrollRef.current.scrollTo({ top: offset, behavior: 'smooth' });
    }
  };

  const handleScrollAndSetFilter = (id: string) => {
    setFilterSceneId(id);
    scrollToElement(id);
  };

  const handleAgentClick = (agentId: string) => {
    const personageData = myApps.filter(({ source }) => source === DataSource.Personal);
    if (agentId === 'personage' && personageData.length > 0) {
      handleScrollAndSetFilter(agentId);
      return;
    } else {
      const data = myApps.filter((item) =>
        item.labelList?.some((it) => String(it.tenantSceneId) === agentId)
      );

      if (data.length <= 0) {
        Toast.info('暂无应用');
        return;
      }
    }
    handleScrollAndSetFilter(agentId);
  };

  const checkScroll = useCallback(() => {
    if (!scrollRef.current) {
      return;
    }

    // 检测搜索框是否粘住顶部
    if (stickyHeaderRef.current) {
      const headerRect = stickyHeaderRef.current.getBoundingClientRect();

      const isSticky = headerRect.top <= 0;
      stickyHeaderRef.current.style.backgroundColor = isSticky
        ? 'rgba(255,255,255, .2)'
        : 'transparent';
      stickyHeaderRef.current.style.backdropFilter = isSticky ? 'blur(20px)' : 'blur(0px)';
      stickyHeaderRef.current.style.boxShadow = isSticky
        ? '0px 4px 17.1px 0px rgba(0, 0, 0, 0.07)'
        : '';
      setIsSticky(headerRect.top <= 0);
    }
  }, []);

  const backToTop = () => {
    scrollRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleWorkPanelOpen = (appId: string, appTaskTypeId: string) => {
    setDeepEditChatItem({} as ChatItemType);
    window.open(`/deepeditor?appId=${appId}&appTaskTypeId=${appTaskTypeId}&init=1`, '_blank');
  };

  const getVisibleSceneId = useCallback((): string => {
    // 获取滚动容器元素
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return filterSceneId;

    // 获取容器的视口信息
    const containerRect = scrollContainer.getBoundingClientRect();
    const containerTop = containerRect.top;
    const containerHeight = containerRect.height;

    // 计算容器顶部5%的阈值位置（相对容器）
    const visibleThreshold = containerHeight * 0.05;

    // 偏移量
    const offset = stickyHeaderRef.current?.clientHeight || 150;

    // 遍历所有场景区块
    const scenes = Array.from(scrollContainer.querySelectorAll<HTMLElement>('[data-scene-id]'));

    let closestScene: { id?: string; distance?: number } = {};

    scenes.forEach((scene) => {
      const sceneRect = scene.getBoundingClientRect();
      const sceneId = scene.dataset.sceneId;

      // 计算元素相对于容器的位置，并减去偏移
      const relativeTop = sceneRect.top - containerTop - offset;
      const relativeBottom = sceneRect.bottom - containerTop - offset;

      // 可见性判断（进入容器顶部5%区域）
      const isVisible =
        relativeTop <= visibleThreshold && // 顶部进入可视区
        relativeBottom >= 0; // 元素底部未完全滚出容器

      if (isVisible && sceneId) {
        const distance = Math.abs(relativeTop);
        if (closestScene?.distance === undefined || distance < closestScene.distance) {
          closestScene = { id: sceneId, distance };
        }
      }
    });

    return closestScene?.id || filterSceneId;
  }, [filterSceneId]);

  const filteredApps = useMemo(() => {
    if (!filterText) return [];
    const regex = new RegExp(filterText, 'i');

    return combinedList
      .filter((scene) => scene.sceneId !== 'recentlyUsed') // 过滤掉最近使用的数据
      .flatMap((scene) => scene.apps)
      .filter((app) => app && regex.test(app.name));
  }, [filterText, combinedList]);

  const { openOverlay: openOverlaySetting } = useOverlayManager();

  const onCommonSetting = () => {
    openOverlaySetting({
      Overlay: SettingModal,
      props: {
        dataSource: commonAppListData,
        onSuccess: (id) => {
          onRmCommonApp(id);
        },
        onFinally: () => {
          refetch();
          loadAppsRefetch();
          eventBus.emit(EventNameEnum.commonAppListToNavbar);
        }
      }
    });
  };

  /* 加载模型 */
  const { isFetching, refetch: loadAppsRefetch } = useQuery(['loadApps'], () => loadMyApps(true), {
    refetchOnMount: true
  });

  // 添加到导航栏
  const onAddToNavbarCommonAppList = (id: string | number) => {
    addToNavbarCommonAppList({ id }).then((res) => {
      Toast.success('已添加');
      refetch();
      loadAppsRefetch();
      eventBus.emit(EventNameEnum.commonAppListToNavbar);
    });
  };

  // 从导航栏移除
  const onRemoveFromNavbarCommonAppList = (id: string | number) => {
    removeFromNavbarCommonAppList({ id }).then((res) => {
      Toast.success('已移除');
      refetch();
      loadAppsRefetch();
      eventBus.emit(EventNameEnum.commonAppListToNavbar);
    });
  };

  // 设为常用
  const onSetCommonApp = (id: string) => {
    setCommonApp({ id }).then((res) => {
      Toast.success('已添加');
      refetch();
      loadAppsRefetch();
    });
  };

  // 移除常用
  const onRmCommonApp = (id: string) => {
    rmCommonApp({ id }).then((res) => {
      Toast.success('已移除');
      refetch();
      loadAppsRefetch();
      eventBus.emit(EventNameEnum.commonAppListToNavbar);
    });
  };

  const dynamicMenuList = (app: AppListItemType) => {
    const simple = !app.isCommonApp
      ? {
          label: '设为常用',
          icon: <SvgIcon name="addCircle" w="16px" h="16px" />,
          onClick: () => onSetCommonApp(app.id)
        }
      : {
          label: '移除常用',
          icon: <SvgIcon name="removeCircle" w="16px" h="16px" />,
          onClick: () => onRmCommonApp(app.id)
        };
    const list = [
      simple,
      {
        label: '复制',
        icon: <SvgIcon name="copy" w="16px" h="16px" />,
        onClick: () => (app.mode === 1 ? onCopySimple(app) : onCopyAdvanced(app))
      }
    ];
    return list;
  };

  const dynamicCommonMenuList = (app: AppListItemType, args: Omit<CommonAppType, 'tenantApp'>) => {
    const simple = !args.isNavbar
      ? {
          label: '添加到导航栏',
          icon: <SvgIcon name="anchor" w="16px" h="16px" />,
          onClick: () => onAddToNavbarCommonAppList(String(args.id))
        }
      : {
          label: '从导航栏移除',
          icon: <SvgIcon name="anchorCancel" w="16px" h="16px" />,
          onClick: () => onRemoveFromNavbarCommonAppList(String(args.id))
        };
    const list = [
      simple,
      {
        label: '排序',
        icon: <SvgIcon name="sort" w="16px" h="16px" />,
        onClick: () => onCommonSetting()
      },
      {
        label: '移除',
        icon: <SvgIcon name="removeCircle" w="16px" h="16px" />,
        onClick: () => onRmCommonApp(app.id)
      }
    ];
    return list;
  };

  useQuery(['tenantDetail'], () => getTenantSceneList(), {
    onSuccess: (data) => {
      if (data) {
        setScenesList(data);
      }
      if (type) {
        let targetSceneId = '';
        if (type === '1') targetSceneId = '34367';
        if (type === '2') targetSceneId = '34363';

        if (targetSceneId) {
          setTimeout(() => {
            handleScrollAndSetFilter(targetSceneId);
          }, 100);
        }
      }
    }
  });

  useQuery(['appCenterHomePageUsedList'], () => getAppCenterHomePageUsedList(), {
    onSuccess: (data) => {
      if (data) {
        setHomePageUsedList(data);
      }
    }
  });

  // useQuery(['recentlyUsedList'], () => getAppCenterRecentlyUsedList(), {
  //   onSuccess: (data) => {
  //     if (data) {
  //       setRecentlyUsedList(data);
  //     }
  //   }
  // });

  const { refetch } = useQuery(['commonAppList'], () => commonAppList(), {
    onSuccess: (data) => {
      if (data) {
        setCommonAppListData(data.filter((item) => item.tenantApp));
      }
    }
  });

  useEffect(() => {
    // 只有在停止滚动后，才更新高亮的场景
    const scrollHandler = () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      scrollTimeoutRef.current = setTimeout(() => {
        const visibleId = getVisibleSceneId();
        if (visibleId && visibleId !== filterSceneId) {
          setFilterSceneId(visibleId);
        }
      }, 200);
    };

    scrollRef.current?.addEventListener('scroll', scrollHandler);
    return () => {
      scrollRef.current?.removeEventListener('scroll', scrollHandler);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [getVisibleSceneId, filterSceneId]);

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollTop = 0;
    }
  }, [filterSceneId]);

  const trackClick = (tenantAppId: string) => {
    console.log(`NavItem clicked: ${tenantAppId}`);
    reportAppsVisit({ tenantAppId });
  };
  useEffect(() => {
    if (type) {
      if (type === '1') setFilterSceneId('34367');
      if (type === '2') setFilterSceneId('34363');
    }
  }, [type]);

  useEffect(() => {
    eventBus.on(EventNameEnum.navbarToCommonAppList, () => {
      refetch();
    });
    return () => eventBus.off(EventNameEnum.navbarToCommonAppList);
  }, []);

  return (
    <Flex
      flexDir="column"
      h="100%"
      pl={respDims(46)}
      pr={respDims(46)}
      backgroundImage={
        tenant?.functionBackgroundImgUrl
          ? tenant.functionBackgroundImgUrl
          : '/imgs/app/app_center_bg4.png'
      }
      backgroundRepeat="no-repeat"
      backgroundSize="cover"
      alignItems="center"
      overflowY="auto"
      pos="relative"
      ref={scrollRef}
      onScroll={checkScroll}
    >
      <Flex
        alignItems="center"
        flexDirection="column"
        justifyContent={'center'}
        mt={respDims(46)}
        mb={respDims(26)}
      >
        <Box
          color="#303133"
          textAlign="center"
          fontSize={respDims('26fpx')}
          fontWeight="700"
          overflow="hidden" // 确保文本不会溢出
          textOverflow="ellipsis" // 使用省略号表示溢出的文本
          whiteSpace="nowrap" // 防止文本换行
          mb={respDims(26)}
        >
          一起来发掘高质量的AI原生应用
        </Box>
      </Flex>

      <Box w={respDims(1235)} ml={respDims(20)}>
        <Flex w={'full'} justifyContent={'space-between'} alignItems={'center'}>
          <Box fontSize={respDims(24)} fontWeight="800" color="#303133">
            常用应用
          </Box>
          {commonAppListData && commonAppListData.length > 0 && (
            <Flex
              alignItems={'center'}
              color={'#4E5969'}
              gap={'6px'}
              cursor={'pointer'}
              onClick={onCommonSetting}
            >
              <SvgIcon name="settings" w="15px" h="15px" />
              <Box fontSize={respDims(16)}>设置</Box>
            </Flex>
          )}
        </Flex>
        {commonAppListData && commonAppListData.length > 0 ? (
          <Grid
            ref={gridRef}
            gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
            gridGap={respDims(20)}
            my={respDims(20)}
          >
            {commonAppListData.map(({ tenantApp: app, ...args }) => (
              <Flex
                key={app.id}
                h={respDims(105)}
                p={respDims(20)}
                flexDir="column"
                position="relative"
                cursor="pointer"
                userSelect="none"
                borderRadius={respDims(14)}
                bgColor="#ffffff"
                // boxShadow="0px 0px 12px 0px rgba(105,105,105,0.07)"
                transition="all 0.3s ease-in-out" // 添加过渡效果
                _hover={{
                  transform: 'translateY(-5px)', // 上移效果
                  zIndex: '9',
                  boxShadow: '0px 9px 15.6px 0px rgba(163, 163, 163, 0.11)',
                  '& .app-menu': {
                    display: 'flex'
                  }
                }}
                onClick={() => {
                  trackClick(app.id);
                  if (app.linkUrl) {
                    const encodedQuery = encodeURIComponent(getToken());
                    window.open(`${app.linkUrl}?token=${encodedQuery}`, '_blank');
                    return;
                  }
                  if (app.appTaskTypeId) {
                    handleWorkPanelOpen(app.id, app.appTaskTypeId);
                  } else {
                    router.push({
                      pathname: '/home',
                      query: {
                        appId: app.id,
                        activeRoute: '/app/list',
                        sceneId: (app.labelList?.length && app.labelList![0].tenantSceneId) || ''
                      }
                    });
                  }
                }}
              >
                <Flex justifyContent="space-between">
                  <Flex alignItems="center">
                    <Image
                      w={respDims(64)}
                      h={respDims(64)}
                      src={app.avatarUrl || APP_ICON}
                      alt=""
                      borderRadius="50%"
                    />

                    <Flex flex="1" flexDir="column" ml={respDims(16)}>
                      <Box
                        flex="0 0 auto"
                        color="#000000"
                        fontSize={respDims(18)}
                        fontWeight="bold"
                        wordBreak="break-all"
                        noOfLines={1}
                        maxW={respDims(250)}
                      >
                        {app.name}
                      </Box>

                      {app.source === DataSource.Tenant && (
                        <Flex alignItems="center" mt={respDims(12)}>
                          <SvgIcon mr="6px" name="appExclusive" w="14px" h="14px" />
                          <Box
                            ml={respDims(2)}
                            fontSize={respDims(14)}
                            color="#909399"
                            fontWeight="400"
                          >
                            专属
                          </Box>
                        </Flex>
                      )}
                      {app.source === DataSource.Offical && (
                        <Flex alignItems="center" mt={respDims(12)}>
                          <SvgIcon mr="6px" name="appAuthority" w="14px" h="14px" />
                          <Box fontSize={respDims(14)} color="#909399" fontWeight="400">
                            官方
                          </Box>
                        </Flex>
                      )}
                      {app.source === DataSource.Personal && (
                        <Flex alignItems="center" mt={respDims(12)}>
                          <SvgIcon color="#c1c1c3" mr="6px" name="user" w="14px" h="14px" />
                          <Box
                            ml={respDims(2)}
                            fontSize={respDims(14)}
                            color="#909399"
                            fontWeight="400"
                          >
                            个人
                          </Box>
                        </Flex>
                      )}
                    </Flex>
                  </Flex>
                  {!app.linkUrl &&
                    (app.source === DataSource.Personal ||
                      app.config === PermissionTypeEnum.Public) && (
                      <MyMenu
                        trigger="hover"
                        offset={[20, 0]}
                        width={20}
                        Button={
                          <MenuButton
                            className="app-menu"
                            display="none"
                            position="absolute"
                            top="0"
                            right="0"
                            w={respDims(30)}
                            h={respDims(30)}
                            _hover={{
                              bg: 'myWhite.600',
                              borderRadius: '2px'
                            }}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Center>
                              <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                            </Center>
                          </MenuButton>
                        }
                        menuList={dynamicCommonMenuList(app, args)}
                        menuListStyles={{
                          boxShadow:
                            '0px 4px 3px 0px rgba(0, 0, 0, 0.05), 0px 2px 2px 0px rgba(0, 0, 0, 0.00)',
                          background: '#FFF',
                          border: '1px solid #E5E7EB',
                          borderRadius: '8px'
                        }}
                      />
                    )}
                </Flex>
              </Flex>
            ))}
          </Grid>
        ) : (
          <Grid
            gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
            gridGap={respDims(20)}
          >
            <Popover placement="bottom" trigger="hover">
              <PopoverTrigger>
                <Flex
                  p={respDims(20)}
                  my={respDims(20)}
                  h={respDims(105)}
                  flexDir="column"
                  color="primary.500"
                  position="relative"
                  cursor="pointer"
                  userSelect="none"
                  borderRadius={respDims(14)}
                  bgColor="#fff"
                  border="1px dashed"
                  borderColor="primary.500"
                  onClick={() => {
                    handleAgentClick(presentScenes[0].sceneId);
                  }}
                >
                  <Flex flex="1" justifyContent="center" alignItems="center">
                    <Flex
                      rounded={'full'}
                      bgColor={'#F2EDFF'}
                      align={'center'}
                      justify={'center'}
                      p={'4px'}
                    >
                      <SvgIcon name="plus" w={respDims(26)} h={respDims(26)} color={'#7D4DFF'} />
                    </Flex>
                    <Box fontSize={respDims(18)} color={'black'} fontWeight="bold" ml={'10px'}>
                      添加常用应用
                    </Box>
                  </Flex>
                </Flex>
              </PopoverTrigger>
              <PopoverContent borderRadius={respDims(14)} w={'full'}>
                <PopoverArrow />
                <Flex
                  flexDirection="column"
                  bgColor="#fff"
                  justify={'center'}
                  p={respDims(10)}
                  borderRadius={respDims(14)}
                >
                  <Box fontSize="14px" fontWeight="400" mb={'10px'}>
                    当前还没有常用应用哦，快去添加吧～
                  </Box>
                  <Image src={'/imgs/app/app_center_config_default.png'} alt="" />
                </Flex>
              </PopoverContent>
            </Popover>
          </Grid>
        )}
      </Box>

      <Flex
        align={'center'}
        justify={'center'}
        direction={'column'}
        ref={stickyHeaderRef}
        pos="sticky"
        top="0"
        zIndex={isSticky ? 9 : 7}
        w={'110%'}
      >
        <Flex
          justifyContent={'space-between'}
          alignItems={'center'}
          w={respDims(1235)}
          ml={respDims(20)}
          mt={respDims(10)}
          mb={respDims(20)}
        >
          <Box fontSize={respDims(24)} fontWeight="800" color="#303133">
            全部应用
          </Box>
          <Box w={respDims(400)} display={'flex'} alignItems={'center'}>
            <InputGroup>
              <Input
                placeholder="请输入关键词,搜索应用"
                bgColor="rgba(255,255,255,1)"
                fontSize="14px"
                border="none"
                borderRadius={respDims(50)}
                _placeholder={{ fontSize: '14px', color: '#4E5969' }}
                value={filterText}
                h="38px"
                mx={respDims(26)}
                onChange={(e) => setFilterText(e.target.value)}
              />
              <InputRightElement alignItems="center">
                <SvgIcon mr={respDims(48)} name="appAgentSearch" w="20px" h="20px" />
              </InputRightElement>
            </InputGroup>

            {filterText ? (
              <Box
                ml={respDims(16)}
                mr={respDims(15)}
                color="#979dab"
                w="40px"
                cursor="pointer"
                onClick={() => {
                  setFilterText('');
                }}
              >
                取消
              </Box>
            ) : (
              <></>
            )}
          </Box>
        </Flex>

        <Flex
          align={'center'}
          justify={'space-between'}
          w={respDims(1235)}
          ml={respDims(20)}
          zIndex="1"
          pb={respDims(16)}
        >
          {!filterText && (
            <>
              <Flex flexDir="column" zIndex="20" borderRadius="15px" justifyContent="center">
                <Flex alignItems="center" overflowX="auto" overflowY="hidden" w="full">
                  {presentScenes.map((it, i) => (
                    <>
                      {it.sceneId && (
                        <Box
                          key={it.sceneId && it.sceneId + i}
                          px={respDims(20)}
                          py={respDims(8)}
                          borderRadius={respDims(8)}
                          cursor="pointer"
                          userSelect="none"
                          fontSize={respDims(20)}
                          position="relative"
                          whiteSpace="nowrap"
                          bg="rgba(0, 0, 0, 0.05)"
                          textAlign="center"
                          mr={respDims(20)}
                          _last={{
                            marginRight: 0
                          }}
                          letterSpacing="1px"
                          {...(it.sceneId === filterSceneId && {
                            color: '#fff',
                            fontWeight: '600',
                            bg: 'linear-gradient(90deg, #DC7EFF 0%, #601CFF 100%)'
                          })}
                          onClick={() => {
                            it.sceneId && handleAgentClick(it.sceneId.toString());
                          }}
                        >
                          {it.name}
                        </Box>
                      )}
                    </>
                  ))}
                </Flex>
              </Flex>
            </>
          )}
        </Flex>
      </Flex>
      <Box
        p={respDims(8)}
        ml={respDims(30)}
        mr={respDims(15)}
        mb={respDims(20)}
        // overflowX="hidden"
        // overflowY="auto"
        h="100%"
        w={respDims(1245)}
      >
        {filterText ? (
          <Grid
            ref={gridRef}
            gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
            gridGap={respDims(20)}
          >
            {filteredApps.length > 0 ? (
              <>
                {filteredApps.map((app) => (
                  <>
                    {app && (
                      <Flex
                        key={app.id}
                        h={respDims(146, 112)}
                        p={respDims(20)}
                        flexDir="column"
                        position="relative"
                        cursor="pointer"
                        userSelect="none"
                        borderRadius={respDims(14)}
                        bgColor="#ffffff"
                        boxShadow="0px 0px 12px 0px rgba(105,105,105,0.07)"
                        transition="all 0.3s ease-in-out" // 添加过渡效果
                        _hover={{
                          transform: 'translateY(-5px)', // 上移效果
                          zIndex: '8',
                          boxShadow: '0px 9px 15.6px 0px rgba(163, 163, 163, 0.11)',
                          '& .app-menu': {
                            display: 'flex'
                          }
                        }}
                        onClick={() => {
                          trackClick(app.id);
                          if (app.linkUrl) {
                            const encodedQuery = encodeURIComponent(getToken());
                            window.open(`${app.linkUrl}?token=${encodedQuery}`, '_blank');
                            return;
                          }
                          if (app?.appTaskTypeId) {
                            handleWorkPanelOpen(app?.id, app?.appTaskTypeId);
                            return;
                          }
                          router.push({
                            pathname: '/home',
                            query: {
                              appId: app.id,
                              sceneId:
                                (app.labelList?.length && app.labelList![0].tenantSceneId) || ''
                            }
                          });
                        }}
                      >
                        <Flex justifyContent="space-between">
                          <Flex alignItems="center">
                            <Image
                              w={respDims(64)}
                              h={respDims(64)}
                              src={app.avatarUrl || APP_ICON}
                              alt=""
                              borderRadius="50%"
                            />

                            <Flex flex="1" flexDir="column" ml={respDims(16)}>
                              <Box
                                flex="0 0 auto"
                                color="#000000"
                                fontSize={respDims(16)}
                                fontWeight="bold"
                                wordBreak="break-all"
                                noOfLines={1}
                                maxW={respDims(250)}
                              >
                                {app.name}
                              </Box>

                              {app.source === DataSource.Tenant && (
                                <Flex alignItems="center" mt={respDims(12)}>
                                  <SvgIcon mr="6px" name="appExclusive" w="14px" h="14px" />
                                  <Box
                                    ml={respDims(2)}
                                    fontSize={respDims(14)}
                                    color="#909399"
                                    fontWeight="400"
                                  >
                                    专属
                                  </Box>
                                </Flex>
                              )}
                              {app.source === DataSource.Offical && (
                                <Flex alignItems="center" mt={respDims(12)}>
                                  <SvgIcon mr="6px" name="appAuthority" w="14px" h="14px" />
                                  <Box fontSize={respDims(14)} color="#909399" fontWeight="400">
                                    官方
                                  </Box>
                                </Flex>
                              )}
                              {app.source === DataSource.Personal && (
                                <Flex alignItems="center" mt={respDims(12)}>
                                  <SvgIcon color="#c1c1c3" mr="6px" name="user" w="14px" h="14px" />
                                  <Box
                                    ml={respDims(2)}
                                    fontSize={respDims(14)}
                                    color="#909399"
                                    fontWeight="400"
                                  >
                                    个人
                                  </Box>
                                </Flex>
                              )}
                            </Flex>
                          </Flex>
                          {!app.linkUrl &&
                            (app.source === DataSource.Personal ||
                              app.config === PermissionTypeEnum.Public) && (
                              <MyMenu
                                trigger="hover"
                                offset={[20, 0]}
                                width={20}
                                Button={
                                  <MenuButton
                                    className="app-menu"
                                    display="none"
                                    position="absolute"
                                    top="0"
                                    right="0"
                                    w={respDims(30)}
                                    h={respDims(30)}
                                    _hover={{
                                      bg: 'myWhite.600',
                                      borderRadius: '2px'
                                    }}
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <Center>
                                      <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                                    </Center>
                                  </MenuButton>
                                }
                                menuList={dynamicMenuList(app)}
                                menuListStyles={{
                                  boxShadow:
                                    '0px 4px 3px 0px rgba(0, 0, 0, 0.05), 0px 2px 2px 0px rgba(0, 0, 0, 0.00)',
                                  background: '#FFF',
                                  border: '1px solid #E5E7EB',
                                  borderRadius: '8px'
                                }}
                              />
                            )}
                        </Flex>

                        <Tooltip
                          bg="#333"
                          p="10px"
                          label={app.intro}
                          aria-label="space-names-tooltip"
                        >
                          <Box
                            mt={respDims(13)}
                            color="#606266"
                            fontSize={respDims(15)}
                            maxH="90px"
                            minH="30px"
                            noOfLines={2}
                            overflow="hidden"
                            textOverflow="ellipsis"
                          >
                            {app.intro || '暂无介绍'}
                          </Box>
                        </Tooltip>
                      </Flex>
                    )}
                  </>
                ))}
              </>
            ) : (
              <Flex
                direction="column"
                justifyContent="center"
                w={respDims(1245)}
                mt={respDims(160)}
                h="100%"
              >
                <Center>
                  <MyIcon name="appListEmpty" w={'100px'} h={'100px'} color={'transparent'} />
                </Center>
                <Center
                  m="20px 0 8px 0"
                  color="#303133"
                  fontWeight="500"
                  fontSize="18px"
                  textAlign="center"
                >
                  没有搜索到匹配到应用，换个关键词吧～
                </Center>
              </Flex>
            )}
          </Grid>
        ) : (
          <Box>
            {combinedList.length > 0 &&
              combinedList
                .filter((item) => item.apps && item.apps.length > 0)
                .map((scene, index) => (
                  <Box
                    key={scene.sceneId}
                    position="relative"
                    data-scene-id={String(scene.sceneId)}
                  >
                    <Flex mt={index !== 0 ? respDims(20) : 0} mb={respDims(32)}>
                      <Box position="absolute" top={respDims(0)} id={String(scene.sceneId)}></Box>
                      <Box fontSize={respDims(24)} fontWeight="500" color="#303133">
                        {scene.name}
                      </Box>
                    </Flex>

                    <Grid
                      ref={gridRef}
                      gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
                      gridGap={respDims(20)}
                    >
                      {scene.apps!.map((app) => (
                        <Flex
                          key={app.id}
                          h={respDims(146, 112)}
                          p={respDims(20)}
                          flexDir="column"
                          position="relative"
                          cursor="pointer"
                          userSelect="none"
                          borderRadius={respDims(14)}
                          bgColor="#ffffff"
                          // boxShadow="0px 0px 12px 0px rgba(105,105,105,0.07)"
                          transition="all 0.3s ease-in-out" // 添加过渡效果
                          _hover={{
                            transform: 'translateY(-5px)', // 上移效果
                            zIndex: '8',
                            boxShadow: '0px 9px 15.6px 0px rgba(163, 163, 163, 0.11)',
                            '& .app-menu': {
                              display: 'flex'
                            }
                          }}
                          onClick={() => {
                            trackClick(app.id);
                            if (app.linkUrl) {
                              const encodedQuery = encodeURIComponent(getToken());
                              window.open(`${app.linkUrl}?token=${encodedQuery}`, '_blank');
                              return;
                            }
                            if (app.appTaskTypeId) {
                              handleWorkPanelOpen(app.id, app.appTaskTypeId);
                            } else {
                              router.push({
                                pathname: '/home',
                                query: {
                                  appId: app.id,
                                  activeRoute: '/app/list',
                                  sceneId:
                                    scene.sceneId === 'recentlyUsed'
                                      ? (app.labelList?.length &&
                                          app.labelList![0].tenantSceneId) ||
                                        ''
                                      : scene.sceneId
                                }
                              });
                            }
                          }}
                        >
                          <Flex justifyContent="space-between">
                            <Flex alignItems="center">
                              <Image
                                w={respDims(64)}
                                h={respDims(64)}
                                src={app.avatarUrl || APP_ICON}
                                alt=""
                                borderRadius="50%"
                              />

                              <Flex flex="1" flexDir="column" ml={respDims(16)}>
                                <Box
                                  flex="0 0 auto"
                                  color="#000000"
                                  fontSize={respDims(18)}
                                  fontWeight="bold"
                                  wordBreak="break-all"
                                  noOfLines={1}
                                  maxW={respDims(250)}
                                >
                                  {app.name}
                                </Box>

                                {app.source === DataSource.Tenant && (
                                  <Flex alignItems="center" mt={respDims(12)}>
                                    <SvgIcon mr="6px" name="appExclusive" w="14px" h="14px" />
                                    <Box
                                      ml={respDims(2)}
                                      fontSize={respDims(14)}
                                      color="#909399"
                                      fontWeight="400"
                                    >
                                      专属
                                    </Box>
                                  </Flex>
                                )}
                                {app.source === DataSource.Offical && (
                                  <Flex alignItems="center" mt={respDims(12)}>
                                    <SvgIcon mr="6px" name="appAuthority" w="14px" h="14px" />
                                    <Box fontSize={respDims(14)} color="#909399" fontWeight="400">
                                      官方
                                    </Box>
                                  </Flex>
                                )}
                                {app.source === DataSource.Personal && (
                                  <Flex alignItems="center" mt={respDims(12)}>
                                    <SvgIcon
                                      color="#c1c1c3"
                                      mr="6px"
                                      name="user"
                                      w="14px"
                                      h="14px"
                                    />
                                    <Box
                                      ml={respDims(2)}
                                      fontSize={respDims(14)}
                                      color="#909399"
                                      fontWeight="400"
                                    >
                                      个人
                                    </Box>
                                  </Flex>
                                )}
                              </Flex>
                            </Flex>
                            {!app.linkUrl &&
                              (app.source === DataSource.Personal ||
                                app.config === PermissionTypeEnum.Public) && (
                                <MyMenu
                                  trigger="hover"
                                  offset={[20, 0]}
                                  width={20}
                                  Button={
                                    <MenuButton
                                      className="app-menu"
                                      display="none"
                                      position="absolute"
                                      top="0"
                                      right="0"
                                      w={respDims(30)}
                                      h={respDims(30)}
                                      _hover={{
                                        bg: 'myWhite.600',
                                        borderRadius: '2px'
                                      }}
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      <Center>
                                        <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                                      </Center>
                                    </MenuButton>
                                  }
                                  menuList={dynamicMenuList(app)}
                                  menuListStyles={{
                                    boxShadow:
                                      '0px 4px 3px 0px rgba(0, 0, 0, 0.05), 0px 2px 2px 0px rgba(0, 0, 0, 0.00)',
                                    background: '#FFF',
                                    border: '1px solid #E5E7EB',
                                    borderRadius: '8px'
                                  }}
                                />
                              )}
                          </Flex>

                          <Tooltip
                            bg="#333"
                            p="10px"
                            label={app.intro}
                            aria-label="space-names-tooltip"
                          >
                            <Box
                              mt={respDims(13)}
                              color="#606266"
                              fontSize={respDims(15)}
                              maxH="90px"
                              minH="30px"
                              noOfLines={2}
                              overflow="hidden"
                              textOverflow="ellipsis"
                            >
                              {app.intro || '暂无介绍'}
                            </Box>
                          </Tooltip>
                        </Flex>
                      ))}
                    </Grid>
                  </Box>
                ))}
          </Box>
        )}
      </Box>
      {isSticky && (
        <Center
          pos={'fixed'}
          right={respDims(80)}
          bottom="20px"
          boxShadow="0px 4px 4px 0px rgba(205,205,205,0.25)"
          borderRadius="50%"
          cursor="pointer"
          border="1px solid #EDEDED"
          onClick={backToTop}
        >
          <SvgIcon
            name="chevronUp"
            color="#606266"
            p="12px"
            w={respDims('32rpx', 45, 45)}
            h={respDims('32rpx', 45, 45)}
          />
        </Center>
      )}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      sceneId: context.query.sceneId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default AppListPc;
