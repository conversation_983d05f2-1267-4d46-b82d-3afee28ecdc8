import React, { useState, useEffect } from 'react';
import { Box, Flex, Stack, Image, Text, Tooltip } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';

import { APP_ICON, DataSource } from '@/constants/common';
import { respDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import type { CommonAppType } from '@/types/api/scene';
import { sortCommonApp } from '@/api/scene';

import SvgIcon from '@/components/SvgIcon';
import MyModal from '@/components/MyModal';

const SettingModal: React.FC<{
  dataSource: CommonAppType[];
  onClose?: () => void;
  onSuccess?: (id: string) => void;
  onFinally?: () => void;
}> = ({ dataSource, onClose, onSuccess, onFinally }) => {
  const [appList, setAppList] = useState<CommonAppType[]>(dataSource || []);

  const onDragEnd = (result: any) => {
    if (!result.destination) return;
    const items = Array.from(appList as CommonAppType[]);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setAppList(items);

    const param = items.map((item, index) => ({
      id: item.id,
      sort: index
    }));
    sortCommonApp({ param }).then(() => {
      setAppList(items);
      onFinally?.();
    });
  };

  const sign = {
    [DataSource.Personal]: {
      name: '专属',
      icon: 'appExclusive'
    },
    [DataSource.Offical]: {
      name: '官方',
      icon: 'appAuthority'
    },
    [DataSource.Tenant]: {
      name: '个人',
      icon: 'user'
    }
  } as Record<string, Record<string, string>>;

  const Item: React.FC<any> = ({ index, app: { tenantApp, ...args } }) => {
    return (
      <Draggable draggableId={`${tenantApp?.name}-${tenantApp?.id}`} index={index}>
        {(provided, snapshot) => (
          <Flex
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            px={respDims(25)}
            py={respDims(15)}
            alignItems="center"
            justifyContent={'space-between'}
            style={{
              ...provided.draggableProps.style,
              borderRadius: '8px',
              background: '#F8FAFC'
            }}
          >
            <Box display="flex" alignItems="center" gap={respDims(20)}>
              <SvgIcon name="Frame" />
              <Flex alignItems="center" gap={respDims(20)}>
                <Image
                  w={respDims(64)}
                  h={respDims(64)}
                  src={tenantApp?.avatarUrl || APP_ICON}
                  alt=""
                  borderRadius="50%"
                />
                <Flex flexDir="column" justifyContent={'center'} gap={respDims(10)}>
                  <Text color={'black'} fontSize={respDims('16fpx')} fontWeight={'500'}>
                    {tenantApp?.name}
                  </Text>
                  {sign[tenantApp?.source] && (
                    <Flex alignItems="center">
                      <SvgIcon
                        mr="6px"
                        name={sign[tenantApp.source].icon as any}
                        w="14px"
                        h="14px"
                      />
                      <Box fontSize={respDims(14)} color="#909399" fontWeight="400">
                        {sign[tenantApp.source].name}
                      </Box>
                    </Flex>
                  )}
                </Flex>
              </Flex>
            </Box>
            <Box
              bg={'white'}
              color={'#E30004'}
              fontSize={respDims('14fpx')}
              p={'5px 16px'}
              borderRadius={'8px'}
              cursor={'pointer'}
              onClick={() => {
                const newList = appList.filter((item) => item.id !== args.id);
                setAppList(newList);
                onSuccess?.(tenantApp?.id);
              }}
            >
              移除
            </Box>
          </Flex>
        )}
      </Draggable>
    );
  };

  const List = () => {
    return (
      <Droppable droppableId="simple_settings">
        {(provided) => (
          <Stack
            spacing="15px"
            ref={provided.innerRef}
            minH={respDims(300)}
            {...(!appList.length && {
              align: 'center',
              justify: 'center',
              minH: respDims(400)
            })}
            {...provided.droppableProps}
          >
            {appList && appList.length ? (
              appList.map((item, index) => <Item key={item.id} index={index} app={item} />)
            ) : (
              <Flex align={'center'} justify={'center'} w={'full'} h={'full'} direction={'column'}>
                <Image
                  src={'/imgs/app/app_center_config_default.png'}
                  alt=""
                  borderRadius={'20px'}
                />
                <Box fontSize="14px" fontWeight="400" mt={'15px'}>
                  当前还没有常用应用哦，快去添加吧～
                </Box>
              </Flex>
            )}
            {provided.placeholder}
          </Stack>
        )}
      </Droppable>
    );
  };

  return (
    <MyModal
      title="常用应用设置"
      width={respDims(800)}
      maxW={respDims(800)}
      isOpen
      isCentered
      onClose={onClose}
    >
      <Stack px={respDims(30)} py={respDims(25)}>
        <DragDropContext onDragEnd={onDragEnd}>
          <List />
        </DragDropContext>
      </Stack>
    </MyModal>
  );
};

export default SettingModal;
