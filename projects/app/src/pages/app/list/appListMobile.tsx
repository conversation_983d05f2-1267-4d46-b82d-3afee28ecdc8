import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Flex,
  Button,
  InputGroup,
  Input,
  InputRightElement,
  Image,
  MenuButton,
  Center,
  Tooltip
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { deleteClientApp } from '@/api/app';
import { serviceSideProps } from '@/utils/i18n';
import MyIcon from '@/components/LegacyIcon';
import AdvancedModal from '@/components/AppModal/advanced';
import SimpleModal from '@/components/AppModal/simple';
import { useAppStore } from '@/store/useAppStore';
import { rpxDim, respDims } from '@/utils/chakra';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { APP_ICON, DataSource } from '@/constants/common';
import { AppListItemType } from '@/types/api/app';
import { Toast } from '@/utils/ui/toast';
import {
  getTenantSceneList,
  getCenterValidAppWorkflow,
  getAppCenterRecentlyUsedList,
  getAppCenterHomePageUsedList,
  getOtherAppList,
  commonAppList
} from '@/api/scene';
import { SceneType, CommonAppType } from '@/types/api/scene';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { MessageBox } from '@/utils/ui/messageBox';
import { motion } from 'framer-motion';
import { ModeTypeEnum } from '@/constants/api/app';
import { getSampleAppInfo } from '@/utils/app';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { ChatItemType } from '@/fastgpt/global/core/chat/type';
import { getToken } from '@/utils/auth';
import { PermissionTypeEnum } from '@/constants/permission';
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';
import { useSystemStore } from '@/store/useSystemStore';
import { useTenantStore } from '@/store/useTenantStore';
import { log } from 'console';

type ConfirmDialogType = {
  destroy: () => void;
};

const images = [
  {
    id: 1,
    bgImage: '/imgs/app/swipe_bg1.png',
    logoImage: '/imgs/app/swipe_logo1.png',
    title: '文生图',
    description: '根据文字描述，生成精美图片'
  },
  {
    id: 2,
    bgImage: '/imgs/app/swipe_bg2.png',
    logoImage: '/imgs/app/swipe_logo2.png',
    title: '教学设计',
    description: '协助教师一步生成完整教学设计'
  },
  {
    id: 4,
    bgImage: '/imgs/app/swipe_bg3.png',
    logoImage: '/imgs/app/swipe_logo3.png',
    title: '公文材料',
    description: '析辞必精，述情必显'
  }
];

const AppListMobile = ({ sceneId }: { sceneId?: string }) => {
  const router = useRouter();
  const { myApps, loadMyApps } = useAppStore();
  const [scenesList, setScenesList] = useState<SceneType[]>([]);
  // const [recentlyUsedList, setRecentlyUsedList] = useState<AppListItemType[]>([]);
  const [commonAppListData, setCommonAppListData] = useState<CommonAppType[]>([]);
  const [homePageUsedList, setHomePageUsedList] = useState<AppListItemType[]>([]);
  const [filterSceneId, setFilterSceneId] = useState(sceneId || 'recentlyUsed');
  const [filterText, setFilterText] = useState('');
  const gridRef = useRef<HTMLDivElement>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isSticky, setIsSticky] = useState(false);
  const { setDeepEditChatItem } = useDeepEditStore();
  const [isDomain, setIsDomain] = useState(false);
  const { isPc, feConfigs } = useSystemStore();
  const [, setRefresh] = useState(false);
  const { tenant } = useTenantStore();

  useEffect(() => {
    if (isBaoAnDomain()) {
      setIsDomain(true);
    } else {
      setIsDomain(false);
    }
  }, [window.location]);

  const { data: otherAppsData } = useQuery({
    queryKey: ['otherApps'],
    queryFn: getOtherAppList
  });
  const otherApps = otherAppsData || [];

  const presentScenes = useMemo(() => {
    const scenes = [
      { name: '常用应用', sceneId: 'commonuse' },
      { name: '我的应用', sceneId: 'personage' },
      ...scenesList.map((it) => ({ ...it, sceneId: it.id }))
    ];

    return scenes;
  }, [scenesList, commonAppListData, isDomain]);

  const combinedList = useMemo(() => {
    return [
      {
        name: '常用应用',
        sceneId: 'commonuse',
        apps: commonAppListData.map((its) => its.tenantApp)
      },
      {
        name: '我的应用',
        sceneId: 'personage',
        apps: myApps.filter(({ source }) => source === DataSource.Personal)
      },
      ...scenesList.map((scene) => {
        const apps = myApps.filter((item) =>
          item.labelList?.some((it) => String(it.tenantSceneId) === String(scene.id))
        );
        if (scene.name === 'AI评价' && otherApps.length > 0) {
          apps.push(...otherApps);
        }
        return {
          name: scene.name,
          sceneId: scene.id,
          apps
        };
      })
    ];
  }, [scenesList, commonAppListData, myApps]);

  const { openOverlay } = useOverlayManager();

  const onAddAdvanced = () => {
    openOverlay({
      Overlay: AdvancedModal,
      props: {
        isCopy: false,
        appModalParams: { sceneId: filterSceneId },
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onEditAdvanced = (app: AppListItemType) => {
    openOverlay({
      Overlay: AdvancedModal,
      props: {
        isCopy: false,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onAddSimple = () => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: false,
        appModalParams: { sceneId: filterSceneId },
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onEditSimple = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: false,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => loadMyApps(true)
      }
    });
  };

  const onCopySimple = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: true,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => {
          setFilterSceneId('personage');
          const element = document.getElementById('personage');
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
          loadMyApps(true);
        }
      }
    });
  };

  const onCopyAdvanced = (app: AppListItemType) => {
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy: true,
        appModalParams: {
          id: app.id,
          sceneId: app.sceneId!,
          name: app.name,
          avatarUrl: app.avatarUrl,
          permission: app.permission,
          labelId: app.labelId,
          intro: app.intro
        },
        onSuccess: () => {
          setFilterSceneId('personage');
          loadMyApps(true);
          const element = document.getElementById('personage');
          if (element) {
            setTimeout(() => {
              element.scrollIntoView({ behavior: 'smooth' });
            }, 300);
          }
        }
      }
    });
  };

  const handleWorkflowClick = (app: AppListItemType) => {
    if (confirmDialogRef.current) {
      confirmDialogRef.current.destroy();
    }
    router.push({
      pathname: '/tenant/workflow',
      query: {
        appName: app.name,
        currentTab:
          app.source === DataSource.Offical
            ? 'official'
            : app.source === DataSource.Tenant
              ? 'public'
              : 'personal'
      }
    });
  };

  const deleteApp = async (app: AppListItemType) => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      const res = await getCenterValidAppWorkflow({ id: app.id });

      if (res) {
        const tips = (
          <span>
            当前应用关联的工作流正在
            <strong>[启用]</strong>
            ，请
            <a style={{ color: 'blue' }} onClick={() => handleWorkflowClick(app)}>
              前往工作流管理
            </a>
            将关联应用进行替换再进行删除！
          </span>
        );

        confirmDialogRef.current = MessageBox.confirm({
          title: '提示',
          content: tips,
          onOk: () => setIsProcessing(false),
          onCancel: () => setIsProcessing(false)
        });
      } else {
        confirmDialogRef.current = MessageBox.confirm({
          title: '删除',
          content: '确认删除该应用所有信息？',
          onOk: async () => {
            try {
              await deleteClientApp({ id: app.id, tmbId: app.tmbId });
              Toast.success('删除成功');
              loadMyApps(true);
            } catch (err: any) {
              Toast.error(err?.message || '删除失败');
            } finally {
              setIsProcessing(false);
            }
          },
          onCancel: () => setIsProcessing(false)
        });
      }
    } catch (error) {
      Toast.error('验证工作流出错');
      setIsProcessing(false);
    }
  };
  const handleIndicatorClick = (index: number) => {
    setCurrentIndex(index);
  };

  const getPrevIndex = () => (currentIndex - 1 + images.length) % images.length;
  const handleAgentClick = (agentId: string) => {
    const scrollToElement = (id: string) => {
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    };

    const handleScrollAndSetFilter = (id: string) => {
      setFilterSceneId(id);
      scrollToElement(id);
    };
    const personageData = myApps.filter(({ source }) => source === DataSource.Personal);
    if (
      (agentId === 'commonuse' && commonAppListData.length > 0) ||
      (agentId === 'personage' && personageData.length > 0)
    ) {
      handleScrollAndSetFilter(agentId);
    } else {
      const data = myApps.filter((item) =>
        item.labelList?.some((it) => String(it.tenantSceneId) === agentId)
      );

      if (data.length <= 0) {
        Toast.info('暂无应用');
        return;
      }
    }
    handleScrollAndSetFilter(agentId);
  };

  const checkScroll = useCallback(() => {
    if (!scrollRef.current) {
      return;
    }
    setShowBackToTop(scrollRef.current.scrollTop > scrollRef.current.clientHeight);
  }, []);

  const backToTop = () => {
    scrollRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const onAppDetail = (id: number) => {
    if (id == 1) {
      setDeepEditChatItem({} as ChatItemType);
      window.open('/deepeditor', '_blank');
      return;
    }

    const app = homePageUsedList.find((item) => item.homePageUse === id);

    if (app?.appTaskTypeId) {
      handleWorkPanelOpen(app?.id, app?.appTaskTypeId);
      return;
    }

    const appId = app?.id;
    const sceneId =
      app?.labelList?.find((item) => item.isDisplayed === 1)?.tenantSceneId ||
      app?.labelList?.[0]?.tenantSceneId;

    if (appId && sceneId) {
      router.push({
        pathname: '/home',
        query: {
          activeRoute: '/app/list',
          appId,
          sceneId
        }
      });
    }
  };

  const handleWorkPanelOpen = (appId: string, appTaskTypeId: string) => {
    setDeepEditChatItem({} as ChatItemType);
    window.open(`/deepeditor?appId=${appId}&appTaskTypeId=${appTaskTypeId}&init=1`, '_blank');
  };
  const getVisibleSceneId = () => {
    const scenes = document.querySelectorAll('[data-scene-id]');
    let visibleSceneId: string = filterSceneId;

    scenes.forEach((scene) => {
      const rect = scene.getBoundingClientRect();
      if (rect.top >= 0 && rect.top <= window.innerHeight / 5) {
        visibleSceneId = scene.getAttribute('data-scene-id') || visibleSceneId;
      }
    });

    return visibleSceneId;
  };

  const filteredApps = useMemo(() => {
    if (!filterText) return [];
    const regex = new RegExp(filterText, 'i');

    return combinedList
      .filter((scene) => scene.sceneId !== 'recentlyUsed') // 过滤掉最近使用的数据
      .flatMap((scene) => scene.apps)
      .filter((app) => app && regex.test(app.name));
  }, [filterText, combinedList]);

  /* 加载模型 */
  const { isFetching } = useQuery(['loadApps'], () => loadMyApps(true), {
    refetchOnMount: true
  });

  useQuery(['tenantDetail'], () => getTenantSceneList(), {
    onSuccess: (data) => {
      if (data) {
        setScenesList(data);
      }
    }
  });

  useQuery(['appCenterHomePageUsedList'], () => getAppCenterHomePageUsedList(), {
    onSuccess: (data) => {
      if (data) {
        setHomePageUsedList(data);
      }
    }
  });

  useQuery(['commonAppList'], () => commonAppList(), {
    onSuccess: (data) => {
      if (data) {
        setCommonAppListData(data);

        // 过滤出type=1的显示
        // const filteredData = data.filter((item) => item.type === 1);
        // setCommonAppListData(filteredData);
        setCommonAppListData(data);
      }
    }
  });

  useEffect(() => {
    const handleScroll = () => {
      if (scrollRef.current) {
        const scrollTop = scrollRef.current.scrollTop;
        const offsetTop = scrollRef.current.offsetTop;

        setIsSticky(scrollTop > offsetTop);

        const visibleSceneId = getVisibleSceneId();

        if (visibleSceneId && visibleSceneId !== filterSceneId) {
          setFilterSceneId(visibleSceneId);
        }
      }
    };

    scrollRef.current?.addEventListener('scroll', handleScroll);
    return () => {
      scrollRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [filterSceneId]);

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollTop = 0;
    }
  }, [filterSceneId]);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (scrollRef.current) {
        const scrollTop = scrollRef.current.scrollTop;
        const offsetTop = scrollRef.current.offsetTop;
        setIsSticky(scrollTop > offsetTop + 180);
      }
    };

    scrollRef.current?.addEventListener('scroll', handleScroll);
    return () => {
      scrollRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 检查配置中是否应该显示返回顶部按钮和小程序提示
  const shouldShowAppTip =
    feConfigs.show_appTip !== false && window.location.href.includes('huayuntiantu.com'); // 默认显示，除非明确设置为false

  return (
    <Flex
      flexDir="column"
      h="100%"
      //   pt={rpxDim(isPc ? 46 * 2 : 32 * 2)}
      pl={rpxDim(isPc ? 46 : 24)}
      pr={rpxDim(isPc ? 46 : 24)}
      backgroundImage={
        tenant?.functionBackgroundImgUrl
          ? tenant.functionBackgroundImgUrl
          : isPc
            ? '/imgs/app/app_center_bg.png'
            : '/imgs/app/app_bg.png'
      }
      backgroundRepeat="no-repeat"
      backgroundSize="cover"
      alignItems="center"
      overflow="hidden"
      pos="relative"
    >
      {/* <Flex alignItems="center">
        <Center pos="sticky" top="0" w={rpxDim(isPc ? 1245 * 2 : 750 * 2)} zIndex="10" pb={rpxDim(20 * 2)}>
          <InputGroup>
            <Input
              placeholder="请输入关键词,搜索应用"
              bgColor="rgba(255,255,255,0.62)"
              fontSize={rpxDim(14 * 2)}
              borderRadius={rpxDim(50 * 2)}
              _placeholder={{ fontSize: rpxDim(14 * 2), fontWeight: '400' }}
              value={filterText}
              border={'none'}
              h={rpxDim(38 * 2)}
              ml={rpxDim(26 * 2)}
              onChange={(e) => setFilterText(e.target.value)}
            />
            <InputRightElement alignItems="center">
              <SvgIcon mr={rpxDim(6 * 2)} name="appAgentSearch" w={rpxDim(20 * 2)} h={rpxDim(20 * 2)} />
            </InputRightElement>
          </InputGroup>

          {filterText ? (
            <Box
              ml={rpxDim(16 * 2)}
              mr={rpxDim(15 * 2)}
              color="#979dab"
              w={rpxDim(40 * 2)}
              cursor="pointer"
              onClick={() => {
                setFilterText('');
              }}
            >
              取消
            </Box>
          ) : (
            <Box ml={rpxDim(16 * 2)} mr={rpxDim(15 * 2)}>
              <MyMenu
                trigger="hover"
                offset={[0, 12]}
                Button={
                  <MenuButton onClick={(e) => e.stopPropagation()}>
                    <Button
                      variant="solid"
                      bgColor="primary.500"
                      color="#fff"
                      px={rpxDim(20 * 2)}
                      _hover={{
                        bgColor: '#507ff7 !important'
                      }}
                      borderRadius={rpxDim(50 * 2)}
                      cursor="pointer"
                    >
                      <SvgIcon name="plus" w={rpxDim(14 * 2)} h={rpxDim(14 * 2)} />
                      <Box fontSize={rpxDim(14 * 2)} fontWeight="400" ml={rpxDim(8 * 2)}>
                        创建应用
                      </Box>
                    </Button>
                  </MenuButton>
                }
                menuList={[
                  {
                    label: (
                      <>
                        <Box fontSize={rpxDim(16 * 2)} color="#303133" fontWeight="400">
                          简易应用
                        </Box>
                        <Box fontSize={rpxDim(14 * 2)} color="#909399" fontWeight="400">
                          通过填表单形式,创建简单的AI应用,适合新手
                        </Box>
                      </>
                    ),
                    icon: <SvgIcon name="appSimpleAgent" w={rpxDim(16 * 2)} h={rpxDim(16 * 2)} />,
                    onClick: () => onAddSimple()
                  },
                  {
                    label: (
                      <>
                        <Box fontSize={rpxDim(16 * 2)} color="#303133" fontWeight="400">
                          高阶应用
                        </Box>
                        <Box fontSize={rpxDim(14 * 2)} color="#909399" fontWeight="400">
                          <Box>通过可视化编程的方式,构建逻辑复杂的多轮</Box>
                          <Box>对话AI应用,推荐高级玩家使用</Box>
                        </Box>
                      </>
                    ),
                    icon: <SvgIcon name="appAdvancedAgent" w={rpxDim(16 * 2)} h={rpxDim(16 * 2)} />,
                    onClick: () => onAddAdvanced()
                  }
                ]}
              />
            </Box>
          )}
        </Center>
      </Flex> */}

      <Flex
        flexDir="column"
        overflowX="hidden"
        overflowY="auto"
        ref={scrollRef}
        onScroll={checkScroll}
        h="100%"
        w={rpxDim(750)}
        zIndex="1"
      >
        {!filterText && (
          <>
            <Flex
              flexDir="column"
              mt={rpxDim(21 * 2)}
              pos={'relative'}
              top="0"
              zIndex="20"
              borderRadius="15px"
              justifyContent="center"
              alignItems="center"
              mb={rpxDim(8)}
              ml={rpxDim(15)}
              mr={rpxDim(15)}
            >
              <Flex
                overflowX="auto"
                overflowY="hidden"
                borderRadius="8px"
                w="100%"
                justifyContent="space-between"
                border="1px solid #FFF"
                bg="rgba(255, 255, 255, 0.37)"
                backdropFilter="blur(1.2999999523162842px)"
                pt={rpxDim(6 * 2)}
                pr={rpxDim(16 * 2)}
                // pb={rpxDim(4 * 2)}
                pl={rpxDim(16 * 2)}
              >
                {presentScenes.map((it, i) => (
                  <>
                    {it.sceneId && (
                      <Box
                        key={it.sceneId && it.sceneId + i}
                        px={rpxDim(20 * 2)}
                        borderRadius={rpxDim(8 * 2)}
                        cursor="pointer"
                        userSelect="none"
                        display={'flex'}
                        alignItems={'center'}
                        justifyContent={'center'}
                        fontSize={rpxDim(20 * 2)}
                        position="relative"
                        whiteSpace="nowrap"
                        pb="8px"
                        {...(it.sceneId === filterSceneId
                          ? {
                              color: '#303133',
                              fontWeight: '500',
                              _after: {
                                position: 'absolute',
                                content: '""',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                bottom: '-1px',
                                w: '29px',
                                borderRadius: '50px',
                                alignSelf: 'center',
                                height: '5px',
                                background: 'linear-gradient(90deg, #784CFF 0%, #A750FF 100%)'
                              }
                            }
                          : {
                              fontWeight: '500',
                              color: '#303133'
                            })}
                        onClick={() => {
                          it.sceneId && handleAgentClick(it.sceneId.toString());
                        }}
                      >
                        {it.name}
                      </Box>
                    )}
                  </>
                ))}
              </Flex>
            </Flex>
          </>
        )}

        <Box p={rpxDim(8)} ml={rpxDim(15)} mr={rpxDim(15)} overflowY="auto" maxH={'100vh'}>
          <Box>
            {combinedList.length > 0 &&
              combinedList.map((scene) => (
                <Box key={scene.sceneId} position="relative" data-scene-id={String(scene.sceneId)}>
                  {scene.apps && scene.apps.length > 0 && (
                    <Flex mt={rpxDim(12 * 2)} mb={rpxDim(16 * 2)}>
                      <Box
                        position="absolute"
                        top={rpxDim(-70 * 2)}
                        id={String(scene.sceneId)}
                        fontSize={rpxDim(17 * 2)}
                      ></Box>
                      <Box fontSize={rpxDim(17 * 2)} fontWeight="500" color="#303133">
                        {scene.name}
                      </Box>
                    </Flex>
                  )}

                  <Grid
                    ref={gridRef}
                    gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
                    gridGap={rpxDim(20 * 2)}
                  >
                    {scene.apps &&
                      scene.apps.map((app) => (
                        <Flex
                          borderRadius={rpxDim(14 * 2)}
                          bgColor="#FFF"
                          boxShadow={`0 ${rpxDim(2 * 2)} ${rpxDim(4.3 * 2)} 0 rgba(75, 86, 115, 0.07)`}
                          key={app?.id}
                          //   h={rpxDim(146 * 2)}
                          px={rpxDim(16 * 2)}
                          py={rpxDim(16 * 2)}
                          flexDir="column"
                          position="relative"
                          cursor="pointer"
                          userSelect="none"
                          transition="all 0.3s ease-in-out"
                          _hover={{
                            transform: 'translateY(-5px)',
                            zIndex: '9',
                            boxShadow: `0 0 ${rpxDim(15 * 2)} 0 rgba(92,92,92,0.09), 0 ${rpxDim(2 * 2)} ${rpxDim(4 * 2)} 0 rgba(75,86,115,0.07)`,
                            '& .app-menu': {
                              display: 'flex'
                            }
                          }}
                          onClick={() => {
                            if (app?.linkUrl) {
                              const encodedQuery = encodeURIComponent(getToken());
                              window.open(`${app?.linkUrl}?token=${encodedQuery}`, '_blank');
                              return;
                            }
                            if (app?.appTaskTypeId) {
                              handleWorkPanelOpen(app?.id, app?.appTaskTypeId);
                            } else {
                              router.push({
                                pathname: '/home',
                                query: {
                                  appId: app?.id,
                                  activeRoute: '/app/list',
                                  sceneId:
                                    scene.sceneId === 'recentlyUsed'
                                      ? (app?.labelList?.length &&
                                          app?.labelList![0].tenantSceneId) ||
                                        ''
                                      : scene.sceneId
                                }
                              });
                            }
                          }}
                        >
                          <Flex justifyContent="space-between">
                            {/* alignItems="center" */}
                            <Flex>
                              <Image
                                w={rpxDim(48 * 2)}
                                h={rpxDim(48 * 2)}
                                src={app?.avatarUrl || APP_ICON}
                                alt=""
                                borderRadius="50%"
                              />

                              <Flex flex="1" flexDir="column" ml={rpxDim(16 * 2)}>
                                <Box
                                  flex="0 0 auto"
                                  color="#000000"
                                  fontSize={rpxDim(16 * 2)}
                                  fontWeight="bold"
                                >
                                  {app?.name}
                                </Box>

                                {app?.source === DataSource.Tenant && (
                                  <Flex alignItems="center" mt={rpxDim(12 * 2)}>
                                    <SvgIcon
                                      mr={rpxDim(6 * 2)}
                                      name="appExclusive"
                                      w={rpxDim(14 * 2)}
                                      h={rpxDim(14 * 2)}
                                    />
                                    <Box
                                      ml={rpxDim(2 * 2)}
                                      fontSize={rpxDim(14 * 2)}
                                      color="#909399"
                                      fontWeight="400"
                                    >
                                      专属
                                    </Box>
                                  </Flex>
                                )}
                                {app?.source === DataSource.Offical && (
                                  <Flex alignItems="center" mt={rpxDim(5 * 2)}>
                                    <SvgIcon
                                      mr={rpxDim(6 * 2)}
                                      name="VIP_line"
                                      w={rpxDim(14 * 2)}
                                      h={rpxDim(14 * 2)}
                                    />
                                    <Box fontSize={rpxDim(14 * 2)} color="#909399" fontWeight="400">
                                      官方
                                    </Box>
                                  </Flex>
                                )}
                                {app?.source === DataSource.Personal && (
                                  <Flex alignItems="center" mt={rpxDim(10 * 2)}>
                                    <SvgIcon
                                      color="#c1c1c3"
                                      mr={rpxDim(6 * 2)}
                                      name="user"
                                      w={rpxDim(14 * 2)}
                                      h={rpxDim(14 * 2)}
                                    />
                                    <Box
                                      ml={rpxDim(2 * 2)}
                                      fontSize={rpxDim(14 * 2)}
                                      color="#909399"
                                      fontWeight="400"
                                    >
                                      个人
                                    </Box>
                                  </Flex>
                                )}
                              </Flex>
                            </Flex>
                          </Flex>

                          <Tooltip bg="#333" label={app?.intro} aria-label="space-names-tooltip">
                            <Box
                              mt={rpxDim(13 * 2)}
                              color="#606266"
                              fontSize={rpxDim(13 * 2)}
                              maxH={rpxDim(90 * 2)}
                              //   minH={rpxDim(38 * 2)}
                              style={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitBoxOrient: 'vertical',
                                WebkitLineClamp: '2',
                                whiteSpace: 'normal',
                                wordBreak: 'break-word',
                                wordWrap: 'break-word'
                              }}
                            >
                              {app?.intro || '暂无介绍'}
                            </Box>
                          </Tooltip>
                        </Flex>
                      ))}
                  </Grid>
                </Box>
              ))}
          </Box>
        </Box>
      </Flex>

      {showBackToTop && isPc && (
        <Center
          position="absolute"
          right={respDims(80)}
          bottom="20px"
          boxShadow="0px 4px 4px 0px rgba(205,205,205,0.25)"
          borderRadius="50%"
          cursor="pointer"
          border="1px solid #EDEDED"
          onClick={backToTop}
        >
          <SvgIcon
            name="chevronUp"
            color="#606266"
            p="12px"
            w={respDims('32rpx', 45, 45)}
            h={respDims('32rpx', 45, 45)}
          />
        </Center>
      )}
      {!isPc && shouldShowAppTip && (
        <Flex
          bottom={'3%'}
          left={'50%'}
          transform="translateX(-50%)"
          position="fixed"
          width={'95%'}
          height={respDims('88rpx')}
          bg="white"
          alignItems="center"
          borderRadius={'8px'}
          boxShadow="0px 1px 10px 0px rgba(0, 0, 0, 0.05), 0px 4px 5px 0px rgba(0, 0, 0, 0.08), 0px 2px 4px -1px rgba(0, 0, 0, 0.12)"
          px={respDims('32rpx')}
          gap={respDims('16rpx')}
          zIndex={999}
          display={localStorage.getItem('hideAppTip') === 'true' ? 'none' : 'flex'}
        >
          <Flex flex={1} alignItems="center" justifyContent="space-between">
            <Box color="#303133" fontSize={respDims('28rpx')}>
              使用「华云天图」小程序体验更多功能
            </Box>
            <Flex alignItems="center">
              <Box
                as="button"
                color="#7D4DFF"
                fontSize={respDims('28rpx')}
                onClick={() => {
                  window.location.href =
                    'weixin://dl/business/?appid=wxd32f7040ae577a88&path=pages/chat/chat';
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  padding: 0,
                  cursor: 'pointer'
                }}
              >
                点击使用
              </Box>
              <Box
                pos="absolute"
                right={-2}
                top={respDims('-14rpx')}
                cursor="pointer"
                // ml={respDims('24rpx')}
                onClick={(e) => {
                  e.stopPropagation();
                  localStorage.setItem('hideAppTip', 'true');
                  setRefresh((prev) => !prev);
                }}
              >
                <SvgIcon
                  name="xCircle"
                  w={respDims('32rpx')}
                  h={respDims('32rpx')}
                  color="#909399"
                />
              </Box>
            </Flex>
          </Flex>
        </Flex>
      )}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      sceneId: context.query.sceneId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default AppListMobile;
