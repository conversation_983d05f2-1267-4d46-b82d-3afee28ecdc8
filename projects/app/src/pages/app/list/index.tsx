import React from 'react';

import { serviceSideProps } from '@/utils/i18n';
import { useSystemStore } from '@/store/useSystemStore';
import AppListMobile from './appListMobile';
import AppListPc from './appListPc';

const AppList = ({ sceneId }: { sceneId?: string }) => {
  const { isPc } = useSystemStore();
  return isPc ? <AppListPc /> : <AppListMobile />;
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      sceneId: context.query.sceneId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default AppList;
