import React from 'react';
import { Box } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';

const scaleFactor = 1.24; // 放大比例

interface CustomBoxProps {
  iconName: any;
  iconColor: string;
  title: string;
  description: React.ReactNode;
  width?: string | number; // 可选的宽度
  height?: string | number; // 可选的高度
  margin?: string | number; // 可选的margin
  [key: string]: any; // 允许传递其他任意props
  backgroundImage?: string;
}

const CustomBox: React.FC<CustomBoxProps> = ({
  iconName,
  iconColor,
  title,
  description,
  width = '100%',
  height = '100%',
  margin = 0, // 默认margin为0
  backgroundImage = 'customBox1',
  colSpan,
  ...rest
}) => {
  return (
    <Box
      w={width}
      h={height}
      boxSizing="border-box"
      borderRadius={respDims(20 * scaleFactor)}
      bg="#fff"
      px={respDims(26 * scaleFactor)}
      py={respDims(15 * scaleFactor)}
      margin={margin} // 使用传入的margin
      position="relative" // 添加相对定位
      overflow="visible" // 确保子元素不会被裁剪
      transition="all 0.2s ease-in-out" // 添加过渡效果
      _hover={{
        transform: 'translateY(-5px)', // 上移效果
        boxShadow: '0px 9px 15.6px 0px rgba(197, 218, 248, 0.47)'
      }}
      // _hover={{
      //   backgroundImage: `url('/imgs/home/<USER>')`,
      //   width,
      //   height,
      //   backgroundSize: '120% 120%',
      //   backgroundPosition: 'center',
      //   filter: `drop-shadow(0px 0px ${15.6 * scaleFactor}px rgba(92, 92, 92, 0.11))`,
      //   backgroundColor: 'transparent',
      //   '& .hover-arrow': {
      //     opacity: 1,
      //     transform: 'translateY(-50%) translateX(0)'
      //   }
      // }}
      {...rest}
    >
      <SvgIcon
        name={iconName}
        w={respDims(50 * scaleFactor)}
        h={respDims(50 * scaleFactor)}
        color={iconColor}
      />
      <Box fontWeight="bold" fontSize={respDims(16 * scaleFactor)} color="#000000">
        {title}
      </Box>
      <Box
        fontWeight="400"
        lineHeight={respDims(20 * scaleFactor)}
        fontSize={respDims(14 * scaleFactor)}
        color="#606266"
        pt={respDims(4 * scaleFactor)}
      >
        {description}
      </Box>
      <Box
        className="hover-arrow"
        position="absolute"
        right={respDims(4 * scaleFactor)}
        top={`${colSpan === 2 ? '1.9vh' : '2.4vh'}`}
        transform="translateY(-50%) translateX(100%)"
        transition="all 0.3s ease"
        opacity={0}
        w={respDims(35 * scaleFactor)}
        h={respDims(35 * scaleFactor)}
        bg="#6200ea"
        borderRadius="50%"
        display="flex"
        alignItems="center"
        justifyContent="center"
        zIndex="999"
      >
        <SvgIcon
          name="boldedArrow"
          w={respDims(16 * scaleFactor)}
          h={respDims(16 * scaleFactor)}
          color="#fff"
        />
      </Box>
    </Box>
  );
};

export default CustomBox;
