import React, { useState, useRef, useEffect } from 'react';
import QuestionForm from './QuestionForm';
import { Box, Center } from '@chakra-ui/react';
import { Tooltip } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import { useChatStore } from '@/store/useChatStore';
import { ClientAppFormDetailType } from '@/types/api/app';
import { FileMetaType } from '@/types/api/file';
import { DynamicFormDataType } from '@/types/api/chat';
import { useAppStore } from '@/store/useAppStore';
import { setSystemTenantGuideFinish } from '@/api/app';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { eventBus, EventNameEnum } from '@/utils/eventbus';
import { formData2ChatData } from '@/utils/chat';
import { ClientUseGuidanceType } from '@/constants/common';

// 定义 formRef 的类型
interface QuestionFormRef {
  validateForm: () => Promise<{ valid: boolean; values?: Record<string, any>; error?: any }>;
}

const QuestionFormWrapper = () => {
  const { chatData, setFormData, clientAppFormDetail, questionFormGuideStep } = useChatStore();
  const formRef = useRef<QuestionFormRef>(null); // 使用定义的类型
  const { refreshIsFinish } = useAppStore();
  const { generateFormData, setGenerateFormData } = useDeepEditStore();
  const [isButtonDisabled, setIsButtonDisabled] = useState(false); // 新增状态

  // 监听 generateFormData 的变化
  useEffect(() => {
    // 检查是否存在 fileParseStatus 为 null 的文件
    const hasParsingFile = Object.values(generateFormData || {})?.some((fieldValue) => {
      if (Array.isArray(fieldValue)) {
        return fieldValue?.some((file) => {
          // 确保 file 是对象类型，并且包含 fileParseStatus 属性
          return (
            typeof file === 'object' &&
            file !== null &&
            'fileParseStatus' in file &&
            file.fileParseStatus === null
          );
        });
      }
      return false;
    });

    // 更新按钮的禁用状态
    setIsButtonDisabled(hasParsingFile);
  }, [generateFormData]); // 依赖项为 generateFormData

  const handleFormChange = (newData: DynamicFormDataType | undefined) => {
    setGenerateFormData(newData);
  };

  const handleGenerateClick = async () => {
    if (formRef.current) {
      const { valid } = await formRef.current.validateForm();
      if (valid && generateFormData) {
        const { ocrFileKey, text, files, images } = formData2ChatData(
          generateFormData,
          clientAppFormDetail
        );

        eventBus.emit(EventNameEnum.sendQuestion, {
          text,
          files: files,
          images: images,
          ocrFileKey: ocrFileKey
        });
        // setFormData(generateFormData);
      } else {
        console.error('表单校验失败:', generateFormData);
      }
    }

    if (questionFormGuideStep !== 3) {
      setSystemTenantGuideFinish(ClientUseGuidanceType.ChatForm).then((res) => {
        refreshIsFinish();
      });
    }
  };

  return (
    <Box
      width="100%"
      h="100%"
      position="relative"
      overflow="hidden"
      display="flex"
      flexDirection="column"
    >
      <QuestionForm
        ref={formRef}
        clientAppFormDetail={clientAppFormDetail}
        appId={chatData.appId!}
        value={generateFormData}
        layout="narrow"
        boxProps={{ flex: 1, overflow: 'auto' }}
        onChange={handleFormChange}
      />
      <Center w="100%" h="75px" bg="#FFF">
        {isButtonDisabled ? (
          <Tooltip title="请稍作等待，文件正在上传或解析中" placement="top">
            <Box
              width="100%"
              color="#fff"
              height="36px"
              display="flex"
              justifyContent="center"
              cursor="not-allowed" // 禁用时设置为 not-allowed
              alignItems="center"
              background="linear-gradient(90deg, #9c78fa 0%, #c27bfa 100%)" // 禁用时的背景色
              boxShadow="0px 4px 5px 0px rgba(202,156,255,0.35)"
              borderRadius="8px 8px 8px 8px"
            >
              <SvgIcon name="star1" w="18px" h="16px" mr="9px" />
              开始一键生成
            </Box>
          </Tooltip>
        ) : (
          <Box
            width="100%"
            color="#fff"
            height="36px"
            display="flex"
            justifyContent="center"
            cursor="pointer" // 启用时设置为 pointer
            alignItems="center"
            background="linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)" // 启用时的背景色
            boxShadow="0px 4px 5px 0px rgba(202,156,255,0.35)"
            borderRadius="8px 8px 8px 8px"
            onClick={handleGenerateClick} // 启用时绑定点击事件
          >
            <SvgIcon name="star1" w="18px" h="16px" mr="9px" />
            开始一键生成
          </Box>
        )}
      </Center>
    </Box>
  );
};

export default QuestionFormWrapper;
