import SvgIcon from '@/components/SvgIcon';
import { respDims, rpxDim } from '@/utils/chakra';
import { useAppStore } from '@/store/useAppStore';
import { useChatStore } from '@/store/useChatStore';
import {
  Box,
  Center,
  ChakraProps,
  Flex,
  Grid,
  Image,
  Text,
  IconButton,
  keyframes,
  Button
} from '@chakra-ui/react';
import { APP_ICON, ClientUseGuidanceType, DataSource } from '@/constants/common';
import { useRouter } from 'next/router';
import React, { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import styleVariables from '@/styles/variable.module.scss';
import { useSceneStore } from '@/store/useSceneStore';
import { useQuery } from '@tanstack/react-query';
import { useSystemStore } from '@/store/useSystemStore';
import { Drawer } from 'antd';
import styles from '@/pages/index.module.scss';
import { TriangleUpIcon } from '@chakra-ui/icons';
import MobileNavbar from '@/components/MobileNavbar';
import { tenantSceneNavbar } from '@/api/scene';
import { appsParams, TenantSceneNavbarType } from '@/types/api/scene';
import WorkflowList from './WorkflowList';
import QuestionForm from './QuestionForm';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import MyTooltip from '@/components/MyTooltip';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { setSystemTenantGuideFinish } from '@/api/app';
import Lottie from '@/components/Lottie';
import QuestionFormWrapper from './QuestionFormWrapper';
import useIsOpenSideBar from '@/hooks/useIsOpenSideBar';

enum ViewType {
  Agent = 'Agent',
  Workflow = 'Workflow'
}

// 定义换页的动画
const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const slideOut = keyframes`
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
`;

const AppList = ({
  isOpen,
  onClose,
  ...props
}: { isOpen?: boolean; onClose?: () => void } & ChakraProps) => {
  const router = useRouter();

  const tenantSceneId = router.query.sceneId;

  const isEnabled = !!tenantSceneId;

  const { isPc, systemGuideMap } = useSystemStore();

  const { scenes, loadScenes } = useSceneStore();

  const { myApps, generalAppId, viewApp, setViewApp, refreshIsFinish } = useAppStore();

  const [appLabels, setAppLabels] = useState<TenantSceneNavbarType[]>([]);

  const {
    chatData,
    clientAppFormDetail,
    isAppListVisible,
    setIsAppListVisible,
    questionFormGuideStep,
    setQuestionFormGuideStep,
    viewType, // 使用 store 中的 viewType
    setViewType, // 使用 store 中的 setViewType
    allIsDisplayeZero, // 从 store 中获取
    setAllIsDisplayeZero // 从 store 中获取
  } = useChatStore();

  const { setWorkflows, setCurrentTab } = useWorkflowStore();

  const [animation, setAnimation] = useState(slideIn);

  const isFinish = useAppStore((state) => state.isFinish);

  const [isFormVisible, setIsFormVisible] = useState(false);

  const handleClose = () => {
    setIsFormVisible(false);
  };

  useEffect(() => {
    if (chatData && chatData.sceneList) {
      const allZero = chatData.sceneList.every((item) => item.isDisplayed === 0);
      setAllIsDisplayeZero(allZero);
    }
  }, [chatData.sceneList, setAllIsDisplayeZero]);

  const showAppList = () => {
    setIsAppListVisible(true);
    viewApp && setViewApp();
  };

  const {} = useIsOpenSideBar(function () {
    setIsAppListVisible(false);
    viewApp && setViewApp();
  });

  useEffect(() => {
    if (chatData.appId) {
      setWorkflows(chatData.source!, chatData.appId).then((data) => {
        if (data && data.length) {
          setCurrentTab(data[0].source);
        } else {
          chatData.source === DataSource.Personal && setCurrentTab(DataSource.Personal);
        }
      });
    } else {
      showAppList();
    }
  }, [chatData.appId, chatData.source, setCurrentTab, setWorkflows]);

  useEffect(() => {
    if (clientAppFormDetail) {
      setViewType(ViewType.Workflow);
    }
  }, [clientAppFormDetail, setViewType]);

  useEffect(() => {
    if (!clientAppFormDetail && viewType === ViewType.Workflow) {
      setViewType(ViewType.Agent);
    }
  }, [clientAppFormDetail, viewType, setViewType]);

  const handleAppListVisibility = () => {
    setIsAppListVisible(false);
    setViewType(tabs?.length === 1 ? tabs[0].type : ViewType.Agent);
  };

  const isAppListActualVisible = isAppListVisible && !viewApp;

  const title = useMemo(
    () => appLabels.find((it) => it.id === tenantSceneId)?.name || '我的应用',
    [scenes, tenantSceneId, appLabels]
  );

  const groups = useMemo(() => {
    if (!myApps.length) {
      return [];
    }
    const groups: Record<
      string,
      { _id: string; name: string; sort: number; apps: appsParams[]; tenantSceneId: string }
    > = {};

    const addAppToGroup = (
      app: appsParams,
      label: { id: string; name: string; sort: number; tenantSceneId?: string }
    ) => {
      if (groups[label.id]) {
        groups[label.id].apps.push(app);
      } else {
        groups[label.id] = {
          _id: label.id,
          name: label.name,
          sort: label.sort,
          tenantSceneId: label.tenantSceneId!,
          apps: [app]
        };
      }
    };

    if (chatData.sceneList.length > 0) {
      const targetLabel = appLabels.find((label) => label.id == tenantSceneId);
      if (targetLabel) {
        targetLabel.labels.forEach(
          ({ apps, id: subLabelId, name: subLabelName, sort: subLabelSort }) => {
            apps.forEach((app) => {
              addAppToGroup(app, {
                id: subLabelId,
                name: subLabelName,
                sort: subLabelSort,
                tenantSceneId: String(targetLabel.id)
              });
            });
          }
        );
      }
    } else {
      myApps.forEach((app) => {
        if (app.permission === 1) {
          const label = {
            id: 'personal',
            name: '个人',
            sort: 0
          };
          addAppToGroup(
            { name: app.name, id: app.id, avatarUrl: app.avatarUrl, sort: 0, source: app.source },
            label
          );
        }
      });
    }
    return Object.values(groups).sort((l, r) => l.sort - r.sort);
  }, [myApps, chatData.sceneList.length, appLabels, tenantSceneId]);

  useQuery(['labels', tenantSceneId], () => tenantSceneNavbar(), {
    enabled: isEnabled,
    onSuccess(data) {
      setAppLabels(data);
    }
  });

  const handleDrawerClose = () => {
    setViewType(ViewType.Agent); // 使用 store 中的 setViewType
    onClose?.();
  };

  const tabs: {
    type: ViewType;
    text: string;
    svgName: SvgIconNameType;
  }[] = useMemo(() => {
    const newTabs = [];
    const hasApps = !allIsDisplayeZero || groups.length > 0;

    if (hasApps) {
      newTabs.push({
        type: ViewType.Agent,
        text: title,
        svgName: 'appNav' as SvgIconNameType
      });
    }

    if (clientAppFormDetail !== null) {
      newTabs.push({
        type: ViewType.Workflow,
        text: '提问表单',
        svgName: 'workflow' as SvgIconNameType
      });
    }

    return newTabs;
  }, [title, clientAppFormDetail, allIsDisplayeZero, groups]);

  if (chatData.appId == generalAppId) {
    return null;
  }

  const handleGuideFinish = () => {
    setSystemTenantGuideFinish(ClientUseGuidanceType.ChatForm).then((res) => {
      refreshIsFinish();
      setQuestionFormGuideStep(3);
    });
  };

  const isTenantSceneValid = DataSource.Personal === 3; //判断为个人

  const isAppListVisibleCondition =
    isTenantSceneValid ??
    ((!allIsDisplayeZero && groups.length > 0) || clientAppFormDetail !== null); //判断tab都可以正常显示的情况

  return ((node: JSX.Element) =>
    isPc ? (
      node
    ) : (
      <Drawer
        open={isOpen}
        placement="top"
        height="auto"
        style={{
          width: 'auto',
          borderBottomLeftRadius: rpxDim(40),
          borderBottomRightRadius: rpxDim(40)
        }}
        styles={{
          body: {
            padding: 0
          }
        }}
        rootClassName={styles['ant-drawer-hide-shadow']}
        onClose={handleDrawerClose}
      >
        {node}
      </Drawer>
    ))(
    <>
      {isAppListActualVisible && isAppListVisibleCondition && (
        <Flex
          flexDir="column"
          display={isAppListActualVisible ? 'flex' : 'none'}
          h="100%"
          animation={
            isAppListVisible ? `${slideIn} 0.3s ease-in-out` : `${slideOut} 0.3s ease-in-out`
          }
          {...(isPc
            ? {
                px: respDims(24),
                pt: respDims(16),
                boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.04)'
              }
            : {
                px: rpxDim(32),
                pb: rpxDim(32),
                bgImage: 'url(/imgs/v2/gradient_bg4.png)'
              })}
          bgColor="#ffffff"
          bgRepeat="no-repeat"
          bgSize="100% auto"
          overflow="hidden"
          {...props}
          w={respDims(500)}
        >
          {isPc && (
            <SvgIcon
              name="close"
              w={respDims(24, 20)}
              h={respDims(24, 20)}
              color="#A8ABB2"
              aria-label="Close"
              position="absolute"
              cursor="pointer"
              top={respDims(24)}
              right={respDims(20)}
              onClick={handleAppListVisibility}
            />
          )}
          <Flex justifyContent="center" alignItems="center">
            <Flex
              flexDirection="row"
              alignItems="center"
              backgroundColor="rgba(223,223,223,0.29)"
              borderRadius={respDims(70)}
              height={respDims('36fpx')}
              p={respDims(4)}
              justifyContent="center"
              cursor="pointer"
              boxShadow="0px -1px 1px rgba(255, 255, 255, 1), 0 1px 1px rgba(255, 255, 255, 0.3)"
            >
              {tabs.map((tab, index) => (
                <Flex
                  key={index}
                  flexDirection="row"
                  alignItems="center"
                  justifyContent="center"
                  onClick={() => {
                    setViewType(tab.type); // 使用 store 中的 setViewType
                    setAnimation(slideIn);
                  }}
                  cursor="pointer"
                  {...(tab.type === viewType
                    ? {
                        color: 'primary.500',
                        bgColor: '#ffffff',
                        fontWeight: 'bold',
                        boxShadow:
                          '0px 3px 5px rgba(52, 58, 69, 0.10), 0px 1px 2px rgba(52, 58, 69, 0.10)'
                      }
                    : {
                        color: '#909399'
                      })}
                  borderRadius={respDims(46)}
                  minWidth={respDims('128fpx')}
                  height="100%"
                >
                  <SvgIcon
                    name={tab.svgName}
                    w={respDims(20)}
                    h={respDims(20)}
                    mr={respDims(4)}
                  ></SvgIcon>
                  <Text fontSize={respDims('15fpx')} textAlign="center" whiteSpace="nowrap">
                    {tab.text}
                  </Text>
                </Flex>
              ))}
            </Flex>
          </Flex>
          {viewType === ViewType.Agent && (
            <>
              {isPc ? (
                <></>
              ) : (
                <MobileNavbar
                  autoback={false}
                  title={
                    <Center cursor="pointer" onClick={onClose}>
                      <Box
                        ml="4px"
                        color="#303133"
                        fontSize={respDims('32rpx', '15fpx')}
                        lineHeight={respDims('52rpx', '20fpx')}
                        fontWeight="bold"
                      >
                        {title}
                      </Box>

                      {!isPc && <TriangleUpIcon ml={rpxDim(4)} fontSize={rpxDim(24)} />}
                    </Center>
                  }
                  bgColor="transparent"
                />
              )}
              {(isTenantSceneValid || (!allIsDisplayeZero && groups.length > 0)) && (
                <Box
                  flex="1"
                  mt={respDims('10rpx', 10)}
                  mr={[`-${styleVariables.scrollbarSmWidth}`, `-${styleVariables.scrollbarWidth}`]}
                  overflow="scroll"
                >
                  {groups.map((group, index) => (
                    <Fragment key={group._id}>
                      <Box
                        mt={index === 0 ? respDims('8rpx', 10) : respDims('32rpx', 20)}
                        color="#303133"
                        fontWeight="400"
                        fontSize={respDims('28rpx', '14fpx')}
                        lineHeight={respDims('40rpx', '20fpx')}
                      >
                        {group.name}
                      </Box>

                      <Grid
                        mt={respDims('20rpx', 10)}
                        gap={respDims('20rpx', 10)}
                        gridTemplateColumns={['1fr 1fr']}
                      >
                        {group.apps.map((app) => (
                          <Flex
                            key={app.id}
                            alignItems="center"
                            px={respDims('20rpx', 9)}
                            py={respDims('24rpx', 11)}
                            bgColor={app.id == chatData.appId ? 'primary.50' : '#F8FAFC'}
                            borderColor={app.id == chatData.appId ? 'primary.500' : 'transparent'}
                            borderStyle="solid"
                            borderWidth="1px"
                            borderRadius={respDims('16rpx', 8)}
                            cursor="pointer"
                            overflow="hidden"
                            _hover={{
                              bgColor: 'primary.50'
                            }}
                            onClick={() => {
                              router.push(
                                `/home?appId=${app.id}&sceneId=${tenantSceneId || group.tenantSceneId || ''}`
                              );
                              onClose?.();
                            }}
                          >
                            <Image
                              src={app.avatarUrl || APP_ICON}
                              alt=""
                              w={respDims('64rpx', '32fpx')}
                              h={respDims('64rpx', '32fpx')}
                              flexShrink="0"
                              borderRadius="50%"
                              mr="10px"
                            />

                            <Flex flexDir="column" alignItems="self-start">
                              <MyTooltip p="10px" label={app.name}>
                                <Box
                                  color="#303133"
                                  fontSize={respDims('28rpx', '14fpx')}
                                  style={{
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitBoxOrient: 'vertical',
                                    WebkitLineClamp: '1',
                                    whiteSpace: 'normal'
                                  }}
                                >
                                  {app.name}
                                </Box>
                              </MyTooltip>
                              {app.source === 1 && (
                                <Flex alignItems="center" mt={respDims(4)}>
                                  <SvgIcon
                                    name="appExclusive"
                                    mr="1px"
                                    w={respDims(15)}
                                    h={respDims(15)}
                                  />
                                  <Box
                                    ml={respDims(2)}
                                    fontSize={respDims(12, 10)}
                                    color="#909399"
                                    fontWeight="400"
                                  >
                                    专属
                                  </Box>
                                </Flex>
                              )}
                              {app.source === 2 && (
                                <Flex alignItems="center" mt={respDims(4)}>
                                  <SvgIcon
                                    mr="3px"
                                    name="appAuthority"
                                    w={respDims(15)}
                                    h={respDims(15)}
                                  />
                                  <Box fontSize={respDims(12, 10)} color="#909399" fontWeight="400">
                                    官方
                                  </Box>
                                </Flex>
                              )}
                            </Flex>
                          </Flex>
                        ))}
                      </Grid>
                    </Fragment>
                  ))}
                </Box>
              )}
            </>
          )}
          <Box
            display={viewType === ViewType.Workflow ? 'block' : 'none'}
            h="100%"
            overflowY="auto"
            bg="#fff"
          >
            {tenantSceneId == '' || tenantSceneId === 'personage'
              ? true
              : clientAppFormDetail !== null && <QuestionFormWrapper></QuestionFormWrapper>}
          </Box>
        </Flex>
      )}

      {isPc && (
        <Box>
          {systemGuideMap[ClientUseGuidanceType.ChatForm] === false &&
            isAppListActualVisible &&
            viewType === ViewType.Workflow &&
            questionFormGuideStep === 2 &&
            clientAppFormDetail && (
              <Box
                textAlign="left"
                width="382px"
                height="115px"
                background="#FFFFFF"
                box-shadow="0px 0px 13px 0px rgba(92,92,92,0.05)"
                borderRadius="12px"
                border="1px solid #FFF"
                display="flex"
                flexDirection="column"
                justifyContent="space-between"
                alignItems="center"
                padding="18px 20px"
                position="absolute"
                right="400px"
                // top="50%"
                top="calc(50% - 115px)"
                backgroundImage="/imgs/app/tipsBg.svg"
                backgroundRepeat="no-repeat"
                backgroundSize="cover"
              >
                <Box display="flex" justifyContent="space-between" width="100%">
                  <Box>
                    <Text fontSize="16px" cursor="pointer" color="#303133">
                      快试试在这里填写「提问表单」，开始一键生成吧～
                    </Text>
                  </Box>
                </Box>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="flex-end"
                  width="100%"
                  position="relative"
                >
                  <SvgIcon
                    name="vector2"
                    width="52px"
                    height="32px"
                    position="absolute"
                    right="-38px"
                    top="42px"
                  />

                  <Button
                    backgroundColor="#000000"
                    color="#FFFFFF"
                    width="88px"
                    height="32px"
                    fontSize="14px"
                    background="#303133"
                    borderRadius=" 100px 100px 100px 100px"
                    onClick={handleGuideFinish}
                  >
                    知道了
                  </Button>
                </Box>
                <Lottie
                  name="click"
                  w={respDims('42fpx')}
                  h={respDims('42fpx')}
                  position="absolute"
                  bottom="-36px"
                  right="-48px"
                />
              </Box>
            )}

          {!isAppListActualVisible && isAppListVisibleCondition && (
            <IconButton
              aria-label="Expand"
              icon={<SvgIcon name="workflow" w={respDims('18fpx')} h={respDims('18fpx')} />}
              position="absolute"
              top={respDims('18fpx')}
              right={respDims('32fpx')}
              color="#303133"
              _hover={{
                color: '#7D4DFF'
              }}
              bgColor="#fff"
              borderRadius="50%"
              boxShadow="0px 0px 7px 0px rgba(0,0,0,0.05)"
              onClick={showAppList}
            />
          )}
        </Box>
      )}
    </>
  );
};

export default AppList;
