import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { Box, BoxProps, Button, Center, Flex } from '@chakra-ui/react';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useAppStore } from '@/store/useAppStore';
import SimpleModal from '@/components/AppModal/simple';
import { useRouter } from 'next/router';
import { useContextSelector } from 'use-context-selector';
import { AppContext, SimpleAppPerm } from '@/components/AppDetail/components/context';
import MyIcon from '@/components/LegacyIcon';
import FormLabel from '@/components/common/MyBox/FormLabel';
import { useTranslation } from 'react-i18next';
import SettingLLMModel from '@/components/core/ai/SettingLLMModel';
import { SettingAIDataType } from '@/fastgpt/global/core/app/type';
import PromptConfig from '@/components/core/app/PromptConfig';
import dynamic from 'next/dynamic';
import { getWebLLMModel } from '@/utils/dataset';
import DatasetConfig from '@/components/core/app/DatasetConfig';
import ToolChoice from '@/components/core/app/ToolChoice';
import { getSampleAppInfo } from '@/utils/app';
import { form2AppWorkflow } from '@/fastgpt/web/core/app/utils';
import { useCallback, useEffect } from 'react';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import FileSelect from '@/components/core/app/FileSelect';
import { useDatasetStore } from '@/store/useDatasetStore';
import { v1Workflow2V2 } from '@/fastgpt/web/core/workflow/adapt';
import { appWorkflow2Form } from '@/fastgpt/global/core/app/utils';
import { debounce } from 'lodash';
import { DataSource } from '@/constants/common';
import { PermissionTypeEnum } from '@/constants/permission';
import { actionButtonStyle } from '@/components/AppDetail/components/constants';

const SimpleDetail = ({ onBack }: { onBack: () => void }) => {
  const { loadMyApps, viewApp, setViewApp } = useAppStore();
  const { openOverlay } = useOverlayManager();
  const router = useRouter();
  const {
    originAppDetail,
    clientAppDetail,
    appForm,
    setAppForm,
    permission,
    onPublish,
    isPublished
  } = useContextSelector(AppContext, (v) => v);

  const { t } = useTranslation();
  const selectedModel = getWebLLMModel(appForm.aiSettings.model);
  const isPrivate =
    viewApp?.permission === PermissionTypeEnum.Private && viewApp?.source !== DataSource.Personal;
  const isPublic =
    viewApp?.permission === PermissionTypeEnum.Public && viewApp?.source !== DataSource.Personal;
  const BoxStyles: BoxProps = {
    py: '16px',
    borderBottomColor: 'borderColor.low'
  };

  const onCopyAdvanced = () => {
    const isCopy = true;
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy,
        appModalParams: {
          id: viewApp?.id,
          sceneId: viewApp?.sceneId!,
          name: viewApp?.name,
          avatarUrl: viewApp?.avatarUrl,
          permission: viewApp?.permission,
          labelId: viewApp?.labelId,
          intro: viewApp?.intro
        },
        onSuccess: (data) => {
          if (data && data.id && isCopy) {
            setViewApp();
            loadMyApps();
            router.push(`/home?appId=${data.id}&chatId=`);
          }
        }
      }
    });
  };

  const onSubmitPublish = useCallback(
    debounce(async (data: AppSimpleEditFormTypeMegre) => {
      const { nodes, edges } = form2AppWorkflow(data, t);

      await onPublish({
        id: clientAppDetail.id as string,
        // nodes,
        // edges,
        // chatConfig: data.chatConfig,
        type: AppTypeEnum.simple,
        name: originAppDetail.name,
        isStructuredPrompt: data.isStructuredPrompt,
        promptList: data.promptList,
        filesList: data.filesList
      });
    }, 1000),
    [onPublish, t, originAppDetail]
  );

  return (
    <Flex direction="column" h="100%">
      <Box position="relative">
        <SvgIcon
          position="absolute"
          left="0"
          name="chevronLeft"
          w={respDims(26)}
          h={respDims(26)}
          cursor="pointer"
          alignSelf="start"
          color="#A8ABB2"
          onClick={onBack}
          borderRadius="8px"
        />
        <Center color="#303133" fontSize="18px" fontWeight="500">
          配置项
        </Center>
      </Box>

      {/* <Box overflowY="auto" overflowX="hidden"> */}
      {/* <Box {...BoxStyles}>
           <Flex alignItems={'center'}>
            <MyIcon name={'core/app/simpleMode/ai'} w={'20px'} color="primary.500" />
            <FormLabel ml={2} flex={1} fontWeight="500">
              {t('AI 配置')}
            </FormLabel>
          </Flex>
         <Flex alignItems={'center'} mt={5}>
            <Box color="#111824" mr="10px">
              AI模型
            </Box>
            <Box flex={'1 0 0'}>
              <SettingLLMModel
                bg="myGray.50"
                llmModelType={'all'}
                defaultData={{
                  model: appForm.aiSettings.model,
                  temperature: appForm.aiSettings.temperature,
                  maxToken: appForm.aiSettings.maxToken,
                  maxHistories: appForm.aiSettings.maxHistories
                }}
                onChange={({ model, temperature, maxToken, maxHistories }: SettingAIDataType) => {
                  setAppForm((state) => ({
                    ...state,
                    aiSettings: {
                      ...state.aiSettings,
                      model,
                      temperature,
                      maxToken,
                      maxHistories: maxHistories ?? 6
                    }
                  }));
                }}
              />
            </Box>
          </Flex>
         
          {permission === SimpleAppPerm.UnPublish ? (
            <Box pt={4} color="#4E5969">
              未公开
            </Box>
          ) : (
            <Box pt={4}>
              <PromptConfig
                value={appForm}
                disabled={permission === SimpleAppPerm.Publish}
                originAppDetail={originAppDetail}
                onChange={(update) => {
                  const updateValue = update(appForm);

                  setAppForm(update);
                  onSubmitPublish(updateValue);
                }}
              />
            </Box>
          )}
           
        </Box>  */}

      {/* File select */}
      {/* <Box {...BoxStyles}>
          <FileSelect
            forbidVision={!selectedModel?.vision}
            value={appForm.filesList}
            disabled={permission === SimpleAppPerm.Publish}
            onChange={(e) => {
              setAppForm((state) => ({
                ...state,
                filesList: e
              }));
              onSubmitPublish({
                ...appForm,
                filesList: e
              });
            }}
          />
        </Box> */}
      {/* <Box {...BoxStyles}>
          <DatasetConfig
            value={appForm}
            onChange={(update) => {
              const updateValue = update(appForm);
              setAppForm(update);
              onSubmitPublish(updateValue);
            }}
            disabled={permission === SimpleAppPerm.Publish}
          />
        </Box>
        <Box {...BoxStyles}>
          <ToolChoice
            disabled={permission === SimpleAppPerm.Publish}
            selectedTools={appForm.selectedTools}
            onChange={(update) => {
              const updateValue = update(appForm);
              setAppForm(update);
              onSubmitPublish(updateValue);
            }}
          />
        </Box> */}
      {/* </Box> */}
      <Flex justifyContent="space-between" alignItems="center" mt="20px">
        <Box color="#303133" fontWeight="500" fontSize={respDims(18, 12)}>
          应用编排
        </Box>
        <Box
          fontSize="14px"
          color="#4E5969"
          fontWeight="400"
          bgColor="#F6F6F6"
          p="5px 16px"
          borderRadius="50px"
          {...actionButtonStyle}
          cursor="pointer"
          onClick={() => {
            viewApp?.source === DataSource.Personal &&
              router.push({
                pathname: '/app/detail',
                query: {
                  appType: viewApp?.type,
                  finalAppId: viewApp?.finalAppId,
                  isAdmin: '0',
                  appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(viewApp)))
                }
              });
          }}
        >
          {isPrivate ? (
            '未公开'
          ) : isPublic && viewApp?.config === PermissionTypeEnum.Public ? (
            <Flex>
              请完成
              <Box color="primary.5" p="0 2px">
                复制
              </Box>
              后进行查看
            </Flex>
          ) : (
            '查看'
          )}
        </Box>
      </Flex>
      {permission === SimpleAppPerm.Self ? (
        <Button
          w="100%"
          mt="24px"
          borderRadius="50px"
          variant="outline"
          colorScheme="primary"
          height="38px"
          onClick={() => {
            router.push({
              pathname: '/app/detail',
              query: {
                appType: viewApp?.type,
                finalAppId: viewApp?.finalAppId,
                isAdmin: '0',
                appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(viewApp!)))
              }
            });
          }}
        >
          <SvgIcon name="appGoAgentConfig" w={respDims(14)} h={respDims(14)} mr={respDims(8)} />
          前往编辑简易应用
        </Button>
      ) : (
        viewApp?.config === PermissionTypeEnum.Public && (
          <Button
            w="100%"
            mt="24px"
            borderRadius="50px"
            variant="outline"
            color="primary.5"
            opacity={
              permission === SimpleAppPerm.UnPublish
                ? '.5'
                : permission === SimpleAppPerm.Publish
                  ? '1'
                  : '1'
            }
            colorScheme="primary"
            height="38px"
            onClick={() => onCopyAdvanced()}
          >
            <SvgIcon name="copy" w={respDims(14)} h={respDims(14)} mr={respDims(8)} />
            复制为我的应用
          </Button>
        )
      )}
    </Flex>
  );
};

export default SimpleDetail;
