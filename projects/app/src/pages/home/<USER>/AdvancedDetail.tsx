import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { Box, Button, Center, Flex, Grid } from '@chakra-ui/react';
import MyIcon from '@/components/LegacyIcon';
import { useSelectFile } from '@/hooks/useSelectFile';
import { useRequest } from '@/hooks/useRequest';
import { uploadFile } from '@/utils/file';
import { FileMetaType } from '@/types/api/file';
import { TenantAppKnowledgeFile } from '@/types/api/app';
import { actionButtonStyle, itemStyles } from '@/components/AppDetail/components/constants';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import Chooser from '@/pages/cloud/list/components/Chooser';
import { Toast } from '@/utils/ui/toast';
import { TenantAppDetailType } from '@/types/api/scene';
import { useAppStore } from '@/store/useAppStore';
import { PermissionTypeEnum } from '@/constants/permission';
import { updateClientApp } from '@/api/app';
import SimpleModal from '@/components/AppModal/simple';
import { useRouter } from 'next/router';
import { DataSource } from '@/constants/common';
import { ModeTypeEnum } from '@/constants/api/app';
import { getSampleAppInfo } from '@/utils/app';
import { fileTypeInfos } from '@/components/ChatBox/MessageInput';
import { FileTypeEnum } from '@/constants/api/cloud';

const MAX_FILES = 5;
const AdvancedDetail = ({
  onBack,
  value = [],
  appDetail,
  onChange
}: {
  onBack: () => void;
  value?: TenantAppKnowledgeFile[];
  appDetail?: TenantAppDetailType;
  onChange: (e: TenantAppKnowledgeFile[]) => void;
}) => {
  const { loadMyApps, viewApp, setViewApp } = useAppStore();
  const { openOverlay } = useOverlayManager();
  const isPrivate =
    viewApp?.permission === PermissionTypeEnum.Private && viewApp?.source !== DataSource.Personal;
  const isPublic =
    viewApp?.permission === PermissionTypeEnum.Public && viewApp?.source !== DataSource.Personal;
  const router = useRouter();

  const { File: FilesSelectInput, onOpen: onOpenLocalSelect } = useSelectFile({
    fileType: '*',
    multiple: true
  });

  const { mutate: onSelectFiles, isLoading: isUploadLoading } = useRequest({
    mutationFn: (files: File[]) => {
      if (!files || files.length === 0) return Promise.resolve(null);
      if (value.length + files.length > MAX_FILES) {
        Toast.error(
          `背景知识的资料最多不能超过${MAX_FILES}个, 当前可以添加${MAX_FILES - value.length}个文件`
        );
        return Promise.reject();
      }
      return Promise.all(
        files.map((file) =>
          uploadFile(file, {
            onProgress(number) {}
          })
        )
      );
    },
    onSuccess(files: FileMetaType[]) {
      if (files) {
        let newValues: TenantAppKnowledgeFile[] = files.map((res) => {
          return {
            fileName: res.fileName,
            fileKey: res.fileKey,
            fileUrl: res.fileUrl
          };
        });
        updateAppData([...value, ...newValues]);
        onChange([...value, ...newValues]);
      }
    }
  });

  const onSelectCloud = () => {
    let files = value
      .map((item) => {
        return {
          fileId: item.fileId!,
          fileKey: item.fileKey!,
          fileName: item.fileName!,
          fileUrl: item.fileUrl,
          rowKey: `${FileTypeEnum.File}-${item.fileId}`
        };
      })
      .filter((item) => item.fileId);
    const accept: string[] = [];

    fileTypeInfos.forEach((item) => {
      accept.push(item.name);
    });
    openOverlay({
      Overlay: Chooser,
      props: {
        files: files, // 回显已选文件
        accept: accept,
        maxCount: 5,

        onSuccess(files) {
          let newValue = files.map((item) => {
            return {
              fileId: item.fileId,
              fileKey: item.fileKey,
              fileName: item.fileName,
              fileUrl: item.fileUrl
            };
          });
          if (value.filter((file) => !file.fileId).length + newValue.length > MAX_FILES) {
            Toast.error(
              `背景知识的资料最多不能超过${MAX_FILES}个, 当前可以添加${MAX_FILES - value.length}个文件`
            );
            return;
          }
          updateAppData([...value.filter((file) => !file.fileId), ...newValue]);
          onChange([...value.filter((file) => !file.fileId), ...newValue]);
        }
      }
    });
  };

  const onRemoveFile = (file: TenantAppKnowledgeFile) => {
    const updatedFiles = value.filter((item) => file.fileKey !== item.fileKey);
    updateAppData(updatedFiles);
    onChange(updatedFiles);
  };

  const updateAppData = (filesList: TenantAppKnowledgeFile[]) => {
    const appData = {
      avatarUrl: appDetail?.avatarUrl || '',
      name: appDetail?.name || '',
      type: appDetail?.type,
      modules: appDetail?.modules || [],
      intro: appDetail?.intro || '',
      mode: ModeTypeEnum.advanced,
      edges: appDetail?.edges || [],
      id: appDetail?.id,
      filesList
    };
    updateClientApp(appData);
  };

  const onCopyAdvanced = () => {
    const isCopy = true;
    openOverlay({
      Overlay: SimpleModal,
      props: {
        isCopy,
        appModalParams: {
          id: viewApp?.id,
          sceneId: viewApp?.sceneId!,
          name: viewApp?.name,
          avatarUrl: viewApp?.avatarUrl,
          permission: viewApp?.permission,
          labelId: viewApp?.labelId,
          intro: viewApp?.intro
        },
        onSuccess: (data) => {
          if (data && data.id && isCopy) {
            setViewApp();
            loadMyApps();
            router.push(`/home?appId=${data.id}&chatId=`);
          }
        }
      }
    });
  };

  return (
    <Flex direction="column">
      <Box position="relative">
        <SvgIcon
          position="absolute"
          left="0"
          name="chevronLeft"
          w={respDims(26)}
          h={respDims(26)}
          cursor="pointer"
          alignSelf="start"
          color="#A8ABB2"
          onClick={onBack}
          borderRadius="8px"
        />
        <Center color="#303133" fontSize="18px" fontWeight="500">
          配置项
        </Center>
      </Box>

      {/* <Flex alignItems="center" mt="20px">
        <MyIcon name={'core/app/simpleMode/bgKnowlegde'} mr={respDims(6, 2)} w={respDims(20, 18)} />
        <Box
          fontSize={respDims(18, 12)}
          color="#303133"
          fontWeight="500"
          ml={respDims(10, 8)}
          mr={respDims(10)}
          flex="1"
        >
          {`背景知识`}
        </Box>
        {viewApp?.source === DataSource.Personal && (
          <>
            <Box
              {...actionButtonStyle}
              fontSize={respDims(12, 8)}
              mr={respDims(12, 6)}
              onClick={onSelectCloud}
            >
              从数据空间选择
            </Box>
            <Box {...actionButtonStyle} fontSize={respDims(12, 8)} onClick={onOpenLocalSelect}>
              从本地上传
            </Box>
          </>
        )}
        {value && value.length > 0 && (
          <SvgIcon
            name="chevronDown"
            w={respDims(24)}
            h={respDims(24)}
            cursor="pointer"
            color="#A8ABB2"
            ml={respDims(12)}
            borderRadius="8px"
          />
        )}
      </Flex> */}

      {/* {value && value.length > 0 ? (
        <Grid
          w="100%"
          mt={respDims(16, 14)}
          gridTemplateColumns={'repeat(2, minmax(0, 1fr))'}
          gridGap={respDims('28rpx', 16)}
        >
          {value.map((item) => (
            <Flex key={item.fileKey as string} {...itemStyles} position="relative">
              <FileIcon
                fileType={item.fileType}
                fileName={item.fileName}
                w={respDims(40)}
                h={respDims(40)}
              ></FileIcon>
              <Box
                color="#1D2129"
                fontSize={respDims(15, 13)}
                ml={respDims(10, 8)}
                className={'textEllipsis'}
                w={0}
                flex={'1 0 0'}
              >
                {item.fileName}
              </Box>
              <SvgIcon
                name="circleClose"
                position="absolute"
                className="close_icon"
                right="-10px"
                top="-10px"
                cursor="pointer"
                w={respDims(30)}
                display="none"
                h={respDims(30)}
                onClick={() => onRemoveFile(item)}
              ></SvgIcon>
            </Flex>
          ))}
        </Grid>
      ) : (
        <Center m="20px" color="#7e7f80">
          暂无数据
        </Center>
      )} */}

      {/* {isUploadLoading && '文件上传中...'}
      <FilesSelectInput onSelect={(files) => onSelectFiles([...files])}></FilesSelectInput>
      {value && value.length > 0 && (
        <Box
          pt={respDims(16)}
          mb={respDims(16)}
          w="100%"
          h="1px"
          borderBottom="1px solid #E5E7EB"
        ></Box>
      )} */}

      <Flex justifyContent="space-between" alignItems="center" mt="20px">
        <Box color="#303133" fontWeight="500" fontSize={respDims(18, 12)}>
          应用编排
        </Box>
        <Box
          fontSize="14px"
          color="#4E5969"
          fontWeight="400"
          bgColor="#F6F6F6"
          p="5px 16px"
          borderRadius="50px"
          {...actionButtonStyle}
          cursor="pointer"
          onClick={() => {
            viewApp?.source === DataSource.Personal &&
              router.push({
                pathname: '/app/detail',
                query: {
                  appType: viewApp?.type,
                  finalAppId: viewApp?.finalAppId,
                  isAdmin: '0',
                  appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(viewApp)))
                }
              });
          }}
        >
          {isPrivate ? (
            '未公开'
          ) : isPublic && viewApp?.config === PermissionTypeEnum.Public ? (
            <Flex>
              请完成
              <Box color="primary.5" p="0 2px">
                复制
              </Box>
              后进行查看
            </Flex>
          ) : (
            '查看'
          )}
        </Box>
      </Flex>
      {viewApp?.source === DataSource.Personal ? (
        <Button
          w="100%"
          mt="24px"
          borderRadius="50px"
          variant="outline"
          colorScheme="primary"
          height="38px"
          onClick={() => {
            router.push({
              pathname: '/app/detail',
              query: {
                appType: viewApp?.type,
                finalAppId: viewApp?.finalAppId,
                isAdmin: '0',
                appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(viewApp)))
              }
            });
          }}
        >
          <SvgIcon name="appGoAgentConfig" w={respDims(14)} h={respDims(14)} mr={respDims(8)} />
          前往编辑高阶应用
        </Button>
      ) : (
        viewApp?.config === PermissionTypeEnum.Public && (
          <Button
            w="100%"
            mt="24px"
            borderRadius="50px"
            variant="outline"
            color="primary.5"
            opacity={isPrivate ? '.5' : isPublic ? '1' : '1'}
            colorScheme="primary"
            height="38px"
            onClick={() => onCopyAdvanced()}
          >
            <SvgIcon name="copy" w={respDims(14)} h={respDims(14)} mr={respDims(8)} />
            复制为我的应用
          </Button>
        )
      )}
    </Flex>
  );
};

export default AdvancedDetail;
