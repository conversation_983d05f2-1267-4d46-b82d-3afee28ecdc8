.input {
  :global {
    .ant-select-selector {
      display: block !important;
      min-height: 22px;
      overflow-y: scroll;
    }

    .ant-select-selection-placeholder {
      white-space: normal !important;
      // padding-top: 8px;
    }
  }
}

.input2 {
  :global {
    .ant-select-selector {
      display: block !important;
      min-height: 52px;
      overflow-y: scroll;
    }

    .ant-select-selection-placeholder {
      white-space: normal !important;
    }
  }
}

.input3 {
  :global {
    .ant-select-selector {
      display: block !important;
      height: 62px;
      overflow-y: scroll;
    }

    .ant-select-selection-placeholder {
      white-space: normal !important;
      padding-top: 48px;
    }
  }
}

.textArea {
  :global {
    .ant-input {
      line-height: 2 !important; // 增加行高
      padding-top: 8px !important;
      padding-bottom: 8px !important;
    }
  }
}
