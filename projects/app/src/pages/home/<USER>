import React, { useState, useEffect, useCallback } from 'react';
import { Box, Flex, Button, Image, Grid, GridItem, HStack } from '@chakra-ui/react';

const scaleFactor = 1.24; // 放大比例

import { respDims } from '@/utils/chakra';
import Lottie from '@/components/Lottie';
import CustomBox from './components/CustomBox';
import { useTenantStore } from '@/store/useTenantStore';
import { getAppCenterRecentlyUsedList } from '@/api/scene';
import { useQuery } from '@tanstack/react-query';
import { AppListItemType } from '@/types/api/app';
import { useRouter } from 'next/router';
import { tenantAppDetail, getAppCenterHomePageUsedList } from '@/api/scene';
import { serviceSideProps } from '@/utils/i18n';
import MyTooltip from '@/components/MyTooltip';
// import { getHomePageUsedList } from '@/api/app';
import { enterpriseCustomBoxData as customBoxDataTwo } from '@/constants/chat';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import GuideStepModal from '../deepeditor/components/GuideStepModal';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { reportAppsVisit } from '@/api/scene';
import { useAppStore } from '@/store/useAppStore';
import { Toast } from '@/utils/ui/toast';
import { HomePageUse } from '@/constants/api/chat';
import SvgIcon from '@/components/SvgIcon';
import { useSystemStore } from '@/store/useSystemStore';

const EducationHome: React.FC = () => {
  const { tenant, redirectHomePath } = useTenantStore();
  const { openOverlay } = useOverlayManager();
  const { setEditType } = useDeepEditStore();
  const [recentlyUsedList, setRecentlyUsedList] = useState<AppListItemType[]>([]);
  const { generalAppId, loadMyApps, setViewApp, viewApp, myApps } = useAppStore();
  // const [filterSceneId, setFilterSceneId] = useState(sceneId || 'recentlyUsed');
  const { isPc } = useSystemStore();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const router = useRouter();

  const [isHovered1, setIsHovered1] = useState(false);
  const [isHovered2, setIsHovered2] = useState(false);

  const [homePageData, setHomePageData] = useState<AppListItemType[]>([]);
  useQuery(['recentlyUsedList'], () => getAppCenterRecentlyUsedList(), {
    onSuccess: (data: AppListItemType[]) => {
      if (data) {
        setRecentlyUsedList(data);
      }
    }
  });

  useEffect(() => {
    if (!isPc) {
      router.push('/app/list/enterprise');
    }
    const fetchHomePageData = async () => {
      try {
        const data = await getAppCenterHomePageUsedList();

        setHomePageData(data);
      } catch (error) {}
    };

    fetchHomePageData();
  }, []);

  const handleAppClick = (app: any) => {
    setEditType('new');
    if (app.title === '智能备课') {
      return window.open('/deepeditor?mode=smartClass&appTaskTypeId=1', '_blank');
    }
    if (
      app.homePageUseType === HomePageUse.ActivityPlan ||
      app.homePageUseType === HomePageUse.PropagandaWriting
    ) {
      const currentApp = homePageData.find((item) => app.homePageUseType == item.homePageUse);

      if (currentApp && currentApp.appTaskTypeId) {
        window.open(
          `/deepeditor?appId=${currentApp.id}&appTaskTypeId=${currentApp.appTaskTypeId}&init=1`,
          '_blank'
        );
      } else {
        Toast.info('请关联工作台');
      }

      return;
    }

    if (app.pathname) {
      router.push(app.pathname);
    } else if (app.homePageUseType) {
      reportAppsVisit({ tenantAppId: getIdByHomePageUse(app.homePageUseType).appId });
      router.push({
        pathname: '/home',
        query: {
          appId: getIdByHomePageUse(app.homePageUseType).appId,
          activeRoute: redirectHomePath,
          sceneId: getIdByHomePageUse(app.homePageUseType).sceneId // Adjust as necessary
        }
      });
    }
  };

  // const handleAppClick = useCallback(
  //   (app: RightCommonAppItemType) => {
  //     const target = homeApps.find((item) => {
  //       return item.homePageUse == app.homePageUseType;
  //     });
  //     console.log(target);

  //     if (target) {
  //       return router.push({
  //         pathname: '/home',
  //         query: {
  //           appId: target.id,
  //           activeRoute: redirectHomePath
  //         }
  //       });
  //     }
  //     if (app.pathname) {
  //       return router.push({
  //         pathname: app.pathname,
  //         query: {
  //           activeRoute: redirectHomePath,
  //           isBack: '1'
  //         }
  //       });
  //     }
  //     Toast.info('敬请期待');
  //   },
  //   [homeApps]
  // );

  const getIdByHomePageUse = (homePageUseValue: number) => {
    const item = homePageData.find((entry) => entry.homePageUse === homePageUseValue);
    const appId = item?.id || '';
    const sceneId = item?.labelList?.[0]?.tenantSceneId || '';
    return { appId, sceneId };
  };

  return (
    <Flex
      flexDir="column"
      h="100%" // 使用视口高度确保父容器高度正确
      backgroundImage={
        tenant?.functionBackgroundImgUrl
          ? tenant.functionBackgroundImgUrl
          : '/imgs/app/enterprise_home_bg.png'
      }
      backgroundRepeat="no-repeat"
      backgroundSize="cover"
      alignItems="center"
      justifyContent="center" // 确保内容垂直居中
      overflow="auto"
    >
      <Box
        maxW="container.xl"
        px={respDims(48 * scaleFactor)}
        pb={respDims(18 * scaleFactor)}
        width={respDims(1200 * scaleFactor)}
        borderRadius={`${40 * scaleFactor}px`}
        position="relative"
      >
        <Box w="100%" h="100%">
          <Box
            h={respDims(120 * scaleFactor)}
            display="flex"
            justifyContent="center"
            alignItems="flex-end"
            color="#303133"
            textAlign="left"
          >
            <Flex
              fontSize={respDims('26fpx')}
              fontWeight="700"
              flexDir="column"
              justifyContent="center"
              alignItems="center"
              mb={respDims(35, 20)}
            >
              <Flex
                fontSize={respDims('26fpx')}
                fontWeight="700"
                // h={respDims(100 * scaleFactor)}
                // lineHeight={respDims(100 * scaleFactor)}
                overflow="hidden" // 确保文本不会溢出
                textOverflow="ellipsis" // 使用省略号表示溢出的文本
                whiteSpace="nowrap" // 防止文本换行
                mb={respDims(12)}
              >
                <Flex fontSize={respDims('32fpx')}>欢迎来到，</Flex>
                <Flex fontSize={respDims('32fpx')}>
                  <span
                    style={{
                      background: 'linear-gradient(0deg, #734BFF 0%, #AD50FF 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      overflow: 'hidden', // 确保文本不会溢出
                      textOverflow: 'ellipsis', // 使用省略号表示溢出的文本
                      whiteSpace: 'nowrap', // 防止文本换行
                      maxWidth: '35vw'
                    }}
                  >
                    {tenant?.fullName || tenant?.name}
                  </span>
                </Flex>
              </Flex>
              <Flex fontSize={respDims(18, 14)} fontWeight="500" color="#7f7f7f">
                智启未来，携手共赢。欢迎来到{tenant?.fullName || tenant?.name}
                ，让我们一起探索无限可能！
              </Flex>
            </Flex>
          </Box>
          <Grid templateRows="repeat(4, 1fr)" templateColumns="repeat(6, 1fr)" gap={respDims(15)}>
            <GridItem rowSpan={2} colSpan={4} position="relative" h={respDims(390)}>
              <Box
                w="100%"
                h="100%"
                bg="url('/imgs/home/<USER>')"
                bgSize="cover"
                bgPosition="center"
                borderRadius={`${20 * scaleFactor}px`}
                position="relative"
                zIndex={1}
                border={`#FFF ${1 * scaleFactor}px solid`}
                onClick={() => {
                  console.log('tenant');
                  reportAppsVisit({ tenantAppId: generalAppId || '' });
                  router.push({
                    pathname: '/home',
                    query: {
                      activeRoute: redirectHomePath,
                      isBack: '1'
                    }
                  });
                }}
              >
                <Lottie
                  name="dialogueEnterprise"
                  w="65%"
                  h="100%"
                  position="absolute"
                  top={'10px'}
                  right={'-30px'}
                ></Lottie>
                <Box
                  position="absolute"
                  top={respDims(70 * scaleFactor)}
                  left={respDims(48 * scaleFactor)}
                >
                  <Box mb={respDims(24)} position="relative">
                    <Box fontSize={respDims('34fpx')} fontWeight="600" color="#220079">
                      通用对话
                    </Box>
                    <SvgIcon
                      position="absolute"
                      name="appLine"
                      w={respDims(77)}
                      h={respDims(52)}
                      bottom={respDims(-20)}
                      left={respDims(0)}
                    ></SvgIcon>
                  </Box>
                  <Box
                    h={respDims(80 * scaleFactor)}
                    fontSize={respDims('14fpx')}
                    color="textPrimary.250"
                    pt={respDims(10)}
                  >
                    汇集全球领先的大模型，让AI能力持续领先
                  </Box>
                  <Button
                    flexShrink="0"
                    w={respDims(120 * scaleFactor)}
                    h={respDims(38 * scaleFactor)}
                    variant="solid"
                    bg="#774CFF linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)"
                    color="white"
                    fontWeight="bold"
                    _hover={{
                      boxShadow: `${4 * scaleFactor}px ${4 * scaleFactor}px ${10 * scaleFactor}px 0px rgba(140, 108, 255, 0.38)`
                    }}
                    fontSize={respDims('14fpx')}
                    borderRadius={respDims(50 * scaleFactor)}
                  >
                    开始对话
                  </Button>
                </Box>
              </Box>
            </GridItem>
            <GridItem
              key={`gridOne-${1}`}
              rowSpan={1}
              colSpan={2}
              position="relative"
              h={respDims(185)}
            >
              <CustomBox
                iconName="documentSummary"
                backgroundImage="customBox"
                iconColor="#A8ABB2"
                title="文档总结"
                colSpan={2}
                onClick={() => {
                  router.push({
                    pathname: '/home',
                    query: {
                      appId: getIdByHomePageUse(12).appId,
                      activeRoute: redirectHomePath,
                      sceneId: getIdByHomePageUse(12).sceneId
                    }
                  });
                }}
                cursor="pointer"
                description={
                  <Box className="homeDescription">
                    AI智能文档总结功能，一键提炼文本核心，支持生成脑图，助力高效阅读和信息快速吸收
                  </Box>
                }
              />
            </GridItem>

            <GridItem
              key={`gridOne-${2}`}
              rowSpan={1}
              colSpan={2}
              position="relative"
              h={respDims(185)}
            >
              <CustomBox
                iconName="textAssistant"
                backgroundImage="customBox"
                iconColor="#A8ABB2"
                title="文本生成助手"
                colSpan={2}
                cursor="pointer"
                onClick={() => {
                  window.open('/deepeditor?mode=smartClass&appTaskTypeId=3&init=1', '_blank');
                }}
                description={
                  <Box className="homeDescription">
                    高效地辅助用户创作和精炼各类文本，无论是商业计划书还是创意文案
                  </Box>
                }
              />
            </GridItem>

            {customBoxDataTwo.map((app, index) => (
              <GridItem
                key={`gridTwo-${index}`}
                rowSpan={app.rowSpan || 1}
                colSpan={app.colSpan || 1}
                h={respDims(185)}
              >
                <CustomBox
                  backgroundImage={app.colSpan === 2 ? 'customBox' : 'customBox1'}
                  iconName={app.iconName}
                  colSpan={app.colSpan}
                  iconColor="#A8ABB2"
                  title={app.fullName}
                  cursor="pointer"
                  onClick={() => handleAppClick(app)}
                  description={
                    <MyTooltip overflowOnly label={app.description}>
                      <Box className="homeDescription textEllipsis2">{app.description}</Box>
                    </MyTooltip>
                  }
                />
              </GridItem>
            ))}
          </Grid>
        </Box>
      </Box>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default EducationHome;
