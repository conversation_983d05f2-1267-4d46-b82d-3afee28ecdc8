import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex, useDisclosure } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import React, { useEffect, useState } from 'react';
import { useSystemStore } from '@/store/useSystemStore';
import ChatPanel from '@/components/ChatPanel';
import AppList from './components/AppList';
import PageLayout from '@/components/PageLayout';
import HomeNavbar from './components/HomeNavbar';
import AppInfo from './components/AppInfo';
import { useChatStore } from '@/store/useChatStore';
import { getClientAppFormDetail } from '@/api/app';
import { ClientAppFormDetailType } from '@/types/api/app';
import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';

const AppTopHeader = () => {
  const router = useRouter();

  return (
    <Box
      as="header"
      bgColor="white"
      p={respDims(4)}
      h={rpxDim(96)}
      boxShadow="md"
      display="flex"
      alignItems="center"
      justifyContent="space-between"
    >
      <Box>
        <SvgIcon
          name="chevronLeft"
          w={rpxDim(48)}
          h={rpxDim(48)}
          cursor="pointer"
          onClick={() => router.back()}
        />
      </Box>
      <Box fontSize="lg" fontWeight="bold" textAlign="center" flex="1">
        宝安智教
      </Box>
    </Box>
  );
};

const Home = ({
  appId = '',
  chatId = '',
  activeRoute = ''
}: {
  appId?: string;
  chatId?: string;
  activeRoute?: string;
}) => {
  const { isPc } = useSystemStore();

  const { chatData, setClientAppFormDetail, setQuestionFormGuideStep } = useChatStore();

  const [isDomain, setIsDomain] = useState(false);

  const { isOpen: isOpenApplist, onOpen: onOpenAppList, onClose: onCloseAppList } = useDisclosure();

  useEffect(() => {
    if (!chatData.appId && (chatData.app as any)?.type !== 'simple') {
      return;
    }

    if (chatData.appId) {
      getClientAppFormDetail(chatData.appId)
        .then((res) => {
          setClientAppFormDetail(res as ClientAppFormDetailType);
          setQuestionFormGuideStep(1);
        })
        .catch((error) => {});
    }
  }, [chatData, setClientAppFormDetail, setQuestionFormGuideStep]);

  useEffect(() => {
    if (!!isBaoAnDomain()) {
      setIsDomain(true);
    } else {
      setIsDomain(false);
    }
  }, [window.location]);

  return (
    <PageLayout
      {...(!isPc && {
        bgImage: 'url(/imgs/v2/gradient_bg4.png)',
        bgRepeat: 'no-repeat',
        bgSize: '100% auto',
        navbar: (
          <>
            <HomeNavbar onOpenAppList={onOpenAppList} />
          </>
        )
      })}
    >
      <Flex flexDir="column" w="100%" h="100%">
        <Flex h="100%" w="100%" alignItems="stretch">
          <Flex flex="1 0 0" overflow="hidden" pos="relative">
            <ChatPanel appId={appId} chatId={chatId} activeRoute={activeRoute} flex="1" />

            {isPc ? (
              <AppList ml={respDims(16)} w={respDims(442, 200)} flexShrink={0} h="100%" />
            ) : (
              <AppList w="100%" maxH="80vh" isOpen={isOpenApplist} onClose={onCloseAppList} />
            )}
          </Flex>

          <AppInfo />
        </Flex>
      </Flex>
    </PageLayout>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      appId: context.query?.appId || '',
      activeRoute: context.query?.activeRoute || '',
      chatId: context.query?.chatId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default Home;
