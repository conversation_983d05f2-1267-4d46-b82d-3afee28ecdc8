import React, { useState, useEffect, useCallback } from 'react';
import { Box, Flex, Button, Image, Grid, GridItem } from '@chakra-ui/react';

const scaleFactor = 1.24; // 放大比例

import { respDims } from '@/utils/chakra';
import Lottie from '@/components/Lottie';
import CustomBox from './components/CustomBox';
import { useTenantStore } from '@/store/useTenantStore';
import { getAppCenterRecentlyUsedList } from '@/api/scene';
import { useQuery } from '@tanstack/react-query';
import { AppListItemType } from '@/types/api/app';
import { useRouter } from 'next/router';
import { tenantAppDetail, getAppCenterHomePageUsedList } from '@/api/scene';
import { serviceSideProps } from '@/utils/i18n';
import MyTooltip from '@/components/MyTooltip';
// import { getHomePageUsedList } from '@/api/app';
import { educationCustomBoxData as customBoxDataTwo } from '@/constants/chat';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import GuideStepModal from '../deepeditor/components/GuideStepModal';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { reportAppsVisit } from '@/api/scene';
import { useAppStore } from '@/store/useAppStore';
import { Toast } from '@/utils/ui/toast';
import { HomePageUse } from '@/constants/api/chat';
import { useSystemStore } from '@/store/useSystemStore';

const EducationHome: React.FC = () => {
  const { tenant, redirectHomePath } = useTenantStore();
  const { openOverlay } = useOverlayManager();
  const { setEditType } = useDeepEditStore();
  const [recentlyUsedList, setRecentlyUsedList] = useState<AppListItemType[]>([]);
  const { generalAppId, loadMyApps, setViewApp, viewApp, myApps } = useAppStore();
  // const [filterSceneId, setFilterSceneId] = useState(sceneId || 'recentlyUsed');
  const { isPc } = useSystemStore();

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const router = useRouter();

  const [isHovered1, setIsHovered1] = useState(false);
  const [isHovered2, setIsHovered2] = useState(false);

  const [homePageData, setHomePageData] = useState<AppListItemType[]>([]);
  useQuery(['recentlyUsedList'], () => getAppCenterRecentlyUsedList(), {
    onSuccess: (data: AppListItemType[]) => {
      if (data) {
        setRecentlyUsedList(data);
      }
    }
  });

  useEffect(() => {
    if (!isPc) {
      router.push('/app/list/educationAdvanced');
    }
    const fetchHomePageData = async () => {
      try {
        const data = await getAppCenterHomePageUsedList();

        setHomePageData(data);
      } catch (error) {}
    };

    fetchHomePageData();
  }, []);

  const handleAppClick = (app: any) => {
    setEditType('new');
    if (app.title === '智能备课') {
      return window.open('/deepeditor?mode=smartClass&appTaskTypeId=1', '_blank');
    }
    if (
      app.homePageUseType === HomePageUse.ActivityPlan ||
      app.homePageUseType === HomePageUse.PropagandaWriting
    ) {
      const currentApp = homePageData.find((item) => app.homePageUseType == item.homePageUse);

      if (currentApp && currentApp.appTaskTypeId) {
        window.open(
          `/deepeditor?appId=${currentApp.id}&appTaskTypeId=${currentApp.appTaskTypeId}&init=1`,
          '_blank'
        );
      } else {
        Toast.info('请关联工作台');
      }

      return;
    }

    if (app.pathname) {
      router.push(app.pathname);
    } else if (app.homePageUseType) {
      reportAppsVisit({ tenantAppId: getIdByHomePageUse(app.homePageUseType).appId });
      router.push({
        pathname: '/home',
        query: {
          appId: getIdByHomePageUse(app.homePageUseType).appId,
          activeRoute: redirectHomePath,
          sceneId: getIdByHomePageUse(app.homePageUseType).sceneId // Adjust as necessary
        }
      });
    }
  };

  // const handleAppClick = useCallback(
  //   (app: RightCommonAppItemType) => {
  //     const target = homeApps.find((item) => {
  //       return item.homePageUse == app.homePageUseType;
  //     });
  //     console.log(target);

  //     if (target) {
  //       return router.push({
  //         pathname: '/home',
  //         query: {
  //           appId: target.id,
  //           activeRoute: redirectHomePath
  //         }
  //       });
  //     }
  //     if (app.pathname) {
  //       return router.push({
  //         pathname: app.pathname,
  //         query: {
  //           activeRoute: redirectHomePath,
  //           isBack: '1'
  //         }
  //       });
  //     }
  //     Toast.info('敬请期待');
  //   },
  //   [homeApps]
  // );

  const getIdByHomePageUse = (homePageUseValue: number) => {
    const item = homePageData.find((entry) => entry.homePageUse === homePageUseValue);
    const appId = item?.id || '';
    const sceneId = item?.labelList?.[0]?.tenantSceneId || '';
    return { appId, sceneId };
  };

  return (
    <Flex
      flexDir="column"
      h="100%" // 使用视口高度确保父容器高度正确
      backgroundImage={
        tenant?.functionBackgroundImgUrl
          ? tenant.functionBackgroundImgUrl
          : '/imgs/app/education_advanced_home_bg.png'
      }
      backgroundRepeat="no-repeat"
      backgroundSize="cover"
      alignItems="center"
      justifyContent="center" // 确保内容垂直居中
      overflow="auto"
    >
      <Box
        maxW="container.xl"
        px={respDims(48 * scaleFactor)}
        pb={respDims(18 * scaleFactor)}
        width={respDims(1200 * scaleFactor)}
        borderRadius={`${40 * scaleFactor}px`}
        position="relative"
      >
        <Box w="100%" h="100%">
          <Box
            h={respDims(100 * scaleFactor)}
            display="flex"
            justifyContent="center"
            alignItems="flex-end"
            color="#303133"
            textAlign="left"
          >
            <Flex
              fontSize={respDims('26fpx')}
              fontWeight="700"
              h={respDims(100 * scaleFactor)}
              lineHeight={respDims(100 * scaleFactor)}
              overflow="hidden" // 确保文本不会溢出
              textOverflow="ellipsis" // 使用省略号表示溢出的文本
              whiteSpace="nowrap" // 防止文本换行
              letterSpacing={`${1 * scaleFactor}px`}
            >
              <Flex fontSize={respDims('32fpx')}>欢迎来到</Flex>
              <Flex fontSize={respDims('32fpx')} mx="2px">
                <span
                  style={{
                    background: 'linear-gradient(0deg, #734BFF 0%, #AD50FF 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    overflow: 'hidden', // 确保文本不会溢出
                    textOverflow: 'ellipsis', // 使用省略号表示溢出的文本
                    whiteSpace: 'nowrap', // 防止文本换行
                    maxWidth: '35vw'
                  }}
                >
                  {tenant?.fullName || tenant?.name}
                </span>
              </Flex>
              <Flex fontSize={respDims('32fpx')}>我是你的专属AI助手～</Flex>
            </Flex>
          </Box>
          <Grid templateRows="repeat(4, 1fr)" templateColumns="repeat(6, 1fr)" gap={respDims(15)}>
            <GridItem rowSpan={2} colSpan={4} position="relative" h={respDims(390)}>
              <Box
                w="100%"
                h="100%"
                bg="url('/imgs/home/<USER>')"
                bgSize="cover"
                bgPosition="center"
                borderRadius={`${20 * scaleFactor}px`}
                position="relative"
                border={`#FFF ${1 * scaleFactor}px solid`}
                overflow="hidden"
                onClick={() => {
                  console.log('tenant');
                  reportAppsVisit({ tenantAppId: generalAppId || '' });
                  router.push('/aiPlatform');
                }}
              >
                <Lottie name="dialogueEducationAdvanced" w="100%" h="100%"></Lottie>
                <Box
                  position="absolute"
                  top={respDims(88 * scaleFactor)}
                  left={respDims(48 * scaleFactor)}
                >
                  <Box
                    fontSize={respDims('34fpx')}
                    fontWeight="600"
                    zIndex={0}
                    color="#220079"
                    pos="relative"
                  >
                    应用精选
                    <Box
                      pos="absolute"
                      zIndex={-1}
                      w={respDims(145)}
                      h={respDims(11)}
                      bgColor="#B5F1FF"
                      flexShrink="0"
                      bottom="3px"
                    />
                  </Box>
                  <Box
                    h={respDims(100 * scaleFactor)}
                    fontSize={respDims('14fpx')}
                    color="#5B76C9"
                    pt={respDims(50)}
                    whiteSpace="wrap"
                    maxW={respDims(230 * scaleFactor)}
                  >
                    专注教育交流，解答学习困惑，共创知识成长之路
                  </Box>
                </Box>
              </Box>
            </GridItem>

            <GridItem
              key={`gridOne-${1}`}
              rowSpan={1}
              colSpan={2}
              position="relative"
              h={respDims(185)}
            >
              <CustomBox
                iconName="PPTGeneration"
                backgroundImage="customBox"
                iconColor="#A8ABB2"
                title="PPT生成"
                colSpan={2}
                onClick={() => {
                  reportAppsVisit({ tenantAppId: generalAppId || '' });
                  router.push(`/pptGenerate`);
                }}
                // height="calc(50% - 10px)"
                // margin="0 0 10px 0"
                cursor="pointer"
                description={
                  <>
                    <Box className="homeDescription">一句话,一段文本，一个文件，一个链接</Box>
                    <Box className="homeDescription">一键生成精美PPT</Box>
                  </>
                }
              />
            </GridItem>

            <GridItem
              key={`gridOne-${2}`}
              rowSpan={1}
              colSpan={2}
              position="relative"
              h={respDims(185)}
            >
              <CustomBox
                iconName="textToImage"
                backgroundImage="customBox"
                iconColor="#A8ABB2"
                title="文生图"
                colSpan={2}
                // height="calc(50% - 10px)"
                // margin="10px 0 0 0"
                cursor="pointer"
                onClick={() => {
                  reportAppsVisit({ tenantAppId: generalAppId || '' });
                  router.push({
                    pathname: '/home',
                    query: {
                      appId: getIdByHomePageUse(1).appId,
                      activeRoute: redirectHomePath,
                      sceneId: getIdByHomePageUse(1).sceneId
                    }
                  });
                }}
                description={
                  <>
                    <Box className="homeDescription">一句优美的诗词、一段风景的描述</Box>
                    <Box className="homeDescription">一键生成生动有趣的图片</Box>
                  </>
                }
              />
            </GridItem>

            {customBoxDataTwo.map((app, index) => (
              <GridItem
                key={`gridTwo-${index}`}
                rowSpan={app.rowSpan || 1}
                colSpan={app.colSpan || 1}
                h={respDims(185)}
              >
                <CustomBox
                  backgroundImage={app.colSpan === 2 ? 'customBox' : 'customBox1'}
                  iconName={app.iconName}
                  colSpan={app.colSpan}
                  iconColor="#A8ABB2"
                  title={app.fullName}
                  cursor="pointer"
                  onClick={() => handleAppClick(app)}
                  description={
                    <MyTooltip overflowOnly label={app.description}>
                      <Box className="homeDescription">{app.description}</Box>
                    </MyTooltip>
                  }
                />
              </GridItem>
            ))}
            <GridItem key={`gridTwoEnd-${1}`} rowSpan={1} colSpan={2} h={respDims(185)}>
              <Box
                w="100%"
                h="100%"
                borderRadius={respDims(20)}
                onClick={() => {
                  router.push({
                    pathname: '/home',
                    query: {
                      appId: getIdByHomePageUse(11).appId,
                      activeRoute: redirectHomePath,
                      sceneId: getIdByHomePageUse(11).sceneId
                    }
                  });
                }}
                border={`#FFF ${1 * scaleFactor}px solid`}
                position="relative"
                overflow="hidden"
                cursor="pointer"
                // _hover={{
                //   '& .hover-image': {
                //     transform: 'scale(1.2)',
                //     transition: 'transform 0.3s ease-in-out'
                //   }
                // }}
                transition="all 0.2s ease-in-out" // 添加过渡效果
                _hover={{
                  transform: 'translateY(-5px)', // 上移效果
                  boxShadow: '0px 9px 15.6px 0px rgba(197, 218, 248, 0.47)'
                }}
              >
                <Image
                  src="/imgs/home/<USER>"
                  w="100%"
                  h="100%"
                  alt=""
                  className="hover-image"
                  zIndex={1}
                />
              </Box>
            </GridItem>
            <GridItem
              key={`gridTwoEnd-${2}`}
              rowSpan={1}
              colSpan={2}
              position="relative"
              h={respDims(185)}
            >
              <CustomBox
                iconName="mindMapping"
                backgroundImage="customBox"
                iconColor="#A8ABB2"
                title="思维导图"
                colSpan={2}
                cursor="pointer"
                onClick={() =>
                  router.push({
                    pathname: '/home',
                    query: {
                      appId: getIdByHomePageUse(7).appId,
                      activeRoute: redirectHomePath,
                      sceneId: getIdByHomePageUse(7).sceneId
                    }
                  })
                }
                description={
                  <Box className="homeDescription">AI绘制思维导图，快速整理信息脉络</Box>
                }
              />
            </GridItem>
            <GridItem key={`gridTwo-${1}`} rowSpan={1} colSpan={2} h={respDims(185)}>
              <Box
                w="100%"
                h="100%"
                borderRadius={respDims(20)}
                onClick={() => {
                  window.open('/deepeditor?mode=smartClass&appTaskTypeId=4&init=1', '_blank');
                }}
                position="relative"
                border={`#FFF ${1 * scaleFactor}px solid`}
                overflow="hidden"
                cursor="pointer"
                _hover={{
                  '& .hover-image': {
                    transform: 'scale(1.2)',
                    transition: 'transform 0.3s ease-in-out'
                  }
                }}
              >
                <Box
                  fontSize={respDims(19 * scaleFactor)}
                  fontWeight="500"
                  color="#290A79"
                  zIndex={3}
                  position="absolute"
                  top={respDims(33)}
                  left={respDims(30)}
                >
                  文档撰写
                </Box>
                <Box
                  fontSize={respDims(11.5 * scaleFactor)}
                  fontWeight="400"
                  color="#5B76C9"
                  zIndex={3}
                  position="absolute"
                  top={respDims(68)}
                  left={respDims(30)}
                >
                  自由书写，文案风格任你选
                  <br />
                  扩写改写，续写总结皆精通
                </Box>
                <Box
                  fontSize={respDims(14 * scaleFactor)}
                  fontWeight="5800"
                  color="#1D2129"
                  zIndex={3}
                  border={'1px solid #FFFFFF'}
                  boxShadow={'4px 4px 10px 0px #CFD5FF'}
                  position="absolute"
                  background={'rgba(255,255,255,0.52)'}
                  borderRadius={respDims(50)}
                  height={respDims(31)}
                  width={respDims(113)}
                  top={respDims(123)}
                  left={respDims(30)}
                  textAlign={'center'}
                  lineHeight={respDims(31)}
                  transition="box-shadow 0.3s ease"
                  _hover={{
                    boxShadow: `${4 * scaleFactor}px ${4 * scaleFactor}px ${10 * scaleFactor}px 0px  #BBC3FF`
                  }}
                  onClick={() => {
                    router.push({
                      pathname: '/home',
                      query: {
                        appId: getIdByHomePageUse(8).appId,
                        activeRoute: redirectHomePath,
                        sceneId: getIdByHomePageUse(8).sceneId
                      }
                    });
                  }}
                >
                  开始创作
                </Box>
                <Image
                  src="/imgs/home/<USER>"
                  w="100%"
                  h="100%"
                  alt=""
                  className="hover-image"
                  zIndex={1}
                />
              </Box>
            </GridItem>
          </Grid>
        </Box>
      </Box>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default EducationHome;
