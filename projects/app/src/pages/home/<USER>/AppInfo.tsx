import { tenantAppDetail } from '@/api/scene';
import ChatSettingsModal from '@/components/ChatSettingsModal';
import History from '@/components/Layout/components/History';
import Lottie from '@/components/Lottie';
import MyMenu from '@/components/MyMenu';
import MyTooltip from '@/components/MyTooltip';
import SvgIcon from '@/components/SvgIcon';
import { ChatHistoryItemType } from '@/fastgpt/global/core/chat/type';
import useHistoryData from '@/hooks/useHistoryData';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useAppStore } from '@/store/useAppStore';
import { useSystemStore } from '@/store/useSystemStore';
import { TenantAppDetailType } from '@/types/api/scene';
import { respDims, rpxDim } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import {
  Box,
  Button,
  Center,
  Flex,
  Image,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  MenuButton,
  Tooltip
} from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useRef, useState } from 'react';
import AdvancedDetail from './AdvancedDetail';
import SimpleDetail from './SimpleDetail';
import AppContextProvider, {
  AppContext,
  SimpleAppPerm
} from '@/components/AppDetail/components/context';
import { useContextSelector } from 'use-context-selector';
import { PermissionTypeEnum } from '@/constants/permission';
import { DataSource } from '@/constants/common';
import { useChatStore } from '@/store/useChatStore';
import { useUpdateEffect } from 'ahooks';
import { appWorkflow2Form } from '@/fastgpt/global/core/app/utils';
import { v1Workflow2V2 } from '@/fastgpt/web/core/workflow/adapt';
import { useDatasetStore } from '@/store/useDatasetStore';
import { ModeTypeEnum } from '@/constants/api/app';

export type ChatHistoryRef = {
  loadMore: () => void;
};
interface AppInfoInnerProps extends ChakraProps {
  onDone?: () => void;
}

const AppInfoInner: React.FC<AppInfoInnerProps> = ({ onDone, ...props }) => {
  const { viewApp, setViewApp } = useAppStore();
  const { chatData } = useChatStore();
  const { isPc } = useSystemStore();
  const [queryKeyword, setQueryKeyword] = useState('');
  const [inputKeyword, setInputKeyword] = useState('');
  const { originAppDetail, clientAppDetail, appForm, setAppForm } = useContextSelector(
    AppContext,
    (v) => v
  );
  const { loadAllDatasets } = useDatasetStore();

  useEffect(() => {
    loadAllDatasets();

    setAppForm({
      ...appWorkflow2Form({
        nodes: originAppDetail.modules,
        chatConfig: originAppDetail.chatConfig
      }),
      promptList: clientAppDetail.promptList || [],
      isStructuredPrompt: clientAppDetail.isStructuredPrompt,
      filesList: clientAppDetail.filesList || []
    });
    if (originAppDetail.version !== 'v2') {
      setAppForm({
        ...appWorkflow2Form({
          nodes: v1Workflow2V2((originAppDetail.modules || []) as any)?.nodes,
          chatConfig: originAppDetail.chatConfig
        }),
        promptList: clientAppDetail.promptList || [],
        isStructuredPrompt: clientAppDetail.isStructuredPrompt,
        filesList: clientAppDetail.filesList || []
      });
    }
  }, [originAppDetail]);

  const { data, loadMoreData, deleteData, refetch } = useHistoryData(
    { size: 20, current: 1, appId: viewApp?.id, keyword: queryKeyword },
    'histories'
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const [settingsChatId, setSettingsChatId] = useState('');
  const router = useRouter();
  const [currentItemId, setCurrentItemId] = useState<string>();
  const [isItemTouching, setIsItemTouching] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { openOverlay } = useOverlayManager();
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isSimpleDetail, setIsSimpleDetail] = useState(true);
  const [isAdvancedDetail, setIsAdvancedDetail] = useState(true);
  const [getAppDetail, setGetAppDetail] = useState({} as TenantAppDetailType);

  const list = useMemo(() => {
    const list = data && data.length ? data.map((it) => ({ ...it, title: it.title })) : [];
    if (!list.length) {
      return list;
    }
    const today = dayjs().startOf('day');
    let lastType = '';
    const result: (ChatHistoryItemType | string)[] = [];
    list
      .sort((l, r) => dayjs(r.updateTime).valueOf() - dayjs(l.updateTime).valueOf())
      .forEach((it) => {
        const t = dayjs(it.updateTime).startOf('day');
        const d = today.diff(t, 'day');
        let type = '';
        if (d === 0) {
          type = '今天';
        } else if (d == 1) {
          type = '昨天';
        } else if (d <= 7 && (t.day() ? t.day() : 7) < (today.day() ? today.day() : 7)) {
          type = '本周';
        } else if (t.year() === today.year()) {
          type = `${t.month() + 1}月`;
        } else {
          type = `${t.year()}年`;
        }
        if (type !== lastType) {
          result.push(type);
          lastType = type;
        }
        result.push(it);
      });
    return result;
  }, [data]);

  const onClickItem = async (item: ChatHistoryItemType) => {
    setCurrentItemId(item.chatId);
    try {
      const data: TenantAppDetailType | null = await tenantAppDetail({ id: item.tenantAppId! });
      if (data && data.sceneList.length > 0) {
        const sceneId = data.sceneList[0].tenantSceneId;
        router.push(`/home?appId=${item.tenantAppId}&chatId=${item.chatId}&sceneId=${sceneId}789`);
        onDone?.();
      } else {
        router.push(`/home?appId=${item.tenantAppId}&chatId=${item.chatId}`);
      }
    } catch (error) {}
  };

  const onRemoveItem = (item: ChatHistoryItemType) => {
    MessageBox.confirm({
      title: '操作确认',
      content: '确定删除该记录？',
      onOk: async () => {
        await deleteData(item.chatId, item.id || '');
      }
    });
  };

  const onSearch = () => {
    setQueryKeyword(inputKeyword);
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  };

  const onSimpleDetail = () => {
    setIsSimpleDetail(false);
  };

  const onAdvancedDetail = () => {
    setIsAdvancedDetail(false);
  };

  const onClickNewChat = () => {
    if (!viewApp) {
      return;
    }
    const sceneId =
      router.query.sceneId &&
      viewApp.labelList?.some((it) => it.tenantSceneId == router.query.sceneId)
        ? router.query.sceneId
        : viewApp.labelList?.[0]?.tenantSceneId || '';
    router.push(`/home?appId=${viewApp.id}&sceneId=${sceneId}`);
  };

  useQuery(['appDetail', viewApp?.id], () => tenantAppDetail({ id: viewApp?.id! }), {
    enabled: !!viewApp?.id,
    onSuccess: (data) => {
      setGetAppDetail(data);
    }
  });

  useEffect(() => {
    if (!isInitialLoad) {
      refetch();
    } else {
      setIsInitialLoad(false);
    }
  }, [queryKeyword, refetch]);

  useEffect(() => {
    const handleScroll = async () => {
      if (containerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
        if (scrollTop + clientHeight >= scrollHeight - 10 && !isLoadingMore) {
          setIsLoadingMore(true);
          await loadMoreData();
          setIsLoadingMore(false);
        }
      }
    };

    if (containerRef.current) {
      containerRef.current.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, [loadMoreData, isLoadingMore]);

  useEffect(() => {
    if (viewApp?.appId) {
      refetch();
      setQueryKeyword('');
      setInputKeyword('');
    }
  }, [viewApp, refetch]);

  useUpdateEffect(() => {
    setViewApp();
  }, [chatData.appId]);

  useEffect(
    () => () => {
      setViewApp();
    },
    [setViewApp]
  );

  return viewApp && isAdvancedDetail && isSimpleDetail ? (
    <Flex
      flexDir="column"
      align="center"
      p={respDims(24)}
      bgColor="#ffffff"
      bgRepeat="no-repeat"
      bgSize="100% auto"
      boxShadow="0px 0px 20px 0px rgba(0,0,0,0.04)"
      {...props}
    >
      <SvgIcon
        mt={respDims(16)}
        name="close"
        w={respDims('22fpx')}
        h={respDims('22fpx')}
        color="#A8ABB2"
        cursor="pointer"
        alignSelf="flex-end"
        onClick={() => setViewApp()}
      />

      <Image
        mt={respDims(16)}
        w={respDims('72fpx')}
        h={respDims('72fpx')}
        src={viewApp.avatarUrl}
        alt=""
      />

      <Box
        mt={respDims(24)}
        color="#303133"
        fontSize={respDims('20fpx')}
        fontWeight="700"
        lineHeight={respDims('23fpx')}
      >
        {viewApp.name}
      </Box>

      <Box
        mt={respDims(16)}
        color="#606266"
        fontSize={respDims('15fpx')}
        lineHeight={respDims('18fpx')}
        minH="18px"
        style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitBoxOrient: 'vertical',
          WebkitLineClamp: '1',
          whiteSpace: 'normal'
        }}
      >
        {viewApp.intro || '暂无介绍'}
      </Box>

      <Button
        my={respDims(20)}
        flexShrink="0"
        w={respDims('394fpx')}
        h={respDims('38fpx')}
        variant="solid"
        colorScheme="primary"
        color="white"
        fontWeight="bold"
        _hover={{
          bgColor: 'primary.400'
        }}
        fontSize={respDims('14fpx')}
        borderRadius={respDims(50)}
        onClick={onClickNewChat}
      >
        新建对话
      </Button>

      <Box mb={respDims(16)} w="100%" h="1px" borderBottom="1px solid #E5E7EB"></Box>

      <Flex justifyContent="space-between" w="100%" mb="14px">
        <Box alignSelf="flex-start" fontSize="15px" color="#303133" fontWeight="500">
          配置项
        </Box>
        <Box
          alignSelf="flex-start"
          fontSize="14px"
          color="#4E5969"
          fontWeight="400"
          p="8px"
          borderRadius="8px"
          mr="16px"
          _hover={{
            bg: 'rgba(0,0,0,0.03)'
          }}
          cursor="pointer"
          onClick={() => {
            getAppDetail.mode === 1 ? onSimpleDetail() : onAdvancedDetail();
          }}
        >
          详情
        </Box>
      </Flex>

      {getAppDetail.mode === ModeTypeEnum.simple ? (
        <Flex w="100%" alignItems="center" overflowX="auto" overflowY="hidden" minH="32px" pb="2px">
          {appForm.promptList && appForm.promptList?.length > 0 && (
            <Box
              fontSize="14px"
              color="#909399"
              p="2px 16px"
              borderRadius="8px"
              fontWeight="500"
              border="1px solid #E5E7EB"
              mr="8px"
            >
              Prompt
            </Box>
          )}

          {appForm.filesList && appForm.filesList.length > 0 && (
            <MyTooltip
              label={
                appForm.filesList.length === 1 ? '知识背景' : `知识背景x${appForm.filesList.length}`
              }
            >
              <SvgIcon name="appBgKnowledge" w="26px" h="26px" color="#4E5969" mr="8px" />
            </MyTooltip>
          )}
          {appForm.dataset.datasets && appForm.dataset.datasets.length > 0 && (
            <MyTooltip
              label={
                appForm.dataset.datasets.length === 1
                  ? '关联知识库'
                  : `关联知识库x${appForm.dataset.datasets.length}`
              }
            >
              <SvgIcon name="appKnowledgeBase" w="26px" h="26px" color="#4E5969" mr="8px" />
            </MyTooltip>
          )}
          {appForm.selectedTools &&
            appForm.selectedTools.length > 0 &&
            appForm.selectedTools.map((tool, index) => (
              <MyTooltip key={index} label={tool.name}>
                <SvgIcon name="appTool" w="26px" h="26px" color="#4E5969" mr="8px" />
              </MyTooltip>
            ))}
        </Flex>
      ) : (
        <Flex w="100%" alignItems="center">
          {getAppDetail.filesList && getAppDetail.filesList.length > 0 && (
            <MyTooltip
              label={
                getAppDetail.filesList.length === 1
                  ? '知识背景'
                  : `知识背景x${getAppDetail.filesList.length}`
              }
            >
              <SvgIcon name="appBgKnowledge" w="26px" h="26px" color="#4E5969" mr="8px" />
            </MyTooltip>
          )}
          <MyTooltip label="应用编排">
            <SvgIcon name="appAdvancedAgent" w="26px" h="26px" color="#4E5969" />
          </MyTooltip>
        </Flex>
      )}

      <Box
        pt={respDims(16)}
        mb={respDims(16)}
        w="100%"
        h="1px"
        borderBottom="1px solid #E5E7EB"
      ></Box>

      <Box alignSelf="flex-start" fontSize="15px" color="#303133" fontWeight="500">
        历史会话
      </Box>

      <Box
        pt={respDims('32rpx', 20)}
        pos="sticky"
        top={respDims('-28rpx', -12)}
        zIndex="1"
        w="100%"
      >
        <InputGroup>
          <InputLeftElement
            {...(isPc
              ? { w: respDims('46fpx'), h: respDims('36fpx') }
              : {
                  w: rpxDim(100),
                  h: rpxDim(72)
                })}
            onClick={onSearch}
          >
            <SvgIcon
              name="search"
              {...(isPc ? { w: '14px', h: '14px' } : { w: rpxDim(36), h: rpxDim(36) })}
              color="#4E5969"
            />
          </InputLeftElement>
          <Input
            placeholder="搜索历史记录"
            border="none"
            autoFocus={false}
            {...(isPc
              ? {
                  h: respDims('36fpx'),
                  bgColor: 'rgba(0,0,0,0.03)',
                  fontSize: respDims('14fpx'),
                  lineHeight: respDims('22fpx'),
                  _placeholder: {
                    fontsize: 'inherit',
                    lineHeight: 'inherit',
                    color: '#A8ABB2'
                  }
                }
              : {
                  h: rpxDim(72),
                  bgColor: 'rgba(0,0,0,0.03)',
                  fontSize: rpxDim(28),
                  lineHeight: rpxDim(44),
                  borderRadius: rpxDim(100)
                })}
            value={inputKeyword}
            onChange={(e) => setInputKeyword(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                onSearch();
                if (!isPc) {
                  e.currentTarget.blur();
                  e.currentTarget.scrollIntoView({
                    block: 'start'
                  });
                }
              }
            }}
          />
          <MyTooltip
            label="查看全部历史会话"
            placement="right"
            shouldWrapChildren={false}
            color="#FFFFFF"
            bg="rgba(0,0,0,0.9)"
            fontSize={respDims('14fpx')}
            lineHeight={respDims('22fpx')}
          >
            <InputRightElement pb="6px">
              <SvgIcon
                mr={respDims(20)}
                name="chatHistoryConversation"
                cursor="pointer"
                color="#858585"
                _hover={{
                  color: '#7D4DFF'
                }}
                onClick={() => {
                  openOverlay({
                    name: 'history',
                    Overlay: History,
                    props: {}
                  });
                }}
                {...(isPc ? { w: '20px', h: '20px' } : { w: rpxDim(36), h: rpxDim(36) })}
              />
            </InputRightElement>
          </MyTooltip>
        </InputGroup>
      </Box>

      <Box
        ref={containerRef}
        w="100%"
        position="relative"
        overflowY="auto"
        overflowX="hidden"
        h="100%"
        {...(!isPc && {
          userSelect: 'none'
        })}
      >
        {list?.map((item, index) => {
          if (typeof item === 'string') {
            return (
              <Box
                key={`${item}-${index}`}
                mt={respDims(24)}
                mb={respDims(16)}
                color="#909399"
                fontSize={respDims('28rpx', '16fpx')}
                lineHeight={respDims('33rpx', '19fpx')}
                fontWeight="bold"
              >
                {item}
              </Box>
            );
          }
          return (
            <Flex
              key={item.id}
              position="relative"
              alignItems="center"
              h={respDims('88rpx', '44fpx')}
              px={respDims('24rpx', 12)}
              color="#303133"
              fontSize={respDims('28rpx', '14fpx')}
              borderRadius={respDims('16rpx', 8)}
              bgColor={
                item.chatId === currentItemId ? (isPc ? '#F3F4F6' : '#F8FAFC') : 'transparent'
              }
              cursor="pointer"
              {...(isPc && {
                _hover: {
                  bgColor: 'primary.50',
                  '.nav-chat-menu': {
                    display: 'flex'
                  }
                }
              })}
              onClick={() => onClickItem(item)}
              onContextMenu={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onTouchStart={() => setIsItemTouching(true)}
              onTouchEnd={() => setIsItemTouching(false)}
              onTouchCancel={() => setIsItemTouching(false)}
            >
              <Tooltip label={item.title} hasArrow>
                <Box flex="1" overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap">
                  {item.title}
                </Box>
              </Tooltip>

              <MyMenu
                trigger="click"
                offset={[20, 0]}
                width={20}
                isOpen={item.chatId === currentItemId}
                outsideClosable={!isItemTouching}
                onClose={() => setCurrentItemId(undefined)}
                Button={
                  <MenuButton
                    className="nav-chat-menu"
                    {...(isPc
                      ? { display: 'none' }
                      : {
                          visibility: 'hidden'
                        })}
                    flexGrow="0"
                    flexShrink="0"
                    flexBasis={respDims('48rpx', 30)}
                    w={respDims('48rpx', 30)}
                    h={respDims('48rpx', 30)}
                    _hover={{
                      bg: 'myWhite.600'
                    }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Center>
                      <SvgIcon
                        name="more"
                        w={respDims('36rpx', '16fpx')}
                        h={respDims('36rpx', '16fpx')}
                      />
                    </Center>
                  </MenuButton>
                }
                menuList={[
                  {
                    label: '重命名',
                    icon: (
                      <SvgIcon
                        name="edit"
                        w={respDims('36rpx', '16fpx')}
                        h={respDims('36rpx', '16fpx')}
                      />
                    ),
                    onClick: () => {
                      setSettingsChatId(item.chatId);
                    }
                  },
                  {
                    label: '删除',
                    icon: (
                      <SvgIcon
                        name="trash"
                        w={respDims('36rpx', '16fpx')}
                        h={respDims('36rpx', '16fpx')}
                      />
                    ),
                    onClick: () => onRemoveItem(item)
                  }
                ]}
              />
            </Flex>
          );
        })}
      </Box>
      {settingsChatId && (
        <ChatSettingsModal chatId={settingsChatId} onClose={() => setSettingsChatId('')} />
      )}
      {isLoadingMore && (
        <Center>
          <Lottie name="Loading" w={respDims('54rpx', 24, 24)} h={respDims('54rpx', 24, 24)} />
        </Center>
      )}
    </Flex>
  ) : viewApp && (getAppDetail.mode === 1 ? !isSimpleDetail : !isAdvancedDetail) ? (
    <Flex
      flexDir="column"
      p={respDims(24)}
      bgColor="#ffffff"
      bgSize="100% auto"
      boxShadow="0px 0px 20px 0px rgba(0,0,0,0.04)"
      {...props}
    >
      {getAppDetail.mode === 1 ? (
        <SimpleDetail onBack={() => setIsSimpleDetail(true)} />
      ) : (
        <AdvancedDetail
          value={getAppDetail.filesList}
          appDetail={getAppDetail}
          onChange={(e) => {
            setGetAppDetail((state) => ({
              ...state,
              filesList: e
            }));
          }}
          onBack={() => setIsAdvancedDetail(true)}
        />
      )}
    </Flex>
  ) : (
    <></>
  );
};

const AppInfo: React.FC = () => {
  const { viewApp, setViewApp } = useAppStore();
  let permission =
    viewApp?.source == DataSource.Personal
      ? SimpleAppPerm.Self
      : viewApp?.permission === PermissionTypeEnum.Private
        ? SimpleAppPerm.UnPublish
        : SimpleAppPerm.Publish;
  return (
    <Box w={viewApp ? respDims(460, 200) : '0'} h="100%">
      {viewApp?.finalAppId && (
        <AppContextProvider
          permission={permission}
          finalAppId={viewApp?.finalAppId!}
          isAdmin={'0'}
          id={viewApp?.id!}
        >
          <AppInfoInner h="100%" />
        </AppContextProvider>
      )}
    </Box>
  );
};

export default AppInfo;
