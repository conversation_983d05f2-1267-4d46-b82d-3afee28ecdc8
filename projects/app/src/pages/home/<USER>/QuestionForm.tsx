import React, { useEffect, useState, forwardRef, useImperativeHandle, useRef } from 'react';
import { Form, Input, Select, Upload, But<PERSON>, Spin } from 'antd';
import {
  Box,
  Image,
  Flex,
  Grid,
  Text,
  Tag,
  Tooltip,
  BoxProps,
  Button as ChakraButton
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import Chooser, { ChooserFileType, ChooserModeEnum } from '@/pages/cloud/list/components/Chooser';
import { UploadChangeParam, UploadFile } from 'antd/lib/upload/interface';
import { uploadFile, uploadImage } from '@/utils/file';
import { FileMetaType } from '@/types/api/file';
import styles from '@/pages/index.module.scss';
import style from '../formSetting.module.scss';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { Toast } from '@/utils/ui/toast';
import DynamicTextArea from './DynamicTextArea';
import { respDims } from '@/utils/chakra';
import { downloadFile } from '@/api/file';
import { DynamicFormDataType } from '@/types/api/chat';
import { useUpdateEffect } from 'ahooks';
import { dynamicRadioFormListFromField, setCloudFileGetContentStatus } from '@/api/app';
import { ComponentType } from '@/constants/api/app';
import { Component } from '@/types/api/app';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { isImage } from '@/utils/chat';
import { debounce } from 'lodash';
import { treeToList } from '@/utils/tree';
import { formatFileSize } from '@/utils/tools';

const imageRegx = /\.(png|jpe?g|gif|bmp|svg)$/i;

// 获取静态选项列表
const getSingleChoiceOptions = () => {
  return [
    {
      id: '',
      type: '1',
      key: 'grade',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '年级',
      sort: 1
    },
    {
      id: '',
      type: '2',
      key: 'volume',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '册别',
      sort: 2
    },
    {
      id: '',
      key: 'discipline',
      type: '3',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '学科',
      sort: 3
    },
    {
      id: '',
      key: 'edition',
      type: '4',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '版本',
      sort: 4
    },
    {
      id: '',
      key: 'unit',
      type: '5',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '单元',
      sort: 5
    },
    {
      id: '',
      type: '6',
      key: '',
      createTime: '',
      updateTime: '',
      isDeleted: 0,
      componentId: '',
      content: '课题',
      sort: 6
    }
  ];
};

const allowedImageTypes = ['jpg', 'jpeg', 'png'];
const allowedDocTypes = ['doc', 'docx', 'pdf', 'txt', 'xls', 'xlsx', 'ppt', 'pptx'];
const isCallOcrTypes = ['doc', 'docx'];
const isUploadAvTypes = ['wav', 'mp3', 'm4a', 'wma', 'aac', 'ogg', 'amr', 'flac', 'mp4'];

// 渲染支持的文件类型
const renderSupportedFileTypes = (item: Component) => {
  if (item.isCallOcr === 1) {
    return `仅支持上传（${isCallOcrTypes.join(', ')}）文件`;
  } else {
    const types = [];
    if (item.isUploadPic === 1) {
      types.push(`图片（${allowedImageTypes.join(', ')}）`);
    }
    if (item.isUploadText === 1) {
      types.push(`文档（${allowedDocTypes.join(', ')}）`);
    }
    if (item.isUploadAv === 1) {
      types.push(`音视频（${isUploadAvTypes.join(', ')}）`);
    }
    return `仅支持上传${types.join('或')}`;
  }
};
const renderSupportedFileTypeList = (item: Component) => {
  const types = [];
  if (item.isUploadPic === 1) {
    types.push(...allowedImageTypes);
  }
  if (item.isUploadText === 1) {
    types.push(...allowedDocTypes);
  }
  if (item.isUploadAv === 1) {
    types.push(...isUploadAvTypes);
  }
  if (item.isCallOcr === 1) {
    types.push(...isCallOcrTypes);
  }
  return types;
};

const renderOption = (
  optionContent: string,
  isSelected: boolean,
  optionsTotal: number,
  optionIndex: number,
  onClick: () => void
) => (
  <Box
    color={isSelected ? '#7D4DFF' : '#303133'}
    bg={isSelected ? '#F4EFFF' : '#F7F8FA'}
    border={isSelected ? '1px solid #B194FF' : ''}
    h={respDims(38)}
    p="0 12px"
    borderRadius="8px"
    {...(optionsTotal > 5 ? { width: 'calc(20% - 15px)' } : { flex: '1 1 calc(20% - 15px)' })}
    mr={
      (optionIndex % 4 === 0 && optionIndex !== 0) || optionsTotal === optionIndex + 1
        ? '0'
        : respDims(15)
    }
    overflow="hidden"
    textOverflow="ellipsis"
    whiteSpace="nowrap"
    mb={respDims(16)}
    display="flex"
    maxWidth="100%"
    justifyContent="center"
    alignItems="center"
    cursor="pointer"
    onClick={onClick}
  >
    <Text overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth="100%">
      {optionContent}
    </Text>
  </Box>
);

const { Option } = Select;
const { Dragger } = Upload;

interface FormDetail {
  components: Component[];
}

type LayoutMode = 'wide' | 'narrow'; //弹窗 wide 侧边栏 narrow;

type QuestionFormProps = {
  clientAppFormDetail: FormDetail;
  appId: string;
  value: DynamicFormDataType | undefined;
  onChange: (newData: DynamicFormDataType | undefined) => void;
  layout?: LayoutMode;
  boxProps?: BoxProps;
};

interface OriginFileObj {
  uid: string;
}

interface FileListItem {
  uid: string;
  lastModified: number;
  lastModifiedDate: string;
  name: string;
  size: number;
  type: string;
  percent: number;
  originFileObj: OriginFileObj;
  status: string;
}

interface FileData {
  fileId: string;
  fileName: string;
  fileKey: string;
  fileUrl: string;
  rowKey: string;
  fileSize: number;
  fileList: FileListItem[];
}

const { Option: SelectOption } = Select;

const QuestionForm: React.ForwardRefRenderFunction<any, QuestionFormProps> = (
  { clientAppFormDetail, appId, value, onChange, layout = 'wide', ...props },
  ref
) => {
  const [form] = Form.useForm();

  const [data, setData] = useState<FormDetail | null>(null);
  const [loading, setLoading] = useState(false);
  const [dynamicOptions, setDynamicOptions] = useState<{ [key: string]: any[] }>({}); // 用于存储动态获取的选项
  const [selectedValues, setSelectedValues] = useState<{ [key: string]: any }>({});
  const { openOverlay, OverlayContainer } = useOverlayManager();

  const fileRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const [lastFileKey, setLastFileKey] = useState<string | null>(null);

  useEffect(() => {
    if (lastFileKey && fileRefs.current[lastFileKey + layout]) {
      setTimeout(() => {
        fileRefs.current[lastFileKey + layout]?.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        });
      }, 0);
    }
  }, [lastFileKey, layout]);

  useEffect(() => {
    setData(clientAppFormDetail);
    if (value && !Object.keys(value).length) {
      form.resetFields();
    } else {
      form.setFieldsValue(value);
    }

    // 新增：在页面加载时检查并调用 fetchDynamicOptionsForItem
    clientAppFormDetail.components?.forEach((item) => {
      if (item.isTiled && layout === 'wide') {
        fetchDynamicOptionsForItem(item, selectedValues);
      }
    });
  }, [form, appId, clientAppFormDetail, value, location.pathname]);

  // 初始化options
  useEffect(() => {
    initSelectedValues();
  }, [value]);

  const initSelectedValues = () => {
    treeToList(clientAppFormDetail.components || [])?.forEach((item) => {
      const matchedOption = getSingleChoiceOptions().find(
        (option) => option.content === item.options[0]?.content
      );
      if (
        matchedOption &&
        (Array.isArray(value?.[item.id]) ? value?.[item.id].length : value?.[item.id])
      ) {
        setSelectedValues((prevValues) => ({
          ...prevValues,
          [matchedOption?.key as string]: value?.[item.id]
        }));
      }
    });
  };

  const handleUpload = debounce(
    async (info: UploadChangeParam<UploadFile<FileData>>, itemId: string) => {
      const item = data?.components.find((item) => item.id === itemId);
      if (!item) return;

      const existingFileList = form.getFieldValue(itemId) || [];

      const newFiles = info.fileList.filter((file: UploadFile<FileData>) => {
        return !existingFileList?.some(
          (existingFile: FileMetaType) => existingFile.fileKey === file.uid
        );
      });

      if (existingFileList.length + newFiles.length > item.maxFiles) {
        Toast.error({
          title: '文件数量超出限制',
          description: `最多不能上传超过 ${item.maxFiles} 个，当前可以添加最多 ${item.maxFiles - existingFileList.length} 个文件`,
          duration: 3000,
          isClosable: true
        });
        return;
      }

      setLoading(true);

      // 上传文件
      const uploadedFiles = await Promise.all(
        newFiles.map((file: any) => {
          if (file.originFileObj) {
            const isImage = /\.(png|jpe?g|gif|bmp|svg)$/i.test(file.name);
            return isImage
              ? uploadImage(file.originFileObj, {}, true)
              : uploadFile(file.originFileObj, {
                  onProgress(number) {}
                });
          }
          return Promise.resolve(file);
        })
      );

      // 更新文件列表状态
      const updatedFileList = uploadedFiles.map((file: FileMetaType) => {
        return {
          fileKey: file.fileKey,
          fileName: file.fileName,
          fileSize: file.fileSize,
          fileType: file.fileType,
          fileUrl: file.fileUrl,
          source: 'upload',
          rowKey: file.rowKey
        };
      });

      // 更新表单字段的值
      form.setFieldsValue({
        [itemId]: [...existingFileList, ...updatedFileList]
      });
      form.validateFields([itemId]);
      updateResult();

      // 设置最后一个文件的 key
      const lastFileKey = updatedFileList[updatedFileList.length - 1]?.fileKey;
      setLastFileKey(lastFileKey);

      setLoading(false);
    },
    500
  );

  const beforeUpload = (file: File, item: Component) => {
    const hint = item.placeholder;
    const maxSizeMatch = hint.match(/文件大小不超过(\d+\.\d+|\d+)M/);
    let maxSize = maxSizeMatch ? parseFloat(maxSizeMatch[1]) * 1024 * 1024 : Infinity;
    const defaultMaxSize = item.isCallOcr ? 7 * 1024 * 1024 : 100 * 1024 * 1024;
    maxSize = maxSize > defaultMaxSize ? defaultMaxSize : maxSize;

    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const isAllowedType =
      item.isCallOcr === 1
        ? isCallOcrTypes.includes(fileExtension || '')
        : (item.isUploadPic === 1 && allowedImageTypes.includes(fileExtension || '')) ||
          (item.isUploadText === 1 && allowedDocTypes.includes(fileExtension || '')) ||
          (item.isUploadAv === 1 && isUploadAvTypes.includes(fileExtension || ''));

    const isAllowedSize = file.size <= maxSize;

    if (!isAllowedType) {
      let allowedTypesText = [];
      if (item.isCallOcr) {
        allowedTypesText.push(`仅支持上传 ${isCallOcrTypes.join(', ')} 格式`);
      } else {
        if (item.isUploadPic === 1) {
          allowedTypesText.push(`图片格式(${allowedImageTypes.join(', ')})`);
        }
        if (item.isUploadText === 1) {
          allowedTypesText.push(`文档格式(${allowedDocTypes.join(', ')})`);
        }
        if (item.isUploadAv === 1) {
          allowedTypesText.push(`音视频格式(${isUploadAvTypes.join(', ')})`);
        }
      }
      Toast.error(`仅支持上传 ${allowedTypesText.join('或')}`);
      return Upload.LIST_IGNORE;
    }

    // 新增：检查文件类型是否在 isUploadAvTypes 中
    if (isUploadAvTypes.includes(fileExtension || '')) {
      Toast.error('请从数据空间上传音视频文件');
      return Upload.LIST_IGNORE;
    }

    if (!isAllowedSize) {
      Toast.error(`文件大小不能超过 ${maxSize / 1024 / 1024}M`);
      return Upload.LIST_IGNORE;
    }

    const existingFileList = form.getFieldValue(item.id) || [];
    if (existingFileList.length >= item.maxFiles) {
      Toast.error(
        `最多不能上传超过 ${item.maxFiles} 个，当前可以添加最多 ${item.maxFiles - existingFileList.length} 个文件`
      );
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  const handleSelectFromCloud = (item: Component, tips: string) => {
    openChooser(item, tips);
  };

  // 新增异步函数来更新文件解析状态
  const updateFileParseStatus = (files: FileMetaType[], itemId: string) => {
    setTimeout(async () => {
      console.log(files, 'files');

      const updatePromises = files.map(async (file) => {
        const response = await setCloudFileGetContentStatus(file.fileKey);
        file.fileParseStatus = response.fileParseStatus || undefined;
      });

      await Promise.all(updatePromises);

      // 确保状态更新
      form.setFieldsValue({
        // 假设 itemId 是你需要更新的字段
        [itemId]: [...files]
      });
      form.validateFields([itemId]);
      updateResult();
    }, 500);
  };

  // 在上传完成后调用 updateFileParseStatus
  const handleChooserSuccess = async (files: ChooserFileType[], itemId: string) => {
    if (!itemId) {
      Toast.error('未找到对应的表单项');
      return;
    }

    const item = data?.components.find((component) => component.id === itemId);
    if (!item) {
      Toast.error('未找到对应的表单项');
      return;
    }

    const maxSize_ = item.isCallOcr ? 7 * 1024 * 1024 : 100 * 1024 * 1024;
    let flag = files?.some((file) => (file.fileSize ?? 0) > maxSize_);
    if (flag) {
      Toast.error(`文件大小不能超过 ${maxSize_ / 1024 / 1024}M`);
      return;
    }

    const hint = item.placeholder;
    const maxSizeMatch = hint.match(/文件大小不超过(\d+\.\d+|\d+)M/);
    let maxSize = maxSizeMatch ? parseFloat(maxSizeMatch[1]) * 1024 * 1024 : Infinity;
    const defaultMaxSize = item.isCallOcr ? 7 * 1024 * 1024 : 100 * 1024 * 1024;
    maxSize = maxSize > defaultMaxSize ? defaultMaxSize : maxSize;

    const validFiles = files.filter((file: ChooserFileType) => {
      const fileExtension = file.fileName.split('.').pop()?.toLowerCase();

      // 如果 isCallOcr 存在，只验证 isCallOcrTypes
      const isAllowedType =
        item.isCallOcr === 1
          ? isCallOcrTypes.includes(fileExtension || '')
          : (item.isUploadPic === 1 && allowedImageTypes.includes(fileExtension || '')) ||
            (item.isUploadText === 1 && allowedDocTypes.includes(fileExtension || '')) ||
            (item.isUploadAv === 1 && isUploadAvTypes.includes(fileExtension || ''));

      const isAllowedSize = maxSize ? (file.fileSize ?? 0) <= maxSize : true;

      if (!isAllowedType) {
        let allowedTypesText = [];
        if (item.isCallOcr) {
          allowedTypesText.push(`仅支持上传 ${isCallOcrTypes.join(', ')} 格式`);
        } else {
          if (item.isUploadPic === 1) {
            allowedTypesText.push(`图片格式(${allowedImageTypes.join(', ')})`);
          }
          if (item.isUploadText === 1) {
            allowedTypesText.push(`文档格式(${allowedDocTypes.join(', ')})`);
          }
          if (item.isUploadAv === 1) {
            allowedTypesText.push(`音视频格式(${isUploadAvTypes.join(', ')})`);
          }
        }
        Toast.error(`仅支持上传 ${allowedTypesText.join('或')}`);
        return false;
      }

      if (!isAllowedSize) {
        Toast.error(`文件大小不能超过 ${maxSize / 1024 / 1024}M`);
        return false;
      }

      return true;
    });

    // 本地上传
    const existingFileList =
      form.getFieldValue(itemId)?.filter((item: FileMetaType) => item.source != 'cloud') || [];

    // 数据空间
    const updatedFiles = validFiles.map((file) => {
      return {
        ...file,
        fileParseStatus: file.fileParseStatus || null,
        fileSize: file.fileSize ?? 0,
        source: 'cloud', // 记为数据空间文件
        rowKey: file.rowKey
      };
    });

    // 更新表单字段的值
    form.setFieldsValue({
      [itemId]: [...existingFileList, ...updatedFiles]
    });

    form.validateFields([itemId]);
    updateResult();

    // 设置最后一个文件的 key
    const lastFileKey = updatedFiles[updatedFiles.length - 1]?.fileKey;
    setLastFileKey(lastFileKey);

    // 仅当 fileParseStatus 为 null 时调用异步函数更新文件解析状态
    if (updatedFiles?.some((file) => file.fileParseStatus === null)) {
      await updateFileParseStatus(updatedFiles as any, itemId);
    }
  };

  const handleRemoveFile = (file: FileMetaType, itemId: string) => {
    // 更新表单字段的值
    const newFileList = (form.getFieldValue(itemId) || []).filter(
      (item: FileMetaType) => item.fileKey !== file.fileKey
    );
    form.setFieldsValue({
      [itemId]: newFileList
    });

    // 手动触发表单验证
    form.validateFields([itemId]);
  };

  const getInputContent = (placeholder: string) => {
    const placeholderStr = placeholder.toString();

    // 计算实际单位数
    let actualUnits = 0;
    for (let char of placeholderStr) {
      // 判断是否为汉字
      if (/[\u4e00-\u9fa5]/.test(char)) {
        actualUnits += 1; // 汉字算一个单位
      } else {
        actualUnits += 0.5; // 非汉字字符算半个单位
      }
    }

    // 向上取整，确保单位数为整数
    actualUnits = Math.ceil(actualUnits);

    if (actualUnits <= 22) {
      return 'input1';
    } else if (actualUnits <= 44) {
      return 'input2';
    } else {
      return 'input3';
    }
  };

  const updateResult = () => {
    const formData = form.getFieldsValue();
    const result = clientAppFormDetail.components.reduce(
      (acc: { [key: string]: any }, item: Component, index: number) => {
        if (item.type === ComponentType.Upload) {
          acc[item.id] = (formData[item.id] || []).map((file: any) => {
            return {
              ...file
            };
          });
        } else {
          acc[item.id] = formData[item.id];
        }

        if (item.type === ComponentType.OptionGroup) {
          item.children?.forEach((child: Component) => {
            acc[child.id] = formData[child.id];
          });
        }
        return acc;
      },
      {} as { [key: string]: any }
    );

    onChange(result);
  };

  // 定义校验方法
  const validateForm = async () => {
    try {
      const values = await form.validateFields();
      return { valid: true, values };
    } catch (error) {
      return { valid: false, error };
    }
  };

  // 使用 useImperativeHandle 将 validateForm 方法暴露给父组件
  useImperativeHandle(ref, () => ({
    validateForm
  }));

  const findType4Components = (components: any) => {
    return components.flatMap((item: Component) => {
      if (item.type === 4) {
        return [item]; // 果当前项是 type === 4，直接返回
        return [item]; // 果当前项是 type === 4，直接返回
      }
      if (item.type === 5) {
        // 对 type === 5 的组件进行特殊处理
        if (Array.isArray(item.children)) {
          return findType4Components(item.children); // 递归查找子组件
        }
      }
      return []; // 如果不是 type === 4 且没有子组件，返回空数组
    });
  };

  const fetchDynamicOptionsForItem = async (
    item: Component,
    selectedValues: { [key: string]: any }
  ) => {
    if (!item.options?.length) {
      return [];
    }

    const matchedOption = getSingleChoiceOptions().find(
      (option) => option.content === item.options[0].content
    );

    if (!matchedOption) {
      return [];
    }

    // 根据选项内容决定不传入的字段
    const filteredValues = { ...selectedValues };
    if (matchedOption.key === 'grade') {
      delete filteredValues.grade;
    } else if (matchedOption.key === 'discipline') {
      delete filteredValues.discipline;
    } else if (matchedOption.key === 'unit') {
      delete filteredValues.unit;
    } else if (matchedOption.key === 'edition') {
      delete filteredValues.edition;
    } else if (matchedOption.key === 'volume') {
      delete filteredValues.volume;
    }
    // 可以继续添加其他条件

    try {
      const options = await dynamicRadioFormListFromField({
        fieldId: matchedOption.id || matchedOption.type,
        ...filteredValues // 使用过滤后的值
      });

      setDynamicOptions((prevOptions: any) => ({
        ...prevOptions,
        [item.id as string]: options
      }));

      return options;
    } catch (error) {
      console.error('Error fetching dynamic options:', error);
      return [];
    }
  };

  const openChooser = (item: Component, tips: string) => {
    const cloudFiles =
      form.getFieldValue(item.id)?.filter((item: FileMetaType) => item.source == 'cloud') || [];

    const maxCount =
      item.maxFiles - ((form.getFieldValue(item.id)?.length || 0) - (cloudFiles.length || 0));

    if (maxCount <= 0) {
      Toast.error('最多只能上传' + item.maxFiles + '个文件');
      return;
    }

    openOverlay({
      Overlay: Chooser,
      props: {
        mode: ChooserModeEnum.Self,
        title: '选择文件',
        files: cloudFiles,
        showUploadBtn: true,
        selectTips: tips,
        accept: renderSupportedFileTypeList(item),
        maxCount: maxCount,
        onSuccess: (files: ChooserFileType[]) => {
          handleChooserSuccess(files, item.id);
        }
      }
    });
  };

  const getAcceptString = (item: any) => {
    const types = [];
    if (item.isCallOcr === 1) {
      types.push(...isCallOcrTypes.map((type) => `.${type}`));
    } else {
      if (item.isUploadPic === 1) {
        types.push(...allowedImageTypes.map((type) => `.${type}`));
      }
      if (item.isUploadText === 1) {
        types.push(...allowedDocTypes.map((type) => `.${type}`));
      }
      if (item.isUploadAv === 1) {
        types.push(...isUploadAvTypes.map((type) => `.${type}`));
      }
    }
    return types.join(',');
  };

  const renderFileStatus = (file: FileMetaType) => {
    let statusColor = '#1D2129';
    let statusText = '';

    switch (file.fileParseStatus) {
      case null:
        statusColor = '#7D4DFF';
        statusText = '解析中...';
        break;
      case -1:
        statusColor = '#F53F3F';
        statusText = '解析失败';
        break;
      default:
        statusColor = '#A8ABB2';
        statusText = `${file.fileKey.split('.').pop()}，${formatFileSize(file.fileSize)}`;
    }

    return (
      <Box fontSize="15px" ml={respDims(6)} className={'textEllipsis'} flex={'1 0 0'}>
        {file.fileName}
        <Flex color={statusColor} fontSize="12px">
          {statusText}
        </Flex>
      </Box>
    );
  };

  return (
    <Box width="100%" position="relative" overflow="auto" {...(props.boxProps || {})}>
      <Box pt="14px">
        <Spin spinning={loading}>
          <Form
            form={form}
            layout="vertical"
            className={`${styles['my-form']} ${styles['my-form-item-color']}`}
          >
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                data?.components?.some((item) => prevValues[item.id] !== currentValues[item.id]) ||
                false
              }
            >
              {({ getFieldValue }) => {
                return (
                  <>
                    {data?.components?.map((item) => (
                      <Box key={item.id} style={{ marginBottom: '16px', padding: '0 2px' }}>
                        <Form.Item
                          name={item.id}
                          label={
                            <Box pb="10px" color="#303133" fontSize="14px" display="flex">
                              <Box
                                style={{
                                  whiteSpace: 'pre-wrap',
                                  marginBottom: 0,
                                  color: '#1D2129',
                                  fontSize:
                                    item.type === ComponentType.OptionGroup ? '18px' : '16px',
                                  fontWeight:
                                    item.type === ComponentType.OptionGroup ? 'bold' : 'none'
                                }}
                              >
                                {/* 在不同情况下展示标题 */}
                                {((item.type === ComponentType.SingleChoice &&
                                  item.isSideBar === 1 &&
                                  layout === 'narrow') ||
                                  (item.type === ComponentType.SingleChoice &&
                                    item.isWideWindow === 1 &&
                                    layout === 'wide') ||
                                  (item.type === ComponentType.DynamicSingleChoice &&
                                    item.isSideBar === 1 &&
                                    layout === 'narrow') ||
                                  (item.type === ComponentType.DynamicSingleChoice &&
                                    item.isWideWindow === 1 &&
                                    layout === 'wide') ||
                                  item.type === ComponentType.TextBox ||
                                  item.type === ComponentType.Upload ||
                                  item.type === ComponentType.OptionGroup) &&
                                  item.title}

                                {item.type === ComponentType.Upload && (
                                  <span style={{ color: '#999', marginLeft: '8px' }}>
                                    ({(getFieldValue(item.id) || []).length}/{item.maxFiles})
                                  </span>
                                )}
                              </Box>
                              {/* 判断placeholder的展示 */}
                              {item.title && item.isWideWindow === 1 && (
                                <Box color="#4E5969" fontSize="14px">
                                  {layout === 'wide' &&
                                  item.placeholder &&
                                  item.isTiled === 1 &&
                                  (item.type === ComponentType.SingleChoice ||
                                    item.type === ComponentType.DynamicSingleChoice)
                                    ? '（' + item.placeholder + '）'
                                    : null}
                                </Box>
                              )}
                            </Box>
                          }
                          rules={[
                            {
                              required: item.isRequired === 1,
                              message: `请填写${item.title}`
                            }
                          ]}
                          valuePropName={item.type === ComponentType.Upload ? 'fileList' : 'value'}
                        >
                          {item.type === ComponentType.SingleChoice &&
                            (item.isTiled === 1 && layout === 'wide' ? (
                              <Box
                                className={style.tiled}
                                w="100%"
                                display="flex"
                                justifyContent="space-between"
                                flexWrap="wrap"
                              >
                                {item.options.map((option, optionIndex) => (
                                  <Tooltip key={optionIndex} label={option.content} placement="top">
                                    {renderOption(
                                      option.content,
                                      getFieldValue(item.id)?.includes(option.content),
                                      item.options.length,
                                      optionIndex,
                                      () => {
                                        const currentValues = getFieldValue(item.id) || [];
                                        let newValues;
                                        if (item.isMultiselect) {
                                          newValues = currentValues.includes(option.content)
                                            ? currentValues.filter(
                                                (tag: string) => tag !== option.content
                                              )
                                            : [...currentValues, option.content];
                                        } else {
                                          newValues = currentValues.includes(option.content)
                                            ? []
                                            : [option.content];
                                        }

                                        form.setFieldsValue({ [item.id]: newValues });
                                        updateResult();
                                      }
                                    )}
                                  </Tooltip>
                                ))}
                              </Box>
                            ) : (
                              <Select
                                dropdownStyle={{
                                  zIndex: 9999
                                }}
                                placeholder={item.placeholder}
                                className={style[getInputContent(item.placeholder)]}
                                mode={item.isMultiselect ? 'multiple' : undefined}
                                allowClear
                                onChange={(value) => {
                                  form.setFieldsValue({ [item.id]: value });
                                  updateResult();
                                }}
                              >
                                {item.options.map((option) => (
                                  <Option key={option.id} value={option.content}>
                                    {option.content}
                                  </Option>
                                ))}
                              </Select>
                            ))}
                          {item.type === ComponentType.TextBox && (
                            <DynamicTextArea
                              placeholder={item.placeholder}
                              value={getFieldValue(item.id)}
                              onChange={(e) => {
                                form.setFieldsValue({ [item.id]: e.target.value });
                                updateResult();
                              }}
                            />
                          )}
                          {item.type === ComponentType.Upload && (
                            <>
                              {/* <Dragger
                                name="file"
                                multiple={item.maxFiles > 1}
                                fileList={[]}
                                onChange={(info) => {
                                  handleUpload(info, item.id);
                                  updateResult();
                                }}
                                beforeUpload={(file) => beforeUpload(file, item)}
                                showUploadList={false}
                                style={{
                                  border: '1px dashed #E5E6EB',
                                  borderRadius: '8px',
                                  backgroundColor: '#F8FAFC',
                                  padding: '16px',
                                  textAlign: 'center'
                                }}
                              >
                                <SvgIcon name="uploadFile" w="80px" h="80px" />
                                <Box
                                  style={{ fontSize: '13px', color: '#1D2129' }}
                                  lineHeight={respDims(18, 14)}
                                >
                                  <Box mb={respDims(8, 4)} fontSize="#1D2129">
                                    点击或拖拽文件到此处上传，或
                                  </Box>
                                  <ChakraButton
                                    variant="primary"
                                    onClick={(event) => {
                                      event.stopPropagation();
                                      handleSelectFromCloud(item, renderSupportedFileTypes(item));
                                    }}
                                  >
                                    <SvgIcon
                                      name="download"
                                      w="16px"
                                      h="16px"
                                      mr={respDims(8, 4)}
                                    />
                                    数据空间选择
                                  </ChakraButton>
                                </Box>
                                <p
                                  style={{ fontSize: '12px', color: '#86909c', paddingTop: '8px ' }}
                                >
                                  {item.placeholder}
                                </p>
                                <p
                                  style={{
                                    fontSize: '12px',
                                    color: '#86909c',
                                    paddingTop: '8px ',
                                    lineHeight: '14px'
                                  }}
                                >
                                  {renderSupportedFileTypes(item)}
                                </p>
                              </Dragger> */}
                              <Dragger
                                name="file"
                                multiple={item.maxFiles > 1}
                                fileList={[]}
                                onChange={(info) => {
                                  handleUpload(info, item.id);
                                  updateResult();
                                }}
                                beforeUpload={(file) => beforeUpload(file, item)}
                                showUploadList={false}
                                accept={getAcceptString(item)} // 添加 accept 属性
                                style={{
                                  border: '1px dashed #E5E6EB',
                                  borderRadius: '8px',
                                  backgroundColor: '#F8FAFC',
                                  padding: '8px',
                                  textAlign: 'center'
                                }}
                              >
                                <SvgIcon name="uploadFile" w="80px" h="70px" />
                                <Box
                                  style={{ fontSize: '13px', color: '#1D2129' }}
                                  lineHeight={respDims(18, 14)}
                                >
                                  <Box mb={respDims(8, 4)} fontSize="#1D2129">
                                    点击或拖拽文件到此处上传，或
                                  </Box>
                                  <ChakraButton
                                    variant="primary"
                                    onClick={(event) => {
                                      event.stopPropagation();
                                      handleSelectFromCloud(item, renderSupportedFileTypes(item));
                                    }}
                                  >
                                    <SvgIcon
                                      name="download"
                                      w="16px"
                                      h="16px"
                                      mr={respDims(8, 4)}
                                    />
                                    数据空间选择
                                  </ChakraButton>
                                </Box>
                                <p
                                  style={{ fontSize: '12px', color: '#86909c', paddingTop: '8px ' }}
                                >
                                  {item.placeholder}
                                </p>
                                <p
                                  style={{
                                    fontSize: '12px',
                                    color: '#86909c',
                                    paddingTop: '8px ',
                                    lineHeight: '14px'
                                  }}
                                >
                                  {renderSupportedFileTypes(item)}
                                </p>
                              </Dragger>

                              <Grid
                                mt={4}
                                gridTemplateColumns={'repeat(2, minmax(0, 1fr))'}
                                gridGap={4}
                              >
                                {(form.getFieldValue(item.id) || []).map((file: FileMetaType) => (
                                  <Flex
                                    key={file.fileKey}
                                    id={file.fileKey + layout}
                                    position="relative"
                                    border="1px solid #E5E6EB"
                                    borderRadius="8px"
                                    cursor="pointer"
                                    p="12px"
                                    justifyContent="space-between"
                                    alignItems="center"
                                    _hover={{
                                      backgroundColor: '#F8FAFC',
                                      '& .close_icon': {
                                        display: 'block'
                                      }
                                    }}
                                    ref={(el) => (fileRefs.current[file.fileKey + layout] = el)}
                                  >
                                    {file.fileUrl && isImage(file.fileUrl) ? (
                                      <Image
                                        src={file.fileUrl}
                                        width="40px"
                                        height="40px"
                                        alt="file"
                                        objectFit="cover"
                                        borderRadius="4px"
                                      />
                                    ) : (
                                      <FileIcon
                                        fileType={file.fileType as any}
                                        fileName={file.fileName}
                                        w="40px"
                                        h="40px"
                                      />
                                    )}
                                    {renderFileStatus(file)}
                                    <SvgIcon
                                      name="circleClose"
                                      position="absolute"
                                      className="close_icon"
                                      right="-10px"
                                      top="-10px"
                                      display="none"
                                      cursor="pointer"
                                      w="30px"
                                      h="30px"
                                      onClick={() => {
                                        handleRemoveFile(file, item.id);
                                        updateResult();
                                      }}
                                    />
                                  </Flex>
                                ))}
                              </Grid>
                              {(form.getFieldValue(item.id) || [])?.some(
                                (file: FileMetaType) => file.fileParseStatus === -1
                              ) && (
                                <Box
                                  w="100%"
                                  mt={respDims(8)}
                                  p={respDims(9, 16)}
                                  borderRadius="8px"
                                  bg="#FFECE8"
                                  color="#1D2129"
                                  fontSize="14px"
                                  display="flex"
                                  justifyContent="space-between"
                                  alignItems="center"
                                >
                                  <SvgIcon name="tipsError" w="16px" h="16px" mr="12px" />
                                  解析失败可能是未成功解析或解析内容为空。您可以重新尝试上传文件，或替换其他文件。
                                </Box>
                              )}
                            </>
                          )}
                          {item.type === ComponentType.DynamicSingleChoice &&
                            (item.isTiled && layout === 'wide' ? (
                              <Box
                                className={style.tiled}
                                w="100%"
                                display="flex"
                                justifyContent="space-between"
                                flexWrap="wrap"
                                mr="-15px"
                              >
                                {dynamicOptions[item.id as string]?.map((option, optionIndex) => {
                                  return (
                                    <Tooltip key={optionIndex} title={option} placement="top">
                                      {renderOption(
                                        option,
                                        form.getFieldValue(item.id)?.includes(option),
                                        dynamicOptions[item.id as string]?.length,
                                        optionIndex,
                                        () => {
                                          const currentValues = form.getFieldValue(item.id) || [];
                                          let newValues;
                                          if (item.isMultiselect) {
                                            newValues = currentValues.includes(option)
                                              ? currentValues.filter(
                                                  (tag: string) => tag !== option
                                                )
                                              : [...currentValues, option];
                                          } else {
                                            newValues = currentValues.includes(option)
                                              ? []
                                              : [option];
                                          }

                                          setDynamicOptions((prevOptions) => ({
                                            ...prevOptions,
                                            [item.key as string]: Array.isArray(newValues)
                                              ? newValues
                                              : [newValues]
                                          }));
                                          const matchedOption = getSingleChoiceOptions().find(
                                            (option) => option.content === item.options[0]?.content
                                          );
                                          if (matchedOption) {
                                            setSelectedValues((prevValues) => ({
                                              ...prevValues,
                                              [matchedOption.key as string]: newValues
                                            }));
                                          }

                                          form.setFieldsValue({ [item.id]: newValues });
                                          updateResult();
                                        }
                                      )}
                                    </Tooltip>
                                  );
                                })}
                              </Box>
                            ) : (
                              <Select
                                dropdownStyle={{
                                  zIndex: 9999
                                }}
                                allowClear
                                onChange={(value) => {
                                  setDynamicOptions((prevOptions) => ({
                                    ...prevOptions,
                                    [item.key as string]: Array.isArray(value) ? value : [value]
                                  }));
                                  const matchedOption = getSingleChoiceOptions().find(
                                    (option) => option.content === item.options[0]?.content
                                  );
                                  if (matchedOption) {
                                    setSelectedValues((prevValues) => ({
                                      ...prevValues,
                                      [matchedOption.key as string]: value
                                    }));
                                  }
                                  form.setFieldsValue({ [item.id]: value });
                                  updateResult();
                                }}
                                placeholder={item.placeholder}
                                className={`${style[getInputContent(item.placeholder)]}`}
                                onDropdownVisibleChange={(visible) => {
                                  if (visible) {
                                    fetchDynamicOptionsForItem(item, selectedValues);
                                  }
                                }}
                              >
                                {dynamicOptions[item.id as string]?.map((option, optionIndex) => (
                                  <SelectOption key={optionIndex} value={option}>
                                    {option as any}
                                  </SelectOption>
                                ))}
                              </Select>
                            ))}
                          {item.type === ComponentType.OptionGroup && (
                            <Box
                              w="100%"
                              display={layout === 'wide' ? 'flex' : 'block'}
                              flexWrap="wrap"
                            >
                              {Array.isArray(item.children) &&
                                item.children
                                  .filter(
                                    (child) =>
                                      child &&
                                      child.title &&
                                      (child.type === ComponentType.SingleChoice ||
                                        child.type === ComponentType.DynamicSingleChoice)
                                  ) // 过滤掉无效的子项
                                  .map((child, childIndex) => (
                                    <Box
                                      key={childIndex}
                                      mb="-2"
                                      w="100%"
                                      h="100%"
                                      {...(item.children &&
                                      item.children.length > 4 &&
                                      layout === 'wide'
                                        ? { width: 'calc(25% - 15px)' }
                                        : { flex: '1 1 calc(25% - 15px)' })}
                                      mr={
                                        (childIndex % 3 === 0 && childIndex !== 0) ||
                                        item.children?.length === childIndex + 1
                                          ? '0'
                                          : respDims(15)
                                      }
                                    >
                                      <Form.Item
                                        name={child.id}
                                        label={
                                          <Box
                                            color="#1D2129" // 设置字体颜色为红色
                                            fontSize="16px"
                                          >
                                            {child.isSideBar === 1 && layout === 'narrow'
                                              ? child.title
                                              : child.isWideWindow === 1 && layout === 'wide'
                                                ? child.title
                                                : ' '}
                                          </Box>
                                        }
                                        rules={[
                                          {
                                            required: child.isRequired === 1,
                                            message: `请填写${child.title}`
                                          }
                                        ]}
                                      >
                                        {child.type === ComponentType.SingleChoice && (
                                          <Select
                                            dropdownStyle={{
                                              zIndex: 9999
                                            }}
                                            placeholder={child.placeholder}
                                            mode={child.isMultiselect ? 'multiple' : undefined}
                                            allowClear
                                            onChange={(value) => {
                                              form.setFieldsValue({ [child.id]: value });
                                              updateResult();
                                            }}
                                          >
                                            {child.options.map((option) => (
                                              <Option key={option.id} value={option.content}>
                                                {option.content}
                                              </Option>
                                            ))}
                                          </Select>
                                        )}
                                        {child.type === ComponentType.DynamicSingleChoice && (
                                          <Select
                                            dropdownStyle={{
                                              zIndex: 9999
                                            }}
                                            virtual={false}
                                            placeholder={child.placeholder}
                                            allowClear
                                            onChange={(value) => {
                                              setDynamicOptions((prevOptions) => ({
                                                ...prevOptions,
                                                [child.key as string]: Array.isArray(value)
                                                  ? value
                                                  : [value]
                                              }));
                                              const matchedOption = getSingleChoiceOptions().find(
                                                (option) =>
                                                  option.content === child.options[0]?.content
                                              );
                                              if (matchedOption) {
                                                setSelectedValues((prevValues) => ({
                                                  ...prevValues,
                                                  [matchedOption.key as string]: value
                                                }));
                                              }
                                              updateResult();
                                            }}
                                            onDropdownVisibleChange={(visible) => {
                                              if (visible) {
                                                fetchDynamicOptionsForItem(child, selectedValues);
                                              }
                                            }}
                                          >
                                            {dynamicOptions[child.id as string]?.map(
                                              (option, optionIndex) => (
                                                <SelectOption key={optionIndex} value={option}>
                                                  {option as any}
                                                </SelectOption>
                                              )
                                            )}
                                          </Select>
                                        )}
                                      </Form.Item>
                                    </Box>
                                  ))}
                            </Box>
                          )}
                        </Form.Item>
                      </Box>
                    ))}
                  </>
                );
              }}
            </Form.Item>
          </Form>
        </Spin>
      </Box>
      <OverlayContainer></OverlayContainer>
    </Box>
  );
};

export default forwardRef(QuestionForm);
