import React, { useEffect, useCallback, useMemo, useState } from 'react';
import { Box, Flex, Image, Text, useTheme, keyframes } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import MyBox from '@/components/common/MyBox';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { TenantWorkflow, TenantWorkflowProcess } from '@/types/api/workflow';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import WorkflowModal from '@/components/WorkflowModal';
import { useChatStore } from '@/store/useChatStore';
import { DataSource } from '@/constants/common';
import Loading from '@/components/Loading';

const allTabs = [
  { type: DataSource.Offical, text: '官方' },
  { type: DataSource.Tenant, text: '专属' },
  { type: DataSource.Personal, text: '我的' }
];

// 定义换页的动画
const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateX(20%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const slideOut = keyframes`
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20%);
  }
`;

const WorkflowList: React.FC<{}> = ({}) => {
  const chatInfo = useChatStore();
  const { chatData } = chatInfo;
  const theme = useTheme();
  const {
    setSelectWorkflow,
    selectWorkflow,
    setSelectStep,
    selectStep,
    workflows,
    setSteps,
    steps,
    currentTab,
    setCurrentTab,
    loading
  } = useWorkflowStore();
  const { openOverlay } = useOverlayManager();
  const [filteredWorkflows, setFilteredWorkflows] = useState<TenantWorkflow[]>([]);
  const [animation, setAnimation] = useState<string>(slideIn);
  const tabs = useMemo(() => {
    if (chatData.source === DataSource.Offical) {
      return allTabs;
    } else if (chatData.source === DataSource.Tenant) {
      return allTabs.filter((tab) => tab.type !== DataSource.Offical);
    } else {
      return allTabs.filter((tab) => tab.type === DataSource.Personal);
    }
  }, [chatData]);

  useEffect(() => {
    !currentTab && setCurrentTab(tabs[0].type);
  }, [tabs]);

  useEffect(() => {
    if (selectWorkflow) {
      setSteps(selectWorkflow.id);
    }
  }, [selectWorkflow]);

  useEffect(() => {
    const filters = workflows.filter((workflow) => workflow.source === currentTab);
    setFilteredWorkflows(filters);

    const index = filters.findIndex((item) => item.id == selectWorkflow?.id);
    if (index === -1) {
      setSelectWorkflow(filters[0] || null);
    }
  }, [workflows, currentTab]);

  const openSettingWorkflow = useCallback(() => {
    openOverlay({
      Overlay: WorkflowModal,
      props: {
        onRefresh: () => {},
        appId: chatData.appId!,
        isManange: false,
        selectWorkflow: selectWorkflow!,
        onClose: () => {},
        onSuccess: () => {}
      }
    });
  }, [chatData, selectWorkflow]);

  const handleTabChange = (type: DataSource) => {
    if (type === currentTab) {
      return;
    }
    setAnimation(slideOut);
    setTimeout(() => {
      setCurrentTab(type);
      setAnimation(slideIn);
    }, 100);
  };

  return (
    <MyBox display="flex" flexDir="column" width="100%" flex="1" height="0" pb={2}>
      {tabs.length > 1 && (
        <Flex
          direction="row"
          align="center"
          justifyContent="space-between"
          w="100%"
          borderBottom="1px solid #E7E7E7"
        >
          {tabs.map((tab) => (
            <Flex
              key={tab.type}
              pt="10px"
              position="relative"
              alignItems="center"
              justifyContent="center"
              w={`${100 / tabs.length}%`}
              fontSize="14px"
              fontWeight="bold"
              cursor="pointer"
              onClick={() => handleTabChange(tab.type)}
            >
              <Box
                pb={respDims(12)}
                position="relative"
                {...(tab.type === currentTab
                  ? {
                      color: 'primary.500',
                      _after: {
                        position: 'absolute',
                        content: '""',
                        left: '0',
                        right: '0',
                        bottom: '0px',
                        w: '100%',
                        height: '2px',
                        bgColor: 'primary.500'
                      }
                    }
                  : {
                      color: '#4E5969'
                    })}
              >
                {tab.text}
              </Box>
            </Flex>
          ))}
        </Flex>
      )}
      {/* 判断有没有工作流，没有为空 */}
      <Box
        animation={`${animation} 0.3s ease-in-out`}
        h="100%"
        flex="1"
        flexDirection="column"
        display="flex"
      >
        {filteredWorkflows?.length ? (
          <>
            <MyBox display="flex" mt={respDims(14)} width="100%" justifyContent="flex-start">
              <Box maxWidth={respDims(442)} mr={respDims(8)}>
                {filteredWorkflows?.map((workflow, index) => (
                  <Flex
                    display="inline-flex"
                    justifyContent="center"
                    alignItems="center"
                    key={index}
                    onClick={() => {
                      setSelectWorkflow(workflow);
                    }}
                    backgroundColor={
                      selectWorkflow?.id === workflow.id
                        ? 'rgba(242, 246, 255, 1)'
                        : 'rgba(0, 0, 0, 0.03)'
                    }
                    borderRadius="8px"
                    border={
                      selectWorkflow?.id === workflow.id
                        ? '1px solid rgba(173, 200, 255, 1)'
                        : '1px solid transparent'
                    }
                    color={
                      selectWorkflow?.id === workflow.id
                        ? 'rgba(51, 102, 255, 1)'
                        : 'rgba(78, 89, 105, 1)'
                    }
                    cursor="pointer"
                    fontSize={respDims(14)}
                    fontWeight={selectWorkflow?.id === workflow.id ? '500' : 'normal'}
                    mx={respDims(2)}
                    px={respDims(10)}
                    py={respDims(4)}
                    mr={respDims(14)}
                    h={respDims(34)}
                    mb={respDims(8)}
                    whiteSpace="nowrap"
                  >
                    {workflow.name}
                  </Flex>
                ))}
              </Box>

              <Flex
                justifyContent="center"
                alignItems="center"
                onClick={() => openSettingWorkflow()}
                backgroundColor={'rgba(0, 0, 0, 0.03)'}
                borderRadius="50px"
                border={'none'}
                h={respDims(34)}
                color={'rgba(78, 89, 105, 1)'}
                cursor="pointer"
                fontSize={respDims(14)}
                fontWeight={'normal'}
                px={respDims(10)}
                py={respDims(6)}
                _hover={{
                  bg: '#F7F9FF'
                }}
              >
                <SvgIcon name="settings" w={respDims(20)} h={respDims(20)}></SvgIcon>
              </Flex>
            </MyBox>
            {selectWorkflow && (
              <MyBox
                isLoading={loading}
                mt={respDims(6)}
                width="100%"
                flex="1"
                overflow="scroll"
                maxWidth={respDims(442)}
                backgroundColor="rgba(255, 255, 255, 1)"
              >
                {steps.length ? (
                  steps?.map((step, index) => (
                    <Box
                      key={index}
                      p={respDims(20)}
                      backgroundColor="#f7f9fb"
                      borderRadius="8px"
                      css={{
                        borderImage: 'rgba(33, 175, 255, 1)'
                      }}
                      position="relative"
                      overflow="hidden"
                      boxSizing="border-box"
                      border={
                        selectStep?.id == step.id ? '#33A9FF solid 1px' : 'solid 1px transparent'
                      }
                      _hover={{
                        bg: '#F7F9FF'
                      }}
                      mb={respDims(12)}
                      onClick={() => setSelectStep(step)} // 将选中的步骤记录到 store 中
                      cursor="pointer"
                    >
                      {selectStep?.id == step.id && (
                        <Flex
                          w={respDims(24)}
                          h={respDims(24)}
                          position="absolute"
                          right="0px"
                          top="0px"
                          justifyContent="center"
                          alignItems="center"
                          background="linear-gradient( 229deg, primary.500 0%, primary.500 100%)"
                          borderRadius="0px 0px 0px 8px"
                        >
                          <SvgIcon
                            name="check"
                            color="#fff"
                            w={respDims(16)}
                            h={respDims(16)}
                          ></SvgIcon>
                        </Flex>
                      )}
                      <Flex direction="row" justify="flex-start" align="center">
                        <Box
                          backgroundColor="rgba(214, 228, 255, 1)"
                          borderRadius="8px 2px 8px 2px"
                          py={respDims(4)}
                          px={respDims(10)}
                          mr={respDims(10)}
                        >
                          <Text
                            color="rgba(51, 102, 255, 1)"
                            fontSize={respDims(13)}
                            fontWeight="700"
                          >
                            {`第${index + 1}步`}
                          </Text>
                        </Box>
                        <Text color="#303133" fontSize={respDims(15)} fontWeight="500">
                          {step.name}
                        </Text>
                      </Flex>
                      <Text mt={respDims(10)} color="#606266" fontSize={respDims(14)}>
                        {step.intro}
                      </Text>
                    </Box>
                  ))
                ) : (
                  <>
                    {
                      <Flex
                        mt={respDims(20)}
                        width="100%"
                        h="100%"
                        alignItems="center"
                        justifyContent="center"
                        flexDirection="column"
                      >
                        <Image
                          src="/imgs/common/empty.svg"
                          w={respDims(85, 65)}
                          alt=""
                          mb={respDims(5)}
                        />
                        <Box color="#303133" fontSize={respDims(16, 14)} fontWeight="500">
                          {'暂无工作环节'}
                        </Box>
                        {currentTab == DataSource.Personal && (
                          <Flex color="#909399" fontSize={respDims(14, 12)} mt={respDims(10)}>
                            请点击
                            <Text
                              color="primary.500"
                              ml={respDims(8)}
                              cursor="pointer"
                              onClick={() => openSettingWorkflow()}
                            >
                              添加工作环节
                            </Text>
                          </Flex>
                        )}
                      </Flex>
                    }
                  </>
                )}
              </MyBox>
            )}
          </>
        ) : (
          <>
            {
              <Flex
                mt={respDims(20)}
                width="100%"
                h="100%"
                alignItems="center"
                justifyContent="center"
                flexDirection="column"
              >
                <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" mb={respDims(5)} />
                <Box color="#303133" fontSize={respDims(16, 14)} fontWeight="500">
                  {'暂无工作流'}
                </Box>
                {(currentTab == DataSource.Personal || tabs.length == 0) && (
                  <Flex color="#909399" fontSize={respDims(14, 12)} mt={respDims(10)}>
                    请点击
                    <Text
                      color="primary.500"
                      ml={respDims(8)}
                      cursor="pointer"
                      onClick={() => openSettingWorkflow()}
                    >
                      添加工作流
                    </Text>
                  </Flex>
                )}
              </Flex>
            }
          </>
        )}
      </Box>
    </MyBox>
  );
};

export default WorkflowList;
