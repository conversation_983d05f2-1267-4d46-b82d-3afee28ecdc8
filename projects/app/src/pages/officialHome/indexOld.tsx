import { Box, Flex, Image, Text, Button, HStack, VStack, Center } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';
import SvgIcon from '@/components/SvgIcon';

const OfficialHome = () => {
  // 新闻列表数据
  const newsList = [
    {
      id: '01',
      title: '关于举办"华师杯"人工智能创新大赛培训（第二期）的通知'
    },
    {
      id: '02',
      title: '当高校遇上DeepSeek 人工智能如何重塑教育生态？'
    },
    {
      id: '03',
      title: '关于举办华南师范大学"人工智能先导计划"——"海大杯"人工智能创新大赛'
    },
    {
      id: '04',
      title: '关于举办"华师杯"人工智能创新大赛培训（第二期）的通知'
    },
    {
      id: '05',
      title: '关于举办华南师范大学"人工智能先导计划"'
    },
    {
      id: '06',
      title: '关于举办"华师杯"人工智能创新大赛培训（第二期）的通知'
    }
  ];

  // 功能卡片数据
  const featureCards = [
    {
      title: '什么是智能体？',
      desc: '智能体是能像人一样思考的AI，能够帮你解决各种知识问答、写作、数据分析等问题'
    },
    {
      title: '如何创建智能体？',
      desc: '智能体是能像人一样思考的AI，能够帮你解决各种知识问答、写作、数据分析等问题'
    },
    {
      title: '智能体能做什么？',
      desc: '智能体是能像人一样思考的AI，能够帮你解决各种知识问答、写作、数据分析等问题'
    },
    {
      title: '如何使用智能体？',
      desc: '智能体是能像人一样思考的AI，能够帮你解决各种知识问答、写作、数据分析等问题'
    }
  ];

  // 热门智能体数据
  const hotAgents = [
    {
      id: 1,
      name: '课堂教学数据提取分析',
      desc: '根据课堂教学内容与AI测诊数据，提取课堂提问理答、教师引导语、评价语、知识与内容要点',
      icon: '/home-hots-img1.png',
      isHot: true
    },
    {
      id: 2,
      name: '"输出为本"评课助手',
      desc: '聚焦教学成果外化，依托智能分析，精准诊断教学问题，提供优化策略，助力课堂质量跃升',
      icon: '/image.png',
      isHot: true
    },
    {
      id: 3,
      name: 'DeepSeek R1',
      desc: '智能体是能像人一样思考的AI，能够帮你解决各种知识问答、写作、数据分析等问题',
      icon: '/home-hots-img2.png',
      isHot: true
    }
  ];

  return (
    <>
      {/* Banner区域 */}
      <Box w="100%" h={respDims(455, GLOBAL_DIMS_MIN_SCALE)} position="relative">
        <Image
          src="/imgs/officialHome/home/<USER>"
          alt="banner"
          w="100%"
          h="100%"
          objectFit="cover"
        />

        {/* Banner内容 */}
        <Flex
          direction="column"
          position="absolute"
          top={respDims(100, GLOBAL_DIMS_MIN_SCALE)}
          left={'20%'}
          gap={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
        >
          <Flex>
            <Text
              fontSize={respDims(34.5, GLOBAL_DIMS_MIN_SCALE)}
              color="#1F2329"
              fontWeight="bold"
              alignItems="flex-end"
              alignSelf="flex-end"
              height={respDims(37.5, GLOBAL_DIMS_MIN_SCALE)}
              lineHeight={respDims(37.5, GLOBAL_DIMS_MIN_SCALE)}
            >
              智擎赋能，华师领航新程
            </Text>
            <Image
              ml={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
              w={respDims(55, GLOBAL_DIMS_MIN_SCALE)}
              h={respDims(74, GLOBAL_DIMS_MIN_SCALE)}
              transform="scaleX(-1)"
              src="/imgs/officialHome/home/<USER>"
              alt="banner-icon"
            />
          </Flex>

          <Button
            w={respDims(254, GLOBAL_DIMS_MIN_SCALE)}
            h={respDims(51, GLOBAL_DIMS_MIN_SCALE)}
            borderRadius={respDims(13.5, GLOBAL_DIMS_MIN_SCALE)}
            bgGradient="linear(209deg, #32D5FE 8%, #4087FD 47%, #9240FD 100%)"
            color="white"
            fontSize={respDims(22.5, GLOBAL_DIMS_MIN_SCALE)}
            fontWeight="500"
          >
            快来定义你的Agent
          </Button>
        </Flex>
      </Box>

      {/* 主要内容区域 */}
      <Box
        mx={respDims(180, GLOBAL_DIMS_MIN_SCALE)}
        mt={respDims(-113, GLOBAL_DIMS_MIN_SCALE)}
        bg="rgba(255, 255, 255, 0.3)"
        borderRadius={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
        border="2px solid white"
        boxShadow="0px 0px 19.9px #341F6912"
        backdropFilter="blur(2px)"
        p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
      >
        {/* 新闻区域 */}
        <Flex alignItems="center" mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)}>
          <Flex alignItems="center" gap={respDims(6, GLOBAL_DIMS_MIN_SCALE)}>
            <SvgIcon
              name="hot"
              w={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
              h={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            />
            <Text fontSize={respDims(20, GLOBAL_DIMS_MIN_SCALE)} color="#1F2329" fontWeight="bold">
              华师智讯
            </Text>
          </Flex>
          <Flex ml="auto" alignItems="center" cursor="pointer">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              xmlnsXlink="http://www.w3.org/1999/xlink"
            >
              <rect width="14" height="14" fill="url(#pattern0_240_2996)" />
              <defs>
                <pattern
                  id="pattern0_240_2996"
                  patternContentUnits="objectBoundingBox"
                  width="1"
                  height="1"
                >
                  <use xlinkHref="#image0_240_2996" transform="scale(0.0714286)" />
                </pattern>
                <image
                  id="image0_240_2996"
                  width="14"
                  height="14"
                  preserveAspectRatio="none"
                  xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAEsSURBVCiRjZLNTcNAEIW/8Q9nd4A7ICWYCkIkEOJELIEQt7gC0sHmFuEcTE4RzmGhAlogHZgOXMDGwyFEJDhE/o6jeTtv3o5wADNbJjRNhOfFqNaE4VuWDurdHtkTTBcxnjcWEVXVdzyvRjUW6KvqKnu4HreEJi97ImLUuTR7vKlaLp5fL8T3+6O7y9QUNhIAU9hInLMaBIO/lnaZ5KUFEoVzDwDnhqo6PyYyhY0QqYCIMKx/w+iImS0TU9ioa38LAZjMlh//hdKamJc9wrDa7KiaiO/bLhYEngA8U9hIRTKgOhYObPZT1VWWDmrZKY4ETkf3V9lB0XQRSxAU2y/bv5y8HP5cyZyTk0+gxrmeqN4CaBhmW1fSermwEc4NReQMQNfrL5rmpUtwnfgGcKCLjv/biOMAAAAASUVORK5CYII="
                />
              </defs>
            </svg>

            <Text
              ml={respDims(8, GLOBAL_DIMS_MIN_SCALE)}
              fontSize={respDims(13, GLOBAL_DIMS_MIN_SCALE)}
              color="#646464"
            >
              更多
            </Text>
          </Flex>
        </Flex>

        {/* 新闻列表 */}
        <Box
          bg="white"
          position="relative"
          borderRadius={respDims(11, GLOBAL_DIMS_MIN_SCALE)}
          p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
        >
          <Flex wrap="wrap" gap={respDims(20, GLOBAL_DIMS_MIN_SCALE)}>
            {newsList.map((news, index) => (
              <Flex
                key={news.id}
                w={{ base: '100%', md: '49%' }}
                alignItems="center"
                gap={respDims(7, GLOBAL_DIMS_MIN_SCALE)}
                borderBottom={
                  index < (window.innerWidth >= 768 ? 4 : 5) ? '1px solid #E0E0E0' : 'none'
                }
                pb={
                  index < (window.innerWidth >= 768 ? 4 : 5)
                    ? respDims(10, GLOBAL_DIMS_MIN_SCALE)
                    : 0
                }
              >
                <Text
                  color="#D2D2D2"
                  fontSize={respDims(18, GLOBAL_DIMS_MIN_SCALE)}
                  fontFamily="YouSheBiaoTiHei-Regular"
                  fontWeight="bold"
                >
                  {news.id}
                </Text>
                <Text color="#1F2329" fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)} noOfLines={1}>
                  {news.title}
                </Text>
              </Flex>
            ))}
          </Flex>
          <Box
            position="absolute"
            w={respDims(80, GLOBAL_DIMS_MIN_SCALE)}
            h={respDims(80, GLOBAL_DIMS_MIN_SCALE)}
            bottom={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            right={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            display={{ base: 'none', md: 'block' }}
          >
            <SvgIcon
              name="hotBg"
              w={respDims(80, GLOBAL_DIMS_MIN_SCALE)}
              h={respDims(80, GLOBAL_DIMS_MIN_SCALE)}
            />
          </Box>
        </Box>

        {/* 功能卡片区域 */}
        <Flex
          gap={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
          mt={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
          flexWrap={{ base: 'wrap', lg: 'nowrap' }}
          justifyContent="space-between"
        >
          {featureCards.map((card, index) => (
            <Box
              key={index}
              w={{ base: '100%', sm: '45%', lg: '23%' }}
              minW={respDims(220, GLOBAL_DIMS_MIN_SCALE)}
              borderRadius={respDims(15, GLOBAL_DIMS_MIN_SCALE)}
              p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
              bgGradient={
                index === 0
                  ? 'linear(180deg, #EFF5FD 0%, #EEF5FE 48%, #C1DDFF 100%)'
                  : index === 1
                    ? 'linear(180deg, #EAF5FF 0%, #E8F4FF 48%, #C3E3FF 100%)'
                    : index === 2
                      ? 'linear(180deg, #F2FEFF 0%, #DFFDFF 62%, #B0F7FC 100%)'
                      : 'linear(180deg, #F5F3FF 0%, #EFECFF 48%, #DBD5FF 100%)'
              }
            >
              <Text
                fontWeight="bold"
                fontSize={respDims(18, GLOBAL_DIMS_MIN_SCALE)}
                color="#163563"
                mb={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
              >
                {card.title}
              </Text>
              <Text
                fontSize={respDims(13, GLOBAL_DIMS_MIN_SCALE)}
                color="#5F7288"
                lineHeight={respDims(15.8, GLOBAL_DIMS_MIN_SCALE)}
              >
                {card.desc}
              </Text>
              <Button
                mt={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                w="100%"
                h={respDims(32, GLOBAL_DIMS_MIN_SCALE)}
                bg="white"
                color="#000"
                border="1px solid #F1F1F1"
                borderRadius="50px"
                fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                _hover={{
                  bg: '#FFFFFF',
                  shadow: '0px 0px 10px 0px rgba(0, 0, 0, 0.1)'
                }}
              >
                查看
              </Button>
            </Box>
          ))}
        </Flex>

        {/* 热门智能体 */}
        <Box mt={respDims(40, GLOBAL_DIMS_MIN_SCALE)}>
          <Text
            fontWeight="bold"
            fontSize={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            color="#1F2329"
            mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
          >
            热门智能体
          </Text>

          {/* 热门智能体卡片区域 */}
          <Flex
            gap={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            flexWrap={{ base: 'wrap', lg: 'nowrap' }}
            justifyContent="space-between"
            h={'100%'}
          >
            {hotAgents.slice(0, 2).map((agent) => (
              <Box
                key={agent.id}
                w={{ base: '100%', sm: '45%', lg: '31%' }}
                minW={respDims(300, GLOBAL_DIMS_MIN_SCALE)}
                bg="white"
                borderRadius={respDims(11, GLOBAL_DIMS_MIN_SCALE)}
                boxShadow="0px 6px 18px #9BC1E733"
                p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                position="relative"
              >
                {/* 排名标识 */}
                <Box
                  position="absolute"
                  top="0"
                  left="0"
                  w={respDims(33, GLOBAL_DIMS_MIN_SCALE)}
                  h={respDims(35, GLOBAL_DIMS_MIN_SCALE)}
                  bg={agent.id === 1 ? '#F73131' : agent.id === 2 ? '#FF8C1A' : '#FFBF00'}
                  borderRadius="12px 0px 12px 0px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text
                    color="white"
                    fontWeight="bold"
                    fontSize={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
                    fontFamily="YouSheBiaoTiHei-Regular"
                  >
                    {agent.id}
                  </Text>
                </Box>

                {/* 图标 */}
                <Image
                  src={agent.icon}
                  alt={agent.name}
                  w={respDims(90, GLOBAL_DIMS_MIN_SCALE)}
                  h={respDims(90, GLOBAL_DIMS_MIN_SCALE)}
                  mx="auto"
                  mt={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50%"
                  border="1px solid #EAEAEA"
                />

                {/* 标题和描述 */}
                <Flex
                  direction="column"
                  alignItems="center"
                  mt={respDims(15, GLOBAL_DIMS_MIN_SCALE)}
                >
                  <Flex alignItems="center" gap={respDims(5, GLOBAL_DIMS_MIN_SCALE)}>
                    {
                      <Box
                        px={respDims(5, GLOBAL_DIMS_MIN_SCALE)}
                        h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                        bg="linear-gradient(90deg, #FF7842 0%, #FF976B 100%)"
                        borderRadius="50px"
                        color="white"
                        fontSize={respDims(12, GLOBAL_DIMS_MIN_SCALE)}
                        lineHeight={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                      >
                        HOT
                      </Box>
                    }
                    <Text
                      fontSize={respDims(15, GLOBAL_DIMS_MIN_SCALE)}
                      color="#1F2329"
                      fontWeight="500"
                    >
                      {agent.name}
                    </Text>
                  </Flex>

                  <Text
                    fontSize={respDims(12, GLOBAL_DIMS_MIN_SCALE)}
                    color="#5F7288"
                    textAlign="center"
                    mt={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
                    noOfLines={2}
                  >
                    {agent.desc}
                  </Text>

                  <Text
                    color="#4187FD"
                    fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                    mt={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                    cursor="pointer"
                  >
                    立即体验
                  </Text>
                </Flex>
              </Box>
            ))}
            <VStack
              w={{ base: '100%', lg: '31%' }}
              justifyContent={'space-between'}
              minW={respDims(300, GLOBAL_DIMS_MIN_SCALE)}
            >
              <HStack
                key={hotAgents[2]?.id}
                w="100%"
                h={'47%'}
                bg="white"
                borderRadius={respDims(12, GLOBAL_DIMS_MIN_SCALE)}
                boxShadow="0px 6px 18px #9BC1E733"
                p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                position="relative"
                justifyContent="space-between"
                overflow="hidden"
              >
                {/* 排名标识 */}
                <Box
                  position="absolute"
                  top="0"
                  left="0"
                  w={respDims(33, GLOBAL_DIMS_MIN_SCALE)}
                  h={respDims(35, GLOBAL_DIMS_MIN_SCALE)}
                  bg={
                    hotAgents[2]?.id === 1
                      ? '#F73131'
                      : hotAgents[2]?.id === 2
                        ? '#FF8C1A'
                        : '#FFBF00'
                  }
                  borderRadius="12px 0px 12px 0px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text
                    color="white"
                    fontWeight="bold"
                    fontSize={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                    fontFamily="YouSheBiaoTiHei-Regular"
                  >
                    {hotAgents[2]?.id}
                  </Text>
                </Box>

                {/* 图标 */}
                <Image
                  src={hotAgents[2]?.icon}
                  alt={hotAgents[2]?.name}
                  w={respDims(80, GLOBAL_DIMS_MIN_SCALE)}
                  h={respDims(80, GLOBAL_DIMS_MIN_SCALE)}
                  mx="auto"
                  mt={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50%"
                  border="1px solid #EAEAEA"
                />

                {/* 标题和描述 */}
                <Flex
                  flex={1}
                  direction="column"
                  overflow="hidden"
                  alignItems="flex-start"
                  mt={respDims(15, GLOBAL_DIMS_MIN_SCALE)}
                >
                  <Flex alignItems="center" gap={respDims(5, GLOBAL_DIMS_MIN_SCALE)}>
                    {
                      <Box
                        px={respDims(5, GLOBAL_DIMS_MIN_SCALE)}
                        h={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                        bg="linear-gradient(90deg, #FF7842 0%, #FF976B 100%)"
                        borderRadius="50px"
                        color="white"
                        fontSize={respDims(12, GLOBAL_DIMS_MIN_SCALE)}
                        lineHeight={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                      >
                        HOT
                      </Box>
                    }
                    <Text
                      fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                      color="#1F2329"
                      fontWeight="500"
                    >
                      {hotAgents[2]?.name}
                    </Text>
                  </Flex>

                  <Text
                    fontSize={respDims(12, GLOBAL_DIMS_MIN_SCALE)}
                    color="#5F7288"
                    textAlign="left"
                    className="textEllipsis"
                    mt={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
                    w={'100%'}
                  >
                    {hotAgents[2]?.desc}
                  </Text>

                  <Text
                    color="#4187FD"
                    fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                    mt={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
                    cursor="pointer"
                    alignSelf="flex-end"
                  >
                    立即体验
                  </Text>
                </Flex>
              </HStack>
              <Flex
                justifyContent="space-between"
                w="100%"
                h={'47%'}
                bg="white"
                borderRadius={respDims(11, GLOBAL_DIMS_MIN_SCALE)}
                boxShadow="0px 6px 18px #9BC1E733"
                p={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                position="relative"
              >
                <Image
                  src="/imgs/officialHome/home/<USER>"
                  alt="agent1"
                  w={respDims(100, GLOBAL_DIMS_MIN_SCALE)}
                  h={respDims(100, GLOBAL_DIMS_MIN_SCALE)}
                />
                <Center flex={1}>
                  <Text
                    color="#4187FD"
                    fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                    mt={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
                    cursor="pointer"
                    alignSelf="center"
                  >
                    {'去智能体广场 >'}
                  </Text>
                </Center>
              </Flex>
            </VStack>
          </Flex>
        </Box>
      </Box>
    </>
  );
};

export default OfficialHome;
