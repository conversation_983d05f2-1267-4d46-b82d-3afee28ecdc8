import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Flex,
  useColorModeValue,
  Text,
  Heading,
  useDisclosure,
  IconButton,
  Drawer,
  Drawer<PERSON><PERSON>,
  Drawer<PERSON><PERSON>er,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  Drawer<PERSON>loseButton,
  Spinner,
  Center,
  Badge,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink
} from '@chakra-ui/react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import remarkMath from 'remark-math';
import rehypeRaw from 'rehype-raw';
import rehypeKatex from 'rehype-katex';
import rehypeSanitize from 'rehype-sanitize';
import { respDims } from '@/utils/chakra';
import { HamburgerIcon, ChevronRightIcon } from '@chakra-ui/icons';
import 'katex/dist/katex.min.css';
import { getMockDocContent, DocContent } from '@/api/docs';
import { useQuery } from '@tanstack/react-query';
import router from 'next/router';

interface TocItem {
  id: string;
  text: string;
  level: number;
}

// 从Markdown内容中提取标题，生成目录
const extractHeadings = (markdown: string): TocItem[] => {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings: TocItem[] = [];
  let match;

  while ((match = headingRegex.exec(markdown)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();
    // 生成一致的唯一标识符
    const id = `heading-${text
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fa5\- ]/g, '')
      .replace(/\s+/g, '-')}`;

    headings.push({ id, text, level });
  }

  return headings;
};

// 目录组件
const TableOfContents: React.FC<{
  toc: TocItem[];
  activeTocId: string;
  onClick: (id: string) => void;
}> = ({ toc, activeTocId, onClick }) => {
  // 更新颜色变量以符合图片样式
  const textColor = useColorModeValue('gray.700', 'gray.300');
  const activeTextColor = useColorModeValue('blue.600', 'blue.200');
  const hoverBgColor = useColorModeValue('gray.100', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const expandedIconColor = useColorModeValue('gray.400', 'gray.500');
  // 用于激活项背景色，防止在递归渲染中重复调用 Hook
  const activeBgColor = useColorModeValue('gray.100', 'gray.700');

  // 支持多层级折叠的目录树和父映射
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});
  const { tocTree, parentMap } = React.useMemo(() => {
    type Node = TocItem & { children: TocItem[] };
    const nodes: Node[] = toc.map((item) => ({ ...item, children: [] }));
    const map: Record<string, string> = {};
    const root: Node[] = [];
    const stack: Node[] = [];
    nodes.forEach((node) => {
      while (stack.length && stack[stack.length - 1].level >= node.level) {
        stack.pop();
      }
      if (stack.length === 0) {
        root.push(node);
      } else {
        map[node.id] = stack[stack.length - 1].id;
        stack[stack.length - 1].children.push(node);
      }
      stack.push(node);
    });
    return { tocTree: root, parentMap: map };
  }, [toc]);

  // 初始化所有节点为折叠状态
  useEffect(() => {
    const initial: Record<string, boolean> = {};
    toc.forEach((item) => {
      // 第一级菜单默认展开，其他级别折叠
      initial[item.id] = item.level === 1;
    });
    setExpandedItems(initial);
  }, [toc]);

  // 激活子节点时展开所有祖先
  useEffect(() => {
    if (activeTocId) {
      let pid = parentMap[activeTocId];
      const expo: Record<string, boolean> = {};
      while (pid) {
        expo[pid] = true;
        pid = parentMap[pid];
      }
      setExpandedItems((prev) => ({ ...prev, ...expo }));
    }
  }, [activeTocId, parentMap]);

  // 递归渲染多层级目录
  const renderItems = (items: Array<TocItem & { children?: TocItem[] }>) =>
    items.map((item) => {
      const hasChildren = !!item.children && item.children.length > 0;
      const isExpanded = expandedItems[item.id];
      const isActive = activeTocId === item.id;
      return (
        <React.Fragment key={item.id}>
          <Flex
            alignItems="center"
            py={3}
            px={4}
            pl={8 * (item.level - 1)}
            cursor={hasChildren ? 'pointer' : 'default'}
            transition="all 0.2s"
            bg={isActive ? activeBgColor : 'transparent'}
            _hover={hasChildren ? { bg: hoverBgColor } : {}}
            onClick={(e: React.MouseEvent<HTMLElement>) => {
              e.stopPropagation();
              if (hasChildren) {
                setExpandedItems((prev) => ({ ...prev, [item.id]: !prev[item.id] }));
              } else {
                onClick(item.id);
              }
            }}
          >
            <Text
              flex="1"
              fontSize={`${16 - (item.level - 1) * 2}px`}
              fontWeight="400"
              color={isActive ? activeTextColor : textColor}
              onClick={(e: React.MouseEvent<HTMLElement>) => {
                e.stopPropagation();
                onClick(item.id);
              }}
            >
              {item.text}
            </Text>
            {hasChildren && (
              <Box
                as="span"
                fontSize="18px"
                cursor="pointer"
                transition="transform 0.2s"
                transform={isExpanded ? 'rotate(90deg)' : 'rotate(0)'}
                color={expandedIconColor}
              >
                ›
              </Box>
            )}
          </Flex>
          {hasChildren && isExpanded && renderItems(item.children!)}
        </React.Fragment>
      );
    });

  return (
    <Box as="nav" width="100%" pr={0} height="100%" overflowY="auto" bg="white">
      {/* 递归渲染多层级目录 */}
      {renderItems(tocTree)}
    </Box>
  );
};

// 自定义渲染组件，为标题添加与目录项相匹配的ID
const HeadingRenderer = ({ children, ...props }: any) => {
  const level = props.level;
  const content = children?.[0] as string;

  if (!content) return null;

  // 使用与 extractHeadings 相同的 ID 生成逻辑，确保一致性
  const id = `heading-${content
    .toLowerCase()
    .replace(/[^\w\u4e00-\u9fa5\- ]/g, '')
    .replace(/\s+/g, '-')}`;

  // 根据标题级别使用不同的组件
  switch (level) {
    case 1:
      return <h1 id={id}>{children}</h1>;
    case 2:
      return <h2 id={id}>{children}</h2>;
    case 3:
      return <h3 id={id}>{children}</h3>;
    case 4:
      return <h4 id={id}>{children}</h4>;
    case 5:
      return <h5 id={id}>{children}</h5>;
    case 6:
      return <h6 id={id}>{children}</h6>;
    default:
      return <h6 id={id}>{children}</h6>;
  }
};

export default function Guide() {
  const [toc, setToc] = useState<TocItem[]>([]);
  const [activeTocId, setActiveTocId] = useState('');
  const [markdownRendered, setMarkdownRendered] = useState(false);
  const [pendingScrollId, setPendingScrollId] = useState<string | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const bgColor = useColorModeValue('white', 'gray.800');
  const tocBgColor = useColorModeValue('gray.50', 'gray.900');

  // 获取文档内容
  const {
    data: docData,
    isLoading,
    error
  } = useQuery<DocContent>({
    queryKey: ['docContent'],
    queryFn: () => getMockDocContent()
  });

  // 提取标题，生成目录
  useEffect(() => {
    if (docData?.content) {
      // 重置渲染状态
      setMarkdownRendered(false);

      const headings = extractHeadings(docData.content);
      setToc(headings);

      // 设置默认激活的目录项
      if (headings.length > 0 && !activeTocId) {
        setActiveTocId(headings[0].id);
      }
    }
  }, [docData?.content, activeTocId]);

  // 处理待处理的滚动，确保在渲染完成后执行
  useEffect(() => {
    if (markdownRendered && pendingScrollId) {
      console.log(`渲染完成，执行待处理的滚动: ${pendingScrollId}`);
      executeScroll(pendingScrollId);
      setPendingScrollId(null);
    }
  }, [markdownRendered, pendingScrollId]);

  // 处理 Markdown 渲染完成
  const handleMarkdownRendered = () => {
    console.log('Markdown 渲染完成');
    setMarkdownRendered(true);
  };

  // 执行滚动操作
  const executeScroll = (id: string) => {
    try {
      // 尝试多种方式查找目标元素
      let targetElement: HTMLElement | null = document.getElementById(id);

      // 调试输出
      console.log(`查找元素: ${id}`, !!targetElement);

      if (!targetElement) {
        // 尝试使用querySelector查找
        targetElement = document.querySelector(
          `h1[id="${id}"], h2[id="${id}"], h3[id="${id}"], h4[id="${id}"], h5[id="${id}"], h6[id="${id}"]`
        );
        console.log(`使用querySelector查找: ${id}`, !!targetElement);
      }

      // 如果仍未找到，尝试遍历所有标题元素
      if (!targetElement) {
        console.log('尝试遍历所有标题元素...');
        const allHeadings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        allHeadings.forEach((heading) => {
          console.log(`标题ID: ${heading.id}`);
          if (heading.id === id) {
            targetElement = heading as HTMLElement;
          }
        });
      }

      if (targetElement) {
        // 使用自定义滚动函数滚动到目标位置
        scrollToPosition(targetElement);
        console.log(`成功滚动到元素: ${id}`);
      } else {
        console.warn(`所有方法都未找到ID为 ${id} 的元素`);
      }
    } catch (error) {
      console.error('滚动过程中出错:', error);
    }
  };

  // 点击目录项时滚动到对应位置
  const scrollToHeading = (id: string) => {
    // 更新当前激活的目录项
    setActiveTocId(id);
    // 直接滚动
    executeScroll(id);

    if (markdownRendered) {
      // 如果已经渲染完成，立即执行滚动
      console.log(`立即执行滚动: ${id}`);
      executeScroll(id);
    } else {
      // 如果尚未渲染完成，设置待处理的滚动
      console.log(`设置待处理的滚动: ${id}`);
      setPendingScrollId(id);
    }

    // 在移动端时关闭抽屉
    if (isOpen) {
      onClose();
    }
  };

  // 自定义滚动位置逻辑
  const scrollToPosition = (element: HTMLElement) => {
    // 使用 scrollIntoView 代替 window.scrollTo
    // 滚动边距通过 CSS scrollMarginTop 属性控制
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  // 加载状态
  if (isLoading) {
    return (
      <Center minHeight="100vh">
        <Spinner size="xl" color="purple.500" thickness="4px" />
      </Center>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Center minHeight="100vh">
        <Text color="red.500">加载文档失败，请刷新页面重试</Text>
      </Center>
    );
  }

  return (
    <Box className="wdnmd" w="100%" minHeight="100vh">
      {/* 移动端显示汉堡菜单按钮 */}
      <Box display={{ base: 'block', md: 'none' }} position="fixed" top={5} left={5} zIndex={10}>
        <IconButton
          aria-label="Open menu"
          icon={<HamburgerIcon />}
          onClick={onOpen}
          colorScheme="gray"
          variant="solid"
          bg="gray.200"
          _hover={{ bg: 'gray.300' }}
          size="md"
        />
      </Box>

      {/* 移动端目录抽屉 */}
      <Drawer isOpen={isOpen} placement="left" onClose={onClose} size="xs">
        <DrawerOverlay />
        <DrawerContent maxWidth="85vw" bg={useColorModeValue('gray.50', 'gray.800')}>
          <DrawerCloseButton size="md" top={respDims(12)} right={respDims(12)} />
          <DrawerBody p={0} overflowY="auto">
            <TableOfContents toc={toc} activeTocId={activeTocId} onClick={scrollToHeading} />
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* 桌面端布局 */}
      <Flex direction={{ base: 'column', md: 'row' }} h="100%" position="relative">
        {/* 左侧目录 */}
        <Box
          position="fixed"
          top="60px"
          bottom="0"
          left="0"
          height={{ md: '100vh' }}
          width={{ base: '100%', md: '280px', lg: '320px' }}
          display={{ base: 'none', md: 'block' }}
          overflowY="auto"
          pt={0}
          zIndex={2}
          boxShadow="md"
          bg="white"
          css={{
            height: 'calc(100vh - 60px)',
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(0, 0, 0, 0.1) transparent',
            '&::-webkit-scrollbar': {
              width: '4px'
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              borderRadius: '4px'
            }
          }}
        >
          <TableOfContents toc={toc} activeTocId={activeTocId} onClick={scrollToHeading} />
        </Box>

        {/* 右侧内容 */}
        <Box
          flex="1"
          ml={{ base: 0, md: '280px', lg: '320px' }}
          p={{ base: 4, md: 10 }}
          pt={{ base: 16, md: 10 }}
          bg={bgColor}
          ref={contentRef}
          id="content-container"
          position={'relative'}
        >
          <Box
            maxWidth="800px"
            mx="auto"
            className="markdown-content"
            css={{
              // 为所有标题设置滚动边距，防止被固定导航栏遮挡
              '& h1, & h2, & h3, & h4, & h5, & h6': {
                scrollMarginTop: '80px'
              },
              '& h1': {
                fontSize: '32px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px',
                paddingBottom: '8px',
                borderBottom: '1px solid',
                borderColor: 'inherit'
              },
              '& h2': {
                fontSize: '24px',
                fontWeight: 'bold',
                marginTop: '32px',
                marginBottom: '16px',
                paddingBottom: '4px'
              },
              '& h3': {
                fontSize: '20px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px'
              },
              '& h4, & h5, & h6': {
                fontSize: '16px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px'
              },
              '& p': {
                marginBottom: '16px',
                lineHeight: 1.7
              },
              '& ul, & ol': {
                marginBottom: '16px',
                paddingLeft: '24px'
              },
              '& li': {
                marginBottom: '8px'
              },
              '& table': {
                width: '100%',
                marginBottom: '16px',
                borderCollapse: 'collapse'
              },
              '& th, & td': {
                border: '1px solid',
                borderColor: 'inherit',
                padding: '8px'
              },
              '& pre': {
                padding: '16px',
                overflow: 'auto',
                fontSize: '14px',
                backgroundColor: 'var(--chakra-colors-gray-100)',
                borderRadius: '4px',
                marginBottom: '16px'
              },
              '& code': {
                fontFamily: 'monospace',
                fontSize: '14px',
                padding: '0 4px',
                backgroundColor: 'var(--chakra-colors-gray-100)',
                borderRadius: '4px'
              },
              '& pre > code': {
                padding: 0,
                backgroundColor: 'transparent'
              },
              '& blockquote': {
                borderLeft: '4px solid',
                borderColor: 'var(--chakra-colors-gray-300)',
                paddingLeft: '16px',
                fontStyle: 'italic',
                marginBottom: '16px'
              },
              '& img': {
                maxWidth: '100%',
                height: 'auto',
                marginBottom: '16px'
              },
              '& hr': {
                margin: '24px 0',
                border: 0,
                borderBottom: '1px solid',
                borderColor: 'inherit'
              }
            }}
          >
            {/* 面包屑 */}
            <Flex
              className="breadcrumb-container"
              margin={'0'}
              alignItems="center"
              gap="2.5"
              mb="6"
              css={{
                ol: {
                  padding: '0',
                  margin: '0'
                }
              }}
            >
              <Breadcrumb fontSize="14px">
                <BreadcrumbItem>
                  <BreadcrumbLink
                    onClick={() => router.push('/officialHome')}
                    color="#3E4147"
                    _hover={{ color: 'blue.500', textDecoration: 'none' }}
                  >
                    首页
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem>
                  <BreadcrumbLink
                    color="#3E4147"
                    _hover={{ color: 'blue.500', textDecoration: 'none' }}
                  >
                    智能体建设
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem isCurrentPage>
                  <BreadcrumbLink
                    color="#3E4147"
                    fontWeight="500"
                    _hover={{ textDecoration: 'none' }}
                  >
                    平台介绍
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </Breadcrumb>
            </Flex>

            <Box
              css={{
                borderRadius: '50px',
                opacity: 0.56,
                background: 'linear-gradient(90deg, #D7E1FF 0%, #D1F8FF 95.19%)',
                boxShadow: '0px 4px 4px 0px rgba(104, 115, 160, 0.32)',
                width: '134px',
                height: '40px',
                position: 'absolute',
                top: '37px',
                right: '98px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                color: '#2468F2'
              }}
              onClick={() => router.push('/zeroCode?tab=my')}
            >
              创建你的Agent
            </Box>

            {docData && (
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkBreaks, remarkMath]}
                rehypePlugins={[rehypeRaw, rehypeKatex, rehypeSanitize]}
                components={{
                  h1: HeadingRenderer,
                  h2: HeadingRenderer,
                  h3: HeadingRenderer,
                  h4: HeadingRenderer,
                  h5: HeadingRenderer,
                  h6: HeadingRenderer
                }}
              >
                {docData.content}
              </ReactMarkdown>
            )}
          </Box>
        </Box>
      </Flex>
    </Box>
  );
}
