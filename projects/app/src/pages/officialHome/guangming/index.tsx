import {
  Box,
  Flex,
  Image,
  Text,
  Button,
  Container,
  SimpleGrid,
  VStack,
  HStack
} from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';
import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAppStore } from '@/store/useAppStore';
import type { AppListItemType } from '@/types/api/app';
interface Question {
  title: string;
  content: string;
}

interface KnowledgeCard {
  title: string;
  bgColor: string;
  questions: Question[];
  count: number;
  textColor?: string;
}

interface Agent {
  id: string;
  name: string;
  users: string;
  desc: string;
  tags: string[];
  bgColor: string;
  index: number;
  textColor?: string;
  avatarUrl: string;
}

interface NewsCardProps {
  title: string;
  content: string;
}

interface KnowledgeCardProps extends KnowledgeCard {}

interface AgentCardProps extends Agent {}

// 知识问答数据
const knowledgeCards: KnowledgeCard[] = [
  {
    title: '智能体基础知识',
    bgColor: 'blue.50',
    textColor: '#2572F4',
    questions: [
      {
        title: '什么是智能体？它与普通AI有什么区别？',
        content:
          '智能体是一种能够感知环境并采取行动以实现特定目标的 AI 系统。与普通 AI 不同，智能体具有自主性、持续学习能力和特定领域的专业知识...'
      },
      {
        title: '智能体在教育领域能解决哪些具体问题？',
        content:
          '智能体可以提供个性化学习指导、自动批改作业、生成教学资源、回答专业问题、进行学情分析等，大幅提升教学效率和学习体验...'
      }
    ],
    count: 5
  },
  {
    title: '智能体创建指南',
    bgColor: 'green.50',
    questions: [
      {
        title: '如何创建一个高质量的教育智能体？',
        content:
          '创建高质量教育智能体需要明确目标、收集专业知识、设计对话流程、编写提示词、测试优化等步骤，详细指南请参考...'
      },
      {
        title: '智能体的知识库如何构建和更新？',
        content:
          '知识库构建需要收集权威资料、结构化整理、定期更新。可通过文档上传、API 连接或手动编辑等方式添加和更新内容...'
      }
    ],
    count: 10,
    textColor: '#20A550'
  },
  {
    title: '智能体应用案例',
    bgColor: '#FFFBEB',
    questions: [
      {
        title: '有哪些成功的教育智能体案例可以参考？',
        content:
          '光明区"教学规划助手"、"作业批改助手"和"个性化学习导师"是三个成功案例，它们分别在教学设计、作业评估和个性化学习方面取得了显著成效...'
      },
      {
        title: '如何评估智能体的实际教学效果？',
        content:
          '可通过用户满意度调查、使用频率统计、学习效果对比、教师工作效率提升等多维度指标进行综合评估，建立科学的评估体系...'
      }
    ],
    count: 10,
    textColor: '#FF8D0E'
  }
];

// 原始智能体数据接口
interface RawAgent extends AppListItemType {
  name: string;
  intro: string;
  avatarUrl: string;
}
// 将原始数据转换为Agent类型
const createAgentData = (rawAgent: RawAgent, index: number, type: string[]): Agent => {
  let bgColor = '#EDF0FD';
  let textColor: string | undefined = undefined;

  if (type[0] === '综合') {
    bgColor =
      index === 0 ? '#F73131' : index === 1 ? '#FF8C1A' : index === 2 ? '#FFBF00' : '#EDF0FD';
  } else if (type[0] === '教师热门') {
    bgColor =
      index === 0 ? '#4087FD' : index === 1 ? '#32D5FE' : index === 2 ? '#9240FD' : '#EDF0FD';
  } else {
    bgColor =
      index === 0 ? '#20A550' : index === 1 ? '#FF8D0E' : index === 2 ? '#8E44AD' : '#EDF0FD';
  }

  if (index === 3) {
    textColor = '#848691';
  }

  return {
    id: rawAgent.id.toString(),
    name: rawAgent.name,
    users: `${Math.floor(Math.random() * 5000 + 1000)}`,
    desc: rawAgent.intro,
    tags: type,
    bgColor,
    textColor,
    avatarUrl: rawAgent.avatarUrl,
    index
  };
};
// 标签页类型
type TabKey = 'comprehensive' | 'teacherHot' | 'studentHot';

const OfficialHome = () => {
  const router = useRouter();
  // 添加状态用于跟踪当前选中的标签
  const [activeTab, setActiveTab] = React.useState<TabKey>('comprehensive');
  const { myAppsOfficialHome, loadMyAppsOfficialHome } = useAppStore();

  // 添加状态用于存储当前显示的智能体
  const [displayedAgents, setDisplayedAgents] = React.useState<Agent[]>([]);
  const { isFetching, refetch: loadAppsRefetch } = useQuery(
    ['loadAppsOfficialHome'],
    () => loadMyAppsOfficialHome(true),
    {
      refetchOnMount: true
    }
  );
  // 处理标签点击事件
  const handleTabClick = (tab: TabKey) => {
    setActiveTab(tab);
    setDisplayedAgents(agentGroups[tab]);
  };
  console.log(myAppsOfficialHome, 'myAppsOfficialHome');

  const agentGroups: Record<TabKey, Agent[]> = useMemo(() => {
    return {
      comprehensive: myAppsOfficialHome
        .slice(0, 4)
        .map((app, index) => createAgentData(app, index, ['综合'])),
      teacherHot: myAppsOfficialHome
        .slice(4, 8)
        .map((app, index) => createAgentData(app, index, ['教师热门'])),
      studentHot: myAppsOfficialHome
        .slice(8, 12)
        .map((app, index) => createAgentData(app, index, ['学生热门']))
    };
  }, [myAppsOfficialHome]);

  useEffect(() => {
    setDisplayedAgents(agentGroups[activeTab]);
  }, [myAppsOfficialHome, activeTab, agentGroups]);

  return (
    <Box minH="100vh">
      <Flex w="100%" flexDir="column">
        {/* Banner区域 */}
        <Box position="relative" mb={respDims(50, GLOBAL_DIMS_MIN_SCALE)}>
          <Box
            position="absolute"
            w="704px"
            h="144px"
            top="225px"
            left="0"
            bg="#ABB4FF78"
            borderRadius="352px/72px"
            filter="blur(60.1px)"
            opacity="0.53"
          />
          {/* 欢迎语区域 */}
          <Container maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)} position="relative">
            <Flex
              flexDir="column"
              alignItems="flex-start"
              pl={respDims(100, GLOBAL_DIMS_MIN_SCALE)}
              pt={respDims(134, GLOBAL_DIMS_MIN_SCALE)}
            >
              <Flex gap="15px" mb="30px">
                <Text
                  fontFamily="Almarai-Bold"
                  fontSize={respDims(52.8, GLOBAL_DIMS_MIN_SCALE)}
                  color="#091221"
                  lineHeight="62.4px"
                  fontWeight="bold"
                >
                  AI 赋能教育,
                </Text>
                <Text
                  fontFamily="Almarai-Bold"
                  fontSize={respDims(52.8, GLOBAL_DIMS_MIN_SCALE)}
                  bgGradient="linear(90deg, #2468F2 0%, #27ACFF 100%)"
                  bgClip="text"
                  lineHeight="62.4px"
                  fontWeight="bold"
                >
                  智慧引领未来
                </Text>
              </Flex>
              <Text fontSize={respDims(14.4, GLOBAL_DIMS_MIN_SCALE)} color="#091221B2" mb="30px">
                光明区教科院智慧教育平台，为教师、学生和家长提供全方位的智能教育服务
              </Text>
              <Button
                onClick={() => router.push('/zeroCode?tab=my')}
                w={respDims(226, GLOBAL_DIMS_MIN_SCALE)}
                h={respDims(48, GLOBAL_DIMS_MIN_SCALE)}
                bgGradient="linear-gradient(268deg, #32D5FE 8%, #4087FD 47.43%, #9240FD 96.63%)"
                color="white"
                fontWeight="bold"
                fontSize={respDims(16.8, GLOBAL_DIMS_MIN_SCALE)}
                borderRadius={respDims(9.6, GLOBAL_DIMS_MIN_SCALE)}
                _hover={{
                  bgGradient: 'linear-gradient(268deg, #32D5FE 8%, #4087FD 47.43%, #9240FD 96.63%)'
                }}
              >
                快来定义你的Agent
              </Button>
            </Flex>
          </Container>
        </Box>
        {/* 热门资讯区域 */}
        <Container maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)} position="relative">
          {/* 热门资讯标题 */}
          <Flex alignItems="center" gap="6px" mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)}>
            <SvgIcon
              name="hot"
              w={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
              h={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
            />
            <Text
              fontFamily="YouSheBiaoTiHei-Regular"
              fontSize={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
              color="#1F2329"
              fontWeight="bold"
            >
              热门资讯
            </Text>
          </Flex>

          {/* 新闻卡片 */}
          <SimpleGrid
            columns={{ base: 1, sm: 2, md: 3 }}
            spacing={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            mb={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
          >
            <NewsCard
              title={'光明区教科院着力打造“AI+教育”先锋城区'}
              content="近日，光明区教科院召开2025年教育工作会议，部署2025年重点工作。区教科院领导班子、全区公办学校负责人、民办及学前教育代表齐聚一堂，共谋教育综合改革。"
            />
            <NewsCard
              title="光明区教师智能化教学能力提升培训计划启动"
              content="计划将覆盖全区 5000 名教师，提升 AI 教学应用能力教师"
            />
            <NewsCard
              title="AI 助教系统在光明区 10 所试点学校取得显著成效"
              content="学生学习积极性提高 32%，教师工作效率提升 45%"
            />
          </SimpleGrid>
        </Container>

        {/* 主体内容区域 */}
        <Container
          maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)}
          py={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
        >
          {/* 知识问答区域 */}
          <Box mb={respDims(40, GLOBAL_DIMS_MIN_SCALE)}>
            <Flex mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)} alignItems="center" gap="6px">
              <SvgIcon
                name="hot"
                w={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
                h={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
              />
              <Text
                fontFamily="YouSheBiaoTiHei-Regular"
                fontSize={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
                color="#1F2329"
                fontWeight="bold"
              >
                AI知识问答
              </Text>
            </Flex>

            <SimpleGrid
              columns={{ base: 1, sm: 2, md: 3 }}
              spacing={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            >
              {knowledgeCards.map((card, index) => (
                <KnowledgeCard key={index} {...card} />
              ))}
            </SimpleGrid>
          </Box>

          {/* 热门智能体 */}
          <Box>
            <Flex mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)} alignItems="center" gap="6px">
              <SvgIcon
                name="hot"
                w={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
                h={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
              />
              <Text
                fontFamily="YouSheBiaoTiHei-Regular"
                fontSize={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
                color="#1F2329"
                fontWeight="bold"
              >
                热门智能体
              </Text>
            </Flex>

            <Flex justifyContent="space-between">
              <Flex
                mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                gap={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
              >
                <Button
                  variant={activeTab === 'comprehensive' ? 'solid' : 'ghost'}
                  bg={activeTab === 'comprehensive' ? '#091221' : '#0912210F'}
                  color={activeTab === 'comprehensive' ? 'white' : '#091221'}
                  h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50px"
                  onClick={() => handleTabClick('comprehensive')}
                >
                  综合
                </Button>
                <Button
                  variant={activeTab === 'teacherHot' ? 'solid' : 'ghost'}
                  bg={activeTab === 'teacherHot' ? '#091221' : '#0912210F'}
                  color={activeTab === 'teacherHot' ? 'white' : '#091221'}
                  h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50px"
                  onClick={() => handleTabClick('teacherHot')}
                >
                  教师热门
                </Button>
                <Button
                  variant={activeTab === 'studentHot' ? 'solid' : 'ghost'}
                  bg={activeTab === 'studentHot' ? '#091221' : '#0912210F'}
                  color={activeTab === 'studentHot' ? 'white' : '#091221'}
                  h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50px"
                  onClick={() => handleTabClick('studentHot')}
                >
                  学生热门
                </Button>
              </Flex>
              <Box>
                <Text
                  color="#2572F4"
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  cursor="pointer"
                  onClick={() => {
                    router.push('/officialHome/guangming/agentCenter');
                  }}
                >
                  前往智能体广场
                </Text>
              </Box>
            </Flex>

            <SimpleGrid
              columns={{ base: 1, sm: 2, md: 4 }}
              spacing={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            >
              {displayedAgents.map((agent, index) => (
                <AgentCard key={agent.id} {...agent} index={index} />
              ))}
            </SimpleGrid>
          </Box>
        </Container>
      </Flex>
    </Box>
  );
};

// 新闻卡片组件
const NewsCard: React.FC<NewsCardProps> = ({ title, content }) => (
  <Box
    w="100%"
    h={{ base: 'auto', md: '120px' }}
    bg="white"
    borderRadius="18px"
    p={{ base: '12px 16px', md: '17px 24px' }}
    boxShadow="0px 4px 14.8px #C3DFFD61"
  >
    <VStack align="flex-start" spacing="9px">
      <VStack align="flex-start" spacing="4px">
        <Text fontSize="16px" fontWeight="600" color="#091221" noOfLines={1}>
          {title}
        </Text>
        <Text fontSize="14px" color="#091221B2" noOfLines={1}>
          {content}
        </Text>
      </VStack>
      <HStack spacing="5px" cursor="pointer">
        <Text fontSize="14px" color="#2572F4">
          查看详情
        </Text>
        <SvgIcon
          transform="rotate(-45deg)"
          name="navIcon"
          color="#2572F4"
          w={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
          h={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
        />
      </HStack>
    </VStack>
  </Box>
);

// 知识卡片组件
const KnowledgeCard: React.FC<KnowledgeCardProps> = ({
  title,
  textColor,
  bgColor,
  questions,
  count
}) => (
  <Box
    w="100%"
    h={{ base: 'auto', md: '350px' }}
    bg={bgColor}
    borderRadius="16.8px"
    border="2px solid white"
    boxShadow="inset 0px 2px 0px #FFFFFF, 0px 2.4px 7.2px #0912210A"
    backdropFilter="blur(2px)"
    p={{ base: '15px', md: '20px' }}
  >
    <Box mb="20px">
      <Box position="relative" display="inline-block">
        <Text fontSize="18px" fontWeight="600" color="#091221" mb="4px">
          {title}
        </Text>
        <Box
          position="absolute"
          bottom="0"
          left="0"
          w="100%"
          h="12px"
          bg="#AAECFF"
          mixBlendMode="multiply"
        />
      </Box>
    </Box>

    <VStack spacing="10px">
      {questions.map((q, index) => (
        <Box
          key={index}
          w="100%"
          bg="white"
          borderRadius="12px"
          p="16px"
          boxShadow="0px 1px 4.8px #0912210A"
        >
          <Text fontSize="15px" fontWeight="500" color="#243E74" mb="6px">
            {q.title}
          </Text>
          <Text fontSize="13px" color="#091221B2" noOfLines={2}>
            {q.content}
          </Text>
        </Box>
      ))}
    </VStack>

    <HStack justify="space-between" mt="20px">
      <Text fontSize="13px" color="#7F7F7F">
        共{count}个问答
      </Text>
      <HStack spacing="5px" cursor="pointer">
        <Text fontSize="14px" color={textColor}>
          查看更多
        </Text>
        <SvgIcon
          name="navIcon"
          w={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
          h={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
          color={textColor}
        />
      </HStack>
    </HStack>
  </Box>
);

// 智能体卡片组件
const AgentCard: React.FC<Agent> = ({
  id,
  index,
  name,
  users,
  desc,
  tags,
  bgColor,
  textColor = 'white',
  avatarUrl
}) => {
  const router = useRouter();

  return (
    <Box
      w="100%"
      h={{ base: 'auto', md: '321px' }}
      bg="white"
      borderRadius="12px"
      boxShadow="0px 6px 18px #C3DFFD61"
      position="relative"
    >
      <Box
        position="absolute"
        top="0"
        left="0"
        w="33px"
        h="35px"
        bg={bgColor}
        borderRadius="12px 0px 12px 0px"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Text
          fontFamily="YouSheBiaoTiHei-Regular"
          fontSize="24px"
          fontWeight="bold"
          color={textColor}
        >
          {index + 1}
        </Text>
      </Box>

      <VStack spacing={{ base: '10px', md: '15px' }} pt={{ base: '15px', md: '21px' }}>
        <Image
          src={avatarUrl}
          alt={name}
          w="80px"
          h="80px"
          borderRadius="50%"
          border="1px solid #EAEAEA"
        />

        <Text fontSize="15px" fontWeight="500" color="#1F2329">
          {name}
        </Text>

        <Text fontSize="12px" color="#5F7288">
          （{users} 人使用）
        </Text>

        <Text fontSize="13px" color="#5F7288" textAlign="center" px="20px" noOfLines={2}>
          {desc}
        </Text>

        <HStack spacing="12px">
          {tags.map((tag, index) => (
            <Box
              key={index}
              px="10px"
              h="24px"
              bg="#0912210F"
              borderRadius="4px"
              display="flex"
              alignItems="center"
            >
              <Text fontSize="12px" color="#091221">
                {tag}
              </Text>
            </Box>
          ))}
        </HStack>

        <Button
          w={{ base: '90%', md: '245px' }}
          h="32px"
          bg="#2572F4"
          color="white"
          fontSize="13px"
          _hover={{
            bgGradient: '#2572F4'
          }}
          borderRadius="8px"
          onClick={() => router.push(`/home?appId=${id}`)}
        >
          立即使用
        </Button>
      </VStack>
    </Box>
  );
};

export default OfficialHome;
