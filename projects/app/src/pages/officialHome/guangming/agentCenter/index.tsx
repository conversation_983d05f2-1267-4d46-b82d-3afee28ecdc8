import { useState, useMemo, useRef, useCallback, useEffect } from 'react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import {
  Box,
  Flex,
  Text,
  InputGroup,
  Input,
  InputRightElement,
  SimpleGrid,
  Image,
  useMediaQuery,
  Toast,
  Grid,
  Center,
  Tooltip,
  getToken,
  MenuButton
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { APP_ICON, DataSource } from '@/constants/common';
import { useAppStore } from '@/store/useAppStore';
import { getOtherAppList, getTenantSceneListOfficialHome } from '@/api/scene';
import { SceneType } from '@/types/api/scene';
import { useQuery } from '@tanstack/react-query';
import MyIcon from '@/components/LegacyIcon';
import { AppListItemType } from '@/types/api/app.d';

// 扩展应用类型，添加Figma设计所需的属性
interface ExtendedAppItemType extends AppListItemType {
  isHot?: boolean;
  category?: string;
  rating?: string;
  usageCount?: string;
}

// 分类标签组件
interface CategoryTabProps {
  label: string;
  isActive: boolean;
  onClick: () => void;
  filterSceneId: string;
  sceneId: string;
}

const CategoryTab = ({ label, isActive, onClick, filterSceneId, sceneId }: CategoryTabProps) => {
  console.log('CategoryTab', sceneId, filterSceneId);
  return (
    <Box
      py={respDims(5)}
      px={respDims(16)}
      bg={sceneId === filterSceneId ? '#2468F2' : 'rgba(0, 0, 0, 0.05)'}
      color={sceneId === filterSceneId ? '#fff' : '#0A0A0A'}
      borderRadius={respDims(8)}
      fontSize={respDims(14)}
      fontWeight={sceneId === filterSceneId ? '500' : '400'}
      cursor="pointer"
      onClick={onClick}
      h={respDims(36)}
      display="flex"
      alignItems="center"
      justifyContent="center"
    >
      {label}
    </Box>
  );
};

// 定义Agent中心页面
export default function AgentCenter({ sceneId }: { sceneId?: string }) {
  const [filterText, setFilterText] = useState('');
  const [activeCategory, setActiveCategory] = useState('最近使用');
  const [isLargerThan768] = useMediaQuery('(min-width: 768px)');
  const { myAppsOfficialHome, loadMyAppsOfficialHome } = useAppStore();
  const [scenesList, setScenesList] = useState<SceneType[]>([]);
  const [filterSceneId, setFilterSceneId] = useState(sceneId || 'all');
  const [showBackToTop, setShowBackToTop] = useState(false);
  const gridRef = useRef<HTMLDivElement>(null);
  /* 加载模型 */
  const { isFetching, refetch: loadAppsRefetch } = useQuery(
    ['loadAppsOfficialHome'],
    () => loadMyAppsOfficialHome(true),
    {
      refetchOnMount: true
    }
  );
  const router = useRouter();
  const scrollRef = useRef<HTMLDivElement>(null);

  const { type } = router.query;

  useQuery(['tenantDetail'], () => getTenantSceneListOfficialHome(), {
    onSuccess: (data) => {
      console.log('data', data);
      if (data) {
        setScenesList(data);
      }
      if (type) {
        let targetSceneId = '';
        if (type === '1') targetSceneId = '34367';
        if (type === '2') targetSceneId = '34363';

        if (targetSceneId) {
          setTimeout(() => {
            handleScrollAndSetFilter(targetSceneId);
          }, 100);
        }
      }
    }
  });

  const presentScenes = useMemo(() => {
    const scenes = [
      { name: '全部', sceneId: 'all' },
      ...scenesList.map((it) => ({ ...it, sceneId: it.id }))
    ];

    return scenes;
  }, [scenesList]);

  const combinedList = useMemo(() => {
    console.log('myAppsOfficialHome', myAppsOfficialHome);
    return [
      ...scenesList.map((scene) => {
        const apps = myAppsOfficialHome.filter((item) =>
          item.labelList?.some((it) => String(it.tenantSceneId) === String(scene.id))
        );
        return {
          name: scene.name,
          sceneId: scene.id,
          apps
        };
      })
    ];
  }, [scenesList, myAppsOfficialHome]);

  useEffect(() => {
    console.log('combinedList', combinedList);
  }, [combinedList]);

  const filteredApps = useMemo(() => {
    if (!filterText) return [];
    const regex = new RegExp(filterText, 'i');

    return combinedList
      .filter((scene) => scene.sceneId !== 'recentlyUsed') // 过滤掉最近使用的数据
      .flatMap((scene) => scene.apps)
      .filter((app) => app && regex.test(app.name));
  }, [filterText, combinedList]);

  const scrollToElement = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleScrollAndSetFilter = (id: string) => {
    console.log('handleScrollAndSetFilter', id);
    setFilterSceneId(id);
    scrollToElement(id);
  };

  const checkScroll = useCallback(() => {
    if (!scrollRef.current) {
      return;
    }
    setShowBackToTop(scrollRef.current.scrollTop > scrollRef.current.clientHeight);
  }, []);

  const backToTop = () => {
    scrollRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleAgentClick = (agentId: string) => {
    console.log('handleAgentClick', agentId);
    if (agentId === 'all') {
      backToTop();
      setFilterSceneId(agentId);
      return;
    }
    const personageData = myAppsOfficialHome.filter(({ source }) => source === DataSource.Personal);
    if (agentId === 'personage' && personageData.length > 0) {
      handleScrollAndSetFilter(agentId);
      return;
    } else {
      const data = myAppsOfficialHome.filter((item) =>
        item.labelList?.some((it) => String(it.tenantSceneId) === agentId)
      );

      if (data.length <= 0) {
        Toast({
          title: '暂无应用',
          status: 'info'
        });
        return;
      }
    }
    handleScrollAndSetFilter(agentId);
  };

  return (
    <Box minH="100vh" position="relative" pb={respDims(100)} display="flex" flexDir="column">
      <Box px={respDims(120)}>
        {/* 主要内容区域 */}
        <Box mx="auto">
          <Box
            bgRepeat="no-repeat"
            h={respDims(360)}
            display="flex"
            justifyContent="center"
            alignItems="center"
          >
            <InputGroup w={respDims(488)} h={respDims(40)}>
              <Input
                placeholder="请输入关键词,搜索Agent"
                bgColor="rgba(255,255,255,1)"
                fontSize={respDims(14)}
                border="none"
                borderRadius={respDims(50)}
                _placeholder={{ fontSize: respDims(14), color: '#4E5969' }}
                value={filterText}
                boxShadow="0px 4px 14.5px 0px rgba(0, 0, 0, 0.05)"
                onChange={(e) => setFilterText(e.target.value)}
              />
              <InputRightElement alignItems="center">
                <SvgIcon mr={respDims(6)} name="appAgentSearch" w={respDims(20)} h={respDims(20)} />
              </InputRightElement>
            </InputGroup>
          </Box>
          {/* 分类标签栏 */}
          <Flex
            pos="sticky"
            top="0"
            mt={respDims(24)}
            gap={respDims(16)}
            overflowX="auto"
            pb={respDims(5)}
            css={{
              '&::-webkit-scrollbar': {
                height: '4px'
              },
              '&::-webkit-scrollbar-track': {
                background: 'rgba(0,0,0,0.05)'
              },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(0,0,0,0.1)',
                borderRadius: '2px'
              }
            }}
          >
            {presentScenes.map((it, i) => (
              <CategoryTab
                key={it.sceneId}
                label={it.name}
                filterSceneId={filterSceneId}
                sceneId={it.sceneId}
                isActive={activeCategory === it.name && !filterText}
                onClick={() => {
                  it.sceneId && handleAgentClick(it.sceneId.toString());
                }}
              />
            ))}
          </Flex>
        </Box>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          mt={respDims(20)}
          // overflowX="hidden"
          // overflowY="auto"
          ref={scrollRef}
          h="100%"
          onScroll={checkScroll}
        >
          {filterText ? (
            <Grid
              ref={gridRef}
              gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
              gridGap={respDims(20)}
              w="100%"
              mx="auto"
              css={{
                ...(filteredApps.length <= 0
                  ? { display: 'flex', alignItems: 'center', justifyContent: 'center' }
                  : {})
              }}
            >
              {filteredApps.length > 0 ? (
                <>
                  {filteredApps.map((app) => (
                    <>
                      {app && (
                        <Flex
                          key={app.id}
                          h={respDims(184)}
                          p={respDims(20)}
                          flexDir="column"
                          position="relative"
                          cursor="pointer"
                          userSelect="none"
                          borderRadius={respDims(12)}
                          bgColor="#ffffff"
                          boxShadow="0px 6px 18px 0px rgba(241, 244, 251, 1)"
                          transition="all 0.3s ease-in-out"
                          _hover={{
                            transform: 'translateY(-5px)',
                            zIndex: '9',
                            boxShadow: '0px 9px 15.6px 0px rgba(163, 163, 163, 0.11)',
                            '& .app-menu': {
                              display: 'flex'
                            }
                          }}
                        >
                          {/* HOT标签 */}
                          {(app as ExtendedAppItemType).isHot && (
                            <Box
                              position="absolute"
                              top={0}
                              right={0}
                              borderRadius="0px 12px 0px 12px"
                              bgGradient="linear(to-r, #FF7842, #FF976B)"
                              px={respDims(8)}
                              py={respDims(2)}
                              color="#FFFFFF"
                              fontSize={respDims(14)}
                              fontWeight="500"
                            >
                              HOT
                            </Box>
                          )}

                          <Flex>
                            <Image
                              w={respDims(60)}
                              h={respDims(60)}
                              src={app.avatarUrl || '/images/agent-card/agent-avatar.png'}
                              alt=""
                              borderRadius="50%"
                            />

                            <Flex direction="column" ml={respDims(12)} gap={respDims(12)}>
                              <Text
                                color="#1F2329"
                                fontSize={respDims(16)}
                                fontWeight="500"
                                lineHeight="1.125em"
                              >
                                {app.name}
                              </Text>

                              <Text
                                color="#5F7288"
                                fontSize={respDims(14)}
                                fontWeight="400"
                                lineHeight="1.125em"
                              >
                                {(app as ExtendedAppItemType).category ||
                                  (app.source === DataSource.Tenant && '专属') ||
                                  (app.source === DataSource.Offical && '官方') ||
                                  (app.source === DataSource.Personal && '个人')}
                              </Text>
                            </Flex>
                          </Flex>

                          <Text
                            mt={respDims(13)}
                            color="#5F7288"
                            fontSize={respDims(13)}
                            fontWeight="400"
                            lineHeight="1.46em"
                            maxH={respDims(60)}
                            style={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitBoxOrient: 'vertical',
                              WebkitLineClamp: '3',
                              whiteSpace: 'normal',
                              wordBreak: 'break-word',
                              wordWrap: 'break-word'
                            }}
                          >
                            {app.intro || '暂无介绍'}
                          </Text>

                          <Flex mt="auto" justifyContent="space-between" alignItems="center">
                            <Flex alignItems="center" gap={respDims(6)}>
                              {[1, 2, 3, 4].map((star) => (
                                <Image
                                  key={star}
                                  src="/images/agent-card/star-fill.svg"
                                  alt="star"
                                  w={respDims(16)}
                                  h={respDims(16)}
                                />
                              ))}
                              <Image
                                src="/images/agent-card/star-half.svg"
                                alt="star-half"
                                w={respDims(16)}
                                h={respDims(16)}
                              />
                              <Text
                                ml={respDims(8)}
                                color="#000000"
                                fontSize={respDims(14)}
                                fontWeight="400"
                              >
                                {(app as ExtendedAppItemType).rating || '4.5'}
                              </Text>
                            </Flex>
                            <Text
                              color="#5F7288"
                              fontSize={respDims(14)}
                              fontWeight="400"
                              textAlign="right"
                            >
                              {(app as ExtendedAppItemType).usageCount || '1250'} 人使用
                            </Text>
                          </Flex>
                        </Flex>
                      )}
                    </>
                  ))}
                </>
              ) : (
                <Flex
                  direction="column"
                  justifyContent="center"
                  alignItems="center"
                  mt={respDims(160)}
                  h="100%"
                >
                  <Center>
                    <MyIcon name="appListEmpty" w={'100px'} h={'100px'} color={'transparent'} />
                  </Center>
                  <Center
                    m="20px 0 8px 0"
                    color="#303133"
                    fontWeight="500"
                    fontSize="18px"
                    textAlign="center"
                  >
                    没有搜索到匹配到应用，换个关键词吧～
                  </Center>
                </Flex>
              )}
            </Grid>
          ) : (
            <Box>
              {combinedList.length > 0 &&
                combinedList
                  .filter((item) => item.apps && item.apps.length > 0)
                  .map((scene, index) => (
                    <Box
                      key={scene.sceneId}
                      position="relative"
                      data-scene-id={String(scene.sceneId)}
                    >
                      <Flex mt={index !== 0 ? respDims(20) : 0} mb={respDims(32)}>
                        <Box position="absolute" top={respDims(0)} id={String(scene.sceneId)}></Box>
                        <Box fontSize={respDims(24)} fontWeight="500" color="#303133">
                          {scene.name}
                        </Box>
                      </Flex>

                      <Grid
                        ref={gridRef}
                        gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
                        gridGap={respDims(20)}
                      >
                        {scene.apps!.map((app) => (
                          <Flex
                            key={app.id}
                            h={respDims(184)}
                            p={respDims(20)}
                            flexDir="column"
                            position="relative"
                            cursor="pointer"
                            userSelect="none"
                            borderRadius={respDims(12)}
                            bgColor="#ffffff"
                            boxShadow="0px 6px 18px 0px rgba(241, 244, 251, 1)"
                            transition="all 0.3s ease-in-out"
                            _hover={{
                              transform: 'translateY(-5px)',
                              zIndex: '9',
                              boxShadow: '0px 9px 15.6px 0px rgba(163, 163, 163, 0.11)',
                              '& .app-menu': {
                                display: 'flex'
                              }
                            }}
                            onClick={() => {
                              router.push(`/home?appId=${app.id}`);
                            }}
                          >
                            {/* HOT标签 */}
                            {(app as ExtendedAppItemType).isHot && (
                              <Box
                                position="absolute"
                                top={0}
                                right={0}
                                borderRadius="0px 12px 0px 12px"
                                bgGradient="linear(to-r, #FF7842, #FF976B)"
                                px={respDims(8)}
                                py={respDims(2)}
                                color="#FFFFFF"
                                fontSize={respDims(14)}
                                fontWeight="500"
                              >
                                HOT
                              </Box>
                            )}

                            <Flex>
                              <Image
                                w={respDims(60)}
                                h={respDims(60)}
                                src={app.avatarUrl || '/images/agent-card/agent-avatar.png'}
                                alt=""
                                borderRadius="50%"
                              />

                              <Flex direction="column" ml={respDims(12)} gap={respDims(12)}>
                                <Text
                                  color="#1F2329"
                                  fontSize={respDims(16)}
                                  fontWeight="500"
                                  lineHeight="1.125em"
                                >
                                  {app.name}
                                </Text>

                                <Text
                                  color="#5F7288"
                                  fontSize={respDims(14)}
                                  fontWeight="400"
                                  lineHeight="1.125em"
                                >
                                  {(app as ExtendedAppItemType).category ||
                                    (app.source === DataSource.Tenant && '专属') ||
                                    (app.source === DataSource.Offical && '官方') ||
                                    (app.source === DataSource.Personal && '个人')}
                                </Text>
                              </Flex>
                            </Flex>

                            <Text
                              mt={respDims(13)}
                              color="#5F7288"
                              fontSize={respDims(13)}
                              fontWeight="400"
                              lineHeight="1.46em"
                              maxH={respDims(60)}
                              style={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitBoxOrient: 'vertical',
                                WebkitLineClamp: '3',
                                whiteSpace: 'normal',
                                wordBreak: 'break-word',
                                wordWrap: 'break-word'
                              }}
                            >
                              {app.intro || '暂无介绍'}
                            </Text>

                            <Flex mt="auto" justifyContent="space-between" alignItems="center">
                              <Flex alignItems="center" gap={respDims(6)}>
                                {[1, 2, 3, 4].map((star) => (
                                  <SvgIcon
                                    key={star}
                                    name="startFill"
                                    w={respDims(22)}
                                    h={respDims(22)}
                                  />
                                ))}
                                <SvgIcon name="startHalf" w={respDims(22)} h={respDims(22)} />
                                <Text
                                  ml={respDims(8)}
                                  color="#000000"
                                  fontSize={respDims(14)}
                                  fontWeight="400"
                                >
                                  {(app as ExtendedAppItemType).rating || '4.5'}
                                </Text>
                              </Flex>
                              <Text
                                color="#5F7288"
                                fontSize={respDims(14)}
                                fontWeight="400"
                                textAlign="right"
                              >
                                {(app as ExtendedAppItemType).usageCount || '1250'} 人使用
                              </Text>
                            </Flex>
                          </Flex>
                        ))}
                      </Grid>
                    </Box>
                  ))}
            </Box>
          )}
        </Box>
      </Box>
      {showBackToTop && (
        <Center
          position="absolute"
          right={respDims(80)}
          bottom="20px"
          boxShadow="0px 4px 4px 0px rgba(205,205,205,0.25)"
          borderRadius="50%"
          cursor="pointer"
          border="1px solid #EDEDED"
          onClick={backToTop}
        >
          <SvgIcon
            name="chevronUp"
            color="#606266"
            p="12px"
            w={respDims('32rpx', 45, 45)}
            h={respDims('32rpx', 45, 45)}
          />
        </Center>
      )}
    </Box>
  );
}
