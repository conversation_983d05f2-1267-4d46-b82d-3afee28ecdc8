import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Flex,
  useColorModeValue,
  Text,
  Heading,
  useDisclosure,
  IconButton,
  Drawer,
  Drawer<PERSON><PERSON>,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  DrawerCloseButton,
  Spinner,
  Center,
  Badge,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink
} from '@chakra-ui/react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import remarkMath from 'remark-math';
import rehypeRaw from 'rehype-raw';
import rehypeKatex from 'rehype-katex';
import rehypeSanitize from 'rehype-sanitize';
import { respDims } from '@/utils/chakra';
import { HamburgerIcon, ChevronRightIcon } from '@chakra-ui/icons';
import 'katex/dist/katex.min.css';
import { getMockDoc<PERSON>ontent, DocContent, getMarkdownContent } from '@/api/docs';
import { useQuery } from '@tanstack/react-query';
import router from 'next/router';
import { useRouter } from 'next/router';

const guideData = [
  {
    _id: '6830625a8017e337cc62ceb3',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '智能体创建指南\n\n目录\n\n第一章 创建应用 2\n\n1.1 简易应用 2\n\n1.1.1 AI创建与标准创建 2\n\n一、AI创建模式步骤 2\n\n二、标准创建模式步骤 4\n\n1.1.2 AI配置调整 5\n\n1.2 高阶应用 10\n\n1.2.1 高阶应用创建 10\n\n第二章 我的应用 15\n\n2.1 创建应用 15\n\n2.2 修改应用 15\n\n第三章 知识库 21\n\n3\\. 1 创建知识库 21\n\n3.2 修改知识库 25',
    a: '',
    chunkIndex: 0
  },
  {
    _id: '6830625b8017e337cc62ceba',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第一章 创建应用\n\n创建应用栏，提供创建简易应用（新手填表创建简单 AI 应用 ）和创建高阶应用（ 高级用户通过可视化编程构建复杂多轮对话 AI 应用 ）两种方式 。用户可以选择对应应用进行创建。\n\n路径：【零代码空间】-【创建应用】\n\n![descript](/api/system/img/683062588017e337cc62ce01.png)',
    a: '',
    chunkIndex: 1
  },
  {
    _id: '6830625c8017e337cc62ced6',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第一章 创建应用\n## 1.1 简易应用\n\n简易应用适合快速开发简单应用。输入需求后，工具会生成基础应用，例如界面和基本功能。简易应用创建分为了AI创建和标准创建两种，下面将分开介绍。',
    a: '',
    chunkIndex: 2
  },
  {
    _id: '6830625a8017e337cc62ceac',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第一章 创建应用\n## 1.1 简易应用\n### 1.1.1 AI创建与标准创建\n#### 一、AI创建模式步骤\n\n（1）点击【零代码空间】-【创建应用】-【创建简易应用】-【AI创建】\n\n![descript](/api/system/img/683062588017e337cc62ce02.png)\n\n![descript](/api/system/img/683062588017e337cc62ce03.png)\n\n（2）在文本框中描述想要创建的应用。例如，你可以输入"一个专注于语言学习和批改的应用"。输入完毕点击【确认】按钮。\n\n![descript](/api/system/img/683062588017e337cc62ce04.png)\n\n（3）【确认创建】之后，就会出现应用的生成界面，我们可以看到创建应用的进程，如下图红框内所示，此时正在生成应用的头像。\n\n![descript](/api/system/img/683062588017e337cc62ce05.png)\n\n（4）AI生成后，即会出现这个界面,所有的配置都已经配好，如果对配置满意的话，可以直接在右侧调试并保存发布。\n\n![descript](/api/system/img/683062588017e337cc62ce06.png)\n\n如果对AI生成的配置不满意，可以自行进行进一步调整，章节1.1.2有各个配置内容的介绍以及调整教程。',
    a: '',
    chunkIndex: 3
  },
  {
    _id: '6830625b8017e337cc62cebf',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第一章 创建应用\n## 1.1 简易应用\n### 1.1.1 AI创建与标准创建\n#### 二、标准创建模式步骤\n\n（1）点击【零代码空间】-【创建应用】-【创建简易应用】。\n\n![descript](/api/system/img/683062588017e337cc62ce07.png)\n\n（2）选择【标准创建】填写应用名称、应用介绍、应用头像后，点击【保存】进入简易配置页。\n\n![descript](/api/system/img/683062588017e337cc62ce08.png)\n\n（3）简易配置页的所有配置都需要我们手动配置，配置教程参考章节【1.1.2AI配置调整】',
    a: '',
    chunkIndex: 4
  },
  {
    _id: '6830625b584ecff133c1e0a4',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第一章 创建应用\n## 1.1 简易应用\n### 1.1.2 AI配置调整\n\n路径：【零代码空间】-【我的应用】找到需要调整配置的应用，点击右上角三个点选择【简易配置】\n\n（1）选择AI模型：\n\n系统有多个AI模型可选择，不同的模型擅长的方面不同，可根据具体需求进行选择。各模型特点如下：\n\n|     |     |     |\n| --- | --- | --- |\n| **模型名称** | **擅长能力** | **适用场景** |\n| **【推荐】教育大模型-pro** | 数据分析、解题思路、多语言翻译等多种综合能力。生成内容速度快。 | 适合大部分通用场景，逻辑推理场景。 |\n| **豆包-32k** | 信息搜索、法律知识理解、情感分析、多模态交互（文本、图像、语音）、角色扮演 | 日常对话、内容创作（自媒体、新闻）、教育辅助、电商营销、娱乐互动 |\n| **通义千问-32k** | 复杂指令理解、文档处理、多语言翻译、代码生成、知识检索、逻辑推理 | 问答系统、内容创作（文章、邮件、脚本）、编程辅助、行业定制化解决方案（电商、金融、政务） |\n| **deepseek-chat** | 编程能力强，尤其擅长代码生成、调试与讲解，具备良好中文表达与逻辑能力 | 编程辅助、学习理解技术文档、技术问答、数学解题、科研写作 |\n| **deepseek-r1** | 推理能力较强，语言表达自然，支持大文本处理 | 长文摘要、论文辅助、内容生成、语言建模 |\n| **文心一言** | 中文生成能力优秀，兼具文案创作、语言理解、图像识别（多模态）等功能 | 政务、教育、企业知识管理、日常对话、文案生成 |\n| **Kimi（月之暗面）** | 超长文本处理（支持200万字）、上下文记忆强、信息检索能力强 | 阅读长篇资料、写作助手、法律合规分析、专业文档处理、个人助理 |\n| **教育大模型-flash** | 擅长多模态推理、跨语言翻译、数据可视化、搜索能力集成 | 多模态应用（图文/音频/视频）、翻译、搜索整合、分析图像或图表内容 |\n| **教育大模型-mini** | 平衡能力模型，支持语音/图像/文本处理，响应快，逻辑性强 | 通用问答、代码理解、语言翻译、语音/视觉场景（OCR、图像描述、语音对话）等 |\n\n生成多样性：回复内容的多样性，\n\n-   越靠近"严谨"，智能体将越严格遵循指令内容生成内容，适用于需准确无误的场合，如正式文档、代码等\n-   在"严谨"和"发散"中间，在创新和精确之间寻求平衡，适用于大多数日常应用场景生成有趣但不失严谨的内容\n-   越靠近"发散"，智能体将更具有创意，为您提供新颖独特的想法，适合需要灵感和独特观点的场景，如头脑风暴、创意写作等\n\n记录轮数：每个聊天记录中，AI能记住的聊天历史轮次，比如：如果设置了6轮，那么AI能知道最近6轮我们都聊了哪些内容，结合这些内容回答新的问题\n\n回复上限：每次AI回复字数的上线\n\n如图：\n\n![descript](/api/system/img/683062588017e337cc62ce09.png)\n\n（2）填写提示词：\n\n提示词是发给大模型的自然语言，需像给实习生布置任务那样，尽可能全面地提供背景、任务、要求等信息，让大模型精准执行任务 。\n\n![descript](/api/system/img/683062588017e337cc62ce0a.png)\n\n在输入我们的需求之后，点击右上角的 "优化" 按钮可能具备一键优化提示词的功能。\n\n（3）知识库配置：\n\n类比于学校的文档档案库，可以在文档档案库中存放各种资料，面向智能体提问时，智能体可以在这个文档档案库中随时查阅，帮助我们快速找到和整理需要的答案和资料。',
    a: '',
    chunkIndex: 5
  },
  {
    _id: '6830625b584ecff133c1e08f',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第一章 创建应用\n## 1.1 简易应用\n### 1.1.2 AI配置调整\n![descript](/api/system/img/683062588017e337cc62ce15.png)\n\n![descript](/api/system/img/683062588017e337cc62ce17.png)\n\n（4）高级配置\n\n![descript](/api/system/img/683062588017e337cc62ce19.png)\n\n"高级配置" 中的工具：在 "高级配置" 部分的 "工具" 区域，可看到可用的工具。图中显示了 "联网搜索" 和 "文生图" 两个选项，并且可以通过点击 "+" 选择来添加更多工具。\n\n![descript](/api/system/img/683062588017e337cc62ce1b.png)\n\n启用或禁用语音播报：在 "开场白" 下方的 "语音播报" 区域，有一个开关按钮可控制语音播报的启用或禁用。点击"选择声音"或下拉按钮即可进入选择声音的界面。\n\n（5）调试与预览、保存：\n\n在右侧，可以看到一个调试区域。在调试区域的输入框中，输入自己的问题，查看AI生成的内容是否满足预期，根据自身需求，进一步调整以上提到的几个配置项。\n\n输入消息或换行：在底部的输入框下方有提示："Enter 发送，Shift+Enter 换行"。点击对话框最右侧按钮即可发送，发送按钮左侧是个【上传文件】按钮。\n\n查看历史版本：在界面的右上角，有一个 【历史版本】 按钮，点击可以查看和管理不同版本的设置。\n\n保存当前设置：在界面的右上角，【历史版本】 旁边有一个 【保存】 按钮。\n\n清空聊天记录：在右上角，保存按钮下方有一个 【清空聊天记录】 的按钮。\n\n保存智能应用：调试后，点击【保存】按钮，保存并应用当前配置。点击【对话】即可与智能应用进行对话。\n\n![descript](/api/system/img/683062588017e337cc62ce1d.png)',
    a: '',
    chunkIndex: 6
  },
  {
    _id: '6830625b584ecff133c1e096',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第一章 创建应用\n## 1.2 高阶应用\n### 1.2.1 高阶应用创建\n\n高阶应用适合复杂需求，能够提供更详细的界面和功能定制选项，生成更高级的应用。\n\n（1）路径：【零代码空间】--【创建高阶应用】\n\n![descript](/api/system/img/683062588017e337cc62ce1f.png)\n\n（2）基础信息填写：\n\n分别输入头像、应用名称、应用介绍等\n\n模板选择：目前提供两种模板选择，简易模板（一个极其简单的 AI 应用，您可以绑定知识库或工具）和问题分类 + 知识库（先对用户的问题进行分类，再根据不同类型问题，执行不同的操作）。\n\n本次案例主要以简易模板为介绍\n\n![descript](/api/system/img/683062588017e337cc62ce21.png)\n\n（3）配置组件：\n\n高阶应用生成后，映入眼帘的便是以下图片的内容，我将从左到右逐一进行介绍。\n\n![descript](/api/system/img/683062588017e337cc62ce23.png)\n\n系统配置：重点介绍以下内容，其他内容可以将鼠标移至"?"符号，即可看见详细说明\n\n文件上传：智能体是否支持上传本地文件\n\n语音输入：是否支持语音对话进行提问，以及自动发送\n\n![descript](/api/system/img/683062588017e337cc62ce25.png)\n\n【流程开始】：次组件无需过多担心，AI已经为我们创建了最合适的配置，直接使用即可。\n\n![descript](/api/system/img/683062588017e337cc62ce27.png)\n\n【问题分类】：用户问题前的设置参考【1.1.1AI创建】即可。\n\n![descript](/api/system/img/683062588017e337cc62ce29.png)\n\n问题分类：简单来说我们只需要看成两个部分 问题1：填写内容为 应用相关问题，与知识库进行连接，AI在回答问题时即可自动进入我们投入的文档进行问题的回复\n\n![descript](/api/system/img/683062588017e337cc62ce2b.png)\n\n问题2：与应用无关的问题，我们则需要连接指定问题答复点击--【回复的内容】 选择【手动输入】--输入你需要AI给的指定答复即可\n\n![descript](/api/system/img/683062588017e337cc62ce2d.png)\n\nAI对话：选择符合需求的大模型并设置回复上线，温度，回复轮数。可参考【1.1.1AI创建】提示词：写出你对AI的回复要求，引导模型进行更精准的回复\n\n![descript](/api/system/img/683062588017e337cc62ce2f.png)',
    a: '',
    chunkIndex: 7
  },
  {
    _id: '6830625b584ecff133c1e09d',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第二章 我的应用\n## 2.1 创建应用\n\n路径：【零代码空间】-【我的应用】-【+创建应用】，鼠标悬停在【+创建应用】时，会出现【简易应用】和【高阶应用】的下拉菜单，这一部分和第一章创建应用内容一致，可以参考第一章内容。\n\n![descript](/api/system/img/683062588017e337cc62ce31.png)',
    a: '',
    chunkIndex: 8
  },
  {
    _id: '6830625b8017e337cc62cecf',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第二章 我的应用\n## 2.2 修改应用\n\n路径：【零代码空间】-【我的应用】当鼠标悬停在应用右上角的三点时，会出现有五个选项的下拉菜单。\n\n（1）立即对话：\n\n点击这个选项会直接进入这个应用的使用界面，可以马上开始与这个应用进行交互或使用它的功能。\n\n![descript](/api/system/img/683062588017e337cc62ce33.png)\n\n背景知识引用: 输入框左侧有一个书本图标，点击配置背景知识。配置背景引用对AI对话的作用是提供上下文信息，帮助AI更准确地理解用户的需求和意图。\n\n![descript](/api/system/img/683062588017e337cc62ce35.png)\n\n使用工具/魔法棒: 输入框右侧有一个魔法棒图标（或类似工具的图标)，点击之后可以编辑快捷指令。\n\n![descript](/api/system/img/683062588017e337cc62ce37.png)\n\n上传文档：魔法棒图标右侧有一个类似于文档的图标，点击它可以上传文件。\n\n![descript](/api/system/img/683062588017e337cc62ce39.png)\n\n语音输入: 魔法棒图标旁边的麦克风图标，点击它可以使用语音输入功能，将你说的话转换为文字发送给助手。\n\n![descript](/api/system/img/683062588017e337cc62ce3b.png)\n\n（2）简易/高级配置：参考文档第一章1.1.2 AI配置调整章节。\n\n![descript](/api/system/img/683062588017e337cc62ce3d.png)\n\n![descript](/api/system/img/683062588017e337cc62ce3f.png)\n\n（3）复制：创建一个应用副本，方便基于现有的设置进行修改或备用。\n\n![descript](/api/system/img/683062588017e337cc62ce41.png)\n\n（4）编辑：可以修改应用的基本信息、简介等常用设置。\n\n![descript](/api/system/img/683062588017e337cc62ce43.png)\n\n（5）删除：可以将应用从应用列表或账户中移除。\n\n![descript](/api/system/img/683062588017e337cc62ce45.png)',
    a: '',
    chunkIndex: 9
  },
  {
    _id: '6830625b584ecff133c1e088',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第三章 知识库\n## 3\\. 1 创建知识库\n\n（1）路径：【零代码空间】-【知识库】-【+创建知识库】\n\n![descript](/api/system/img/683062588017e337cc62ce47.png)\n\n（2）填写创建知识库表单：输入知识库的名称、选择一个合适的索引模型和文件处理模型。再点击右下角【完成】即可。\n\n![descript](/api/system/img/683062588017e337cc62ce49.png)\n\n（3）创建知识库之后，点击该知识库进入的界面，以下是知识库界面各个功能的简单介绍。\n\n搜索测试：提供搜索测试功能，用户可以输入想查找的内容，可以测试能否正常检索到正确的知识片段。\n\n![descript](/api/system/img/683062588017e337cc62ce4b.png)\n\n新建文件夹：用户创建新的文件夹来管理数据集。\n\n![descript](/api/system/img/683062588017e337cc62ce4d.png)\n\n导入数据： 使用户能够将新的数据导入到数据集中。\n\n![descript](/api/system/img/683062588017e337cc62ce4f.png)\n\n索引模型：加速知识检索和匹配。\n\n文本理解模型：理解和分析文本内容。\n\n![descript](/api/system/img/683062588017e337cc62ce51.png)\n\n（4）导入数据：可以将外部文件上传到这个知识库中，作为应用的知识来源。\n\n路径：点击对应知识库进入知识库界面，点击右上角的【导入数据】按钮，即可将文档资料导入到知识库中。以下是导入界面各个功能的简单介绍。\n\n选择导入方式: 在顶部的"导入方式"区域，可以选择【自定义文本】、【网页链接】或【表格数据】，但当前界面展示的是【文件】方式。\n\n![descript](/api/system/img/683062588017e337cc62ce53.png)\n\n**注意：**为了保证知识库每次能够检索到完整内容和检索内容的准确性，在导入文档资料之前，请按格式处理文档内容；\n\n![descript](/api/system/img/683062588017e337cc62ce55.png)\n\n不超过5000字的文档，不需要做额外处理原文档。超过5000字的文档，需要将文档内的内容进行语义分割，并在每段分割后内容最前面添加一下文档标题、当前这段内容的简要概述和自定义分隔符。（请确保加上文档标题和简要概述信息后，仍不超过5000字）原文档。\n\n![descript](/api/system/img/683062588017e337cc62ce57.png)\n\n在导入的数据完成后，即可点击【确定治理】\n\n![descript](/api/system/img/683062588017e337cc62ce59.png)',
    a: '',
    chunkIndex: 10
  },
  {
    _id: '6830625b8017e337cc62cec8',
    datasetId: '67d3ab23b53c80a07dbd27ca',
    collectionId: '683062588017e337cc62ce66',
    q: '# 第三章 知识库\n## 3.2 修改知识库\n\n路径：【零代码空间】-【知识库】鼠标悬停在已经创建好的知识库，会出现三个下拉菜单。\n\n![descript](/api/system/img/683062588017e337cc62ce5b.png)\n\n（1）编辑信息: 点击这个选项可以修改知识库的基本信息，例如它的名称、介绍或图标。\n\n![descript](/api/system/img/683062588017e337cc62ce5d.png)\n\n（2）权限管理: 点击这个选项可以设置谁有权限访问、使用或管理这个知识库。\n\n![descript](/api/system/img/683062588017e337cc62ce5f.png)\n\n（3）删除: 点击这个选项可以将这个知识库彻底移除。\n\n![descript](/api/system/img/683062588017e337cc62ce61.png)',
    a: '',
    chunkIndex: 11
  }
];

// 定义目录项类型
interface TocItem {
  id: string;
  text: string;
  level: number;
  markdownFile?: string;
  children?: TocItem[];
  chunkIndexes?: number[]; // 添加 chunkIndexes 字段
}

// 预定义的目录结构
const predefinedToc: TocItem[] = [
  {
    id: 'create-app',
    text: '创建应用',
    level: 1,
    children: [
      {
        id: 'simple-app-ai',
        text: '简易应用-AI创建',
        level: 2,
        markdownFile: '创建应用-简易应用-AI创建 .markdown',
        chunkIndexes: [1, 2, 3, 5, 6] // 添加 5 和 6，包含 AI配置调整 的内容
      },
      {
        id: 'simple-app-standard',
        text: '简易应用-标准创建',
        level: 2,
        markdownFile: '创建应用-简易应用-标准创建.markdown',
        chunkIndexes: [4] // 对应的 chunkIndex
      },
      {
        id: 'advanced-app',
        text: '高阶应用',
        level: 2,
        markdownFile: '创建应用-高阶应用.markdown',
        chunkIndexes: [7] // 对应的 chunkIndex
      }
    ]
  },
  {
    id: 'my-apps',
    text: '我的应用',
    level: 1,
    children: [
      {
        id: 'my-apps-create',
        text: '创建应用',
        level: 2,
        markdownFile: '我的应用-创建应用.markdown',
        chunkIndexes: [8] // 对应的 chunkIndex
      },
      {
        id: 'my-apps-modify',
        text: '修改我的应用',
        level: 2,
        markdownFile: '我的应用-修改我的应用.markdown',
        chunkIndexes: [9] // 对应的 chunkIndex
      }
    ]
  },
  {
    id: 'knowledge-base',
    text: '知识库',
    level: 1,
    children: [
      {
        id: 'knowledge-create',
        text: '创建知识库',
        level: 2,
        markdownFile: '知识库-创建知识库.markdown',
        chunkIndexes: [10] // 对应的 chunkIndex
      },
      {
        id: 'knowledge-modify',
        text: '修改知识库',
        level: 2,
        markdownFile: '知识库-修改知识库.markdown',
        chunkIndexes: [11] // 对应的 chunkIndex
      }
    ]
  }
];

// 目录组件
const TableOfContentsWithState: React.FC<{
  toc: TocItem[];
  activeTocId: string;
  onClick: (item: TocItem) => void;
  expandedItems: Record<string, boolean>;
  setExpandedItems: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
}> = ({ toc, activeTocId, onClick, expandedItems, setExpandedItems }) => {
  // 更新颜色变量以符合图片样式
  const textColor = useColorModeValue('gray.700', 'gray.300');
  const activeTextColor = useColorModeValue('blue.600', 'blue.200');
  const hoverBgColor = useColorModeValue('gray.100', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const expandedIconColor = useColorModeValue('gray.400', 'gray.500');
  // 用于激活项背景色，防止在递归渲染中重复调用 Hook
  const activeBgColor = useColorModeValue('gray.100', 'gray.700');

  // 递归渲染多层级目录
  const renderItems = (items: TocItem[]) =>
    items.map((item) => {
      const hasChildren = !!item.children && item.children.length > 0;
      const isExpanded = expandedItems[item.id];
      const isActive = activeTocId === item.id;
      return (
        <React.Fragment key={item.id}>
          <Flex
            alignItems="center"
            py={3}
            px={4}
            pl={8 * (item.level - 1)}
            cursor="pointer"
            transition="all 0.2s"
            bg={isActive ? activeBgColor : 'transparent'}
            _hover={{ bg: hoverBgColor }}
            onClick={(e: React.MouseEvent<HTMLElement>) => {
              e.stopPropagation();
              if (hasChildren && !item.markdownFile) {
                // 如果有子项且没有markdown文件，只展开/折叠
                setExpandedItems((prev) => ({ ...prev, [item.id]: !prev[item.id] }));
              } else {
                // 否则触发点击事件
                onClick(item);
              }
            }}
          >
            <Text
              flex="1"
              fontSize={`${16 - (item.level - 1) * 2}px`}
              fontWeight="400"
              color={isActive ? activeTextColor : textColor}
            >
              {item.text}
            </Text>
            {hasChildren && (
              <Box
                as="span"
                fontSize="18px"
                cursor="pointer"
                transition="transform 0.2s"
                transform={isExpanded ? 'rotate(90deg)' : 'rotate(0)'}
                color={expandedIconColor}
              >
                ›
              </Box>
            )}
          </Flex>
          {hasChildren && isExpanded && renderItems(item.children!)}
        </React.Fragment>
      );
    });

  return (
    <Box as="nav" width="100%" pr={0} height="100%" overflowY="auto" bg="white">
      {/* 递归渲染多层级目录 */}
      {renderItems(toc)}
    </Box>
  );
};

export default function Guide() {
  const router = useRouter();
  const [activeTocId, setActiveTocId] = useState('simple-app-ai'); // 默认选中第一个子文档
  const [currentMarkdownFile, setCurrentMarkdownFile] = useState(
    '创建应用-简易应用-AI创建 .markdown'
  );
  const contentRef = useRef<HTMLDivElement>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const bgColor = useColorModeValue('white', 'gray.800');
  const tocBgColor = useColorModeValue('gray.50', 'gray.900');

  // 根据路由参数定位目录
  useEffect(() => {
    if (router.query.tab) {
      const tabMapping: Record<
        string,
        { parentId: string; defaultChildId: string; defaultMarkdownFile: string }
      > = {
        createApp: {
          parentId: 'create-app',
          defaultChildId: 'simple-app-ai',
          defaultMarkdownFile: '创建应用-简易应用-AI创建 .markdown'
        },
        myApp: {
          parentId: 'my-apps',
          defaultChildId: 'my-apps-create',
          defaultMarkdownFile: '我的应用-创建应用.markdown'
        },
        knowledge: {
          parentId: 'knowledge-base',
          defaultChildId: 'knowledge-create',
          defaultMarkdownFile: '知识库-创建知识库.markdown'
        }
      };

      const tab = router.query.tab as string;
      const mapping = tabMapping[tab];

      if (mapping) {
        // 如果有doc参数，优先使用doc参数定位
        if (router.query.doc) {
          const docId = router.query.doc as string;
          // 根据doc参数查找对应的文档
          const docMapping: Record<string, { id: string; markdownFile: string }> = {
            'simple-app-ai': {
              id: 'simple-app-ai',
              markdownFile: '创建应用-简易应用-AI创建 .markdown'
            },
            'simple-app-standard': {
              id: 'simple-app-standard',
              markdownFile: '创建应用-简易应用-标准创建.markdown'
            },
            'advanced-app': {
              id: 'advanced-app',
              markdownFile: '创建应用-高阶应用.markdown'
            },
            'my-apps-create': {
              id: 'my-apps-create',
              markdownFile: '我的应用-创建应用.markdown'
            },
            'my-apps-modify': {
              id: 'my-apps-modify',
              markdownFile: '我的应用-修改我的应用.markdown'
            },
            'knowledge-create': {
              id: 'knowledge-create',
              markdownFile: '知识库-创建知识库.markdown'
            },
            'knowledge-modify': {
              id: 'knowledge-modify',
              markdownFile: '知识库-修改知识库.markdown'
            }
          };

          const docInfo = docMapping[docId];
          if (docInfo) {
            setActiveTocId(docInfo.id);
            setCurrentMarkdownFile(docInfo.markdownFile);
          } else {
            // 如果doc参数无效，使用默认值
            setActiveTocId(mapping.defaultChildId);
            setCurrentMarkdownFile(mapping.defaultMarkdownFile);
          }
        } else {
          // 没有doc参数时使用默认值
          setActiveTocId(mapping.defaultChildId);
          setCurrentMarkdownFile(mapping.defaultMarkdownFile);
        }

        // 确保父级目录展开
        setExpandedItems((prev) => ({
          ...prev,
          [mapping.parentId]: true
        }));
      }
    }
  }, [router.query.tab, router.query.doc]);

  // 根据当前激活的目录项获取对应的 markdown 内容
  const getCurrentMarkdownContent = () => {
    const findItem = (items: TocItem[]): TocItem | null => {
      for (const item of items) {
        if (item.id === activeTocId) {
          return item;
        }
        if (item.children) {
          const found = findItem(item.children);
          if (found) return found;
        }
      }
      return null;
    };

    const activeItem = findItem(predefinedToc);
    if (activeItem && activeItem.chunkIndexes) {
      // 根据 chunkIndexes 获取对应的内容
      const contents = activeItem.chunkIndexes
        .map((index) => {
          const data = guideData.find((item) => item.chunkIndex === index);
          return data ? data.q : '';
        })
        .filter((content) => content !== '')
        .join('\n\n');

      // 替换图片地址前缀
      const processedContents = contents.replace(
        /!\[([^\]]*)\]\(\/api\/system\/img\/([^)]+)\)/g,
        '![$1](https://gpt-fast-pre.hwzxs.com/api/system/img/$2)'
      );

      return processedContents;
    }

    return '';
  };

  // 获取当前的 markdown 内容
  const markdownContent = getCurrentMarkdownContent();

  // 点击目录项时的处理
  const handleTocClick = (item: TocItem) => {
    if (item.markdownFile) {
      setActiveTocId(item.id);
      setCurrentMarkdownFile(item.markdownFile);
      // 滚动到页面顶部
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
    }

    // 在移动端时关闭抽屉
    if (isOpen) {
      onClose();
    }
  };

  // 获取当前激活的目录项信息
  const getActiveItem = (items: TocItem[]): TocItem | null => {
    for (const item of items) {
      if (item.id === activeTocId) {
        return item;
      }
      if (item.children) {
        const found = getActiveItem(item.children);
        if (found) return found;
      }
    }
    return null;
  };

  const activeItem = getActiveItem(predefinedToc);

  // 支持多层级折叠的目录树
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  // 初始化所有一级目录为展开状态
  useEffect(() => {
    const initial: Record<string, boolean> = {};
    predefinedToc.forEach((item) => {
      if (item.level === 1) {
        initial[item.id] = true;
      }
    });

    // 根据路由参数确保对应的父级展开
    if (router.query.tab) {
      const tabMapping: Record<string, string> = {
        createApp: 'create-app',
        myApp: 'my-apps',
        knowledge: 'knowledge-base'
      };
      const parentId = tabMapping[router.query.tab as string];
      if (parentId) {
        initial[parentId] = true;
      }
    }

    setExpandedItems(initial);
  }, [predefinedToc, router.query.tab]);

  return (
    <Box className="wdnmd" w="100%" minHeight="100vh">
      {/* 移动端显示汉堡菜单按钮 */}
      <Box display={{ base: 'block', md: 'none' }} position="fixed" top={5} left={5} zIndex={10}>
        <IconButton
          aria-label="Open menu"
          icon={<HamburgerIcon />}
          onClick={onOpen}
          colorScheme="gray"
          variant="solid"
          bg="gray.200"
          _hover={{ bg: 'gray.300' }}
          size="md"
        />
      </Box>

      {/* 移动端目录抽屉 */}
      <Drawer isOpen={isOpen} placement="left" onClose={onClose} size="xs">
        <DrawerOverlay />
        <DrawerContent maxWidth="85vw" bg={useColorModeValue('gray.50', 'gray.800')}>
          <DrawerCloseButton size="md" top={respDims(12)} right={respDims(12)} />
          <DrawerBody p={0} overflowY="auto">
            <TableOfContentsWithState
              toc={predefinedToc}
              activeTocId={activeTocId}
              onClick={handleTocClick}
              expandedItems={expandedItems}
              setExpandedItems={setExpandedItems}
            />
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* 桌面端布局 */}
      <Flex direction={{ base: 'column', md: 'row' }} h="100%" position="relative">
        {/* 左侧目录 */}
        <Box
          position="fixed"
          top="60px"
          bottom="0"
          left="0"
          height={{ md: '100vh' }}
          width={{ base: '100%', md: '280px', lg: '320px' }}
          display={{ base: 'none', md: 'block' }}
          overflowY="auto"
          pt={0}
          zIndex={2}
          boxShadow="md"
          bg="white"
          css={{
            height: 'calc(100vh - 60px)',
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(0, 0, 0, 0.1) transparent',
            '&::-webkit-scrollbar': {
              width: '4px'
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              borderRadius: '4px'
            }
          }}
        >
          <TableOfContentsWithState
            toc={predefinedToc}
            activeTocId={activeTocId}
            onClick={handleTocClick}
            expandedItems={expandedItems}
            setExpandedItems={setExpandedItems}
          />
        </Box>

        {/* 右侧内容 */}
        <Box
          flex="1"
          ml={{ base: 0, md: '280px', lg: '320px' }}
          p={{ base: 4, md: 10 }}
          pt={{ base: 16, md: 10 }}
          bg={bgColor}
          ref={contentRef}
          id="content-container"
          position={'relative'}
          overflowY="auto"
        >
          <Box
            maxWidth="800px"
            mx="auto"
            className="markdown-content"
            css={{
              // 为所有标题设置滚动边距，防止被固定导航栏遮挡
              '& h1, & h2, & h3, & h4, & h5, & h6': {
                scrollMarginTop: '80px'
              },
              '& h1': {
                fontSize: '32px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px',
                paddingBottom: '8px',
                borderBottom: '1px solid',
                borderColor: 'inherit'
              },
              '& h2': {
                fontSize: '24px',
                fontWeight: 'bold',
                marginTop: '32px',
                marginBottom: '16px',
                paddingBottom: '4px'
              },
              '& h3': {
                fontSize: '20px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px'
              },
              '& h4, & h5, & h6': {
                fontSize: '16px',
                fontWeight: 'bold',
                marginTop: '24px',
                marginBottom: '16px'
              },
              '& p': {
                marginBottom: '16px',
                lineHeight: 1.7
              },
              '& ul, & ol': {
                marginBottom: '16px',
                paddingLeft: '24px'
              },
              '& li': {
                marginBottom: '8px'
              },
              '& table': {
                width: '100%',
                marginBottom: '16px',
                borderCollapse: 'collapse'
              },
              '& th, & td': {
                border: '1px solid',
                borderColor: 'inherit',
                padding: '8px'
              },
              '& pre': {
                padding: '16px',
                overflow: 'auto',
                fontSize: '14px',
                backgroundColor: 'var(--chakra-colors-gray-100)',
                borderRadius: '4px',
                marginBottom: '16px'
              },
              '& code': {
                fontFamily: 'monospace',
                fontSize: '14px',
                padding: '0 4px',
                backgroundColor: 'var(--chakra-colors-gray-100)',
                borderRadius: '4px'
              },
              '& pre > code': {
                padding: 0,
                backgroundColor: 'transparent'
              },
              '& blockquote': {
                borderLeft: '4px solid',
                borderColor: 'var(--chakra-colors-gray-300)',
                paddingLeft: '16px',
                fontStyle: 'italic',
                marginBottom: '16px'
              },
              '& img': {
                maxWidth: '100%',
                height: 'auto',
                marginBottom: '16px'
              },
              '& hr': {
                margin: '24px 0',
                border: 0,
                borderBottom: '1px solid',
                borderColor: 'inherit'
              }
            }}
          >
            {/* 面包屑 */}
            <Flex
              className="breadcrumb-container"
              margin={'0'}
              alignItems="center"
              gap="2.5"
              mb="6"
              css={{
                ol: {
                  padding: '0',
                  margin: '0'
                }
              }}
            >
              <Breadcrumb fontSize="14px">
                <BreadcrumbItem>
                  <BreadcrumbLink
                    onClick={() => router.push('/officialHome')}
                    color="#3E4147"
                    _hover={{ color: 'blue.500', textDecoration: 'none' }}
                  >
                    首页
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem>
                  <BreadcrumbLink
                    color="#3E4147"
                    _hover={{ color: 'blue.500', textDecoration: 'none' }}
                  >
                    智能体建设
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem isCurrentPage>
                  <BreadcrumbLink
                    color="#3E4147"
                    fontWeight="500"
                    _hover={{ textDecoration: 'none' }}
                  >
                    {activeItem?.text || '平台介绍'}
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </Breadcrumb>
            </Flex>

            <Box
              css={{
                borderRadius: '50px',
                opacity: 0.56,
                background: 'linear-gradient(90deg, #D7E1FF 0%, #D1F8FF 95.19%)',
                boxShadow: '0px 4px 4px 0px rgba(104, 115, 160, 0.32)',
                width: '134px',
                height: '40px',
                position: 'absolute',
                top: '37px',
                right: '98px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                color: '#2468F2'
              }}
              onClick={() => router.push('/zeroCode?tab=my')}
            >
              创建你的Agent
            </Box>

            {/* 渲染 markdown 内容 */}
            {markdownContent && (
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkBreaks, remarkMath]}
                rehypePlugins={[rehypeRaw, rehypeKatex, rehypeSanitize]}
              >
                {markdownContent}
              </ReactMarkdown>
            )}
          </Box>
        </Box>
      </Flex>
    </Box>
  );
}
