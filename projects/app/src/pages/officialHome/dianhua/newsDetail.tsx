import { Box, Container, Text, VStack, HStack, IconButton, Skeleton } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';
import { useRouter } from 'next/router';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import React, { useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface NewsDetailData {
  title: string;
  content: string;
  isMarkdown?: boolean;
  date?: string;
  source?: string;
}

const NewsDetail = () => {
  const router = useRouter();
  const [newsData, setNewsData] = useState<NewsDetailData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadNewsContent = async () => {
      try {
        setLoading(true);

        // 如果有 link 参数，从 markdown 文件读取内容
        if (router.query.link) {
          const link = decodeURIComponent(router.query.link as string);
          const response = await fetch(link);

          if (!response.ok) {
            throw new Error('Failed to fetch markdown file');
          }

          const markdownContent = await response.text();

          // 解析 markdown 的 frontmatter（如果有的话）
          let title = decodeURIComponent(router.query.title as string) || '';
          let date = '';
          let source = '';
          let content = markdownContent;

          // 简单的 frontmatter 解析
          if (markdownContent.startsWith('---')) {
            const endIndex = markdownContent.indexOf('---', 3);
            if (endIndex !== -1) {
              const frontmatter = markdownContent.substring(3, endIndex);
              const contentWithoutFrontmatter = markdownContent.substring(endIndex + 3).trim();

              // 解析 frontmatter
              const lines = frontmatter.split('\n');
              lines.forEach((line) => {
                if (line.startsWith('title:')) {
                  title = line.replace('title:', '').trim();
                } else if (line.startsWith('date:')) {
                  date = line.replace('date:', '').trim();
                } else if (line.startsWith('source:')) {
                  source = line.replace('source:', '').trim();
                }
              });

              content = contentWithoutFrontmatter;
            }
          }

          setNewsData({
            title,
            content,
            isMarkdown: true,
            date,
            source
          });
        }
        // 否则从路由参数获取内容
        else if (router.query.title && router.query.content) {
          setNewsData({
            title: decodeURIComponent(router.query.title as string),
            content: decodeURIComponent(router.query.content as string),
            isMarkdown: router.query.isMarkdown === 'true'
          });
        }
      } catch (error) {
        console.error('Error loading news content:', error);
        setNewsData(null);
      } finally {
        setLoading(false);
      }
    };

    if (router.isReady) {
      loadNewsContent();
    }
  }, [router.isReady, router.query]);

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <Container
        maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)}
        py={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
      >
        <VStack spacing={4} align="stretch">
          <Skeleton height="40px" />
          <Skeleton height="20px" />
          <Skeleton height="300px" />
        </VStack>
      </Container>
    );
  }

  if (!newsData) {
    return (
      <Container
        maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)}
        py={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
      >
        <Text>未找到新闻内容</Text>
      </Container>
    );
  }

  return (
    <Box minH="100vh" bg="#F5F7FA">
      <Container
        maxW={respDims(900, GLOBAL_DIMS_MIN_SCALE)}
        py={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
      >
        <VStack spacing={respDims(30, GLOBAL_DIMS_MIN_SCALE)} align="stretch">
          {/* 返回按钮 */}
          <HStack>
            <IconButton
              aria-label="返回"
              icon={<ChevronLeftIcon w={6} h={6} />}
              onClick={handleBack}
              variant="ghost"
              _hover={{ bg: '#E6E8EB' }}
            />
            <Text fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)} color="#5F7288">
              返回
            </Text>
          </HStack>

          {/* 文章内容区 */}
          <Box
            bg="white"
            borderRadius={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
            p={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
            boxShadow="0px 2px 8px rgba(0, 0, 0, 0.06)"
          >
            {/* 标题 */}
            <Text
              fontSize={respDims(32, GLOBAL_DIMS_MIN_SCALE)}
              fontWeight="bold"
              color="#091221"
              mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
              lineHeight="1.4"
            >
              {newsData.title}
            </Text>

            {/* 发布信息 */}
            <HStack
              spacing={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
              mb={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
            >
              <Text fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)} color="#5F7288">
                发布时间：{newsData.date || new Date().toLocaleDateString('zh-CN')}
              </Text>
              <Text fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)} color="#5F7288">
                来源：{newsData.source || '电化教育馆'}
              </Text>
            </HStack>

            {/* 内容区 */}
            <Box
              className="news-content"
              sx={{
                '& h1': {
                  fontSize: respDims(28, GLOBAL_DIMS_MIN_SCALE),
                  fontWeight: 'bold',
                  color: '#091221',
                  mt: respDims(35, GLOBAL_DIMS_MIN_SCALE),
                  mb: respDims(20, GLOBAL_DIMS_MIN_SCALE)
                },
                '& h2': {
                  fontSize: respDims(24, GLOBAL_DIMS_MIN_SCALE),
                  fontWeight: 'bold',
                  color: '#091221',
                  mt: respDims(30, GLOBAL_DIMS_MIN_SCALE),
                  mb: respDims(15, GLOBAL_DIMS_MIN_SCALE)
                },
                '& h3': {
                  fontSize: respDims(20, GLOBAL_DIMS_MIN_SCALE),
                  fontWeight: '600',
                  color: '#091221',
                  mt: respDims(25, GLOBAL_DIMS_MIN_SCALE),
                  mb: respDims(12, GLOBAL_DIMS_MIN_SCALE)
                },
                '& p': {
                  fontSize: respDims(16, GLOBAL_DIMS_MIN_SCALE),
                  color: '#3D3D3D',
                  lineHeight: '1.8',
                  mb: respDims(15, GLOBAL_DIMS_MIN_SCALE)
                },
                '& ul, & ol': {
                  pl: respDims(20, GLOBAL_DIMS_MIN_SCALE),
                  mb: respDims(15, GLOBAL_DIMS_MIN_SCALE)
                },
                '& li': {
                  fontSize: respDims(16, GLOBAL_DIMS_MIN_SCALE),
                  color: '#3D3D3D',
                  lineHeight: '1.8',
                  mb: respDims(8, GLOBAL_DIMS_MIN_SCALE)
                },
                '& strong': {
                  fontWeight: 'bold',
                  color: '#091221'
                },
                '& blockquote': {
                  borderLeft: '4px solid #2572F4',
                  pl: respDims(20, GLOBAL_DIMS_MIN_SCALE),
                  ml: 0,
                  my: respDims(20, GLOBAL_DIMS_MIN_SCALE),
                  color: '#5F7288',
                  fontStyle: 'italic'
                },
                '& img': {
                  maxWidth: '100%',
                  height: 'auto',
                  my: respDims(20, GLOBAL_DIMS_MIN_SCALE),
                  borderRadius: respDims(8, GLOBAL_DIMS_MIN_SCALE)
                },
                '& code': {
                  backgroundColor: '#f5f5f5',
                  padding: '2px 4px',
                  borderRadius: '3px',
                  fontSize: '0.9em',
                  fontFamily: 'monospace'
                },
                '& pre': {
                  backgroundColor: '#f5f5f5',
                  padding: respDims(16, GLOBAL_DIMS_MIN_SCALE),
                  borderRadius: respDims(8, GLOBAL_DIMS_MIN_SCALE),
                  overflow: 'auto',
                  mb: respDims(15, GLOBAL_DIMS_MIN_SCALE),
                  '& code': {
                    backgroundColor: 'transparent',
                    padding: 0
                  }
                }
              }}
            >
              {newsData.isMarkdown ? (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    // 自定义图片组件，处理相对路径
                    img: ({ node, ...props }) => {
                      let src = props.src || '';

                      // 如果是相对路径，需要处理成正确的路径
                      if (src.startsWith('./') && router.query.link) {
                        const link = decodeURIComponent(router.query.link as string);
                        const basePath = link.substring(0, link.lastIndexOf('/'));
                        src = src.replace('./', basePath + '/');
                      }

                      return <img {...props} src={src} />;
                    }
                  }}
                >
                  {newsData.content}
                </ReactMarkdown>
              ) : (
                <Text
                  fontSize={respDims(16, GLOBAL_DIMS_MIN_SCALE)}
                  color="#3D3D3D"
                  lineHeight="1.8"
                  whiteSpace="pre-wrap"
                >
                  {newsData.content}
                </Text>
              )}
            </Box>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default NewsDetail;
