import {
  Box,
  Flex,
  Image,
  Text,
  But<PERSON>,
  Container,
  SimpleGrid,
  VStack,
  HStack
} from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';
import SvgIcon from '@/components/SvgIcon';
import router, { useRouter } from 'next/router';
import React, { useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAppStore } from '@/store/useAppStore';
import type { AppListItemType } from '@/types/api/app';
import { newsData } from '@/data/officialHome/newsData';

interface Question {
  title: string;
  content: string;
  link?: string;
}

interface KnowledgeCard {
  title: string;
  bgColor: string;
  questions: Question[];
  count: number;
  textColor?: string;
  link?: string;
}

interface Agent {
  id: string;
  name: string;
  users: string;
  desc: string;
  tags: string[];
  bgColor: string;
  index: number;
  textColor?: string;
  avatarUrl: string;
}

interface NewsCardProps {
  id: string;
  title: string;
  summary: string;
  content: string;
  isMarkdown?: boolean;
  link: string;
}

interface KnowledgeCardProps extends KnowledgeCard {}

interface AgentCardProps extends Agent {}

// 知识问答数据
const knowledgeCards: KnowledgeCard[] = [
  {
    title: '创建应用',
    bgColor: 'blue.50',
    textColor: '#2572F4',
    questions: [
      {
        title: '如何使用AI快速创建应用？',
        content:
          'AI创建模式让您只需描述需求，系统就会自动生成应用配置。包括自动配置AI模型、生成提示词、设置知识库等，大大简化了应用创建流程...',
        link: '/officialHome/dianhua/guide?tab=createApp&doc=simple-app-ai'
      },
      {
        title: '简易应用和高阶应用有什么区别？',
        content:
          '简易应用适合快速开发，通过配置即可完成；高阶应用支持工作流编排，可以实现复杂的业务逻辑，如问题分类、条件判断、多步骤处理等...',
        link: '/officialHome/dianhua/guide?tab=createApp&doc=advanced-app'
      }
    ],
    count: 3, // 3个子文档：简易应用-AI创建、简易应用-标准创建、高阶应用
    link: '/officialHome/dianhua/guide?tab=createApp'
  },
  {
    title: '我的应用',
    bgColor: 'green.50',
    questions: [
      {
        title: '如何管理和修改已创建的应用？',
        content:
          '在"我的应用"中，您可以查看所有创建的应用，进行立即对话、高级编辑、复制、基本信息编辑和删除等操作。支持版本管理和配置调整...',
        link: '/officialHome/dianhua/guide?tab=myApp&doc=my-apps-modify'
      },
      {
        title: '应用的高级编辑功能包括哪些？',
        content:
          '高级编辑提供工作流编排界面，可以修改应用的底层逻辑、添加组件、设置条件分支、配置知识库搜索等，实现更复杂的功能需求...',
        link: '/officialHome/dianhua/guide?tab=myApp&doc=my-apps-modify'
      }
    ],
    count: 2, // 2个子文档：创建应用、修改我的应用
    textColor: '#20A550',
    link: '/officialHome/dianhua/guide?tab=myApp'
  },
  {
    title: '知识库',
    bgColor: '#FFFBEB',
    questions: [
      {
        title: '如何创建和维护专属知识库？',
        content:
          '知识库支持文档上传、网页导入、自定义文本等多种方式。建议将长文档按语义分割，每段不超过3000字，并添加标题和概述以提高检索准确性...',
        link: '/officialHome/dianhua/guide?tab=knowledge&doc=knowledge-create'
      },
      {
        title: '知识库的搜索配置如何优化？',
        content:
          '可以选择语义检索、全文检索或混合检索模式，调整引用上限和相关度阈值。建议开启问题优化功能，让系统自动优化搜索语句以获得更准确的结果...',
        link: '/officialHome/dianhua/guide?tab=knowledge&doc=knowledge-modify'
      }
    ],
    count: 2, // 2个子文档：创建知识库、修改知识库
    textColor: '#FF8D0E',
    link: '/officialHome/dianhua/guide?tab=knowledge'
  }
];

// 原始智能体数据接口
interface RawAgent extends AppListItemType {
  name: string;
  intro: string;
  avatarUrl: string;
}
// 将原始数据转换为Agent类型
const createAgentData = (rawAgent: RawAgent, index: number, type: string[]): Agent => {
  let bgColor = '#EDF0FD';
  let textColor: string | undefined = undefined;

  if (type[0] === '综合') {
    bgColor =
      index === 0 ? '#F73131' : index === 1 ? '#FF8C1A' : index === 2 ? '#FFBF00' : '#EDF0FD';
  } else if (type[0] === '教师热门') {
    bgColor =
      index === 0 ? '#4087FD' : index === 1 ? '#32D5FE' : index === 2 ? '#9240FD' : '#EDF0FD';
  } else {
    bgColor =
      index === 0 ? '#20A550' : index === 1 ? '#FF8D0E' : index === 2 ? '#8E44AD' : '#EDF0FD';
  }

  if (index === 3) {
    textColor = '#848691';
  }

  return {
    id: rawAgent.id.toString(),
    name: rawAgent.name,
    users: `${Math.floor(Math.random() * 5000 + 1000)}`,
    desc: rawAgent.intro,
    tags: type,
    bgColor,
    textColor,
    avatarUrl: rawAgent.avatarUrl,
    index
  };
};
// 标签页类型
type TabKey = 'comprehensive' | 'teacherHot' | 'studentHot';

const OfficialHome = () => {
  const router = useRouter();
  // 添加状态用于跟踪当前选中的标签
  const [activeTab, setActiveTab] = React.useState<TabKey>('comprehensive');
  const { myAppsOfficialHome, loadMyAppsOfficialHome } = useAppStore();

  // 添加状态用于存储当前显示的智能体
  const [displayedAgents, setDisplayedAgents] = React.useState<Agent[]>([]);
  const { isFetching, refetch: loadAppsRefetch } = useQuery(
    ['loadAppsOfficialHome'],
    () => loadMyAppsOfficialHome(true),
    {
      refetchOnMount: true
    }
  );
  // 处理标签点击事件
  const handleTabClick = (tab: TabKey) => {
    setActiveTab(tab);
    setDisplayedAgents(agentGroups[tab]);
  };
  console.log(myAppsOfficialHome, 'myAppsOfficialHome');

  const agentGroups: Record<TabKey, Agent[]> = useMemo(() => {
    return {
      comprehensive: myAppsOfficialHome
        .slice(0, 4)
        .map((app, index) => createAgentData(app, index, ['综合'])),
      teacherHot: myAppsOfficialHome
        .slice(4, 8)
        .map((app, index) => createAgentData(app, index, ['教师热门'])),
      studentHot: myAppsOfficialHome
        .slice(8, 12)
        .map((app, index) => createAgentData(app, index, ['学生热门']))
    };
  }, [myAppsOfficialHome]);

  useEffect(() => {
    setDisplayedAgents(agentGroups[activeTab]);
  }, [myAppsOfficialHome, activeTab, agentGroups]);

  return (
    <Box minH="100vh">
      <Flex w="100%" flexDir="column">
        {/* Banner区域 */}
        <Box position="relative" mb={respDims(50, GLOBAL_DIMS_MIN_SCALE)}>
          <Box
            position="absolute"
            w="704px"
            h="144px"
            top="225px"
            left="0"
            bg="#ABB4FF78"
            borderRadius="352px/72px"
            filter="blur(60.1px)"
            opacity="0.53"
          />
          {/* 欢迎语区域 */}
          <Container maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)} position="relative">
            <Flex
              flexDir="column"
              alignItems="flex-start"
              pl={respDims(100, GLOBAL_DIMS_MIN_SCALE)}
              pt={respDims(134, GLOBAL_DIMS_MIN_SCALE)}
            >
              <Flex gap="15px" mb="30px">
                <Text
                  fontFamily="Almarai-Bold"
                  fontSize={respDims(52.8, GLOBAL_DIMS_MIN_SCALE)}
                  color="#091221"
                  lineHeight="62.4px"
                  fontWeight="bold"
                >
                  AI 赋能教育,
                </Text>
                <Text
                  fontFamily="Almarai-Bold"
                  fontSize={respDims(52.8, GLOBAL_DIMS_MIN_SCALE)}
                  bgGradient="linear(90deg, #2468F2 0%, #27ACFF 100%)"
                  bgClip="text"
                  lineHeight="62.4px"
                  fontWeight="bold"
                >
                  智慧引领未来
                </Text>
              </Flex>
              <Text fontSize={respDims(14.4, GLOBAL_DIMS_MIN_SCALE)} color="#091221B2" mb="30px">
                电化教育馆智慧教育平台，为教师、学生和家长提供全方位的智能教育服务
              </Text>
              <Button
                onClick={() => router.push('/zeroCode?tab=my')}
                w={respDims(226, GLOBAL_DIMS_MIN_SCALE)}
                h={respDims(48, GLOBAL_DIMS_MIN_SCALE)}
                bgGradient="linear-gradient(268deg, #32D5FE 8%, #4087FD 47.43%, #9240FD 96.63%)"
                color="white"
                fontWeight="bold"
                fontSize={respDims(16.8, GLOBAL_DIMS_MIN_SCALE)}
                borderRadius={respDims(9.6, GLOBAL_DIMS_MIN_SCALE)}
                _hover={{
                  bgGradient: 'linear-gradient(268deg, #32D5FE 8%, #4087FD 47.43%, #9240FD 96.63%)'
                }}
              >
                快来定义你的智能体
              </Button>
            </Flex>
          </Container>
        </Box>
        {/* 热门资讯区域 */}
        <Container maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)} position="relative">
          {/* 热门资讯标题 */}
          <Flex alignItems="center" gap="6px" mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)}>
            <SvgIcon
              name="hot"
              w={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
              h={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
            />
            <Text
              fontFamily="YouSheBiaoTiHei-Regular"
              fontSize={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
              color="#1F2329"
              fontWeight="bold"
            >
              热门资讯
            </Text>
          </Flex>

          {/* 新闻卡片 */}
          <SimpleGrid
            columns={{ base: 1, sm: 2, md: 3 }}
            spacing={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            mb={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
          >
            {newsData.map((news) => (
              <NewsCard
                key={news.id}
                id={news.id}
                title={news.title}
                summary={news.summary}
                content={news.content}
                isMarkdown={news.isMarkdown}
                link={news.link}
              />
            ))}
          </SimpleGrid>
        </Container>

        {/* 主体内容区域 */}
        <Container
          maxW={respDims(1440, GLOBAL_DIMS_MIN_SCALE)}
          py={respDims(40, GLOBAL_DIMS_MIN_SCALE)}
        >
          {/* 知识问答区域 */}
          <Box mb={respDims(40, GLOBAL_DIMS_MIN_SCALE)}>
            <Flex mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)} alignItems="center" gap="6px">
              <SvgIcon
                name="hot"
                w={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
                h={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
              />
              <Text
                fontFamily="YouSheBiaoTiHei-Regular"
                fontSize={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
                color="#1F2329"
                fontWeight="bold"
              >
                AI知识问答
              </Text>
            </Flex>

            <SimpleGrid
              columns={{ base: 1, sm: 2, md: 3 }}
              spacing={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            >
              {knowledgeCards.map((card, index) => (
                <KnowledgeCard key={index} {...card} />
              ))}
            </SimpleGrid>
          </Box>

          {/* 热门智能体 */}
          <Box>
            <Flex mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)} alignItems="center" gap="6px">
              <SvgIcon
                name="hot"
                w={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
                h={respDims(22, GLOBAL_DIMS_MIN_SCALE)}
              />
              <Text
                fontFamily="YouSheBiaoTiHei-Regular"
                fontSize={respDims(24, GLOBAL_DIMS_MIN_SCALE)}
                color="#1F2329"
                fontWeight="bold"
              >
                热门智能体
              </Text>
            </Flex>

            <Flex justifyContent="space-between">
              <Flex
                mb={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
                gap={respDims(10, GLOBAL_DIMS_MIN_SCALE)}
              >
                <Button
                  variant={activeTab === 'comprehensive' ? 'solid' : 'ghost'}
                  bg={activeTab === 'comprehensive' ? '#091221' : '#0912210F'}
                  color={activeTab === 'comprehensive' ? 'white' : '#091221'}
                  h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50px"
                  onClick={() => handleTabClick('comprehensive')}
                >
                  综合
                </Button>
                <Button
                  variant={activeTab === 'teacherHot' ? 'solid' : 'ghost'}
                  bg={activeTab === 'teacherHot' ? '#091221' : '#0912210F'}
                  color={activeTab === 'teacherHot' ? 'white' : '#091221'}
                  h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50px"
                  onClick={() => handleTabClick('teacherHot')}
                >
                  教师热门
                </Button>
                <Button
                  variant={activeTab === 'studentHot' ? 'solid' : 'ghost'}
                  bg={activeTab === 'studentHot' ? '#091221' : '#0912210F'}
                  color={activeTab === 'studentHot' ? 'white' : '#091221'}
                  h={respDims(30, GLOBAL_DIMS_MIN_SCALE)}
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  borderRadius="50px"
                  onClick={() => handleTabClick('studentHot')}
                >
                  学生热门
                </Button>
              </Flex>
              <Box>
                <Text
                  color="#2572F4"
                  fontSize={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
                  cursor="pointer"
                  onClick={() => {
                    router.push('/officialHome/dianhua/agentCenter');
                  }}
                >
                  前往智能体广场
                </Text>
              </Box>
            </Flex>

            <SimpleGrid
              columns={{ base: 1, sm: 2, md: 4 }}
              spacing={respDims(20, GLOBAL_DIMS_MIN_SCALE)}
            >
              {displayedAgents.map((agent, index) => (
                <AgentCard key={agent.id} {...agent} index={index} />
              ))}
            </SimpleGrid>
          </Box>
        </Container>
      </Flex>
    </Box>
  );
};

// 新闻卡片组件
const NewsCard: React.FC<NewsCardProps> = ({
  id,
  title,
  summary,
  content,
  isMarkdown = true,
  link
}) => {
  const router = useRouter();

  const handleViewDetail = () => {
    // 跳转到详情页，传递标题和内
    window.open(link, '_blank');
  };

  return (
    <Box
      w="100%"
      h={{ base: 'auto', md: '140px' }}
      bg="white"
      borderRadius="18px"
      p={{ base: '12px 16px', md: '17px 24px' }}
      boxShadow="0px 4px 14.8px #C3DFFD61"
    >
      <VStack align="flex-start" spacing="9px">
        <VStack align="flex-start" spacing="4px">
          <Text fontSize="16px" fontWeight="600" color="#091221" noOfLines={1}>
            {title}
          </Text>
          <Text fontSize="14px" color="#091221B2" noOfLines={2}>
            {summary}
          </Text>
        </VStack>
        <HStack spacing="5px" cursor="pointer" onClick={() => handleViewDetail()}>
          <Text fontSize="14px" color="#2572F4">
            查看详情
          </Text>
          <SvgIcon
            transform="rotate(-45deg)"
            name="navIcon"
            color="#2572F4"
            w={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            h={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
          />
        </HStack>
      </VStack>
    </Box>
  );
};

// 知识卡片组件
const KnowledgeCard: React.FC<KnowledgeCardProps> = ({
  title,
  textColor,
  bgColor,
  questions,
  count,
  link
}) => {
  const router = useRouter();

  return (
    <Box
      w="100%"
      h={{ base: 'auto', md: '350px' }}
      bg={bgColor}
      borderRadius="16.8px"
      border="2px solid white"
      boxShadow="inset 0px 2px 0px #FFFFFF, 0px 2.4px 7.2px #0912210A"
      backdropFilter="blur(2px)"
      p={{ base: '15px', md: '20px' }}
    >
      <Box mb="20px">
        <Box position="relative" display="inline-block">
          <Text fontSize="18px" fontWeight="600" color="#091221" mb="4px">
            {title}
          </Text>
          <Box
            position="absolute"
            bottom="0"
            left="0"
            w="100%"
            h="12px"
            bg="#AAECFF"
            mixBlendMode="multiply"
          />
        </Box>
      </Box>

      <VStack spacing="10px">
        {questions.map((q, index) => (
          <Box
            key={index}
            w="100%"
            bg="white"
            borderRadius="12px"
            p="16px"
            boxShadow="0px 1px 4.8px #0912210A"
            cursor={q.link ? 'pointer' : 'default'}
            transition="all 0.2s"
            _hover={
              q.link
                ? {
                    transform: 'translateY(-2px)',
                    boxShadow: '0px 4px 12px #0912211A'
                  }
                : {}
            }
            onClick={() => {
              if (q.link) {
                router.push(q.link);
              }
            }}
          >
            <Text fontSize="15px" fontWeight="500" color="#243E74" mb="6px">
              {q.title}
            </Text>
            <Text fontSize="13px" color="#091221B2" noOfLines={2}>
              {q.content}
            </Text>
          </Box>
        ))}
      </VStack>

      <HStack justify="space-between" mt="20px">
        <Text fontSize="13px" color="#7F7F7F">
          共{count}个问答
        </Text>
        <HStack spacing="5px" cursor="pointer">
          <Text
            fontSize="14px"
            color={textColor}
            onClick={() => {
              router.push(link || '/');
            }}
          >
            查看更多
          </Text>
          <SvgIcon
            name="navIcon"
            w={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            h={respDims(14, GLOBAL_DIMS_MIN_SCALE)}
            color={textColor}
          />
        </HStack>
      </HStack>
    </Box>
  );
};

// 智能体卡片组件
const AgentCard: React.FC<Agent> = ({
  id,
  index,
  name,
  users,
  desc,
  tags,
  bgColor,
  textColor = 'white',
  avatarUrl
}) => {
  const router = useRouter();

  return (
    <Box
      w="100%"
      h={{ base: 'auto', md: '321px' }}
      bg="white"
      borderRadius="12px"
      boxShadow="0px 6px 18px #C3DFFD61"
      position="relative"
    >
      <Box
        position="absolute"
        top="0"
        left="0"
        w="33px"
        h="35px"
        bg={bgColor}
        borderRadius="12px 0px 12px 0px"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Text
          fontFamily="YouSheBiaoTiHei-Regular"
          fontSize="24px"
          fontWeight="bold"
          color={textColor}
        >
          {index + 1}
        </Text>
      </Box>

      <VStack spacing={{ base: '10px', md: '15px' }} pt={{ base: '15px', md: '21px' }}>
        <Image
          src={avatarUrl}
          alt={name}
          w="80px"
          h="80px"
          borderRadius="50%"
          border="1px solid #EAEAEA"
        />

        <Text fontSize="15px" fontWeight="500" color="#1F2329">
          {name}
        </Text>

        <Text fontSize="12px" color="#5F7288">
          （{users} 人使用）
        </Text>

        <Text fontSize="13px" color="#5F7288" textAlign="center" px="20px" noOfLines={2}>
          {desc}
        </Text>

        <HStack spacing="12px">
          {tags.map((tag, index) => (
            <Box
              key={index}
              px="10px"
              h="24px"
              bg="#0912210F"
              borderRadius="4px"
              display="flex"
              alignItems="center"
            >
              <Text fontSize="12px" color="#091221">
                {tag}
              </Text>
            </Box>
          ))}
        </HStack>

        <Button
          w={{ base: '90%', md: '245px' }}
          h="32px"
          bg="#2572F4"
          color="white"
          fontSize="13px"
          _hover={{
            bgGradient: '#2572F4'
          }}
          borderRadius="8px"
          onClick={() => router.push(`/home?appId=${id}`)}
        >
          立即使用
        </Button>
      </VStack>
    </Box>
  );
};

export default OfficialHome;
