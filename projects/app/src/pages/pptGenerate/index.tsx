import { Box, Center, Spinner } from '@chakra-ui/react';
import { useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { thirdPPtCode } from '@/api/pptGenerate';
import { serviceSideProps } from '@/utils/i18n';

const PptGenerate = ({ ...props }) => {
  const iFrameRef = useRef<HTMLIFrameElement | null>(null);

  const {
    data: pptCode,
    isLoading,
    error
  } = useQuery({
    queryKey: ['pptCode'],
    queryFn: thirdPPtCode,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2
  });

  if (isLoading) {
    return (
      <Center w="100%" h="100%">
        <Spinner size="xl" color="primary.500" />
      </Center>
    );
  }

  if (error) {
    return <Box>Error: {(error as Error).message}</Box>;
  }

  const iframeSrc = `https://aippt.huayungpt.com/ai-beta${pptCode ? `?code=${pptCode}` : ''}`;

  return (
    <Box
      w="100%"
      h="100%"
      {...props}
      ref={iFrameRef}
      as="iframe"
      css={{
        '.AILayout-bg': {
          background: 'none!important'
        }
      }}
      src={iframeSrc}
      onLoad={() => {}}
    />
  );
};

export default PptGenerate;

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}
