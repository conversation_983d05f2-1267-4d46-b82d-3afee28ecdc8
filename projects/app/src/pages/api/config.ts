import type { NextApiRequest, NextApiResponse } from 'next';
import type { SystemConfigType } from '@/types/api/system';
type ResponseData = {
  message: string;
  data?: SystemConfigType;
};

// 获取环境变量
export default function handler(req: NextApiRequest, res: NextApiResponse<ResponseData>) {
  // 根据HTTP方法处理不同请求
  if (req.method === 'GET') {
    const config: Record<string, string> = {};

    Object.keys(process.env).forEach((key) => {
      if (key.startsWith('SYSTEM_CONFIG')) {
        // 去掉SYSTEM_CONFIG，转驼峰
        config[
          key
            .replace('SYSTEM_CONFIG_', '')
            .toLowerCase()
            .split('_')
            .map((word, index) =>
              index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
            )
            .join('')
        ] = process.env[key] || '';
      }
    });
    res.status(200).json({ message: '获取成功', data: config as SystemConfigType });
  } else {
    // 处理不支持的方法
    res.setHeader('Allow', ['GET']);
    res.status(405).json({ message: `不支持 ${req.method} 方法` });
  }
}
