import { respDims } from '@/utils/chakra';
import { Box, Flex } from '@chakra-ui/react';
import { Toast } from '@/utils/ui/toast';
import { Input, Upload, Button as AtdButton } from 'antd';
import type { UploadProps } from 'antd';
import { Button } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
const { Dragger } = Upload;
import { updateStudentList, deleteStudentList } from '@/api/ai/correction/task';
import { ICompositionFiles } from '@/types/api/ai/correction/task';
import styles from './index.module.scss';
import Loading from '@/components/Loading';
import React from 'react';
import { AppNewCorrectionProps } from '@/types/compositionCorrection/interface';
// import { isTablet } from '@/utils/mobile';

interface studentListType {
  id?: number;
  studentId?: string;
  title: string;
  content?: any;
  type?: 'img' | 'pdf' | 'text';
  parseResult?: string;
  parseStatus?: 'parsing' | 'in-progress' | 'completed';
  compositionFiles?: ICompositionFiles[];
  contents?: any;
}

interface MemoizedAppNewCorrectionProps extends AppNewCorrectionProps {
  onUpload: (info: any, index?: number) => void;
  onUploadCreate: (info: { file: any; fileList: any[] }) => void;
  studentId?: string;
  onTitleChange: (title: string) => void;
  cardWidth: string;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  setUploadIndex: (index: number | null) => void;
  setIsManualInputOpen: (isOpen: boolean) => void;
  handleSelectFromCloud: (index: number) => void;
  beforeUpload: (file: any) => any;
  studentList: studentListType[];
  setStudentList: (list: studentListType[]) => void;
  uploadIndex: number | null;
  isAdd: boolean;
}

// 修改为使用 forwardRef 和正确的类型
export const AppNewCorrection = forwardRef<
  { openFileDialog: () => void },
  MemoizedAppNewCorrectionProps
>(
  (
    {
      studentName,
      index,
      uploadType,
      studentListIndex,
      parseResult,
      parseStatus,
      onUpload,
      onUploadCreate,
      studentId,
      onTitleChange,
      cardWidth,
      loading,
      setLoading,
      setUploadIndex,
      setIsManualInputOpen,
      handleSelectFromCloud,
      beforeUpload,
      studentList,
      setStudentList,
      uploadIndex,
      isAdd
    },
    ref
  ) => {
    const [isEditing, setIsEditing] = useState(false);
    const [editTitle, setEditTitle] = useState('');
    const [fileList, setFileList] = useState<any[]>([]);
    const uploadRef = React.useRef<HTMLInputElement>(null);
    const draggerRef = React.useRef<any>(null);
    const [isTablet, setIsTablet] = useState(false);

    useEffect(() => {
      const checkIsTablet = () => {
        const width = window.innerWidth;
        setIsTablet(width >= 800 && width < 1200);
      };

      checkIsTablet();
      window.addEventListener('resize', checkIsTablet);

      return () => {
        window.removeEventListener('resize', checkIsTablet);
      };
    }, []);

    // 暴露打开文件对话框的方法
    useImperativeHandle(ref, () => ({
      openFileDialog: () => {
        if (uploadRef.current) {
          uploadRef.current.click();
        }
      }
    }));

    // 修改上传处理函数,通过props调用父组件方法
    const handleUpload = (info: any, index?: number) => {
      onUpload(info, index);
    };

    const handleUploadCreate = (info: { file: any; fileList: any[] }) => {
      onUploadCreate(info);
    };

    const handleDoubleClick = () => {
      if (studentName) {
        setIsEditing(true);
        setEditTitle(studentName);
      }
    };

    const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setEditTitle(e.target.value);
    };

    const handleTitleBlur = () => {
      setIsEditing(false);
      if (editTitle !== studentName && editTitle.trim()) {
        if (studentList[studentListIndex]?.studentId) {
          updateStudentList([
            { id: studentList[studentListIndex].studentId, name: editTitle }
          ]).then(() => {
            onTitleChange(editTitle);
          });
        }
      }
    };

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        handleTitleBlur();
      }
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (!files) return;

      // 转换为 antd Upload 需要的格式
      const fileList = Array.from(files).map((file) => ({
        originFileObj: file,
        status: 'done',
        name: file.name,
        type: file.type,
        uid: Math.random().toString()
      }));

      // 使用现有的上传处理逻辑
      if (uploadType === 'replace' || uploadIndex !== null) {
        fileList.forEach((file) => {
          handleUpload(file, index);
        });
      } else {
        if (isAdd) {
          handleUpload(fileList[0], index);
        } else {
          handleUploadCreate({
            file: fileList[0].originFileObj,
            fileList: fileList.map((f) => f.originFileObj)
          });
        }
      }

      // 清空文件输入框，以便能够重复选择同一个文件
      event.target.value = '';
    };

    return (
      <Flex w="100%" h="100%" cursor="pointer">
        {loading && <Loading zIndex={9999999} />}
        <input
          type="file"
          ref={uploadRef}
          style={{ display: 'none' }}
          multiple
          accept=".pdf,.jpg,.jpeg,.png"
          onChange={handleFileChange}
        />
        <Box
          position="relative"
          borderRadius="8px"
          px={respDims(14)}
          pb={respDims(19)}
          width={isTablet ? respDims(300) : undefined}
          bg="white"
          display="flex"
          flexDirection="column"
        >
          <Box
            fontSize={isTablet ? respDims(14) : respDims('16fpx')}
            mb={respDims(14)}
            pt={respDims(14)}
            fontWeight="500"
            color="#303133"
            textAlign={studentName ? 'left' : 'center'}
            onDoubleClick={handleDoubleClick}
          >
            {studentName ? (
              !!isEditing ? (
                <Input
                  autoFocus
                  value={editTitle}
                  onChange={handleTitleChange}
                  onBlur={() => handleTitleBlur()}
                  onKeyPress={handleKeyPress}
                  className={styles['edit-title-input']}
                  style={{
                    width: `${isTablet ? `${respDims(180)}px` : '200px'}`,
                    height: `${isTablet ? `${respDims(25)}px` : '25px'}`,
                    lineHeight: `${isTablet ? `${respDims(25)}px` : '25px'}`
                  }}
                />
              ) : (
                <Box
                  style={{
                    width: `${isTablet ? `${respDims(180)}px` : '200px'}`,
                    height: `${isTablet ? `${respDims(25)}px` : '25px'}`,
                    lineHeight: `${isTablet ? `${respDims(25)}px` : '25px'}`
                  }}
                  display="flex"
                  alignItems="center"
                  position="relative"
                  role="group"
                >
                  {studentName}
                  <Box
                    ml={respDims(4)}
                    opacity={0}
                    transition="opacity 0.3s"
                    _groupHover={{ opacity: 1 }}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <SvgIcon name="edit" w={respDims(20)} h={respDims(20)} />
                  </Box>
                </Box>
              )
            ) : (
              '添加一篇作文'
            )}
          </Box>

          <Box h={isTablet ? respDims(195) : respDims(195)} mb={respDims(14)}>
            <Dragger
              ref={draggerRef}
              name="file"
              multiple={true}
              maxCount={3}
              showUploadList={false}
              fileList={fileList}
              beforeUpload={(file) => beforeUpload(file)}
              customRequest={({ file, onSuccess }: any) => {
                if (onSuccess) {
                  setTimeout(() => {
                    onSuccess('ok');
                  }, 0);
                }
              }}
              onChange={(info) => {
                // 1. 检查文件类型和数量限制
                const pdfFiles = info.fileList.filter((file) => file.type === 'application/pdf');
                const imageFiles = info.fileList.filter(
                  (file) => file.type?.startsWith('image/') ?? false
                );

                // PDF 文件限制检查
                if (pdfFiles.length > 1) {
                  Toast.warning('只能上传1张PDF文件');
                  setFileList(info.fileList.filter((file) => file.uid !== info.file.uid));
                  return;
                }

                // 总文件数限制检查
                if (info.fileList.length > 3) {
                  Toast.warning('最多只能添加3个文件');
                  setFileList(info.fileList.slice(0, 3));
                  return;
                }

                // 如果已有 PDF，不允许上传图片；如果已有图片，不允许上传 PDF
                if (pdfFiles.length > 0 && imageFiles.length > 0) {
                  Toast.warning('不能同时上传PDF和图片');
                  setFileList(info.fileList.filter((file) => file.uid !== info.file.uid));
                  return;
                }

                // 2. 更新文件列表
                setFileList(info.fileList);

                // 3. 处理文件上传
                if (info.file.status === 'done') {
                  if (uploadType === 'replace' || uploadIndex !== null) {
                    // 编辑模式：检查是否所有文件都上传完成
                    const allFilesUploaded = info.fileList.every((file) => file.status === 'done');

                    if (allFilesUploaded) {
                      // 所有文件都上传完成时，一次性处理所有文件
                      info.fileList.forEach((file) => {
                        handleUpload(file, index);
                      });
                      setFileList([]); // 清空文件列表
                    }
                  } else {
                    // 新建模式
                    if (isAdd) {
                      handleUpload(info.file, index);
                      setFileList([]); // 清空文件列表
                    } else {
                      // 检查是否所有文件都上传完成
                      const allFilesUploaded = info.fileList.every(
                        (file) => file.status === 'done'
                      );

                      if (allFilesUploaded) {
                        // 所有文件都上传完成时，一次性创建
                        const allFiles = info.fileList.map((file) => file.originFileObj);
                        handleUploadCreate({
                          file: allFiles[0],
                          fileList: allFiles
                        });
                        setFileList([]); // 清空文件列表
                      }
                    }
                  }
                }
              }}
              accept=".pdf,.jpg,.jpeg,.png"
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: '8px',
                background: '#fff',
                textAlign: 'center',
                cursor: 'pointer',
                position: 'relative'
              }}
              className="custom-dragger"
              data-testid="upload-input"
            >
              {studentList[studentListIndex]?.studentId && (
                <Box
                  position="absolute"
                  top={respDims(-58)}
                  right={respDims(-25)}
                  zIndex={5}
                  sx={{
                    opacity: 0,
                    transition: 'opacity 0.3s ease',
                    '.ant-upload-drag:hover &': {
                      opacity: 1
                    }
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (studentList[studentListIndex]?.studentId) {
                      setLoading(true);
                      deleteStudentList([
                        { id: Number(studentList[studentListIndex].studentId) }
                      ]).then(() => {
                        setStudentList(
                          studentList.filter(
                            (item) => item.studentId !== studentList[studentListIndex].studentId
                          )
                        );
                        setLoading(false);
                      });
                    }
                  }}
                  cursor="pointer"
                >
                  <SvgIcon name="closeCircleFill" w={respDims(26)} h={respDims(26)} />
                </Box>
              )}

              <Box
                position="absolute"
                top="50%"
                left="50%"
                transform="translate(-50%, -50%)"
                sx={{
                  display: index === undefined ? 'none' : 'block',
                  '.ant-upload-drag:hover &, .ant-upload-drag-hover &': {
                    display: 'none'
                  }
                }}
              >
                <Box
                  borderRadius="50%"
                  bg="#F4F6F8"
                  display="flex"
                  padding="9px"
                  justifyContent="center"
                  alignItems="center"
                  background="var(--fill-2, #F2F3F5)"
                  textAlign="center"
                >
                  <SvgIcon name="plus" w={respDims(24)} h={respDims(24)} color="#909399" />
                </Box>
              </Box>

              <Box
                w={cardWidth}
                sx={{
                  opacity: index === undefined ? 1 : 0,
                  transition: 'opacity 0.3s ease',
                  ...(index !== undefined && {
                    '.ant-upload-drag:hover &, .ant-upload-drag-hover &': {
                      opacity: 1
                    }
                  })
                }}
                lineHeight={respDims(18, 14)}
                textAlign="center"
                display="flex"
                flexDirection="column"
                alignItems="center"
                pt={respDims(10)}
                justifyContent="center"
              >
                <Box
                  borderRadius="50%"
                  bg="#F8F5FF"
                  display="flex"
                  padding="9px"
                  justifyContent="center"
                  alignItems="center"
                  textAlign="center"
                >
                  <SvgIcon name="plus" w={respDims(24)} h={respDims(24)} color="#7D4DFF" />
                </Box>
                <Box
                  color="var(--text-1, #1D2129)"
                  textAlign="center"
                  fontSize={isTablet ? respDims(14) : respDims('14fpx')}
                  fontStyle="normal"
                  fontWeight="400"
                  lineHeight={isTablet ? respDims(22) : respDims('22fpx')}
                  mt={respDims(16)}
                >
                  <Box
                    as="span"
                    fontSize={isTablet ? respDims(16) : respDims('16fpx')}
                    fontWeight="500"
                    mr={respDims(4)}
                  >
                    拖拽
                  </Box>
                  <Box
                    as="span"
                    fontSize={isTablet ? respDims(16) : respDims('16fpx')}
                    color="#606266"
                  >
                    文件到此，添加一篇作文
                  </Box>
                </Box>
                <Box
                  color="var(--text-3, #86909C)"
                  textAlign="center"
                  fontSize={isTablet ? respDims(12) : respDims('12fpx')}
                  fontStyle="normal"
                  fontWeight="400"
                  lineHeight={isTablet ? respDims(20) : '20px'}
                >
                  仅支持图片、PDF格式
                </Box>

                <Flex mt={respDims(16)} mb={respDims(16)}>
                  <Button
                    variant="outline"
                    _hover={{ color: '#7D4DFF', background: '#fff' }}
                    mr={respDims(13)}
                    h={respDims(40)}
                    w={isTablet ? respDims(100) : respDims(150)}
                    onClick={(event) => {
                      event.stopPropagation();
                      setUploadIndex(studentListIndex);
                      setIsManualInputOpen(true);
                    }}
                  >
                    <SvgIcon
                      name="textbox_line"
                      width={respDims(16)}
                      height={respDims(16)}
                      mr={respDims(4)}
                    />
                    <Box as="span" fontSize={isTablet ? respDims(13) : respDims('13fpx')}>
                      手动输入
                    </Box>
                  </Button>
                  <Button
                    variant="outline"
                    h={respDims(40)}
                    w={isTablet ? respDims(120) : respDims(150)}
                    _hover={{ color: '#7D4DFF', background: '#fff' }}
                    onClick={(event) => {
                      event.stopPropagation();
                      handleSelectFromCloud(studentListIndex);
                    }}
                  >
                    <SvgIcon
                      name="inbox_line"
                      width={respDims(16)}
                      height={respDims(16)}
                      mr={respDims(4)}
                    />
                    <Box as="span" fontSize={isTablet ? respDims(13) : respDims('13fpx')}>
                      从数据空间添加
                    </Box>
                  </Button>
                </Flex>
              </Box>
            </Dragger>
          </Box>
        </Box>
      </Flex>
    );
  }
);

// 添加显示名称
AppNewCorrection.displayName = 'AppNewCorrection';

// 添加默认导出
export default AppNewCorrection;
