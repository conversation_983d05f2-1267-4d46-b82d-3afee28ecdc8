import MyModal from '@/components/MyModal';
import { respDims } from '@/utils/chakra';
import { Button, Form, Input } from 'antd';
import styles from '@/pages/index.module.scss';
import { Box, ModalBody } from '@chakra-ui/react';
import { setFirstLoginUpdatePwd } from '@/api/user';
import { Toast } from '@/utils/ui/toast';
export interface CheckAccountActivationModalProps {
  onSuccess?: (data: any) => void;
  onError?: (data: any) => void;
  onFinally?: (data: any) => void;
}

type FieldType = {
  password1: string;
  password2: string;
};

const CheckAccountActivationModal: React.FC<CheckAccountActivationModalProps> = (props) => {
  console.log(props);
  const [form] = Form.useForm();

  async function onSubmit(values: FieldType) {
    console.log(values);
    // 这里调用修改密码接口-用新的接口
    const res = await setFirstLoginUpdatePwd({ password: values.password1 });
    if (res) {
      props?.onSuccess?.(res);
    } else {
      props?.onError?.(res);
    }
    props?.onFinally?.(res);
  }

  function handleReset() {
    form.resetFields();
  }

  return (
    <MyModal isOpen title="密码设置" w={respDims(480, 480)}>
      <ModalBody style={{ paddingTop: 30, paddingBottom: 0 }}>
        <Box color={'#bd3124'} marginBottom={'20px'}>
          为保障您的账号安全，首次登录请修改您的密码。
        </Box>
        <Form form={form} onFinish={onSubmit} layout="vertical" className={styles['my-form']}>
          <Form.Item<FieldType>
            name="password1"
            rules={[
              { required: true, message: '请输入新密码' },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                message: '密码需8至16位，包含大小写字母和数字的组合，可以输入特殊符号'
              }
            ]}
          >
            <Input.Password placeholder="请输入新密码" minLength={8} maxLength={16} />
          </Form.Item>
          <Form.Item<FieldType>
            name="password2"
            rules={[
              { required: true, message: '请再次输入新密码' },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                message: '密码需8至16位，包含大小写字母和数字的组合，可以输入特殊符号'
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password1') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                }
              })
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" minLength={8} maxLength={16} />
          </Form.Item>
          <Form.Item style={{ marginTop: 35, textAlign: 'right' }}>
            <Button onClick={handleReset} style={{ marginRight: 20 }}>
              重置
            </Button>
            <Button type="primary" htmlType="submit">
              确认修改
            </Button>
          </Form.Item>
        </Form>
      </ModalBody>
    </MyModal>
  );
};

export default CheckAccountActivationModal;
