import React, { useEffect, useMemo, useState } from 'react';
import { Box, Flex, Grid, Button, Image } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import Avatar from '@/components/Avatar';
import MyIcon from '@/components/LegacyIcon';
import { serviceSideProps } from '@/utils/i18n';
import MyMenu from '@/components/MyMenu';
import { useConfirm } from '@/hooks/useConfirm';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';
import DatasetTypeTag from '@/pages/dataset/component/DatasetTypeTag';
import { getDatasetsPage, putDatasetById, delDatasetById } from '@/api/tenant/dataset';
import { DatasetItemType, UpdateDatasetProps } from '@/types/api/tenant/dataset';
import { respDims } from '@/utils/chakra';
import { CollaborationTypeEnum, DatasetTypeEnum, DatasetTypeMap } from '@/constants/api/dataset';
import { PermissionTypeEnum } from '@/constants/permission';
import CreateModal from './component/CreateModal';
import { Pagination } from 'antd';
import { useToast } from '@/hooks/useToast';
import { DataSource } from '@/constants/common';

const DatasetList = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { parentId } = router.query as { parentId: string };
  const queryClient = useQueryClient();
  const { openConfirm, ConfirmModal } = useConfirm({ type: 'delete' });
  const { openOverlay } = useOverlayManager();
  const [filterType, setFilterType] = useState<PermissionTypeEnum>();
  const [current, setCurrent] = useState(1);
  const [size, setSize] = useState(10);
  const { toast } = useToast();

  const deleteTipsMap: Record<DatasetTypeEnum, string> = {
    [DatasetTypeEnum.Dataset]: '确认删除该知识库？删除后数据无法恢复，请确认！',
    [DatasetTypeEnum.WebsiteDataset]: '确认删除该知识库？删除后数据无法恢复，请确认！'
  };

  const { data, refetch, isFetching } = useQuery(['loadDataset', parentId, current, size], () => {
    return getDatasetsPage({ current, size, source: DataSource.Tenant });
  });

  const myDatasets = data?.records || [];
  const total = data?.total || 0;

  const presentDatasets = useMemo(() => {
    const datasets =
      filterType === undefined
        ? myDatasets
        : myDatasets.filter((it) => it.permission === filterType);
    return datasets.map((item) => ({
      ...item,
      label: DatasetTypeMap[item.type]?.label,
      icon: DatasetTypeMap[item.type]?.icon
    }));
  }, [filterType, myDatasets]);

  const updateDatasetMutation = useMutation(putDatasetById, {
    onSuccess: () => {
      queryClient.invalidateQueries(['loadDataset', parentId, current, size]);
    }
  });

  const deleteDatasetMutation = useMutation(delDatasetById, {
    onSuccess: () => {
      toast({
        title: '删除成功',
        status: 'success'
      });
      queryClient.invalidateQueries(['loadDataset', parentId, current, size]);
    }
  });

  const onEditDatasetModal = (dataset: DatasetItemType) => {
    openOverlay({
      Overlay: CreateModal,
      props: {
        type: 'edit',
        dataset,
        onSuccess: () => refetch()
      }
    });
  };

  const onCreateDatasetModal = () => {
    openOverlay({
      Overlay: CreateModal,
      props: {
        type: 'add',
        parentId,
        onSuccess: () => refetch()
      }
    });
  };

  return (
    <Flex
      flexDir="column"
      h="100%"
      p={respDims(40)}
      bgColor="#ffffff"
      bgImage="url(/imgs/v2/gradient_bg.png)"
      bgRepeat="no-repeat"
      bgSize="100% auto"
      borderRadius={respDims(20)}
      boxShadow="0px 0px 20px 0px rgba(0,0,0,0.04)"
    >
      <Flex alignItems={'center'} justifyContent={'space-between'}>
        <Button onClick={onCreateDatasetModal}>
          <SvgIcon name="plus" mr={respDims(4)}></SvgIcon>
          {t('创建知识库')}
        </Button>
      </Flex>

      <Box flex="1" overflow="auto">
        <Grid
          py={5}
          gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
          gridGap={5}
          userSelect={'none'}
        >
          {presentDatasets.map((dataset) => (
            <Box
              display={'flex'}
              flexDirection={'row'}
              key={dataset.id}
              py={3}
              px={5}
              cursor={'pointer'}
              borderWidth={1.5}
              borderColor={'borderColor.low'}
              bg={'white'}
              borderRadius={'md'}
              minH={'130px'}
              position={'relative'}
              draggable
              onDragStart={(e) => {
                e.dataTransfer.setData('text/plain', dataset.id);
              }}
              onDragOver={(e) => {
                e.preventDefault();
              }}
              onDrop={async (e) => {
                e.preventDefault();
                const dragStartId = e.dataTransfer.getData('text/plain');
                if (dragStartId && dragStartId !== dataset.id) {
                  await updateDatasetMutation.mutateAsync({
                    id: dragStartId,
                    parentId: dataset.id
                  });
                }
              }}
              _hover={{
                borderColor: 'primary.300',
                boxShadow: '1.5',
                '& .delete': {
                  display: 'block'
                }
              }}
              onClick={() => {
                router.push({
                  pathname: '/tenant/dataset/detail',
                  query: {
                    finalDatasetId: dataset.finalDatasetId
                  }
                });
              }}
            >
              <Avatar src={dataset.avatarUrl} borderRadius={'md'} w={'50px'} h={'50px'} mr={4} />
              <Box flex={1} display={'flex'} flexDirection={'column'}>
                <Flex justifyContent={'space-between'} alignItems={'center'}>
                  <Box className="textEllipsis3" fontWeight={'bold'} fontSize={'lg'}>
                    {dataset.name}
                  </Box>
                  <Box
                    position={'relative'}
                    borderRadius={'md'}
                    _hover={{
                      color: 'primary.500',
                      '& .icon': {
                        bg: 'myGray.100'
                      }
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <MyMenu
                      width={120}
                      Button={
                        <Box w={'22px'} h={'22px'}>
                          <MyIcon
                            className="icon"
                            name={'more'}
                            h={'16px'}
                            w={'16px'}
                            px={1}
                            py={1}
                            borderRadius={'md'}
                            cursor={'pointer'}
                          />
                        </Box>
                      }
                      menuList={[
                        {
                          label: (
                            <Flex alignItems={'center'} justifyContent="center" color="#303133">
                              <SvgIcon
                                name={'edit'}
                                w={respDims(20)}
                                h={respDims(20)}
                                mr={respDims(5)}
                              />
                              编辑信息
                            </Flex>
                          ),
                          onClick: () => onEditDatasetModal(dataset)
                        },
                        {
                          label: (
                            <Flex alignItems={'center'} justifyContent="center" color="#303133">
                              <SvgIcon
                                name="trash"
                                w={respDims(20)}
                                h={respDims(20)}
                                mr={respDims(5)}
                              />
                              {t('common.Delete')}
                            </Flex>
                          ),
                          onClick: () => {
                            openConfirm(
                              () => deleteDatasetMutation.mutate(dataset.id),
                              undefined,
                              deleteTipsMap[dataset.type]
                            )();
                          }
                        }
                      ]}
                    />
                  </Box>
                </Flex>
                <Box
                  flex={1}
                  className={'textEllipsis3'}
                  py={1}
                  wordBreak={'break-all'}
                  fontSize={respDims(14, 12)}
                  color={'myGray.500'}
                >
                  {dataset.intro || t('core.dataset.Intro Placeholder')}
                </Box>
                <Flex alignItems={'center'} fontSize={'sm'}>
                  <Box flex={1}>
                    {dataset.collaborationType == CollaborationTypeEnum.Collaboration && (
                      <SvgIcon
                        name="datasetGroup"
                        color="#909399"
                        w={respDims(20, 16)}
                        h={respDims(20, 16)}
                      ></SvgIcon>
                    )}
                  </Box>
                  <DatasetTypeTag type={dataset.type} py={1} px={2} />
                </Flex>
              </Box>
            </Box>
          ))}
        </Grid>

        {myDatasets.length === 0 && (
          <Flex mt={'35vh'} flexDirection={'column'} alignItems={'center'}>
            <MyIcon name="empty" w={'48px'} h={'48px'} color={'transparent'} />
            <Box mt={2} color={'myGray.500'}>
              {t('core.dataset.Empty Dataset Tips')}
            </Box>
          </Flex>
        )}
      </Box>

      <Flex justifyContent="flex-end" mt={respDims(16)}>
        <Pagination
          current={current}
          pageSize={size}
          total={total}
          onChange={(page, pageSize) => {
            setCurrent(page);
            setSize(pageSize);
          }}
        />
      </Flex>

      <ConfirmModal />
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default DatasetList;
