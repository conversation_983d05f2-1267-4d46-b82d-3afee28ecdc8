import React, { useState, useEffect, useMemo } from 'react';
import { Flex, Input, Button } from '@chakra-ui/react';
import { SearchBarProps } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { WorkflowStatusEnum, WorkflowStatusMap } from '@/constants/api/workflow';
import { Select } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EditWorkflowModal from './EditWorkflowModal';
import { TenantWorkflowsPageRequest, TenantWorkflow } from '@/types/api/tenant/workflow';
import { WorkflowTabType } from '@/types/pages/workflow';

const SearchBar = ({
  onSearch,
  query,
  tableInstance,
  currentTab
}: SearchBarProps<TenantWorkflowsPageRequest, TenantWorkflow> & {
  currentTab: WorkflowTabType['value'];
}) => {
  const router = useRouter();
  const [appName, setAppName] = useState(query?.appName || '');
  const [workflowName, setWorkflowName] = useState(query?.name || '');
  const [status, setStatus] = useState<WorkflowStatusEnum | undefined>(query?.status);
  const [userName, setUserName] = useState(query?.userName || '');
  const [tenantName, setTenantName] = useState(query?.tenantName || '');
  const { openOverlay, OverlayContainer } = useOverlayManager();

  const onAdd = () => {
    openOverlay({
      Overlay: EditWorkflowModal,
      props: {
        formStatus: 'add',
        onClose: () => {},
        onSuccess() {
          tableInstance?.reload();
        }
      }
    });
  };

  useEffect(() => {
    setAppName(query?.appName || '');
    setWorkflowName(query?.name || '');
    setStatus(query?.status);
    setUserName(query?.userName || '');
    setTenantName(query?.tenantName || '');
  }, [router, query]);

  const handleSearch = () => {
    const params = { ...query, appName, name: workflowName, status, userName, tenantName };
    onSearch && onSearch(params);
  };

  const handleReset = () => {
    setAppName('');
    setWorkflowName('');
    setStatus(undefined);
    setUserName('');
    setTenantName('');
  };

  const statusOptions = useMemo(
    () =>
      Object.values(WorkflowStatusMap).map((option) => ({
        value: option.value,
        label: option.label
      })),
    []
  );

  return (
    <Flex alignItems="center">
      <Input
        placeholder="请输入应用名称"
        value={appName}
        onChange={(e) => setAppName(e.target.value)}
        style={{ width: '200px', marginRight: '16px', borderRadius: '1px' }}
      />
      <Input
        placeholder="请输入工作流名称"
        value={workflowName}
        onChange={(e) => setWorkflowName(e.target.value)}
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginRight: '16px' }}
      />
      {currentTab === 'personal' && (
        <>
          <Input
            placeholder="请输入用户名"
            value={userName}
            onChange={(e) => setUserName(e.target.value)}
            style={{ width: '200px', borderRadius: '1px', height: '40px', marginRight: '16px' }}
          />
          <Input
            placeholder="请输入租户名称"
            value={tenantName}
            onChange={(e) => setTenantName(e.target.value)}
            style={{ width: '200px', borderRadius: '1px', height: '40px', marginRight: '16px' }}
          />
        </>
      )}
      {currentTab === 'public' && (
        <Input
          placeholder="请输入租户名称"
          value={tenantName}
          onChange={(e) => setTenantName(e.target.value)}
          style={{ width: '200px', borderRadius: '1px', height: '40px', marginRight: '16px' }}
        />
      )}
      {currentTab !== 'personal' && (
        <Select
          value={status}
          options={statusOptions}
          placeholder="请选择状态"
          allowClear
          onChange={(value) => setStatus(value)}
          style={{
            width: '200px',
            borderRadius: '1px',
            height: '40px',
            marginRight: '16px'
          }}
        ></Select>
      )}
      <Button onClick={handleReset} variant={'grayBase'} mr={respDims(10)}>
        重置
      </Button>
      <Button
        ml="4px"
        h="36px"
        mr={respDims(10)}
        colorScheme="primary"
        variant="outline"
        onClick={handleSearch}
      >
        查询
      </Button>
      {currentTab === 'public' && (
        <Button ml="4px" h="36px" variant="primary" onClick={onAdd}>
          <SvgIcon name="plus"></SvgIcon> 添加工作流
        </Button>
      )}
      <OverlayContainer></OverlayContainer>
    </Flex>
  );
};

export default SearchBar;
