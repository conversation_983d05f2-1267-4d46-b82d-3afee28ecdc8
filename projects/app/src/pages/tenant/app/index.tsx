import React, { memo, useEffect, useState, useRef, RefAttributes, ComponentType } from 'react';
import PageContainer from '@/components/PageContainer';
import { serviceSideProps } from '@/utils/i18n';
import { Box, Flex, useDisclosure } from '@chakra-ui/react';
import IndustryList from './components/IndustryList/index';
import TenantList from './components/TenantList/index';
import IndividualList from './components/IndividualList/index';
import { AppPageProps, AppTabType, SubPageAppRef } from '@/types/pages/app';
import {
  deleteTenantApp,
  topApp,
  updateAppStatus,
  getTenantValidAppWorkflow
} from '@/api/tenant/app';
import { MessageBox } from '@/utils/ui/messageBox';
import { useRouter } from 'next/router';
import { useToast } from '@/hooks/useToast';
import AppTenantModal from '@/components/AppTenantModal';
import { TenantAppListItemType, AppModalDataType } from '@/types/api/tenant/app';
import ImportApp, { ImportAppRef } from './components/ImportApp';
import { ForwardRefExoticComponent } from 'react';
import { DataSource } from '@/constants/common';
import { getSampleAppInfo } from '@/utils/app';

type TabComponentType = ForwardRefExoticComponent<AppPageProps & RefAttributes<SubPageAppRef>>;

const tabs: { name: string; value: AppTabType['value']; component: TabComponentType }[] = [
  {
    name: '公共应用',
    value: 'industry',
    component: IndustryList
  },
  {
    name: '成员个人应用',
    value: 'individual',
    component: IndividualList
  }
];

type ConfirmDialogType = {
  destroy: () => void;
};

type ModeType = 1 | 2;

const AppCenter = ({ tenantId }: { tenantId: string }) => {
  const [currentTab, setCurrentTab] = useState<AppTabType['value']>('industry');
  const [appModalId, setAppModalId] = useState<AppModalDataType>();
  const [isProcessing, setIsProcessing] = useState(false);
  const [mode, setMode] = useState<ModeType>();
  const {
    isOpen: isOpenCreateModal,
    onOpen: onOpenCreateModal,
    onClose: onCloseCreateModal
  } = useDisclosure();
  const actionRef = useRef<any>(); // 创建 actionRef
  const importAppRef = useRef<ImportAppRef>(null);
  const router = useRouter();
  const { toast } = useToast();
  const { industry } = router.query;
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);

  const onAddAppCenter = (mode: 1 | 2) => {
    setMode(mode);
    setAppModalId({
      source: DataSource.Tenant
    } as any);
    onOpenCreateModal();
  };

  const onEditAppCenter = (app: TenantAppListItemType) => {
    setAppModalId(app);
    onOpenCreateModal();
  };

  const onEditSetting = (record: TenantAppListItemType) => {
    router.push({
      pathname: '/tenant/app/detail',
      query: {
        appType: record.type,
        finalAppId: record.finalAppId,
        source: record.source,
        tmbId: record.tmbId,
        tenantId: record.tenantId,
        isAdmin: '1',
        id: record.id,
        appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(record)))
      }
    });
  };

  const onDelete = async (record: TenantAppListItemType) => {
    if (isProcessing) return;
    try {
      setIsProcessing(true);
      const res = await getTenantValidAppWorkflow({ id: record.id });

      if (res) {
        const tips = (
          <span>
            当前应用关联的工作流正在
            <strong>[启用]</strong>
            ，请
            <a style={{ color: 'blue' }} onClick={() => handleWorkflowClick(record)}>
              前往工作流管理
            </a>
            将关联应用进行替换再进行删除！
          </span>
        );

        confirmDialogRef.current = MessageBox.confirm({
          title: '删除警告',
          content: tips,
          onOk: () => setIsProcessing(false),
          onCancel: () => setIsProcessing(false)
        });
      } else {
        confirmDialogRef.current = MessageBox.confirm({
          title: '删除',
          content: '确认删除该应用所有信息？',
          onOk: async () => {
            try {
              await deleteTenantApp({ id: record.id, tmbId: record.tmbId });
              toast({
                title: '删除成功',
                status: 'success'
              });
              actionRef.current?.reload();
            } catch (err: any) {
              toast({
                title: err?.message || '删除失败',
                status: 'error'
              });
            } finally {
              setIsProcessing(false);
            }
          },
          onCancel: () => setIsProcessing(false)
        });
      }
    } catch (error) {
      toast({
        title: '验证工作流出错',
        status: 'error'
      });
      setIsProcessing(false);
    }
  };

  const onSetTop = async (app: TenantAppListItemType) => {
    await topApp({ id: app.id });
    toast({
      title: '置顶成功',
      status: 'success'
    });
    actionRef.current?.reload();
  };

  const onImportType = () => {
    if (importAppRef.current) {
      importAppRef.current.openModal({
        formStatus: 'add'
      });
    }
  };

  const handleWorkflowClick = (app: TenantAppListItemType) => {
    if (confirmDialogRef.current) {
      confirmDialogRef.current.destroy();
    }
    router.push({
      pathname: '/tenant/workflow',
      query: {
        appName: app.name,
        currentTab: currentTab === 'industry' ? 'public' : 'personal'
      }
    });
  };

  // 找到当前选中的 tab
  const currentTabComponent = tabs.find((tab) => tab.value === currentTab)?.component;
  const TabRender = () => {
    return (
      <Flex alignItems="stretch" flexShrink="0">
        {tabs.map((tab) => (
          <Box
            key={tab.value}
            mr="32px"
            py="10px"
            position="relative"
            {...(tab.value === currentTab
              ? {
                  color: 'primary.500',
                  _after: {
                    position: 'absolute',
                    content: '""',
                    left: '0',
                    right: '0',
                    bottom: '-1px',
                    w: '100%',
                    height: '2px',
                    bgColor: 'primary.500'
                  }
                }
              : {
                  color: '#4E5969'
                })}
            fontSize="14px"
            fontWeight="bold"
            cursor="pointer"
            onClick={() => setCurrentTab(tab.value)}
          >
            {tab.name}
          </Box>
        ))}
      </Flex>
    );
  };
  return (
    <PageContainer pageBgColor="rgba(255,255,255,0.6)" border="2px solid #FFFFFF">
      <Flex w="100%" h="100%" flexDir="column">
        {currentTabComponent &&
          React.createElement(currentTabComponent, {
            currentTab,
            onAddAppCenter,
            onEditAppCenter,
            onEditSetting,
            onDelete,
            onSetTop,
            onImportType,
            ref: actionRef,
            TabRender
          })}
      </Flex>

      {isOpenCreateModal && (
        <AppTenantModal
          onClose={onCloseCreateModal}
          onSuccess={() => actionRef.current?.reload()} // 在 onSuccess 中调用 reload 方法
          appModalParams={appModalId!}
          mode={mode as ModeType}
        />
      )}

      <ImportApp ref={importAppRef} onSuccess={() => actionRef.current?.reload()} />
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      tenantId: context?.query?.tenantId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default memo(AppCenter);
