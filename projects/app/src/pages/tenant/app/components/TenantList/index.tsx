import { Ref, forwardRef, memo, useImperativeHandle, useRef } from 'react';
import { Flex, Tag, useDisclosure } from '@chakra-ui/react';
import { deleteTenantApp, getTenantAppPage } from '@/api/tenant/app';
import { TenantAppListItemType, AppModalDataType } from '@/types/api/tenant/app';
import { MessageBox } from '@/utils/ui/messageBox';
import { useToast } from '@/hooks/useToast';
import { Button } from 'antd';
import MyTable from '@/components/MyTable';
import SearchBar from '../SearchBar';
import { AppPageProps } from '@/types/pages/app';
import { SubPageAppRef } from '../IndustryList';
import { MyTableRef } from '@/components/MyTable/types';
import { useRouter } from 'next/router';

const TenantList = (
  { currentTab, onEditAppCenter, onEditSetting, onDelete, TabRender }: AppPageProps,
  ref: Ref<SubPageAppRef>
) => {
  const actionRef = useRef<MyTableRef>(null); // 创建 actionRef
  const router = useRouter();
  const { industry } = router.query;
  const columns = [
    {
      title: '客户',
      dataIndex: 'tenantId',
      key: 'tenantId'
    },
    {
      title: '应用',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '介绍',
      dataIndex: 'intro',
      key: 'intro'
    },
    {
      title: '场景',
      dataIndex: 'tenantSceneIds',
      key: 'tenantSceneIds',
      render: (tenantSceneIds: string[], record: TenantAppListItemType) => (
        <span>
          {record.labelList?.map((label) => label.tenantSceneName).join(', ') || '暂无场景'}
        </span>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: 1 | 2) => (
        <Tag color={status === 1 ? 'green' : 'gray'}>{status === 1 ? '上线' : '下线'}</Tag>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime'
    },
    {
      title: '操作',
      key: 'action',
      width: 380,
      render: (dom: React.ReactNode, record: TenantAppListItemType) => (
        <>
          <Button
            type="link"
            onClick={() => {
              onEditAppCenter(record);
            }}
          >
            编辑信息
          </Button>
          <Button type="link" onClick={() => onEditSetting(record)}>
            编辑编排
          </Button>

          <Button type="link" danger onClick={() => onDelete(record)}>
            删除
          </Button>
        </>
      )
    }
  ];
  useImperativeHandle(ref, () => ({
    reload: () => {
      actionRef.current?.reload();
    }
  }));

  return (
    <Flex flex="1" h="calc(100% - 56px)" key="tenantList">
      <MyTable
        cacheKey="tenantList"
        columns={columns}
        api={getTenantAppPage}
        rowKey="id"
        defaultQuery={{}}
        ref={actionRef} // 传递 actionRef 给 ProTable
        headerConfig={{
          showHeader: true,
          HeaderComponent: (props) => {
            return (
              <Flex justifyContent="space-between" alignItems="center" w="100%">
                <TabRender></TabRender>
                <SearchBar {...props}></SearchBar>
              </Flex>
            );
          }
        }}
      />
    </Flex>
  );
};

export default forwardRef<SubPageAppRef, AppPageProps>(TenantList);
