import { Ref, forwardRef, memo, useImperativeHandle, useRef } from 'react';
import { Flex, Tag, useDisclosure } from '@chakra-ui/react';
import { getPersonalAppPage, upPersonalApp } from '@/api/tenant/app';
import { TenantAppListItemType } from '@/types/api/tenant/app';
import { Button } from 'antd';
import MyTable from '@/components/MyTable';
import SearchBar from '../SearchBar';
import { SubPageAppRef } from '../IndustryList';
import { AppPageProps } from '@/types/pages/app';
import { MyTableRef } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { Config, ConfigMap } from '@/constants/api/app';
import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import { setTenantAppUpdateConfig } from '@/api/tenant/app';
import { respDims } from '@/utils/chakra';
import ButtonGroup from '@/components/ButtonGroup';
import SvgIcon from '@/components/SvgIcon';

const IndividualList = (
  { currentTab, onEditAppCenter, onEditSetting, onDelete, TabRender }: AppPageProps,
  ref: Ref<SubPageAppRef>
) => {
  const actionRef = useRef<MyTableRef>(null); // 创建 actionRef
  const router = useRouter();
  const { industry } = router.query;
  const { toast } = useToast();
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);
  type ConfirmDialogType = {
    destroy: () => void;
  };

  const handleOrderClick = (app: TenantAppListItemType) => {
    const query = {
      appName: app.name,
      currentTab:
        app.source === 2 ? 'authority' : app.source === 1 ? 'commonality' : 'PersonageList',
      tenantName: app.source === 1 ? app.tenantName : '',
      userName: !app.source ? app.userName : ''
    };
    router.push({
      pathname: '/tenant/tenantPrompt',
      query
    });
  };

  const handlePublicClick = (app: TenantAppListItemType) => {
    upPersonalApp({ id: app.id }).then(() => {
      toast({
        title: '操作成功',
        status: 'success'
      });
      actionRef.current?.reload();
    });
  };

  const handleWorkflowClick = (app: TenantAppListItemType) => {
    const query = {
      appName: app.name,
      currentTab: app.source === 2 ? 'official' : app.source === 1 ? 'public' : 'personal',
      tenantName: app.source === 1 ? app.tenantName : '',
      userName: !app.source ? app.userName : ''
    };
    router.push({
      pathname: '/tenant/workflow',
      query
    });
  };

  const handleConfigClick = (app: TenantAppListItemType) => {
    confirmDialogRef.current = MessageBox.confirm({
      title: '确认操作',
      content: `${ConfigMap[app.config].label === '公开配置' ? '确认私有配置？' : '公开配置该应用后，所有用户可复制该应用，确认公开配置？'}`,
      onOk: async () => {
        try {
          await setTenantAppUpdateConfig({
            id: app.id,
            config: ConfigMap[app.config].updateStatus,
            updateUsername: app.updateUsername
          });
          toast({
            title: '操作成功',
            status: 'success'
          });
          actionRef.current?.reload();
        } catch (err: any) {
          toast({
            title: err?.message || '操作失败',
            status: 'error'
          });
        } finally {
        }
      }
    });
  };

  const columns = [
    {
      title: '用户',
      dataIndex: 'userName',
      key: 'userName',
      width: 120,
      ellipsis: true
    },
    {
      title: '应用',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      ellipsis: true
    },
    {
      title: '介绍',
      dataIndex: 'intro',
      key: 'intro',
      width: 260,
      ellipsis: true
    },
    {
      title: '公开状态',
      dataIndex: 'config',
      key: 'config',
      width: 100,
      ellipsis: true,
      render: (config: Config, record: any) => (
        <Flex
          color={ConfigMap[config].color}
          fontSize={respDims(14, 12)}
          alignItems="center"
          justifyContent="center"
          border={`1px solid ${ConfigMap[config].color}`}
          borderRadius="5px"
          bg={ConfigMap[config].bgColor}
          display="inline"
          p="2px 8px"
        >
          {ConfigMap[config].title}
        </Flex>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 120,
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      ellipsis: true,
      render: (dom: React.ReactNode, record: TenantAppListItemType) => {
        const buttons = [
          {
            label: '编辑信息',
            icon: <SvgIcon name="edit" w="16px" h="16px" />,
            onClick: () => onEditAppCenter(record)
          },
          {
            label: '编辑编排',
            icon: <SvgIcon name="appSetting" w="16px" h="16px" />,
            onClick: () => onEditSetting(record)
          },
          {
            label: '上架为公共',
            icon: <SvgIcon name="commonList" w="16px" h="16px" />,
            onClick: () => handlePublicClick(record)
          },
          {
            label: '管理指令',
            icon: <SvgIcon name="order" w="16px" h="16px" />,
            onClick: () => handleOrderClick(record)
          },
          {
            label: '管理工作流',
            icon: <SvgIcon name="workflow" w="16px" h="16px" />,
            onClick: () => handleWorkflowClick(record)
          },
          {
            label: ConfigMap[record.config]?.label === '公开配置' ? '私有配置' : '公开配置',
            icon: <SvgIcon name={ConfigMap[record.config]?.publishIcon as any} w="16px" h="16px" />,
            onClick: () => handleConfigClick(record)
          },
          {
            label: '删除',
            icon: <SvgIcon name="trash" w="16px" h="16px" />,
            onClick: () => onDelete(record),
            danger: true
          }
        ];

        return <ButtonGroup buttons={buttons} visibleCount={3} />;
      }
    }
  ];

  useImperativeHandle(ref, () => ({
    reload: () => {
      actionRef.current?.reload();
    }
  }));
  return (
    <Flex flex="1" h="calc(100% - 56px)" key="individual">
      <MyTable
        cacheKey="individual"
        columns={columns}
        api={getPersonalAppPage}
        rowKey="id"
        defaultQuery={{}}
        ref={actionRef} // 传递 actionRef 给 ProTable
        headerConfig={{
          showHeader: true,
          HeaderComponent: (props) => {
            return (
              <Flex justifyContent="space-between" alignItems="center" w="100%">
                <TabRender></TabRender>
                <SearchBar {...props}></SearchBar>
              </Flex>
            );
          }
        }}
      />
    </Flex>
  );
};

export default forwardRef<SubPageAppRef, AppPageProps>(IndividualList);
