import React, {
  useCallback,
  useState,
  useMemo,
  useRef,
  useEffect,
  forwardRef,
  Ref,
  useImperativeHandle
} from 'react';
import {
  Box,
  Grid,
  Flex,
  Button,
  useDisclosure,
  InputGroup,
  Input,
  InputRightElement,
  Image,
  MenuButton,
  Center,
  Tooltip
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { Popover } from 'antd';
import { AddIcon, SearchIcon } from '@chakra-ui/icons';
import { useToast } from '@/hooks/useToast';
import { useConfirm } from '@/hooks/useConfirm';
import { serviceSideProps } from '@/utils/i18n';
import ImportApp, { ImportAppRef } from '../ImportApp';
import { respDims } from '@/utils/chakra';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { useUserStore } from '@/store/useUserStore';
import { AppModalDataType, TenantAppListItemType } from '@/types/api/tenant/app';
import { MessageBox } from '@/utils/ui/messageBox';
import { AppPageProps, AppTabType } from '@/types/pages/app';
import {
  getMyAppPage,
  deleteTenantApp,
  topApp,
  updateAppStatus,
  cancelTopApp,
  copyApp,
  getTenantValidAppWorkflow,
  setTenantAppUpdateConfig
} from '@/api/tenant/app';
import { useQueryPage } from '@/hooks/useQueryPage';
import { Dropdown, Menu, Pagination, Space } from 'antd';
import SearchBar from '../SearchBar'; // 引入 SearchBar 组件
import { useLoading } from '@/hooks/useLoading';
import { AppStatusMap, Sort, SortMap, Source, SourceMap, ConfigMap } from '@/constants/api/app';
import { DownOutlined } from '@ant-design/icons';
import LabelList from '@/components/MyTags';
import MyEllipeseTooltip from '@/components/MyEllipeseTooltip';
import { DataSource } from '@/constants/common';
import MyTooltip from '@/components/MyTooltip';

export interface SubPageAppRef {
  reload: () => void;
}

type ConfirmDialogType = {
  destroy: () => void;
};

const IndustryList = (
  {
    currentTab,
    onAddAppCenter,
    onEditAppCenter,
    onEditSetting,
    onDelete,
    onSetTop,
    onImportType,
    TabRender
  }: AppPageProps,
  ref: Ref<SubPageAppRef>
) => {
  const { toast } = useToast();
  const { userInfo } = useUserStore();
  const [filterSceneId, setFilterSceneId] = useState('all');
  const [filterText, setFilterText] = useState('');
  const [filterSource, setFilterSource] = useState('all'); // 添加 filterSource 状态
  const gridRef = useRef<HTMLDivElement>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const confirmDialogRef = useRef<ConfirmDialogType | null>(null);
  const [visible, setVisible] = useState(false);

  const { Loading } = useLoading();
  const router = useRouter();
  const { industry } = router.query;

  const {
    current,
    size,
    data: myApps,
    total,
    isFetching,
    refetch,
    setCurrent,
    setSize,
    query,
    setQuery
  } = useQueryPage<TenantAppListItemType>({
    key: `loadApps${industry}`,
    api: getMyAppPage,
    defaultQuery: {
      industry
    },
    defaultCurrent: 1,
    defaultSize: 12,
    onSuccess: (data) => {},
    onError: (error) => {
      console.error('Error fetching data:', error);
    }
  });

  useImperativeHandle(ref, () => ({
    reload: refetch
  }));

  const presentApps = useMemo(() => {
    return filterText ? myApps.filter((it) => it.name.includes(filterText)) : myApps;
  }, [myApps, filterSceneId, filterText]);

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollTop = 0;
    }
  }, [filterSceneId]);

  const handleSearch = (params: { searchKey?: string; source?: string }) => {
    setQuery({
      ...query,
      industry,
      source: filterSource == 'all' ? null : filterSource,
      ...params
    });
  };

  const handleSetTop = async (app: TenantAppListItemType) => {
    try {
      const newSort = SortMap[app.sort].updateStatus;
      if (newSort === Sort.Pinned) {
        await topApp({ id: app.id });
        toast({
          title: '置顶成功',
          status: 'success'
        });
      } else {
        await cancelTopApp({ id: app.id });
        toast({
          title: '取消置顶成功',
          status: 'success'
        });
      }
      refetch();
    } catch (err: any) {
      toast({
        title: err?.message || '操作失败',
        status: 'error'
      });
    }
  };

  const handleCopyApp = async (app: TenantAppListItemType) => {
    try {
      await copyApp({ id: app.id });
      toast({
        title: '复制成功',
        status: 'success'
      });
      refetch();
    } catch (err: any) {
      toast({
        title: err?.message || '复制失败',
        status: 'error'
      });
    }
  };

  const handleAppStatusUpdate = async (app: TenantAppListItemType) => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      const res = await getTenantValidAppWorkflow({ id: app.id });

      if (res) {
        const tips = (
          <span>
            当前应用关联的工作流正在
            <strong>[启用]</strong>
            ，请
            <a style={{ color: 'blue' }} onClick={() => handleWorkflowClick(app)}>
              前往工作流管理
            </a>
            将关联应用进行替换再进行操作！
          </span>
        );

        confirmDialogRef.current = MessageBox.confirm({
          title: '下线警告',
          content: tips,
          onOk: () => setIsProcessing(false),
          onCancel: () => setIsProcessing(false)
        });
      } else {
        confirmDialogRef.current = MessageBox.confirm({
          title: '确认操作',
          content: `确认要${AppStatusMap[app.status].label === '上线' ? '下线' : '上线'}该应用吗？`,
          onOk: async () => {
            try {
              await updateAppStatus({
                id: app.id,
                status: AppStatusMap[app.status].updateStatus
              });
              toast({
                title: AppStatusMap[app.status].label === '上线' ? '下线成功' : '上线成功',
                status: 'success'
              });
              refetch();
            } catch (err: any) {
              toast({
                title: err?.message || '操作失败',
                status: 'error'
              });
            } finally {
              setIsProcessing(false);
            }
          },
          onCancel: () => setIsProcessing(false)
        });
      }
    } catch (error) {
      toast({
        title: '验证工作流时出错',
        status: 'error'
      });
      setIsProcessing(false); // 处理完成后重置状态
    }
  };

  const selectMenu = (
    <Menu
      onClick={(e) => {
        setFilterSource(e.key);
        handleSearch({ source: (e.key == 'all' ? null : e.key) as string });
      }}
    >
      <Menu.Item key="1">本组织公共应用</Menu.Item>
      <Menu.Item key="2">官方公共应用</Menu.Item>
      <Menu.Item key="all">全部公共应用</Menu.Item>
    </Menu>
  );

  const handleOrderClick = (app: TenantAppListItemType) => {
    const query = {
      appName: app.name,
      currentTab:
        app.source === 2 ? 'authority' : app.source === 1 ? 'commonality' : 'PersonageList',
      tenantName: app.source === 1 ? app.tenantName : '',
      userName: !app.source ? app.userName : ''
    };
    router.push({
      pathname: '/tenant/tenantPrompt',
      query
    });
  };

  const handleWorkflowClick = (app: TenantAppListItemType) => {
    if (confirmDialogRef.current) {
      confirmDialogRef.current.destroy();
    }
    const query = {
      appName: app.name,
      currentTab: app.source === 2 ? 'official' : app.source === 1 ? 'public' : 'personal',
      tenantName: app.source === 1 ? app.tenantName : '',
      userName: !app.source ? app.userName : ''
    };
    router.push({
      pathname: '/tenant/workflow',
      query
    });
  };

  const onChangeApp = () => {
    setVisible(true);
  };

  const handleVisibleChange = (newVisible: boolean) => {
    setVisible(newVisible);
  };

  const handleConfigClick = (app: TenantAppListItemType) => {
    confirmDialogRef.current = MessageBox.confirm({
      title: '确认操作',
      content: `${ConfigMap[app.config].label === '公开配置' ? '确认私有配置？' : '公开配置该应用后，所有用户可复制该应用，确认公开配置？'}`,
      onOk: async () => {
        try {
          await setTenantAppUpdateConfig({
            id: app.id,
            config: ConfigMap[app.config].updateStatus,
            updateUsername: app.updateUsername
          });
          toast({
            title: '操作成功',
            status: 'success'
          });
          refetch();
        } catch (err: any) {
          toast({
            title: err?.message || '操作失败',
            status: 'error'
          });
        } finally {
        }
      }
    });
  };

  const content = (
    <Box>
      <Flex direction="column" p={4}>
        <Box
          display="flex"
          alignItems="center"
          mb={2}
          cursor="pointer"
          onClick={() => onAddAppCenter(1)}
        >
          <SvgIcon w={respDims(32)} mr={respDims(12)} h={respDims(32)} name="easy" />
          <Box mb={2}>
            简易应用
            <Box fontSize={respDims(14, 12)} color="gray.500">
              通过表单的方式，创建简单的AI应用，适合新手
            </Box>
          </Box>
        </Box>
        <Box display="flex" alignItems="center" cursor="pointer" onClick={() => onAddAppCenter(2)}>
          <SvgIcon w={respDims(32)} mr={respDims(12)} h={respDims(32)} name="advanced" />
          <Box>
            高阶应用
            <Box fontSize={respDims(14, 12)} color="gray.500">
              通过编程的方式，构建更复杂的多场景AI应用，推荐有经验者使用
            </Box>
          </Box>
        </Box>
      </Flex>
    </Box>
  );

  return (
    <Flex flexDir="column" h="100%" px={respDims(32)} py={respDims(16)}>
      <Flex alignItems="center" mb={respDims(24)}>
        <Box flex="1">
          <TabRender></TabRender>
        </Box>

        <Flex mr={respDims(10)} alignItems="center">
          <SearchBar onSearch={handleSearch} query={query as any} />
        </Flex>

        <Dropdown overlay={selectMenu}>
          <Button
            h={respDims(36, 36)}
            mr={respDims(10)}
            colorScheme="primary"
            variant="outline"
            fontSize={respDims(14, 12)}
            borderRadius={respDims(4)}
          >
            <Space>
              {filterSource === 'all'
                ? '全部公共应用'
                : filterSource === '2'
                  ? '官方公共应用'
                  : filterSource === '1'
                    ? '本组织公共应用'
                    : ''}
              <DownOutlined />
            </Space>
          </Button>
        </Dropdown>
        <Popover
          content={content}
          title=""
          trigger="click"
          visible={visible}
          onVisibleChange={handleVisibleChange}
        >
          <Button
            h={respDims(38, 26)}
            mr={respDims(10)}
            leftIcon={<AddIcon />}
            variant="primary"
            fontSize={respDims(14, 12)}
            borderRadius={respDims(4)}
            onClick={onChangeApp}
          >
            新建公共应用
          </Button>
        </Popover>
      </Flex>

      <Box flex="1" overflow="auto">
        <Loading loading={isFetching}></Loading>

        {myApps.length > 0 ? (
          <Grid
            ref={gridRef}
            gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
            gridGap={respDims(16)}
          >
            {presentApps.map((app: TenantAppListItemType) => (
              <Flex
                key={app.id}
                p={respDims(20)}
                position="relative"
                cursor="pointer"
                flexDirection="column"
                userSelect="none"
                border="1px solid #E5E7EB"
                borderRadius={respDims(8)}
                _hover={{
                  boxShadow:
                    '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                  '& .app-menu': {
                    display: 'flex'
                  }
                }}
              >
                <Flex>
                  {app.sort == Sort.Pinned && (
                    <SvgIcon
                      color="#909399"
                      w={respDims(24)}
                      mr={respDims(8)}
                      h={respDims(24)}
                      name="alignTop"
                    />
                  )}

                  <Flex
                    flex="1"
                    w="100%"
                    overflow="hidden"
                    justifyContent="flex-start"
                    alignItems="center"
                  >
                    <Box
                      mr={respDims(14)}
                      flex="0 0 auto"
                      color="#000000"
                      fontSize={respDims(16, 14)}
                      fontWeight="bold"
                    >
                      {app.name}
                    </Box>
                    <Flex
                      mr={respDims(16)}
                      px={respDims(8)}
                      justifyContent="center"
                      alignItems="center"
                      bg={SourceMap[app.source].tagBgColor}
                      color={SourceMap[app.source].tagColor}
                    >
                      {SourceMap[app.source].label}
                    </Flex>
                    <Flex
                      mr={respDims(16)}
                      color={AppStatusMap[app.status].color}
                      fontSize={respDims(14, 12)}
                      justifyContent="center"
                      alignItems="center"
                    >
                      <Box
                        w={respDims(6, 6)}
                        h={respDims(6, 6)}
                        bg={AppStatusMap[app.status].color}
                        borderRadius="50px"
                      ></Box>
                      <Box ml={respDims(5)}>{AppStatusMap[app.status].label}</Box>
                    </Flex>
                  </Flex>
                  <MyMenu
                    trigger="click"
                    offset={[20, 0]}
                    width={20}
                    Button={
                      <MenuButton
                        className="app-menu"
                        display="none"
                        position="absolute"
                        top="0"
                        right="0"
                        w={respDims(30)}
                        h={respDims(30)}
                        _hover={{
                          bg: 'myWhite.600'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Center>
                          <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                        </Center>
                      </MenuButton>
                    }
                    menuList={
                      app.source === DataSource.Offical // 判断是否为官方应用
                        ? [
                            {
                              label: '复制',
                              icon: <SvgIcon name="copy" w="16px" h="16px" />,
                              onClick: () => handleCopyApp(app)
                            },
                            {
                              label: AppStatusMap[AppStatusMap[app.status].updateStatus].label,
                              icon: (
                                <SvgIcon
                                  name={AppStatusMap[app.status].publishIcon as any}
                                  w="16px"
                                  h="16px"
                                />
                              ),
                              onClick: () => handleAppStatusUpdate(app)
                            },
                            {
                              label: '管理指令',
                              icon: <SvgIcon name="order" w="16px" h="16px" />,
                              onClick: () => handleOrderClick(app)
                            },
                            {
                              label: '管理工作流',
                              icon: <SvgIcon name="workflow" w="16px" h="16px" />,
                              onClick: () => handleWorkflowClick(app)
                            },
                            {
                              label:
                                ConfigMap[app.config]?.label === '公开配置'
                                  ? '私有配置'
                                  : '公开配置',
                              icon: (
                                <SvgIcon
                                  name={ConfigMap[app.config]?.publishIcon as any}
                                  w="16px"
                                  h="16px"
                                />
                              ),
                              onClick: () => handleConfigClick(app)
                            }
                          ]
                        : [
                            {
                              label: '编辑信息',
                              icon: <SvgIcon name="edit" w="16px" h="16px" />,
                              onClick: () => onEditAppCenter(app)
                            },
                            {
                              label: '编辑编排',
                              icon: <SvgIcon name="appSetting" w="16px" h="16px" />,
                              onClick: () => onEditSetting(app)
                            },
                            {
                              label: '管理指令',
                              icon: <SvgIcon name="order" w="16px" h="16px" />,
                              onClick: () => handleOrderClick(app)
                            },
                            {
                              label: '管理工作流',
                              icon: <SvgIcon name="workflow" w="16px" h="16px" />,
                              onClick: () => handleWorkflowClick(app)
                            },
                            {
                              label:
                                ConfigMap[app.config]?.label === '公开配置'
                                  ? '私有配置'
                                  : '公开配置',
                              icon: (
                                <SvgIcon
                                  name={ConfigMap[app.config]?.publishIcon as any}
                                  w="16px"
                                  h="16px"
                                />
                              ),
                              onClick: () => handleConfigClick(app)
                            },
                            {
                              label: SortMap[app.sort].label === '置顶' ? '取消置顶' : '置顶',
                              icon: (
                                <SvgIcon
                                  name={SortMap[app.sort].publishIcon as any}
                                  w="16px"
                                  h="16px"
                                />
                              ),
                              onClick: () => handleSetTop(app)
                            },
                            {
                              label: AppStatusMap[AppStatusMap[app.status].updateStatus].label,
                              icon: (
                                <SvgIcon
                                  name={AppStatusMap[app.status].publishIcon as any}
                                  w="16px"
                                  h="16px"
                                />
                              ),
                              onClick: async () => {
                                try {
                                  await updateAppStatus({
                                    id: app.id,
                                    status: AppStatusMap[app.status].updateStatus
                                  });
                                  toast({
                                    title:
                                      AppStatusMap[app.status].label === '上线'
                                        ? '下线成功'
                                        : '上线成功',
                                    status: 'success'
                                  });
                                  refetch();
                                } catch (err: any) {
                                  toast({
                                    title: err?.message || '操作失败',
                                    status: 'error'
                                  });
                                }
                              }
                            },
                            {
                              label: '删除',
                              icon: <SvgIcon name="trash" w="16px" h="16px" />,
                              onClick: () => onDelete(app)
                            }
                          ]
                    }
                  />
                </Flex>
                <MyTooltip label={app.intro} overflowOnly>
                  <Box
                    flex={1}
                    className={'textEllipsis2'}
                    py={1}
                    wordBreak={'break-all'}
                    fontSize={respDims(14, 12)}
                    color={'myGray.500'}
                  >
                    {app.intro || '暂无介绍'}
                  </Box>
                </MyTooltip>
                <Box
                  mt={respDims(14, 10)}
                  color="#303133"
                  fontSize={respDims(14, 12)}
                  fontWeight="500"
                >
                  场景:
                </Box>

                <Box height={respDims(42, 46)}>
                  <LabelList
                    labelList={
                      app.labelList?.map((item) => ({
                        id: item.id,
                        content: `${item.tenantSceneName}-${item.tenantLabelName}`
                      })) || []
                    }
                    maxLines={1}
                  />
                </Box>
                <Flex justifyContent="space-between" alignItems="center" mt={respDims(8, 8)}>
                  <Box display="flex" alignItems="center">
                    <Tooltip label={app.mode === 1 ? '简单应用' : '高阶应用'}>
                      <Box>
                        <SvgIcon
                          name={app.mode === 1 ? 'appSimpleAgent' : 'appAdvancedAgent'}
                          w="18px"
                          h="18px"
                          mr="12px"
                        />
                      </Box>
                    </Tooltip>
                    <Tooltip label={ConfigMap[app.config]?.label}>
                      <Box>
                        <SvgIcon
                          name={ConfigMap[app.config]?.publishIcon as any}
                          w="18px"
                          h="18px"
                          color="#85888e"
                        />
                      </Box>
                    </Tooltip>
                  </Box>
                  <Box color="#909399" fontSize={respDims(14, 12)}>
                    {`${app.createUsername} 更新于${app.updateTime}`}
                  </Box>
                </Flex>
              </Flex>
            ))}
          </Grid>
        ) : (
          <Flex h="100%" justifyContent="center" flexDirection="column" alignItems="center">
            <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />

            <Box color="#909399" fontSize={respDims(14, 12)}>
              暂无数据
            </Box>
          </Flex>
        )}
      </Box>

      <Flex justifyContent="flex-end" mt={respDims(16)}>
        <Pagination
          current={current}
          pageSize={size}
          total={total}
          showSizeChanger
          pageSizeOptions={[12, 24, 36, 60]}
          onChange={(page, pageSize) => {
            setCurrent(page);
            setSize(pageSize);
          }}
        />
      </Flex>
    </Flex>
  );
};

export default forwardRef<SubPageAppRef, AppPageProps>(IndustryList);
