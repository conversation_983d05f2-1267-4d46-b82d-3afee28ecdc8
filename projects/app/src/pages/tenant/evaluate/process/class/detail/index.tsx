import { serviceSideProps } from '@/utils/i18n';
import { useEffect, useRef, useState } from 'react';
import { Box, Center, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import MySelect from '@/components/MySelect';
import SvgIcon from '@/components/SvgIcon';
import MyMenu from '@/components/MyMenu';
import Students, { StudentsRef } from './components/Students';
import SubTabs from '../../components/SubTabs';
import MainTabs from '../../components/MainTabs';
import Groups, { GroupsRef } from './components/Groups';
import Breadcrumb from '../../components/Breadcrumb';
import { useQuery } from '@tanstack/react-query';
import { getClazzIndactorList, getEvaluateSubjectList } from '@/api/tenant/evaluate/process';
import { deserializeData } from '@/utils/tools';

const viewTypes = [
  {
    label: '学生',
    value: 'student'
  },
  {
    label: '微团队',
    value: 'group'
  }
];

const studentViewTypes = [
  {
    label: '按学号顺序排列',
    value: 'code'
  },
  {
    label: '按座位排列',
    value: 'seat'
  }
];

const ClazzDetail = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId: defaultSubjectId
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName: string;
  subjectId?: string;
}) => {
  const [viewType, setViewType] = useState('student' as 'student' | 'group');

  const [studentViewType, setStudentViewType] = useState('code' as 'code' | 'seat');

  const studentsRef = useRef<StudentsRef>(null);

  const groupsRef = useRef<GroupsRef>(null);

  const [subjectId, setSubjectId] = useState(defaultSubjectId);

  const { data: subjects = [] } = useQuery(['subjects'], () =>
    getEvaluateSubjectList({ deptId: clazzId })
  );

  const { data: indactors = [] } = useQuery(
    ['indactors', gradeId, clazzId, subjectId],
    () => getClazzIndactorList({ gradeId, clazzId, subjectId: subjectId || '' }),
    {
      enabled: !!(gradeId && clazzId && subjectId)
    }
  );

  useEffect(() => {
    setSubjectId((state) => (state ? state : subjects[0]?.id || ''));
  }, [subjects]);

  return (
    <Flex
      direction="column"
      w="100%"
      h="100%"
      pt={respDims(24)}
      bgColor="#ffffff"
      borderRadius={respDims(20)}
      pos="relative"
    >
      <Breadcrumb
        mx={respDims(24)}
        list={[{ label: '班级列表' }, { label: clazzName }]}
        mb={respDims(18)}
      />

      <Flex align="center" mx={respDims(24)}>
        <MainTabs
          value={subjectId}
          list={subjects}
          labelKey="name"
          valueKey="id"
          onChange={setSubjectId}
        />

        {viewType === 'student' && (
          <>
            <Box flex="1"></Box>

            {studentViewType === 'seat' && isClazzTeacher && (
              <Box ml={respDims(16)}>
                <MyMenu
                  placement="left-start"
                  Button={
                    <Center
                      w={respDims('34fpx')}
                      h={respDims('34fpx')}
                      bgColor="rgba(0,0,0,0.03)"
                      borderRadius="50%"
                      cursor="pointer"
                    >
                      <SvgIcon name="settings" />
                    </Center>
                  }
                  menuList={[
                    {
                      label: '调整学生座位',
                      onClick: () => studentsRef.current?.showAdjustSeats()
                    },
                    {
                      label: '快速新增座位',
                      onClick: () => studentsRef.current?.showAddSeats()
                    }
                  ]}
                />
              </Box>
            )}

            <Box
              ml={respDims(16)}
              flex="0 0 auto"
              w={respDims(247)}
              {...(subjects.length > 0 ? {} : { pos: 'absolute', right: '20px', top: '65px' })}
            >
              <MySelect
                list={studentViewTypes}
                value={studentViewType}
                bgColor="rgba(0,0,0,0.03)"
                border="none"
                borderRadius={respDims(8)}
                onchange={setStudentViewType}
              />
            </Box>
          </>
        )}

        {viewType === 'group' && isClazzTeacher && (
          <Box ml="auto">
            <MyMenu
              Button={
                <Center
                  w={respDims('34fpx')}
                  h={respDims('34fpx')}
                  bgColor="rgba(0,0,0,0.03)"
                  borderRadius="50%"
                  cursor="pointer"
                >
                  <SvgIcon name="settings" />
                </Center>
              }
              menuList={[
                {
                  label: '调整学生分组',
                  onClick: () => groupsRef.current?.showAdjustGroups()
                },
                {
                  label: '快速新增分组',
                  onClick: () => groupsRef.current?.showAddGroups()
                }
              ]}
            />
          </Box>
        )}
      </Flex>

      <SubTabs
        alignSelf="self-start"
        mx={respDims(24)}
        mt={respDims(14)}
        value={viewType}
        list={viewTypes}
        onChange={setViewType}
      />

      <Box flex="1" mt={respDims(16)} overflow="hidden">
        {viewType === 'student' && (
          <Students
            ref={studentsRef}
            viewType={studentViewType}
            isClazzTeacher={isClazzTeacher}
            gradeId={gradeId}
            clazzId={clazzId}
            clazzName={clazzName}
            subjectId={subjectId}
            indactors={indactors}
          />
        )}
        {viewType === 'group' && (
          <Groups
            ref={groupsRef}
            isClazzTeacher={isClazzTeacher}
            gradeId={gradeId}
            clazzId={clazzId}
            clazzName={clazzName}
            subjectId={subjectId}
            indactors={indactors}
          />
        )}
      </Box>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default ClazzDetail;
