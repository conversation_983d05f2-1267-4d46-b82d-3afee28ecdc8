import { ChakraProps } from '@chakra-ui/system';
import { Box, Center, Checkbox, Flex, HStack } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import EvaluateModal from '../EvaluateModal';
import { Toast } from '@/utils/ui/toast';
import { MessageBox } from '@/utils/ui/messageBox';
import StudentList from '../../../../components/StudentList';
import SeatList from '../../../../components/SeatList';
import SeatCountModal from '../../../../components/SeatCountModal';
import AdjustSeatModal from '../../../../components/AdjustSeatModal';
import { EvaluateIndactorType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import {
  getClazzStudentListByCode,
  getClazzStudentListBySeat,
  getClazzStudentPage,
  resetClazzEvaluate
} from '@/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';

export type StudentsRef = {
  showAddSeats: () => void;

  showAdjustSeats: () => void;
};

const Students = (
  {
    viewType = 'code',
    isClazzTeacher,
    gradeId,
    clazzId,
    clazzName,
    subjectId,
    indactors,
    ...props
  }: {
    viewType?: 'code' | 'seat';
    isClazzTeacher?: boolean;
    gradeId: string;
    clazzId: string;
    clazzName: string;
    subjectId?: string;
    indactors?: EvaluateIndactorType[];
  } & ChakraProps,
  ref: ForwardedRef<StudentsRef>
) => {
  const [checkType, setCheckType] = useState<'evaluate' | 'reset' | ''>('');

  const [checkedIds, setCheckedIds] = useState<string[]>([]);

  const { openOverlay } = useOverlayManager();

  const {
    data: codeStudents,
    isFetched: isCodeStudentFetched,
    refetch: refetchCodeStudents
  } = useQuery(
    ['codeStudents', clazzId, subjectId],
    () =>
      getClazzStudentListByCode({
        clazzId,
        subjectId
      }),
    {
      enabled: !!(viewType === 'code' && clazzId && subjectId)
    }
  );

  const {
    data: seatStudents,
    isFetched: isSeatStudentFetched,
    refetch: refetchSeatStudents
  } = useQuery(
    ['seatStudents', clazzId, subjectId],
    () =>
      getClazzStudentListBySeat({
        clazzId,
        subjectId
      }),
    {
      enabled: !!(viewType === 'seat' && clazzId && subjectId)
    }
  );

  const students = (viewType === 'code' ? codeStudents : seatStudents) || [];

  const refetchStudents = viewType === 'code' ? refetchCodeStudents : refetchSeatStudents;

  const onEvaluate = (students: EvaluateStudentType[]) => {
    clazzId &&
      subjectId &&
      students.length &&
      openOverlay({
        Overlay: EvaluateModal,
        props: {
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          subjectId,
          students,
          indactors,
          onSuccess: () => {
            refetchStudents();
            setCheckedIds([]);
          }
        }
      });
  };

  const onClickStudent = (student: EvaluateStudentType) => onEvaluate([student]);

  const onClickSelectAll = () => {
    setCheckedIds((state) =>
      state.length && state.length === students.length ? [] : students.map((it) => it.id)
    );
  };

  const onClickEvaluate = () => {
    if (!checkedIds.length) {
      Toast.info('请先选择学生');
      return;
    }

    onEvaluate(students.filter((it) => checkedIds.includes(it.id)));
  };

  const onClickReset = () => {
    const ids = students.filter((it) => checkedIds.includes(it.id)).map((it) => it.id);
    clazzId &&
      subjectId &&
      ids.length &&
      MessageBox.confirm({
        title: '提示',
        content:
          '重新计算后表扬及待改进分统计将会清零，且不可恢复，点评记录不会删除，确定重新计算吗？',
        onOk: () => {
          resetClazzEvaluate({
            studentIds: ids,
            gradeId,
            clazzId,
            subjectId
          }).then(() => {
            refetchStudents();
            setCheckedIds([]);
          });
        }
      });
  };

  const onAdjustSeat = async (options?: {
    rowCount?: number;
    columnCount?: number;
    incRowCount?: number;
    incColumnCount?: number;
  }) => {
    let students = codeStudents;
    if (!isCodeStudentFetched) {
      students = await getListFromPage(getClazzStudentPage, { clazzId });
    }

    if (seatStudents?.length) {
      students?.forEach((student) => {
        const found = seatStudents.find((seatStudent) => seatStudent.id === student.id);
        if (found) {
          student.seatId = found.seatId;
          student.rowNo = found.rowNo;
          student.colNo = found.colNo;
        }
      });
    }

    openOverlay({
      Overlay: AdjustSeatModal,
      props: {
        clazzId,
        students: students || [],
        rowCount: options?.rowCount,
        columnCount: options?.columnCount,
        incRowCount: options?.incRowCount,
        incColumnCount: options?.incColumnCount,
        onSuccess: () => {
          refetchStudents();
        }
      }
    });
  };

  const onInitSeat = () => {
    openOverlay({
      Overlay: SeatCountModal,
      props: {
        onSuccess: async (props) => {
          const students = await getListFromPage(getClazzStudentPage, { clazzId });
          if (students.length <= props.columnCount * props.rowCount) {
            onAdjustSeat(props);
          } else {
            Toast.warning('当前座位数小于班级学生数量');
            onInitSeat();
          }
        }
      }
    });
  };

  const onCancelCheck = () => {
    setCheckType('');
    setCheckedIds([]);
  };

  useImperativeHandle(ref, () => ({
    showAddSeats: () => {
      openOverlay({
        Overlay: SeatCountModal,
        props: {
          onSuccess: ({ rowCount, columnCount }) =>
            onAdjustSeat({ incRowCount: rowCount, incColumnCount: columnCount })
        }
      });
    },
    showAdjustSeats: () => onAdjustSeat()
  }));

  useEffect(() => {
    onCancelCheck();
  }, [subjectId]);

  return (
    <Flex direction="column" w="100%" h="100%" {...props}>
      {viewType === 'code' && (
        <StudentList
          flex="1"
          overflowY="auto"
          type="evaluate"
          students={students}
          isCheckable={!!checkType}
          checkedIds={checkedIds}
          onCheckChange={setCheckedIds}
          onClickStudent={onClickStudent}
        />
      )}

      {viewType === 'seat' && (
        <>
          {students.length ? (
            <SeatList
              flex="1"
              mx={respDims(24)}
              type="evaluate"
              students={students}
              isCheckable={!!checkType}
              checkedIds={checkedIds}
              onCheckChange={setCheckedIds}
              onClickStudent={onClickStudent}
            />
          ) : (
            isSeatStudentFetched && (
              <Flex direction="column" flex="1" justify="center" align="center" mb="80px">
                <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
                <Box>班级暂未进行座位排列，需班主任安排</Box>
                {isClazzTeacher && (
                  <Box
                    mt={respDims(18)}
                    px={respDims(44)}
                    py={respDims(9)}
                    color="primary.500"
                    bgColor="primary.50"
                    fontSize={respDims('16fpx')}
                    lineHeight={respDims('22fpx')}
                    borderRadius={respDims(100)}
                    cursor="pointer"
                    _hover={{
                      bgColor: '#f3f5fe'
                    }}
                    onClick={onInitSeat}
                  >
                    快速设置座位
                  </Box>
                )}
              </Flex>
            )
          )}
        </>
      )}

      {!!indactors?.length && students.length > 0 && (
        <Flex
          h={respDims(78)}
          justify="center"
          align="center"
          borderTop="1px solid #E5E7EB"
          color="#4E5969"
          fontSize={respDims('16fpx')}
          lineHeight={respDims('22fpx')}
        >
          {!checkType ? (
            <>
              <Center
                w={respDims(200)}
                h={respDims(40)}
                fontWeight="bold"
                cursor="pointer"
                onClick={() => setCheckType('evaluate')}
              >
                <SvgIcon name="evaluationMutiple" />
                <Box ml={respDims(10)}>多选评分</Box>
              </Center>

              <Center
                w={respDims(200)}
                h={respDims(40)}
                fontWeight="bold"
                cursor="pointer"
                onClick={() => setCheckType('reset')}
              >
                <SvgIcon name="evaluationLoop" />
                <Box ml={respDims(10)}>重新记分</Box>
              </Center>
            </>
          ) : (
            <HStack spacing={respDims(16)}>
              <Center cursor="pointer" userSelect="none" onClick={onClickSelectAll}>
                <Box onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    colorScheme="primary"
                    size="lg"
                    isChecked={checkedIds.length > 0 && checkedIds.length === students.length}
                    onChange={onClickSelectAll}
                  />
                </Box>
                <Box ml={respDims(16)}>选择全部</Box>
              </Center>

              <Center
                w={respDims('292fpx')}
                h={respDims('40fpx')}
                color="primary.500"
                bgColor="primary.50"
                boxSizing="border-box"
                borderRadius={respDims(8)}
                cursor="pointer"
                onClick={() => (checkType === 'evaluate' ? onClickEvaluate() : onClickReset())}
              >
                {`${checkType === 'evaluate' ? '点评' : '重新计算'} (${checkedIds.length})`}
              </Center>

              <Center
                w={respDims('292fpx')}
                h={respDims('40fpx')}
                bgColor="#F9FAFB"
                boxSizing="border-box"
                borderRadius={respDims(8)}
                cursor="pointer"
                onClick={onCancelCheck}
              >
                取消
              </Center>
            </HStack>
          )}
        </Flex>
      )}
    </Flex>
  );
};

export default forwardRef(Students);
