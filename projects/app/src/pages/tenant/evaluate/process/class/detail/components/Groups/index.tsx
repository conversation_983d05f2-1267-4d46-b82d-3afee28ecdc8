import { ChakraProps } from '@chakra-ui/system';
import { Box, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import GroupCountModal from '../../../../components/GroupCountModal';
import AdjustGroupModal from '../../../../components/AdjustGroupModal';
import { ForwardedRef, forwardRef, useImperativeHandle } from 'react';
import { EvaluateGroupType, EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import GroupList from '../../../../components/GroupList';
import GroupEvaluateModal from '../../../../components/GroupEvaluateModal';
import EvaluateModal from '../EvaluateModal';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import { getGroupList } from '@/api/tenant/evaluate/process';

export type GroupsRef = {
  showAddGroups: () => void;
  showAdjustGroups: () => void;
};

const Groups = (
  {
    isClazzTeacher,
    gradeId,
    clazzId,
    clazzName,
    subjectId,
    indactors,
    ...props
  }: {
    isClazzTeacher?: boolean;
    gradeId: string;
    clazzId: string;
    clazzName?: string;
    subjectId?: string;
    indactors?: EvaluateIndactorType[];
  } & ChakraProps,
  ref: ForwardedRef<GroupsRef>
) => {
  const {
    data: groups = [],
    isFetched: isGroupsFetched,
    refetch: refetchGroups
  } = useQuery(['groups', clazzId, subjectId], () => getGroupList({ clazzId, subjectId }), {
    enabled: !!(clazzId && subjectId)
  });

  const { openOverlay } = useOverlayManager();

  const onAdjustGroup = (options?: { incCount?: number }) => {
    openOverlay({
      Overlay: AdjustGroupModal,
      props: {
        clazzId,
        incCount: options?.incCount,
        onSuccess: () => {
          refetchGroups();
        }
      }
    });
  };

  const onInitGroup = () => {
    openOverlay({
      Overlay: GroupCountModal,
      props: {
        onSuccess: (count) => onAdjustGroup({ incCount: count })
      }
    });
  };

  const onClickGroup = (group: EvaluateGroupType) => {
    if (!gradeId || !clazzId || !subjectId) {
      return;
    }

    if (indactors && !indactors.length) {
      openOverlay({
        Overlay: EvaluateModal,
        props: {
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          subjectId,
          students: group.students || [],
          indactors
        }
      });
      return;
    }

    openOverlay({
      Overlay: GroupEvaluateModal,
      props: {
        group,
        onEvaluate(students) {
          openOverlay({
            Overlay: EvaluateModal,
            props: {
              isClazzTeacher,
              gradeId,
              clazzId,
              clazzName,
              subjectId,
              students,
              indactors,
              onSuccess: () => {
                refetchGroups();
              }
            }
          });
        }
      }
    });
  };

  useImperativeHandle(ref, () => ({
    showAddGroups: () => {
      openOverlay({
        Overlay: GroupCountModal,
        props: {
          onSuccess: (count) => onAdjustGroup({ incCount: count })
        }
      });
    },
    showAdjustGroups: () => onAdjustGroup()
  }));

  return (
    <Flex direction="column" w="100%" h="100%" {...props}>
      {groups.length ? (
        <GroupList
          flex="1 0 0"
          groups={groups}
          mx={respDims(24)}
          mb={respDims(24)}
          overflowY="scroll"
          onClickGroup={onClickGroup}
        />
      ) : (
        isGroupsFetched && (
          <Flex direction="column" flex="1" justify="center" align="center" mb="80px">
            <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
            <Box>班级暂未进行团队分组，需班主任安排</Box>
            {isClazzTeacher && (
              <Box
                mt={respDims(18)}
                px={respDims(44)}
                py={respDims(9)}
                color="primary.500"
                bgColor="primary.50"
                fontSize={respDims('16fpx')}
                lineHeight={respDims('22fpx')}
                borderRadius={respDims(100)}
                cursor="pointer"
                _hover={{
                  bgColor: '#f3f5fe'
                }}
                onClick={onInitGroup}
              >
                快速随机分组
              </Box>
            )}
          </Flex>
        )
      )}
    </Flex>
  );
};

export default forwardRef(Groups);
