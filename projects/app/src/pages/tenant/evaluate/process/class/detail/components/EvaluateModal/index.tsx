import MyModal from '@/components/MyModal';
import { EvaluateIndactorType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { Box, Center, Flex, HStack, Image } from '@chakra-ui/react';
import SubTabs from '../../../../components/SubTabs';
import { useEffect, useMemo, useState } from 'react';
import EvaluateItem from '../EvaluateItem';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EvaluateEffect from '../EvaluateEffect';
import { addClazzEvaluate, getClazzIndactorList } from '@/api/tenant/evaluate/process';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { serializeData } from '@/utils/tools';
import { respDims } from '@/utils/chakra';
import { color } from 'framer-motion';

const EvaluateModal = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  students,
  indactors,
  onSuccess,
  onClose
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId: string;
  students: EvaluateStudentType[];
  indactors?: EvaluateIndactorType[];
  onSuccess?: (evaluate: EvaluateIndactorType) => void;
  onClose?: () => void;
}) => {
  const router = useRouter();

  const [activeTab, setActiveTab] = useState('');

  const [isClosing, setIsClosing] = useState(false);

  const { openOverlay } = useOverlayManager();

  const { data: queryIndactors } = useQuery(
    ['indactors', gradeId, clazzId, subjectId],
    () => getClazzIndactorList({ gradeId, clazzId, subjectId }),
    {
      enabled: !indactors
    }
  );

  const usedIndactors = indactors || queryIndactors;

  const tabList = useMemo(
    () =>
      usedIndactors?.map((it) => ({
        id: it.id,
        name: it.indactorName,
        items: it.subs
      })) || [],

    [usedIndactors]
  );

  const tabItems = useMemo(
    () => tabList.find((it) => it.id === activeTab)?.items || tabList[0]?.items || [],
    [tabList, activeTab]
  );

  const title = useMemo(() => `点评 ${students.map((it) => it.name).join('、')}`, [students]);

  const handleEvaluate = async (item: EvaluateIndactorType) => {
    await addClazzEvaluate({
      studentIds: students.map((it) => it.id),
      clazzId,
      subjectId,
      indactorId: item.id,
      evaluateType: item.evaluateType!,
      score: item.score!
    });

    setIsClosing(true);
    onSuccess?.(item);
    openOverlay({
      Overlay: EvaluateEffect,
      props: {
        item,
        students,
        onClose
      }
    });
  };

  useEffect(
    () => setActiveTab((state) => (state ? state : usedIndactors?.[0]?.id || '')),
    [usedIndactors]
  );

  const handleToClassPage = (tab: 'clazz' | 'student') =>
    router.replace({
      pathname: '/tenant/evaluate/process/class/record',
      query: {
        q: serializeData({
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          tab: tab
        })
      }
    });

  return (
    <MyModal
      isOpen={!isClosing}
      isCentered
      title={
        <Box ml="5px" maxW="12em" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
          {title}
        </Box>
      }
      maxW="684px"
      w="684px"
      h="473"
      onClose={onClose}
    >
      {usedIndactors && usedIndactors.length === 0 ? (
        <Center flexDir="column" w="100%" h="100%">
          <Flex align="flex-end">
            <Image src="/imgs/evaluate/evaluate_empty.png" alt="" w="24px" h="24px" />
            <Box ml="6px" color="#303133" fontSize="16px" lineHeight="22px">
              未设置评价指标，请先至评价规则中设置指标。
            </Box>
          </Flex>
          <HStack mt="38px" spacing="16px">
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() => handleToClassPage('clazz')}
            >
              班级表现
            </Center>
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() => handleToClassPage('student')}
            >
              学生表现
            </Center>
          </HStack>
        </Center>
      ) : (
        <Flex direction="column" w="100%" pl="28px" overflow="hidden">
          <Flex mt="24px" justifyContent="space-between" alignItems="center">
            <SubTabs
              labelKey="name"
              valueKey="id"
              value={activeTab}
              list={tabList}
              onChange={setActiveTab}
            />
            <Box
              mr={respDims(32)}
              fontSize={respDims(16, 14)}
              onClick={() => handleToClassPage('student')}
              color="#4E5969"
              cursor="pointer"
            >
              查看学生表现
            </Box>
          </Flex>

          <Flex flex="1" my="30px" align="center" mr="-84px" flexWrap="wrap" overflowY="auto">
            {tabItems.map((item, index) => (
              <EvaluateItem
                key={item.id}
                mr={(index + 1) % 5 ? '54.8px' : 0}
                mt={index >= 5 ? '30px' : 0}
                item={item}
                onClick={() => handleEvaluate(item)}
              />
            ))}
          </Flex>
        </Flex>
      )}
    </MyModal>
  );
};

export default EvaluateModal;
