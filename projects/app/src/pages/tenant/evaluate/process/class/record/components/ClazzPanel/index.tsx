import { ChakraProps, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useState } from 'react';
import Sidebar, { Navs, NavType } from './Sidebar';
import Stats from '../Stats';
import { RelTimeType } from '../../../../components/RelTimeSelect';
import Rank from './Rank';

const ClazzPanel = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  relTime,
  onSidebarChange,
  ...props
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName: string;
  subjectId?: string;
  relTime?: RelTimeType;
  onSidebarChange?: (nav: NavType) => void;
} & ChakraProps) => {
  const [nav, setNav] = useState<NavType>(Navs[0]);

  return (
    <Flex
      align="stretch"
      border="1px solid #E5E7EB"
      borderRadius={respDims(14)}
      overflow="hidden"
      {...props}
    >
      <Sidebar
        borderRight="1px solid 1px solid #E5E7EB"
        onChange={(e) => {
          setNav(e);
          onSidebarChange?.(e);
        }}
      />
      {nav.type === 'record' && (
        <Stats
          flex="1 0 0"
          type="clazz"
          title={clazzName}
          subTitle={relTime?.label}
          isClazzTeacher={isClazzTeacher}
          gradeId={gradeId}
          clazzId={clazzId}
          clazzName={clazzName}
          subjectId={subjectId}
          relTime={relTime}
        />
      )}
      {nav.type === 'rank' && (
        <Rank flex="1 0 0" clazzId={clazzId} subjectId={subjectId} relTime={relTime} />
      )}
    </Flex>
  );
};

export default ClazzPanel;
