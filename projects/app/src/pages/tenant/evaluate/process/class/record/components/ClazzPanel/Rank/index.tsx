import { getClazzEvaluateRankList } from '@/api/tenant/evaluate/process';
import MyTable from '@/components/MyTable';
import { IndactorTypeEnum, IndactorTypeNameMap } from '@/constants/api/tenant/evaluate/process';
import { RelTimeType } from '@/pages/tenant/evaluate/process/components/RelTimeSelect';
import { ClazzEvaluateRecordType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { TableProps } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useCallback, useMemo } from 'react';

const Rank = ({
  clazzId,
  subjectId,
  relTime,
  ...props
}: { clazzId: string; subjectId?: string; relTime?: RelTimeType } & ChakraProps) => {
  const query = useMemo(
    () => ({ clazzId, subjectId, ...relTime?.getTime() }),
    [clazzId, subjectId, relTime]
  );

  const columns: ColumnsType<ClazzEvaluateRecordType> = [
    {
      title: '学生姓名',
      key: 'studentName',
      dataIndex: 'studentName'
    },
    {
      title: '总分',
      key: 'score',
      dataIndex: 'score'
    },
    {
      title: '表扬',
      dataIndex: 'score',
      key: 'score',
      render: (_, record: ClazzEvaluateRecordType) => {
        const goodScores =
          record.scores?.filter(
            (item) => item.indactorName === IndactorTypeNameMap[IndactorTypeEnum.Good]
          ) || [];
        const totalGoodScore = goodScores.reduce((sum, item) => sum + (item.score || 0), 0);
        return <Box>{totalGoodScore}</Box>;
      }
    },
    {
      title: '待改进',
      dataIndex: 'score',
      key: 'score',
      render: (_, record) => {
        const badScores =
          record.scores?.filter(
            (item) => item.indactorName === IndactorTypeNameMap[IndactorTypeEnum.Bad]
          ) || [];
        const totalBadScore = badScores.reduce((sum, item) => sum + (item.score || 0), 0);
        return <Box>{totalBadScore}</Box>;
      }
    }
  ];

  const TableHeader = useCallback(() => {
    return (
      <Flex align="center">
        <Box
          w={respDims(7)}
          h={respDims('14fpx')}
          bgColor="#175DFF"
          borderRadius={respDims(8)}
        ></Box>
        <Box
          ml={respDims(6)}
          color="rgba(0,0,0,0.9)"
          fontSize={respDims('16fpx')}
          lineHeight={respDims('22fpx')}
          fontWeight="bold"
        >
          全部点评-排行榜
        </Box>
      </Flex>
    );
  }, []);

  return (
    <Box px={respDims(24)} py={respDims(10)} {...props}>
      <MyTable
        columns={columns}
        api={getClazzEvaluateRankList}
        defaultQuery={query}
        pageConfig={{ showPaginate: false }}
        headerConfig={{
          HeaderComponent: TableHeader
        }}
        boxStyle={{
          p: 0
        }}
        rowKey="studentId"
      />
    </Box>
  );
};

export default Rank;
