import { Box, Flex } from '@chakra-ui/react';
import Breadcrumb from '../../components/Breadcrumb';
import { respDims } from '@/utils/chakra';
import { indactorListType } from '@/types/api/tenant/evaluate/process';
import { serviceSideProps } from '@/utils/i18n';

const Indactor = () => {
  const groups: { label: string; list: indactorListType[] }[] = [
    {
      label: '表扬',
      list: [
        {
          id: '1',
          indactorName: '1',
          score: 1
        },
        {
          id: '2',
          indactorName: '2',
          score: 2
        }
      ]
    },
    {
      label: '待改进',
      list: [
        {
          id: '3',
          indactorName: '3',
          score: 44
        }
      ]
    }
  ];

  return (
    <Flex
      flexDir="column"
      w="100%"
      h="100%"
      borderRadius={respDims(20)}
      bgColor="#ffffff"
      p={respDims(24)}
    >
      <Breadcrumb list={[{ label: '班级列表' }, { label: '评价指标设置' }]} mb={respDims(18)} />
      <Box flex="1 0 0" overflow="auto">
        {groups.map((item) => (
          <Box key={item.label}>
            <Flex align="center">
              <Box
                w={respDims(7)}
                h={respDims('14fpx')}
                bgColor="#175DFF"
                borderRadius={respDims(8)}
              ></Box>
              <Box
                ml={respDims(6)}
                color="rgba(0,0,0,0.9)"
                fontSize={respDims('16fpx')}
                lineHeight={respDims('22fpx')}
                fontWeight="bold"
              >
                {item.label}
              </Box>
            </Flex>
            <Flex>hello world</Flex>
          </Box>
        ))}
      </Box>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Indactor;
