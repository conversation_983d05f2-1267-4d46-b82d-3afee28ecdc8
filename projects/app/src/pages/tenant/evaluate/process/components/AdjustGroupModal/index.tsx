import MyModal from '@/components/MyModal';
import { Box, Button, Flex } from '@chakra-ui/react';
import AdjustGroupList, { GroupListRef } from '../AdjustGroupList';
import { useRef } from 'react';
import { AddIcon } from '@chakra-ui/icons';
import { getAllGroupList, getClazzStudentPage, submitGroups } from '@/api/tenant/evaluate/process';
import { useQuery } from '@tanstack/react-query';
import { EvaluateGroupType } from '@/types/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';

const AdjustGroupModal = ({
  clazzId,
  incCount,
  onSuccess,
  onClose
}: {
  clazzId: string;
  incCount?: number;
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const groupListRef = useRef<GroupListRef>(null);

  const groupsRef = useRef<EvaluateGroupType[]>();

  const { data: groups = [] } = useQuery(['groups'], async () => {
    const groups = await getAllGroupList({ clazzId });
    const clazzStudents = await getListFromPage(getClazzStudentPage, { clazzId });
    const addCount = incCount || (groups.length ? 0 : 1);

    for (let i = 0; i < addCount; i++) {
      groups.push({
        id: 'new' + i,
        groupName: `第${groups.length + 1}组`
      });
    }

    groups.forEach((group) => {
      if (group.students) {
        group.students.forEach((student) => {
          const index = clazzStudents.findIndex((it) => it.id === student.id);
          if (index >= 0) {
            clazzStudents.splice(index, 1);
          }
        });
      } else {
        group.students = [];
      }
    });

    if (clazzStudents.length) {
      const emptyGroups = groups.filter((it) => !it.students?.length);
      if (!emptyGroups.length) {
        groups.push({
          id: '',
          groupName: `第${groups.length + 1}组`,
          students: []
        });
        emptyGroups.push(groups[groups.length - 1]);
      }
      while (clazzStudents.length) {
        emptyGroups.some((group) => {
          const student = clazzStudents.shift();
          if (!student) {
            return true;
          }
          group.students!.push(student);
          return false;
        });
      }
      groupsRef.current = groups;
    }

    return groups;
  });

  const onSubmit = async () => {
    if (groupsRef.current) {
      await submitGroups({
        clazzId,
        groups: groupsRef.current
          .map((it) => {
            if (it.id.includes('new')) {
              it.id = '';
            }
            return it;
          })
          .filter((it) => it.students?.length)
          .map((it) => ({
            id: it.id,
            groupName: it.groupName,
            studentIds: it.students?.length ? it.students?.map((it) => it.id) : undefined
          }))
      });
      onSuccess?.();
    }
    onClose?.();
  };

  return (
    <MyModal
      isOpen
      isCentered
      onClose={onClose}
      maxW="96vw"
      maxH="96vh"
      hideCloseButton
      closeOnOverlayClick={false}
    >
      <Flex flexDir="column" minW="80vw" minH="80vh" pt="16px" overflow="hidden">
        <Box alignSelf="center" color="#000000" fontSize="18px" fontWeight="bold" lineHeight="32px">
          微团队分组
        </Box>
        <Box alignSelf="center" my="8px" color="#606266" fontSize="18px" lineHeight="32px">
          可拖动学生名称到其他分组
        </Box>

        <AdjustGroupList
          ref={groupListRef}
          flex="1"
          mx="24px"
          groups={groups}
          onChange={(e) => {
            groupsRef.current = e;
          }}
        />

        <Flex align="center" mt="10px" px="24px" py="21px" borderTop="1px solid #E5E7EB">
          <Button
            variant="grayBase"
            w="150px"
            h="36px"
            borderRadius="150px"
            onClick={() => groupListRef.current?.addGroup(1)}
          >
            <AddIcon />
            <Box ml="10px">添加分组</Box>
          </Button>

          <Button
            ml="auto"
            variant="grayBase"
            w="150px"
            h="36px"
            borderRadius="150px"
            onClick={onClose}
          >
            取消
          </Button>

          <Button ml="16px" mr="auto" w="150px" h="36px" borderRadius="150px" onClick={onSubmit}>
            确定
          </Button>

          <Box w="150px"></Box>
        </Flex>
      </Flex>
    </MyModal>
  );
};

export default AdjustGroupModal;
