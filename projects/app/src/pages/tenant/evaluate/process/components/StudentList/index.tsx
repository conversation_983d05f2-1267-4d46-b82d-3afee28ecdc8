import { useMemo } from 'react';
import { Box, ChakraProps, Flex, useBreakpointValue } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import Student from '../Student';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';

const StudentList = ({
  type = 'normal',
  students,
  isCheckable,
  checkedIds,
  columnCount,
  onCheckChange,
  onClickStudent,
  ...props
}: {
  type?: 'normal' | 'evaluate';
  students: EvaluateStudentType[];
  isCheckable?: boolean;
  checkedIds?: string[];
  columnCount?: number;
  onCheckChange?: (checkedIds: string[]) => void;
  onClickStudent?: (student: EvaluateStudentType) => void;
} & ChakraProps) => {
  const breakpointColumnCount =
    useBreakpointValue({
      base: 5,
      sm: 6,
      md: 7,
      lg: 8,
      xl: 9
    }) || 8;

  const realColumnCount = columnCount || breakpointColumnCount;

  const rows = useMemo(() => {
    const rows: (EvaluateStudentType | undefined)[][] = [];
    for (let i = 0; i < students.length; i += realColumnCount) {
      rows.push(students.slice(i, i + realColumnCount));
    }
    const fillCount = realColumnCount - (students.length % realColumnCount);
    if (fillCount !== realColumnCount) {
      for (let i = 0; i < fillCount; i++) {
        rows[rows.length - 1].push(undefined);
      }
    }
    return rows;
  }, [students, realColumnCount]);

  const handleCheckChange = ({ id }: EvaluateStudentType, isChecked: boolean) => {
    const ids = isChecked
      ? [...(checkedIds || []), id]
      : checkedIds?.filter((it) => it !== id) || [];
    onCheckChange?.(ids);
  };

  return (
    <Box w="100%" h="100%" {...props}>
      {rows.map((row, rowIndex) => (
        <Flex key={rowIndex} justify="space-between" mb={respDims(24)}>
          {row.map((student, colIndex) =>
            student ? (
              <Student.Hover
                key={student.id}
                type={type}
                student={student}
                isCheckable={isCheckable}
                isClickCheck={true}
                isChecked={!!checkedIds?.includes(student.id)}
                ml={colIndex === 0 ? respDims(24) : 0}
                mr={colIndex === realColumnCount - 1 ? respDims(24) : 0}
                onCheckChange={handleCheckChange}
                onClick={onClickStudent}
              />
            ) : (
              <Box
                key={`col-${colIndex}`}
                w={Student.HoverBoxW}
                h="1px"
                mr={colIndex === realColumnCount - 1 ? respDims(24) : 0}
              />
            )
          )}
        </Flex>
      ))}
    </Box>
  );
};

export default StudentList;
