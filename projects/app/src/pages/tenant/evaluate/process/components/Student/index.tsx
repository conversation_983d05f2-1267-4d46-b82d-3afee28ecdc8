import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Box, Center, ChakraProps, Checkbox, Flex, Image } from '@chakra-ui/react';
import StudentAvatar from '../StudentAvatar';

type StudentProps = {
  type?: 'normal' | 'evaluate' | 'seat';
  student?: EvaluateStudentType;
  isCheckable?: boolean;
  isClickCheck?: boolean;
  isChecked?: boolean;
  avatarBgColor?: string;
  onCheckChange?: (student: EvaluateStudentType, isChecked: boolean) => void;
  onClick?: (student: EvaluateStudentType) => void;
} & ChakraProps;

const StudentBoxDms = '.8ms';
const StudentBoxW = respDims(80, StudentBoxDms);
const StudentHoverBoxW = respDims(80 + 34 * 2, StudentBoxDms);

const Student = ({
  type = 'normal',
  student,
  isCheckable,
  isClickCheck,
  isChecked,
  avatarBgColor = 'primary.50',
  onCheckChange,
  onClick,
  ...props
}: StudentProps) => {
  return (
    <Center
      w={StudentBoxW}
      onClick={() => {
        if (!student) {
          return;
        }
        if (isCheckable && isClickCheck) {
          onCheckChange?.(student, !isChecked);
        } else {
          onClick?.(student);
        }
      }}
      cursor="pointer"
      {...props}
    >
      <Flex w={StudentBoxW} direction="column" align="center" pos="relative">
        {type === 'seat' ? (
          <Image
            src="/imgs/evaluate/seat_empty.svg"
            w={StudentBoxW}
            h={StudentBoxW}
            alt=""
            borderRadius="50%"
            draggable={false}
          />
        ) : (
          <StudentAvatar
            student={student}
            w={StudentBoxW}
            h={StudentBoxW}
            borderRadius="50%"
            bgColor={avatarBgColor}
          />
        )}

        {type === 'evaluate' && !!student && (
          <Flex
            mt={respDims(-11, StudentBoxDms)}
            w="100%"
            h={respDims(26, StudentBoxDms)}
            align="center"
            bgColor="#FFF6CC"
            fontSize={respDims(14, StudentBoxDms)}
            lineHeight={respDims(22, StudentBoxDms)}
            borderRadius={respDims(50, StudentBoxDms)}
            pos="relative"
          >
            {student.score !== undefined ? (
              <Center flex="1 0 0" color={student.score == '0' ? '#606266' : '#71D4FF'}>
                {student.score}
              </Center>
            ) : (
              <>
                {!!student.good && (
                  <Center flex="1 0 0" color="#71D4FF" fontWeight="500">
                    {student.good}
                  </Center>
                )}

                {!!(student.good && student.bad) && <Box w="1px" h="100%" bgColor="#FFE984" />}

                {!!student.bad && (
                  <Center flex="1 0 0" color="#FF8471" fontWeight="500">
                    {student.bad}
                  </Center>
                )}

                {!student.good && !student.bad && (
                  <Center flex="1 0 0" color="#606266">
                    0
                  </Center>
                )}
              </>
            )}
          </Flex>
        )}

        <Center
          mt={respDims(6, StudentBoxDms)}
          w="100%"
          color="#303133"
          fontSize={respDims(16, StudentBoxDms)}
          fontWeight="bold"
          whiteSpace="nowrap"
        >
          {type === 'seat'
            ? '空位'
            : student
              ? student.code
                ? `${student.name}(${student.code.substring(student.code.length - 2)})`
                : student.name
              : '　'}
        </Center>

        {isCheckable && !!student && (
          <Box pos="absolute" top="0" right="0" onClick={(e) => e.stopPropagation()}>
            <Checkbox
              colorScheme="primary"
              size="lg"
              isChecked={isChecked}
              bgColor="rgba(255,255,255,0.9)"
              onChange={(e) => onCheckChange?.(student, e.target.checked)}
            />
          </Box>
        )}
      </Flex>
    </Center>
  );
};

Student.BoxW = StudentBoxW;

// eslint-disable-next-line react/display-name
Student.Hover = (props: StudentProps) => {
  return (
    <Student
      {...props}
      w={StudentHoverBoxW}
      py={respDims(14, StudentBoxDms)}
      borderRadius={respDims(14, StudentBoxDms)}
      boxSizing="border-box"
      border="1px solid transparent"
      {...(props.type !== 'seat'
        ? {
            _hover: {
              boxShadow: '0px 1px 11px 0px rgba(0,0,0,0.09)',
              border: '1px solid #D6E4FF'
            }
          }
        : { cursor: 'default' })}
    />
  );
};

Student.HoverBoxW = StudentHoverBoxW;

export default Student;
