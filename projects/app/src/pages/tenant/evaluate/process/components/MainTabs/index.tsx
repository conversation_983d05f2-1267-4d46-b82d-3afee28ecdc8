import { respDims } from '@/utils/chakra';
import { Box, Center, Flex } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import { Fragment } from 'react';

const MainTabs = <DT extends Record<PropertyKey, any>, VT extends any = string>({
  list,
  value,
  labelKey = 'label' as keyof DT,
  valueKey = 'value' as keyof DT,
  onChange,
  ...props
}: {
  list?: DT[];
  value?: VT;
  labelKey?: keyof DT;
  valueKey?: keyof DT;
  onChange?: (value: VT) => void;
} & ChakraProps) => {
  return (
    <Flex
      align="center"
      flexWrap="wrap"
      bgColor="#F9FAFB"
      px={respDims(6)}
      py={respDims(4)}
      borderRadius={respDims(8)}
      {...props}
    >
      {list?.map((item, index) => (
        <Fragment key={item[valueKey]}>
          {index > 0 && <Box w="1px" h="14px" mx={respDims(15)} bgColor={'#e5e6eb'}></Box>}

          <Center
            flex="0 0 1"
            minW={respDims('80fpx')}
            px={respDims(12)}
            py={respDims(9)}
            fontSize={respDims('16fpx')}
            lineHeight={respDims('22fpx')}
            {...(item[valueKey] === value
              ? { color: '#165ddf', bgColor: '#ffffff', fontWeight: 'bold' }
              : { color: '#4e5969' })}
            borderRadius={respDims(6)}
            cursor="pointer"
            userSelect="none"
            whiteSpace="nowrap"
            onClick={() => item[valueKey] !== value && onChange?.(item[valueKey])}
          >
            {item[labelKey]}
          </Center>
        </Fragment>
      ))}
    </Flex>
  );
};

export default MainTabs;
