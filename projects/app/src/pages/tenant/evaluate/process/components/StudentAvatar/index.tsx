import { SexEnum } from '@/constants/api/tenant/evaluate/process';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { ChakraProps, Image } from '@chakra-ui/react';
import { useEffect, useState } from 'react';

const defaultAvatars = {
  [SexEnum.Male]: [
    '/imgs/evaluate/avatar/boy0.png',
    '/imgs/evaluate/avatar/boy1.png',
    '/imgs/evaluate/avatar/boy2.png',
    '/imgs/evaluate/avatar/boy3.png',
    '/imgs/evaluate/avatar/boy4.png'
  ],
  [SexEnum.Female]: [
    '/imgs/evaluate/avatar/girl0.png',
    '/imgs/evaluate/avatar/girl1.png',
    '/imgs/evaluate/avatar/girl2.png',
    '/imgs/evaluate/avatar/girl3.png',
    '/imgs/evaluate/avatar/girl4.png'
  ]
};

const generateAvatar = (code?: string, name?: string, sex?: SexEnum) => {
  const str = name || code || '';
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const sexAvatars = defaultAvatars[sex || (hash % 2 ? SexEnum.Male : SexEnum.Female)];
  return sexAvatars[Math.abs(hash) % sexAvatars.length];
};

const StudentAvatar = ({
  student,
  code,
  name,
  sex,
  avatarUrl,
  ...props
}: {
  student?: EvaluateStudentType;
  code?: string;
  name?: string;
  avatarUrl?: string;
  sex?: SexEnum;
} & ChakraProps) => {
  code = student?.code || code;
  name = student?.name || name;
  sex = student?.sex || sex;
  avatarUrl = student?.avatarUrl || avatarUrl;

  const [fallbackUrl, setFallbackUrl] = useState('');

  useEffect(() => {
    if (avatarUrl) {
      setFallbackUrl('');
    } else {
      setFallbackUrl(generateAvatar(code, name, sex));
    }
  }, [code, name, sex, avatarUrl]);

  return (
    <Image
      src={fallbackUrl || avatarUrl}
      alt=""
      draggable={false}
      borderRadius="50%"
      {...props}
      onError={() => setFallbackUrl(generateAvatar(code, name, sex))}
    />
  );
};

export default StudentAvatar;
