import MyModal from '@/components/MyModal';
import { EvaluateGroupType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import StudentList from '../StudentList';
import { Box, Center, Checkbox, Flex } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { respDims } from '@/utils/chakra';

const GroupEvaluateModal = ({
  group,
  onEvaluate,
  onClose
}: {
  group: EvaluateGroupType;
  onEvaluate?: (students: EvaluateStudentType[]) => void;
  onClose?: () => void;
}) => {
  const [checkedIds, setCheckedIds] = useState<string[]>([]);

  const students = group.students || [];

  const onClickSelectAll = () => {
    if (checkedIds.length && checkedIds.length === students.length) {
      setCheckedIds([]);
    } else {
      setCheckedIds(students.map((it) => it.id));
    }
  };

  const handleEvaluate = () => {
    const list = students.filter((it) => checkedIds.includes(it.id));
    if (list.length) {
      onEvaluate?.(list);
    }
    onClose?.();
  };

  useEffect(() => {
    setCheckedIds((state) => (state.length ? state : students.map((it) => it.id)));
  }, [students]);

  return (
    <MyModal
      title={group.groupName}
      isOpen
      isCentered
      onClose={onClose}
      bgImage="/imgs/evaluate/modal_bg.png"
      bgSize="100% 40%"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent',
        borderBottom: '1px solid #E5E7EB'
      }}
    >
      <Flex h="390px" flexDir="column" mt="23px">
        <StudentList
          flex="1 0 0"
          overflowY="auto"
          columnCount={4}
          students={students || []}
          isCheckable
          checkedIds={checkedIds}
          onCheckChange={setCheckedIds}
        />
        <Center h="78px" borderTop="1px solid #E5E7EB" pos="relative">
          <Center
            cursor="pointer"
            userSelect="none"
            pos="absolute"
            left="32px"
            top="0"
            bottom="0"
            h="22px"
            my="auto"
            onClick={onClickSelectAll}
          >
            <Box onClick={(e) => e.stopPropagation()}>
              <Checkbox
                colorScheme="primary"
                size="lg"
                isChecked={checkedIds.length > 0 && checkedIds.length === students.length}
                onChange={onClickSelectAll}
              />
            </Box>
            <Box ml="16px">全选</Box>
          </Center>

          <Center
            bgImage="linear-gradient(90deg, #28D3FF 0%, #2072FF 100%)"
            px="42px"
            py="7px"
            borderRadius="100px"
            color="#ffffff"
            fontSize="14px"
            lineHeight="22px"
            cursor="pointer"
            onClick={() => handleEvaluate()}
          >
            给小组发送点评
          </Center>
        </Center>
      </Flex>
    </MyModal>
  );
};

export default GroupEvaluateModal;
