import SvgIcon from '@/components/SvgIcon';
import { EvaluateGroupType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Box, Center, ChakraProps, Flex } from '@chakra-ui/react';
import { useMemo } from 'react';
import StudentAvatar from '../StudentAvatar';

const GroupBoxDms = '.8ms';

const Group = ({
  group,
  onClick,
  ...props
}: { group: EvaluateGroupType; onClick?: (group: EvaluateGroupType) => void } & ChakraProps) => {
  const avatarCodes = useMemo(() => {
    const codes = group.studentIds?.slice(0, 3).map(String) || [];
    if (codes.length < 3) {
      codes.push(String(group.studentNum));
    }
    if (codes.length < 3) {
      codes.push(group.groupName);
    }
    if (codes.length < 3) {
      codes.push(String(group.id));
    }
    return codes;
  }, [group]);

  return (
    <Box
      border="1px solid #E5E7EB"
      borderRadius={respDims(8, GroupBoxDms)}
      boxShadow="0px 2px 4px 0px rgba(75,86,115,0.07)"
      onClick={() => onClick?.(group)}
      cursor="pointer"
      {...props}
    >
      <Flex
        h={respDims(106, GroupBoxDms)}
        px={respDims(20, GroupBoxDms)}
        align="center"
        overflow="hidden"
        borderBottom="1px solid #E5E7EB"
      >
        <Flex align="center" w={respDims(56 * 2, GroupBoxDms)}>
          {avatarCodes.map((code, index) => (
            <StudentAvatar
              key={index}
              code={code}
              w={respDims(56, GroupBoxDms)}
              h={respDims(56, GroupBoxDms)}
              pos="relative"
              left={index > 0 ? respDims(-28 * index, GroupBoxDms) : 0}
              bgColor="primary.50"
              border="1px solid #ADC8FF"
              zIndex={3 - index}
            />
          ))}
        </Flex>

        <Box flex="1" ml={respDims(8, GroupBoxDms)}>
          <Box
            color="#000000"
            fontSize={respDims(16, GroupBoxDms)}
            fontWeight="bold"
            lineHeight={respDims(24, GroupBoxDms)}
          >
            {group.groupName}
          </Box>

          <Box
            mt={respDims(8, GroupBoxDms)}
            color="#606266"
            fontSize={respDims(15, GroupBoxDms)}
            lineHeight={respDims(20, GroupBoxDms)}
          >
            组员：{group.studentNum}人
          </Box>
        </Box>
      </Flex>

      <Flex
        align="center"
        h={respDims(68, GroupBoxDms)}
        color="#606266"
        fontSize={respDims(14, GroupBoxDms)}
      >
        <Center flex="1 0 0">
          <SvgIcon name="emojiSmile" w={respDims(28, GroupBoxDms)} h={respDims(28, GroupBoxDms)} />
          <Box ml={respDims(6, GroupBoxDms)}>{group.good || 0}</Box>
        </Center>

        <Box w="1px" bgColor="#E5E7EB" h={respDims(22, GroupBoxDms)} />

        <Center flex="1 0 0">
          <SvgIcon name="emojiAngry" w={respDims(28, GroupBoxDms)} h={respDims(28, GroupBoxDms)} />
          <Box ml={respDims(6, GroupBoxDms)}>{group.bad || 0}</Box>
        </Center>
      </Flex>
    </Box>
  );
};

export default Group;
