import { addHomework, updateHomework } from '@/api/tenant/evaluate/process';
import MyModal from '@/components/MyModal';
import { EvaluateHomeworkType } from '@/types/api/tenant/evaluate/process';
import { Box, Button, HStack } from '@chakra-ui/react';
import { Calendar } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useMemo, useState } from 'react';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import weekday from 'dayjs/plugin/weekday';
import 'dayjs/locale/zh-cn';

dayjs.extend(customParseFormat as any);
dayjs.extend(advancedFormat as any);
dayjs.extend(weekday as any);
dayjs.extend(localeData as any);
dayjs.extend(weekOfYear as any);
dayjs.extend(weekYear as any);

const HomeworkModal = ({
  clazzId,
  subjectId,
  homework,
  generateName,
  onSuccess,
  onClose
}: {
  clazzId: string;
  subjectId: string;
  homework?: EvaluateHomeworkType;
  generateName?: (date: Dayjs, homework?: EvaluateHomeworkType) => string;
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const defaultValue = useMemo(() => {
    if (!homework?.homeworkName) {
      return undefined;
    }
    const m = /\d{8}/.exec(homework.homeworkName);

    return m
      ? dayjs(`${m[0].substring(0, 4)}-${m[0].substring(4, 6)}-${m[0].substring(6)}`)
      : undefined;
  }, [homework?.homeworkName]);

  const [date, setDate] = useState<Dayjs>();

  const onSubmit = async () => {
    if (generateName) {
      const homeworkName = generateName(date || dayjs(), homework);
      if (homeworkName !== homework?.homeworkName) {
        if (homework?.id) {
          await updateHomework({
            id: homework.id,
            clazzId: homework.clazzId!,
            subjectId: homework.subjectId!,
            homeworkName
          });
        } else {
          await addHomework({
            clazzId,
            subjectId,
            homeworkName
          });
        }
        onSuccess?.();
      }
    }
    onClose?.();
  };

  return (
    <MyModal title="选择作业日期" isOpen onClose={onClose}>
      <Box
        p="24px"
        pt="12px"
        css={{
          '.ant-picker-calendar-mode-switch': {
            display: 'none'
          }
        }}
      >
        <Calendar
          fullscreen={false}
          defaultValue={defaultValue}
          onChange={(date) => setDate(date)}
        />
        <HStack justify="flex-end" mt="24px" spacing="16px">
          <Button variant="grayBase" onClick={onClose}>
            取消
          </Button>
          <Button onClick={onSubmit}>确定</Button>
        </HStack>
      </Box>
    </MyModal>
  );
};

export default HomeworkModal;
