import { serviceSideProps } from '@/utils/i18n';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Center, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import MySelect from '@/components/MySelect';
import SvgIcon from '@/components/SvgIcon';
import MyMenu from '@/components/MyMenu';
import Students, { StudentsRef } from './components/Students';
import MainTabs from '../../components/MainTabs';
import Breadcrumb from '../../components/Breadcrumb';
import { getEvaluateSubjectList, getHomeworkIndactorList } from '@/api/tenant/evaluate/process';
import { useQuery } from '@tanstack/react-query';
import { deserializeData } from '@/utils/tools';

const studentViewTypes = [
  {
    label: '按学号顺序排列',
    value: 'code'
  },
  {
    label: '按座位排列',
    value: 'seat'
  }
];

const HomeworkDetail = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName: string;
}) => {
  const [studentViewType, setStudentViewType] = useState('code' as 'code' | 'seat');

  const studentsRef = useRef<StudentsRef>(null);

  const [subjectId, setSubjectId] = useState('');

  const { data: subjects = [] } = useQuery(['subjects'], () =>
    getEvaluateSubjectList({ deptId: clazzId })
  );

  const { data: indactors = [] } = useQuery(
    ['indactors', gradeId, clazzId, subjectId],
    () =>
      getHomeworkIndactorList({
        gradeId,
        clazzId,
        subjectId
      }),
    {
      enabled: !!(gradeId && clazzId && subjectId)
    }
  );

  const subject = useMemo(() => subjects.find((it) => it.id === subjectId), [subjects, subjectId]);

  useEffect(() => {
    setSubjectId((state) => (state ? state : subjects[0]?.id || ''));
  }, [subjects]);

  return (
    <Flex
      direction="column"
      w="100%"
      h="100%"
      pt={respDims(24)}
      bgColor="#ffffff"
      borderRadius={respDims(20)}
    >
      <Breadcrumb
        mx={respDims(24)}
        list={[{ label: '班级列表' }, { label: clazzName }]}
        mb={respDims(18)}
      />

      <Flex align="center" mx={respDims(24)}>
        <MainTabs
          value={subjectId}
          list={subjects}
          labelKey="name"
          valueKey="id"
          onChange={setSubjectId}
        />

        {
          <>
            <Box flex="1"></Box>

            {studentViewType === 'seat' && isClazzTeacher && (
              <Box ml={respDims(16)}>
                <MyMenu
                  placement="left-start"
                  Button={
                    <Center
                      w={respDims('34fpx')}
                      h={respDims('34fpx')}
                      bgColor="rgba(0,0,0,0.03)"
                      borderRadius="50%"
                      cursor="pointer"
                    >
                      <SvgIcon name="settings" />
                    </Center>
                  }
                  menuList={[
                    {
                      label: '调整学生座位',
                      onClick: () => studentsRef.current?.showAdjustSeats()
                    },
                    {
                      label: '快速新增座位',
                      onClick: () => studentsRef.current?.showAddSeats()
                    }
                  ]}
                />
              </Box>
            )}

            <Box ml={respDims(16)} flex="0 0 auto" w={respDims(247)}>
              <MySelect
                list={studentViewTypes}
                value={studentViewType}
                bgColor="rgba(0,0,0,0.03)"
                border="none"
                borderRadius={respDims(8)}
                onchange={setStudentViewType}
              />
            </Box>
          </>
        }
      </Flex>

      <Box flex="1" pt={respDims(16)} overflow="hidden" pos="relative">
        <Students
          ref={studentsRef}
          flex="1 0 0"
          viewType={studentViewType}
          isClazzTeacher={isClazzTeacher}
          gradeId={gradeId}
          clazzId={clazzId}
          clazzName={clazzName}
          subjectId={subjectId}
          subjectName={subject?.name}
          indactors={indactors}
        />
      </Box>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default HomeworkDetail;
