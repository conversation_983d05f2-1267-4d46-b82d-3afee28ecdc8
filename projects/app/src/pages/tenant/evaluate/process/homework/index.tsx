import { getHomeworkEvaluateClazzList } from '@/api/tenant/evaluate/process';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { useUserStore } from '@/store/useUserStore';
import { respDims } from '@/utils/chakra';
import { serviceSideProps } from '@/utils/i18n';
import { serializeData } from '@/utils/tools';
import { Box, Center, Flex, Grid, MenuButton } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';

const HomeworkShow = () => {
  const router = useRouter();

  const { userInfo } = useUserStore();

  const { data: clazzes = [], isFetched: isClazzesFetched } = useQuery(['clazzes'], () =>
    getHomeworkEvaluateClazzList()
  );

  return (
    <Box
      w="100%"
      h="100%"
      bgColor="#F8FAFC"
      py={respDims(12)}
      borderRadius={respDims(20)}
      border="2px solid #FFFFFF"
    >
      <Box w="100%" h="100%" overflow="auto" px={respDims(24)} py={respDims(12)}>
        {!!clazzes?.length ? (
          <Grid
            gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
            gridGap={respDims(18)}
          >
            {clazzes.map((clazz) => (
              <Box
                key={clazz.id}
                bgColor="#ffffff"
                border="1px solid #E5E7EB"
                borderRadius={respDims(8)}
                boxShadow="0px 2px 4px 0px rgba(75,86,115,0.07)"
                cursor="pointer"
                overflow="hidden"
                onClick={() =>
                  router.push({
                    pathname: '/tenant/evaluate/process/homework/detail',
                    query: {
                      q: serializeData({
                        isClazzTeacher: clazz.teachers?.some((it) => it.id == userInfo?.tmbId),
                        gradeId: clazz.parentId,
                        clazzId: clazz.id,
                        clazzName: clazz.clazzName
                      })
                    }
                  })
                }
              >
                <Flex
                  h={respDims(50, '.9ms')}
                  px={respDims(20)}
                  justify="space-between"
                  align="center"
                  borderBottom="1px solid #E5E7EB"
                >
                  <Box
                    color="#000000"
                    fontSize={respDims('16fpx')}
                    fontWeight="bold"
                    overflow="hidden"
                    textOverflow="ellipsis"
                  >
                    {clazz.clazzName}
                  </Box>

                  <MyMenu
                    trigger="hover"
                    offset={[20, 0]}
                    width={20}
                    Button={
                      <MenuButton
                        w={respDims('20fpx')}
                        h={respDims('20fpx')}
                        borderRadius={respDims(4)}
                        _hover={{
                          bgColor: '#f3f4f6'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Center>
                          <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                        </Center>
                      </MenuButton>
                    }
                    menuList={[
                      {
                        icon: <SvgIcon name="emojiClassPerformance" />,
                        label: '班级作业记录',
                        onClick: () =>
                          router.push({
                            pathname: '/tenant/evaluate/process/homework/record',
                            query: {
                              q: serializeData({
                                isClazzTeacher: clazz.teachers?.some(
                                  (it) => it.id == userInfo?.tmbId
                                ),
                                gradeId: clazz.parentId,
                                clazzId: clazz.id,
                                clazzName: clazz.clazzName,
                                tab: 'clazz'
                              })
                            }
                          })
                      },
                      {
                        icon: <SvgIcon name="emojiStudentPerformance" />,
                        label: '学生作业记录',
                        onClick: () =>
                          router.push({
                            pathname: '/tenant/evaluate/process/homework/record',
                            query: {
                              q: serializeData({
                                isClazzTeacher: clazz.teachers?.some(
                                  (it) => it.id == userInfo?.tmbId
                                ),
                                gradeId: clazz.parentId,
                                clazzId: clazz.id,
                                clazzName: clazz.clazzName,
                                tab: 'student'
                              })
                            }
                          })
                      }
                    ]}
                  />
                </Flex>

                <Box
                  maxW="100%"
                  mx={respDims(20)}
                  mt={respDims(16)}
                  color="#000000"
                  fontSize={respDims('16fpx')}
                  fontWeight="bold"
                  lineHeight={respDims('24fpx')}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  whiteSpace="nowrap"
                >
                  班主任: {clazz.teacherNames}
                </Box>

                <Flex
                  mx={respDims(20)}
                  mt={respDims(14)}
                  mb={respDims(20)}
                  align="center"
                  color="#606266"
                  fontSize={respDims('15fpx')}
                  lineHeight={respDims('20fpx')}
                >
                  <Box>学生:</Box>
                  <Center ml={respDims(4)} minW="1.5em">
                    {clazz.studentNum || 0}
                  </Center>

                  <Box w="1px" h={respDims('16fpx')} mx={respDims(14)} bgColor="#606266" />

                  <Box>作业数量:</Box>

                  <Box ml={respDims(6)} fontSize={respDims('14fpx')}>
                    {clazz.homeworkNum || 0}
                  </Box>
                </Flex>
              </Box>
            ))}
          </Grid>
        ) : (
          isClazzesFetched && (
            <Flex flexDir="column" justify="center" align="center" w="100%" h="100%">
              <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
              <Box>暂无数据</Box>
            </Flex>
          )
        )}
      </Box>
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}
export default HomeworkShow;
