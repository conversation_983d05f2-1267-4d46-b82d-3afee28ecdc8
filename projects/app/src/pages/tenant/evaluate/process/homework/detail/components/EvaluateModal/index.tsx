import { addHomeworkEvaluate, getHomeworkIndactorList } from '@/api/tenant/evaluate/process';
import { getScoreLevelValueList } from '@/api/tenant/evaluate/rule';
import MyModal from '@/components/MyModal';
import MyBox from '@/components/common/MyBox';
import { EvaluateType } from '@/constants/api/tenant/evaluate/rule';
import { EvaluateIndactorType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { EvaluaScoreLevelValue } from '@/types/api/tenant/evaluate/rule';
import { respDims } from '@/utils/chakra';
import { serializeData } from '@/utils/tools';
import { Toast } from '@/utils/ui/toast';
import {
  Box,
  Button,
  Center,
  Flex,
  HStack,
  Image,
  NumberInput,
  NumberInputField
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';

const EvaluateModal = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  homeworkId,
  students,
  indactors,
  onSuccess,
  onClose
}: {
  students: EvaluateStudentType[];
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId: string;
  homeworkId: string;
  indactors?: EvaluateIndactorType[];
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const router = useRouter();

  const [levelValue, setLevelValue] = useState<EvaluaScoreLevelValue>();

  const [score, setScore] = useState<number>();

  const title = useMemo(() => `评分 ${students.map((it) => it.name).join('、')}`, [students]);

  const { data: queryIndactors, isLoading } = useQuery(
    ['indactors'],
    () =>
      getHomeworkIndactorList({
        gradeId,
        clazzId,
        subjectId
      }),
    {
      enabled: !indactors
    }
  );

  const usedIndactors = indactors || queryIndactors;

  const indactor = usedIndactors?.[0];

  const { data: levelValues } = useQuery(
    ['levelValues', indactor?.evaluateType],
    async () => getScoreLevelValueList({ scoreLevelId: indactor?.scoreLevelId }),
    {
      enabled: indactor?.evaluateType === EvaluateType.Grade
    }
  );

  const onSubmit = async () => {
    if (!indactor) {
      return;
    }
    try {
      let result;
      if (indactor.evaluateType === EvaluateType.Grade) {
        if (!levelValue) {
          Toast.error('请选择等级');
          return;
        }
        result = await addHomeworkEvaluate({
          clazzId,
          subjectId,
          homeworkId,
          indactorId: indactor?.id!,
          evaluateType: indactor.evaluateType,
          scoreLevelId: levelValue.scoreLevelId,
          scoreLevelValue: levelValue.name,
          scoreLevelValueId: levelValue.id,
          studentIds: students.map((it) => it.id)
        });
      } else {
        if (score === undefined) {
          Toast.error('请输入评分');
          return;
        }
        result = await addHomeworkEvaluate({
          clazzId,
          subjectId,
          homeworkId,
          indactorId: indactor.id!,
          evaluateType: indactor.evaluateType!,
          score,
          studentIds: students.map((it) => it.id)
        });
      }

      // 处理返回的结果
      if (result && result.length > 0) {
        const alreadyEvaluatedNames = result.map((item) => item.name).join('、');
        Toast.info(`以下学生在周期内已经评价过: ${alreadyEvaluatedNames}`);
      }

      onSuccess?.();
      onClose?.();
    } catch (error) {
      console.error('评分提交失败:', error);
      Toast.error('评分提交失败，请稍后重试');
    }
  };

  useEffect(() => {
    const stat =
      students.length === 1 && students[0].homeworkEvaluas?.length
        ? students[0].homeworkEvaluas[students[0].homeworkEvaluas.length - 1]
        : undefined;

    if (indactor?.evaluateType === EvaluateType.Grade) {
      const levelValueId =
        stat?.evaluateType === EvaluateType.Grade ? stat.scoreLevelValueId : indactor.scoreLevel;
      const levelValue = levelValues?.find((it) => it.id == levelValueId);
      setLevelValue(levelValue);
    } else if (indactor?.evaluateType === EvaluateType.Score) {
      setScore(stat?.evaluateType === EvaluateType.Score ? stat.score : indactor?.score);
    }
  }, [indactor, levelValues, students]);

  return (
    <MyModal
      isOpen
      isCentered
      title={
        <Box maxW="12em" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
          {title}
        </Box>
      }
      onClose={onClose}
    >
      {usedIndactors && usedIndactors.length === 0 ? (
        <Center flexDir="column" w="600px" h="400px">
          <Flex align="flex-end">
            <Image src="/imgs/evaluate/evaluate_empty.png" alt="" w="24px" h="24px" />
            <Box ml="6px" color="#303133" fontSize="16px" lineHeight="22px">
              未设置评价指标，请先至评价规则中设置指标。
            </Box>
          </Flex>
          <HStack mt="38px" spacing="16px">
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() =>
                router.replace({
                  pathname: '/tenant/evaluate/process/homework/record',
                  query: {
                    q: serializeData({
                      isClazzTeacher,
                      gradeId,
                      clazzId,
                      clazzName,
                      tab: 'clazz'
                    })
                  }
                })
              }
            >
              班级作业记录
            </Center>
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() =>
                router.replace({
                  pathname: '/tenant/evaluate/process/homework/record',
                  query: {
                    q: serializeData({
                      isClazzTeacher,
                      gradeId,
                      clazzId,
                      clazzName,
                      tab: 'student'
                    })
                  }
                })
              }
            >
              学生作业记录
            </Center>
          </HStack>
        </Center>
      ) : (
        <Box px={respDims(32)} pb={respDims(24)}>
          <MyBox
            display="flex"
            isLoading={!indactors && isLoading}
            h={respDims(120)}
            flexDir="column"
            justifyContent="center"
          >
            {!indactor && <Center>暂无评分指标</Center>}

            {indactor?.evaluateType === EvaluateType.Grade && !!levelValues?.length && (
              <>
                <Box>等级</Box>
                <HStack mt={respDims(10)} spacing={respDims(24)}>
                  {levelValues.map((it) => (
                    <Center
                      key={it.id}
                      minW={respDims('32fpx')}
                      minH={respDims('32fpx')}
                      fontWeight="bold"
                      borderWidth="1px"
                      borderStyle="solid"
                      borderRadius="50%"
                      fontSize={respDims('15fpx')}
                      lineHeight={respDims('15fpx')}
                      cursor="pointer"
                      {...(it.id === levelValue?.id
                        ? {
                            color: 'primary.500',
                            bgColor: 'primary.50',
                            borderColor: 'primary.500'
                          }
                        : {
                            color: '#000000',
                            bgColor: '#F6F6F6',
                            borderColor: '#F6F6F6'
                          })}
                      onClick={() => setLevelValue(it)}
                    >
                      {it.name}
                    </Center>
                  ))}
                </HStack>
              </>
            )}

            {indactor?.evaluateType === EvaluateType.Score && (
              <>
                <Box>评分</Box>
                <NumberInput
                  mt={respDims(10)}
                  min={indactor.scoreMin}
                  max={indactor.scoreMax}
                  value={score ?? ''}
                  onChange={(e, v) => setScore(isNaN(v) ? undefined : v)}
                >
                  <NumberInputField />
                </NumberInput>
              </>
            )}
          </MyBox>
          <HStack justify="flex-end" mt="24px" spacing="16px">
            <Button variant="grayBase" onClick={onClose}>
              取消
            </Button>
            <Button onClick={onSubmit}>确定</Button>
          </HStack>
        </Box>
      )}
    </MyModal>
  );
};

export default EvaluateModal;
