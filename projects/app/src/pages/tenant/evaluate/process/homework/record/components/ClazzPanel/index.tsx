import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import Sidebar from './Sidebar';
import { respDims } from '@/utils/chakra';
import MyTable from '@/components/MyTable';
import { TableProps } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';
import { getHomeworkRecordListByHomeworkId } from '@/api/tenant/evaluate/process';
import { EvaluateHomeworkType } from '@/types/api/tenant/evaluate/process';

const ClazzPanel = ({
  clazzId,
  subjectId,
  ...props
}: { clazzId: string; subjectId?: string } & ChakraProps) => {
  const [homework, setHomework] = useState<EvaluateHomeworkType>();

  const columns: TableProps['columns'] = [
    {
      title: '学生姓名',
      dataIndex: 'studentName'
    },
    {
      title: '点评人',
      dataIndex: 'evaluatorName',
      render: (value, record) => {
        return record.evaluatorName;
      }
    },
    {
      title: '评分',
      render: (value, record) => {
        return record.score || record.scoreLevelValue;
      }
    },
    {
      title: '评分时间',
      dataIndex: 'createTime',
      render: (text) => dayjs(text).format('YYYY/MM/DD HH:mm')
    }
  ];

  const TableHeader = useCallback(
    () => (
      <Flex align="center">
        <Box
          w={respDims(7)}
          h={respDims('14fpx')}
          bgColor="#175DFF"
          borderRadius={respDims(8)}
        ></Box>
        <Box
          ml={respDims(6)}
          color="rgba(0,0,0,0.9)"
          fontSize={respDims('16fpx')}
          lineHeight={respDims('22fpx')}
          fontWeight="bold"
        >
          班级作业明细
        </Box>
      </Flex>
    ),
    []
  );

  return (
    <Flex border="1px solid #E5E7EB" borderRadius={respDims(14)} overflow="hidden" {...props}>
      <Sidebar
        borderRight="1px solid 1px solid #E5E7EB"
        clazzId={clazzId}
        subjectId={subjectId}
        onChange={setHomework}
      />
      <MyTable
        columns={columns}
        api={getHomeworkRecordListByHomeworkId}
        queryConfig={{ enabled: !!homework }}
        defaultQuery={{ homeworkId: homework?.id || '', clazzId }}
        pageConfig={{ showPaginate: false }}
        boxStyle={{ p: respDims(16) }}
        headerConfig={{
          HeaderComponent: TableHeader
        }}
      />
    </Flex>
  );
};

export default ClazzPanel;
