import MyMenu from '@/components/MyMenu';
import MyTooltip from '@/components/MyTooltip';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { EvaluateHomeworkType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import { Box, Button, Center, ChakraProps, Flex, MenuButton } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import HomeworkModal from '../HomeworkModal';
import { useQuery } from '@tanstack/react-query';
import { getHomeworkPage, removeHomeowk } from '@/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';

const HomeworkList = ({
  clazzId,
  subjectId,
  subjectName,
  onChange,
  ...props
}: {
  clazzId: string;
  subjectId?: string;
  subjectName?: string;
  onChange?: (homework?: EvaluateHomeworkType) => void;
} & ChakraProps) => {
  const [activeId, setActiveId] = useState<string>();

  const [collapsed, setCollapsed] = useState(false);

  const { openOverlay } = useOverlayManager();

  const {
    data: homeworks = [],
    isFetched: isHomeworksFetched,
    refetch: refetchHomeworks
  } = useQuery(
    ['homeworks', clazzId, subjectId],
    () =>
      getListFromPage(getHomeworkPage, {
        clazzId,
        subjectId
      }),
    {
      enabled: !!(clazzId && subjectId)
    }
  );

  const onClickHomework = (homework: EvaluateHomeworkType) => {
    if (homework.id === activeId) {
      return;
    }
    setActiveId(homework.id);
    onChange?.(homework);
  };

  const onEdit = (homework?: EvaluateHomeworkType) => {
    clazzId &&
      subjectId &&
      openOverlay({
        Overlay: HomeworkModal,
        props: {
          clazzId,
          subjectId,
          homework,
          generateName: (date, homework) => {
            const name = `${date.format('YYYYMMDD')}${subjectName || ''}作业`;
            if (homework?.homeworkName?.startsWith(name)) {
              return name;
            }
            if (homeworks.some((it) => it.homeworkName === name)) {
              for (let i = 2; i < 10000; i++) {
                const newName = `${name}${i}`;
                if (!homeworks.some((it) => it.homeworkName === newName)) {
                  return newName;
                }
              }
            }
            return name;
          },
          onSuccess: () => {
            refetchHomeworks();
          }
        }
      });
  };

  const onRemove = (homework: EvaluateHomeworkType) => {
    MessageBox.confirm({
      title: '提示',
      content: `确定要删除 ${homework.homeworkName}？`,
      onOk: () => {
        removeHomeowk({
          id: homework.id
        }).then(() => {
          refetchHomeworks();
        });
      }
    });
  };

  useEffect(() => {
    if (!homeworks.some((it) => it.id === activeId)) {
      setActiveId(homeworks[0]?.id);
      onChange?.(homeworks[0]);
    }
  }, [homeworks]);

  if (collapsed) {
    return (
      <Center
        pos="absolute"
        left={respDims(24)}
        top={respDims(8)}
        w={respDims('24fpx')}
        h={respDims('24fpx')}
        bgColor="#ffffff"
        border="1px solid #e5e7eb"
        borderRadius="50%"
        cursor="pointer"
        _hover={{
          boxShadow: '0px 2px 3px 0px rgba(0,0,0,0.05)'
        }}
        onClick={() => setCollapsed(false)}
      >
        <MyTooltip label="展开作业列表" shouldWrapChildren={false}>
          <Center>
            <SvgIcon
              name="backBoundray"
              color="#09244B"
              transform="rotate(180deg)"
              transformOrigin="center center"
              w={respDims('14fpx')}
              h={respDims('14fpx')}
            />
          </Center>
        </MyTooltip>
      </Center>
    );
  }

  return (
    <Flex
      flexDir="column"
      ml={respDims(24)}
      w={respDims(229, '.8ms')}
      border="1px solid #E5E7EB"
      borderRadius={respDims(14)}
      bgColor="#F9FAFB"
      {...props}
    >
      <Flex
        justify="space-between"
        align="center"
        pl={respDims(24)}
        pr={respDims(8)}
        py={respDims(15)}
      >
        <Box color="rgba(0,0,0,0.9)" fontSize={respDims('15fpx')} lineHeight={respDims('22fpx')}>
          作业名称
        </Box>

        <Center
          w={respDims('24fpx')}
          h={respDims('24fpx')}
          bgColor="rgba(0,0,0,0.03)"
          borderRadius="50%"
          cursor="pointer"
          onClick={() => setCollapsed(true)}
        >
          <MyTooltip label="收起作业列表">
            <SvgIcon name="backBoundray" w={respDims('14fpx')} h={respDims('14fpx')} />
          </MyTooltip>
        </Center>
      </Flex>

      <Flex flex="1 0 0" flexDir="column" overflow="auto">
        {isHomeworksFetched && !homeworks.length ? (
          <Flex flexDir="column" justify="center" align="center" w="100%" h="100%">
            <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
            <Box>暂无作业</Box>
          </Flex>
        ) : (
          homeworks.map((item, index) => (
            <Flex
              key={item.id}
              fontSize={respDims('15fpx')}
              lineHeight={respDims('22fpx')}
              fontWeight="bold"
              pl={respDims(16)}
              pr={respDims(20)}
              py={respDims(10)}
              mx={respDims(8)}
              mt={index > 0 ? respDims(4) : 0}
              borderRadius={respDims(8)}
              cursor="pointer"
              {...(item.id === activeId
                ? {
                    color: '#3366ff',
                    bgColor: '#FFFFFF'
                  }
                : {
                    color: '#303133'
                  })}
              _hover={{
                bgColor: '#FFFFFF'
              }}
              onClick={() => onClickHomework(item)}
            >
              <Box flex="1 0 0" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
                {item.homeworkName}
              </Box>

              <MyMenu
                trigger="hover"
                offset={[20, 0]}
                width={20}
                Button={
                  <MenuButton
                    w={respDims('20fpx')}
                    h={respDims('20fpx')}
                    borderRadius={respDims(4)}
                    _hover={{
                      bgColor: '#f3f4f6'
                    }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Center>
                      <SvgIcon name="more" w={respDims(16)} h={respDims(16)} />
                    </Center>
                  </MenuButton>
                }
                menuList={[
                  {
                    icon: <SvgIcon name="edit" />,
                    label: '编辑',
                    onClick: () => onEdit(item)
                  },
                  {
                    icon: <SvgIcon name="trash" />,
                    label: '删除',
                    onClick: () => onRemove(item)
                  }
                ]}
              />
            </Flex>
          ))
        )}
      </Flex>

      <Button
        variant="whitePrimary"
        border="none"
        mx={respDims(8)}
        my={respDims(13)}
        borderRadius={respDims(50)}
        onClick={() => onEdit()}
      >
        添加作业
      </Button>
    </Flex>
  );
};

export default HomeworkList;
