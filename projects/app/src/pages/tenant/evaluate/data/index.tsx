import { respDims } from '@/utils/chakra';
import { Box } from '@chakra-ui/react';
import { useState } from 'react';
import DataDetails from './components/DataDetails';
import { serviceSideProps } from '@/utils/i18n';
import Tabs from './components/Tabs';

const EvaluateData = () => {
  const [activeTabKey, setActiveTabKey] = useState('dataDetails');

  const tabItems = [
    {
      key: 'dataDetails',
      label: '评价数据明细',
      Component: DataDetails
    }
  ];

  const activeTab = tabItems.find((it) => it.key === activeTabKey) || tabItems[0];

  return (
    <Box h="100%" px={respDims(24)} py={respDims(20)} borderRadius={respDims(20)} bgColor="#FFFFFF">
      <activeTab.Component
        tabs={<Tabs activeKey={activeTabKey} items={tabItems} onChange={setActiveTabKey}></Tabs>}
      />
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default EvaluateData;
