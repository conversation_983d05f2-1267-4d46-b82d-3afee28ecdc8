import { respDims } from '@/utils/chakra';
import { Center, HStack } from '@chakra-ui/react';

const Tabs = ({
  activeKey,
  items,
  onChange
}: {
  activeKey?: string;
  items: { key: string; label: string }[];
  onChange?: (key: string) => void;
}) => {
  return (
    <HStack spacing={respDims(32)}>
      {items.map((item) => (
        <Center
          key={item.key}
          h={respDims('40fpx')}
          boxSizing="border-box"
          fontSize={respDims('15fpx')}
          {...(item.key === activeKey
            ? {
                color: '#165dff',
                fontWeight: 'bold',
                borderBottom: '2px solid primary.500'
              }
            : {
                color: '#4E5969',
                borderBottom: '2px solid transparent'
              })}
          cursor="pointer"
          onClick={() => item.key !== activeKey && onChange?.(item.key)}
        >
          {item.label}
        </Center>
      ))}
    </HStack>
  );
};

export default Tabs;
