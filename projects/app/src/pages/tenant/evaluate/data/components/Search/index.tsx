import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import {
  Button,
  Center,
  ChakraProps,
  Flex,
  Input,
  InputGroup,
  InputLeftElement
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { getClientSemesterPage } from '@/api/tenant/teamManagement/semester';
import { Cascader, Select } from 'antd';
import { useQuery } from '@tanstack/react-query';
import { getClientSchoolDeptTree, getClientStudentPage } from '@/api/tenant/teamManagement/student';
import { ClientStudentPageType, DepartmentTree } from '@/types/api/tenant/teamManagement/student';
import { getTenantUserPage } from '@/api/tenant';
import { TenantUserType } from '@/types/api/tenant';
import { SubjectType } from '@/types/api/tenant/evaluate/rule';

interface Semester {
  id: string;
  year: string;
  type: 1 | 2;
  isCurrent: 0 | 1;
}

interface TreeSelectDataItem {
  value: string;
  title: string;
  children?: TreeSelectDataItem[];
  isLeaf: boolean;
}

interface SearchProps extends ChakraProps {
  value?: string;
  placeholder?: string;
  onChange?: (text: string) => void;
  subjectsData?: SubjectType[];
  onSearch: (params: {
    year?: string;
    term?: number;
    clazzId?: string;
    evaluatorId?: string;
    evaluateeId?: string;
    searchKey?: string;
    subjectId?: string;
  }) => void;
}
const Search: React.FC<SearchProps> = ({
  value = '',
  placeholder,
  onChange,
  onSearch,
  subjectsData,
  ...props
}) => {
  const { Option } = Select;
  const [innerValue, setInnerValue] = useState(value);
  const [studentList, setStudentList] = useState<ClientStudentPageType[]>();
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [tenantUserList, setTenantUserList] = useState<TenantUserType[]>([]);
  const [currentSemester, setCurrentSemester] = useState<string | undefined>(undefined);
  const [defaultSemester, setDefaultSemester] = useState<Semester | null>(null);
  const [evaluateDataList, setEvaluateDataList] = useState<{ title: string; value: string }[]>([]);
  const [selectedEvaluator, setSelectedEvaluator] = useState<string | undefined>(undefined);
  const [selectedEvaluated, setSelectedEvaluated] = useState<string | undefined>(undefined);
  const [selectedClazzId, setSelectedClazzId] = useState<string>('');
  const [subjectId, setSubjectId] = useState<string | undefined>(undefined);

  const { isLoading: isTreeLoading, data: treeData } = useQuery(
    ['treeData'],
    getClientSchoolDeptTree,
    {
      onSuccess(data) {
        selectDefaultClass(data);
      }
    }
  );

  const selectDefaultClass = (data: DepartmentTree) => {
    if (data && data.length > 0) {
      let defaultClassId = '';

      const findFirstLeaf = (nodes: DepartmentTree): string => {
        for (const node of nodes) {
          if (!node.children || node.children.length === 0) {
            return node.id;
          }
          if (node.children) {
            const childResult = findFirstLeaf(node.children);
            if (childResult) return childResult;
          }
        }
        return '';
      };

      defaultClassId = findFirstLeaf(data);

      if (defaultClassId) {
        setSelectedClazzId(defaultClassId);
        const fullPath = findFullPath(data, defaultClassId);
        if (fullPath.length > 0) {
          setSelectedClazzId(fullPath[fullPath.length - 1]);
        }
      }
    }
  };

  const findFullPath = (tree: DepartmentTree, targetId: string): string[] => {
    for (const node of tree) {
      if (node.id === targetId) {
        return [node.id];
      }
      if (node.children) {
        const childPath = findFullPath(node.children, targetId);
        if (childPath.length > 0) {
          return [node.id, ...childPath];
        }
      }
    }
    return [];
  };

  const fetchSemesters = async () => {
    try {
      const response = await getClientSemesterPage({ current: 1, size: 999 });
      setSemesters(response.records || []);
      const current = response.records.find((sem) => sem.isCurrent === 1);
      if (current) {
        setCurrentSemester(current.id);
        setDefaultSemester(current);
      }
    } catch (error) {}
  };

  const handleSemesterChange = (value: string | undefined) => {
    setCurrentSemester(value);
    handleSearch();
  };

  const getSemesterLabel = (sem: Semester) => {
    const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
    return `${sem.year}学年${typeMap[sem.type] || ''}`;
  };

  const handleSearch = () => {
    const selectedSemester = semesters.find((sem) => sem.id === currentSemester);
    const searchParams = {
      year: selectedSemester ? selectedSemester.year : undefined,
      term: selectedSemester ? selectedSemester.type : undefined,
      clazzId: selectedClazzId,
      evaluatorId: selectedEvaluator, //评价人id
      evaluateeId: selectedEvaluated, //被评价人id
      searchKey: innerValue,
      subjectId: subjectId
    };
    onSearch(searchParams);
  };
  const handleReset = () => {
    setSelectedEvaluator(undefined);
    setSelectedEvaluated(undefined);
    setSubjectId(undefined);
    setInnerValue('');
    const selectedSemester = semesters.find((sem) => sem.id === currentSemester);
    const searchParams = {
      year: selectedSemester ? selectedSemester.year : undefined,
      term: selectedSemester ? selectedSemester.type : undefined,
      clazzId: selectedClazzId
    };
    onSearch(searchParams);
  };

  useQuery(
    ['teachers'],
    () =>
      getTenantUserPage({
        current: 1,
        searchKey: '',
        searchType: '',
        size: 9999,
        status: ''
      }),
    {
      onSuccess(data) {
        const list = data.records.map((res) => {
          return {
            label: res.username,
            value: res.id,
            ...res
          };
        });
        setTenantUserList(list);
      }
    }
  );

  useQuery(['studentPage'], () => getClientStudentPage({ current: 1, size: 9999 }), {
    onSuccess(data) {
      const list = data.records.map((res) => {
        return {
          label: res.name,
          value: res.id,
          ...res
        };
      });
      setStudentList(list);
    }
  });

  useEffect(() => {
    setInnerValue(value);
  }, [value]);

  useEffect(() => {
    if (treeData) {
      selectDefaultClass(treeData);
    }
  }, [treeData]);

  useEffect(() => {
    fetchSemesters();
  }, []);

  useEffect(() => {
    if (subjectsData) {
      setSubjectId(undefined);
    }
  }, [subjectsData]);

  useEffect(() => {
    const convertToTreeSelectData = (data: DepartmentTree): TreeSelectDataItem[] => {
      return data.map((item) => ({
        value: item.id,
        title: item.deptName,
        children: item.children ? convertToTreeSelectData(item.children) : undefined,
        isLeaf: !item.children || item.children.length === 0
      }));
    };

    getClientSchoolDeptTree().then((res) => {
      const convertedData = convertToTreeSelectData(res);
      setEvaluateDataList(convertedData);
    });
  }, []);

  useEffect(() => {
    if (defaultSemester) {
      handleSearch();
    }
  }, [defaultSemester, selectedClazzId]);

  return (
    <Flex w="100%" justifyContent="space-between" {...props} mt={respDims(18)}>
      <Flex flexWrap="wrap" w="100%" alignItems="center">
        <Select
          showSearch
          value={currentSemester}
          style={{ width: '220px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          placeholder="请选择学期"
          onChange={handleSemesterChange}
        >
          {semesters.map((sem) => (
            <Option key={sem.id} value={sem.id}>
              {getSemesterLabel(sem)}
            </Option>
          ))}
        </Select>

        <Cascader
          options={treeData || []}
          variant="filled"
          showSearch
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          fieldNames={{
            label: 'deptName',
            value: 'id',
            children: 'children'
          }}
          onChange={(value) => {
            const selectedValue = Array.isArray(value) ? value[value.length - 1] : null;
            setSelectedClazzId(selectedValue as string);
          }}
          value={selectedClazzId ? findFullPath(treeData || [], selectedClazzId) : []}
          dropdownStyle={{ overflowX: 'auto', zIndex: 2000 }}
          maxTagCount="responsive"
          allowClear={false}
          placeholder="请选择评价年级和班级"
        />

        <Select
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          placeholder="请选择评价方"
          showSearch
          filterOption={(input, option) =>
            (option?.username ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={tenantUserList}
          value={selectedEvaluator}
          dropdownStyle={{ zIndex: 9999 }}
          allowClear
          onChange={(value) => setSelectedEvaluator(value)}
        />

        <Select
          value={selectedEvaluated}
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          showSearch
          filterOption={(input, option) =>
            (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
          }
          placeholder="请选择被评价方"
          options={studentList!}
          allowClear
          onChange={(value) => setSelectedEvaluated(value)}
          dropdownStyle={{ zIndex: 9999 }}
        />

        {subjectsData && subjectsData?.length > 0 && (
          <Select
            value={subjectId}
            style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
            variant="filled"
            showSearch
            fieldNames={{
              label: 'subjectName',
              value: 'subjectId'
            }}
            filterOption={(input, option) =>
              (option?.subjectName ?? '').toLowerCase().includes(input.toLowerCase())
            }
            placeholder="请选择学科"
            options={subjectsData!}
            allowClear
            onChange={(value) => setSubjectId(value)}
            dropdownStyle={{ zIndex: 9999 }}
          />
        )}

        <InputGroup
          w="200px"
          h="32px"
          bgColor="#f5f5f5"
          borderRadius={respDims(8)}
          boxSizing="border-box"
          overflow="hidden"
          mb="12px"
          {...props}
        >
          <InputLeftElement h="100%">
            <SvgIcon name="search" w={respDims('14fpx')} h={respDims('14fpx')} color="#4E5969" />
          </InputLeftElement>

          <Input
            value={innerValue}
            w="100%"
            h="100%"
            placeholder={placeholder}
            color="#000"
            bgColor="transparent"
            fontSize={respDims('14fpx')}
            border="none"
            _focus={{
              border: 'none',
              outline: 'none',
              boxShadow: 'none'
            }}
            _placeholder={{
              color: '#bdbdbd',
              fontSize: 'inherit'
            }}
            onChange={(e) => {
              setInnerValue(e.target.value);
              onChange?.(e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
          />
        </InputGroup>
      </Flex>

      <Center mb="12px">
        <Button
          onClick={() => {
            handleSearch();
          }}
          variant="outline"
          p="0 30px"
          h="32px"
          mb="6px"
          colorScheme="primary"
          ml={respDims(16)}
        >
          查询
        </Button>
        <Button
          onClick={handleReset}
          p="0 30px"
          h="32px"
          mb="6px"
          variant={'grayBase'}
          ml={respDims(16)}
          mr={respDims(16)}
        >
          重置
        </Button>
      </Center>
    </Flex>
  );
};

export default Search;
