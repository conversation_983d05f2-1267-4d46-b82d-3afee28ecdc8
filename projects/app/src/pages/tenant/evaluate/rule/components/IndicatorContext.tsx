import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import {
  DimenType,
  EvaluaDimension,
  EvaluaIndactorVO,
  EvaluaProject,
  EvaluationRuleDetail
} from '@/types/api/tenant/evaluate/rule';
import { treeFindPaths, treeToList } from '@/utils/tree';
import { ModalStatus } from './RulesTab/components/SelectIndicatorModal';
import { useQuery } from '@tanstack/react-query';
import { getUsedIndactorsByClazzs } from '@/api/tenant/evaluate/rule';
import { HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';
import dayjs from 'dayjs';

interface SearchCondition {
  keyword: string;
  evaluationStage: string;
  evaluationMethod: string;
}

interface IndicatorContextType {
  modalStatus: ModalStatus;
  selectedDimensionCategory: DimenType | null;
  setSelectedDimensionCategory: (category: DimenType | null) => void;
  selectedDimension: EvaluaDimension | null;
  setSelectedDimension: (dimension: EvaluaDimension | null) => void;
  selectedProject: EvaluaProject | null;
  setSelectedProject: (project: EvaluaProject | null) => void;
  selectedIndicators: EvaluaIndactorVO[];
  setSelectedIndicators: (indicators: EvaluaIndactorVO[]) => void;
  searchCondition: SearchCondition;
  setSearchCondition: (condition: SearchCondition) => void;
  formData: EvaluationRuleDetail;
  usedIndactorsByClazzList: EvaluaIndactorVO[];
  initialSelectedIndicators: EvaluaIndactorVO[];
  treeData: IndicatorTree[];
  setTreeData: (data: IndicatorTree[]) => void;
  selectedIndicatorPathMap: Record<string, string[]>;
  setSelectedIndicatorPathMap: (pathMap: Record<string, string[]>) => void;
}
const IndicatorContext = createContext<IndicatorContextType | undefined>(undefined);

interface IndicatorProviderProps {
  children: ReactNode;
  initialSelectedIndicators?: EvaluaIndactorVO[];
  initModalStatus?: ModalStatus;
  formData?: EvaluationRuleDetail;
}

export type IndicatorTree = {
  id: string;
  isHasSub: HasSubIndicator;
  children: IndicatorTree[];
};

export const IndicatorProvider: React.FC<IndicatorProviderProps> = ({
  children,
  initialSelectedIndicators = [],
  initModalStatus = 'select',
  formData = {} as EvaluationRuleDetail
}) => {
  const [modalStatus, setModalStatus] = useState(initModalStatus);
  const [selectedDimensionCategory, setSelectedDimensionCategory] = useState<DimenType | null>(
    null
  );
  const [selectedDimension, setSelectedDimension] = useState<EvaluaDimension | null>(null);
  const [selectedProject, setSelectedProject] = useState<EvaluaProject | null>(null);
  const [selectedIndicators, setSelectedIndicators] =
    useState<EvaluaIndactorVO[]>(initialSelectedIndicators);
  const [searchCondition, setSearchCondition] = useState<SearchCondition>({
    keyword: '',
    evaluationStage: '',
    evaluationMethod: ''
  });
  const [treeData, setTreeData] = useState<IndicatorTree[]>([]);
  const [selectedIndicatorPathMap, setSelectedIndicatorPathMap] = useState<
    Record<string, string[]>
  >({});

  const { isFetching, data: usedIndactorsByClazzList } = useQuery(
    ['init'],
    () => {
      const evaluateeIds = formData?.evaluateeIdsCoustom as any;

      const clazzIds = evaluateeIds?.map((item: string[]) => {
        return item[item.length - 1];
      });

      return getUsedIndactorsByClazzs({
        clazzIds,
        ruleId: formData.id,
        startTime: dayjs(formData.startTime).format('YYYY-MM-DD HH:mm:ss'),
        endTime: dayjs(formData.endTime).format('YYYY-MM-DD HH:mm:ss'),
        semesterId: formData.semesterId!,
        periodType: formData.periodType!
      }).then((res) => {
        return res.filter((item) => !initialSelectedIndicators.find((it) => it.id == item.id));
      });
    },
    {
      enabled: !!formData?.evaluateeIdsCoustom
    }
  );

  useEffect(() => {
    if (treeData.length > 0 && selectedIndicators.length > 0) {
      const paths = treeFindPaths(
        treeData,
        selectedIndicators.map((i) => i.id)
      );

      const pathMap: Record<string, string[]> = {};

      Object.entries(paths).forEach(([id, path]) => {
        pathMap[id] = path.map((node) => node.id);
      });

      setSelectedIndicatorPathMap(pathMap);
    } else {
      setSelectedIndicatorPathMap({});
    }
  }, [treeData, selectedIndicators]);

  return (
    <IndicatorContext.Provider
      value={{
        usedIndactorsByClazzList: usedIndactorsByClazzList || [],
        modalStatus,
        formData,
        selectedDimensionCategory,
        initialSelectedIndicators,
        setSelectedDimensionCategory,
        selectedDimension,
        setSelectedDimension,
        selectedProject,
        setSelectedProject,
        selectedIndicators,
        setSelectedIndicators,
        searchCondition,
        setSearchCondition,
        treeData,
        setTreeData,
        selectedIndicatorPathMap,
        setSelectedIndicatorPathMap
      }}
    >
      {formData?.evaluateeIdsCoustom
        ? usedIndactorsByClazzList !== undefined && children
        : children}
    </IndicatorContext.Provider>
  );
};

export const useIndicator = () => {
  const context = useContext(IndicatorContext);
  if (!context) {
    throw new Error('useIndicator must be used within an IndicatorProvider');
  }
  return context;
};

export default IndicatorProvider;
