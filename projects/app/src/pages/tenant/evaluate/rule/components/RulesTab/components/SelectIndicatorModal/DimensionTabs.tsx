import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Button } from '@chakra-ui/react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useIndicator } from '../../../IndicatorContext';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { deleteDimenType, listDimenType } from '@/api/tenant/evaluate/rule';
import { DimenType } from '@/types/api/tenant/evaluate/rule';
import { useToast } from '@/hooks/useToast';
import { promisifyDelete } from '@/utils/ui/messageBox';

const DimensionTabs: React.FC = () => {
  const { selectedDimensionCategory, setSelectedDimensionCategory } = useIndicator();
  const { openOverlay } = useOverlayManager();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const {
    data: tabs,
    isLoading,
    error
  } = useQuery(['dimensionCategories'], () => listDimenType({}), {
    onSuccess: (data) => {
      if (data && data[0]) {
        setSelectedDimensionCategory(data[0] || null);
      } else {
        setSelectedDimensionCategory(null);
      }
    }
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载出错: {(error as Error).message}</div>;

  const onCurrentTab = (tab: DimenType) => {
    setSelectedDimensionCategory(tab);
  };

  return (
    <Flex alignItems="center" justifyContent="space-between" borderBottom="1px solid #E5E7EB">
      <Flex alignItems="stretch" flexShrink="0" w="100%" px={respDims(32)}>
        {tabs?.map((tab: DimenType) => (
          <Flex
            key={tab.id}
            alignItems="center"
            py="8px"
            m="0px 32px 0px 0"
            position="relative"
            {...(tab.id?.toString() == selectedDimensionCategory?.id
              ? {
                  color: 'primary.500',
                  _after: {
                    position: 'absolute',
                    content: '""',
                    left: '0',
                    right: '0',
                    bottom: '-1px',
                    w: '100%',
                    height: '2px',
                    bgColor: 'primary.500'
                  }
                }
              : {
                  color: '#4E5969'
                })}
            fontSize="14px"
            fontWeight="bold"
            cursor="pointer"
            _hover={{
              '.action-btn': {
                display: 'flex'
              }
            }}
            onClick={() => onCurrentTab(tab)}
          >
            <Box>{tab.name}</Box>
          </Flex>
        ))}
      </Flex>
    </Flex>
  );
};

export default DimensionTabs;
