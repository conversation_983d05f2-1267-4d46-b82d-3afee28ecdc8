import React, { useState, useEffect } from 'react';
import { Flex, Button, HStack } from '@chakra-ui/react';
import { SearchBarProps } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { respDims } from '@/utils/chakra';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EditEvaluationRuleModal from './EditEvaluationRuleModal';
import EvaluateLevelModal from './EvaluateLevelModal/index';
import { EvaluationRuleDetail, EvaluationRuleListParams } from '@/types/api/tenant/evaluate/rule';
import SvgIcon from '@/components/SvgIcon';
import styles from '@/pages/index.module.scss';
import { Input } from 'antd';

const SearchBar = ({
  onSearch,
  query,
  tableInstance
}: SearchBarProps<EvaluationRuleListParams, EvaluationRuleDetail>) => {
  const router = useRouter();
  const [searchKey, setSearchKey] = useState(query?.searchKey || '');
  const { openOverlay, OverlayContainer } = useOverlayManager();

  useEffect(() => {
    setSearchKey(query?.searchKey || '');
  }, [router, query]);

  const handleSearch = () => {
    const params = { ...query, searchKey };
    onSearch && onSearch(params);
  };

  const handleReset = () => {
    setSearchKey('');
    onSearch && onSearch({ ...query, searchKey: '' });
  };

  const onAdd = () => {
    openOverlay({
      Overlay: EditEvaluationRuleModal,
      props: {
        formStatus: 'add',
        onClose: () => {},
        onSuccess() {
          tableInstance?.reload();
        }
      }
    });
  };

  const onOpenEvaluateLevel = () => {
    openOverlay({
      Overlay: EvaluateLevelModal,
      props: {
        onClose: () => {},
        onSuccess() {
          tableInstance?.reload();
        }
      }
    });
  };

  return (
    <Flex alignItems="center" justifyContent="space-between" className={styles['my-form']}>
      <HStack>
        <Input
          placeholder="请输入评价规则名称"
          value={searchKey}
          onChange={(e) => setSearchKey(e.target.value)}
          prefix={<SvgIcon name="search"></SvgIcon>}
          style={{ width: '200px', borderRadius: '8px', height: '36px' }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
        />

        <Button onClick={handleReset} variant={'grayBase'}>
          重置
        </Button>

        <Button
          ml="4px"
          h="36px"
          colorScheme="primary"
          variant="outline"
          onClick={onOpenEvaluateLevel}
        >
          管理评分等级
        </Button>

        <Button ml="4px" h="36px" variant="primary" onClick={onAdd}>
          <SvgIcon name="plus"></SvgIcon> 添加评价规则
        </Button>
      </HStack>

      <OverlayContainer />
    </Flex>
  );
};

export default SearchBar;
