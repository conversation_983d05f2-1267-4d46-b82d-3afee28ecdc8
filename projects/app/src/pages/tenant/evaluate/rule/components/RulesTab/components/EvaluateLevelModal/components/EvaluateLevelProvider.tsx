import React, { createContext, useContext, useState, ReactNode, useMemo, useCallback } from 'react';
import { EvaluaScoreLevel as EvaluateLevel } from '@/types/api/tenant/evaluate/rule';
import { Form } from 'antd';
import { promisifyConfirm, promisifyDelete, promisifyWarning } from '@/utils/ui/messageBox';

interface EvaluateLevelContextProps {
  selectedEvaluateLevel: EvaluateLevel | null;
  setSelectedEvaluateLevel: (scoreGrade: EvaluateLevel | null) => void;
  scoreGrades: EvaluateLevel[] | null;
  setScoreGrades: (scoreGrade: EvaluateLevel[] | null) => void;
  formStatus: 'add' | 'edit' | null;
  setFormStatus: (status: 'add' | 'edit' | null) => void;
  form: any; // Antd Form instance
  initialValues: any;
  setInitialValues: (values: any) => void;
  hasFormChanged: () => boolean;
  unSaveBeformConfirm: () => Promise<any>;
}

interface EvaluateLevelProviderProps {
  children: ReactNode;
}

const EvaluateLevelContext = createContext<EvaluateLevelContextProps | undefined>(undefined);

export const EvaluateLevelProvider: React.FC<EvaluateLevelProviderProps> = ({ children }) => {
  const [selectedEvaluateLevel, setSelectedEvaluateLevel] = useState<EvaluateLevel | null>(null);
  const [scoreGrades, setScoreGrades] = useState<EvaluateLevel[] | null>(null);
  const [formStatus, setFormStatus] = useState<'add' | 'edit' | null>(null);
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState<any>(null);
  const hasFormChanged = useCallback(() => {
    if (!initialValues) return false;

    const currentValues = form.getFieldsValue();

    // 比较名称和状态
    if (
      currentValues.name !== initialValues.name ||
      currentValues.status !== initialValues.status
    ) {
      return true;
    }

    // 比较详情数组
    if (currentValues.details?.length !== initialValues.details?.length) {
      return true;
    }

    for (let i = 0; i < (currentValues.details?.length || 0); i++) {
      const currentDetail = currentValues.details[i];
      const initialDetail = initialValues.details[i];

      if (!initialDetail) return true; // 新增的项

      if (
        currentDetail.name !== initialDetail.name ||
        currentDetail.scoreMin !== initialDetail.scoreMin ||
        currentDetail.scoreMax !== initialDetail.scoreMax
      ) {
        return true;
      }
    }

    return false;
  }, [initialValues, form]);

  const unSaveBeformConfirm = useCallback(async (): Promise<any> => {
    if (!hasFormChanged()) {
      return;
    }
    return promisifyConfirm({
      title: '当前页面有未保存的修改，是否继续？'
    });
  }, [hasFormChanged]);

  const contextValue = useMemo(
    () => ({
      selectedEvaluateLevel,
      setSelectedEvaluateLevel,
      formStatus,
      setFormStatus,
      scoreGrades,
      setScoreGrades,
      form,
      initialValues,
      setInitialValues,
      hasFormChanged,
      unSaveBeformConfirm
    }),
    [selectedEvaluateLevel, formStatus, scoreGrades, initialValues, hasFormChanged]
  );

  return (
    <EvaluateLevelContext.Provider value={contextValue}>{children}</EvaluateLevelContext.Provider>
  );
};

export const useEvaluateLevel = () => {
  const context = useContext(EvaluateLevelContext);
  if (!context) {
    throw new Error('useEvaluateLevel must be used within a EvaluateLevelProvider');
  }
  return context;
};

export default EvaluateLevelProvider;
