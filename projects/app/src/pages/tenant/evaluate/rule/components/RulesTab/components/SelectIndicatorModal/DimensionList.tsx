import React, { useCallback, useState, useRef } from 'react';
import { <PERSON>, Flex, Button as ChakraButton } from '@chakra-ui/react';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { useIndicator } from '../../../IndicatorContext';
import { Input } from 'antd';
import { respDims } from '@/utils/chakra';
import { AddIcon } from '@chakra-ui/icons';
import { MyTableRef } from '@/components/MyTable/types';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import DimensionTabs from './DimensionTabs';
import { EvaluaDimension, EvaluaIndicatorQueryReq } from '@/types/api/tenant/evaluate/rule';
import {
  listPageDimension,
  addDimension,
  updateDimension,
  deleteDimension,
  runSortIndicators
} from '@/api/tenant/evaluate/rule';
import { useToast } from '@/hooks/useToast';
import { MessageBox, promisifyDelete } from '@/utils/ui/messageBox';

const DimensionList: React.FC = () => {
  const { selectedDimensionCategory, selectedDimension, setSelectedDimension } = useIndicator();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const tableRef = useRef<MyTableRef>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [dimensions, setDimensions] = useState<EvaluaDimension[]>([]);

  const {
    data: dimensionsData,
    isFetching,
    refetch
  } = useQuery(
    ['dimensions', selectedDimensionCategory],
    () =>
      listPageDimension({
        dimenTypeId: selectedDimensionCategory?.id,
        type: 1
      } as EvaluaIndicatorQueryReq),
    {
      onSuccess: (data) => {
        setDimensions(data.records);

        if (data.records.length > 0) {
          setSelectedDimension(data.records[0]);
        } else {
          setSelectedDimension(null);
        }
      }
    }
  );

  const addMutation = useMutation(addDimension, {
    onSuccess: (newDimension) => {
      queryClient.invalidateQueries(['dimensions', selectedDimensionCategory]);
      refetch();
      toast({
        title: '添加成功',
        status: 'success'
      });
    }
  });

  const updateMutation = useMutation(updateDimension, {
    onSuccess: () => {
      queryClient.invalidateQueries(['dimensions', selectedDimensionCategory]);
      toast({
        title: '更新成功',
        status: 'success'
      });
    }
  });

  const deleteMutation = useMutation(deleteDimension, {
    onSuccess: () => {
      queryClient.invalidateQueries(['dimensions', selectedDimensionCategory]);
      toast({
        title: '删除成功',
        status: 'success',
        duration: 2000,
        isClosable: true
      });
    }
  });

  const handleEdit = (record: EvaluaDimension) => {
    setEditingId(record.id || null);
    setEditingName(record.indactorName);
  };

  const handleSaveEdit = (record: EvaluaDimension) => {
    if (record.id === 'new') {
      addMutation.mutate({
        indactorName: editingName,
        sortNo: dimensions.length - 1,
        dimenTypeId: selectedDimensionCategory?.id
      });
    } else if (record.id) {
      updateMutation.mutate({
        id: record.id,
        indactorName: editingName,
        sortNo: record.sortNo,
        dimenTypeId: selectedDimensionCategory?.id
      });
    }
    setEditingId(null);
  };

  const handleCancelEdit = () => {
    if (editingId === 'new') {
      setDimensions(dimensions.filter((d) => d.id !== 'new'));
    }
    setEditingId(null);
  };
  const handleDelete = async (id: string) => {
    await promisifyDelete({
      title: '确定删除该维度？'
    });
    deleteMutation.mutate({ id });
  };

  const handleAdd = () => {
    const newDimension: EvaluaDimension = {
      id: 'new',
      indactorName: '',
      sortNo: dimensions.length || 0,
      dimenTypeId: selectedDimensionCategory?.id
    };
    setDimensions([...dimensions, newDimension]);
    setEditingId('new');
    setEditingName('');
  };

  const columns: ColumnsType<EvaluaDimension> = [
    {
      title: '维度',
      dataIndex: 'indactorName',
      key: 'indactorName',
      render: (text, record) => {
        if (editingId === record.id) {
          return (
            <Input
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
              onPressEnter={() => handleSaveEdit(record)}
              autoFocus
            />
          );
        }
        return text;
      }
    }
  ];

  const onRow = useCallback(
    (record: EvaluaDimension) => ({
      onClick: () => {
        setSelectedDimension(record || null);
      }
    }),
    [setSelectedDimension]
  );

  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: EvaluaDimension[]) => {
      const { active, over } = event;
      if (editingId) {
        return toast({
          title: '正在编辑中，请先完成编辑',
          status: 'warning'
        });
      }

      if (active.id !== over.id) {
        const sortParams = newDataSource.map((item, index) => ({
          id: item.id!,
          sortNo: index + 1
        }));

        await runSortIndicators(sortParams);
        toast({
          status: 'success',
          title: '操作成功'
        });
        refetch();
      }
    },
    [runSortIndicators]
  );

  return (
    <Flex flexDirection="column" h="100%" w="100%">
      <Flex bg="#f7f9fb" borderRadius="8px" flex="1" h="0" p={respDims(16)} flexDirection="column">
        <Box
          flex="1"
          overflow="auto"
          css={{
            '& .selected-row': {
              background: '#eff5fe!important',
              borderRadius: '8px'
            }
          }}
        >
          <MyTable
            ref={tableRef}
            columns={columns}
            loading={
              isFetching ||
              addMutation.isLoading ||
              updateMutation.isLoading ||
              deleteMutation.isLoading
            }
            dataSource={dimensions}
            boxStyle={{
              px: '0',
              py: '0',
              borderRadius: '12px',
              css: {
                '& .ant-table': {
                  borderRadius: '12px 12px 12px 12px!important',
                  overflow: 'hidden!important'
                },

                '.ant-table tbody .ant-table-row:last-child .ant-table-cell': {
                  borderBottom: 'none'
                }
              }
            }}
            size="small"
            headerConfig={{
              showHeader: false
            }}
            showHeader={false}
            pageConfig={{
              showPaginate: false
            }}
            onRow={onRow}
            rowClassName={(record) => (selectedDimension?.id === record.id ? 'selected-row' : '')}
          />
        </Box>
      </Flex>
    </Flex>
  );
};

export default DimensionList;
