import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Box, Text, Flex, Button, HStack, Center } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { respDims } from '@/utils/chakra';
import { Form, Input, InputNumber, Space, Switch } from 'antd';
import { useEvaluateLevel } from './EvaluateLevelProvider';
import { useToast } from '@/hooks/useToast';
import MyTable from '@/components/MyTable';
import SvgIcon from '@/components/SvgIcon';
import { TableProps } from 'antd';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  updateScoreLevel,
  createScoreLevelValue,
  updateScoreLevelValue,
  deleteScoreLevelValue,
  getScoreLevelList,
  getScoreLevelValueList,
  batchUpdateScoreLevelValue
} from '@/api/tenant/evaluate/rule';
import { <PERSON>luaScoreLevel, EvaluaScoreLevelValue } from '@/types/api/tenant/evaluate/rule';
import MyBox from '@/components/common/MyBox';
import styles from '@/pages/index.module.scss';
import { promisifyDelete } from '@/utils/ui/messageBox';
import { MyTableRef } from '@/components/MyTable/types';

const LevelDetail = ({ onSuccess, onClose }: { onSuccess: () => void; onClose: () => void }) => {
  const { t } = useTranslation();
  const {
    selectedEvaluateLevel,
    setSelectedEvaluateLevel,
    scoreGrades,
    setScoreGrades,
    form,
    initialValues,
    setInitialValues,
    unSaveBeformConfirm
  } = useEvaluateLevel();
  const { toast } = useToast();
  const actionRef = useRef<MyTableRef>(null);

  const {
    data: scoreLevelData,
    isFetching: isLoadingScoreLevel,
    refetch
  } = useQuery(
    ['scoreLevel', selectedEvaluateLevel],
    () => {
      return getScoreLevelValueList({ scoreLevelId: selectedEvaluateLevel?.id! });
    },
    {
      enabled: !!selectedEvaluateLevel?.id,
      onSuccess: (data) => {
        const initialValues = {
          name: selectedEvaluateLevel?.name,
          status: selectedEvaluateLevel?.status === 1,
          details: data
        };
        form.setFieldsValue(initialValues);
        setInitialValues(initialValues);
      }
    }
  );

  const validateScoreRanges = (details: EvaluaScoreLevelValue[]) => {
    if (details.length === 0) return;

    const sortedDetails = [...details].sort((a, b) => (a.scoreMin || 0) - (b.scoreMin || 0));
    const errors: { index: number; message: string }[] = [];

    for (let i = 0; i < sortedDetails.length - 1; i++) {
      const current = sortedDetails[i];
      const next = sortedDetails[i + 1];

      // 检查是否有未定义的值
      if (
        current.scoreMin === undefined ||
        current.scoreMax === undefined ||
        next.scoreMin === undefined ||
        next.scoreMax === undefined
      ) {
        continue;
      }

      // 检查重叠

      if (Number(current.scoreMax) >= Number(next.scoreMin)) {
        errors.push({
          index: i,
          message: `${current.name}(${current.scoreMin}-${current.scoreMax})与${next.name}(${next.scoreMin}-${next.scoreMax})的分数范围重叠`
        });
      }

      // 检查不连续
      if (Number(current.scoreMax) != Number(next.scoreMin - 1)) {
        errors.push({
          index: i,
          message: `${current.name}(${current.scoreMax})与${next.name}(${next.scoreMin})之间的分数范围不连续`
        });
      }
    }

    return errors.length > 0
      ? errors.map((item, index) => `${index + 1}、${item.message}`).join(';\n\r')
      : undefined;
  };

  useEffect(() => {
    if (selectedEvaluateLevel) {
      refetch();
    } else {
      form.resetFields();
    }
  }, [selectedEvaluateLevel, form]);

  const updateMutation = useMutation(
    async (values: any) => {
      const { name, status, details } = values;

      const updatedLevel: EvaluaScoreLevel = {
        ...selectedEvaluateLevel!,
        name,
        status: status ? 1 : 0
      };

      // 更新评分等级
      await updateScoreLevel(updatedLevel);

      // 分离更新项和新增项
      const updateItems: EvaluaScoreLevelValue[] = [];
      const createItems: Omit<EvaluaScoreLevelValue, 'id'>[] = [];
      const deleteItems: string[] = [];
      // 找出需要删除的项
      initialValues.details.forEach((initialDetail: EvaluaScoreLevelValue) => {
        if (
          !details.find(
            (detail: EvaluaScoreLevelValue) => detail.id === initialDetail.id && detail.id !== 'new'
          )
        ) {
          deleteItems.push(initialDetail.id!);
        }
      });

      details.forEach((detail: EvaluaScoreLevelValue, index: number) => {
        const updatedDetail = { ...detail, sortNo: index + 1 };
        if (detail.id && !detail.id.startsWith('new')) {
          // 更新项
          updateItems.push(updatedDetail);
        } else {
          // 新增项
          const { id, ...detailWithoutId } = updatedDetail;
          createItems.push(detailWithoutId);
        }
      });

      // // 批量删除
      // if (deleteItems.length > 0) {
      //   await Promise.all(deleteItems.map((id) => deleteScoreLevelValue({ id })));
      // }

      // 批量更新
      // if (updateItems.leng th > 0) {
      //   await updateScoreLevelValue(updateItems);
      // }
      // // 批量创建
      // if (createItems.length > 0) {
      //   await createScoreLevelValue(createItems);
      // }
      await batchUpdateScoreLevelValue({
        createReqs: createItems,
        updateReqs: updateItems
      });

      return updatedLevel;
    },
    {
      onSuccess: (updatedLevel) => {
        setScoreGrades(
          scoreGrades?.map((item) => (item.id === updatedLevel.id ? updatedLevel : item)) || null
        );
        setSelectedEvaluateLevel(updatedLevel);
        toast({ status: 'success', title: '保存成功' });
        onSuccess();
        refetch();
      }
    }
  );

  const deleteMutation = useMutation(deleteScoreLevelValue, {
    onSuccess: () => {
      toast({ status: 'success', title: '删除成功' });
      form.validateFields(); // 重新验证表单
    }
  });

  const handleDelete = async (id: string) => {
    if (!(id && id.startsWith('new'))) {
      await promisifyDelete({
        title: t('确定删除该评分等级吗？')
      });
      await deleteMutation.mutateAsync({ id: id });
    }

    const details = form.getFieldValue('details');
    const updatedDetails = details.filter(
      (item: EvaluaScoreLevelValue) => item.id?.toString() !== id
    );
    form.setFieldsValue({ details: updatedDetails });
  };

  const clearDynamicFieldsValidate = () => {
    const details = form.getFieldValue('details') || [];
    const resetFields = details.flatMap((_: any, index: any) =>
      ['name', 'scoreMin', 'scoreMax'].map((field) => ({
        name: ['details', index, field],
        errors: []
      }))
    );
    resetFields.push({
      name: ['details'],
      errors: []
    });
    form.setFields(resetFields);
  };

  const handleAdd = () => {
    const details = form.getFieldValue('details') || [];
    const newId = details.reduce((maxId: number, item: EvaluaScoreLevelValue) => {
      if (item.id && item.id.startsWith('new')) {
        const num = parseInt(item.id.slice(3));
        return Math.max(maxId, num);
      }
      return maxId;
    }, 0);

    const newData: EvaluaScoreLevelValue = {
      scoreLevelId: selectedEvaluateLevel!.id!,
      name: '',
      scoreMin: undefined,
      scoreMax: undefined,
      status: 1,
      id: `new${newId + 1}`
    };
    form.setFieldsValue({ details: [...details, newData] });
    setTimeout(() => {
      clearDynamicFieldsValidate();
      actionRef.current?.scrollToBottom();
    }, 0);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      // 添加自定义校验
      const error = validateScoreRanges(values.details);
      if (error) {
        toast({ status: 'error', title: error });
        return;
      }

      if (selectedEvaluateLevel) {
        await updateMutation.mutateAsync(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: EvaluaScoreLevelValue[]) => {
      const { active, over } = event;

      if (active.id !== over.id) {
        form.setFieldsValue({ details: newDataSource });
      }
    },
    [form]
  );

  const columns: TableProps['columns'] = [
    {
      title: '评价等级',
      dataIndex: 'grade',
      width: 400,
      render: (_, record, index) => (
        <Flex justifyContent="space-between" alignItems="center">
          <Space size="middle" align="start">
            <Form.Item
              validateTrigger={[]}
              name={['details', index, 'name']}
              rules={[
                { required: true, message: '请输入评价等级' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const details = getFieldValue('details') || [];
                    const isDuplicate = details.some(
                      (item: EvaluaScoreLevelValue, idx: number) =>
                        item.name === value && idx !== index
                    );
                    if (isDuplicate) {
                      return Promise.reject(new Error('名称不能重复'));
                    }
                    return Promise.resolve();
                  }
                })
              ]}
              style={{ marginBottom: 0 }}
            >
              <Input style={{ width: 80 }} placeholder="等级" />
            </Form.Item>
            <span style={{ lineHeight: '32px' }}>对应:</span>
            <Form.Item
              validateTrigger={[]}
              name={['details', index, 'scoreMin']}
              rules={[{ required: true, message: '请输入最小值' }]}
              style={{ marginBottom: 0 }}
            >
              <InputNumber style={{ width: 80 }} placeholder="最小值" max={1000} min={0} />
            </Form.Item>
            <span style={{ lineHeight: '32px' }}>-</span>
            <Form.Item
              validateTrigger={[]}
              name={['details', index, 'scoreMax']}
              rules={[{ required: true, message: '请输入最大值' }]}
              style={{ marginBottom: 0 }}
            >
              <InputNumber style={{ width: 80 }} placeholder="最大值" max={1000} min={0} />
            </Form.Item>
            <span style={{ lineHeight: '32px' }}>分数段</span>
          </Space>
          <SvgIcon
            cursor="pointer"
            name="trash"
            color="#ff3e3c"
            w="16px"
            h="16px"
            onClick={() => handleDelete(record.id)}
            style={{ cursor: 'pointer' }}
          />
        </Flex>
      )
    }
  ];

  return (
    <Flex flexDir="column" w="100%" h="100%">
      <MyBox
        isLoading={isLoadingScoreLevel}
        w="100%"
        p={4}
        pb={0}
        borderRadius="md"
        flex="1"
        overflow="auto"
      >
        {selectedEvaluateLevel ? (
          <Form form={form} layout="vertical" className={styles['my-form']} validateTrigger={[]}>
            <Form.Item
              validateTrigger={[]}
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.details !== currentValues.details
              }
            >
              {({ getFieldValue }) => {
                const details = getFieldValue('details');

                return (
                  <>
                    <Form.Item
                      validateTrigger={[]}
                      label="评分项目名称"
                      name="name"
                      rules={[{ required: true, message: '请输入评分项目名称' }]}
                    >
                      <Input placeholder="请输入评分项目名称" />
                    </Form.Item>
                    <Form.Item
                      validateTrigger={[]}
                      label="评分等级设置"
                      name="details"
                      rules={[
                        { required: true, message: '请设置评分等级' },
                        {
                          validator: async (_, value) => {
                            const errors = validateScoreRanges(value);
                            if (errors) {
                              throw new Error(errors);
                            }
                          }
                        }
                      ]}
                      shouldUpdate={(prevValues, currentValues) => prevValues != currentValues}
                    >
                      <Box
                        css={{
                          '& .ant-table-cell-row-hover': {
                            background: 'transparent!important'
                          },
                          '& td': {
                            'border-bottom': 'none!important'
                          },
                          '& .ant-input-number': {
                            border: 'none!important'
                          }
                        }}
                      >
                        <MyTable
                          ref={actionRef}
                          columns={columns}
                          dataSource={details || []}
                          headerConfig={{
                            showHeader: false
                          }}
                          boxStyle={{
                            py: 0,
                            pt: 0,
                            pb: respDims(20)
                          }}
                          dragConfig={{
                            enabled: true,
                            rowKey: 'id',
                            onDragEnd: handleDragSortEnd
                          }}
                          showHeader={false}
                          pagination={false}
                          pageConfig={{
                            showPaginate: false
                          }}
                          emptyConfig={{
                            EmptyComponent: () => <></>
                          }}
                        />
                      </Box>
                      <Box
                        as="button"
                        width="173px"
                        height="32px"
                        borderRadius="8px"
                        border="1px solid primary.5"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        w="100%"
                        cursor="pointer"
                        _hover={{ bg: '#eff4fe' }}
                        onClick={handleAdd}
                      >
                        <HStack spacing={2}>
                          <SvgIcon name="plus" w="16px" h="16px" color="primary.5" />
                          <Text color="primary.5" fontSize="14px">
                            添加评分等级
                          </Text>
                        </HStack>
                      </Box>
                    </Form.Item>
                    <Form.Item
                      validateTrigger={[]}
                      label="评分项目状态"
                      name="status"
                      valuePropName="checked"
                      rules={[{ required: true, message: '请选择评分项目状态' }]}
                    >
                      <Flex>
                        <Switch
                          disabled={!!selectedEvaluateLevel.bindCount}
                          value={getFieldValue('status')}
                          onChange={(checked) =>
                            form.setFieldsValue({ ...details, status: checked })
                          }
                        ></Switch>
                        <Box ml={2} color="#4E5969" fontSize={respDims(14, 12)}>
                          当前评分项目已被评价指标关联,不可禁用
                        </Box>
                      </Flex>
                    </Form.Item>
                  </>
                );
              }}
            </Form.Item>
          </Form>
        ) : (
          <Text color="gray.500">请选择一个评分等级以查看详情</Text>
        )}
      </MyBox>
      <Flex
        justifyContent="flex-end"
        alignItems="center"
        padding={respDims(10)}
        borderTop="1px solid #ccc"
        h={respDims(65)}
      >
        <Button
          variant="grayBase"
          mr={3}
          onClick={async () => {
            await unSaveBeformConfirm();
            onClose();
          }}
        >
          {t('取消')}
        </Button>
        {selectedEvaluateLevel && (
          <Button colorScheme="blue" isLoading={updateMutation.isLoading} onClick={handleSave}>
            {t('保存')}
          </Button>
        )}
      </Flex>
    </Flex>
  );
};

export default LevelDetail;
