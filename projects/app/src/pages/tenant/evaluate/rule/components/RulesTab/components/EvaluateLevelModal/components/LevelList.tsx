import React, { useEffect, useState } from 'react';
import {
  Box,
  Flex,
  VStack,
  Center,
  Spinner,
  Text,
  HStack,
  MenuButton,
  Input
} from '@chakra-ui/react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import { useEvaluateLevel } from './EvaluateLevelProvider';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { getScoreLevelList, deleteScoreLevel, createScoreLevel } from '@/api/tenant/evaluate/rule';

import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import { EvaluaScoreLevel } from '@/types/api/tenant/evaluate/rule';
import { respDims } from '@/utils/chakra';

const LevelList = () => {
  const { t } = useTranslation();
  const {
    selectedEvaluateLevel,
    setSelectedEvaluateLevel,
    formStatus,
    setFormStatus,
    scoreGrades,
    setScoreGrades,
    unSaveBeformConfirm
  } = useEvaluateLevel();
  const { toast } = useToast();
  const [editingGradeId, setEditingGradeId] = useState<string | null>(null);
  const [newGradeName, setNewGradeName] = useState<string>('');

  // 在组件内部
  const { isLoading, refetch } = useQuery(['init'], () => getScoreLevelList({}), {
    onSuccess: (data) => {
      if (data.length > 0) {
        if (data.find((item) => item.id == selectedEvaluateLevel?.id)) {
          setSelectedEvaluateLevel(selectedEvaluateLevel ? { ...selectedEvaluateLevel } : null);
        } else {
          setSelectedEvaluateLevel(data[0]);
        }
      }

      setScoreGrades([...data]);
    }
  });

  const createMutation = useMutation(createScoreLevel, {
    onSuccess: () => {
      setNewGradeName('');
      setEditingGradeId(null);
      toast({ status: 'success', title: t('创建成功') });
      refetch();
    }
  });

  const handleDelete = (grade: EvaluaScoreLevel) => {
    MessageBox.confirm({
      title: '删除',
      content: '删除该评分等级后，不可恢复，确认删除？',
      onOk: async () => {
        deleteScoreLevel({ id: grade.id! }).then(() => {
          refetch();
          toast({ status: 'success', title: t('删除成功') });
        });
      }
    });
  };
  const handleAddGrade = () => {
    setNewGradeName('');
    setEditingGradeId('new');
    setFormStatus('add');
  };

  const handleSaveGrade = (gradeId: string, name: string) => {
    if (!name.trim()) {
      if (gradeId === 'new') {
        setEditingGradeId(null);
      }
      return;
    }
    if (gradeId === 'new') {
      createMutation.mutate({ name });
    }
  };

  const handleInputBlur = (gradeId: string, name: string) => {
    handleSaveGrade(gradeId, name);
  };

  const handleInputKeyPress = (
    e: React.KeyboardEvent<HTMLInputElement>,
    gradeId: string,
    name: string
  ) => {
    if (e.key === 'Enter') {
      handleSaveGrade(gradeId, name);
    }
  };

  const handleClickGrade = async (grade: EvaluaScoreLevel) => {
    await unSaveBeformConfirm();

    setSelectedEvaluateLevel(grade);
    setEditingGradeId(null); // 取消新增状态
    setNewGradeName(''); // 清空新增评分等级名称
  };
  useEffect(() => {}, [scoreGrades]);

  return (
    <Flex w="100%" flexDir="column" h="100%">
      <Box w="100%" flex="1" overflow="auto">
        {isLoading ? (
          <Center h="100%">
            <Spinner size="xl" />
          </Center>
        ) : (
          <VStack align="start" spacing={0}>
            {scoreGrades?.map((grade) => (
              <Box
                key={grade.id}
                w="full"
                p={2}
                pl={respDims(20)}
                position="relative"
                bg={selectedEvaluateLevel?.id === grade.id ? '#f8f9fb' : 'white'}
                borderRight="solid 3px #fff"
                borderColor={selectedEvaluateLevel?.id === grade.id ? 'primary.600' : 'white'}
                cursor="pointer"
                onClick={() => handleClickGrade(grade)}
                _hover={{
                  boxShadow:
                    '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                  '& .delete-button': { display: 'flex' }
                }}
              >
                <Box>
                  {}
                  <Flex
                    justifyContent="space-between"
                    flexDir="column"
                    color={selectedEvaluateLevel?.id === grade.id ? 'primary.600' : '#303133'}
                  >
                    <Flex
                      position="relative"
                      color={selectedEvaluateLevel?.id === grade.id ? 'primary.500' : '#303133'}
                      alignItems="center"
                      mb={respDims(5)}
                    >
                      {grade.name}
                      <Box
                        className="delete-button"
                        display="none"
                        w={respDims(30)}
                        h={respDims(30)}
                        position="absolute"
                        right="0"
                        top="50%"
                        transform="translateY(-50%)"
                        _hover={{ bg: 'myWhite.600' }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(grade);
                        }}
                      >
                        <Center>
                          <SvgIcon
                            name="trash"
                            color={'#d43b3b'}
                            w={respDims(16)}
                            h={respDims(16)}
                          />
                        </Center>
                      </Box>
                    </Flex>
                    <Box
                      color="#909399"
                      fontSize="14px"
                    >{`已被${grade.bindCount}个评价指标关联`}</Box>
                  </Flex>
                </Box>
              </Box>
            ))}
            {editingGradeId === 'new' && (
              <Box
                w="full"
                p={2}
                pl={respDims(20)}
                position="relative"
                borderRight="solid 3px #fff"
                bg={'#eff4fe'}
                borderColor={'primary.600'}
              >
                <Input
                  value={newGradeName}
                  autoFocus
                  onChange={(e) => setNewGradeName(e.target.value)}
                  onBlur={() => handleSaveGrade('new', newGradeName)}
                  onKeyUp={(e) => handleInputKeyPress(e, 'new', newGradeName)}
                  placeholder="点击空白或键入回车保存"
                />
              </Box>
            )}
          </VStack>
        )}
      </Box>
      {
        <Flex
          justifyContent="center"
          alignItems="center"
          padding={respDims(10)}
          borderTop="1px solid #ccc"
          h={respDims(65)}
        >
          <Box
            as="button"
            width="173px"
            height="32px"
            borderRadius="100px"
            border="1px solid primary.5"
            display="flex"
            alignItems="center"
            justifyContent="center"
            cursor="pointer"
            _hover={{ bg: '#eff4fe' }}
            onClick={handleAddGrade}
          >
            <HStack spacing={2}>
              <SvgIcon name="plus" w="16px" h="16px" color="primary.5" />
              <Text color="primary.5" fontSize="14px">
                添加评分项目
              </Text>
            </HStack>
          </Box>
        </Flex>
      }
    </Flex>
  );
};

export default LevelList;
