import React, { useCallback, useRef, useState } from 'react';
import { Button, Flex, Box, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import PageContainer from '@/components/PageContainer';
import LayoutOverlay from '@/components/LayoutOverlay';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EditIndicatorModal from './EditIndicatorModal';
import MyTable from '@/components/MyTable';
import { Button as AntButton, Input, InputRef } from 'antd';
import {
  listPageIndactor,
  deleteIndactor,
  getIndactorTreeByProject,
  runSortIndicators,
  evaluaIndactorEnable,
  evaluaIndactorDisable,
  evaluaIndactorDetail
} from '@/api/tenant/evaluate/rule';
import { MyTableRef } from '@/components/MyTable/types';
import {
  DimenType,
  EvaluaDimension,
  EvaluaIndactorVO,
  EvaluaIndicatorQueryReq,
  EvaluaProject
} from '@/types/api/tenant/evaluate/rule';
import {
  HasSubIndicatorMap,
  IndicatorType,
  EvaluateTypeMap,
  HasSubIndicator,
  EvaluateType,
  Status,
  ProjectType,
  EducationStage,
  EducationStageMap,
  ScoreInputType
} from '@/constants/api/tenant/evaluate/rule';
import dayjs from 'dayjs';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { Dimensions } from 'reactflow';
import { treeFind, treeToList } from '@/utils/tree';
import styles from '@/pages/index.module.scss';
import { myToFixed } from '@/utils/tools';

const statusStyles = {
  enabled: {
    color: '#52c41a',
    display: 'flex',
    alignItems: 'center'
  },
  disabled: {
    color: '#bfbfbf',
    display: 'flex',
    alignItems: 'center'
  },
  dot: {
    height: '8px',
    width: '8px',
    borderRadius: '50%',
    display: 'inline-block',
    marginRight: '8px'
  }
};
const IndicatorsTab = ({
  onClose,
  onRefresh,
  projectId,
  indactorName,
  paths
}: {
  onClose: () => void;
  onRefresh: () => void;
  projectId: string;
  indactorName: string;
  paths: [DimenType | null, EvaluaDimension | null, EvaluaProject | null];
}) => {
  const searchInputRef = useRef<InputRef>(null);
  const { openOverlay } = useOverlayManager();
  const tableRef = useRef<MyTableRef<EvaluaIndicatorQueryReq, EvaluaIndactorVO>>(null);
  // const { data: indicators, isLoading } = useQuery(['indicators', projectId], () =>
  //   getIndactorTreeByProject({ parentId: projectId })
  // );

  const columns: ColumnsType<EvaluaIndactorVO> = [
    {
      title: '评价指标',
      dataIndex: 'indactorName',
      key: 'indactorName',
      width: '250px'
    },
    {
      title: '存在下级指标',
      dataIndex: 'isHasSub',
      key: 'isHasSub',
      render: (value: HasSubIndicator) => HasSubIndicatorMap[value]?.label || '-'
    },
    {
      title: '评价学段',
      dataIndex: 'stage',
      key: 'stage',
      render: (value: string, record) => {
        if (!value || record.isHasSub === HasSubIndicator.Yes) return '-';

        return (
          value
            .split(',')
            .map((stage) => EducationStageMap[stage as unknown as EducationStage]?.label)
            .filter(Boolean)
            .join('、') || '-'
        );
      }
    },
    {
      title: '评价方式',
      dataIndex: 'evaluateType',
      key: 'evaluateType',
      render: (value: EvaluateType, record) => {
        if (record.isHasSub === HasSubIndicator.Yes) return '-';
        return EvaluateTypeMap[value]?.label || '-';
      }
    },
    {
      title: '分值范围',
      dataIndex: 'scoreRange',
      key: 'scoreRange',
      render: (_, record) => {
        if (record.isHasSub === HasSubIndicator.Yes) return '-';
        if (
          record.scoreInputType === ScoreInputType.Fixed ||
          record.evaluateType !== EvaluateType.Score
        )
          return '-';
        const min =
          record.scoreMin !== undefined && record.scoreMin !== null ? record.scoreMin : '-';
        const max =
          record.scoreMax !== undefined && record.scoreMax !== null ? record.scoreMax : '-';
        if (min == '-' && max == '-') {
          return '-';
        }
        return `${min} 至 ${max}`;
      }
    },
    {
      title: '评分默认值',
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      render: (_, record) => {
        if (record.evaluateType !== EvaluateType.Score || record.isHasSub === HasSubIndicator.Yes)
          return '-';
        if (record.evaluateType == EvaluateType.Score) {
          return record.score !== undefined && record.score !== null ? record.score : '-';
        }
      }
    },
    {
      title: '评分占比',
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      align: 'center',
      render: (scoreRate, record) => {
        if (record.isHasSub === HasSubIndicator.Yes) {
          return `${myToFixed(scoreRate * 100, 2)}%`;
        }
        if (record.evaluateType !== EvaluateType.Score) {
          return '-';
        }
        return `${myToFixed(scoreRate * 100, 2)}%`;
      }
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      dataIndex: 'status',
      render: (status: number) => {
        return (
          <Box style={status === 1 ? statusStyles.enabled : statusStyles.disabled}>
            <Box
              style={{
                ...statusStyles.dot,
                backgroundColor: status === 1 ? '#52c41a' : '#bfbfbf'
              }}
            />
            {status === 1 ? '启用' : '禁用'}
          </Box>
        );
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      render: (value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm') : '')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Flex>
          <AntButton type="link" onClick={() => handleEdit(record)}>
            编辑
          </AntButton>
          <AntButton type="link" onClick={() => handleToggleStatus(record)}>
            {record.status === 1 ? '禁用' : '启用'}
          </AntButton>
          {record.isHasSub === HasSubIndicator.Yes && (
            <AntButton type="link" onClick={() => handleAddSubIndicator(record)}>
              添加子指标
            </AntButton>
          )}
          {record.status === 2 && (
            <AntButton type="link" danger onClick={() => handleDelete(record.id!)}>
              删除
            </AntButton>
          )}
        </Flex>
      )
    }
  ];

  const onAddMainindicators = () => {
    const parentNode: EvaluaIndactorVO = paths[2]! as any;
    const treeData = tableRef.current?.data || [];

    parentNode.children = treeData;
    openOverlay({
      Overlay: EditIndicatorModal,
      props: {
        formStatus: 'create',
        parentId: projectId,
        parentNode,
        onSuccess: () => {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: EvaluaIndactorVO[]) => {
      const { active, over } = event;

      if (active.id !== over.id) {
        const sortParams = treeToList(newDataSource).map((item, index) => ({
          id: item.id!,
          sortNo: index + 1
        }));

        try {
          await runSortIndicators(sortParams);
          tableRef.current?.reload();
        } catch (error) {}
      }
    },
    [runSortIndicators]
  );

  const handleSearch = () => {
    const searchValue = searchInputRef?.current?.input?.value || ''; // 修改为 Ant Design Input 的获取方式
    tableRef.current?.setQuery({
      type: IndicatorType.EvaluationIndicator,
      parentId: projectId,
      searchKey: searchValue
    });
    tableRef.current?.reload();
  };

  const handleReset = () => {
    if (
      searchInputRef.current &&
      searchInputRef.current?.input &&
      searchInputRef.current?.input.value
    ) {
      searchInputRef.current.input.value = ''; // 修改为 Ant Design Input 的清空方式
    }
    handleSearch();
  };

  const handleEdit = async (indicator: EvaluaIndactorVO) => {
    const treeData = tableRef.current?.data || [];
    // 使用 treeFind 查找父节点

    let parentNode = treeFind<EvaluaIndactorVO>(
      treeData,
      (node) => node.id == indicator.parentId,
      'children'
    );
    console.log('parentNode', parentNode, treeData);
    if (!parentNode) {
      parentNode = paths[2]! as any;
      const treeData = tableRef.current?.data || [];
      if (parentNode) {
        parentNode.children = treeData;
      }
    }
    const data = await evaluaIndactorDetail({ id: indicator.id! });
    data.scoreLevel = String(data.scoreLevel);
    data.scoreLevelId = String(data.scoreLevelId);
    data.children = indicator.children || [];
    openOverlay({
      Overlay: EditIndicatorModal,
      props: {
        formStatus: 'edit',
        indicatorId: indicator.id,
        initialData: data,
        parentNode: parentNode,
        parentId: String(data.parentId),
        onSuccess: () => {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleToggleStatus = (data: EvaluaIndactorVO) => {
    const isEnabling = data.status !== Status.Normal;
    const title = isEnabling ? '启用提示' : '禁用提示';
    const content = isEnabling
      ? '启用后,当前评价规则可被规则关联，确认启用？'
      : '禁用后,当前评价规则不可被规则关联,确认禁用？';

    MessageBox.confirm({
      title,
      content,
      onOk: async () => {
        try {
          const toggleFunction = isEnabling ? evaluaIndactorEnable : evaluaIndactorDisable;
          await toggleFunction({
            id: data.id!,
            status: data.status === Status.Disabled ? Status.Normal : Status.Disabled
          });
          Toast.success('操作成功');
          tableRef.current?.reload();
        } catch (error) {
          console.error('Toggle status error:', error);
        }
      }
    });
  };

  const handleAddSubIndicator = (indicator: EvaluaIndactorVO) => {
    openOverlay({
      Overlay: EditIndicatorModal,
      props: {
        formStatus: 'create',
        parentId: indicator.id,
        parentNode: indicator,
        onSuccess: () => {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleDelete = async (id: string) => {
    MessageBox.confirm({
      title: '删除提示',
      content: '删除后,当前评价规则不可恢复,确认删除？',
      onOk: async () => {
        try {
          await deleteIndactor({ id });
          Toast.success('操作成功');
          tableRef.current?.reload();
        } catch (error) {
          console.error('Toggle status error:', error);
        }
      }
    });
  };
  const customExpandIcon = (props: any) => {
    const { expanded, onExpand, record } = props;
    return record.children?.length ? (
      <Box
        display="inline-flex"
        cursor="pointer"
        justifyContent="center"
        alignItems="center"
        onClick={(e) => onExpand(record, e)}
        color={expanded ? '#1A5EFF' : '#909399'}
        style={{ verticalAlign: 'middle', marginBottom: '1px' }}
      >
        {!expanded ? (
          <SvgIcon name="chevronUpCircle" fontSize={respDims(24, 22)} mr={1} />
        ) : (
          <SvgIcon name="chevronUpCircle" mr={1} style={{ transform: 'rotate(180deg)' }} />
        )}
      </Box>
    ) : (
      <></>
    );
  };
  return (
    <LayoutOverlay>
      <Flex h="100%" padding={respDims(20, 20)} flexDirection="column" overflow="hidden">
        <PageContainer bgColor="rgba(255,255,255)">
          <Flex
            justifyContent="space-between"
            alignItems="center"
            px={respDims(32)}
            pt={respDims(16)}
          >
            <Flex alignItems="center">
              <Box
                cursor="pointer"
                color="#1A5EFF"
                display="flex"
                alignItems="center"
                onClick={() => {
                  onRefresh();
                  onClose();
                }}
              >
                <SvgIcon name="chevronLeft" w={respDims('18fpx')} h={respDims('18fpx')} />
                返回维度和项目
              </Box>
              {paths.map((item: any) => {
                return (
                  <>
                    <SvgIcon
                      name="slash"
                      w={respDims('16fpx')}
                      h={respDims('16fpx')}
                      mx={respDims(4)}
                      color="#909399"
                    />
                    {item?.indactorName || item?.name}
                  </>
                );
              })}
            </Flex>
            <Flex className={styles['my-form']}>
              <Input
                ref={searchInputRef}
                prefix={<SvgIcon name="search"></SvgIcon>}
                style={{ width: '200px', borderRadius: '8px', height: '36px' }}
                placeholder="请输入评价指标"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
              />
              <Button
                onClick={() => handleSearch()}
                variant="outline"
                p="0 30px"
                colorScheme="primary"
                ml={respDims(16)}
              >
                查询
              </Button>
              <Button
                onClick={handleReset}
                p="0 30px"
                variant={'grayBase'}
                ml={respDims(16)}
                mr={respDims(16)}
              >
                重置
              </Button>

              <Button p="0 40px" onClick={onAddMainindicators}>
                添加评价指标
              </Button>
            </Flex>
          </Flex>
          <Box flex={1} overflowY="auto" h="92%">
            <MyTable
              dragConfig={{
                enabled: true,
                rowKey: 'id',
                onDragEnd: handleDragSortEnd
              }}
              ref={tableRef}
              columns={columns}
              boxStyle={{
                px: respDims(32),
                py: respDims(4)
              }}
              emptyConfig={{
                EmptyPicComponent: () => <></>
              }}
              pageConfig={{ showPaginate: false }}
              api={getIndactorTreeByProject}
              defaultQuery={{
                type: IndicatorType.EvaluationIndicator,
                parentId: projectId,
                searchKey: ''
              }}
              pagination={false}
              expandable={{
                defaultExpandAllRows: true,
                expandIcon: customExpandIcon
              }}
            />
          </Box>
        </PageContainer>
      </Flex>
    </LayoutOverlay>
  );
};

export default IndicatorsTab;
