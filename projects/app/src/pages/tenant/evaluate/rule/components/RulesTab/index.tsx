import React, { useRef, useMemo } from 'react';
import { Button, Tag } from 'antd';
import { Box, Flex } from '@chakra-ui/react';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useToast } from '@/hooks/useToast';
import { MessageBox, promisifyConfirm, promisifyDelete } from '@/utils/ui/messageBox';
import { RulesTabProps } from '../..';
import EditEvaluationRuleModal from './components/EditEvaluationRuleModal';
import {
  getEvaluationRuleList,
  copyEvaluationRule,
  deleteEvaluationRule
} from '@/api/tenant/evaluate/rule';
import {
  RuleStatus,
  PeriodType,
  Term,
  TermMap,
  PeriodTypeMap
} from '@/constants/api/tenant/evaluate/rule';
import { EvaluationRuleDetail } from '@/types/api/tenant/evaluate/rule';

const RulesTab: React.FC<RulesTabProps> = ({ TabCompoent }) => {
  const actionRef = useRef<MyTableRef>(null);
  const { openOverlay } = useOverlayManager();
  const { toast } = useToast();

  const handleCopy = async (record: EvaluationRuleDetail) => {
    openOverlay({
      Overlay: EditEvaluationRuleModal,
      props: {
        ruleId: record.id,
        formStatus: 'copy',
        onClose: () => {},
        onSuccess() {
          actionRef.current?.reload();
        }
      }
    });
  };

  const handleDelete = async (record: EvaluationRuleDetail) => {
    try {
      await promisifyDelete({
        title: '删除确认',
        content: '确定要删除这条评价规则吗？'
      });
      const result = await deleteEvaluationRule({ id: record.id! });
      if (result) {
        toast({
          status: 'success',
          title: '删除成功'
        });
        actionRef.current?.reload();
      }
    } catch (error) {
      // 用户取消删除操作
    }
  };

  const handleEdit = (record: EvaluationRuleDetail) => {
    openOverlay({
      Overlay: EditEvaluationRuleModal,
      props: {
        formStatus: 'edit',
        ruleId: record.id,
        onClose: () => {},
        onSuccess() {
          actionRef.current?.reload();
        }
      }
    });
  };

  const columns = useMemo(() => {
    return [
      {
        title: '评价规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName'
      },
      {
        title: '评价方',
        dataIndex: 'evaluatorNum',
        key: 'evaluatorNum',
        render: (num: number, record: EvaluationRuleDetail) =>
          `${record.evaluatorType === 1 ? '教师' : '未知'}(${record.evaluatorNum}人)`
      },
      {
        title: '被评价方',
        dataIndex: 'evaluateeNum',
        key: 'evaluateeNum',
        render: (num: number, record: EvaluationRuleDetail) =>
          `${record.evaluateeType === 1 ? '学生' : '未知'}(${record.evaluateeNum}人)`
      },
      {
        title: '评价学年',
        dataIndex: 'year',
        key: 'year'
      },
      {
        title: '评价学期',
        dataIndex: 'term',
        key: 'term',
        render: (term: Term) => TermMap[term] && TermMap[term].label
      },
      {
        title: '评价周期',
        dataIndex: 'periodType',
        key: 'periodType',
        render: (periodType: PeriodType) =>
          PeriodTypeMap[periodType] && PeriodTypeMap[periodType].label
      },
      {
        title: '评价时间',
        key: 'evaluationTime',
        render: (_: any, record: EvaluationRuleDetail) =>
          `${record.startTime?.slice(0, 16)} ~ ${record.endTime?.slice(0, 16)}`
      },
      {
        title: '关联指标(个)',
        dataIndex: 'indactorNum',
        key: 'indactorNum'
      },
      {
        title: '评价状态',
        dataIndex: 'ruleStatus',
        key: 'ruleStatus',
        render: (status: RuleStatus) => {
          const statusMap = {
            [RuleStatus.NotStarted]: { color: 'orange', text: '未开始' },
            [RuleStatus.InProgress]: { color: 'green', text: '进行中' },
            [RuleStatus.Finished]: { color: 'default', text: '已结束' }
          };
          return (
            <Tag color={statusMap[status] && statusMap[status]!.color}>
              {statusMap[status] && statusMap[status].text}
            </Tag>
          );
        }
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime'
      },
      {
        title: '操作',
        key: 'action',
        render: (_: any, record: EvaluationRuleDetail) => (
          <>
            {record.ruleStatus !== RuleStatus.Finished && (
              <Button type="link" onClick={() => handleEdit(record)}>
                编辑
              </Button>
            )}

            <Button type="link" onClick={() => handleCopy(record)}>
              复制
            </Button>
            {record.ruleStatus !== RuleStatus.InProgress && (
              <Button type="link" danger onClick={() => handleDelete(record)}>
                删除
              </Button>
            )}
          </>
        )
      }
    ];
  }, []);

  return (
    <MyTable
      columns={columns}
      api={getEvaluationRuleList}
      rowKey="id"
      ref={actionRef}
      emptyConfig={{
        EmptyPicComponent: () => <></>
      }}
      headerConfig={{
        showHeader: true,
        showIfEmpty: true,
        HeaderComponent: (props) => (
          <Flex justifyContent="space-between" w="100%" alignItems="center">
            <TabCompoent></TabCompoent>
            <SearchBar {...props}></SearchBar>
          </Flex>
        )
      }}
    />
  );
};

export default RulesTab;
