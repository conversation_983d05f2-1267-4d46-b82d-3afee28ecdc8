import React, { useCallback, useEffect, useRef } from 'react';
import { Box, Flex, Text, Button as ChakraButton, Tag } from '@chakra-ui/react';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { IndicatorTree, useIndicator } from '../../../IndicatorContext';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { respDims } from '@/utils/chakra';
import { AddIcon } from '@chakra-ui/icons';
import { MyTableRef } from '@/components/MyTable/types';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { Button } from 'antd';
import {
  listPageProject,
  deleteProject,
  listProject,
  runSortIndicators,
  getIndactorTreeByProject
} from '@/api/tenant/evaluate/rule';
import { EvaluaIndactorVO, EvaluaProject } from '@/types/api/tenant/evaluate/rule';
import { useToast } from '@/hooks/useToast';
import { promisifyDelete } from '@/utils/ui/messageBox';
import { treeToList, treeTraverse } from '@/utils/tree';
import { HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';
import MyTooltip from '@/components/MyTooltip';

const ProjectList: React.FC = () => {
  const {
    selectedProject,
    setSelectedProject,
    selectedDimension,
    selectedDimensionCategory,
    selectedIndicators,
    setTreeData,
    treeData,
    selectedIndicatorPathMap
  } = useIndicator();
  const { openOverlay } = useOverlayManager();
  const tableRef = useRef<MyTableRef>(null);
  const router = useRouter();
  const { toast } = useToast();
  useEffect(() => {
    setSelectedProject(null);
  }, [selectedDimension]);
  const columns: ColumnsType<EvaluaProject> = [
    {
      title: '评价项目',
      dataIndex: 'indactorName',
      key: 'indactorName',
      render: (_: any, record) => {
        return (
          <Flex alignItems="center" justifyContent="space-between">
            <Flex alignItems="center" flex="1">
              <MyTooltip label={record.indactorName}>
                <Box maxWidth="80px" className="textEllipsis">
                  {record.indactorName}
                </Box>
              </MyTooltip>
              <Box>
                {record.subjects?.slice(0, 1).map((item) => {
                  return (
                    <Tag color="#2BA471" bgColor="#E3F9E9" ml={1} my={1} key={item.id}>
                      {item.subjectName}
                    </Tag>
                  );
                })}
                {(record.subjects?.length || 0) > 1 && (
                  <MyTooltip
                    label={
                      <Text>
                        {record.subjects
                          ?.slice(1)
                          .map((item) => item.subjectName)
                          .join('、')}
                      </Text>
                    }
                  >
                    <Tag color="#2BA471" bgColor="#E3F9E9" ml={2}>
                      +{(record.subjects?.length || 0) - 1}
                    </Tag>
                  </MyTooltip>
                )}
              </Box>
            </Flex>
          </Flex>
        );
      }
    },

    {
      title: '占比',
      dataIndex: 'indactorName',
      key: 'indactorName',
      width: '150px',
      render: (_: any, record) => {
        const allLength = treeToList(
          treeData.find((item) => item.id == record.id)?.children || []
        ).filter((item) => item.isHasSub == HasSubIndicator.No).length;
        const selectLength = Object.values(selectedIndicatorPathMap || {}).filter(
          (item: string[]) => {
            return !!item.find((id) => id == record.id);
          }
        ).length;

        return (
          <Flex alignItems="center" justifyContent="flex-end">
            <Flex alignItems="center">
              <Box>占比{Math.floor((record.scoreRate || 0) * 100)}%</Box>
              {<Box ml={2}> ({`${selectLength}/${allLength}`})</Box>}
            </Flex>
          </Flex>
        );
      }
    }
  ];

  const convertToIndicatorTree = (item: any): IndicatorTree => {
    return {
      id: item.id,
      isHasSub: item.isHasSub,
      children: Array.isArray(item.children) ? item.children.map(convertToIndicatorTree) : []
    };
  };

  const buildTreeData = async (data: EvaluaProject[]) => {
    const result = await Promise.all(
      data.map((item) => {
        return getIndactorTreeByProject({ parentId: item.id });
      })
    );

    const newTreeData: IndicatorTree[] = data.map((item, index) => {
      return {
        id: item.id!,
        isHasSub: item.isHasSub || HasSubIndicator.Yes,
        children: Array.isArray(result[index])
          ? result[index].map(convertToIndicatorTree)
          : [convertToIndicatorTree(result[index])]
      };
    });

    setTreeData(newTreeData);
  };

  const onRow = useCallback(
    (record: EvaluaProject) => ({
      onClick: () => {
        if (record.id !== selectedProject?.id) {
          setSelectedProject(record);
        }
      }
    }),
    [selectedProject, setSelectedProject]
  );

  return (
    <Flex flexDirection="column" h="100%" w="100%">
      <Flex bg="#f7f9fb" borderRadius="8px" flex="1" h="0" p={respDims(16)} flexDirection="column">
        <Box
          flex="1"
          overflow="auto"
          css={{
            '& .selected-row': {
              background: '#eff5fe!important',
              borderRadius: '8px'
            }
          }}
        >
          {
            <MyTable
              ref={tableRef}
              columns={columns}
              api={(params) => {
                if (!params.parentId) {
                  return [] as any;
                }
                return listProject(params);
              }}
              queryConfig={{
                onSuccess: (data) => {
                  let list = data as EvaluaProject[];
                  if (list[0]) {
                    setSelectedProject(list[0] || '');
                    buildTreeData(list);
                  } else {
                    setSelectedProject(null);
                  }
                }
              }}
              showHeader={false}
              defaultQuery={{
                parentId: selectedDimension?.id
              }}
              boxStyle={{
                px: '0',
                py: '0',
                borderRadius: '12px',
                css: {
                  '& .ant-table': {
                    borderRadius: '12px 12px 12px 12px!important',
                    overflow: 'hidden!important'
                  },
                  '.ant-table tbody .ant-table-row:last-child .ant-table-cell': {
                    borderBottom: 'none'
                  }
                }
              }}
              size="small"
              headerConfig={{
                showHeader: false
              }}
              pageConfig={{
                showPaginate: false
              }}
              onRow={onRow}
              rowClassName={(record) => (selectedProject?.id === record.id ? 'selected-row' : '')}
            />
          }
        </Box>
      </Flex>
    </Flex>
  );
};

export default ProjectList;
