import React, { useState, useEffect, useMemo } from 'react';
import {
  Form,
  Input,
  Select,
  Radio,
  DatePicker,
  Table,
  Row,
  Col,
  Cascader,
  TreeDataNode
} from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SelectPersonModal, { SubDeptType } from './SelectPersonModal';
import SelectIndicatorModal from './SelectIndicatorModal/index';
import { Box, Flex, HStack, Link, Text, Button } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useQuery } from '@tanstack/react-query';
import { ModalBody, ModalFooter } from '@chakra-ui/react';
import MyTooltip from '@/components/MyTooltip';
import { respDims } from '@/utils/chakra';
import { QuestionOutlineIcon } from '@chakra-ui/icons';
import styles from '@/pages/index.module.scss';
import SvgIcon from '@/components/SvgIcon';
import {
  getEvaluationRuleDetail,
  addEvaluationRule,
  updateEvaluationRule,
  getTeachers,
  getIndactorTreeByAuto
} from '@/api/tenant/evaluate/rule';

import {
  EvaluatorType,
  EvaluateeType,
  MatchType,
  PeriodType,
  RuleStatus,
  Term,
  EvaluatorTypeMap,
  EvaluateeTypeMap,
  MatchTypeMap,
  PeriodTypeMap,
  TermMap,
  HasSubIndicator
} from '@/constants/api/tenant/evaluate/rule';
import { useToast } from '@/hooks/useToast';
import {
  EvaluationRuleDetail,
  EvaluationRule,
  TeacherType,
  EvaluaIndactorVO,
  EvaluaRuleEvaluatee,
  EvaluaRuleEvaluator
} from '@/types/api/tenant/evaluate/rule';
import { getClientSchoolDeptTree } from '@/api/tenant/teamManagement/student';
import { getClientSemesterList } from '@/api/tenant/teamManagement/semester';
import { termItem } from '@/types/api/tenant/teamManagement/semester';
import {
  ClientStudentPageType,
  Department,
  DepartmentTree
} from '@/types/api/tenant/teamManagement/student';
import 'dayjs/locale/zh-cn';

import dayjs from 'dayjs';
import { treeFindPaths, treeToList } from '@/utils/tree';
import { useRouter } from 'next/router';

import { Button as AntButton } from 'antd';
import { RadioChangeEvent } from 'antd/lib';
import { rest } from 'lodash';
import SelectClazzModal from './SelectClazzModal';
import { log } from 'console';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
dayjs.extend(localizedFormat as any);
dayjs.extend(customParseFormat as any);
dayjs.locale('zh-cn');
dayjs.extend(weekday as any);
dayjs.extend(localeData as any);

interface EditEvaluationRuleModalProps {
  onClose: () => void;
  onSuccess: () => void;
  ruleId?: string;
  formStatus: 'add' | 'edit' | 'copy';
}

const { Option } = Select;
const { RangePicker } = DatePicker;

const EditEvaluationRuleModal: React.FC<EditEvaluationRuleModalProps> = ({
  onClose,
  onSuccess,
  ruleId,
  formStatus
}) => {
  const [form] = Form.useForm<EvaluationRuleDetail>();
  const [matchType, setMatchType] = useState<MatchType>(MatchType.AutoMatch);

  const [indicators, setIndicators] = useState<EvaluaIndactorVO[]>([]);
  const [evaluators, setEvaluators] = useState<EvaluaRuleEvaluator[]>([]);

  const { openOverlay, OverlayContainer } = useOverlayManager();
  const [termList, setTermList] = useState<termItem[]>([]);
  const { toast } = useToast();

  const { data: semesterList } = useQuery(['semesterList'], getClientSemesterList, {
    onSuccess() {}
  });
  const { data: ruleDetail, isLoading } = useQuery(
    ['evaluationRule', ruleId],
    () => getEvaluationRuleDetail({ id: ruleId! }),
    {
      enabled: (formStatus === 'edit' || formStatus === 'copy') && !!ruleId,
      onSuccess: (data) => {
        data.startTime = dayjs(data.startTime) as any;
        data.endTime = dayjs(data.endTime) as any;

        setIndicators(treeToList(data.indacators || []));
        data.indacators = treeToList(data.indacators || []);

        setEvaluators(data?.evaluators || []);
        if (formStatus === 'copy') {
          // 如果是复制,清除id字段
          const { id, ...restData } = data;
          restData.copyId = id!;
          form.setFieldsValue(restData);
        } else {
          form.setFieldsValue(data);
        }

        setMatchType(data.matchType!);
      }
    }
  );

  const { data: teachers } = useQuery(['INIT'], () => getTeachers({}));
  const router = useRouter();
  const { isLoading: isTreeLoading, data: treeData } = useQuery(
    ['treeData'],
    getClientSchoolDeptTree,
    {
      onSuccess(treeData) {
        // 如果在这里也需要设置路径,可以这样使用:
      }
    }
  );

  const processInDisabled = useMemo(() => {
    return ruleDetail?.ruleStatus === RuleStatus.InProgress && !(formStatus === 'copy');
  }, [ruleDetail]);

  useEffect(() => {
    if (ruleDetail?.evaluatees && treeData?.length) {
      const evaluateePaths = treeFindPaths(
        treeData || [],
        ruleDetail.evaluatees.map((e) => String(e.clazzId))
      );

      const formattedPaths = Object.values(evaluateePaths).map((path) =>
        path.map((node) => node.id)
      );
      if (ruleDetail.matchType === MatchType.AutoMatch) {
        form.setFieldValue('evaluateeIds', formattedPaths);
      } else {
        form.setFieldValue('evaluateeIdsCoustom', formattedPaths);
      }
    }
  }, [treeData, ruleDetail]);

  useEffect(() => {
    if (ruleDetail?.year) {
      handleSemesterChange(ruleDetail?.year);
    }
  }, [semesterList, ruleDetail]);

  const handleSelectPerson = (type: 'evaluator' | 'evaluatee') => {
    openOverlay({
      Overlay: SelectPersonModal,
      props: {
        type: type,
        formStatus: processInDisabled ? 'view' : 'select',
        defaultSelectEvaluators: evaluators,
        onClose: () => {},
        onSelect: (data: EvaluaRuleEvaluator[]) => {
          if (type === 'evaluator') {
            setEvaluators(data);
            form.setFieldsValue({ evaluators: data });
          } else {
            form.setFieldsValue({ evaluatees: data });
          }
        }
      }
    });
  };

  const handleSelectIndicator = (modalStatus: 'view' | 'select' | 'cancel') => {
    const formData = form.getFieldsValue();
    // 可以取消指标判断
    if (modalStatus == 'view' && formData.matchType === MatchType.AutoMatch) {
      modalStatus = 'cancel';
    }
    openOverlay({
      Overlay: SelectIndicatorModal,
      props: {
        modalStatus,
        formData: form.getFieldsValue(),
        onClose: () => {},
        onSuccess: (selectedIndicators: EvaluaIndactorVO[]) => {
          form.setFieldValue('indacators', selectedIndicators);
          setIndicators(selectedIndicators);
        },
        defaultSelectedIndicators: indicators // 传入当前已选择的指标
      }
    });
  };

  const handleAutoGetIndicator = async (evaluateeIds: string[]) => {
    // 通过evaluateeIds找到所有年级，treeData找

    const paths = treeFindPaths(
      treeData || [],
      evaluateeIds.map((item) => {
        return item[item.length - 1];
      })
    );
    const clazzIds: string[] = (Object.values(paths)
      .map((path) => {
        return path.find((item) => item.subDeptType === SubDeptType.Class)?.id;
      })
      .filter((id) => !!id) || []) as string[];
    const gradeIds: string[] = (Object.values(paths)
      .map((path) => {
        return path.find((item) => item.subDeptType === SubDeptType.Grade)?.id;
      })
      .filter((id) => !!id) || []) as string[];

    const formData = form.getFieldsValue();

    getIndactorTreeByAuto({
      startTime: dayjs(formData.startTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(formData.endTime).format('YYYY-MM-DD HH:mm:ss'),
      semesterId: formData.semesterId!,
      periodType: formData.periodType!,
      gradeIds: [...new Set(gradeIds)],
      clazzIds: [...new Set(clazzIds)],
      ruleId: ruleDetail?.id
    }).then((res) => {
      res = treeToList(res).filter((item) => item.isHasSub === HasSubIndicator.No);

      if (res && res.length) {
        toast({
          status: 'success',
          title: `匹配到${res.length}个指标`
        });
        form.setFieldValue('indacators', res);
        setIndicators(res);
      } else {
        toast({
          status: 'warning',
          title: '匹配指标为空'
        });
      }
    });
  };

  const handleSubmit = async () => {
    try {
      if (!teachers?.length) {
        return toast({
          title: '请同步教师信息',
          status: 'warning'
        });
      }
      const values = await form.validateFields();

      // 自动匹配时，后端为默认’0‘ 为默认评价人，需要过滤
      values.evaluatorIds = evaluators
        .map((evaluator) => evaluator.evaluatorId)
        .filter((id) => id != '0') as string[];
      if (values.startTime) {
        values.startTime = dayjs(values.startTime).format('YYYY-MM-DD HH:mm:ss');
      }
      if (values.endTime) {
        values.endTime = dayjs(values.endTime).format('YYYY-MM-DD HH:mm:ss');
      }
      if (values.evaluateeIds && values.matchType === MatchType.AutoMatch) {
        values.evaluateeIds = values.evaluateeIds
          .filter((path) => !treeData?.some((node) => node.id === path[path.length - 1]))
          .map((path) => path[path.length - 1]);
        values.evaluatorIds = [];
      } else {
        values.evaluateeIds =
          values.evaluateeIdsCoustom
            ?.filter((path) => !treeData?.some((node) => node.id === path[path.length - 1]))
            .map((path) => path[path.length - 1]) || [];

        if (!values.evaluatorIds.length) {
          return toast({
            status: 'warning',
            title: '请选择评价人'
          });
        }
      }

      values.indacatorIds = indicators.map((indicator) => indicator.id) as string[];
      values.evaluateeType = EvaluateeType.Student;
      values.evaluatorType = EvaluatorType.Teacher;
      Reflect.deleteProperty(values, 'evaluateeIdsCoustom');
      let apiFunc;
      if (formStatus === 'add' || formStatus === 'copy') {
        Reflect.deleteProperty(values, 'id');
        apiFunc = addEvaluationRule;
      } else {
        values.id = ruleDetail?.id;
        apiFunc = updateEvaluationRule;
      }
      const result = await apiFunc(values);
      if (result) {
        toast({
          status: 'success',
          title: `${formStatus === 'edit' ? '编辑' : '添加'}成功`
        });
        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleSemesterChange = (value: string) => {
    let target = semesterList?.find((item) => item.year === value);
    if (target) {
      form.setFieldValue('semesterId', target.id);
      setTermList(target.termList);
    }
  };

  const validateTimeRange = (_: any, value: any) => {
    const { startTime, endTime } = form.getFieldsValue(['startTime', 'endTime']);
    const now = dayjs();

    if (!startTime || !endTime) {
      return Promise.reject(new Error('请选择开始时间和结束时间'));
    }

    if (startTime.isAfter(endTime)) {
      return Promise.reject(new Error('开始时间不能晚于结束时间'));
    }

    if (endTime.isBefore(now)) {
      return Promise.reject(new Error('结束时间不能早于当前时间'));
    }

    return Promise.resolve();
  };
  const handleMatchTypeChange = (e: RadioChangeEvent) => {
    setMatchType(e.target.value);
    // 清空被评价方
    if (e.target.value === MatchType.AutoMatch) {
      form.setFieldValue('evaluateeIdsCoustom', undefined);
    } else {
      form.setFieldValue('evaluateeIds', undefined);
    }
    setIndicators([]);
    form.setFieldValue('indacators', []);
  };

  const hanldeLookClazz = () => {
    const evaluatees = form.getFieldValue('evaluatees');
    const paths = treeFindPaths(
      treeData || [],
      ruleDetail?.evaluatees?.map((e) => String(e.clazzId)) || []
    );

    openOverlay({
      Overlay: SelectClazzModal,
      props: {
        onClose: () => {},
        selectClasses: Object.values(paths).map((value, index) => {
          return {
            id: index,
            name: value.map((item) => item.deptName).join('/')
          };
        }) // 传入当前已选择的指标
      }
    });
  };

  return (
    <MyModal
      title={
        formStatus === 'add'
          ? '添加评价规则'
          : formStatus === 'edit'
            ? '编辑评价规则'
            : '复制评价规则'
      }
      isOpen={true}
      onClose={onClose}
      closeOnOverlayClick={false}
      minW={['850px']}
    >
      <ModalBody pt={respDims(24)} borderBottom="1px solid #E5E7EB">
        <OverlayContainer></OverlayContainer>
        <Form
          form={form}
          initialValues={
            ruleDetail || {
              matchType: MatchType.AutoMatch,
              evaluateeType: EvaluateeType.Student,
              evaluatorType: EvaluatorType.Teacher
            }
          }
          className={`${styles['my-form']} ${styles['my-form-vertical']}`}
          layout="vertical"
        >
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.evaluateeIds !== currentValues.evaluateeIds ||
              prevValues.evaluateeIdsCoustom !== currentValues.evaluateeIdsCoustom ||
              prevValues.term !== currentValues.term ||
              prevValues.periodType !== currentValues.periodType ||
              prevValues.startTime !== currentValues.startTime ||
              prevValues.endTime !== currentValues.endTime
            }
          >
            {({ getFieldValue }) => {
              const evaluateeIds = getFieldValue('evaluateeIds');
              const evaluateeIdsCoustom = getFieldValue('evaluateeIdsCoustom');
              const term = getFieldValue('term');
              const periodType = getFieldValue('periodType');
              const startTime = getFieldValue('startTime');
              const endTime = getFieldValue('endTime');

              const enabledIndacatorSelect = term && periodType && endTime && startTime;
              return (
                <>
                  <Form.Item name="id" hidden>
                    <Input />
                  </Form.Item>
                  <Form.Item name="ruleName" label="评价规则名称" rules={[{ required: true }]}>
                    <Input placeholder="请输入评价规则名称" disabled={processInDisabled} />
                  </Form.Item>
                  <Form.Item name="semesterId" label="" hidden>
                    <Input placeholder="" disabled={processInDisabled} />
                  </Form.Item>
                  <Form.Item
                    name="matchType"
                    label="评价方和被评价方匹配规则"
                    rules={[{ required: true }]}
                  >
                    <Radio.Group onChange={handleMatchTypeChange} disabled={processInDisabled}>
                      {Object.values(MatchTypeMap).map(({ value, label }) => (
                        <Radio key={value} value={value}>
                          <Flex alignItems="center">
                            {label}
                            <MyTooltip
                              label={
                                value === MatchType.AutoMatch
                                  ? '系统根据年级、班级、学科自动生成评价方式下具体的评价人和相应的评价方式下具体的被评价人'
                                  : '手动指定评价方式下具体的评价人、和被评价方式下具体的被评价人'
                              }
                              maxW={respDims(200)}
                              placement="top"
                            >
                              <QuestionOutlineIcon
                                w={'0.9rem'}
                                ml={respDims(10)}
                                mb={respDims(1)}
                              />
                            </MyTooltip>
                          </Flex>
                        </Radio>
                      ))}
                    </Radio.Group>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={matchType === MatchType.CustomMatch ? 12 : 6}>
                      {teachers?.length ? (
                        <Form.Item
                          name="evaluatorType"
                          label={
                            <Flex justifyContent="space-between" alignItems="center" w="100%">
                              <Text>评价方</Text>
                              {matchType == MatchType.CustomMatch && (
                                <Text>
                                  已选择：教师/
                                  {evaluators?.filter((item) => Number(item.evaluatorId) != 0)
                                    .length || 0}
                                  人
                                </Text>
                              )}
                            </Flex>
                          }
                          rules={[{ required: true }]}
                        >
                          {matchType === MatchType.CustomMatch ? (
                            <Flex
                              onClick={() => handleSelectPerson('evaluator')}
                              w="100%"
                              bg="#F6F6F6"
                              borderRadius="8px"
                              justifyContent="center"
                              alignItems="center"
                              py={respDims(7)}
                              cursor="pointer"
                              color="primary.500"
                            >
                              <SvgIcon name="plus" mr={respDims(6)}></SvgIcon>
                              {processInDisabled ? '查看' : '选择评价方'}
                            </Flex>
                          ) : (
                            <Select
                              disabled={processInDisabled}
                              placeholder="请选择评价方"
                              dropdownStyle={{ zIndex: 2000 }}
                            >
                              {Object.values(EvaluatorTypeMap).map(({ value, label }) => (
                                <Option key={value} value={value}>
                                  {label}
                                </Option>
                              ))}
                            </Select>
                          )}
                        </Form.Item>
                      ) : (
                        <>
                          教师列表为空，请
                          <AntButton
                            type="link"
                            onClick={() => {
                              router.push({
                                pathname: '/tenant/teamManagement/teach'
                              });
                            }}
                          >
                            前往教学管理同步
                          </AntButton>
                        </>
                      )}
                    </Col>
                    {matchType === MatchType.CustomMatch ? (
                      <Col span={12}>
                        <Form.Item
                          name="evaluateeIdsCoustom"
                          label={
                            <HStack>
                              <Text>被评价方</Text>
                              {processInDisabled && <Link onClick={hanldeLookClazz}> 查看 </Link>}
                            </HStack>
                          }
                          rules={[{ required: true, message: '请选择被评价方' }]}
                        >
                          {
                            <Cascader
                              disabled={processInDisabled}
                              options={treeData || []} // 这里需要从后端获取年级和班级数据
                              multiple
                              fieldNames={{
                                label: 'deptName',
                                value: 'id',
                                children: 'children'
                              }}
                              showCheckedStrategy={Cascader.SHOW_CHILD}
                              dropdownStyle={{ overflowX: 'auto', zIndex: 2000 }}
                              maxTagCount="responsive"
                              placeholder="请选择评价年级和班级"
                              displayRender={(label, selectedOptions) => {
                                return label.join('/');
                              }}
                            />
                          }
                        </Form.Item>
                      </Col>
                    ) : (
                      <Col span={6}>
                        <Form.Item
                          name="evaluateeType"
                          label={
                            <Flex justifyContent="space-between" alignItems="center">
                              <Text>被评价方</Text>
                            </Flex>
                          }
                          rules={[{ required: true }]}
                        >
                          {
                            <Select
                              disabled={processInDisabled}
                              placeholder="请选择被评价方"
                              dropdownStyle={{ zIndex: 2000 }}
                            >
                              <Option value={EvaluateeType.Student}>学生</Option>
                            </Select>
                          }
                        </Form.Item>
                      </Col>
                    )}

                    {matchType === MatchType.AutoMatch && (
                      <Col span={12}>
                        <Form.Item
                          name="evaluateeIds"
                          label={
                            <HStack>
                              <Text>评价年级和班级</Text>
                              {processInDisabled && <Link onClick={hanldeLookClazz}> 查看 </Link>}
                            </HStack>
                          }
                          rules={[{ required: true, message: '请选择评价年级和班级' }]}
                        >
                          <Cascader
                            disabled={processInDisabled}
                            options={treeData || []} // 这里需要从后端获取年级和班级数据
                            multiple
                            fieldNames={{
                              label: 'deptName',
                              value: 'id',
                              children: 'children'
                            }}
                            showCheckedStrategy={Cascader.SHOW_CHILD}
                            dropdownStyle={{ overflowX: 'auto', zIndex: 2000 }}
                            maxTagCount="responsive"
                            placeholder="请选择评价年级和班级"
                            displayRender={(label, selectedOptions) => {
                              return label.join('/');
                            }}
                          />
                        </Form.Item>
                      </Col>
                    )}
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="year" label="评价学年" rules={[{ required: true }]}>
                        <Select
                          disabled={processInDisabled}
                          dropdownStyle={{ zIndex: 2000 }}
                          placeholder="请选择评价学年"
                          onChange={handleSemesterChange}
                        >
                          {semesterList?.map((item) => (
                            <Option key={item.id} value={item.year}>
                              {item.year}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="term" label={'评价学期'} rules={[{ required: true }]}>
                        <Select
                          disabled={processInDisabled}
                          dropdownStyle={{ zIndex: 2000 }}
                          placeholder="请选择评价学期"
                        >
                          {termList?.map((item) => (
                            <Option key={item.value} value={item.value}>
                              {item.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="periodType" label="评价周期" rules={[{ required: true }]}>
                        <Select
                          disabled={processInDisabled}
                          dropdownStyle={{ zIndex: 2000 }}
                          placeholder="请选择评价周期"
                        >
                          {Object.values(PeriodTypeMap).map(({ value, label }) => (
                            <Option key={value} value={value}>
                              {label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="评价时间"
                        name=""
                        rules={[{ required: true, validator: validateTimeRange }]}
                      >
                        <Form.Item name="startTime" noStyle>
                          <DatePicker
                            disabled={processInDisabled}
                            showTime={{ format: 'HH:mm' }}
                            format="YYYY-MM-DD HH:mm"
                            style={{ width: '45%' }}
                            popupStyle={{ zIndex: 2000 }}
                          />
                        </Form.Item>
                        <span
                          style={{ display: 'inline-block', width: '10%', textAlign: 'center' }}
                        >
                          ~
                        </span>
                        <Form.Item name="endTime" noStyle>
                          <DatePicker
                            showTime={{ format: 'HH:mm' }}
                            format="YYYY-MM-DD HH:mm"
                            style={{ width: '45%' }}
                            popupStyle={{ zIndex: 2000 }}
                          />
                        </Form.Item>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item label="关联指标" name="indacators" rules={[{ required: true }]}>
                    {
                      <HStack alignItems="center">
                        {matchType === MatchType.AutoMatch ? (
                          <AntButton
                            type={'primary'}
                            onClick={() => handleAutoGetIndicator(evaluateeIds)}
                            disabled={!evaluateeIds?.length || !enabledIndacatorSelect}
                          >
                            匹配评价指标
                          </AntButton>
                        ) : (
                          <AntButton
                            type={'primary'}
                            onClick={() => handleSelectIndicator('select')}
                            disabled={!evaluateeIdsCoustom?.length || !enabledIndacatorSelect}
                          >
                            选择评价指标
                          </AntButton>
                        )}
                        {indicators.length > 0 && (
                          <AntButton onClick={() => handleSelectIndicator('view')}>查看</AntButton>
                        )}
                        {indicators.length > 0 && (
                          <Text>已关联 {(indicators || []).length} 个指标</Text>
                        )}
                      </HStack>
                    }
                  </Form.Item>
                </>
              );
            }}
          </Form.Item>
        </Form>
      </ModalBody>
      <ModalFooter>
        <Button onClick={onClose} style={{ marginRight: 8 }} variant={'grayBase'}>
          取消
        </Button>
        <Button onClick={handleSubmit} variant="primary">
          完成
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default EditEvaluationRuleModal;
