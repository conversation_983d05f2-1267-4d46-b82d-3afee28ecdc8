import React, { Key, useEffect, useMemo, useRef } from 'react';
import { useIndicator } from '../../../IndicatorContext';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { getIndactorTreeByProject, getUsedIndactorsByClazzs } from '@/api/tenant/evaluate/rule';
import { EvaluaIndactorVO } from '@/types/api/tenant/evaluate/rule';
import {
  EducationStage,
  EducationStageMap,
  EvaluateType,
  EvaluateTypeMap,
  HasSubIndicator,
  HasSubIndicatorMap,
  ScoreInputType,
  ScoreInputTypeMap,
  Status
} from '@/constants/api/tenant/evaluate/rule';
import { TableRowSelection } from 'antd/lib/table/interface';
import SearchBar from './SearchBar';
import { Box, Flex } from '@chakra-ui/react';
import { treeToList } from '@/utils/tree';
import { useQuery } from '@tanstack/react-query';
import { myToFixed } from '@/utils/tools';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';

const IndicatorList = () => {
  const {
    selectedIndicators,
    setSelectedIndicators,
    selectedProject,
    searchCondition,
    modalStatus,
    usedIndactorsByClazzList,
    initialSelectedIndicators,
    formData
  } = useIndicator();
  const tableRef = useRef<MyTableRef>(null);

  useEffect(() => {
    tableRef.current?.setQuery({ ...tableRef.current.query, ...searchCondition });
  }, [searchCondition]);
  const columns: ColumnsType<EvaluaIndactorVO> = [
    {
      title: '评价指标',
      dataIndex: 'indactorName',
      key: 'indactorName',
      width: '250px'
    },
    {
      title: '存在下级指标',
      dataIndex: 'isHasSub',
      key: 'isHasSub',
      render: (value: HasSubIndicator) => HasSubIndicatorMap[value]?.label || '-'
    },
    {
      title: '评价学段',
      dataIndex: 'stage',
      key: 'stage',
      render: (value: string, record) => {
        if (!value || record.isHasSub === HasSubIndicator.Yes) return '-';

        return (
          value
            .split(',')
            .map((stage) => EducationStageMap[stage as unknown as EducationStage]?.label)
            .filter(Boolean)
            .join('、') || '-'
        );
      }
    },
    {
      title: '评价方式',
      dataIndex: 'evaluateType',
      key: 'evaluateType',
      render: (value: EvaluateType, record) => {
        if (record.isHasSub === HasSubIndicator.Yes) return '-';
        return EvaluateTypeMap[value]?.label || '-';
      }
    },
    {
      title: '分值范围',
      dataIndex: 'scoreRange',
      key: 'scoreRange',
      render: (_, record) => {
        if (record.isHasSub === HasSubIndicator.Yes) return '-';
        if (record.scoreInputType === ScoreInputType.Fixed) return '-';

        const min =
          record.scoreMin !== undefined && record.scoreMin !== null ? record.scoreMin : '-';
        const max =
          record.scoreMax !== undefined && record.scoreMax !== null ? record.scoreMax : '-';
        if (min == '-' && max == '-') {
          return '-';
        }
        return `${min} 至 ${max}`;
      }
    },
    {
      title: '评分默认值',
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      render: (_, record) => {
        if (record.evaluateType !== EvaluateType.Score || record.isHasSub === HasSubIndicator.Yes)
          return record.score !== undefined && record.score !== null ? record.score : '-';
      }
    },
    {
      title: '评分占比',
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      align: 'center',
      render: (scoreRate, record) => {
        if (record.isHasSub === HasSubIndicator.Yes) {
          return `${myToFixed(scoreRate * 100, 2)}%`;
        }
        if (record.evaluateType !== EvaluateType.Score) {
          return '-';
        }
        return `${myToFixed(scoreRate * 100, 2)}%`;
      }
    }
  ];

  const customExpandIcon = (props: any) => {
    const { expanded, onExpand, record } = props;
    return record.children?.length ? (
      <Box
        display="inline-flex"
        cursor="pointer"
        justifyContent="center"
        alignItems="center"
        onClick={(e) => onExpand(record, e)}
        color={expanded ? '#1A5EFF' : '#909399'}
        style={{ verticalAlign: 'middle', marginBottom: '2px' }}
      >
        {!expanded ? (
          <SvgIcon name="chevronUpCircle" fontSize={respDims(24, 22)} mr={1} />
        ) : (
          <SvgIcon name="chevronUpCircle" mr={1} style={{ transform: 'rotate(180deg)' }} />
        )}
      </Box>
    ) : (
      <></>
    );
  };
  const findChildNodes = (
    node: EvaluaIndactorVO,
    allNodes: EvaluaIndactorVO[]
  ): EvaluaIndactorVO[] => {
    const children: EvaluaIndactorVO[] = [];
    const stack = [node];
    while (stack.length > 0) {
      const currentNode = stack.pop();
      const childNodes = allNodes.filter((n) => n.parentId == currentNode?.id);
      children.push(...childNodes);
      stack.push(...childNodes);
    }
    return children;
  };

  const isNodeOrChildrenUsed = (record: EvaluaIndactorVO): boolean => {
    if (usedIndactorsByClazzList?.find((item) => item.id === record.id)) {
      return true;
    }
    if (record.children) {
      const allChildren = treeToList(record.children);
      return allChildren.some((child) =>
        usedIndactorsByClazzList?.find((item) => item.id === child.id)
      );
    }
    return false;
  };

  const rowSelection: TableRowSelection<EvaluaIndactorVO> = {
    checkStrictly: false,
    selectedRowKeys: selectedIndicators
      .map((item) => item.id as Key)
      .filter((id): id is Key => id !== undefined),
    onSelect: (
      record: EvaluaIndactorVO,
      selected: boolean,
      selectedRows: EvaluaIndactorVO[],
      nativeEvent
    ) => {
      const allNodes = treeToList(tableRef.current?.data || []);
      const children = findChildNodes(record, allNodes);
      const otherSelects = selectedIndicators.filter(
        (item) => !children.find((it) => it.id == item.id)
      );

      if (selected) {
        setSelectedIndicators([
          ...otherSelects,
          ...[...children, record].filter((it) => it.isHasSub === HasSubIndicator.No)
        ]);
      } else {
        // const children = findChildNodes(record, allNodes);
        setSelectedIndicators(
          selectedIndicators.filter((item) => ![...children, record].find((it) => it.id == item.id))
        );
      }
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: EvaluaIndactorVO[],
      changeRows: EvaluaIndactorVO[]
    ) => {
      const otherSelects = selectedIndicators.filter(
        (item) => !changeRows.find((it) => it.id == item.id)
      );
      if (selected) {
        setSelectedIndicators([
          ...otherSelects,
          ...changeRows.filter((item) => item.isHasSub === HasSubIndicator.No)
        ]);
      } else {
        setSelectedIndicators(otherSelects);
      }
    },
    getCheckboxProps: (record: EvaluaIndactorVO) => ({
      disabled:
        modalStatus === 'cancel'
          ? initialSelectedIndicators.findIndex((item) => item.id == record.id) == -1
          : modalStatus === 'view' ||
            isNodeOrChildrenUsed(record) ||
            (!record.children?.length && record.isHasSub === HasSubIndicator.Yes)
    })
  };

  return (
    <MyTable
      columns={columns}
      ref={tableRef}
      pageConfig={{
        showPaginate: false
      }}
      pagination={false}
      showHeader={true}
      defaultQuery={{
        parentId: selectedProject?.id,
        status: Status.Normal
      }}
      queryConfig={
        {
          // enabled: !!selectedProject
        }
      }
      boxStyle={{
        p: 1
      }}
      emptyConfig={{
        EmptyPicComponent: () => <></>
      }}
      headerConfig={{
        HeaderComponent: (props) => {
          return (
            <Flex w="100%" justifyContent="flex-end">
              <SearchBar {...props} />
            </Flex>
          );
        }
      }}
      api={(params) => {
        if (!params.parentId) {
          return [] as any;
        }
        return getIndactorTreeByProject(params);
      }}
      rowSelection={rowSelection}
      expandable={{
        defaultExpandAllRows: true,
        expandIcon: customExpandIcon
      }}
      rowKey="id"
    />
  );
};

export default IndicatorList;
