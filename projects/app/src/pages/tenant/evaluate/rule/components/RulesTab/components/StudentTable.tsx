import React, { useEffect } from 'react';
import { Table } from 'antd';
import { useRequest2 } from '@/hooks/useRequest';
import { getClientStudentList } from '@/api/tenant/teamManagement/student';
import { ClientStudentPageType } from '@/types/api/tenant/teamManagement/student';
import { TreeNode, SubDeptType } from './SelectPersonModal';

interface StudentTableProps {
  selectedRows: ClientStudentPageType[];
  onSelectChange: (selectedRows: ClientStudentPageType[], total: number) => void;
  selectedDept: TreeNode | null;
}

const StudentTable: React.FC<StudentTableProps> = ({
  selectedRows,
  onSelectChange,
  selectedDept
}) => {
  const {
    data: students,
    run: fetchStudents,
    loading
  } = useRequest2(getClientStudentList, {
    manual: true,
    errorToast: 'Failed to fetch students'
  });

  useEffect(() => {
    if (selectedDept && selectedDept.subDeptType === SubDeptType.Class) {
      fetchStudents({ clazzId: selectedDept.id });
    }
  }, [selectedDept, fetchStudents]);

  const columns = [
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '学号', dataIndex: 'studentNo', key: 'studentNo' },
    { title: '班级', dataIndex: 'className', key: 'className' }
  ];

  // 过滤出当前班级的选中行
  const currentClassSelectedRows = selectedRows.filter((row) => row.clazzId == selectedDept?.id);

  const handleSelectChange = (_: any, newSelectedRows: ClientStudentPageType[]) => {
    // 过滤出其他班级的选中行
    const otherClassSelectedRows = selectedRows.filter((row) => row.clazzId != selectedDept?.id);

    // 合并当前班级的新选择和其他班级的选择
    const updatedSelectedRows = [...otherClassSelectedRows, ...newSelectedRows];

    onSelectChange(updatedSelectedRows, students?.length ?? 0);
  };

  return (
    <Table
      rowKey={'id'}
      rowSelection={{
        selectedRowKeys: currentClassSelectedRows.map((row) => row.id),
        onChange: handleSelectChange
      }}
      columns={columns}
      dataSource={students}
      loading={loading}
    />
  );
};

export default StudentTable;
