import React, { useMemo, useRef, useState } from 'react';
import { Button, Form, Input, InputRef } from 'antd';
import { <PERSON><PERSON><PERSON>oot<PERSON>, ModalBody } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { addDimenType, updateDimenType } from '@/api/tenant/evaluate/rule';
import { DimenTypeStatus } from '@/constants/api/tenant/evaluate/rule';

const EditDimensionCategory = ({
  onClose,
  formStatus,
  id,
  initialName = '',
  onSuccess
}: {
  onClose: () => void;
  formStatus: 'create' | 'edit';
  id?: string;
  initialName?: string;
  onSuccess: () => void;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const inputRef = useRef<InputRef>(null);
  const [inputLength, setInputLength] = useState(initialName.length);

  const typeMap = useMemo(
    () => ({
      create: {
        title: t('添加评价维度归属'),
        action: addDimenType
      },
      edit: {
        title: t('编辑评价维度归属'),
        action: updateDimenType
      }
    }),
    [t]
  );

  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: async (data: { name: string }) => {
      if (formStatus === 'create') {
        return typeMap.create.action({
          name: data.name,
          status: DimenTypeStatus.Normal
        });
      } else {
        if (!id) throw new Error('Folder ID is required for editing');
        return typeMap.edit.action({
          id,
          name: data.name,
          status: DimenTypeStatus.Normal
        });
      }
    },
    onSuccess: () => {
      onClose();
      onSuccess();
    }
  });

  const onSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };

  return (
    <MyModal isOpen onClose={onClose} title={typeMap[formStatus].title} closeOnOverlayClick={false}>
      <Form form={form} initialValues={{ name: initialName }} onFinish={onSubmit}>
        <ModalBody>
          <Form.Item
            name="name"
            style={{ marginBottom: '0' }}
            rules={[{ required: true, message: '请输入评价维度归属' }]}
          >
            <Input
              ref={inputRef}
              placeholder={t('请输入评价维度归属') || ''}
              autoFocus
              onChange={(e) => setInputLength(e.target.value.length)}
            />
          </Form.Item>
        </ModalBody>
        <ModalFooter>
          <Button loading={isLoading} onClick={onSubmit} type="primary">
            {t('common.Confirm')}
          </Button>
        </ModalFooter>
      </Form>
    </MyModal>
  );
};

export default EditDimensionCategory;
