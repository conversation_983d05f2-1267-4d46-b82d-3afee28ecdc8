import React, { useCallback, useRef } from 'react';
import { Box, Flex, Text, Button as ChakraButton, Tag, Image } from '@chakra-ui/react';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { useIndicator } from '../IndicatorContext';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EditProjectModal from './EditProjectModal';
import { respDims } from '@/utils/chakra';
import { AddIcon } from '@chakra-ui/icons';
import { MyTableRef } from '@/components/MyTable/types';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import IndicatorsTab from '../IndicatorsTab';
import { Button } from 'antd';
import {
  listPageProject,
  deleteProject,
  listProject,
  runSortIndicators
} from '@/api/tenant/evaluate/rule';
import { <PERSON>luaIndactorVO, EvaluaProject } from '@/types/api/tenant/evaluate/rule';
import { useToast } from '@/hooks/useToast';
import { promisifyDelete } from '@/utils/ui/messageBox';
import { myToFixed } from '@/utils/tools';

const ProjectList: React.FC = () => {
  const { selectedProject, setSelectedProject, selectedDimension, selectedDimensionCategory } =
    useIndicator();
  const { openOverlay } = useOverlayManager();
  const tableRef = useRef<MyTableRef>(null);
  const router = useRouter();
  const { toast } = useToast();
  const columns: ColumnsType<EvaluaProject> = [
    {
      title: '评价项目',
      dataIndex: 'indactorName',
      key: 'indactorName',
      render: (indactorName, record) => (
        <Flex alignItems="center" flex="1">
          <Box>{record.indactorName}</Box>
          <Box ml={2}>
            {record.subjects?.slice(0, 2).map((item) => {
              return (
                <Tag key={item.subjectId} color="#2BA471" bgColor="#E3F9E9" ml={2}>
                  {item.subjectName}
                </Tag>
              );
            })}
            {(record.subjects?.length || 0) > 2 && (
              <Tag color="#2BA471" bgColor="#E3F9E9" ml={2}>
                +{(record.subjects?.length || 0) - 2}
              </Tag>
            )}
          </Box>
        </Flex>
      )
    },
    {
      title: '评分占比',
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      align: 'center',
      render: (scoreRate) => `${myToFixed(scoreRate * 100, 2)}%`
    },
    {
      title: '关联指标(个)',
      dataIndex: 'indactorNum',
      key: 'indactorNum',
      align: 'center'
      // 这个字段可能需要后端提供或者前端计算
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Flex justifyContent="flex-start">
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type="link" onClick={() => handleSettingIndicator(record)}>
            设置评价指标
          </Button>
          <Button type="link" danger onClick={() => handleDelete(record)}>
            删除
          </Button>
        </Flex>
      )
    }
  ];

  const handleEdit = (record: EvaluaProject) => {
    openOverlay({
      Overlay: EditProjectModal,
      props: {
        parentId: selectedDimension?.id || '',
        formStatus: 'edit',
        initialData: {
          ...record,
          subjectIds: record.subjects.map((item) => String(item.subjectId))
        },
        onSuccess: () => {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleAdd = () => {
    openOverlay({
      Overlay: EditProjectModal,
      props: {
        parentId: selectedDimension?.id || '',
        formStatus: 'create',
        onSuccess: () => {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: EvaluaProject[]) => {
      const { active, over } = event;
      if (active.id !== over.id) {
        const sortParams = newDataSource.map((item, index) => ({
          id: item.id!,
          sortNo: index + 1
        }));

        await runSortIndicators(sortParams);
        toast({
          status: 'success',
          title: '操作成功'
        });
        tableRef.current?.reload();
      }
    },
    [runSortIndicators]
  );

  const handleSettingIndicator = (record: EvaluaProject) => {
    openOverlay({
      Overlay: IndicatorsTab,
      props: {
        projectId: record.id!,
        indactorName: record.indactorName!,
        paths: [selectedDimensionCategory, selectedDimension, record],
        onRefresh() {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleDelete = async (record: EvaluaProject) => {
    await promisifyDelete({
      title: '确定删除该项目？'
    });
    try {
      await deleteProject({ id: record.id! });
      toast({
        status: 'success',
        title: '删除成功'
      });
      tableRef.current?.reload();
    } catch (error) {
      console.error('删除项目失败:', error);
    }
  };

  const onRow = useCallback(
    (record: EvaluaProject) => ({
      onClick: () => {
        if (record.id !== selectedProject?.id) {
          setSelectedProject(record);
        }
      }
    }),
    [selectedProject, setSelectedProject]
  );

  return (
    <Flex flexDirection="column" h="100%" w="100%">
      <Box mb={respDims(10)} fontSize={respDims(18, 14)} fontWeight="600" color="#1D2129">
        评价项目
        <ChakraButton aria-label="Add dimension" variant={'grayBase'} ml={4} visibility="hidden">
          添加
        </ChakraButton>
      </Box>
      <Flex bg="#f7f9fb" borderRadius="8px" flex="1" h="0" p={respDims(16)} flexDirection="column">
        <Box
          flex="1"
          overflow="auto"
          css={{
            '& .selected-row': {
              background: '#eff5fe!important'
            }
          }}
        >
          <MyTable
            ref={tableRef}
            columns={columns}
            api={listProject}
            defaultQuery={{
              parentId: selectedDimension?.id
            }}
            queryConfig={{
              enabled: !!selectedDimension?.id
            }}
            dragConfig={{
              enabled: true,
              rowKey: 'id',
              onDragEnd: handleDragSortEnd
            }}
            emptyConfig={{
              EmptyPicComponent: () => (
                <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
              )
            }}
            boxStyle={{
              px: '0',
              py: '0',
              borderRadius: '12px',
              css: {
                '& .ant-table': {
                  borderRadius: '12px 12px 12px 12px!important',
                  overflow: 'hidden!important'
                },
                '.ant-table tbody .ant-table-row:last-child .ant-table-cell': {
                  borderBottom: 'none'
                }
              }
            }}
            size="small"
            headerConfig={{
              showHeader: true
            }}
            pageConfig={{
              showPaginate: false
            }}
            onRow={onRow}
            rowClassName={(record) => (selectedProject?.id === record.id ? 'selected-row' : '')}
          />
        </Box>
        <Flex justifyContent="flex-end">
          <Box w="100%" pt={respDims(20)}>
            {selectedDimension && (
              <ChakraButton
                bg="#fff"
                color="#4E5969"
                w="100%"
                leftIcon={<AddIcon />}
                onClick={handleAdd}
              >
                新增评价项目
              </ChakraButton>
            )}
          </Box>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default ProjectList;
