import React, { useEffect } from 'react';
import { EvaluateLevelProvider, useEvaluateLevel } from './components/EvaluateLevelProvider';
import { Box, Flex, ModalBody, Center, VStack, HStack, Text, Spinner } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useQuery } from '@tanstack/react-query';
import MyModal from '@/components/MyModal';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import LevelDetail from './components/LevelDetail';

import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import { DataSource, DataSourceMap } from '@/constants/common';
import LevelList from './components/LevelList';
import { EvaluaScoreLevel } from '@/types/api/tenant/evaluate/rule';

const EvaluateLevelModalContent = ({
  onClose,
  onSuccess,
  currentLevel,
  selectLevel
}: {
  onClose: () => void;
  onSuccess: () => void;
  currentLevel?: EvaluaScoreLevel;
  selectLevel?: EvaluaScoreLevel;
}) => {
  const { t } = useTranslation();
  const { selectedEvaluateLevel, setSelectedEvaluateLevel, setFormStatus } = useEvaluateLevel();
  const { toast } = useToast();

  useEffect(() => {
    if (currentLevel || selectLevel) {
      setSelectedEvaluateLevel(currentLevel || selectLevel!);
      setFormStatus('edit');
    } else {
      setFormStatus('edit');
    }
  }, [currentLevel, setSelectedEvaluateLevel, setFormStatus, selectLevel]);

  return (
    <MyModal
      title={'评价等级管理'}
      isOpen
      onClose={onClose}
      isCentered={true}
      minW={['45vw']}
      h={respDims(700)}
      bgSize="100%"
      borderRadius="18px"
      bgRepeat="no-repeat"
      closeOnOverlayClick={false}
      overflow="hidden"
      hideCloseButton
      headerStyle={{
        background: 'transparent'
      }}
    >
      <ModalBody padding={respDims(0)} h="100%" borderTop="1px solid #E5E7EB">
        <Flex h="100%">
          {!currentLevel && (
            <Flex w="25%" flexDir="column">
              <LevelList />
            </Flex>
          )}

          <Flex flexDir="column" w={'75%'} borderLeft="1px solid #E5E7EB">
            {<LevelDetail onClose={onClose} onSuccess={() => {}} />}
          </Flex>
        </Flex>
      </ModalBody>
    </MyModal>
  );
};

const EvaluateLevelModal = (props: {
  onClose: () => void;
  onSuccess: () => void;
  onRefresh?: () => void;
  currentLevel?: EvaluaScoreLevel;
  selectLevel?: EvaluaScoreLevel;
}) => {
  return (
    <EvaluateLevelProvider>
      <EvaluateLevelModalContent {...props} />
    </EvaluateLevelProvider>
  );
};

export default EvaluateLevelModal;
