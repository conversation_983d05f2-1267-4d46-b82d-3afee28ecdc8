import React, { useState, useEffect } from 'react';
import { Input, Button, Select, Space } from 'antd';
import { SearchBarProps } from '@/components/MyTable/types';
import { useQuery } from '@tanstack/react-query';
import { getScoreLevelList } from '@/api/tenant/evaluate/rule';
import { EducationStage, EvaluateTypeMap } from '@/constants/api/tenant/evaluate/rule';
import { GetIndactorTreeByProjectParams, EvaluaScoreLevel } from '@/types/api/tenant/evaluate/rule';
import { Box, Flex, HStack } from '@chakra-ui/react';
import styles from '@/pages/index.module.scss';

const { Option } = Select;

const SearchBar = ({
  onSearch,
  query
}: Omit<SearchBarProps<GetIndactorTreeByProjectParams>, 'tableInstance'>) => {
  const [searchCondition, setSearchCondition] = useState<GetIndactorTreeByProjectParams>(
    query || {}
  );

  useEffect(() => {
    if (query) {
      setSearchCondition(query);
    }
  }, [query]);

  const handleInputChange = (value: string) => {
    setSearchCondition({ ...searchCondition, searchKey: value });
  };

  const handleStageChange = (value: EducationStage) => {
    setSearchCondition({ ...searchCondition, stage: value });
  };

  const handleScoreLevelChange = (value: number) => {
    setSearchCondition({ ...searchCondition, evaluateType: value });
  };

  const handleSearch = () => {
    onSearch && onSearch(searchCondition || {});
  };

  return (
    <Flex justifyContent="flex-end" className={styles['my-form']} w="100%">
      <HStack w="100%">
        <Input
          placeholder="请输入评价指标"
          value={searchCondition.searchKey}
          onChange={(e) => handleInputChange(e.target.value)}
          style={{ width: 'calc(33% - 30px)' }}
        />
        <Select
          placeholder="请选择评价学段"
          value={searchCondition.stage}
          onChange={handleStageChange}
          style={{ width: 'calc(33% - 30px)' }}
          allowClear
          dropdownStyle={{ zIndex: 2000 }}
        >
          <Option value={EducationStage.PrimarySchool}>小学</Option>
          <Option value={EducationStage.JuniorHighSchool}>初中</Option>
        </Select>
        <Select
          dropdownStyle={{ zIndex: 2000 }}
          placeholder="请选择评分等级"
          value={searchCondition.evaluateType}
          onChange={handleScoreLevelChange}
          style={{ width: 'calc(33% - 30px)' }}
          allowClear
        >
          {Object.values(EvaluateTypeMap)?.map((level) => (
            <Option key={level.value} value={level.value}>
              {level.label}
            </Option>
          ))}
        </Select>
        <Button type="primary" onClick={handleSearch}>
          搜索
        </Button>
      </HStack>
    </Flex>
  );
};

export default SearchBar;
