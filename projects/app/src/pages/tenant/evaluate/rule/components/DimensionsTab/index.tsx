import React, { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  ModalCloseButton,
  ModalBody,
  ModalFooter
} from '@chakra-ui/react';
import DimensionList from './DimensionList';
import ProjectList from './ProjectList';
import MyModal from '@/components/MyModal'; // 假设 MyModal 组件的路径
import { useTranslation } from 'next-i18next';
import { IndicatorProvider } from '../IndicatorContext';
import { respDims } from '@/utils/chakra';

const DimensionsTab = ({ TabCompoent }: { TabCompoent: React.ComponentType<any> }) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const onOpen = () => setIsOpen(true);

  return (
    <IndicatorProvider>
      <Flex px={respDims(32)} py={respDims(16)} flex="1" h="100%" flexDir="column">
        <TabCompoent></TabCompoent>
        <Flex mt={respDims(16)} flex="1">
          <Box width="30%" pr={respDims(14)} h="100%">
            <DimensionList />
          </Box>
          <Box width="70%" h="100%">
            <ProjectList />
          </Box>
        </Flex>
      </Flex>
    </IndicatorProvider>
  );
};

export default DimensionsTab;
