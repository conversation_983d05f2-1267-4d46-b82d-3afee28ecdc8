import React, { Key, useEffect, useMemo } from 'react';
import { Table } from 'antd';
import { useRequest2 } from '@/hooks/useRequest';
import { getTeachers } from '@/api/tenant/evaluate/rule';
import { TreeNode, SubDeptType } from './SelectPersonModal';
import {
  EvaluaProject,
  EvaluaRuleEvaluator,
  GetTeachersParams,
  SubjectType
} from '@/types/api/tenant/evaluate/rule';
import { Tag } from '@chakra-ui/react';
import MyTable from '@/components/MyTable';

interface TeacherTableProps {
  selectedRows: EvaluaRuleEvaluator[];
  onSelectChange: (selectedRows: EvaluaRuleEvaluator[]) => void;
  selectedDept: TreeNode | null;
  formStatus: 'view' | 'select';
}

const TeacherTable: React.FC<TeacherTableProps> = ({
  selectedRows,
  onSelectChange,
  selectedDept,
  formStatus
}) => {
  const columns = [
    { title: '姓名', dataIndex: 'username', key: 'username' },
    { title: '角色', dataIndex: 'roleName', key: 'roleName' },
    {
      title: '学科',
      dataIndex: 'subjects',
      key: 'subjects',
      render: (value: SubjectType[]) => {
        return (
          <>
            {value?.slice(0, 2).map((item) => {
              return (
                <Tag color="#2BA471" bgColor="#E3F9E9" mr={2} key={item.id}>
                  {item.subjectName}
                </Tag>
              );
            })}
          </>
        );
      }
    }
  ];

  const handleSelectChange = (_: any, newSelectedRows: EvaluaRuleEvaluator[]) => {
    const currentDeptTeacherIds = [
      ...new Set(selectedDept?.teachers?.map((teacher) => teacher.id))
    ];

    // 保留其他部门的选中行
    const otherDeptSelectedRows = selectedRows?.filter(
      (row) => !currentDeptTeacherIds.find((id) => id == row.evaluatorId)
    );

    // 合并当前部门的新选择和其他部门的选择
    const updatedSelectedRows = [
      ...otherDeptSelectedRows,
      ...newSelectedRows.map((item) => {
        return {
          evaluatorId: item.id,
          ...item
        };
      })
    ];

    onSelectChange(updatedSelectedRows);
  };

  // 获取当前部门的选中行
  const currentDeptSelectedRowKeys = useMemo(() => {
    const currentDeptTeacherIds = [
      ...new Set(selectedDept?.teachers?.map((teacher) => teacher.id))
    ];
    return (
      selectedRows
        ?.filter((row) => !!currentDeptTeacherIds.find((id) => id == row.evaluatorId))
        .map((row) => String(row.evaluatorId)) || []
    );
  }, [selectedRows, selectedDept]);

  return (
    <MyTable
      rowKey={'id'}
      rowSelection={{
        selectedRowKeys: currentDeptSelectedRowKeys as Key[],
        onChange: handleSelectChange,
        getCheckboxProps: (record) => {
          return {
            disabled: formStatus === 'view'
          };
        }
      }}
      boxStyle={{
        py: 0,
        px: 0
      }}
      pageConfig={{
        showPaginate: false
      }}
      pagination={false}
      columns={columns}
      dataSource={selectedDept?.teachers || []}
    />
  );
};

export default TeacherTable;
