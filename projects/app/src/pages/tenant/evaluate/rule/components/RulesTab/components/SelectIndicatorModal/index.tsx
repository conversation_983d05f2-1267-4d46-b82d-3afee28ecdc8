import React, { useState } from 'react';
import { Box, Flex, Text, Button, ModalBody, ModalFooter } from '@chakra-ui/react';
import SearchBar from './SearchBar';
import DimensionTabs from './DimensionTabs';
import DimensionList from './DimensionList';
import ProjectList from './ProjectList';
import IndicatorList from './IndicatorList';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { IndicatorProvider, useIndicator } from '../../../IndicatorContext';
import { EvaluaIndactorVO, EvaluationRuleDetail } from '@/types/api/tenant/evaluate/rule';
import { respDims } from '@/utils/chakra';
export type ModalStatus = 'view' | 'select' | 'cancel';
const IndicatorSelectorContent = ({
  onClose,
  onSuccess
}: {
  onClose: () => void;
  onSuccess: (selectedIndicators: EvaluaIndactorVO[]) => void;
}) => {
  const { t } = useTranslation();
  const { selectedIndicators, setSelectedIndicators, selectedProject, searchCondition } =
    useIndicator();

  const handleSuccess = () => {
    onSuccess(selectedIndicators);
    onClose();
  };

  return (
    <>
      <ModalBody p={0} borderBottom="1px solid #E5E7EB">
        <Box>
          <DimensionTabs />
        </Box>

        <Box px={respDims(32)}>
          <Flex h="550px">
            <Box width="15%" mr={'12px'} pt={respDims(12)}>
              <DimensionList />
            </Box>
            <Box width="25%" mr={'12px'} pt={respDims(12)}>
              <ProjectList />
            </Box>
            <Box width="60%" borderLeft="1px solid #E5E7EB" pt={respDims(12)}>
              <IndicatorList />
            </Box>
          </Flex>
        </Box>
      </ModalBody>
      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          {t('取消')}
        </Button>
        <Button colorScheme="blue" onClick={handleSuccess}>
          {t('common.Confirm')}
        </Button>
      </ModalFooter>
    </>
  );
};

const IndicatorSelector = ({
  onClose,
  onSuccess,
  defaultSelectedIndicators = [],
  modalStatus,
  formData
}: {
  onClose: () => void;
  onSuccess: (selectedIndicators: EvaluaIndactorVO[]) => void;
  defaultSelectedIndicators?: EvaluaIndactorVO[];
  modalStatus: ModalStatus;
  formData: EvaluationRuleDetail;
}) => {
  return (
    <IndicatorProvider
      initialSelectedIndicators={defaultSelectedIndicators}
      initModalStatus={modalStatus}
      formData={formData}
    >
      <MyModal isOpen={true} onClose={onClose} title={'选择评价指标'} minW={['85%']}>
        <IndicatorSelectorContent onClose={onClose} onSuccess={onSuccess} />
      </MyModal>
    </IndicatorProvider>
  );
};

export default IndicatorSelector;
