import React, { useCallback, useEffect, useState } from 'react';
import { Form, Input, Select, InputNumber, Radio, Checkbox, Space } from 'antd';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import {
  addIndactor,
  getScoreLevelList,
  getScoreLevelValueList,
  tenantEvaluateIconAdd,
  tenantEvaluateIconList,
  updateIndactor
} from '@/api/tenant/evaluate/rule';
import MyModal from '@/components/MyModal';
import { ModalBody, ModalFooter, Button, Image, Box, Grid, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import styles from '@/pages/index.module.scss';
import {
  EvaluaIndactor,
  EvaluaIndactorVO,
  EvaluaScoreLevel,
  EvaluaScoreLevelValue,
  IconListType
} from '@/types/api/tenant/evaluate/rule';
import {
  IndicatorType,
  EvaluateType,
  HasSubIndicator,
  EducationStage,
  ScoreInputType,
  UseLogo,
  NeedSignature
} from '@/constants/api/tenant/evaluate/rule';
import { useQuery } from '@tanstack/react-query';
import Avatar from '@/components/Avatar';
import { useSelectFile } from '@/hooks/useSelectFile';
import { uploadImage } from '@/utils/file';
import { Toast } from '@/utils/ui/toast';
import { getErrText } from '@/utils/string';
import { useToast } from '@/hooks/useToast';
import { myToFixed } from '@/utils/tools';
const { Option } = Select;

const EditIndicatorModal = ({
  onClose,
  onSuccess,
  formStatus,
  indicatorId,
  initialData = {
    scoreInputType: ScoreInputType.Input,
    stage: [EducationStage.JuniorHighSchool, EducationStage.PrimarySchool]
  },
  parentNode,
  projectId,
  parentId
}: {
  onClose: () => void;
  onSuccess: () => void;
  formStatus: 'create' | 'edit';
  indicatorId?: string;
  initialData?: Partial<EvaluaIndactor>;
  projectId?: string;
  parentId?: string;
  parentNode?: EvaluaIndactorVO;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [iconList, setIconList] = useState<IconListType[]>([]);
  const [selectedIcon, setSelectedIcon] = useState<string | null>(null);
  const [scoreLevelList, setScoreLevelList] = useState<EvaluaScoreLevel[]>([]);
  const [scoreLevelValueList, setScoreLevelValueList] = useState<EvaluaScoreLevelValue[]>([]);
  const [scoreLevelId, setScoreLevelId] = useState<string | null>(null);
  const [maxScoreRate, setMaxScoreRate] = useState<number>(100);
  const { toast } = useToast();
  console.log(maxScoreRate, parentNode);

  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false
  });

  const typeMap = {
    create: {
      title: t('添加评价指标'),
      action: addIndactor
    },
    edit: {
      title: t('编辑评价指标'),
      action: updateIndactor
    }
  };

  useEffect(() => {
    if (parentNode) {
      const parentScoreRate = (parentNode.scoreRate || 0) * 100;
      const siblingsScoreRate = (parentNode.children || [])
        .filter((child) => child.id !== indicatorId)
        .reduce((sum, child) => sum + (child.scoreRate || 0) * 100, 0);

      const availableScoreRate = parentScoreRate - siblingsScoreRate;
      setMaxScoreRate(myToFixed(Math.max(0, availableScoreRate), 2));
    } else {
      setMaxScoreRate(100);
    }
  }, [parentNode, indicatorId]);

  const toggleIcon = (fileKey: string) => {
    if (selectedIcon === fileKey) {
      setSelectedIcon(null);
    } else {
      setSelectedIcon(fileKey);
    }
  };

  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: (data: EvaluaIndactor) => {
      let modifiedData = { ...data };

      if (modifiedData.scoreRate) {
        modifiedData.scoreRate = myToFixed(modifiedData.scoreRate / 100, 4);
      }

      if (Array.isArray(modifiedData.stage)) {
        modifiedData.stage = modifiedData.stage.join(',');
      }
      modifiedData.isUseLogo = modifiedData.isUseLogo ? UseLogo.Yes : UseLogo.No;
      if (modifiedData.isUseLogo) {
        modifiedData.iconFileKey = selectedIcon!;
      }
      modifiedData.isNeedSign = modifiedData.isNeedSign ? NeedSignature.Yes : NeedSignature.No;

      if (modifiedData.score === null || modifiedData.score === undefined) {
        modifiedData.score = '' as any;
      }

      if (formStatus === 'create') {
        return addIndactor(modifiedData);
      } else {
        if (!indicatorId) throw new Error('Indicator ID is required for editing');
        return updateIndactor(modifiedData);
      }
    },
    onSuccess: () => {
      onClose();
      onSuccess();
      Toast.success('操作成功');
    }
  });

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      // 检查是否勾选了"使用图标"但没有选择具体图标
      if (values.isUseLogo && !selectedIcon) {
        toast({
          status: 'warning',
          title: '请选择图标'
        });
        return;
      }
      const indicatorData: EvaluaIndactor = {
        ...values,
        type: IndicatorType.EvaluationIndicator,
        parentId: parentId,
        projectId: projectId,

        isHasSub: values.isHasSub ? HasSubIndicator.Yes : HasSubIndicator.No
      };
      if (formStatus === 'edit' && indicatorId) {
        indicatorData.id = indicatorId;
      }
      onSave(indicatorData);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const [existChild, setExistChild] = useState(initialData.isHasSub === HasSubIndicator.Yes);
  const handleExist = (event: CheckboxChangeEvent) => {
    setExistChild(event.target.checked);
    if (event.target.checked) {
      form.setFieldValue('isUseLogo', !event.target.checked);
    }
  };

  const { refetch } = useQuery(['iconList'], () => tenantEvaluateIconList(), {
    onSuccess(data) {
      setIconList(data || []);
    }
  });

  const { data: scoreLevelData } = useQuery(['scoreLevelList'], () => getScoreLevelList({}), {
    onSuccess(data) {
      setScoreLevelList(data || []);
    }
  });

  const { refetch: fetchScoreLevelValues } = useQuery(
    ['scoreLevelValueList'],
    () => getScoreLevelValueList({ scoreLevelId: scoreLevelId || initialData.scoreLevelId }),
    {
      enabled: !!(scoreLevelId && initialData.scoreLevelId),
      onSuccess: (data) => {
        setScoreLevelValueList(data || []);
      }
    }
  );

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300
        });
        tenantEvaluateIconAdd({ fileKey: data.fileKey }).then((res) => {
          if (res) {
            refetch();
          }
        });
      } catch (err: any) {
        Toast.warning({
          title: getErrText(err, t('common.error.Select avatar failed'))
        });
      }
    },
    [refetch]
  );

  useEffect(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    if (
      scoreLevelData &&
      initialData.scoreLevelId &&
      initialData.evaluateType === EvaluateType.Grade
    ) {
      setScoreLevelId(initialData.scoreLevelId);
      fetchScoreLevelValues();
    }
  }, [scoreLevelData, initialData.scoreLevelId, fetchScoreLevelValues]);

  useEffect(() => {
    if (scoreLevelId) {
      fetchScoreLevelValues();
    }
  }, [scoreLevelId, fetchScoreLevelValues]);

  useEffect(() => {
    if (initialData.iconFileKey) {
      setSelectedIcon(initialData.iconFileKey);
    }
  }, [initialData.iconFileKey]);

  useEffect(() => {
    if (initialData.scoreRate) {
      initialData.scoreRate = myToFixed(initialData.scoreRate * 100, 2);
    }
  }, [initialData.scoreRate]);

  const handleScoreLevelChange = (value: string) => {
    setScoreLevelId(value);
    form.setFieldsValue({ scoreLevelId: value, scoreLevel: undefined });
  };

  return (
    <MyModal
      title={typeMap[formStatus].title}
      isOpen
      onClose={onClose}
      w={respDims(700)}
      closeOnOverlayClick={false}
    >
      <ModalBody mt={respDims(10)}>
        <Box maxH={respDims(600, 200)} overflow="auto">
          <Form
            layout="vertical"
            className={styles['my-form']}
            form={form}
            initialValues={initialData}
          >
            <Form.Item
              name="indactorName"
              label="评价指标名称"
              rules={[{ required: true, message: '请输入评价指标名称' }]}
            >
              <Input placeholder="请输入评价指标名称" />
            </Form.Item>

            <Form.Item name="isHasSub" valuePropName="checked">
              <Checkbox disabled={!!(indicatorId && initialData.isHasSub)} onChange={handleExist}>
                存在下级指标
              </Checkbox>
            </Form.Item>

            {!existChild && (
              <>
                <Form.Item
                  name="stage"
                  label="评价学段"
                  rules={[{ required: true, message: '请选择评价学段' }]}
                >
                  <Checkbox.Group>
                    <Checkbox value={EducationStage.PrimarySchool}>小学</Checkbox>
                    <Checkbox value={EducationStage.JuniorHighSchool}>初中</Checkbox>
                  </Checkbox.Group>
                </Form.Item>

                <Form.Item
                  name="evaluateType"
                  label="评价方式"
                  rules={[{ required: true, message: '请选择评价方式' }]}
                >
                  <Select dropdownStyle={{ zIndex: 2000 }} placeholder="请选择评价方式">
                    <Option value={EvaluateType.Score}>评分</Option>
                    <Option value={EvaluateType.Comment}>评语</Option>
                    <Option value={EvaluateType.Grade}>评等级</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.evaluateType !== currentValues.evaluateType
                  }
                >
                  {({ getFieldValue }) => {
                    const evaluateType = getFieldValue('evaluateType');

                    if (evaluateType === EvaluateType.Score) {
                      return (
                        <>
                          <Form.Item
                            name="scoreInputType"
                            label="录入方式"
                            rules={[{ required: true, message: '请选择录入方式' }]}
                          >
                            <Radio.Group>
                              <Radio value={ScoreInputType.Input}>输入</Radio>
                              <Radio value={ScoreInputType.Fixed}>固定值</Radio>
                            </Radio.Group>
                          </Form.Item>

                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) =>
                              prevValues.scoreInputType !== currentValues.scoreInputType
                            }
                          >
                            {({ getFieldValue }) => {
                              const scoreInputType = getFieldValue('scoreInputType');

                              if (scoreInputType === ScoreInputType.Input) {
                                return (
                                  <Form.Item label="分值范围" required>
                                    <Flex alignItems="center">
                                      <Form.Item
                                        name="scoreMin"
                                        noStyle
                                        rules={[
                                          { required: true, message: '请输入最小值' },
                                          {
                                            type: 'number',
                                            min: -1000,
                                            max: 1000,
                                            message: '最小值必须在-1000到1000之间'
                                          }
                                        ]}
                                      >
                                        <InputNumber
                                          style={{ width: '100%' }}
                                          min={-1000}
                                          max={1000}
                                          placeholder="最小值"
                                        />
                                      </Form.Item>
                                      <Box m="0 10px">-</Box>
                                      <Form.Item
                                        name="scoreMax"
                                        noStyle
                                        rules={[
                                          { required: true, message: '请输入最大值' },
                                          {
                                            type: 'number',
                                            min: -1000,
                                            max: 1000,
                                            message: '最大值必须在-1000到1000之间'
                                          },
                                          ({ getFieldValue }) => ({
                                            validator(_, value) {
                                              if (!value || getFieldValue('scoreMin') <= value) {
                                                return Promise.resolve();
                                              }
                                              return Promise.reject(
                                                new Error('最大值必须大于或等于最小值')
                                              );
                                            }
                                          })
                                        ]}
                                      >
                                        <InputNumber
                                          style={{ width: '100%' }}
                                          min={-1000}
                                          max={1000}
                                          placeholder="最大值"
                                        />
                                      </Form.Item>
                                    </Flex>
                                  </Form.Item>
                                );
                              }
                              return null;
                            }}
                          </Form.Item>

                          <Form.Item
                            name="score"
                            label="评分默认值"
                            rules={[
                              ({ getFieldValue }) => ({
                                validator(_, value) {
                                  const scoreMin = getFieldValue('scoreMin');
                                  const scoreMax = getFieldValue('scoreMax');
                                  const scoreInputType = getFieldValue('scoreInputType');
                                  if (
                                    scoreInputType === ScoreInputType.Fixed ||
                                    value === '' ||
                                    value === undefined ||
                                    value === null
                                  ) {
                                    return Promise.resolve();
                                  }
                                  if (value >= scoreMin && value <= scoreMax) {
                                    return Promise.resolve();
                                  }
                                  return Promise.reject(
                                    new Error(
                                      `评分默认值必须在${scoreMin || 0}到${scoreMax || 0}之间`
                                    )
                                  );
                                }
                              }),
                              ({ getFieldValue }) => ({
                                required: getFieldValue('scoreInputType') === ScoreInputType.Fixed,
                                message: '请输入评分占比'
                              })
                            ]}
                          >
                            <InputNumber style={{ width: '100%' }} min={-1000} max={1000} />
                          </Form.Item>

                          <Form.Item
                            name="scoreRate"
                            label="评分占比"
                            rules={[
                              { required: true, message: '请输入评分占比' },
                              {
                                type: 'number',
                                min: 0,
                                max: maxScoreRate,
                                message:
                                  maxScoreRate == 0
                                    ? '已没有剩余评分占比，请调整'
                                    : `评分占比必须在0-${maxScoreRate}%之间`
                              }
                            ]}
                          >
                            <InputNumber
                              style={{ width: '100%' }}
                              min={0}
                              max={100}
                              addonAfter={'%'}
                              precision={2}
                            />
                          </Form.Item>
                        </>
                      );
                    }

                    if (evaluateType === EvaluateType.Comment) {
                      return (
                        <Form.Item name="isNeedSign" valuePropName="checked">
                          <Checkbox>评语需要签名</Checkbox>
                        </Form.Item>
                      );
                    }

                    if (evaluateType === EvaluateType.Grade) {
                      return (
                        <>
                          <Form.Item
                            name="scoreLevelId"
                            label="评分项目"
                            rules={[{ required: true, message: '请选择评分项目' }]}
                          >
                            <Select
                              dropdownStyle={{ zIndex: 2000 }}
                              placeholder="请选择评分项目"
                              loading={isLoading}
                              onChange={handleScoreLevelChange}
                            >
                              {scoreLevelList.map((level) => (
                                <Option key={level.id} value={level.id}>
                                  {level.name}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>
                          <Form.Item name="scoreLevel" label="评分等级默认值">
                            <Select
                              dropdownStyle={{ zIndex: 2000 }}
                              placeholder="请选择评分等级默认值"
                              loading={isLoading}
                            >
                              {scoreLevelValueList.map((level) => (
                                <Option key={level.id} value={level.id}>
                                  {level.name}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </>
                      );
                    }

                    return null;
                  }}
                </Form.Item>
              </>
            )}

            {existChild && (
              <Form.Item
                name="scoreRate"
                label="评分占比"
                rules={[
                  { required: true, message: '请输入评分占比' },
                  {
                    type: 'number',
                    min: 0,
                    max: maxScoreRate,
                    message:
                      maxScoreRate == 0
                        ? '已没有剩余评分占比，请调整'
                        : `评分占比必须在0-${maxScoreRate}%之间`
                  }
                ]}
              >
                <InputNumber min={0} max={100} addonAfter={'%'} precision={2} />
              </Form.Item>
            )}

            {!existChild && (
              <Form.Item name="isUseLogo" valuePropName="checked">
                <Checkbox>使用图标</Checkbox>
              </Form.Item>
            )}
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isUseLogo !== currentValues.isUseLogo
              }
            >
              {({ getFieldValue }) => {
                const isUseLogo = getFieldValue('isUseLogo');
                if (isUseLogo) {
                  return (
                    <Grid templateColumns="repeat(9, 1fr)" gap={2}>
                      {iconList.map((icon) => (
                        <Flex
                          w="42px"
                          h="42px"
                          key={icon.id}
                          onClick={() => toggleIcon(icon.fileKey)}
                          borderRadius="full"
                          overflow="hidden"
                          cursor="pointer"
                          alignItems="center"
                          justifyContent="center"
                          border={
                            selectedIcon === icon.fileKey
                              ? '1px solid primary.500'
                              : '1px solid #fff'
                          }
                          _hover={{ boxShadow: 'md' }}
                        >
                          <Image
                            w="40px"
                            h="40px"
                            src={icon?.file?.fileUrl}
                            objectFit={'cover'}
                            alt={`Icon ${icon.id}`}
                            borderRadius="50%"
                          />
                        </Flex>
                      ))}
                      <Avatar
                        isAddIcon
                        onAddClick={onOpenSelectFile}
                        flexShrink={0}
                        w={['38px']}
                        h={['38px']}
                        cursor={'pointer'}
                      />
                    </Grid>
                  );
                }
                return null;
              }}
            </Form.Item>
          </Form>
        </Box>
      </ModalBody>
      <ModalFooter>
        <Button variant="grayBase" mr={3} onClick={onClose}>
          {t('取消')}
        </Button>
        <Button onClick={handleOk} isLoading={isLoading}>
          {t('确定')}
        </Button>
      </ModalFooter>

      <File onSelect={onSelectFile} />
    </MyModal>
  );
};

export default EditIndicatorModal;
