import React, { useEffect, useState } from 'react';
import { Tree, Input, TreeDataNode } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { Box, Button, Flex, ModalBody, ModalFooter } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import MyModal from '@/components/MyModal';
import { useQuery } from '@tanstack/react-query';
import MyBox from '@/components/common/MyBox';
import { respDims } from '@/utils/chakra';
import { getClientSchoolDeptTree } from '@/api/tenant/teamManagement/student';
import { Department } from '@/types/api/tenant/teamManagement/student';
import StudentTable from './StudentTable';
import TeacherTable from './TeacherTable';
import { EvaluateeType, EvaluatorType } from '@/constants/api/tenant/evaluate/rule';
import {
  DeptTeacherType,
  EvaluaRuleEvaluator,
  TeacherType
} from '@/types/api/tenant/evaluate/rule';
import { getTeacherByTree } from '@/api/tenant/evaluate/rule';
import styles from '@/pages/index.module.scss';

export type PersonType = EvaluatorType.Teacher | EvaluateeType.Student;
export enum SubDeptType {
  Stage = 1,
  Grade = 2,
  Class = 3
}

export interface TreeNode extends DataNode {
  key: string;
  title: string;
  children?: TreeNode[];
  id: string;
  deptName: string;
  subDeptType: SubDeptType;
  parentId: string;
  teachers: TeacherType[];
}

const convertDepartmentToTreeNode = (dept: DeptTeacherType): TreeNode => ({
  key: dept.id,
  title: dept.deptName,
  id: dept.id,
  deptName: dept.deptName,
  subDeptType: dept.subDeptType,
  parentId: dept.parentId,
  teachers: dept.teachers,
  children: dept.children ? dept.children.map(convertDepartmentToTreeNode) : undefined
});

// 辅助函数：聚合教师
function aggregateTeachers(nodes: TreeNode[]): TreeNode[] {
  return nodes.map((node) => {
    if (node.children && node.children.length > 0) {
      const childrenWithAggregatedTeachers = aggregateTeachers(node.children);
      const allTeachers = [
        ...(node.teachers || []),
        ...childrenWithAggregatedTeachers.flatMap((child) => child.teachers || [])
      ];

      // 根据教师id去重
      const uniqueTeachers = Array.from(
        new Map(allTeachers.map((teacher) => [teacher.id, teacher])).values()
      );

      return {
        ...node,
        children: childrenWithAggregatedTeachers,
        teachers: uniqueTeachers
      };
    }
    return node;
  });
}
const SelectPersonModal = ({
  onClose,
  onSelect,
  type,
  defaultSelectEvaluators,
  formStatus
}: {
  onClose: () => void;
  onSelect: (selectedPersons: EvaluaRuleEvaluator[]) => void;
  type: 'evaluator' | 'evaluatee';
  defaultSelectEvaluators: EvaluaRuleEvaluator[];
  formStatus: 'view' | 'select';
}) => {
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<EvaluaRuleEvaluator[]>(
    defaultSelectEvaluators || []
  );
  const [defaultExpandedKeys, setDefaultExpandedKeys] = useState<React.Key[]>([]);

  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const { t } = useTranslation();
  const [selectedDept, setSelectedDept] = useState<TreeNode | null>(null);

  const { isLoading: isTreeLoading } = useQuery(['treeData', type], getTeacherByTree, {
    onSuccess(data) {
      const convertedData = data.map(convertDepartmentToTreeNode);
      const processedData = aggregateTeachers(convertedData);
      const updatedTreeData = updateTreeData(processedData, selectedRows);
      setTreeData(updatedTreeData);
      // setTreeData(processedData);
    }
  });

  const updateTreeData = (list: TreeNode[], selectedPersons: EvaluaRuleEvaluator[]): TreeNode[] => {
    return list.map((node) => {
      const checkedCount = selectedPersons.filter(
        (person) => !!node.teachers.find((it) => it.id == person.evaluatorId)
      ).length;

      const totalCount = node.teachers ? node.teachers.length : 0;
      let title = node.deptName;
      title = `${title}(${checkedCount}/${totalCount})`;

      const updatedNode = {
        ...node,
        title: title
      };

      if (node.children) {
        updatedNode.children = updateTreeData(node.children, selectedPersons);
      }

      return updatedNode;
    });
  };

  useEffect(() => {
    if (treeData.length > 0) {
      const updatedTreeData = updateTreeData(treeData, selectedRows);
      setTreeData(updatedTreeData);
    }
  }, [selectedRows]);

  const handleTableChange = (newSelectedRows: EvaluaRuleEvaluator[], total: number = 0) => {
    setSelectedRows(newSelectedRows);
    if (treeData && selectedDept && selectedDept.subDeptType === SubDeptType.Class) {
      // 附加逻辑如果有需要的话
    }
  };

  const onTreeSelect = (selectedKeys: React.Key[], info: any) => {
    const node = info.node as TreeNode;
    setSelectedDept(node);
    setSelectedKeys([node.key]);
  };

  const handleSubmit = () => {
    onSelect(selectedRows);
    onClose();
  };

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  useEffect(() => {
    if (treeData.length > 0) {
      const allKeys = getAllKeys(treeData);
      setDefaultExpandedKeys(allKeys);
    }
  }, [treeData]);

  const getAllKeys = (data: TreeNode[]): React.Key[] => {
    return data.reduce((keys: React.Key[], node) => {
      keys.push(node.key);
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children));
      }
      return keys;
    }, []);
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
  };

  return (
    <MyModal title={type === 'evaluatee' ? '添加学生' : '添加教师'} minW={respDims(950)} isOpen>
      <ModalBody
        borderTop="1px solid #E2E8F0"
        borderBottom="1px solid #E2E8F0"
        borderColor={'#E2E8F0'}
        py={0}
      >
        <Flex h={'70vh'} overflow="hidden">
          <MyBox
            isLoading={isTreeLoading}
            width="30%"
            h={'100%'}
            py={2}
            borderRight="1px solid #E2E8F0"
            mr={2}
            overflow="auto"
          >
            {defaultExpandedKeys.length != 0 && (
              <Tree
                treeData={treeData}
                onSelect={onTreeSelect}
                defaultExpandAll
                defaultExpandParent
                autoExpandParent
                defaultExpandedKeys={defaultExpandedKeys}
                selectedKeys={selectedKeys}
              />
            )}
          </MyBox>
          <Flex
            width="70%"
            py={2}
            className={styles['my-form']}
            h={'100%'}
            overflow="hidden"
            flexDir="column"
          >
            <Input
              placeholder={t('common.Search')}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              style={{ marginBottom: 0 }}
            />
            {type === 'evaluatee' ? (
              <></>
            ) : (
              <Box flex="1" overflow="hidden">
                <TeacherTable
                  formStatus={formStatus}
                  selectedRows={selectedRows}
                  onSelectChange={handleTableChange}
                  selectedDept={selectedDept}
                />
              </Box>
            )}
          </Flex>
        </Flex>
      </ModalBody>
      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          {t('取消')}
        </Button>
        <Button onClick={handleSubmit}>{t('common.Confirm')}</Button>
      </ModalFooter>
    </MyModal>
  );
};

export default SelectPersonModal;
