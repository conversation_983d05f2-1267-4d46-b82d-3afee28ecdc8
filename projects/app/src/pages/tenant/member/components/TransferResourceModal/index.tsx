import React, { useEffect, useState } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  ModalBody,
  useToast
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { detailTenantUser, transferResources } from '@/api/tenant';
import MyModal from '@/components/MyModal';
import { Toast } from '@/utils/ui/toast';

// 定义 FormData 接口
interface FormData {
  username: string;
  avatar?: string;
  roleId: string;
  avatarUrl?: string;
  deptId: string;
  phone: string;
  password?: string;
  targetPhone: string; // 新增字段，用于存储被转移账号
}

// 定义 TransferResourceModalProps 接口
interface TransferResourceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  tenantId: string;
}

const TransferResourceModal: React.FC<TransferResourceModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  tenantId
}) => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<FormData>();
  const toast = useToast();
  const [transferPhone, setTransferPhone] = useState<string>('');

  const onSubmit = (data: FormData) => {
    const params = {
      targetPhone: data.targetPhone,
      id: tenantId
    };
    // 调用转移资源的API或处理逻辑
    transferResources(params).then((res) => {
      Toast.success('删除成功');
      onClose();
      onSuccess();
    });
  };

  useEffect(() => {
    if (!tenantId) return;
    detailTenantUser(tenantId).then((res) => {
      setTransferPhone(res.phone); // 设置转移账号的手机号
      setValue('username', res.username);
      setValue('phone', res.phone);
      setValue('password', res.password);
      setValue('avatar', res.avatar);
      setValue('avatarUrl', res.avatarFile ? res.avatarFile.fileUrl : '');
      setValue('deptId', res.deptId);
      setValue('roleId', res.roleId);
    });
  }, [tenantId, setValue]);

  return (
    <MyModal isOpen={isOpen} title={'转移资源'}>
      <ModalBody>
        <Box p="20px">
          <FormControl>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box>转移账号：</Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  value={transferPhone} // 显示转移账号的手机号
                  isReadOnly
                  disabled
                />
              </Flex>
            </Flex>
          </FormControl>
          <FormControl mt="14px" isInvalid={!!errors.targetPhone}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  被转移账号：
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('targetPhone', {
                    required: '请输入手机号码',
                    pattern: {
                      value: /^1[3-9]\d{9}$/,
                      message: '请输入有效的手机号'
                    }
                  })}
                  placeholder="请输入手机号码"
                />
                {errors.targetPhone && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.targetPhone.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="center">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="center">
                <Button h="36px" mr="24px" borderRadius="8px" onClick={handleSubmit(onSubmit)}>
                  确定
                </Button>

                <Button
                  borderColor="#0052D9"
                  variant="outline"
                  h="36px"
                  color="#1A5EFF"
                  borderRadius="8px"
                  onClick={onClose}
                >
                  取消
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default TransferResourceModal;
