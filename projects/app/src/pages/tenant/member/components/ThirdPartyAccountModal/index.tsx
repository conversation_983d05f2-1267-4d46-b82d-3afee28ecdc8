import MyModal from '@/components/MyModal';
import { ClientUserThirdPartyAccountResponse } from '@/types/api/tenant';
import {
  Box,
  VStack,
  HStack,
  Text,
  <PERSON><PERSON>,
  <PERSON>Footer,
  Divider,
  ModalBody
} from '@chakra-ui/react';

const ThirdPartyAccountModal = ({
  detailData,
  username,
  name,
  studentNumber,
  onClose,
  onSuccess
}: {
  detailData: ClientUserThirdPartyAccountResponse[];
  username: string;
  name: string;
  studentNumber: string;
  onClose: () => void;
  onSuccess: () => void;
}) => {
  const onSubmit = () => {
    onSuccess();
    onClose();
  };

  return (
    <MyModal isOpen={true} title="第三方平台账号">
      <ModalBody>
        <Box p="10px 0">
          <VStack spacing={6} align="stretch">
            {/* User Info */}
            <HStack spacing={8}>
              <HStack spacing={2}>
                <Text color="gray.600">成员姓名：</Text>
                <Text>{name || '暂无内容'}</Text>
              </HStack>
              <HStack spacing={2}>
                <Text color="gray.600">学号或工号：</Text>
                <Text>{studentNumber || '暂无内容'}</Text>
              </HStack>
            </HStack>

            <Box>
              <Text fontSize="md" fontWeight="medium" mb={4}>
                开通第三方平台账号
              </Text>
              {detailData.length > 0 ? (
                <VStack spacing={4} align="stretch" mb="10px">
                  {detailData.map((platform, index) => (
                    <Box key={index} bg="gray.50" p={4} borderRadius="md">
                      <VStack spacing={4} align="stretch">
                        <HStack>
                          <Text color="gray.600">开通平台：</Text>
                          <Text>{platform.platformName}</Text>
                        </HStack>
                        <HStack spacing={8}>
                          <HStack>
                            <Text color="gray.600">用户姓名：</Text>
                            <Text>{platform.username}</Text>
                          </HStack>
                          {platform.accounts.map((account, index) => (
                            <HStack key={index}>
                              <HStack>
                                <Text color="gray.600">账号：</Text>
                                <Text>{account.account}</Text>
                              </HStack>
                              <HStack>
                                <Text color="gray.600">密码：</Text>
                                <Text>{account.password}</Text>
                              </HStack>
                            </HStack>
                          ))}
                        </HStack>
                      </VStack>
                    </Box>
                  ))}
                </VStack>
              ) : (
                <Box
                  position="absolute"
                  top="50%"
                  left="50%"
                  transform="translate(-50%, -50%)"
                  color="gray.600"
                  mt="20px"
                >
                  暂无数据
                </Box>
              )}
            </Box>
          </VStack>

          {detailData.length > 0 && <Divider m="20px 0" />}

          <HStack spacing={4} justify="flex-end" width="full">
            <Button
              borderColor="#0052D9"
              variant="grayBase"
              h="36px"
              borderRadius="8px"
              onClick={() => onClose()}
            >
              取消
            </Button>
            <Button colorScheme="purple" onClick={onSubmit}>
              确定
            </Button>
          </HStack>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default ThirdPartyAccountModal;
