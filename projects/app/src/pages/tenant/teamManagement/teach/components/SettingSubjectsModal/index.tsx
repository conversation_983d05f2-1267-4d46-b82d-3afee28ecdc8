import React, { useEffect, useState } from 'react';
import { Box, FormControl, FormLabel, Flex, Button, BoxProps, ModalBody } from '@chakra-ui/react';
import { Select } from 'antd';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useTranslation } from 'next-i18next';
import { getTenantUserPage } from '@/api/tenant';
import MyModal from '@/components/MyModal';
import { setClientSchoolDeptSubjectManageBind } from '@/api/tenant/teamManagement/teach';
import styles from '../../../index.module.scss';

interface FormData {
  userIds: { username: string; id: string }[];
}

interface User {
  id: string;
  username: string;
}

const MAX_COUNT = 5;

const SettingSubjectsModal = ({
  settingId,
  title,
  deptName,
  onClose,
  onSuccess,
  selectedUsers,
  ...props
}: {
  settingId: string;
  title: string;
  deptName: string;
  selectedUsers: { username: string; id: string }[];
  onClose: (submited: boolean, settingId?: string) => void;
  onSuccess: () => void | Promise<void>;
} & BoxProps) => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<User[]>([]);

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    defaultValues: {
      userIds: []
    }
  });

  const { mutate, isLoading: isSubmiting } = useRequest({
    mutationFn: (data: FormData) => {
      const tmbIds = data.userIds.map((user) => user.id).join(',');
      return setClientSchoolDeptSubjectManageBind({ id: settingId, tmbIds });
    },
    onSuccess(res) {
      onSuccess();
      onClose(true, res.id);
    },
    successToast: '操作成功'
  });

  const onSubmit: SubmitHandler<FormData> = (data) => {
    mutate(data);
  };

  useEffect(() => {
    if (selectedUsers.length > 0) {
      setValue('userIds', selectedUsers);
    }
  }, [selectedUsers, setValue]);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const params = {
          current: 1,
          searchKey: '',
          searchType: '',
          size: 999,
          status: ''
        };
        const response = await getTenantUserPage(params);
        setUsers(response.records || []);
      } catch (error) {
        console.error('Failed to fetch users:', error);
      }
    };

    fetchUsers();
  }, []);

  return (
    <MyModal isOpen={true} title={title} isCentered>
      <ModalBody>
        <Box p="20px" {...props}>
          {deptName && (
            <FormControl>
              <Flex alignItems="center" whiteSpace="nowrap">
                <FormLabel color="#4E5969" fontSize="14px">
                  {deptName}
                </FormLabel>
              </Flex>
            </FormControl>
          )}

          <FormControl mt="14px" isInvalid={!!errors.userIds}>
            <Flex alignItems="center" whiteSpace="nowrap" justifyContent="end">
              <Flex flexDirection="column">
                <Controller
                  name="userIds"
                  control={control}
                  rules={{ required: '请选择用户' }}
                  render={({ field }) => (
                    <Select
                      mode="multiple"
                      showSearch
                      maxCount={MAX_COUNT}
                      value={field.value.map((user) => user.id)}
                      className={styles['formItem']}
                      onChange={(selectedIds) => {
                        const selectedUsers = users
                          .filter((user) => selectedIds.includes(user.id))
                          .map((user) => ({ username: user.username, id: user.id }));
                        field.onChange(selectedUsers);
                      }}
                      dropdownStyle={{ zIndex: 2000 }}
                      style={{ width: '460px', height: '38px' }}
                      placeholder="请选择用户"
                      filterOption={(input, option) => {
                        if (!option) return false;
                        const childrenAsString = option.children?.toString() || '';
                        return childrenAsString.toLowerCase().includes(input.toLowerCase());
                      }}
                    >
                      {users.map((user) => (
                        <Select.Option key={user.id} value={user.id}>
                          {user.username}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                />
                {errors.userIds && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.userIds.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="end">
                <Button
                  variant={'grayBase'}
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default SettingSubjectsModal;
