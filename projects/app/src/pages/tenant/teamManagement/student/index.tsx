import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Button,
  Flex,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from '@chakra-ui/react';
import { Tree } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import { serviceSideProps } from '@/utils/i18n';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import {
  getClientSchoolDeptTree,
  setClientSchoolDeptReSort,
  getClientStudentPage,
  changeStatusClientStudent,
  deleteClientSchoolDept
} from '@/api/tenant/teamManagement/student';
import {
  ClientStudentPageType,
  Department,
  DepartmentTree
} from '@/types/api/tenant/teamManagement/student';
import PageContainer from '@/components/PageContainer';
import { DownOutlined } from '@ant-design/icons';
import MyTable from '@/components/MyTable';
import ImportPanel from '@/components/ImportPanel';
import { MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import AddStudentModal from './components/AddStudentModal/index';
import ClassModal from './components/ClassModal/index';
import ChangeClassModal from './components/ChangeClassModal/index';
import styles from './student.module.scss';

interface TreeNode {
  title: string;
  key: string;
  tenantId: string;
  parentId: string;
  parentName: string;
  children?: TreeNode[];
  level: string;
  pos: string;
}

const convertToTreeData = (
  data: DepartmentTree,
  parentName?: string,
  depth: number = 0,
  parentPos: string = ''
): TreeNode[] => {
  return data.map((item: Department, index: number) => {
    let level: string;
    if (depth === 0) {
      level = '学段';
    } else if (depth === 1) {
      level = '年级';
    } else {
      level = '班级';
    }

    const currentPos = parentPos ? `${parentPos}-${index}` : `${index}`;

    const node: TreeNode = {
      title: item.deptName,
      key: item.id,
      tenantId: item.tenantId,
      parentId: item.parentId,
      parentName: parentName || '',
      level,
      pos: currentPos,
      children:
        item.children && item.children.length > 0
          ? convertToTreeData(item.children, item.deptName, depth + 1, currentPos)
          : undefined
    };

    return node;
  });
};

const Student = () => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  const tableRef = useRef<MyTableRef>(null);

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const [stageId, steStageId] = useState('');

  const [gradeId, steGradeId] = useState('');

  const [clazzId, steClazzId] = useState('');

  const containerRef = useRef<HTMLDivElement>(null);

  const [studentId, setStudentId] = useState('');

  const [classId, setClassId] = useState('');

  const [gradeName, setGradeName] = useState('');

  const [parentId, setParentId] = useState('');

  const [name, setName] = useState('');

  const [mode, setMode] = useState('');

  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);

  const [studentInfo, setStudentInfo] = useState({
    name: '',
    gradeName: '',
    clazzName: ''
  });

  const {
    isOpen: isOpenAddStudentModal,
    onOpen: onOpenAddStudentModal,
    onClose: onCloseAddStudentModal
  } = useDisclosure();

  const {
    isOpen: isOpenClassModal,
    onOpen: onOpenClassModal,
    onClose: onCloseClassModal
  } = useDisclosure();

  const { isOpen, onOpen, onClose } = useDisclosure();

  interface DropInfo {
    node: TreeNode;
    dragNode: TreeNode;
    dropPosition: number;
    dropToGap: boolean;
    event: React.MouseEvent;
  }

  useEffect(() => {
    fetchDeptList();

    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchDeptList = async () => {
    const res = await getClientSchoolDeptTree();

    const data = convertToTreeData(res);

    setTreeData(data);
    setExpandedKeys(getAllKeys(data)); // 设置所有节点的 key

    // 设置默认选中的节点
    if (data.length > 0) {
      setSelectedKeys([data[0].key]);
      steStageId(data[0].key);
    }
  };

  const getAllKeys = (data: TreeNode[]): string[] => {
    let keys: string[] = [];

    const traverse = (node: TreeNode) => {
      if (node.key) {
        keys.push(node.key.toString());
      }
      if (node.children && node.children.length > 0) {
        node.children.forEach(traverse);
      }
    };

    data.forEach(traverse);
    return keys;
  };

  const onAdd = () => {
    setStudentId('');
    onOpenAddStudentModal();
    setMode('');
  };

  const onEdit = (id: string) => {
    setStudentId(id);
    onOpenAddStudentModal();
    setMode('');
  };

  const onView = (id: string) => {
    setStudentId(id);
    onOpenAddStudentModal();
    setMode('view');
  };

  const onTransferResources = (row: ClientStudentPageType) => {
    setStudentId(row.id);
    setStudentInfo({ name: row.name, gradeName: row.gradeName, clazzName: row.clazzName });
    onOpen();
  };

  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    const key = info.node?.key;
    if (!isNaN(Number(key))) {
      const selectedNode = findNodeById(treeData, key);

      if (selectedNode) {
        let stageId, gradeId, clazzId;

        switch (selectedNode.level) {
          case '学段':
            stageId = key;
            gradeId = undefined;
            clazzId = undefined;
            break;
          case '年级':
            stageId = selectedNode.parentId;
            gradeId = key;
            clazzId = undefined;
            break;
          case '班级':
            const parentNode = findNodeById(treeData, selectedNode.parentId);
            stageId = parentNode ? parentNode.parentId : undefined;
            gradeId = selectedNode.parentId;
            clazzId = key;
            break;
          default:
            break;
        }

        // 更新状态
        steStageId(stageId);
        steGradeId(gradeId);
        steClazzId(clazzId);

        setSelectedKeys([key]); // 更新 selectedKeys 状态
      }
    }
  };

  // 辅助函数：根据 id 查找节点
  const findNodeById = (tree: TreeNode[], id: string | number): TreeNode | undefined => {
    for (const node of tree) {
      if (node.key === id) {
        return node;
      }
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return undefined;
  };

  const handleCloseAddStudentModal = (submited: boolean, tenantId?: string) => {
    onCloseAddStudentModal();
  };

  const handleDrop = (info: DropInfo) => {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;

    // 检查是否为班级节点
    const dragNode = findNodeById(treeData, dragKey);
    const dropNode = findNodeById(treeData, dropKey);

    if (!dragNode || !dropNode || dragNode.level !== '班级' || dropNode.level !== '班级') {
      Toast.error('只能对班级进行排序');
      return;
    }

    // 检查是否为同级
    if (dragNode.parentId !== dropNode.parentId) {
      Toast.error('只能在同一年级内排序班级');
      return;
    }

    const data = [...treeData];

    // 找到父节点（年级）
    const parentNode = findNodeById(data, dragNode.parentId);
    if (!parentNode || !parentNode.children) {
      return;
    }

    // 在父节点的children中重新排序
    const newChildren = [...parentNode.children];
    const dragIndex = newChildren.findIndex((child) => child.key === dragKey);
    const dropIndex = newChildren.findIndex((child) => child.key === dropKey);

    if (dragIndex < 0 || dropIndex < 0) {
      return;
    }

    const [removed] = newChildren.splice(dragIndex, 1);
    newChildren.splice(dropIndex, 0, removed);

    // 更新父节点的children
    parentNode.children = newChildren;

    setTreeData(data);
    setExpandedKeys(getAllKeys(data));

    // 准备排序数据
    const sortedClasses = newChildren.map((child, index) => ({
      id: child.key,
      sort: index + 1 // 从1开始排序
    }));

    // 调用排序接口
    const requestData = {
      schoolDeptList: sortedClasses
    };

    setClientSchoolDeptReSort(requestData)
      .then(() => {
        fetchDeptList(); // 重新获取部门列表
        Toast.success('排序成功');
      })
      .catch((error) => {
        Toast.error('排序失败');
        console.error(error);
      });
  };

  const onSetStatus = (id: string, status: number) => {
    let title, content;

    switch (status) {
      case 1:
        title = '恢复提示';
        content = '确定要恢复该学生的状态吗？';
        break;
      case 2:
        title = '失效提示';
        content = '学生设置失效后，将不参与评价，确定将学生设置为失效吗？';
        break;
      case 3:
        title = '毕业提示';
        content = '学生设置毕业状态后，将不可恢复，确定设置为毕业状态？';
        break;
      default:
        return;
    }

    MessageBox.confirm({
      title,
      content,
      onOk: async () => {
        try {
          await changeStatusClientStudent({ id, status });
          Toast.success('操作成功');
          tableRef.current?.reload();
        } catch (error) {
          Toast.error('操作失败');
          console.error(error);
        }
      }
    });
  };

  const renderTitle = (node: TreeNode, level: number) => {
    return (
      <Flex
        alignItems="center"
        justifyContent="space-between"
        boxSizing="border-box"
        p="6px 0"
        title={node.title}
        onMouseEnter={() => setHoveredNodeId(node.key)}
        onMouseLeave={() => setHoveredNodeId(null)}
      >
        <Box
          overflow="hidden"
          whiteSpace="nowrap"
          textOverflow="ellipsis"
          width="100%"
          display="flex"
          alignItems="center"
        >
          {node?.level === '学段' && (
            <SvgIcon name="emojiStudentPerformance" w={18} h={18} mr="6px" />
          )}
          <Box>{node.title}</Box>
        </Box>
        {node?.level !== '学段' && hoveredNodeId === node.key && (
          <Popover placement="bottom-end">
            <PopoverTrigger>
              <Box display="flex" justifyContent="center" alignItems="center">
                <SvgIcon name="more" w={19} h={19} />
              </Box>
            </PopoverTrigger>
            <PopoverContent
              w="160px"
              borderRadius="8px"
              padding="8px"
              sx={{
                background: '#FFFFFF',
                boxShadow: '0px 2px 2px 0px rgba(0,0,0,0), 0px 4px 3px 0px rgba(0,0,0,0.05)',
                border: '1px solid #E5E7EB'
              }}
            >
              {node?.level === '年级' && (
                <Box
                  w="100%"
                  h="36px"
                  display="flex"
                  justifyContent="flex-start"
                  alignItems="center"
                  padding="12px"
                  boxSizing="border-box"
                  borderRadius="8px"
                  onClick={() => {
                    setParentId(node.key);
                    setGradeName(node.title);
                    setClassId('');
                    onOpenClassModal();
                  }}
                  sx={{
                    _hover: {
                      backgroundColor: '#F8FAFC'
                    }
                  }}
                >
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    fontSize="15px"
                    fontWeight="500"
                    color="#303133"
                  >
                    <SvgIcon name="plus" w={18} h={18} mr="12px" />
                    班级
                  </Box>
                </Box>
              )}
              {node?.level === '班级' && (
                <Box>
                  <Box
                    w="100%"
                    h="36px"
                    display="flex"
                    justifyContent="flex-start"
                    alignItems="center"
                    padding="12px"
                    boxSizing="border-box"
                    borderRadius="8px"
                    sx={{
                      _hover: {
                        backgroundColor: '#F8FAFC'
                      }
                    }}
                    onClick={() => {
                      setClassId(node.key);
                      setGradeName(node.parentName);
                      setName(node.title);
                      onOpenClassModal();
                    }}
                  >
                    <Box
                      display="flex"
                      justifyContent="center"
                      alignItems="center"
                      fontSize="15px"
                      fontWeight="500"
                      color="#303133"
                    >
                      <SvgIcon name="editLine" w={18} h={18} mr="12px" />
                      编辑
                    </Box>
                  </Box>
                  <Box
                    w="100%"
                    h="36px"
                    display="flex"
                    justifyContent="flex-start"
                    alignItems="center"
                    padding="12px"
                    boxSizing="border-box"
                    borderRadius="8px"
                    sx={{
                      _hover: {
                        backgroundColor: '#F8FAFC'
                      }
                    }}
                    onClick={() => {
                      onDelete(node.key);
                    }}
                  >
                    <Box
                      display="flex"
                      justifyContent="center"
                      alignItems="center"
                      fontSize="15px"
                      fontWeight="500"
                      color="#303133"
                    >
                      <SvgIcon name="chatDelete" w={18} h={18} mr="12px" />
                      删除
                    </Box>
                  </Box>
                </Box>
              )}
            </PopoverContent>
          </Popover>
        )}
      </Flex>
    );
  };

  const renderTreeNodes = (data: TreeNode[], level: number = 1): React.ReactNode => {
    return data.map((node) => (
      <Tree.TreeNode title={renderTitle(node, level)} key={node.key}>
        {node.children && node.children.length > 0
          ? renderTreeNodes(node.children, level + 1)
          : null}
      </Tree.TreeNode>
    ));
  };

  const onDelete = (key: any) => {
    MessageBox.confirm({
      title: '删除提示',
      content: `确定要删除该班级吗？`,
      onOk: () => {
        deleteClientSchoolDept(key).then((res) => {
          if (res) {
            fetchDeptList();
            Toast.success('删除成功');
          }
        });
      }
    });
  };

  const handleUploadSuccess = () => {
    tableRef.current?.reload();
  };

  const columns = [
    {
      title: '学号',
      key: 'code',
      dataIndex: 'code'
    },
    {
      title: '学生姓名',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: '性别',
      key: 'sex',
      dataIndex: 'sex',
      render: (row: ClientStudentPageType) => {
        return <Box>{Number(row) === 1 ? '男' : '女'}</Box>;
      }
    },
    {
      title: '年级',
      key: 'gradeName',
      dataIndex: 'gradeName'
    },
    {
      title: '班级',
      key: 'clazzName',
      dataIndex: 'clazzName'
    },
    {
      title: '出生年月',
      key: 'birthday',
      dataIndex: 'birthday'
    },
    {
      title: '入学时间',
      key: 'enrollmentDate',
      dataIndex: 'enrollmentDate'
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      dataIndex: 'status',
      render: (status: number) => {
        let text: string;
        let color: string;

        switch (status) {
          case 1:
            text = '正常';
            color = '#2BA471'; // 绿色
            break;
          case 2:
            text = '失效';
            color = '#D54941'; // 红色
            break;
          case 3:
            text = '毕业';
            color = 'primary.500'; // 黄色
            break;
          default:
            text = '未知';
            color = '#bfbfbf'; // 灰色
        }

        return (
          <Box
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              padding: '2px 8px',
              borderRadius: '12px',
              color: color,
              fontSize: '12px'
            }}
          >
            <Box
              style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                backgroundColor: color,
                marginRight: '6px'
              }}
            />
            {text}
          </Box>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      render: (_: any, row: ClientStudentPageType) => (
        <Flex>
          {row.status === 1 ? (
            <Box>
              <Button color="#0052D9" variant="link" onClick={() => onEdit(row.id)}>
                编辑
              </Button>
              <Button color="#0052D9" variant="link" onClick={() => onTransferResources(row)}>
                调班
              </Button>
              <Button color="#0052D9" variant="link" onClick={() => onSetStatus(row.id, 2)}>
                失效
              </Button>
              <Button color="#0052D9" variant="link" onClick={() => onSetStatus(row.id, 3)}>
                毕业
              </Button>
            </Box>
          ) : row.status === 2 ? (
            <Box>
              <Button color="#0052D9" variant="link" onClick={() => onSetStatus(row.id, 1)}>
                恢复
              </Button>
              <Button color="#0052D9" variant="link" onClick={() => onView(row.id)}>
                查看
              </Button>
            </Box>
          ) : (
            <Button color="#0052D9" variant="link" onClick={() => onView(row.id)}>
              查看
            </Button>
          )}
        </Flex>
      )
    }
  ];

  const ButtonsComponent = () => (
    <Box>
      <Button mx="16px" h="36px" borderRadius="8px" onClick={onAdd}>
        添加学生
      </Button>
      <ImportPanel
        templateUrl="/client/student/downloadTemplate"
        importUrl="/huayun-ai/client/student/import"
        onUploadSuccess={handleUploadSuccess}
      ></ImportPanel>
    </Box>
  );

  return (
    <PageContainer>
      <Box display="flex" h="100%">
        <Box
          w="400px"
          borderRight="1px solid #F3F4F6"
          bg="#fff"
          ref={containerRef}
          overflow="hidden"
          borderRadius="24px 0 0 24px"
        >
          <Box
            h="76px"
            borderBottom="1px solid #E5E7EB"
            fontSize="16px"
            fontWeight="600"
            color="#303133"
            lineHeight="76px"
            pl="24px"
          >
            班级管理
          </Box>

          <Box p="8px 20px 100px 20px" h="100%" overflowY="auto">
            <Tree
              className={styles['space-tree']}
              fieldNames={{ title: 'title', key: 'key', children: 'children' }}
              expandedKeys={expandedKeys}
              onExpand={(keys) => setExpandedKeys(keys as string[])}
              draggable
              blockNode
              switcherIcon={<DownOutlined />}
              onDrop={handleDrop}
              onSelect={handleSelect}
              selectedKeys={selectedKeys}
            >
              {renderTreeNodes(treeData)}
            </Tree>
          </Box>
        </Box>
        <Box w="100%" h="100%" flexDir="column">
          <MyTable
            ref={tableRef}
            api={getClientStudentPage}
            columns={columns}
            defaultQuery={{
              stageId: stageId || '',
              gradeId: gradeId || '',
              clazzId: clazzId || ''
            }}
            headerConfig={{
              showHeader: true,
              SearchComponent: SearchBar,
              ButtonsComponent: ButtonsComponent
            }}
          ></MyTable>
        </Box>
      </Box>

      {isOpenClassModal && (
        <ClassModal
          classId={classId}
          onClose={onCloseClassModal}
          onSuccess={fetchDeptList}
          gradeName={gradeName}
          parentId={parentId}
          name={name}
        />
      )}
      {isOpenAddStudentModal && (
        <AddStudentModal
          studentId={studentId}
          mode={mode}
          onClose={handleCloseAddStudentModal}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
      {isOpen && (
        <ChangeClassModal
          studentId={studentId}
          studentInfo={studentInfo}
          isOpen={isOpen}
          onClose={onClose}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Student;
