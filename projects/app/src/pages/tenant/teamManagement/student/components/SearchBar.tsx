import { Box, Button, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { Select } from 'antd';
import { useMemo, useState, useEffect } from 'react';
import { SearchBarProps } from '@/components/MyTable/types';
import styles from '../student.module.scss';

const SearchBar = ({ onSearch, query, defaultQuery }: SearchBarProps) => {
  const statusOptions = useMemo(
    () => [
      { value: 1, label: '正常' },
      { value: 2, label: '失效' },
      { value: 3, label: '毕业' }
    ],
    []
  );

  const [searchWord, setSearchWord] = useState(query?.name || '');
  const [searchStatus, setSearchStatus] = useState<number | undefined>(query?.status);

  const handleSearch = () => {
    let params = {
      name: searchWord,
      status: searchStatus,
      stageId: defaultQuery?.stageId || '',
      gradeId: defaultQuery?.gradeId || '',
      clazzId: defaultQuery?.clazzId || ''
    };
    if (typeof onSearch === 'function') {
      onSearch(params);
    }
  };

  useEffect(() => {
    setSearchWord(query?.name || '');
    setSearchStatus(query?.status);
  }, [query]);

  return (
    <>
      <InputGroup w="200px">
        <InputLeftElement>
          <SvgIcon name="search" w="12px" h="12px" tabIndex={-1} />
        </InputLeftElement>

        <Input
          value={searchWord}
          placeholder="请输入学员姓名"
          style={{ backgroundColor: '#f6f6f6', border: 'none' }}
          onChange={(e) => setSearchWord(e.target.value)}
          onKeyDown={(e) => {
            e.key === 'Enter' && handleSearch();
          }}
        />
      </InputGroup>

      <Select
        style={{ width: '200px', borderRadius: '1px', height: '40px', marginLeft: '16px' }}
        className={styles['custom-cascader']}
        onChange={(val: number) => setSearchStatus(val)}
        placeholder="请选择状态"
        value={searchStatus}
        options={statusOptions}
        allowClear
      />

      <Button
        ml="16px"
        h="36px"
        colorScheme="primary"
        variant="outline"
        borderRadius="8px"
        onClick={handleSearch}
      >
        查询
      </Button>
    </>
  );
};

export default SearchBar;
