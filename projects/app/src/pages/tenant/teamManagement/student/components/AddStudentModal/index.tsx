import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  BoxProps,
  ModalBody,
  Textarea,
  ModalFooter
} from '@chakra-ui/react';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useState, useEffect, useRef } from 'react';
import { Select, DatePicker } from 'antd';
import { useTranslation } from 'next-i18next';
import RegionSelector from '../CascaderAddress/index';
import MyModal from '@/components/MyModal';
import UploadImage from '@/components/UploadImage';
import { Toast } from '@/utils/ui/toast';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
import styles from '../../student.module.scss';
import {
  getClientSchoolDeptTree,
  createClientStudent,
  updateClientStudent,
  detailClientStudent
} from '@/api/tenant/teamManagement/student';
import SchoolStructureSelect from '../SchoolStructureSelect/SchoolStructureSelect';
import {
  DepartmentTree,
  DetailClientStudentType,
  CityItem
} from '@/types/api/tenant/teamManagement/student';

interface FormData {
  name: string;
  avatarUrl: string;
  avatar: string;
  code: string;
  sex: number;
  birthday: Date | null;
  enrollmentDate: Date | null;
  experienceIntroduction: string;
  address: string;
  schoolStructure: null;
  stageId: string;
  gradeId: string;
  clazzId: string;
}

const AddStudentModal = ({
  studentId,
  mode,
  onClose,
  onSuccess,
  ...props
}: {
  studentId: string;
  mode: string;
  onClose: (submited: boolean, studentId?: string) => void;
  onSuccess: () => void;
} & BoxProps) => {
  const { t } = useTranslation();
  const [, setRefresh] = useState(false);
  const dataFetchedRef = useRef(false);
  const [schoolStructure, setSchoolStructure] = useState<{ title: string; value: string }[]>([]);
  const [city, setCity] = useState({});
  const [address, setAddress] = useState('');

  const {
    register,
    control,
    getValues,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<FormData>({
    mode: 'onChange'
  });

  useEffect(() => {
    if (dataFetchedRef.current) return;
    dataFetchedRef.current = true;

    interface TreeSelectDataItem {
      value: string;
      title: string;
      children?: TreeSelectDataItem[];
      isLeaf: boolean;
    }

    const convertToTreeSelectData = (data: DepartmentTree): TreeSelectDataItem[] => {
      return data.map((item) => ({
        value: item.id,
        title: item.deptName,
        children: item.children ? convertToTreeSelectData(item.children) : undefined,
        isLeaf: !item.children || item.children.length === 0
      }));
    };

    getClientSchoolDeptTree().then((res) => {
      const convertedData = convertToTreeSelectData(res);
      setSchoolStructure(convertedData);
    });
  }, []);

  useEffect(() => {
    if (!studentId) return;
    detailClientStudent(studentId).then((res: DetailClientStudentType) => {
      setValue('name', res.name);
      setValue('avatarUrl', res.avatarUrl);
      setValue('avatar', res.avatarUrl);
      setValue('code', res.code);
      setValue('sex', res.sex);
      setAddress(res.address);
      setValue('birthday', res.birthday ? dayjs(res.birthday).toDate() : null);
      setValue('enrollmentDate', res.enrollmentDate ? dayjs(res.enrollmentDate).toDate() : null);
      if (res.stageId && res.gradeId && res.clazzId) {
        setValue('schoolStructure' as any, {
          stageId: res.stageId,
          gradeId: res.gradeId,
          clazzId: res.clazzId
        });
      } else {
        setValue('schoolStructure', null);
      }
      setValue('experienceIntroduction', res.experienceIntroduction);

      // 设置地址相关的状态
      setCity({
        provinceCode: res.provinceCode,
        provinceName: res.provinceName,
        cityCode: res.cityCode,
        cityName: res.cityName,
        districtCode: res.districtCode,
        districtName: res.districtName
      });
      handleRegionChange({
        provinceCode: res.provinceCode,
        provinceName: res.provinceName,
        cityCode: res.cityCode,
        cityName: res.cityName,
        districtCode: res.districtCode,
        districtName: res.districtName
      });

      setRefresh((state) => !state);
    });
  }, [studentId, setValue]);

  const handleRegionChange = (value: CityItem) => {
    setCity(value);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return null;
    return dayjs(dateString).format('YYYY-MM-DD');
  };

  const handleImageSelect = (type: keyof FormData, fileKey: string, fileUrl: string) => {
    setValue(type, fileKey);
    setValue(`${type}Url` as keyof FormData, fileUrl);
    setRefresh((state) => !state);
  };

  const handleAddressChange = (val: string) => {
    setAddress(val);
  };

  const { mutate, isLoading: isSubmitting } = useRequest({
    mutationFn: (data: FormData) => {
      let schoolStructure = { stageId: '', gradeId: '', clazzId: '' };
      if (data.schoolStructure) {
        if (Array.isArray(data.schoolStructure)) {
          schoolStructure = data.schoolStructure[0] || schoolStructure;
        } else if (typeof data.schoolStructure === 'object') {
          schoolStructure = data.schoolStructure;
        }
      }

      const baseParams = {
        name: data.name,
        avatarUrl: data.avatarUrl,
        code: data.code,
        sex: data.sex,
        birthday: data.birthday,
        enrollmentDate: data.enrollmentDate,
        experienceIntroduction: data.experienceIntroduction,
        ...city,
        ...schoolStructure,
        address
      } as any;

      const action = studentId ? updateClientStudent : createClientStudent;

      return action(studentId ? { id: studentId, ...baseParams } : baseParams);
    },
    onSuccess(res) {
      Toast.success({ title: studentId ? '更新成功' : '新增成功' });
      onSuccess();
      onClose(true);
    },
    onError: (error) => {
      console.error('提交出错:', error);
    }
  });

  // 创建一个包装函数来桥接 mutate 和 handleSubmit
  const onSubmit: SubmitHandler<FormData> = (data) => {
    mutate(data);
  };

  return (
    <MyModal isOpen={true} title={studentId ? '编辑学生' : '添加学生'} maxW="100%">
      <ModalBody>
        <Box p="24px 8px">
          <Flex justifyContent="center">
            <UploadImage
              imageUrl={getValues('avatarUrl')}
              isCircular={true}
              onImageSelect={(fileKey, fileUrl) => handleImageSelect('avatar', fileKey, fileUrl)}
              maxWidthOrHeight={300}
              showPlaceholderAsBox={true}
              className={styles['custom-cascader']}
              {...register('avatarUrl', { required: '请选择学生照片' })}
            />
          </Flex>
          <Box textAlign="center" color="#4E5969" fontWeight="12px" p="10px 0 20px 0">
            点击上传学生照片
          </Box>

          <Flex justifyContent="space-between" mb="14px">
            <FormControl isInvalid={!!errors.name} w="328px" mr="32px ">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  学生姓名
                </Box>
              </FormLabel>
              <Input
                {...register('name', { required: '请输入学生姓名' })}
                placeholder="请输入学生姓名"
                isDisabled={mode === 'view'}
                style={{
                  height: '38px',
                  borderRadius: '8px',
                  backgroundColor: '#F6F6F6',
                  border: 'none'
                }}
                sx={{
                  '::placeholder': {
                    color: '#959699',
                    fontSize: '14px'
                  }
                }}
              />
              {errors.name && (
                <Box color="#F53F3F" fontSize="13px" mt="8px">
                  {errors.name.message}
                </Box>
              )}
            </FormControl>
            <FormControl isInvalid={!!errors.code} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  学号
                </Box>
              </FormLabel>
              <Input
                {...register('code', { required: '请输入学号' })}
                placeholder="请输入学号"
                isDisabled={mode === 'view'}
                style={{
                  width: '100%',
                  height: '38px',
                  borderRadius: '8px',
                  backgroundColor: '#F6F6F6',
                  border: 'none'
                }}
                sx={{
                  '::placeholder': {
                    color: '#959699',
                    fontSize: '14px'
                  }
                }}
              />
              {errors.code && (
                <Box color="#F53F3F" fontSize="13px" mt="8px">
                  {errors.code.message}
                </Box>
              )}
            </FormControl>
          </Flex>
          <Flex justifyContent="space-between" mb="14px">
            <FormControl isInvalid={!!errors.sex} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  性别
                </Box>
              </FormLabel>
              <Controller
                name="sex"
                control={control}
                rules={{ required: '请选择性别' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    className={styles['custom-cascader']}
                    placeholder="请选择性别"
                    options={[
                      { value: 1, label: '男' },
                      { value: 2, label: '女' }
                    ]}
                    dropdownStyle={{ zIndex: 9999 }}
                    disabled={mode === 'view'}
                    style={{
                      width: '100%',
                      height: '38px',
                      borderRadius: '8px',
                      backgroundColor: '#F6F6F6',
                      border: 'none'
                    }}
                  />
                )}
              />
              {errors.sex && (
                <Box color="#F53F3F" fontSize="13px" mt="8px">
                  {errors.sex.message}
                </Box>
              )}
            </FormControl>
            <FormControl isInvalid={!!errors.birthday} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  出生日期
                </Box>
              </FormLabel>
              <Controller
                name="birthday"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    {...field}
                    popupClassName={`datePicker ${styles.datePicker} hidden-ascader-level1-check`}
                    value={field.value ? dayjs(field.value) : null}
                    onChange={(date) =>
                      field.onChange(date ? formatDate(date.format('YYYY-MM-DD')) : '')
                    }
                    format="YYYY-MM-DD"
                    placeholder="请选择出生日期"
                    style={{
                      width: '100%',
                      height: '38px',
                      borderRadius: '8px',
                      zIndex: 999,
                      backgroundColor: '#F6F6F6',
                      border: 'none'
                    }}
                    className={`customPlaceholder ${styles.customPlaceholder}`} // 确保应用了 CSS Modules 的类名
                    disabled={mode === 'view'}
                  />
                )}
              />
            </FormControl>
          </Flex>
          <Flex justifyContent="space-between" mb="14px">
            <FormControl isInvalid={!!errors.schoolStructure} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  班级
                </Box>
              </FormLabel>
              <SchoolStructureSelect
                control={control}
                name="schoolStructure"
                treeData={schoolStructure}
                placeholder="请选择学段/年级/班级"
                mode={mode}
              />
            </FormControl>
            <FormControl isInvalid={!!errors.enrollmentDate} w="328px">
              <FormLabel color="#4E5969" fontSize="14px">
                入学时间
              </FormLabel>
              <Controller
                name="enrollmentDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    className={`customPlaceholder ${styles.customPlaceholder}`} // 确保应用了 CSS Modules 的类名
                    popupClassName={`datePicker ${styles.datePicker} hidden-ascader-level1-check`}
                    {...field}
                    value={field.value ? dayjs(field.value) : null}
                    onChange={(date) =>
                      field.onChange(date ? formatDate(date.format('YYYY-MM-DD')) : null)
                    }
                    format="YYYY-MM-DD"
                    placeholder="请选择入学时间"
                    style={{
                      width: '100%',
                      height: '38px',
                      borderRadius: '8px',
                      backgroundColor: '#F6F6F6',
                      border: 'none'
                    }}
                    disabled={mode === 'view'}
                  />
                )}
              />
            </FormControl>
          </Flex>
          <FormControl mb="14px">
            <FormLabel color="#4E5969" fontSize="14px">
              家庭地址
            </FormLabel>
            <Box>
              <RegionSelector onChange={handleRegionChange} initialValue={city} mode={mode} />
              <Input
                mt="14px"
                placeholder="详细地址"
                value={address}
                onChange={(e) => handleAddressChange(e.target.value)}
                disabled={mode === 'view'}
                style={{
                  height: '38px',
                  borderRadius: '8px',
                  borderColor: '#d9d9d9',
                  backgroundColor: '#F6F6F6',
                  border: 'none'
                }}
                sx={{
                  '::placeholder': {
                    color: '#959699',
                    fontSize: '14px'
                  }
                }}
              />
            </Box>
          </FormControl>
          <FormControl mb="14px">
            <FormLabel color="#4E5969" fontSize="14px">
              实习经历
            </FormLabel>
            <Textarea
              {...register('experienceIntroduction')}
              placeholder="请输入实习经历"
              disabled={mode === 'view'}
              style={{
                height: '84px',
                borderRadius: '8px',
                backgroundColor: '#F6F6F6',
                border: 'none'
              }}
              sx={{
                '::placeholder': {
                  color: '#959699',
                  fontSize: '14px'
                }
              }}
            />
          </FormControl>
        </Box>
      </ModalBody>
      <ModalFooter
        justifyContent="end"
        position="sticky"
        bottom="0"
        left="0"
        width="100%"
        bg="white"
        p="24px"
        borderRadius="0 0 12px 12px"
      >
        <Button variant={'grayBase'} mr={3} onClick={() => onClose(false)}>
          取消
        </Button>
        <Button onClick={handleSubmit(onSubmit)} isLoading={isSubmitting} mr="10px">
          确定
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default AddStudentModal;
