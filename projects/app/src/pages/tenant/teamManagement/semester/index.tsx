import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Box, Button, Flex, useDisclosure } from '@chakra-ui/react';
import { Table, Switch } from 'antd';
import {
  getClientSemesterPage,
  setCurrentClientSemester
} from '@/api/tenant/teamManagement/semester';
import { ClientSemesterPageType } from '@/types/api/tenant/teamManagement/semester';
import { serviceSideProps } from '@/utils/i18n';
import { useToast } from '@/hooks/useToast';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SemesterModal from './components/SemesterModal';
import { TableProps } from 'antd';
import PageContainer from '@/components/PageContainer';

const Semester = () => {
  const { toast } = useToast();
  const [modalId, setModalId] = useState('');
  const actionRef = useRef<MyTableRef<ClientSemesterPageType>>(null);

  const tableRef = useRef<MyTableRef>(null);

  const {
    isOpen: isOpenSemesterModal,
    onOpen: onOpenSemesterModal,
    onClose: onCloseSemesterModal
  } = useDisclosure();

  const onAdd = () => {
    setModalId('');
    onOpenSemesterModal();
  };

  const onEdit = (id: string) => {
    setModalId(id);
    onOpenSemesterModal();
  };

  const handleSwitchChange = (checked: boolean, id: string) => {
    // 这里你可以添加更新数据的逻辑
    setCurrentClientSemester({ id, isCurrent: checked ? 1 : 0 }).then(() => {
      toast({
        status: 'success',
        title: '操作成功'
      });
      tableRef.current?.reload();
    });
  };

  const columns: TableProps<ClientSemesterPageType>['columns'] = useMemo(() => {
    return [
      {
        title: '所属学年',
        key: 'year',
        dataIndex: 'year'
      },
      {
        title: '学期类型',
        key: 'type',
        render: (_: any, row: ClientSemesterPageType) => (
          <Flex>{row.type === 1 ? '第一学期' : '第二学期'}</Flex>
        )
      },
      {
        title: '学期周期',
        key: 'startDate',
        dataIndex: 'startDate',
        render: (_: any, row: ClientSemesterPageType) => (
          <Flex>
            {row.startDate} 至 {row.endDate}
          </Flex>
        )
      },
      {
        title: '当前学期',
        key: 'isCurrent',
        dataIndex: 'isCurrent',
        render: (_: any, row: ClientSemesterPageType) => (
          <Switch
            checked={row.isCurrent == 1 ? true : false}
            onChange={(checked) => handleSwitchChange(checked, row.id)}
          />
        )
      },
      {
        title: '创建时间',
        key: 'createTime',
        dataIndex: 'createTime'
      },
      {
        title: '操作',
        key: 'action',
        width: 220,
        render: (_: any, row: ClientSemesterPageType) => (
          <Flex>
            <Button color="#0052D9" variant="link" onClick={() => onEdit(row.id)}>
              修改
            </Button>
          </Flex>
        )
      }
    ];
  }, []);

  const ButtonsComponent = () => (
    <Button h="36px" borderRadius="8px" onClick={onAdd}>
      设置学期
    </Button>
  );

  return (
    <PageContainer>
      <Box display="flex" justifyContent="space-between" alignItems="center" p="24px 24px 0 24px">
        <Box fontSize="16px" color="primary.500" fontWeight="700">
          学期配置
        </Box>
        <Box display="flex" alignItems="center">
          <ButtonsComponent />
        </Box>
      </Box>
      <Flex w="100%" h="100%" flexDir="column">
        <MyTable columns={columns} api={getClientSemesterPage} rowKey="id" ref={tableRef} />
      </Flex>

      {isOpenSemesterModal && (
        <SemesterModal
          modalId={modalId}
          onClose={onCloseSemesterModal}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Semester;
