import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  BoxProps,
  Textarea,
  border
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { Steps, Tree } from 'antd';
import { useTranslation } from 'next-i18next';
import {
  createRole,
  getMenuTreeList,
  createRoleMenu,
  getDetailRole,
  updateRole,
  getApplicationList
} from '@/api/tenant';
import { useToast } from '@/hooks/useToast';
import { MenuListTypeItem } from '@/types/api/tenant';

interface FormData {
  name: string;
  info?: string;
}

type CheckedInfo = {
  checked: React.Key[];
  halfChecked: React.Key[];
};

const RolePanel = ({
  modalId,
  onClose,
  ...props
}: {
  modalId: string;
  onClose: (submited: boolean, modalId?: string) => void;
} & BoxProps) => {
  const { t } = useTranslation();
  const [, setRefresh] = useState(false);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const [currentStep, setCurrentStep] = useState(0); // 用于跟踪当前步骤

  const [roleId, setRoleId] = useState(''); // 用于跟踪当前步骤

  const [info, setInfo] = useState<string>('');

  const [name, setName] = useState<string>('');

  const [treeData, setTreeData] = useState<ConvertedTreeNode[]>([]);

  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);

  const [appSceneIds, setAppSceneIds] = useState<string[]>([]);

  const [appCenterParentId, setAppCenterParentId] = useState<string>('');

  const onSubmit = () => {
    if (!checkedKeys.length && !appSceneIds.length) {
      toast({
        title: '请选择权限',
        status: 'warning'
      });
      return;
    }

    const tenantSceneIdsParam = appSceneIds.length > 0 ? appSceneIds.join(',') : '';

    let menuIds = [...checkedKeys];

    if (appSceneIds.length > 0 && appCenterParentId && !menuIds.includes(appCenterParentId)) {
      menuIds.push(appCenterParentId);
    }

    createRoleMenu({
      menuIds: menuIds.join(),
      roleId,
      tenantSceneIds: tenantSceneIdsParam
    }).then((res) => {
      toast({
        title: '操作成功',
        status: 'success'
      });
      onClose(true);
    });
  };

  const handleInputChangeInfo = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInfo(e.target.value);
  };

  const handleInputChangeName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  useEffect(() => {
    if (!modalId) return;
    getDetailRole(modalId).then((res) => {
      setName(res.name);
      setInfo(res.info);

      let menuIds = res.menuIds ? res.menuIds.split(',') : [];

      let sceneIds: string[] = [];
      if (res.tenantSceneIds) {
        sceneIds = res.tenantSceneIds.split(',');
      }

      Promise.all([getMenuTreeList(), getApplicationList()])
        .then(([menuTreeData, appData]) => {
          const treeDataConverted = convertTreeData(menuTreeData);

          const findAppCenterNode = (nodes: ConvertedTreeNode[]): ConvertedTreeNode | null => {
            for (const node of nodes) {
              if (node.title === '应用中心') {
                return node;
              }
              if (node.children && node.children.length > 0) {
                const found = findAppCenterNode(node.children);
                if (found) return found;
              }
            }
            return null;
          };

          const appCenterNode = findAppCenterNode(treeDataConverted);

          if (appCenterNode) {
            const appCenterId = appCenterNode.key as string;
            setAppCenterParentId(appCenterId);

            if (sceneIds.length > 0 && !menuIds.includes(appCenterId)) {
              menuIds.push(appCenterId);
            }

            if (Array.isArray(appData) && appData.length > 0) {
              if (sceneIds.length > 0 && sceneIds.length < appData.length) {
                const index = menuIds.indexOf(appCenterId);
                if (index !== -1) {
                  menuIds.splice(index, 1);
                }
              }
            }
          }

          setCheckedKeys(menuIds);
          setAppSceneIds(sceneIds);
          setRefresh((state) => !state);
        })
        .catch((error) => {
          console.error('获取菜单树或应用中心数据失败:', error);
          setCheckedKeys(menuIds);
          setAppSceneIds(sceneIds);
          setRefresh((state) => !state);
        });
    });
  }, [modalId, setValue]);

  useEffect(() => {
    // 每次步骤切换时，重新设置表单的值
    setValue('name', name);
    setValue('info', info);
  }, [currentStep, name, info, setValue]);

  const next = async () => {
    if (isLoading) return; // 避免重复调用

    setIsLoading(true);
    try {
      // 创建或更新角色
      const params = { name, info, type: 2 };
      const roleResponse =
        modalId || roleId
          ? await updateRole(Object.assign(params, { id: modalId || roleId }))
          : await createRole(params);

      if (roleResponse && roleResponse.id) {
        setRoleId(roleResponse.id);
        setCurrentStep(currentStep + 1);

        // 获取菜单树列表
        const menuResponse = await getMenuTreeList();

        let list = convertTreeData(menuResponse);

        // 获取应用中心数据
        try {
          const appResponse = await getApplicationList();

          const findAppCenterNode = (nodes: ConvertedTreeNode[]): ConvertedTreeNode | null => {
            for (const node of nodes) {
              if (node.title === '应用中心') {
                setAppCenterParentId(node.key as string);
                return node;
              }

              // 递归查找子节点
              if (node.children && node.children.length > 0) {
                const found = findAppCenterNode(node.children);
                if (found) return found;
              }
            }
            return null;
          };

          const appCenterNode = findAppCenterNode(list);

          if (appCenterNode && appResponse && Array.isArray(appResponse)) {
            const appItems = appResponse.map((item) => ({
              title: item.name,
              key: `app_${item.id}`,
              isAppScene: true,
              children: [] as ConvertedTreeNode[]
            }));

            appCenterNode.children = [...appCenterNode.children, ...appItems];
          }
        } catch (error) {
          console.error('获取应用中心数据失败:', error);
        }

        setTreeData(list);
      } else {
      }
    } catch (error) {
      console.error('Error:', error);
      // 这里可以添加错误提示或其他错误处理逻辑
    } finally {
      setIsLoading(false);
    }
  };

  interface TreeNode {
    name: string;
    id: string;
    children?: TreeNode[];
  }

  interface ConvertedTreeNode {
    title: string;
    key: string;
    isAppScene?: boolean;
    children: ConvertedTreeNode[];
  }

  const convertTreeData = (data: MenuListTypeItem[]): ConvertedTreeNode[] => {
    return data.map((item: MenuListTypeItem) => ({
      title: item.name,
      key: item.id,
      children: item.children ? convertTreeData(item.children) : []
    }));
  };

  const onCheck = (checked: React.Key[] | CheckedInfo, info: any) => {
    let checkedKeysValue: React.Key[];
    if (Array.isArray(checked)) {
      checkedKeysValue = checked;
    } else {
      checkedKeysValue = checked.checked;
    }

    const menuKeys: React.Key[] = [];
    const appKeys: string[] = [];

    checkedKeysValue.forEach((key) => {
      const keyStr = String(key);
      if (keyStr.startsWith('app_')) {
        appKeys.push(keyStr.replace('app_', ''));
      } else {
        menuKeys.push(key);
      }
    });

    setCheckedKeys(menuKeys);
    setAppSceneIds(appKeys);
  };

  const steps = [
    {
      title: '角色信息',
      content: (
        <>
          <FormControl isInvalid={!!errors.name} mt="36px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  角色名称
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="8px"
                  style={{ border: 'none', backgroundColor: '#f6f6f6' }}
                  w="400px"
                  {...register('name', {
                    required: '请输入角色名称',
                    minLength: {
                      value: 2,
                      message: '至少输入2个字符'
                    },
                    maxLength: {
                      value: 20,
                      message: '最多输入不超过20个字符'
                    }
                  })}
                  value={name}
                  onChange={handleInputChangeName}
                  placeholder="请输入角色名称"
                />
                {errors.name && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.name.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box>角色描述</Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Textarea
                  style={{ border: 'none', backgroundColor: '#f6f6f6' }}
                  placeholder="请输入角色描述"
                  w="400px"
                  value={info}
                  onChange={handleInputChangeInfo}
                />
              </Flex>
            </Flex>
          </FormControl>
        </>
      )
    },
    {
      title: '选择权限',
      content: (
        <Box mt="36px">
          <Tree
            checkable
            onCheck={onCheck}
            checkedKeys={{
              checked: [...checkedKeys, ...appSceneIds.map((id) => `app_${id}`)],
              halfChecked: []
            }}
            treeData={treeData}
          />
        </Box>
      )
    }
  ];

  return (
    <Box p="20px" {...props}>
      <Box>
        <Steps current={currentStep}>
          {steps.map((item) => (
            <Steps.Step key={item.title} title={item.title} />
          ))}
        </Steps>
        <Box>{steps[currentStep].content}</Box>
        <FormControl mt="14px">
          <Flex justifyContent="end ">
            <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
            <Flex w="400px" justifyContent="end">
              {currentStep > 0 && (
                <Button
                  h="36px"
                  mr="16px"
                  borderRadius="8px"
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  上一步
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Box>
                  <Button
                    variant={'grayBase'}
                    h="36px"
                    mr="16px"
                    borderRadius="8px"
                    onClick={() => onClose(false)}
                  >
                    取消
                  </Button>
                  <Button h="36px" borderRadius="8px" onClick={() => next()} colorScheme="primary">
                    下一步
                  </Button>
                </Box>
              )}
              {currentStep === steps.length - 1 && (
                <Button
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onSubmit()}
                  colorScheme="primary"
                >
                  确定
                </Button>
              )}
            </Flex>
          </Flex>
        </FormControl>
      </Box>
    </Box>
  );
};

export default RolePanel;
