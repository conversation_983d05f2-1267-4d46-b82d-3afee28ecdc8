import { Ref, forwardRef, useImperativeHandle, useRef } from 'react';
import { Box, Button, Flex } from '@chakra-ui/react';
import MyTable from '@/components/MyTable';
import SearchBar from './SearchBar';
import { MyTableRef } from '@/components/MyTable/types';
import { PromptPageProps } from '@/types/pages/tenantPrompt';
import { respDims } from '@/utils/chakra';
import dayjs from 'dayjs';
import { TenantPromptStatus, TenantPromptStatusMap } from '@/constants/api/tenantPrompt';
import { getTenantPromptPage } from '@/api/tenant/prompt';
import { TenantPromptPageType } from '@/types/api/tenant/prompt';
import { DataSource } from '@/constants/common';

const CommonalityList = (
  { currentTab, onEditPrompt, onDelete, onSetStatus, TabRender, appName }: PromptPageProps,
  ref: Ref<{ reload: () => void }>
) => {
  const actionRef = useRef<MyTableRef>(null);
  const ellipsisStyle = {
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    WebkitLineClamp: 3,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'normal'
  };
  const columns = [
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName',
      width: 250
    },
    {
      title: '指令名称',
      dataIndex: 'promptTitle',
      key: 'promptTitle',
      width: 250
    },
    {
      title: '指令介绍',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true
    },
    {
      title: '增强提示词',
      dataIndex: 'hiddenContent',
      key: 'hiddenContent',
      width: '180px',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '100px',
      render: (status: TenantPromptStatus, record: TenantPromptPageType) => (
        <Flex
          color={TenantPromptStatusMap[status].color}
          fontSize={respDims(14, 12)}
          alignItems="center"
        >
          <Box
            w={respDims(10)}
            h={respDims(10)}
            bg={TenantPromptStatusMap[status].color}
            borderRadius="50px"
          ></Box>
          <Box ml={respDims(5)}>{TenantPromptStatusMap[status].label}</Box>
        </Flex>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 160,
      render: (value: Date) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm') : '')
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (dom: React.ReactNode, record: TenantPromptPageType) => (
        <Flex>
          {record.status === 1 ? (
            <Button color="#0052D9" variant="link" onClick={() => onSetStatus(record.id, 2)}>
              禁用
            </Button>
          ) : (
            <Button color="#0052D9" variant="link" onClick={() => onSetStatus(record.id, 1)}>
              启用
            </Button>
          )}

          <Button
            color="#0052D9"
            variant="link"
            onClick={() => {
              onEditPrompt(record);
            }}
          >
            编辑
          </Button>

          <Button color="#F53F3F" variant="link" onClick={() => onDelete(record.id)}>
            删除
          </Button>
        </Flex>
      )
    }
  ];

  useImperativeHandle(ref, () => ({
    reload: () => {
      actionRef.current?.reload();
    }
  }));
  return (
    <Flex flex="1" h="calc(100% - 56px)">
      <MyTable
        columns={columns}
        api={getTenantPromptPage}
        rowKey="id"
        defaultQuery={{ source: DataSource.Tenant }}
        ref={actionRef}
        headerConfig={{
          showHeader: true,
          HeaderComponent: (props) => {
            return (
              <Flex justifyContent="space-between" alignItems="center" w="100%">
                <TabRender></TabRender>
                <SearchBar {...props} currentTab={currentTab} name={appName}></SearchBar>
              </Flex>
            );
          }
        }}
      />
    </Flex>
  );
};

export default forwardRef<{ reload: () => void }, PromptPageProps>(CommonalityList);
