import { Button, Flex, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import { SearchBarProps } from '@/components/MyTable/types';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { Select } from 'antd';
import { TenantPromptStatusMap } from '@/constants/api/tenantPrompt';
import style from '../tenantPrompt.module.scss';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import PromptModal from '../components/PromptModal';
import { PromptTabType } from '@/types/pages/tenantPrompt';
import { DataSource } from '@/constants/common';

const SearchBar = ({
  onSearch,
  query,
  currentTab,
  name
}: SearchBarProps & { currentTab: PromptTabType['value']; name?: string }) => {
  const router = useRouter();
  const [appName, setAppName] = useState(query?.appName || '');
  const [promptTitle, setPromptTitle] = useState(query?.promptTitle || '');
  const [status, setStatus] = useState(query?.status || undefined);
  const { openOverlay, OverlayContainer } = useOverlayManager();

  const statusOptions = useMemo(
    () =>
      Object.values(TenantPromptStatusMap).map((option) => ({
        value: option.value,
        label: option.label
      })),
    []
  );

  const handleReset = () => {
    setAppName(query?.appName || '');
    setPromptTitle(query?.promptTitle || '');
    setStatus(query?.status || undefined);
    onSearch && onSearch('');
  };

  const handleAddPrompt = () => {
    openOverlay({
      Overlay: PromptModal,
      props: {
        prompt: {},
        currentTab,
        onClose: () => {},
        onSuccess() {
          handleSearch();
        }
      }
    });
  };

  const handleSearch = () => {
    let params = {
      appName,
      status,
      promptTitle,
      source:
        currentTab == 'authority'
          ? DataSource.Offical
          : currentTab == 'commonality'
            ? DataSource.Tenant
            : DataSource.Personal
    };

    onSearch && onSearch(params);
  };

  useEffect(() => {
    handleReset();
  }, [router]);

  useEffect(() => {
    if (name) {
      setAppName(name);
    }
  }, [name]);

  useEffect(() => {
    if (appName === name) {
      handleSearch();
    }
  }, [appName, name]);

  return (
    <Flex alignItems="center">
      <InputGroup w={respDims(220)} mr="16px">
        <InputLeftElement>
          <SvgIcon name="appAgentSearch" w="14px" h="14px" ml="10px" />
        </InputLeftElement>
        <Input
          style={{ borderRadius: '8px', height: '36px' }}
          value={appName}
          color="#606266"
          fontWeight="400"
          bgColor="rgba(0,0,0,0.03)"
          placeholder="请输入应用名称"
          onChange={(e) => setAppName(e.target.value)}
          onKeyDown={(e) => {
            e.key === 'Enter' && handleSearch();
          }}
        />
      </InputGroup>

      <InputGroup w={respDims(220)} mr="16px">
        <InputLeftElement>
          <SvgIcon name="appAgentSearch" w="14px" h="14px" ml="10px" />
        </InputLeftElement>
        <Input
          style={{ borderRadius: '8px', height: '36px' }}
          value={promptTitle}
          color="#606266"
          fontWeight="400"
          bgColor="rgba(0,0,0,0.03)"
          placeholder="请输入指令名称"
          onChange={(e) => setPromptTitle(e.target.value)}
          onKeyDown={(e) => {
            e.key === 'Enter' && handleSearch();
          }}
        />
      </InputGroup>

      <Select
        value={status}
        options={statusOptions}
        placeholder="请选择状态"
        allowClear
        onChange={(value) => setStatus(value)}
        className={style['custom-select']}
        style={{
          width: '200px',
          borderRadius: '8px',
          height: '36px',
          marginRight: '16px'
        }}
      ></Select>

      <Button
        fontSize={respDims(14)}
        w={respDims(68)}
        fontWeight="500"
        h="36px"
        borderRadius="8px"
        variant={'grayBase'}
        onClick={handleReset}
      >
        重置
      </Button>
      <Button
        fontSize={respDims(14)}
        w={respDims(68)}
        fontWeight="500"
        m="0 16px"
        h="36px"
        variant="outline"
        color="#3366FF"
        border="1px solid #3366FF"
        borderRadius="8px"
        onClick={handleSearch}
      >
        查询
      </Button>
      {currentTab === 'commonality' && (
        <Button
          fontSize={respDims(14)}
          fontWeight="500"
          h="36px"
          variant="primary"
          borderRadius="8px"
          onClick={handleAddPrompt}
        >
          添加指令
        </Button>
      )}

      <OverlayContainer></OverlayContainer>
    </Flex>
  );
};

export default SearchBar;
