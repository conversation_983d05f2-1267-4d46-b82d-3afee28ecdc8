import React, { useEffect, useState, useImperative<PERSON><PERSON><PERSON>, forwardRef, Ref } from 'react';
import {
  <PERSON>,
  Button,
  ModalFooter,
  ModalBody,
  Flex,
  useTheme,
  useDisclosure
} from '@chakra-ui/react';
import { useForm, Controller } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { getMyAppPage, moveApp } from '@/api/tenant/app';
import { Select, Tag } from 'antd';

type FormType = {
  appIds: string[];
};

export interface ImportAppSceneRef {
  openModal: (modalInfo: {
    records?: FormType & { id: string };
    formStatus: 'view' | 'add' | 'edit';
  }) => void;
  closeModal: () => void;
}

type ImportAppSceneProps = {
  onSuccess: () => void;
  defaultValues?: FormType;
  selectedSubScene: string;
  selectedScene: string;
};

const ImportAppScene = (
  { onSuccess, selectedSubScene, selectedScene }: ImportAppSceneProps,
  ref: Ref<ImportAppSceneRef>
) => {
  const [isAdd, setIsAdd] = useState(false);
  const [defaultValues, setDefaultValues] = useState({ appIds: [] });
  const { t } = useTranslation();
  const theme = useTheme();
  const { control, handleSubmit, setValue } = useForm<FormType>({
    defaultValues: { appIds: [] }
  });

  const {
    isOpen: isOpenImportAppScene,
    onOpen: onOpenImportAppScene,
    onClose: onCloseImportAppScene
  } = useDisclosure();
  const [options, setOptions] = useState([]); // 存储下拉选项

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      const formData: any = {
        ...data,
        appIds: data.appIds.join(',') // Assuming the backend expects a comma-separated string
      };
      return moveApp({
        tenantAppIds: data.appIds,
        tenantLabelId: selectedSubScene,
        tenantSceneId: selectedScene
      });
    },
    onSuccess() {
      onSuccess();
      onCloseImportAppScene();
    },
    successToast: t('导入成功')
  });

  useImperativeHandle(ref, () => ({
    openModal: (modalInfo: {
      records?: FormType & { id: string };
      formStatus: 'view' | 'add' | 'edit';
    }) => {
      const { records, formStatus } = modalInfo;
      if (records) {
        setDefaultValues(records as any);
        Object.keys(records).forEach((key) => {
          setValue(key as keyof FormType, records[key as keyof FormType]);
        });
      } else {
        Object.keys(defaultValues).forEach((key) => {
          setValue(key as keyof FormType, defaultValues[key as keyof FormType]);
        });
      }
      setIsAdd(formStatus == 'add');

      onOpenImportAppScene();
    },
    closeModal: () => {
      onCloseImportAppScene();
    }
  }));

  useEffect(() => {
    // 获取下拉选项
    const fetchOptions = async () => {
      if (!isOpenImportAppScene) {
        return;
      }
      try {
        const response = await getMyAppPage({
          size: 9999,
          current: 1,
          excludeLabelId: selectedSubScene
        });

        setOptions(response.records as any); // 假设接口返回的数据格式为 { data: [...] }
      } catch (error) {
        console.error('Failed to fetch options:', error);
      }
    };

    fetchOptions();
  }, [isOpenImportAppScene]);

  return isOpenImportAppScene ? (
    <MyModal title={'移入应用'} isOpen onClose={onCloseImportAppScene} isCentered>
      <ModalBody>
        <Flex mt={3} alignItems={'center'}>
          <Controller
            name="appIds"
            control={control}
            rules={{ required: t('core.app.error.App name can not be empty') }}
            render={({ field }) => (
              <Select
                {...field}
                mode="multiple"
                showSearch
                getPopupContainer={() => document.body}
                dropdownStyle={{ zIndex: 99999 }} // 提高下拉菜单的 z-index
                style={{ width: '100%' }}
                options={options}
                fieldNames={{
                  label: 'name',
                  value: 'id'
                }}
                placeholder="请选择应用"
                onChange={(value) => field.onChange(value)}
                filterOption={(input, option: any) =>
                  option?.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            )}
          />
        </Flex>
      </ModalBody>

      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onCloseImportAppScene}>
          {t('取消')}
        </Button>
        <Button onClick={handleSubmit((data) => onClickConfirm(data))} isLoading={creating}>
          {t('确定')}
        </Button>
      </ModalFooter>
    </MyModal>
  ) : (
    <></>
  );
};

export default forwardRef<ImportAppSceneRef, ImportAppSceneProps>(ImportAppScene);
