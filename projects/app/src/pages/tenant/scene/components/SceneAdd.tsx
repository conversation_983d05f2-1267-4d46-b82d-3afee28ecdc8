import React, { useEffect, useState, useImperativeHandle, forwardRef, Ref } from 'react';
import {
  Box,
  Button,
  ModalFooter,
  ModalBody,
  Input,
  Flex,
  useTheme,
  useDisclosure,
  Switch // 使用 Chakra UI 的 Switch 组件
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import { createScene, updateScene } from '@/api/tenant/scene';
import { useRouter } from 'next/router';

type FormType = {
  createUsername: string;
  industry: string;
  name: string;
  permission: number;
  sceneId: number;
  sort: number;
  isDisplayed: number; // 添加 isDisplayed 字段
};

export interface SceneAddRef {
  openModal: (modalInfo: {
    records?: FormType & { id: string };
    formStatus: 'view' | 'add' | 'edit';
  }) => void;
  closeModal: () => void;
}

const itemDefaultValues = {
  createUsername: '',
  industry: '',
  name: '',
  permission: 0,
  sceneId: 0,
  sort: 0,
  isDisplayed: 1, // 添加默认值
  id: ''
};

const SceneAdd = (
  { onSuccess }: { onSuccess: () => void; defaultValues?: FormType },
  ref: Ref<SceneAddRef>
) => {
  const router = useRouter();
  const { industry: currentIndustry } = router.query;
  itemDefaultValues.industry = currentIndustry as string;
  const [isAdd, setIsAdd] = useState(false);
  const [defaultValues, setDefaultValues] = useState(itemDefaultValues);
  const { t } = useTranslation();
  const theme = useTheme();

  const { register, handleSubmit, setValue, watch } = useForm<FormType>({
    defaultValues: itemDefaultValues
  });

  const {
    isOpen: isOpenSceneAdd,
    onOpen: onOpenSceneAdd,
    onClose: onCloseSceneAdd
  } = useDisclosure();

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      const formData = {
        ...defaultValues,
        ...data
      };
      const appId = isAdd ? undefined : { id: defaultValues.id };
      return isAdd ? createScene(formData) : updateScene({ ...formData, ...appId });
    },
    onSuccess() {
      onSuccess();
      onCloseSceneAdd();
    },
    successToast: isAdd ? t('common.Create Success') : t('common.Update Success')
  });

  useImperativeHandle(ref, () => ({
    openModal: (modalInfo: {
      records?: FormType & { id: string };
      formStatus: 'view' | 'add' | 'edit';
    }) => {
      const { records, formStatus } = modalInfo;
      setIsAdd(formStatus === 'add');

      if (records && formStatus !== 'add') {
        setDefaultValues(records);
        Object.keys(records).forEach((key) => {
          setValue(key as keyof FormType, records[key as keyof FormType]);
        });
      } else {
        Object.keys(itemDefaultValues).forEach((key) => {
          setValue(key as keyof FormType, itemDefaultValues[key as keyof FormType]);
        });
      }

      onOpenSceneAdd();
    },
    closeModal: () => {
      onCloseSceneAdd();
    }
  }));

  const isDisplayed = watch('isDisplayed');

  return isOpenSceneAdd ? (
    <MyModal
      title={isAdd ? t('新增场景') : t('编辑场景')}
      isOpen
      onClose={onCloseSceneAdd}
      isCentered
    >
      <ModalBody>
        <Box color={'myGray.800'} fontWeight={'bold'}>
          {t('场景名称')}
        </Box>
        <Flex mt={3} alignItems={'center'}>
          <Input
            flex={1}
            autoFocus
            placeholder="请输入场景名称"
            bg={'myWhite.600'}
            {...register('name', {
              required: t('core.app.error.App name can not be empty')
            })}
          />
        </Flex>
        <Flex mt={5} alignItems={'center'}>
          <Box color={'myGray.800'} fontWeight={'bold'} mr={3}>
            在应用中心展示
          </Box>
          <Switch
            colorScheme="purple"
            isChecked={isDisplayed === 1}
            onChange={(e) => setValue('isDisplayed', e.target.checked ? 1 : 0)}
          />
        </Flex>
      </ModalBody>

      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onCloseSceneAdd}>
          {t('取消')}
        </Button>
        <Button onClick={handleSubmit((data) => onClickConfirm(data))} isLoading={creating}>
          {isAdd ? t('添加') : t('确认')}
        </Button>
      </ModalFooter>
    </MyModal>
  ) : (
    <></>
  );
};

export default forwardRef<SceneAddRef, { onSuccess: () => void; defaultValues?: FormType }>(
  SceneAdd
);
