import { Box, Center, Flex, Input } from '@chakra-ui/react';
import MyIcon from '@/components/LegacyIcon';
import { useEffect, useRef, useState } from 'react';

const SelectSettings = ({
  placeholder,
  options,
  onChange
}: {
  placeholder?: string;
  options?: string[];
  onChange?: (payload: { options?: string[]; placeholder?: string }) => void;
}) => {
  const [innerPlaceholder, setInnerPlaceHolder] = useState(placeholder || '');

  const [innerOptions, setInnerOptions] = useState<string[]>(options?.length ? options : ['']);

  const settings = { placeholder, options, innerPlaceholder, innerOptions, onChange };

  const settingsRef = useRef(settings);

  settingsRef.current = settings;

  useEffect(
    () => () => {
      const { placeholder, options, innerPlaceholder, innerOptions, onChange } =
        settingsRef.current;
      const filterOptions = innerOptions.filter((it) => it);
      if (
        onChange &&
        (innerPlaceholder !== placeholder || filterOptions.join(',') !== (options?.join(',') || ''))
      ) {
        onChange({ options: filterOptions, placeholder: innerPlaceholder });
      }
    },
    []
  );

  return (
    <Box my="16px" px="20px" w="400px" maxH="400px" overflow="auto">
      <Flex alignItems="center" overflow="hidden">
        <Box mr="16px" flexShrink="0" minW="44px" textAlign="right" color="#4E5969" fontSize="14px">
          提示:
        </Box>

        <Input
          h="32px"
          flex="1"
          autoFocus
          placeholder="请输入提示"
          bgColor="#F2F3F5"
          fontSize="14px"
          borderRadius="2px"
          value={innerPlaceholder}
          onChange={(e) => {
            setInnerPlaceHolder(e.target.value);
          }}
        />
      </Flex>

      {innerOptions.map((option, index) => (
        <Flex key={index} mt="16px" alignItems="center" overflow="hidden">
          <Box
            mr="16px"
            flexShrink="0"
            minW="44px"
            textAlign="right"
            color="#4E5969"
            fontSize="14px"
          >
            {`选项${index + 1}`}
          </Box>

          <Input
            h="32px"
            flex="1"
            value={option}
            placeholder="请输入选项"
            bgColor="#F2F3F5"
            fontSize="14px"
            borderRadius="2px"
            onChange={(e) => {
              const newOptions = [...innerOptions];
              newOptions[index] = e.target.value;
              setInnerOptions(newOptions);
            }}
          />

          <Center
            flexShrink="0"
            ml="14px"
            w="32px"
            h="32px"
            bgColor="#F3F4F6"
            cursor="pointer"
            onClick={() => {
              const newOptions = [...innerOptions];
              newOptions.splice(index + 1, 0, '');
              setInnerOptions(newOptions);
            }}
          >
            <MyIcon name="common/plus" w="14px" h="14px" color="#4E5969" />
          </Center>

          <Center
            flexShrink="0"
            ml="10px"
            w="32px"
            h="32px"
            bgColor="#F3F4F6"
            cursor="pointer"
            onClick={() => {
              const newOptions = [...innerOptions];
              newOptions.splice(index, 1);
              if (newOptions.length === 0) {
                newOptions.push('');
              }
              setInnerOptions(newOptions);
            }}
          >
            <MyIcon name="common/trash" w="14px" h="14px" color="#4E5969" />
          </Center>
        </Flex>
      ))}
    </Box>
  );
};

export default SelectSettings;
