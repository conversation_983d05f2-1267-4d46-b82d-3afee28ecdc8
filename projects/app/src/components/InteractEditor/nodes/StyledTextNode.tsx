import { DecoratorNode, LexicalNode, Node<PERSON>ey, SerializedLexicalNode } from 'lexical';
import React from 'react';
import { Box } from '@chakra-ui/react';

export type SerializedStyledTextNode = SerializedLexicalNode & {
  text: string;
  style: React.CSSProperties;
  id: string;
};

export class StyledTextNode extends DecoratorNode<React.ReactNode> {
  __text: string;
  __style: React.CSSProperties;
  __id: string;

  constructor(text: string, style: React.CSSProperties, key?: NodeKey, id?: string | null) {
    super(key);
    this.__text = text;
    this.__style = style;
    this.__id = id || '';
  }

  static getType(): string {
    return 'styled-text';
  }

  static clone(node: StyledTextNode): StyledTextNode {
    return new StyledTextNode(node.__text, node.__style, node.__key, node.__id);
  }

  getDomById(): HTMLElement | null {
    return document.getElementById('styled-text-' + this.__id);
  }

  removeDomById() {
    const dom = this.getDomById();
    if (dom) {
      dom.parentElement?.removeChild(dom);
    }
  }

  createDOM(): HTMLElement {
    const element = document.createElement('span');
    element.style.backgroundColor = this.__style.backgroundColor || 'transparent';
    element.style.color = 'var(--, linear-gradient(90deg, #DC7EFF 0%, #601CFF 100%))';
    element.style.borderRadius = '8px';
    element.style.padding = '6px 12px';
    element.style.margin = '0 4px';
    element.style.display = 'inline-block';
    element.style.fontSize = '14px';
    element.style.borderRadius = '6px';
    element.style.background =
      'linear-gradient(90deg, rgba(243, 235, 255, 0.85) 0%, rgba(245, 236, 255, 0.76) 100%)';
    element.tabIndex = 0;
    element.style.alignItems = 'center';
    element.style.justifyContent = 'center';
    element.setAttribute('data-value', this.__text);
    element.setAttribute('data-id', this.__id);
    element.setAttribute('id', 'styled-text-' + this.__id);
    element.classList.add('styled-text');
    return element;
  }
  updateDOM(prevNode: StyledTextNode, dom: HTMLElement): boolean {
    if (prevNode.__text !== this.__text || prevNode.__style !== this.__style) {
      dom.style.backgroundColor = this.__style.backgroundColor || 'transparent';
      dom.style.color = this.__style.color || 'inherit';
      dom.style.borderRadius = '8px';
      dom.style.padding = '2px 8px';
      dom.style.margin = '0 4px';
      dom.style.display = 'inline-block';
      dom.textContent = this.__text;
      dom.setAttribute('data-id', this.__id);
      return true;
    }
    return false;
  }
  exportJSON(): SerializedStyledTextNode {
    return {
      ...super.exportJSON(),
      text: this.__text,
      style: this.__style,
      id: this.__id
    };
  }

  // 实现 decorate 方法
  decorate(): React.ReactNode {
    return (
      <Box as="span" style={this.__style} data-id={this.__id}>
        {this.__text}
      </Box>
    );
  }
}
