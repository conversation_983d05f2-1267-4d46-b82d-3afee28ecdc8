import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import LexicalErrorBoundary from '@lexical/react/LexicalErrorBoundary';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { StyledTextNode } from './nodes/StyledTextNode';
import React, {
  ClipboardEvent,
  ReactNode,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import {
  $createInputNode,
  $isInputNode,
  InputNode,
  InputPayload,
  InputTypeEnum
} from './nodes/InputNode';
import InputPlugin, { INSERT_INPUT_COMMAND } from './plugins/InputPlugin';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import {
  $createLineBreakNode,
  $createNodeSelection,
  $createParagraphNode,
  $createTextNode,
  $getRoot,
  $getSelection,
  $setSelection,
  CLEAR_HISTORY_COMMAND,
  COMMAND_PRIORITY_HIGH,
  LexicalEditor,
  LexicalNode,
  NodeSelection,
  PASTE_COMMAND
} from 'lexical';
import EditorPlugin from './plugins/EditorPlugin';
import styles from './editor.module.css';
import { Box, BoxProps } from '@chakra-ui/react';
import { mergeRegister } from '@lexical/utils';
import { debounce } from 'lodash';
import { TAG_OPTIONS } from '@/pages/deepeditor/components/EnhanceContent';

export interface TagOption {
  label: string;
  value: string;
}

export enum EditorModeEnum {
  design = 'design',
  interact = 'interact',
  plain = 'plain'
}

export type EditorJsonItemType = {
  text?: string;
  payload?: InputPayload;
};

export type EditorJsonType = EditorJsonItemType[];

export type EditorRef = {
  focus: () => void;
  clearHistory: () => void;
  insertText: (text: string) => void;
  insertTextInput: (payload: { text?: string; placeholder?: string }) => void;
  insertSelectInput: (payload: { text?: string; options?: string[]; placeholder?: string }) => void;
  getDom: () => HTMLElement | null;
  // insertStyledText: (text: string, style: React.CSSProperties) => void;
  // removeStyledText: (node: StyledTextNode) => void;
};

function parseText(text: string, callback: (data: EditorJsonItemType) => boolean | void): boolean {
  const m = text.match(/```\s*{.+?}\s*```/gm);
  const arr = text.split(/```\s*{.+?}\s*```/m);

  for (let i = 0; i < arr.length; i++) {
    arr[i] &&
      arr[i].split('\n').forEach((text, index) => {
        if (index > 0) {
          if (callback({ text: '\n' }) === false) {
            return false;
          }
        }
        if (callback({ text }) === false) {
          return false;
        }
      });

    if (m?.[i]) {
      let payload: any;
      try {
        const start = m[i].indexOf('{');
        const end = m[i].lastIndexOf('}') + 1;
        payload = JSON.parse(m[i].substring(start, end));
      } catch (e) {
        console.error('InteractEditor: parse input error', e);
      }
      if (callback(payload?.type ? { payload } : { text: m[i] }) === false) {
        return false;
      }
    }
  }
  return true;
}

function createNodesFromText(mode: EditorModeEnum, text: string) {
  const nodes: LexicalNode[] = [];
  parseText(text, ({ text, payload }) => {
    if (text === '\n') {
      nodes.push($createLineBreakNode());
    } else if (text !== undefined) {
      nodes.push($createTextNode(text));
    } else if (payload) {
      nodes.push($createInputNode({ ...payload, mode }));
    }
  });
  return nodes;
}

export function getJsonFromText(text: string): EditorJsonType {
  const list: EditorJsonType = [];
  parseText(text, ({ text, payload }) => {
    if (text !== undefined) {
      const last = list[list.length - 1];
      if (last?.text !== undefined) {
        last.text += text;
      } else {
        list.push({ text });
      }
    } else {
      list.push({ payload });
    }
  });
  return list;
}

export function getTextFromJson(json: EditorJsonType): string {
  const list: string[] = [];
  json.forEach((it) => {
    if (it.text !== undefined) {
      list.push(it.text);
    } else if (it.payload) {
      list.push('```' + JSON.stringify(it.payload) + '```');
    }
  });
  return list.join('');
}

const enTable = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
export function extractPlainText(source: string | EditorJsonType, checkEmpty = true): string {
  const arr: string[] = [];
  let preType = '' as 'text' | 'payload';
  let preText = '';

  const callback = ({ text, payload }: EditorJsonItemType) => {
    if (text) {
      if (
        preType === 'payload' &&
        preText &&
        enTable.includes(preText[preText.length - 1]) &&
        enTable.includes(text[0])
      ) {
        arr.push(' ');
      }

      arr.push(text);

      preType = 'text';
      preText = text;
    } else if (payload) {
      if (payload.text) {
        if (
          preText &&
          enTable.includes(preText[preText.length - 1]) &&
          enTable.includes(payload.text[0])
        ) {
          arr.push(' ');
        }

        arr.push(payload.text);

        preText = payload.text;
      } else if (checkEmpty) {
        return false;
      }
      preType = 'payload';
    }
  };
  const success =
    typeof source === 'string'
      ? parseText(source, callback)
      : source.every((it) => callback(it) !== false);

  return success ? arr.join('') : '';
}

const editorConfig = {
  namespace: 'InteractEditor',
  onError(error: Error) {
    throw error;
  },
  nodes: [InputNode, StyledTextNode],
  theme: {
    paragraph: styles['paragraph']
  }
};

type EditorProps = {
  mode?: EditorModeEnum;
  value?: string;
  placeholder?: ReactNode;
  spellCheck?: boolean;
  prefix?: string | JSX.Element;
  onChange?: (value: string) => void;
  options?: TagOption[];
  optionValues?: string[];
  onTagChange?: (tags: string[]) => void;
};

const InteractEditor = (
  {
    mode = EditorModeEnum.interact,
    value,
    placeholder = null,
    spellCheck = false,
    prefix,
    onChange,
    onTagChange,
    options,
    optionValues,
    ...props
  }: EditorProps & Omit<BoxProps, keyof EditorProps>,
  ref: React.ForwardedRef<EditorRef>
) => {
  const domRef = useRef<HTMLDListElement>(null);

  const [editor, setEditor] = useState<LexicalEditor>();

  const prefixRef = useRef<HTMLDivElement>(null);

  const [prefixWidth, setPrefixWidth] = useState(0);

  const modeRef = useRef(mode);

  const valueRef = useRef('');

  const onChangRef = useRef(onChange);

  const [isEmpty, setIsEmpty] = useState(!value);

  const [tagData, setTagData] = useState<TagOption[]>([]);

  modeRef.current = mode;

  onChangRef.current = onChange;
  const styledNodesRef = useRef<
    {
      id: string;
      node: StyledTextNode;
    }[]
  >([]);

  const getTagLabel = useCallback((id: string) => {
    const newTagData = TAG_OPTIONS.filter((option) => id === option.value);
    return newTagData[0]?.label;
  }, []);

  const insertStyledText = useCallback(
    (text: string, style: React.CSSProperties, id: string) => {
      editor?.update(() => {
        const selection = $getSelection();
        console.log('selection', selection);
        if (selection) {
          const node = new StyledTextNode(text, style, undefined, id);
          console.log('node', node);
          selection.insertNodes([node]);
          styledNodesRef.current.push({
            id,
            node
          });
        }
      });
    },
    [editor]
  );

  useEffect(() => {
    if (!editor) return;
    editor.update(() => {
      styledNodesRef.current.forEach((info) => {
        info.node?.remove();
      });
    });
  }, [editor, styledNodesRef]);

  useEffect(() => {
    editor?.update(() => {
      const addNodes =
        optionValues?.filter((it) => !styledNodesRef.current.find((node) => node.id === it)) || [];
      const removeNodes = styledNodesRef.current.filter((node) => !optionValues?.includes(node.id));

      removeNodes.forEach((node) => {
        node.node?.remove();
      });
      styledNodesRef.current = styledNodesRef.current.filter((node) =>
        optionValues?.includes(node.id)
      );
      addNodes.forEach((value) => {
        insertStyledText(
          getTagLabel(value),
          {
            background: 'linear-gradient(0deg, #734BFF 0%, #AD50FF 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            overflow: 'hidden',
            fontSize: '14px',
            fontWeight: '500',
            backgroundColor: '#f0f0f0'
          },
          value
        );
        // insertStyledText('', {});
      });
    });
  }, [editor, insertStyledText, optionValues, getTagLabel]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        styledNodesRef.current = styledNodesRef.current.filter((node) => {
          if (node.node.getDomById()) {
            return true;
          } else {
            return false;
          }
        });
        onTagChange?.(styledNodesRef.current.map((node) => node.id));
      }
    };

    const editorElement = editor?.getRootElement();
    editorElement?.addEventListener('keydown', handleKeyDown);

    return () => {
      editorElement?.removeEventListener('keydown', handleKeyDown);
    };
  }, [tagData, editor, insertStyledText]);

  const onCopy = (e: ClipboardEvent<HTMLDivElement>) => {
    if (mode !== EditorModeEnum.interact) {
      return;
    }
    const { clipboardData } = e;
    const data = clipboardData?.getData('text/plain');
    if (!data) {
      return;
    }
    const plain = extractPlainText(data);
    if (plain !== data) {
      clipboardData.setData('text/plain', plain);
    }
  };

  useImperativeHandle(ref, () => ({
    focus: () => editor?.focus(),

    clearHistory: () => editor?.dispatchCommand(CLEAR_HISTORY_COMMAND, undefined),

    insertText: (text) => {
      text &&
        editor?.update(() => {
          const selection = $getSelection();
          selection?.insertRawText(text);
        });
    },

    insertTextInput: ({ text, placeholder }) =>
      editor?.dispatchCommand(INSERT_INPUT_COMMAND, {
        mode: modeRef.current,
        type: InputTypeEnum.text,
        placeholder,
        text
      }),

    insertSelectInput: ({ text, options, placeholder }) =>
      editor?.dispatchCommand(INSERT_INPUT_COMMAND, {
        mode: modeRef.current,
        type: InputTypeEnum.select,
        placeholder,
        text,
        options
      }),

    // insertStyledText: (text: string, style: React.CSSProperties) => {
    //   let styledTextNode: StyledTextNode | null = null;
    //   editor?.update(() => {
    //     const selection = $getSelection();
    //     if (selection) {
    //       styledTextNode = new StyledTextNode(text, style);
    //       selection.insertNodes([styledTextNode]);
    //       valueRef.current += text;
    //       onChangRef.current?.(valueRef.current);
    //     }
    //   });
    //   return styledTextNode;
    // },

    // removeStyledText: (node: StyledTextNode) => {
    //   editor?.update(() => {
    //     node.remove();
    //   });
    //   console.log('removeStyledText', node);
    // },

    getValue: () => valueRef.current,

    getDom: () => domRef.current
  }));

  useEffect(() => {
    if (!editor) {
      return;
    }
    return mergeRegister(
      editor.registerTextContentListener((text) => {
        valueRef.current = text;
        setIsEmpty(!text);
        onChangRef.current?.(text);
      }),

      editor.registerUpdateListener(({ editorState }) => {}),

      editor.registerCommand(
        PASTE_COMMAND,
        (e: ClipboardEvent) => {
          if (modeRef.current !== EditorModeEnum.design) {
            return false;
          }
          const { clipboardData } = e;
          if (!clipboardData) {
            return false;
          }
          const text = clipboardData.getData('text/plain');
          if (!text) {
            return false;
          }
          const selection = $getSelection();
          if (selection) {
            const nodes = createNodesFromText(modeRef.current, text);
            selection.insertNodes(nodes);
            return true;
          }
          return false;
        },
        COMMAND_PRIORITY_HIGH
      )
    );
  }, [editor]);

  const handleChange = useCallback(() => {
    const currentValue = value || '';
    setIsEmpty(!currentValue);

    if (!editor || valueRef.current === currentValue) {
      return;
    }

    editor.update(() => {
      console.log('update,editor');

      const rootNode = $getRoot();
      rootNode.clear();
      const contentNode = $createParagraphNode();
      rootNode.append(contentNode);
      let selection: NodeSelection | null = null;
      if (currentValue) {
        const nodes = createNodesFromText(modeRef.current, currentValue);
        let emptyInputNode: LexicalNode | undefined;
        nodes.forEach((it) => {
          contentNode.append(it);
          if (!emptyInputNode && $isInputNode(it) && !it.getInputText()) {
            emptyInputNode = it;
          }
        });
        if (emptyInputNode) {
          selection = $createNodeSelection();
          selection.add(emptyInputNode.getKey());
        }
      }
      if (selection) {
        $setSelection(selection);
      } else {
        $getRoot().selectEnd();
      }
    });
  }, [editor, value]);

  useEffect(() => {
    const debouncedChange = debounce(handleChange, 300);
    debouncedChange();
    return () => debouncedChange.cancel();
  }, [editor, value, handleChange]);

  useEffect(() => {
    editor?.dispatchCommand(CLEAR_HISTORY_COMMAND, undefined);
  }, [editor, mode]);

  useEffect(() => {
    setPrefixWidth(prefixRef.current?.offsetWidth || 0);
  }, [prefix]);

  return (
    <Box
      ref={domRef}
      className={`${styles['editor']} ${styles[`editor-is-${mode}`]}`}
      lineHeight="1.8em"
      overflowX="hidden"
      overflowY="auto"
      {...props}
      {...(prefix && {
        css: {
          [`& .${styles['editor-content']} > :first-child::before`]: {
            content: '""',
            display: 'inline-block',
            width: `${prefixWidth}px`
          }
        }
      })}
      onCopy={onCopy}
    >
      <LexicalComposer initialConfig={editorConfig}>
        {prefix ? (
          <Box
            ref={prefixRef}
            className={styles['editor-prefix']}
            onClick={() => {
              editor?.update(() => {
                $getRoot().selectStart();
              });
            }}
          >
            {prefix}
            {isEmpty && placeholder && (
              <Box className={styles['editor-placeholder']}>{placeholder}</Box>
            )}
          </Box>
        ) : isEmpty && placeholder ? (
          <Box whiteSpace="normal" className={styles['editor-placeholder']}>
            {placeholder}
          </Box>
        ) : (
          ''
        )}

        <PlainTextPlugin
          contentEditable={
            <ContentEditable className={styles['editor-content']} spellCheck={spellCheck} />
          }
          placeholder={null}
          ErrorBoundary={LexicalErrorBoundary}
        />

        <EditorPlugin setEditor={setEditor} />

        <HistoryPlugin />

        <InputPlugin />
      </LexicalComposer>
    </Box>
  );
};

export default forwardRef(InteractEditor);
