import { useLayout } from '@/hooks/useLayout';
import { Box, ChakraProps, useUpdateEffect } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { ForwardedRef, ReactNode, useEffect, useId, useImperativeHandle, useRef } from 'react';

const LayoutOverlay = ({
  children,
  onClose,
  ref,
  ...props
}: { children?: ReactNode; onClose?: () => void; ref?: ForwardedRef<any> } & ChakraProps) => {
  const id = useId();

  const router = useRouter();

  const { contentSize, overlayId, setOverlayId } = useLayout();

  const closeOverlay = () => {
    onClose?.();
  };

  useImperativeHandle(ref, () => ({
    close: (shouldClose: boolean) => {
      if (!shouldClose) {
        closeOverlay();
      }
    }
  }));

  useEffect(() => {
    setOverlayId(id);
  }, [id, setOverlayId]);

  useUpdateEffect(() => {
    overlayId && overlayId !== id && onClose?.();
  }, [overlayId]);

  useUpdateEffect(() => {
    onClose?.();
  }, [router]);

  return (
    <Box
      pos="fixed"
      right="0"
      bottom="0"
      w={contentSize?.width || '100%'}
      h={contentSize?.height || '100%'}
      overflow="hidden"
      zIndex={900}
      {...props}
    >
      {children}
    </Box>
  );
};

export default LayoutOverlay;
