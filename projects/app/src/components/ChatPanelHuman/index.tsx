import ChatBox from '@/components/ChatBox/indexHuman';
import { streamFetch } from '@/utils/fetch';
import { useLoading } from '@/hooks/useLoading';
import { chatUpdate, getInitChatInfo, updateChatItem } from '@/api/chat';
import { useChatStore } from '@/store/useChatStore';
import { useUserStore } from '@/store/useUserStore';
import { Flex, useToast } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import { useQuery } from '@tanstack/react-query';
import { nanoid } from 'nanoid';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppStore } from '@/store/useAppStore';
import { getNanoid } from '@/utils/tools';
import { ChatRoleEnum, ChatStatusEnum } from '@/fastgpt/global/core/chat/constants';
import { ChatBoxRef, StartChatFnProps } from '../ChatBox/type';
import { UpdateHistoryProps } from '@/types/api/chat';
import { APP_ICON } from '@/constants/common';
import { Toast } from '@/utils/ui/toast';
import useHistoryData from '@/hooks/useHistoryData';
import { ChatSiteItemType } from '@/fastgpt/global/core/chat/type';

const titlePromptTemplate = `
目标：
-通过一轮对话内容生成一个简短的标题。

回答要求：
- 标题应该与对话内容一致，且与对话内容相关。
- 标题不带标点符号。
- 标题不带任何解释性前缀。
- 标题长度少于20个字。

下面是一个对话内容：

问题:’‘’{{question}}‘’‘

回答:‘’‘{{answer}}‘’‘

`;

const text = '✨ Hello，需要我帮你做些什么吗？';
const charCount = text.length;
const singleCharDuration = 0.17;
const displayDuration = 2;
const totalDuration = charCount * singleCharDuration + displayDuration;

const fadeInStyle = {
  display: 'inline-block',
  opacity: 0,
  animation: `textAnimation ${singleCharDuration}s forwards`,
  animationIterationCount: 1
};

const cursorStyle = {
  display: 'inline-block',
  opacity: 1,
  animation: 'cursorBlink 0.8s infinite'
};

const fadeInKeyframes = `
  @keyframes textAnimation {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }
  @keyframes cursorBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }
`;

const AnimatedMessage = () => {
  const [currentIndex, setCurrentIndex] = useState(1);
  const [key, setKey] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setKey((k) => k + 1);
      setCurrentIndex(0);
    }, totalDuration * 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (currentIndex < text.length - 1) {
      const timer = setTimeout(() => {
        setCurrentIndex((i) => i + 1);
      }, singleCharDuration * 1000);
      return () => clearTimeout(timer);
    }
  }, [currentIndex]);

  return (
    <Flex justifyContent="center" mt="12px" key={key}>
      <style>{fadeInKeyframes}</style>
      {text.split('').map((char, index) => (
        <span key={index}>
          <span
            style={{
              ...fadeInStyle,
              animationDelay: `${index * singleCharDuration}s`
            }}
          >
            {char}
          </span>
          {index === currentIndex && index < text.length - 1 && <span style={cursorStyle}>_</span>}
        </span>
      ))}
    </Flex>
  );
};

const ChatPanelHuman = ({
  chatId,
  activeRoute,
  answerCallback,
  ...props
}: {
  answerCallback?: (value: string, isAnswerDone?: boolean) => void;
  activeRoute?: string; // 将 activeRoute 设置为可选参数
  chatId: string;
} & ChakraProps) => {
  const chatBoxRef = useRef<ChatBoxRef>(null);
  const forbidRefresh = useRef(false);
  const toast = useToast({ position: 'top' });
  const router = useRouter();
  const { generalAppId, loadMyApps, setViewApp, viewApp } = useAppStore();
  const {
    setLastChatAppId,
    setLastChatId,
    pushHistory,
    updateHistory,
    chatData,
    setChatData,
    delOneHistoryItem,
    setIsAppListVisible,
    setChatId,
    setAllIsDisplayeZero,
    allIsDisplayeZero
  } = useChatStore();

  const flexRef = useRef<HTMLDivElement>(null);

  const { userInfo } = useUserStore();

  const { Loading, setIsLoading } = useLoading();

  const appId = chatId;

  const newChatId = useMemo(() => (appId && chatId ? '' : nanoid()), [appId, chatId]);

  const routeHistory = useRef<string[]>([]);

  const [isShare, setIsShare] = useState(false);

  const [isStartedChat, setIsStartedChat] = useState(false);
  const startChat = useCallback(
    async ({
      chatAppId,
      value,
      ocrFileKey,
      content,
      fileKeys,
      messages,
      responseChatItemId,
      controller,
      generatingMessage,
      variables,
      rawParseResult
    }: StartChatFnProps) => {
      const prompts = messages.slice(-1);

      const completionChatId = chatId || newChatId;

      const data = {
        ocrFileKey,
        value,
        content,
        fileKeys,
        messages: prompts,
        responseChatItemId,
        variables,
        tenantAppId: appId,
        chatId: nanoid(),
        rawParseResult
      };

      // 打印传入的data对象

      const { responseText, responseData } = await streamFetch({
        url: '/huayun-ai/client/chat/completionsOriginal',
        data: data,
        onMessage: generatingMessage,
        abortCtrl: controller
      });
      console.log(responseText, responseData);
      // 检查 responseText 是否为空字符串
      if (responseText.trim() === '') {
        // 如果为空,不更新历史记录,并将输入内容返回到输入框

        return Promise.reject('');
      }

      const newTitle = '新建对话';

      // new chat
      if (completionChatId !== chatId && controller.signal.reason !== 'cancel') {
        const newHistory: UpdateHistoryProps = {
          title: chatData.customTitle || '',
          chatId: completionChatId,
          tenantAppId: appId
        };

        if (chatData.customTitle) {
        }
        if (controller.signal.reason !== 'leave') {
          forbidRefresh.current = true;

          if (!chatData.customTitle) {
          }
        }
      } else {
        // update chat
      }
      // update chat window
      setChatData((state) => ({
        ...state,
        ...(!state.chatId && { chatId: completionChatId }),
        title: newTitle,
        history: chatBoxRef.current?.getChatHistories() || state.history
      }));

      console.log(completionChatId, 'completionChatId');
      setChatId(completionChatId);

      return { responseText, responseData, isNewChat: forbidRefresh.current };
    },
    [appId, chatId, newChatId, chatData, router, pushHistory, setChatData, updateHistory]
  );

  // get chat app info
  const loadChatInfo = useCallback(
    async ({
      appId,
      chatId,
      loading = false
    }: {
      appId: string;
      chatId: string;
      loading?: boolean;
    }) => {
      try {
        loading && setIsLoading(true);

        const res = await getInitChatInfo({
          tenantAppId: appId,
          chatId
        });

        const history = res.history.map(
          (item) =>
            ({
              ...item,
              dataId: item.dataId || getNanoid(),
              status: ChatStatusEnum.finish,
              value: item.value || [],
              responseData: (item.obj === ChatRoleEnum.AI && item.responseData) || []
            }) as ChatSiteItemType
        );

        setChatData({
          ...res,
          history
        });

        // have records.
        chatBoxRef.current?.resetHistory(history);
        chatBoxRef.current?.resetVariables(res.variables);
        if (res.history.length > 0) {
          setTimeout(() => {
            chatBoxRef.current?.scrollToBottom('auto');
          }, 500);
        }
      } catch (e: any) {
        setLastChatAppId('');
        setLastChatId('');
        e?.code !== 406 && Toast.error('初始化对话失败');
      }
      setIsLoading(false);
      return null;
    },
    [setIsLoading, setChatData, setLastChatAppId, setLastChatId]
  );

  const onDelMessage = useCallback(
    (e: { contentId: string }) => delOneHistoryItem({ ...e, tenantAppId: appId, chatId }),
    [appId, chatId, delOneHistoryItem]
  );

  useEffect(() => {
    if (chatData && chatData.sceneList) {
      const allZero = chatData.sceneList.every((item) => item.isDisplayed === 1);
      setAllIsDisplayeZero(allZero);
    }
  }, [chatData.sceneList]);

  // 初始化聊天框
  useQuery(['init', { appId, chatId }], () => {
    if (!appId) {
      return loadMyApps()
        .then((apps) => {
          const chatAppId = generalAppId || apps[0]?.id;
          if (chatAppId) {
          } else {
            toast({
              title: '请先创建应用',
              status: 'error'
            });
          }
        })
        .catch(() => {
          toast({
            title: '初始化对话失败',
            status: 'error'
          });
        });
    }

    // store id
    appId && setLastChatAppId(appId);
    setLastChatId(chatId);

    if (forbidRefresh.current) {
      forbidRefresh.current = false;
      return null;
    }

    // 打开右侧弹窗
    setIsAppListVisible(true);
    viewApp && setViewApp();

    return loadChatInfo({
      appId,
      chatId,
      loading: appId !== chatData.appId
    });
  });
  useEffect(() => {
    return () => {
      setChatData((state) => ({
        ...state,
        appId: '',
        chatId: ''
      }));
    };
  }, [setChatData]);

  useEffect(() => {
    // 在组件挂载时记录当前路由
    routeHistory.current.push(router.asPath);

    // 在组件卸载时清除路由记录
    return () => {
      routeHistory.current = [];
    };
  }, [router.asPath]);

  return (
    <Flex
      w="100%"
      ref={flexRef}
      flexDir="column"
      alignItems="center"
      overflow="hidden"
      {...props}
      backgroundImage="/imgs/app/chatMiniBg.svg"
      backgroundRepeat="no-repeat"
      backgroundSize="auto 218px"
      backgroundPosition="top center"
      h="100%"
      pb="16px"
    >
      {!isStartedChat ? <></> : <></>}

      <ChatBox
        ref={chatBoxRef}
        flex="1"
        w="100%"
        overflow="hidden"
        showEmptyIntro
        appAvatar={chatData.appAvatarUrl || APP_ICON}
        userAvatar={userInfo?.avatar}
        useVision={true}
        chatConfig={chatData.app.chatConfig}
        useInternet={chatData.app?.useInternet}
        onStartChat={startChat}
        onDelMessage={onDelMessage}
        onCancelShare={() => {
          setIsShare(false);
        }}
        appId={appId}
        isShare={isShare}
        chatId={chatId || newChatId}
        setIsStartedChat={setIsStartedChat}
        answerCallback={answerCallback}
      />

      <Loading fixed={false} />
    </Flex>
  );
};

export default React.memo(ChatPanelHuman);
