import React, { useRef, useState } from 'react';
import {
  Box,
  Button,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Text,
  Center
} from '@chakra-ui/react';
// import { FaPlay, FaPause } from 'react-icons/fa';
import SvgIcon from '@/components/SvgIcon';

interface CustomAudioPlayerProps {
  src: string;
}

const CustomAudioPlayer: React.FC<CustomAudioPlayerProps> = ({ src }) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const togglePlayPause = () => {
    if (isPlaying) {
      audioRef.current?.pause();
    } else {
      audioRef.current?.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleSeek = (value: number) => {
    if (audioRef.current) {
      const seekTime = (value / 100) * duration;
      audioRef.current.currentTime = seekTime;
      setCurrentTime(seekTime);
    }
  };

  return (
    <Box
      display="flex"
      width="100%"
      h="62px"
      padding="10px"
      backgroundColor="rgba(0, 0, 0, 0.03)"
      alignItems="center"
      borderRadius="30px"
      color="#000"
      fontSize="14px"
      fontWeight="500"
    >
      <audio
        ref={audioRef}
        src={src}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
      />
      <Box p="14px 0 0 20px">
        {!isPlaying && <SvgIcon name="play" w="35px" h="35px" onClick={togglePlayPause} />}
        {isPlaying && <SvgIcon name="pauseCircle" w="35px" h="35px" onClick={togglePlayPause} />}
      </Box>
      <Text marginLeft="10px">
        {Math.floor(currentTime / 60)}:
        {Math.floor(currentTime % 60)
          .toString()
          .padStart(2, '0')}
      </Text>
      <Text marginLeft="5px">/</Text>
      <Text marginLeft="5px">
        {Math.floor(duration / 60)}:
        {Math.floor(duration % 60)
          .toString()
          .padStart(2, '0')}
      </Text>
      <Slider
        value={(currentTime / duration) * 100 || 0}
        onChange={handleSeek}
        marginLeft="20px"
        flexGrow="1"
      >
        <SliderTrack bg="#e0e0e0">
          <Box position="relative" width="100%" height="100%">
            <SliderFilledTrack bg="#4f4f4f" />
          </Box>
        </SliderTrack>
        <SliderThumb boxSize={4} />
      </Slider>
      <Box p="14px 0 0 20px">
        <SvgIcon name="volume" w="35px" h="35px" />
      </Box>
    </Box>
  );
};

export default CustomAudioPlayer;
