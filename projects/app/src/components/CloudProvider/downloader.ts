import {
  createDownloadRecord,
  createDownloadZip,
  getDownloadRecordPage,
  getDownloadSize,
  updateDownloadRecord
} from '@/api/cloud';
import {
  DownloadBatchOptions,
  DownloadOptions,
  DownloadFileType,
  DownloadFilesType,
  DownloadFileOptions,
  DownloadFolderOptions,
  DownloadTypeEnum,
  DownloadSourceEnum,
  DownloadTask,
  ScheduleErrorEnum,
  DownloadStatusEnum,
  DownloadStatType,
  OnDownloadChangeListenerType
} from './type';
import { formatFileSize } from '@/utils/tools';
import { DownloadRecordType, GetDownloadRecordPageProps } from '@/types/api/cloud';
import { DownloadRecordStatus, FileTypeEnum } from '@/constants/api/cloud';
import { downloadFile } from '@/utils/file';
import { Toast } from '@/utils/ui/toast';

const debug = true;

const queueLimit = 2;

const sizeLimit = 0;

const fetchInterval = 5 * 1000;

const pageSize = 100;

const recordStatusPriority = {
  [DownloadRecordStatus.Packing]: 0,
  [DownloadRecordStatus.Waiting]: 0,
  [DownloadRecordStatus.Downloaded]: 1,
  [DownloadRecordStatus.Failed]: 2
};

const downloadStatusPriority = {
  [DownloadStatusEnum.Waiting]: 1,
  [DownloadStatusEnum.Downloading]: 0,
  [DownloadStatusEnum.Downloaded]: 2,
  [DownloadStatusEnum.Failed]: 3
};

const recordStatus2DownloadStatus = {
  [DownloadRecordStatus.Packing]: DownloadStatusEnum.Waiting,
  [DownloadRecordStatus.Waiting]: DownloadStatusEnum.Waiting,
  [DownloadRecordStatus.Downloaded]: DownloadStatusEnum.Downloaded,
  [DownloadRecordStatus.Failed]: DownloadStatusEnum.Failed
};

export class Downloader {
  private tenantId: string;

  private tmbId: string;

  private records: DownloadRecordType[] = [];

  private total?: number;

  private queue: DownloadTask[] = [];

  private fetchTimeoutId?: NodeJS.Timeout;

  private onChangeListeners: OnDownloadChangeListenerType[] = [];

  private onChangePending?: boolean;

  private destroyed?: boolean;

  constructor(tenantId: string, tmbId: string) {
    this.tenantId = tenantId;
    this.tmbId = tmbId;
    this.refreshFiles();
  }

  public addOnChangeListener(listener: OnDownloadChangeListenerType) {
    this.onChangeListeners.includes(listener) || this.onChangeListeners.push(listener);
  }

  public removeOnChangeListener(listener: OnDownloadChangeListenerType) {
    this.onChangeListeners = this.onChangeListeners.filter((it) => it !== listener);
  }

  public async addFiles(options: DownloadOptions): Promise<void> {
    if (options.type === DownloadTypeEnum.File) {
      await this.addFile(options);
    } else if (options.type === DownloadTypeEnum.Folder) {
      await this.addFolder(options);
    } else if (options.type === DownloadTypeEnum.Batch) {
      await this.addBatch(options);
    } else {
      return Promise.reject('unsupported type');
    }
    this.getPendingRecords();
  }

  public async refreshFiles() {
    this.stopCheckPacking();
    try {
      this.records = await this.getHeadRecords();
    } catch {}
    this.startCheckPacking();
    this.schedule();
    this.onChange();
  }

  public async loadMoreFiles() {
    this.loadMoreRecords();
  }

  public removeFiles(files: DownloadFilesType) {
    let changed = false;
    (Array.isArray(files) ? files : [files]).forEach((it) => {
      const id = typeof it === 'string' ? it : it.id;
      const task = this.queue.find((it) => it.id === id);
      task?.abortController.abort();
      const index = this.records.findIndex((it) => it.id === id);
      if (index > 0) {
        this.records.splice(index, 1);
        changed = true;
      }
    });
    if (changed) {
      this.onChange();
    }
  }

  public destroy() {
    this.destroyed = true;
    this.records = [];
    this.queue.forEach((task) => task.abortController.abort());
    this.queue = [];
    this.onChangeListeners = [];
    this.stopCheckPacking();
  }

  private async addFile({ bizType, fileId, fileKey }: DownloadFileOptions): Promise<void> {
    await createDownloadRecord({
      bizType,
      cloudFileId: fileId,
      downloadStatus: DownloadRecordStatus.Waiting,
      fileType: FileTypeEnum.File,
      tenantId: this.tenantId,
      fileKey
    });
    return Promise.resolve();
  }

  private static showLimitToast(sizeLimit: number) {
    const error = `下载文件大小超过${formatFileSize(sizeLimit)}`;
    Toast.error(error);
  }

  private async addFolder({
    bizType,
    source,
    folderId,
    maxSize
  }: DownloadFolderOptions): Promise<void> {
    const auditStatus = source === DownloadSourceEnum.Audit ? 2 : 1;
    const version = source === DownloadSourceEnum.History ? 2 : 1;

    const { fileSize } = await getDownloadSize({
      bizType,
      spaceIds: [folderId],
      tenantId: this.tenantId,
      tmbId: this.tmbId,
      auditStatus
    });

    const limit = maxSize || sizeLimit || 0;
    if (limit && +fileSize > limit) {
      Downloader.showLimitToast(limit);
      return Promise.reject();
    }

    await createDownloadZip({
      bizType,
      id: folderId,
      tenantId: this.tenantId,
      range: 2,
      version,
      auditStatus
    });

    return Promise.resolve();
  }

  private async addBatch({
    bizType,
    source,
    parentId,
    folderIds,
    fileIds,
    maxSize
  }: DownloadBatchOptions): Promise<void> {
    const auditStatus = source === DownloadSourceEnum.Audit ? 2 : 1;
    const version = source === DownloadSourceEnum.History ? 2 : 1;

    const { fileSize } = await getDownloadSize({
      bizType,
      spaceIds: folderIds,
      fileIds: fileIds,
      tenantId: this.tenantId,
      tmbId: this.tmbId,
      auditStatus
    });

    const limit = maxSize || sizeLimit || 0;
    if (limit && +fileSize > limit) {
      Downloader.showLimitToast(limit);
      return Promise.reject();
    }

    await createDownloadZip({
      bizType,
      id: parentId,
      tenantId: this.tenantId,
      spaceIds: folderIds,
      fileIds: fileIds,
      range: 1,
      version,
      auditStatus
    });

    return Promise.resolve();
  }

  private async getHeadRecords() {
    const data: GetDownloadRecordPageProps = {
      current: 1,
      size: pageSize
    };

    let total: number | undefined;
    const records: DownloadRecordType[] = [];

    while (true) {
      const res = await getDownloadRecordPage(data);
      if (total === undefined) {
        total = res.total;
      } else if (res.total !== total) {
        total = undefined;
        data.current = 1;
        records.length = 0;
        continue;
      }

      this.total = res.total;

      if (!res.records.length) {
        break;
      }

      records.push(...res.records.filter((it) => !records?.some((r) => r.id === it.id)));
      if (records.length >= total!) {
        break;
      }

      if (
        [DownloadRecordStatus.Downloaded, DownloadRecordStatus.Failed].includes(
          records[records.length - 1]?.downloadStatus
        )
      ) {
        break;
      }

      data.current = data.current! + 1;
    }

    return records;
  }

  private async getPendingRecords() {
    this.stopCheckPacking();

    let changed = false;
    try {
      const records = await this.getHeadRecords();
      changed = this.addRecords(records);
    } catch {}
    this.startCheckPacking();

    if (changed) {
      this.schedule();
      this.onChange();
    }
  }

  private async refreshLastPageRecords() {
    const data = {
      current: Math.floor(this.records.length / pageSize) + 1,
      size: pageSize
    };
    const res = await getDownloadRecordPage(data);
    const changed = this.addRecords(res.records);
    if (changed) {
      this.onChange();
    }
  }

  private async loadMoreRecords() {
    if (this.total !== undefined && this.records.length >= this.total) {
      return Promise.reject('no more');
    }

    if (this.records.length === 0) {
      return this.getHeadRecords();
    }

    const current = Math.floor(this.records.length / pageSize) + 1;
    const data = {
      current: current * pageSize === this.records.length ? current + 1 : current,
      size: pageSize
    };
    const res = await getDownloadRecordPage(data);
    const changed = this.addRecords(res.records);
    if (changed) {
      this.onChange();
    }
  }

  private addRecords(records: DownloadRecordType[]): boolean {
    let changed = false;
    records.forEach((record) => {
      const index = this.records.findIndex(({ id }) => id === record.id);
      if (index < 0) {
        this.records.push(record);
        changed = true;
      } else if (!this.isRecordEquals(record, this.records[index])) {
        this.records[index] = record;
        changed = true;
      }
    });
    return changed;
  }

  private isRecordEquals(l: DownloadRecordType, r: DownloadRecordType): boolean {
    return l.id === r.id && l.downloadStatus === r.downloadStatus && l.fileKey === r.fileKey;
  }

  public getFiles() {
    const files: DownloadFileType[] = this.records.map((it) => {
      const task =
        it.downloadStatus === DownloadRecordStatus.Waiting
          ? this.queue.find((task) => task.id === it.id)
          : undefined;

      const status = task
        ? DownloadStatusEnum.Downloading
        : recordStatus2DownloadStatus[it.downloadStatus];

      return {
        bizType: it.bizType,
        id: it.id,
        name: it.fileName || it.files?.fileName || '',
        size: it.fileSize || 0,
        url: it.files?.fileUrl,
        createTime: it.createTime,
        status,
        loadedSize: task
          ? task.loadedSize
          : status === DownloadStatusEnum.Downloaded
            ? it.fileSize || 0
            : 0
      };
    });

    files.sort((l, r) => {
      const ret = downloadStatusPriority[l.status] - downloadStatusPriority[r.status];
      return ret ? ret : r.createTime.localeCompare(l.createTime);
    });
    return files;
  }

  public getStat(): DownloadStatType {
    const downloadFiles = this.getFiles();
    let downloadingCount = 0;
    let downloadedCount = 0;
    let downloadFailedCount = 0;
    downloadFiles.forEach((file) => {
      if (
        file.status === DownloadStatusEnum.Waiting ||
        file.status === DownloadStatusEnum.Downloading
      ) {
        downloadingCount++;
      } else if (file.status === DownloadStatusEnum.Downloaded) {
        downloadedCount++;
      } else if (file.status === DownloadStatusEnum.Failed) {
        downloadFailedCount++;
      }
    });
    return {
      downloadFiles,
      downloadingCount,
      downloadedCount,
      downloadFailedCount
    };
  }

  private onChange() {
    if (this.onChangeListeners.length && !this.onChangePending) {
      this.onChangePending = true;
      Promise.resolve().then(() => {
        this.onChangePending = false;
        const stat = this.getStat();
        this.onChangeListeners.forEach((listener) => listener(stat));
      });
    }
  }

  private startCheckPacking() {
    if (this.fetchTimeoutId) {
      clearTimeout(this.fetchTimeoutId);
      this.fetchTimeoutId = undefined;
    }

    const packing = this.records.reduce(
      (packing: DownloadRecordType | undefined, record: DownloadRecordType) =>
        record.downloadStatus === DownloadRecordStatus.Packing &&
        (!packing || packing.createTime > record.createTime)
          ? record
          : packing,
      undefined
    );

    if (!packing) {
      return;
    }

    const waitCount = this.records.reduce(
      (acc, cur) =>
        cur.downloadStatus === DownloadRecordStatus.Waiting &&
        !this.queue?.some(({ id }) => id === cur.id) &&
        cur.createTime <= packing!.createTime
          ? acc + 1
          : acc,
      0
    );

    if (waitCount >= queueLimit) {
      return;
    }

    this.fetchTimeoutId = setTimeout(() => {
      this.fetchTimeoutId = undefined;
      this.getPendingRecords();
    }, fetchInterval);
  }

  private stopCheckPacking() {
    if (this.fetchTimeoutId) {
      clearTimeout(this.fetchTimeoutId);
      this.fetchTimeoutId = undefined;
    }
  }

  private schedule() {
    if (this.destroyed) {
      return;
    }

    for (let i = this.queue.length; i < queueLimit; i++) {
      this.doSchedule()
        .then((res) => {
          this.schedule();
          return res;
        })
        .catch((err) => {
          debug && console.log('uploader error', err);
          if (err !== ScheduleErrorEnum.Empty) {
            this.schedule();
          }
          return err;
        });
    }
  }

  private doSchedule(): Promise<void> {
    if (this.queue.length >= queueLimit) {
      return Promise.reject(ScheduleErrorEnum.Limited);
    }

    const record = this.records.find(
      (it) =>
        it.downloadStatus === DownloadRecordStatus.Waiting &&
        !this.queue?.some((task) => task.id === it.id)
    );

    if (!record) {
      return Promise.reject(ScheduleErrorEnum.Empty);
    }

    const task = {
      id: record.id,
      loadedSize: 0,
      abortController: new AbortController()
    };

    this.queue.push(task);

    this.startCheckPacking();
    this.onChange();

    return downloadFile(record.fileKey, {
      signal: task.abortController.signal,
      onProgress: (_, loaded) => {
        if (loaded && record.fileSize && (loaded - task.loadedSize) / record.fileSize >= 0.0001) {
          task.loadedSize = loaded;
          this.onChange();
        }
      }
    })
      .then(() => {
        this.queue = this.queue.filter((it) => it.id !== task.id);
        this.startCheckPacking();
        return this.updateRecordStatus(record, DownloadRecordStatus.Downloaded);
      })
      .catch(() => {
        this.queue = this.queue.filter((it) => it.id !== task.id);
        this.startCheckPacking();
        return this.updateRecordStatus(record, DownloadRecordStatus.Failed).finally(() => {
          return Promise.reject();
        });
      });
  }

  private updateRecordStatus(record: DownloadRecordType, status: DownloadRecordStatus) {
    record.downloadStatus = status;
    return updateDownloadRecord({
      id: record.id,
      downloadStatus: status
    }).finally(() => {
      if (this.guessSortLast(record)) {
        this.records.filter((it) => it.id !== record.id);
        this.refreshLastPageRecords();
      } else {
        this.onChange();
      }
    });
  }

  private guessSortLast(record: DownloadRecordType) {
    const priority = this.queue.some((task) => task.id === record.id)
      ? -1
      : recordStatusPriority[record.downloadStatus] ?? 100;

    return !this.records.some((other) => {
      if (other.id === record.id) {
        return false;
      }
      const otherPriority = this.queue.some((task) => task.id === other.id)
        ? -1
        : recordStatusPriority[other.downloadStatus] ?? 100;
      if (otherPriority > priority) {
        return true;
      }
      return otherPriority === priority && record.createTime > other.createTime;
    });
  }
}
