import { ReactNode, createContext, useEffect, useState } from 'react';
import { useUserStore } from '@/store/useUserStore';
import { CloudContextType } from './type';
import { Uploader } from './uploader';
import { Downloader } from './downloader';

export const CloudContext = createContext<CloudContextType>({});

const CloudProvider = ({ children }: { children: ReactNode }) => {
  const { userInfo } = useUserStore();

  const [uploader, setUploader] = useState<Uploader>();

  const [downloader, setDownloader] = useState<Downloader>();

  useEffect(() => {
    if (!userInfo?.tmbId) {
      return;
    }

    const uploader = new Uploader(userInfo.tmbId);
    const downloader = new Downloader(userInfo.tenantId, userInfo.tmbId);

    setUploader(uploader);
    setDownloader(downloader);

    return () => {
      uploader.destroy();
      downloader.destroy();
    };
  }, [userInfo?.tenantId, userInfo?.tmbId]);

  return (
    <CloudContext.Provider
      value={{
        uploader,
        downloader
      }}
    >
      {children}
    </CloudContext.Provider>
  );
};

export default CloudProvider;
