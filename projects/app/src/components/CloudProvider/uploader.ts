import { BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import {
  UploadOptions,
  DBUploadFileType,
  FinishStatusEnum,
  UploadFileType,
  UploadFilesType,
  UploadTaskType,
  UploadStatusEnum,
  ScheduleErrorEnum,
  OnUploadChangeListenerType,
  UploadStatType
} from './type';
import { nanoid } from 'nanoid';
import {
  cancelMyUploadFolder,
  cancelTenantUploadFolder,
  createMyUploadFolder,
  createTenantUploadFolder,
  finishMyUploadFolder,
  finishTenantUploadFolder,
  getUploadTaskInfo,
  initUploadTask,
  uploadPart,
  updateUploadFile,
  uploadFile,
  mergeFileParts
} from '@/api/cloud';
import { CloudDB, obtainCloudDB, releaseCloudDB } from './cloudDB';
import { AxiosProgressEvent } from 'axios';

const debug = true;

const queueLimit = 2;
const retryLimit = 1;

const statusPriority = {
  [UploadStatusEnum.Waiting]: 2,
  [UploadStatusEnum.Uploading]: 1,
  [UploadStatusEnum.Uploaded]: 3,
  [UploadStatusEnum.Failed]: 0
};

export class Uploader {
  private tmbId: string;

  private db: CloudDB;

  private rootFiles: UploadFileType[] = [];

  private allFiles: UploadFileType[] = [];

  private allFilesMap: Record<string, UploadFileType> = {};

  private queue: UploadTaskType[] = [];

  private onChangeListeners: OnUploadChangeListenerType[] = [];

  private onChangePending?: boolean;

  constructor(tmbId: string) {
    this.tmbId = tmbId;
    this.db = obtainCloudDB();
    this.db.getUploadFiles(tmbId).then((files) => {
      this.setDBFiles(files);
    });
  }

  public addOnChangeListener(listener: OnUploadChangeListenerType) {
    this.onChangeListeners?.includes(listener) || this.onChangeListeners.push(listener);
  }

  public removeOnChangeListener(listener: OnUploadChangeListenerType) {
    this.onChangeListeners = this.onChangeListeners.filter((it) => it !== listener);
  }

  public async addFiles({
    bizType,
    path: remotePath,
    parentId: remoteParentId,
    oldId: remoteOldId,
    files
  }: UploadOptions): Promise<void> {
    const rootFiles: UploadFileType[] = [];
    const allFiles: UploadFileType[] = [];

    const now = Date.now();

    files.forEach((file) => {
      if (!file.webkitRelativePath) {
        const uploadFile: UploadFileType = {
          type: FileTypeEnum.File,
          localId: nanoid(),
          remotePath,
          remoteParentId,
          bizType,
          name: file.name,
          size: file.size,
          file,
          tmbId: this.tmbId,
          uploadStatus: UploadStatusEnum.Waiting,
          status: UploadStatusEnum.Waiting,
          retryCount: 0,
          createTime: now,
          updateTime: now,
          loadedSize: 0
        };
        rootFiles.push(uploadFile);
        allFiles.push(uploadFile);
        return;
      }

      let parentFolder: UploadFileType | undefined;

      const path = file.webkitRelativePath.split('/');
      path.slice(0, path.length - 1).forEach((name) => {
        let uploadFolder = (parentFolder ? parentFolder.children : rootFiles)?.find(
          (it) => it.type === FileTypeEnum.Folder && it.name === name
        );

        if (!uploadFolder) {
          uploadFolder = {
            type: FileTypeEnum.Folder,
            localId: nanoid(),
            localParentId: parentFolder?.localId,
            bizType,
            name,
            size: 0,
            tmbId: this.tmbId,
            children: [],
            uploadStatus: UploadStatusEnum.Waiting,
            status: UploadStatusEnum.Waiting,
            retryCount: 0,
            createTime: now,
            updateTime: now,
            loadedSize: 0
          };

          allFiles.push(uploadFolder);
          if (!parentFolder) {
            uploadFolder.remotePath = remotePath;
            uploadFolder.remoteParentId = remoteParentId;
            rootFiles.push(uploadFolder);
          } else {
            parentFolder.children!.push(uploadFolder);
          }
        }
        parentFolder = uploadFolder;
      });

      const uploadFile: UploadFileType = {
        type: FileTypeEnum.File,
        localId: nanoid(),
        localParentId: parentFolder!.localId,
        bizType,
        name: file.name,
        size: file.size,
        file,
        tmbId: this.tmbId,
        uploadStatus: UploadStatusEnum.Waiting,
        status: UploadStatusEnum.Waiting,
        retryCount: 0,
        createTime: now,
        updateTime: now,
        loadedSize: 0
      };

      parentFolder!.children!.push(uploadFile);
      allFiles.push(uploadFile);
    });

    if (remoteOldId) {
      if (rootFiles.length > 1) {
        return Promise.reject('replace multiple error');
      }
      rootFiles[0].remoteOldId = remoteOldId;
    }

    this.updateSize(rootFiles);

    this.rootFiles = this.rootFiles.concat(rootFiles);
    this.allFiles = this.allFiles.concat(allFiles);
    allFiles.forEach((it) => (this.allFilesMap[it.localId] = it));
    this.saveToDB(allFiles);
    this.schedule();
    this.onChange();
  }

  public removeFiles(files: UploadFilesType) {
    const removedMap: Record<string, UploadFileType> = {};

    const trRemove = (files: UploadFileType[]) => {
      files.forEach((it) => {
        removedMap[it.localId] = it;
        if (it.children?.length) {
          trRemove(it.children);
        }
      });
    };

    (Array.isArray(files) ? files : [files]).forEach((it) => {
      const localId = typeof it === 'string' ? it : it.localId;
      const file = this.allFilesMap[localId];
      if (!file) {
        return;
      }
      removedMap[localId] = file;
      const parent = file.localParentId && this.allFilesMap[file.localParentId!];
      if (parent) {
        parent.children = parent.children?.filter((it) => it.localId !== localId);
      }
      file?.children?.length && trRemove(file.children);
    });

    const removedFiles = Object.values(removedMap);
    if (!removedFiles.length) {
      return;
    }

    this.rootFiles = this.rootFiles.filter((it) => !removedMap[it.localId]);
    this.allFiles = this.allFiles.filter((it) => !removedMap[it.localId]);
    removedFiles.forEach((it) => {
      delete this.allFilesMap[it.localId];
      if (
        it.type === FileTypeEnum.Folder &&
        it.uploadStatus === UploadStatusEnum.Uploaded &&
        it.finishStatus !== FinishStatusEnum.Finished &&
        !it.localParentId &&
        it.remoteId
      ) {
        if (it.bizType === BizTypeEnum.TenantLibrary) {
          cancelTenantUploadFolder(it.remoteId);
        } else {
          cancelMyUploadFolder(it.remoteId);
        }
      }
    });
    this.queue.forEach((it) => {
      if (removedMap[it.localId]) {
        it.abortController.abort();
      }
    });
    this.removeFromDB(removedFiles);

    const updatedFiles: UploadFileType[] = [];
    this.updateSize(this.rootFiles, updatedFiles);
    this.saveToDB(updatedFiles);

    this.onChange();

    return Promise.resolve();
  }

  private retryFilesRecursive(files: UploadFileType[], updatedFiles: UploadFileType[]) {
    files.forEach((file) => {
      if (file.uploadStatus === UploadStatusEnum.Failed) {
        file.uploadStatus = UploadStatusEnum.Waiting;
        file.retryCount = 0;
        updatedFiles.push(file);
      } else if (file.uploadStatus === UploadStatusEnum.Uploaded && file.children?.length) {
        this.retryFilesRecursive(file.children, updatedFiles);
      }
    });
  }

  public retryFiles(files: UploadFilesType) {
    const updatedFiles: UploadFileType[] = [];

    (Array.isArray(files) ? files : [files]).forEach((it) => {
      const localId = typeof it === 'string' ? it : it.localId;
      const file = this.allFilesMap[localId];
      if (!file) {
        return;
      }
      if (file.uploadStatus === UploadStatusEnum.Failed) {
        file.uploadStatus = UploadStatusEnum.Waiting;
        file.retryCount = 0;
        updatedFiles.push(file);
      } else if (file.finishStatus === FinishStatusEnum.Failed) {
        file.finishStatus = FinishStatusEnum.Waiting;
        file.retryCount = 0;
        updatedFiles.push(file);
      } else if (file.children?.length) {
        this.retryFilesRecursive(file.children, updatedFiles);
      }
    });

    if (!updatedFiles.length) {
      return;
    }

    this.saveToDB(updatedFiles);
    this.checkFinishRoots();
    this.schedule();

    return Promise.resolve();
  }

  public getFiles(): UploadFileType[] {
    return this.cloneFilesState(this.rootFiles).sort((l, r) => {
      const ret = (statusPriority[l.status] ?? 100) - (statusPriority[r.status] ?? 100);
      return ret ? ret : r.createTime - l.createTime;
    });
  }

  public getStat(): UploadStatType {
    const uploadFiles = this.getFiles();
    let uploadingCount = 0;
    let uploadedCount = 0;
    let uploadFailedCount = 0;
    uploadFiles.forEach((file) => {
      if (file.status === UploadStatusEnum.Waiting || file.status === UploadStatusEnum.Uploading) {
        uploadingCount++;
      } else if (file.status === UploadStatusEnum.Uploaded) {
        uploadedCount++;
      } else if (file.status === UploadStatusEnum.Failed) {
        uploadFailedCount++;
      }
    });
    return {
      uploadFiles,
      uploadingCount,
      uploadedCount,
      uploadFailedCount
    };
  }

  public destroy() {
    releaseCloudDB(this.db);
    this.queue.forEach((it) => it.abortController.abort());
    this.queue = [];
    this.rootFiles = [];
    this.allFiles = [];
    this.allFilesMap = {};
    this.onChangeListeners = [];
  }

  private updateSize(files: UploadFileType[], updatedFiles?: UploadFileType[]) {
    files.forEach((file) => {
      if (file.children?.length) {
        this.updateSize(file.children, updatedFiles);
        const size = file.children.reduce((acc, cur) => acc + cur.size, 0);
        if (size !== file.size) {
          file.size = size;
          updatedFiles?.push(file);
        }
      }
    });
    return updatedFiles;
  }

  private cloneFilesState(files: UploadFileType[]): UploadFileType[] {
    return files.map((file) => {
      const newFile: UploadFileType = { ...file, status: file.uploadStatus };

      if (file.children?.length) {
        newFile.children = this.cloneFilesState(file.children);

        if (newFile.status !== UploadStatusEnum.Failed) {
          for (let child of newFile.children) {
            if (child.status === UploadStatusEnum.Failed) {
              newFile.status = UploadStatusEnum.Failed;
              break;
            } else if (child.status === UploadStatusEnum.Uploading) {
              newFile.status = UploadStatusEnum.Uploading;
              break;
            } else if (child.status === UploadStatusEnum.Waiting) {
              newFile.status = UploadStatusEnum.Waiting;
            }
          }
        }
      }

      if (
        newFile.status === UploadStatusEnum.Uploaded &&
        newFile.type === FileTypeEnum.Folder &&
        !newFile.localParentId
      ) {
        if (newFile.finishStatus === FinishStatusEnum.Failed) {
          newFile.status = UploadStatusEnum.Failed;
        } else if (file.finishStatus !== FinishStatusEnum.Finished) {
          newFile.status = UploadStatusEnum.Uploading;
        }
      } else if (newFile.finishStatus && newFile.status !== UploadStatusEnum.Failed) {
        if (newFile.finishStatus === FinishStatusEnum.Finishing) {
          newFile.status = UploadStatusEnum.Uploading;
        } else if (newFile.finishStatus === FinishStatusEnum.Failed) {
          newFile.status = UploadStatusEnum.Failed;
        }
      }

      if (newFile.type === FileTypeEnum.File) {
        if (newFile.uploadStatus === UploadStatusEnum.Uploaded) {
          newFile.loadedSize = newFile.size;
        } else if (newFile.uploadStatus === UploadStatusEnum.Failed) {
          // 失败状态保留已上传的进度
          newFile.loadedSize = file.loadedSize;
        } else if (newFile.uploadStatus !== UploadStatusEnum.Uploading) {
          newFile.loadedSize = 0;
        }
      } else {
        // 文件夹的进度计算 - 这里可以添加进度百分比
        newFile.loadedSize = newFile.children?.reduce((acc, cur) => acc + cur.loadedSize, 0) ?? 0;
        // 添加进度百分比计算
        newFile.progress = newFile.size > 0 ? (newFile.loadedSize / newFile.size) * 100 : 0;
      }

      return newFile;
    });
  }

  private onChange() {
    if (this.onChangeListeners.length && !this.onChangePending) {
      this.onChangePending = true;
      Promise.resolve().then(() => {
        this.onChangePending = false;
        const stat = this.getStat();
        this.onChangeListeners.forEach((listener) => listener(stat));
      });
    }
  }

  private saveToDB(files: UploadFileType[]) {
    const dbFiles = files
      .filter((it) => this.allFilesMap[it.localId])
      .map((file) => {
        const newFile: UploadFileType = { ...file };
        delete newFile.children;
        delete (newFile as any).status;
        delete (newFile as any).loadedSize;
        return newFile;
      });
    this.db.saveUploadFiles(dbFiles);
  }

  private removeFromDB(files: UploadFileType[]) {
    this.db.removeUploadFiles(files);
  }

  private setDBFiles(dbFiles: DBUploadFileType[]) {
    const rootFiles: UploadFileType[] = [];
    const allFiles: UploadFileType[] = dbFiles.map((file) => ({
      ...file,
      children: file.type === FileTypeEnum.Folder ? [] : undefined,
      uploadStatus:
        file.uploadStatus === UploadStatusEnum.Uploading
          ? UploadStatusEnum.Waiting
          : file.uploadStatus,
      finishStatus:
        file.finishStatus === FinishStatusEnum.Finishing
          ? FinishStatusEnum.Waiting
          : file.finishStatus,
      status: UploadStatusEnum.Waiting,
      loadedSize: 0
    }));

    allFiles.forEach((file) => {
      const parent = file.localParentId
        ? allFiles.find((parent) => parent.localId === file.localParentId)
        : undefined;
      if (parent) {
        parent.children!.push(file);
      } else {
        rootFiles.push(file);
      }
    });

    this.rootFiles = rootFiles.concat(this.rootFiles);
    this.rootFiles.sort((l, r) => l.createTime - r.createTime);
    this.allFiles = allFiles.concat(this.allFiles);
    allFiles.forEach((it) => (this.allFilesMap[it.localId] = it));

    this.checkFinishRoots();
    this.schedule();
    this.onChange();
  }

  private checkFinishRoots() {
    this.rootFiles.forEach((it) => {
      if (!it.finishStatus || it.finishStatus === FinishStatusEnum.Waiting) {
        this.checkFinishRoot(it);
      } else if (it.finishStatus === FinishStatusEnum.Failed && it.retryCount <= retryLimit) {
        it.retryCount++;
        this.checkFinishRoot(it);
      }
    });
  }

  private schedule() {
    for (let i = this.queue.length; i < queueLimit; i++) {
      this.doSchedule()
        .then((res) => {
          this.schedule();
          return res;
        })
        .catch((err) => {
          debug && console.log('uploader error', err);
          if (err !== ScheduleErrorEnum.Empty) {
            this.schedule();
          }
          return err;
        });
    }
  }

  private findScheduleFile(
    files: UploadFileType[],
    failedFiles: UploadFileType[]
  ): UploadFileType | undefined {
    for (let file of files) {
      if (file.uploadStatus === UploadStatusEnum.Waiting) {
        return file;
      }

      if (file.uploadStatus === UploadStatusEnum.Failed) {
        failedFiles.push(file);
      } else if (file.uploadStatus === UploadStatusEnum.Uploaded && file.children?.length) {
        const next = this.findScheduleFile(file.children, failedFiles);
        if (next) {
          return next;
        }
      }
    }

    return undefined;
  }

  private doSchedule(): Promise<UploadFileType> {
    if (this.queue.length >= queueLimit) {
      return Promise.reject(ScheduleErrorEnum.Limited);
    }

    const failedFiles: UploadFileType[] = [];
    const file =
      this.findScheduleFile(this.rootFiles, failedFiles) ||
      failedFiles.sort((l, r) => l.retryCount - r.retryCount)?.[0];

    if (!file || file.retryCount > retryLimit) {
      return Promise.reject(ScheduleErrorEnum.Empty);
    }

    return this.doScheduleFile(file);
  }

  private doScheduleFile(file: UploadFileType): Promise<UploadFileType> {
    const remoteParentId =
      file.remoteParentId || (file.localParentId && this.allFilesMap[file.localParentId]?.remoteId);

    if (file.uploadStatus === UploadStatusEnum.Failed) {
      file.retryCount++;
    }

    file.uploadStatus = UploadStatusEnum.Uploading;
    file.loadedSize = 0;

    const task: UploadTaskType = {
      localId: file.localId,
      abortController: new AbortController()
    };

    this.queue.push(task);

    let req: Promise<any>;

    if (file.type === FileTypeEnum.File) {
      if (file.remoteOldId) {
        req = this.uploadFileWithResume(file).then(() => {
          const formData = new FormData();
          formData.append('file', file.file!, file.file!.name);
          formData.append('fileKey', file.fileKey!);
          formData.append('fileId', file.remoteOldId!);
          return updateUploadFile(formData, {
            signal: task.abortController.signal
          });
        });
      } else {
        req = this.uploadFileWithResume(file).then(() => {
          const formData = new FormData();
          formData.append('fileKey', file.fileKey!);
          formData.append('bizType', file.bizType + '');
          formData.append('folderId', remoteParentId || '');
          formData.append('isAuditRoot', file.localParentId ? '0' : '1');

          return remoteParentId
            ? uploadFile(formData, {
                signal: task.abortController.signal
              })
            : Promise.reject();
        });
      }
    } else if (file.bizType === BizTypeEnum.TenantLibrary) {
      req = remoteParentId
        ? createTenantUploadFolder({
            parentId: remoteParentId,
            spaceName: file.name,
            isFirst: file.localParentId ? 0 : 1,
            isAuditRoot: file.localParentId ? 0 : 1
          }).then((res) => {
            file.remoteId = String(res);
          })
        : Promise.reject();
    } else {
      req = remoteParentId
        ? createMyUploadFolder({
            parentId: remoteParentId,
            folderName: file.name,
            isFirst: file.localParentId ? 0 : 1
          }).then((res) => {
            file.remoteId = String(res);
          })
        : Promise.reject();
    }

    this.onChange();

    return req
      .then(() => {
        file.uploadStatus = UploadStatusEnum.Uploaded;
        file.updateTime = Date.now();
        delete file.file;
        this.queue = this.queue.filter((it) => it !== task);
        this.checkFinishRoot(file);
        this.saveToDB([file]);
        this.onChange();
        return file;
      })
      .catch((err) => {
        debug && console.log('cloud upload error', err, file);
        file.uploadStatus = UploadStatusEnum.Failed;
        file.updateTime = Date.now();
        this.queue = this.queue.filter((it) => it !== task);
        this.saveToDB([file]);
        this.onChange();
        return Promise.reject(ScheduleErrorEnum.Error);
      });
  }

  private getStatusForFinish(file: UploadFileType) {
    let status = file.uploadStatus;
    if (file.children?.length) {
      for (let child of file.children) {
        const childStatus = this.getStatusForFinish(child);
        if (childStatus === UploadStatusEnum.Failed) {
          status = UploadStatusEnum.Failed;
          break;
        } else if (childStatus === UploadStatusEnum.Uploading) {
          status = UploadStatusEnum.Uploading;
        } else if (childStatus === UploadStatusEnum.Waiting) {
          status = UploadStatusEnum.Waiting;
        }
      }
    }
    return status;
  }

  private checkFinishRoot(file: UploadFileType) {
    let rootFile = file;
    while (rootFile.localParentId) {
      const next = this.allFilesMap[rootFile.localParentId];
      if (!next) {
        break;
      }
      rootFile = next;
    }

    if (rootFile.type !== FileTypeEnum.Folder) {
      return;
    }

    if (this.getStatusForFinish(rootFile) !== UploadStatusEnum.Uploaded) {
      return;
    }

    rootFile.finishStatus = FinishStatusEnum.Finishing;

    let req: Promise<any>;
    if (rootFile.bizType === BizTypeEnum.TenantLibrary) {
      req = finishTenantUploadFolder({
        id: rootFile.remoteId!,
        isUpdate: rootFile.remoteOldId ? 1 : 0,
        oldFolderId: rootFile.remoteOldId
      });
    } else {
      req = finishMyUploadFolder({
        id: rootFile.remoteId!,
        isUpdate: rootFile.remoteOldId ? 1 : 0,
        oldFolderId: rootFile.remoteOldId
      });
    }

    this.onChange();

    req
      .then(() => {
        rootFile.finishStatus = FinishStatusEnum.Finished;
      })
      .catch(() => {
        rootFile.finishStatus = FinishStatusEnum.Failed;
      })
      .finally(() => {
        this.onChange();
        this.saveToDB([rootFile]);
      });
  }

  async uploadFileWithResume(file: UploadFileType): Promise<void> {
    const PART_SIZE = 5 * 1024 * 1024; // 5MB 分片大小
    const MAX_RETRIES = 3;
    const task = this.queue.find((t) => t.localId === file.localId);
    console.log('this.queue', this.queue);
    if (!task) {
      throw new Error('上传任务不存在');
    }
    const { abortController } = task;

    try {
      let fileKey: string;

      // 1. 如果是重试上传，直接获取已有的 fileKey
      if (file.fileKey) {
        fileKey = file.fileKey;

        // 获取已上传的分片信息
        const { exitPartList } = await getUploadTaskInfo({ fileKey });
        // 计算已上传的大小
        const uploadedSize = exitPartList.reduce((sum, part) => sum + part.size, 0);
        file.loadedSize = uploadedSize;
      } else {
        // 首次上传，需要初始化任务
        const initResult = await initUploadTask({
          fileName: file.file!.name
        });
        fileKey = initResult.fileKey;
        file.fileKey = fileKey;
      }

      file.uploadStatus = UploadStatusEnum.Uploading;
      this.onChange();

      // 2. 计算分片信息 - 确保至少有一个分片
      const totalParts = Math.max(1, Math.ceil(file.file!.size / PART_SIZE));

      // 3. 获取已上传的分片列表
      const { exitPartList } = await getUploadTaskInfo({ fileKey });
      const uploadedParts = new Set(exitPartList.map((part) => part.partNumber));

      // 4. 继续上传未完成的分片
      for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
        if (uploadedParts.has(partNumber)) {
          continue;
        }

        const start = (partNumber - 1) * PART_SIZE;
        const end = Math.min(start + PART_SIZE, file.file!.size);
        const chunk = file.file!.slice(start, end);

        let retries = 0;
        while (retries < MAX_RETRIES) {
          try {
            const formData = new FormData();
            // 即使是空文件也创建一个空的 File 对象
            formData.append('sliceFile', new File([chunk], file.file!.name));
            formData.append('slice', partNumber.toString());
            formData.append('fileKey', fileKey);

            await uploadPart(formData, {
              signal: abortController.signal, // 传入 signal 以支持取消
              onUploadProgress: (progressEvent) => {
                // 计算当前分片的进度
                const previousPartsSize = (partNumber - 1) * PART_SIZE;
                const currentProgress = previousPartsSize + progressEvent.loaded;
                file.loadedSize = Math.min(currentProgress, file.file!.size);

                // 计算总进度百分比
                const progress = (file.loadedSize / file.file!.size) * 100;
                file.progress = progress;
                console.log(`Upload progress: ${progress.toFixed(2)}%`);
                this.onChange();
              }
            });

            break;
          } catch (error) {
            // 检查是否是取消操作导致的错误
            if (abortController.signal.aborted) {
              console.log('上传已取消');
              // 重置文件状态
              file.uploadStatus = UploadStatusEnum.Failed;
              this.onChange();
              this.saveToDB([file]);
              return;
            }

            retries++;

            // 检查分片是否已上传成功
            const { exitPartList, finished } = await getUploadTaskInfo({ fileKey });
            if (exitPartList.some((part) => part.partNumber === partNumber)) {
              break; // 该分片已上传成功，继续下一个
            }

            if (retries === MAX_RETRIES) {
              const currentProgress = file.loadedSize;
              throw { error: error, currentProgress };
            }
            await new Promise((resolve) => setTimeout(resolve, 1000 * retries));
          }
        }
      }

      // 5. 合并文件
      await mergeFileParts({
        fileKey,
        fileName: file.file!.name
      });

      file.uploadStatus = UploadStatusEnum.Uploaded;
      file.loadedSize = file.file!.size;
      this.onChange();
      this.saveToDB([file]);
    } catch (error) {
      console.error('上传失败:', error);

      // 在标记为失败之前保存当前进度
      const currentProgress = file.loadedSize;

      // 批量更新状态，避免多次触发更新
      const updates = {
        uploadStatus: UploadStatusEnum.Failed,
        loadedSize: currentProgress,
        updateTime: Date.now(),
        progress: (file.loadedSize / file.file!.size) * 100
      };
      Object.assign(file, updates);
      // 只触发一次更新和保存
      this.onChange();
      this.saveToDB([file]);
      throw error;
    }
  }
}
