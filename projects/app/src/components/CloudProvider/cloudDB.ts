import { DBUploadFileType } from './type';

enum DBStatusEnum {
  Created,
  Initing,
  Inited,
  Deinited,
  Failed
}

const debug = true;

const dbName = 'cloud';

const version = 12;

const uploadFileStoreName = 'uploadFile';

export class CloudDB {
  private refCount: number;

  private status: DBStatusEnum;

  private db?: IDBDatabase;

  private initQueue: ((ok: boolean) => void)[];

  constructor() {
    this.refCount = 1;
    this.status = DBStatusEnum.Created;
    this.initQueue = [];

    const w = typeof window !== 'undefined' ? (window as any) : undefined;
    if (w) {
      if (!w.indexedDB) {
        w.indexedDB = w.mozIndexedDB || w.webkitIndexedDB || w.msIndexedDB;
      }
      if (!w.IDBTransaction) {
        w.IDBTransaction = w.webkitIDBTransaction || w.msIDBTransaction;
      }
      if (!w.IDBKeyRange) {
        w.IDBKeyRange = w.webkitIDBKeyRange || w.msIDBKeyRange;
      }
      this.init();
    }
  }

  private init(): Promise<unknown> {
    if (this.status !== DBStatusEnum.Created) {
      return Promise.reject();
    }
    if (!window.indexedDB) {
      this.status = DBStatusEnum.Failed;
      return Promise.reject();
    }
    this.status = DBStatusEnum.Initing;

    return new Promise((resolve, reject) => {
      const request = window.indexedDB.open(dbName, version);

      request.onupgradeneeded = (event: any) => {
        const db = event.target.result;
        db.objectStoreNames.contains(uploadFileStoreName) &&
          db.deleteObjectStore(uploadFileStoreName);
        db.createObjectStore(uploadFileStoreName, { keyPath: 'localId' });
      };

      request.onerror = (event: any) => {
        this.status = DBStatusEnum.Failed;
        reject(event.target.errorCode);
      };

      request.onsuccess = (event: any) => {
        const db = event.target.result as IDBDatabase;
        if (this.status !== DBStatusEnum.Initing) {
          db.close();
          reject(null);
          this.initQueue.splice(0).forEach((it) => it(false));
        } else {
          this.status = DBStatusEnum.Inited;
          this.db = db;
          resolve(null);
          this.initQueue.splice(0).forEach((it) => it(true));
        }
      };
    });
  }

  private deinit() {
    if (this.status === DBStatusEnum.Deinited) {
      return;
    }
    this.status = DBStatusEnum.Deinited;
    this.db?.close();
    this.db = undefined;
  }

  retain() {
    this.refCount++;
    return this;
  }

  release() {
    if (this.refCount > 0 && --this.refCount === 0) {
      this.deinit();
      return true;
    }
    return false;
  }

  getUploadFiles(tmbId: string): Promise<DBUploadFileType[]> {
    if (this.status === DBStatusEnum.Initing) {
      return new Promise((resolve, reject) => {
        this.initQueue.push((ok) => {
          return ok ? resolve(this.getUploadFiles(tmbId)) : reject();
        });
      });
    }

    if (!this.db) {
      return Promise.reject();
    }

    const transaction = this.db.transaction([uploadFileStoreName]);
    const store = transaction.objectStore(uploadFileStoreName);
    const request = store.getAll();

    return new Promise((resolve, reject) => {
      request.onsuccess = (event: any) => {
        resolve((event.target.result as DBUploadFileType[]).filter((it) => it.tmbId === tmbId));
      };

      request.onerror = () => {
        reject();
      };
    });
  }

  saveUploadFiles(files: DBUploadFileType[]): Promise<unknown> {
    if (!files.length) {
      return Promise.reject();
    }

    if (this.status === DBStatusEnum.Initing) {
      return new Promise((resolve, reject) => {
        this.initQueue.push((ok) => {
          return ok ? resolve(this.saveUploadFiles(files)) : reject();
        });
      });
    }

    if (!this.db) {
      return Promise.reject();
    }

    const transaction = this.db.transaction([uploadFileStoreName], 'readwrite');
    const store = transaction.objectStore(uploadFileStoreName);
    files.forEach((it) => store.put({ ...it }));
    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => {
        resolve(undefined);
      };

      transaction.onerror = (event) => {
        debug && console.error('FileDB transaction onerror', event);
        reject();
      };
    });
  }

  removeUploadFiles(files: DBUploadFileType[]): Promise<unknown> {
    if (!files.length) {
      return Promise.reject();
    }

    if (this.status === DBStatusEnum.Initing) {
      return new Promise((resolve, reject) => {
        this.initQueue.push((ok) => {
          return ok ? resolve(this.removeUploadFiles(files)) : reject();
        });
      });
    }

    if (!this.db) {
      return Promise.reject();
    }
    const transaction = this.db.transaction([uploadFileStoreName], 'readwrite');
    const store = transaction.objectStore(uploadFileStoreName);
    files.forEach((it) => store.delete(it.localId));
    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => {
        resolve(undefined);
      };
      transaction.onerror = (event) => {
        debug && console.error('remove db files error', event);
        reject();
      };
    });
  }
}

let instance: CloudDB | undefined;

export const obtainCloudDB = () => {
  if (instance) {
    instance.retain();
  } else {
    instance = new CloudDB();
  }
  return instance;
};

export const releaseCloudDB = (fileDB: CloudDB) => {
  if (fileDB === instance && instance?.release()) {
    instance = undefined;
  }
};
