import { useRouter } from 'next/router';
import { CreateSignatureOptions, FastGPTOptions } from './type';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { createFullUrl, createSignature, postMessageTo, receiveMessageFrom } from './utils';
import { MessageTypeEnum } from './constants';
import { Box, BoxProps } from '@chakra-ui/react';

const FastGPT = ({ options, ...props }: { options: FastGPTOptions } & BoxProps) => {
  const router = useRouter();
  const iFrameRef = useRef<HTMLIFrameElement | null>(null);

  // 发消息签名
  const signature = useMemo(
    () => createSignature({ ...(options as CreateSignatureOptions) }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [options.token, options.pageType, options.authType]
  );

  // 目标页面地址
  const src = useMemo(
    () => createFullUrl(options),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      options.url,
      options.token,
      options.authType,
      options.pageType,
      options.huayunToken,
      options.parentOrigin
    ]
  );

  const postMessage = useCallback(
    (type: MessageTypeEnum, data?: any) => {
      iFrameRef.current?.contentWindow &&
        signature &&
        options.url &&
        postMessageTo(iFrameRef.current.contentWindow, signature, type, data, options.url);
    },
    [signature, options.url]
  );

  const stateRef = useRef({ signature, options });

  useEffect(() => {
    stateRef.current = { signature, options };

    const handleMessage = receiveMessageFrom(stateRef.current, ({ type, data }) => {
      const {
        onBack,
        onNavTo,
        onOpenSelectDataset,
        onOpenSelectApp,
        onOpenSelectCloudFile,
        onLogout,
        onEditSimpleApp,
        onGetSimpleAppTemplate
      } = stateRef.current.options;
      if (type === MessageTypeEnum.back) {
        onBack ? onBack() : router.back();
      }

      if (type === MessageTypeEnum.navTo) {
        onNavTo ? onNavTo(data) : router.push(data);
      }

      if (type === MessageTypeEnum.logout) {
        onLogout?.();
      }

      // 选择知识库
      if (type === MessageTypeEnum.openSelectDataset) {
        onOpenSelectDataset?.(data)
          .then((res) => {
            postMessage(MessageTypeEnum.selectDataset, res);
          })
          .then((err) => {
            postMessage(MessageTypeEnum.selectDataset, err);
          });
      }

      // 选择应用
      if (type === MessageTypeEnum.openSelectApp) {
        onOpenSelectApp?.()
          .then((res) => {
            postMessage(MessageTypeEnum.selectApp, res);
          })
          .then((err) => {
            postMessage(MessageTypeEnum.selectApp, err);
          });
      }

      // 选择云文件
      if (type === MessageTypeEnum.openSelectCloudFile) {
        onOpenSelectCloudFile?.(data)
          .then((res) => {
            postMessage(MessageTypeEnum.selectCloudFile, res);
          })
          .catch((err) => {
            postMessage(MessageTypeEnum.selectCloudFile, err);
          });
      }

      // 编辑简单应用
      if (type === MessageTypeEnum.editSimpleApp) {
        onEditSimpleApp?.(data)
          .then((res) => {
            postMessage(MessageTypeEnum.editSimpleAppSuccess, res);
          })
          .catch((err) => {
            postMessage(MessageTypeEnum.editSimpleAppSuccess, err);
          });
      }

      // 获取一键创建简易应用模版
      if (type === MessageTypeEnum.getSimpleAppTemplate) {
        onGetSimpleAppTemplate?.(data)
          .then((res) => {
            postMessage(MessageTypeEnum.getSimpleAppTemplateSuccess, res);
          })
          .catch((err) => {
            postMessage(MessageTypeEnum.getSimpleAppTemplateSuccess, err);
          });
      }
    });

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [postMessage, router]);

  return src ? (
    <Box
      w="100%"
      h="100%"
      {...props}
      ref={iFrameRef}
      as="iframe"
      src={src}
      onLoad={() => {
        console.log('iFrame loaded');
      }}
    />
  ) : null;
};

export default FastGPT;
