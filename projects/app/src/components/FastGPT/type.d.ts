import { selectDatasetParams } from '@/types/pages/dataset';
import { AuthTypeEnum, MessageTypeEnum, PageTypeEnum } from './constants';

export type MessageType = {
  signature: string;
  type: MessageTypeEnum;
  data: any;
};

export type CreateSignatureOptions = {
  pageType: PageTypeEnum;
  authType: AuthTypeEnum;
  token: string;
};

export type AppDetailOptions = {
  pageType: PageTypeEnum.appDetail;
  appId: string;
};

export type DatasetDetailOptions = {
  pageType: PageTypeEnum.datasetDetail;
  datasetId: string;
};

export type AppTemplateOptions = {
  name: string;
  intro: string;
  avatar: string;
  prompt: string;
  modelChoice: {
    model: string;
    modelReason: string;
    temperature: string;
    temperatureReason: string;
  };
  opening: string;

  toolList: {
    toolId: string;
    toolName: string;
    purpose: string;
  }[];
  voiceChoice: {
    voice: string;
    voiceReason: string;
  };
};

export type FastGPTOptions = {
  url?: string;
  token?: string;
  authType: AuthTypeEnum;
  source?: DataSource;
  query?: string;
  huayunToken?: string;
  parentOrigin?: string;
  onBack?: () => void;
  onNavTo?: (url: string) => void;
  onLogout?: () => void;
  onOpenSelectDataset?: (data: selectDatasetParams) => Promise<any>;
  onOpenSelectApp?: () => Promise<any>;
  onOpenSelectCloudFile?: (data: CloudChooseProps) => Promise<any>;
  onEditSimpleApp?: (data: any) => Promise<any>;
  onGetSimpleAppTemplate?: (data: any) => Promise<AppTemplateOptions | null>;
} & (AppDetailOptions | DatasetDetailOptions);

export enum DataSource {
  Tenant = 1,
  Offical = 2,
  Personal = 3
}

export type FastGPTRef = {};
