// 嵌入页面类型
export enum PageTypeEnum {
  // 应用详情页
  appDetail = 'appDetail',
  // 知识库详情页
  datasetDetail = 'datasetDetail',
  // 无效值
  none = ''
}

export const validPageTypeMap = {
  [PageTypeEnum.appDetail]: true,
  [PageTypeEnum.datasetDetail]: true,
  [PageTypeEnum.none]: false
};

// 页面授权类型
export enum AuthTypeEnum {
  // 用户端嵌入页面
  user = 'user',
  // 管理端嵌入页面
  admin = 'admin',
  // 无效值
  none = ''
}

export const validAuthTypeMap = {
  [AuthTypeEnum.user]: true,
  [AuthTypeEnum.admin]: true,
  [AuthTypeEnum.none]: false
};

// 消息类型
export enum MessageTypeEnum {
  back = 'back',
  navTo = 'navTo',
  logout = 'logout',

  openSelectDataset = 'openSelectDataset',
  selectDataset = 'selectDataset',

  openSelectApp = 'openSelectApp',
  selectApp = 'selectApp',
  openSelectCloudFile = 'openSelectCloudFile',
  selectCloudFile = 'selectCloudFile',

  // 编辑应用
  editSimpleApp = 'editSimpleApp',
  editSimpleAppSuccess = 'editSimpleAppSuccess',

  // 获取简易应用模版
  getSimpleAppTemplate = 'getSimpleAppTemplate',
  getSimpleAppTemplateSuccess = 'getSimpleAppTemplateSuccess'
}
