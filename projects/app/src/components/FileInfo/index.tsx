import { respDims } from '@/utils/chakra';
import { formatFileSize } from '@/utils/tools';
import { Flex, Box, Text, FlexProps } from '@chakra-ui/react';
import FileIcon from '../FileIcon';
import { FileMetaType } from '@/types/api/file';
import useFilePreview from '@/hooks/useFilePreview';
const FileInfo = ({
  fileInfo,
  isPreview = false,
  ...props
}: { fileInfo: FileMetaType } & FlexProps & { isPreview?: boolean }) => {
  const { previewFile, OverlayContainer } = useFilePreview();
  const handlePreview = (fileInfo: FileMetaType) => {
    previewFile({
      fileType: 1,
      fileUrl: fileInfo.fileUrl,
      fileKey: fileInfo.fileKey,
      useModal: true,
      showModalImmediate: true
    });
  };
  return (
    <Flex
      border="1px solid #E5E7EB"
      borderRadius="8px"
      position="relative"
      px={respDims(12)}
      py={respDims(10)}
      alignItems="center"
      onClick={() => {
        console.log('isPreview', isPreview);
        if (isPreview) {
          handlePreview(fileInfo);
        }
      }}
      w={respDims(260, 200)}
      {...props}
      overflow="hidden"
      {...(isPreview ? { cursor: 'pointer' } : {})}
    >
      <OverlayContainer></OverlayContainer>

      <FileIcon fileName={fileInfo.fileName} w={respDims(50, 40)} h={respDims(50, 40)}></FileIcon>
      <Box flex="1" ml={respDims(10, 8)} overflow="hidden">
        <Box
          fontSize={respDims(15, 13)}
          color="#1D2129"
          className={'textEllipsis'}
          flex={'1 0 0'}
          mb={1}
        >
          {fileInfo.fileName}
        </Box>
        <Flex w="100%" align="center" color="#A8ABB2" fontSize="12px">
          <Text>{` ${fileInfo.fileName.split('.').pop()}，`}</Text>
          <Text>{formatFileSize(fileInfo.fileSize)}</Text>
        </Flex>
      </Box>
    </Flex>
  );
};

export default FileInfo;
