import { useOverlayManager } from '@/hooks/useOverlayManager';
import SimpleModal from '@/components/AppModal/simple';
import { getAppDetail } from '@/api/app';

export const useEditSimpleAppModal = () => {
  const { openOverlay } = useOverlayManager();
  const open = async (appId: string) => {
    const appDetail = await getAppDetail(appId, false);
    const res = await new Promise((resolve, reject) => {
      openOverlay({
        Overlay: SimpleModal,
        props: {
          isCopy: false,
          appModalParams: {
            ...appDetail
          },
          onSuccess: () => {
            resolve?.('success');
          }
        }
      });
    });
    return res;
  };
  return { open };
};
