import { cloudFilePdf2txtObtainUrl, downloadFile } from '@/api/file';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import Chooser, { CloudChooseProps, ModalModeEnum } from '@/pages/cloud/list/components/Chooser';

// text,docx,csv,xlsx,pdf,md,html,ppt
const mimeTypeMap: { [key: string]: string } = {
  txt: 'text/plain',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  pdf: 'application/pdf',
  md: 'text/markdown',
  html: 'text/html',
  ppt: 'application/vnd.ms-powerpoint'
};

export const useCloudSelect = () => {
  const { openOverlay } = useOverlayManager();
  const open = (data: CloudChooseProps) => {
    console.log(data);

    return new Promise((resolve, reject) => {
      openOverlay({
        Overlay: Chooser,
        props: {
          ...data,
          modalMode: ModalModeEnum.Choose,
          onSuccess: async (files, inputFileName) => {
            const fileList: File[] = [];
            for (const file of files) {
              let extensionQuery = file.fileName?.split('.').pop()?.toLowerCase() || '';
              let pdf2txtFileName = '';
              let fileKeyToDownload = file.fileKey;
              if (extensionQuery === 'pdf') {
                const res = await cloudFilePdf2txtObtainUrl(file.fileId);
                if (res && res.fileKey) {
                  fileKeyToDownload = res.fileKey;
                  pdf2txtFileName = res.fileName;
                } else {
                  return;
                }
              }
              const res: any = await downloadFile(fileKeyToDownload);
              const fileName = extensionQuery === 'pdf' ? pdf2txtFileName : file.fileName;
              const blob = res.data;
              const rawFile = new File([blob], fileName, {
                type: mimeTypeMap[extensionQuery === 'pdf' ? 'txt' : extensionQuery]
              });
              fileList.push(rawFile);
            }
            resolve(fileList);
          },
          onCloseCustom: () => {
            reject?.('canceled');
          }
        }
      });
    });
  };
  return { open };
};
