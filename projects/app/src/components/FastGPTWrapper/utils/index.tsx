import { IsStructuredPrompt } from '@/constants/api/app';
import { ChatCompletionRequestMessageRoleEnum } from '@/fastgpt/global/core/ai/constants';
import { streamFetch } from '@/utils/fetch';
import { Toast } from '@/utils/ui/toast';
import { nanoid } from 'nanoid';

export const onOptimizePrompt = async (prompt: Record<string, any>) => {
  const {
    name,
    description,
    promptList = [],
    isStructuredPrompt,
    unstructuredPrompt
  } = prompt || {};
  const templates = `智能体名称：${name}
智能体介绍：${description}`;
  const { responseText: promtText } = await streamFetch({
    url: '/huayun-ai/system/fast/structuredPrompt',
    data: {
      messages: [
        {
          dataId: nanoid(),
          role: ChatCompletionRequestMessageRoleEnum.User,
          content: templates
        }
      ],
      chatId: nanoid()
    },
    onMessage: () => {},
    abortCtrl: new AbortController()
  });
  return promtText;
};
