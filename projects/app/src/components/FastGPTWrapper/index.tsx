import { BoxProps } from '@chakra-ui/react';
import FastGPT from '../FastGPT';
import { AppTemplateOptions, FastGPTOptions } from '../FastGPT/type';
import { useSystemStore } from '@/store/useSystemStore';
import { useQuery } from '@tanstack/react-query';
import { respDims } from '@/utils/chakra';
import { selectDatasetParams } from '@/types/pages/dataset';
import useDatasetSelect from '@/pages/dataset/list/hooks/useDatasetSelect';
import useAppSelect from '../AppDetail/components/AdvanceApp/hooks/useAppSelect';
import { useRouter } from 'next/router';
import { CloudChooseProps } from '@/pages/cloud/list/components/Chooser';
import { useCloudSelect } from './hooks/cloud';
import { useMemo } from 'react';
import { AppListItemType } from '@/types/api/app';
import { onOptimizePrompt } from './utils';
import { clearToken } from '@/utils/auth';
import { useEditSimpleAppModal } from './hooks/app';
const FastGPTWrapper = ({
  fullscreen,
  options,
  ...props
}: { fullscreen?: boolean; options: FastGPTOptions } & BoxProps) => {
  const { getFastGPTData } = useSystemStore();
  const router = useRouter();
  const { data } = useQuery(['fastGPTData'], () => getFastGPTData(true));
  const { open: openSelectDatasetModal } = useDatasetSelect();
  const { open: onSelectAppModal } = useAppSelect();
  const { open: openSelectCloudFileModal } = useCloudSelect();
  const appDetail = useMemo(() => {
    try {
      return JSON.parse(decodeURIComponent(router.query.appDetail as string)) as AppListItemType;
    } catch (error) {
      return {} as AppListItemType;
    }
  }, [router.query]);
  const { open: openEditSimpleAppModal } = useEditSimpleAppModal();
  const onOpenSelectDataset = (data: selectDatasetParams) => {
    return openSelectDatasetModal(data);
  };

  const onOpenSelectApp = () => {
    return onSelectAppModal();
  };

  const onOpenSelectCloudFile = (data: CloudChooseProps) => {
    return openSelectCloudFileModal(data);
  };

  const onNavTo = (url: string) => {
    // 新版本更新路径
    if (url === '/dataset/list') {
      return router.back();
    }
    router.push(url);
  };

  const onLogout = () => {
    clearToken();
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  };

  const onEditSimpleApp = async (data: any) => {
    return openEditSimpleAppModal(appDetail.id);
  };

  const onGetSimpleAppTemplate = async ({ finalAppId }: { finalAppId: string }) => {
    const localstorageData = localStorage.getItem(`simpleAppTemplate-${finalAppId}`);
    if (localstorageData) {
      localStorage.removeItem(`simpleAppTemplate-${finalAppId}`);
      return JSON.parse(localstorageData) as AppTemplateOptions;
      // {
      //   name: '21',
      //   intro: '21',
      //   avatar: '21',
      //   prompt: '晚上模版提示词',
      //   modelChoice: {
      //     model: 'gpt-4o-mini',
      //     modelReason: '21',
      //     temperature: '7',
      //     temperatureReason: '21'
      //   },
      //   opening: '我是模版输入欢迎词',
      //   toolList: [
      //     {
      //       toolId: '66b2e634c4e5583694efcf21',
      //       toolName: 'test',
      //       purpose: 'test'
      //     },
      //     {
      //       toolId: '67db9aa3112ada70e35bb9ae',
      //       toolName: '联网搜索',
      //       purpose: 'test'
      //     }
      //   ],
      //   voiceChoice: {
      //     voice: 'nova',
      //     voiceReason: '21'
      //   }
      // } as AppTemplateOptions;
    }
    return null;
  };

  return (
    <FastGPT
      options={{
        // url: 'http://localhost:3000',
        // token:
        //   'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NjlmOGJjN2FiMTFiZWJiMjg5ODBlMmYiLCJ0ZWFtSWQiOiI2NjlmOGJjN2FiMTFiZWJiMjg5ODBlNDEiLCJ0bWJJZCI6IjY2OWY4YmM3YWIxMWJlYmIyODk4MGU0OCIsImlzUm9vdCI6dHJ1ZSwiZXhwIjoxNzQzMDYzODUxLCJpYXQiOjE3NDI0NTkwNTF9.igZ99OOxX0lH4QXeM-0SXzY3AgMQe-AKH6evnbcQ8A8',
        url: data?.domain,
        token: data?.token,
        ...options,
        onOpenSelectDataset,
        onOpenSelectApp,
        onOpenSelectCloudFile,
        onNavTo,
        onLogout,
        onEditSimpleApp: onEditSimpleApp,
        onGetSimpleAppTemplate: onGetSimpleAppTemplate
      }}
      {...(fullscreen
        ? {
            pos: 'fixed',
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
            zIndex: 1001
          }
        : {})}
      {...props}
    />
  );
};

export default FastGPTWrapper;
