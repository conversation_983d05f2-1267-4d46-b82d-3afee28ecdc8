import { useEffect, useRef, useState } from 'react';
import { Button } from 'antd';
import regression from 'regression';
import { Card, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';

interface RegressionResult {
  points: [number, number][];
  predict: (x: number) => [number, number];
  equation: number[];
  string: string;
  r2: number;
}

const jsxgraphStyle = `
.jxgbox {
  touch-action: none;
  -ms-touch-action: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;
  overflow: hidden;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin: 0;
}
`;

interface Point {
  x: number;
  y: number;
}

export const InteractiveFitting: React.FC = () => {
  const boardRef = useRef<HTMLDivElement>(null);
  const [points, setPoints] = useState<Point[]>([]);
  const [board, setBoard] = useState<any>(null);
  const [regressionType, setRegressionType] = useState<'linear' | 'quadratic' | 'cubic'>('linear');

  useEffect(() => {
    const loadJSXGraph = async () => {
      const JXG = await import('jsxgraph');
      if (boardRef.current) {
        const board = JXG.JSXGraph.initBoard(boardRef.current, {
          boundingbox: [-5, 5, 5, -5],
          axis: true,
          showCopyright: false
        });

        board.on('down', (e: any) => {
          const coords = board.getUsrCoordsOfMouse(e);
          const point = board.create('point', coords, { fixed: false });
          setPoints((prev) => [...prev, { x: coords[0], y: coords[1] }]);
        });

        setBoard(board);
      }
    };

    const style = document.createElement('style');
    style.textContent = jsxgraphStyle;
    document.head.appendChild(style);

    loadJSXGraph();
    return () => {
      style.remove();
    };
  }, [boardRef]);

  const fitCurve = () => {
    if (!board) return;

    // 清除之前的拟合曲线
    board.removeObject('fitCurve');

    const data = points.map((p) => [p.x, p.y] as [number, number]);
    let result: RegressionResult;

    switch (regressionType) {
      case 'linear':
        result = regression.linear(data);
        board.create('functiongraph', [(x: number) => result.predict(x)[1]], { name: 'fitCurve' });
        break;
      case 'quadratic':
        result = regression.polynomial(data, { order: 2 });
        board.create('functiongraph', [(x: number) => result.predict(x)[1]], { name: 'fitCurve' });
        break;
      case 'cubic':
        result = regression.polynomial(data, { order: 3 });
        board.create('functiongraph', [(x: number) => result.predict(x)[1]], { name: 'fitCurve' });
        break;
    }
  };

  return (
    <>
      <Card w={respDims(760, 180, 100)}>
        <div
          ref={boardRef}
          style={{ width: '100%', height: '500px', marginBottom: '16px' }}
          className="jxgbox"
        />
      </Card>
      <Flex alignItems="center" justifyContent="space-between">
        <Button.Group style={{ marginTop: '16px' }}>
          <Button
            onClick={() => setRegressionType('linear')}
            type={regressionType === 'linear' ? 'primary' : 'default'}
          >
            一次拟合
          </Button>
          <Button
            onClick={() => setRegressionType('quadratic')}
            type={regressionType === 'quadratic' ? 'primary' : 'default'}
          >
            二次拟合
          </Button>
          <Button
            onClick={() => setRegressionType('cubic')}
            type={regressionType === 'cubic' ? 'primary' : 'default'}
          >
            三次拟合
          </Button>
        </Button.Group>
        <Button type="primary" onClick={fitCurve} style={{ marginLeft: '16px', marginTop: '16px' }}>
          执行拟合
        </Button>
      </Flex>
    </>
  );
};
