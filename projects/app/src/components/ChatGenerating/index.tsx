import { Box, Flex } from '@chakra-ui/react';
import <PERSON><PERSON> from '../<PERSON><PERSON>';
import { respDims } from '@/utils/chakra';

const ChatGenerating = () => {
  return (
    <Flex alignItems="center">
      <Lottie
        name="chating"
        w={respDims(`${(130 / 3) * 2}rpx`, 130 / 3)}
        h={respDims(`${(45 / 3) * 2}rpx`, 45 / 3)}
      />
      <Box ml={respDims('12rpx', 6)} color="#606266" fontSize={respDims('28rpx', '14fpx')}>
        生成中...
      </Box>
    </Flex>
  );
};

export default ChatGenerating;
