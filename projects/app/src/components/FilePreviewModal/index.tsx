import React from 'react';
import { Modal } from 'antd';
import CustomAudioPlayer from '@/components/CustomAudioPlayer/index';
import { Box } from '@chakra-ui/react';
import MyModal from '../MyModal';
import { respDims } from '@/utils/chakra';

interface FilePreviewModalProps {
  visible: boolean;
  onClose: () => void;
  iframeSrc: string;
  title: string;
}

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({
  visible,
  onClose,
  iframeSrc,
  title
}) => {
  // 判断是否为音频文件
  const isAudioFile = () => {
    const audioExtensions = ['.mp3', '.m4a', '.wma', '.aac', '.ogg', '.amr', '.flac']; // 支持的音频格式
    const fileExtension = title.split('.').pop()?.toLowerCase(); // 获取文件扩展名
    return fileExtension && audioExtensions.includes(`.${fileExtension}`);
  };

  // 动态设置 iframe 高度

  return (
    <MyModal
      title={title}
      isOpen={visible}
      onClose={onClose}
      isCentered
      minWidth={respDims(1200, 850)}
    >
      {isAudioFile() ? (
        <Box h="18vh" display="flex" alignItems="center">
          <CustomAudioPlayer src={iframeSrc} />
        </Box>
      ) : (
        <iframe
          src={iframeSrc}
          style={{ width: '100%', height: '80vh', border: 'none' }}
          title={title}
        />
      )}
    </MyModal>
  );
};

export default FilePreviewModal;
