import React, { useState, useEffect, useRef } from 'react';
import { Tour, ConfigProvider } from 'antd';
import { Box, Text, Flex, Button, BoxProps } from '@chakra-ui/react'; // 导入 BoxProps
import styles from './tour.module.scss';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { setSystemTenantGuideFinish } from '@/api/app';
import { useChatStore } from '@/store/useChatStore';
import Lottie from '@/components/Lottie';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { ClientUseGuidanceType } from '@/constants/common';
import { useSystemStore } from '@/store/useSystemStore';
import { useQuery } from '@tanstack/react-query';
let currentStepValue = 0;

export const getCurrentStep = () => currentStepValue;
interface CustomTourProps {
  chatId: string | undefined;
  steps?: number;
  visible?: boolean;
  maskExclude?: string[];
  targetIdentifier?: string;
  chat?: any;
}

const CustomTour = ({ chatId, maskExclude, chat }: CustomTourProps) => {
  const { currentStep, setCurrentStep, hasCompletedGuidance, setHasCompletedGuidance, chatData } =
    useChatStore();
  const [isVisible, setIsVisible] = useState(false);
  const { setDeepEditChatItem, setEditType } = useDeepEditStore();
  const [radius, setRadius] = useState(8);
  const [offsetX, setOffsetX] = useState(2);
  const [offsetY, setOffsetY] = useState(2);
  const [offsetDirection, setOffsetDirection] = useState<'both' | 'individual'>('individual');
  const { setSystemGuideMap, systemGuideMap, getSystemGuideMap } = useSystemStore();
  // 添加一个 ref 来存储 chat 数据
  const chatRef = useRef(chat);
  const updateCurrentStep = (step: number) => {
    setCurrentStep(step);
  };

  // 当 chat 改变时更新 ref
  useEffect(() => {
    if (chat) {
      chatRef.current = chat;
    }
  }, [chat]);

  useEffect(() => {
    const isVisible =
      systemGuideMap[ClientUseGuidanceType.Background] &&
      systemGuideMap[ClientUseGuidanceType.DeepEditorGuide] === false &&
      chatData.editStatus === 1 &&
      !!chatId;
    if (isVisible) {
      setTimeout(() => {
        setIsVisible(true);
      }, 1000);
    } else {
      setIsVisible(false);
    }
  }, [chatId, systemGuideMap]);

  useEffect(() => {
    if (isVisible) {
      const targetFn = defaultSteps[currentStep]?.target;
      // 检查 target 是否为函数，如果不是则直接返回
      if (typeof targetFn !== 'function') return;

      const currentTarget = targetFn() as HTMLElement | null;
      if (currentTarget) {
        currentTarget.style.pointerEvents = 'none';
        currentTarget.style.opacity = '0.7';

        return () => {
          currentTarget.style.pointerEvents = '';
          currentTarget.style.opacity = '';
        };
      }
    }
  }, [isVisible, currentStep]);

  const handleTourClose = () => {
    const currentTarget = defaultSteps[currentStep]?.target() as HTMLElement | null;
    if (currentTarget) {
      currentTarget.style.pointerEvents = '';
      currentTarget.style.opacity = '';
    }
    setCurrentStep(0);
    setIsVisible(false);
  };

  const commonBoxStyle: BoxProps = {
    backgroundImage: '/imgs/app/tips_bg.png',
    backgroundRepeat: 'cover',
    backgroundSize: '100% 100%',
    p: '0px 28px 22px',
    h: respDims(530),
    borderRadius: '20px',
    display: 'flex',
    flexDir: 'column',
    justifyContent: 'space-between'
  };

  const createStep = (targetSelector: string, coverContent: React.ReactNode) => ({
    title: '',
    target: () => document.querySelector(targetSelector),
    cover: <Box {...commonBoxStyle}>{coverContent}</Box>,
    nextButtonProps: {
      style: {
        display: 'none'
      }
    },
    prevButtonProps: {
      style: {
        display: 'none'
      }
    }
  });

  const defaultSteps = [
    createStep(
      '#step1',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          新增「编辑模式」
          <img
            src="/imgs/app/welcome.gif"
            style={{ width: '49px', height: '49px', marginTop: '-10px' }}
          />
        </Text>
        <Text display="flex" alignItems="center" mb={respDims(17)}>
          <Box fontWeight="500" fontSize="16px" color="#303133" whiteSpace="nowrap">
            入口一：
          </Box>
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            您可以直接进入编辑模式，在文本编辑器中对已有的文稿内容进行导入，进一步撰写和调整～
          </Box>
        </Text>
        <Box
          w="100%"
          borderRadius="9px"
          flexGrow={1}
          mb="20px"
          overflow="hidden"
          border="1px solid #f6f6f6"
        >
          <Lottie name="step1" w="100%" h="100%" display="inline" />
        </Box>
      </>
    ),
    createStep(
      '#step2',
      <>
        <Text paddingTop="30px" mb={respDims(17)} display="flex">
          <Box fontWeight="500" fontSize={respDims('16fpx')} color="#303133" whiteSpace="nowrap">
            入口二：
          </Box>
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            AI
            生成初稿后，点击「深入编辑」，也可进入「编辑模式」，在文本编辑器中进一步调整初稿内容～
          </Box>
        </Text>
        <Box
          w="100%"
          borderRadius="9px"
          flexGrow={1}
          mb="20px"
          overflow="hidden"
          border="1px solid #f6f6f6"
        >
          <Lottie name="step2" w="100%" h="100%" display="inline" />
        </Box>
      </>
    )
  ];

  const excludeSelector = maskExclude || ['.exclude'];

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
    if (stepIndex === 2) {
      setEditType('edit');
      setDeepEditChatItem(chat);
      window.open(`/deepeditor?appId=${chatData?.appId}&mode=chat&init=1`, '_blank');
    }
  };

  const handleSkip = () => {
    setSystemTenantGuideFinish(ClientUseGuidanceType.DeepEditorGuide).then((res) => {
      setHasCompletedGuidance(true);
      setSystemGuideMap(ClientUseGuidanceType.DeepEditorGuide, true);
      handleTourClose();
    });
    window.open(`/deepeditor?appId=${chatData?.appId}&mode=chat&init=1`, '_blank');
  };

  const offsetGap: { offset: number | [number, number] } =
    offsetDirection === 'both' ? { offset: 2 } : { offset: [offsetX, offsetY] };

  return (
    <ConfigProvider
      theme={{
        token: {
          padding: 0,
          colorIcon: 'rgba(0, 0, 0, 0)',
          colorIconHover: 'rgba(0, 0, 0, 0)',
          colorPrimaryBorder: 'rgba(0, 0, 0, 0)',
          borderRadiusLG: 20
        },
        components: {
          Tour: {
            closeBtnSize: -999,
            colorWhite: '#fff'
          }
        }
      }}
    >
      {isVisible && (
        <Tour
          className={`${styles['custom-tour']} ${styles['no-padding']}`}
          steps={defaultSteps as any[]}
          open={isVisible}
          onClose={handleTourClose}
          current={currentStep}
          disabledInteraction
          onChange={(current) => {
            updateCurrentStep(current);
          }}
          closeIcon
          arrow={true}
          gap={{ ...offsetGap, radius }}
          indicatorsRender={(current, total) => (
            <Flex
              w="520px"
              h="46px"
              p="0 28px 30px 28px"
              borderRadius="0 0 20px 20px"
              position="absolute"
              bottom="0"
              left="0"
              zIndex={9}
              justifyContent="space-between"
              color="#909399"
            >
              <Flex alignItems="center" pt={respDims(22)}>
                <SvgIcon
                  name="chevronLeft"
                  w="16px"
                  h="16px"
                  color="#909399"
                  cursor="pointer"
                  onClick={() => {
                    if (current > 0) {
                      goToStep(current - 1);
                    }
                  }}
                />
                <Box>
                  {current + 1} / {total}
                </Box>
                <SvgIcon
                  name="chevronLeft"
                  transform="rotate(180deg)"
                  w="16px"
                  h="16px"
                  cursor="pointer"
                  onClick={() => {
                    if (current < total - 1) {
                      goToStep(current + 1);
                    }
                  }}
                />
              </Flex>
              <Flex alignItems="center" pt={respDims(26)}>
                {current < total - 1 && (
                  <>
                    <Box
                      fontSize={respDims('14fpx')}
                      mr="20px"
                      cursor="pointer"
                      onClick={handleSkip}
                      alignItems="center"
                      pb={respDims(8)}
                    >
                      跳过
                    </Box>
                    <Button
                      flexShrink="0"
                      w={respDims(98)}
                      h={respDims(34)}
                      lineHeight="60px"
                      variant="solid"
                      _hover={{
                        bgColor: 'primary.400'
                      }}
                      bg="#774CFF linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)"
                      color="white"
                      fontWeight="bold"
                      fontSize={respDims('14fpx')}
                      borderRadius={respDims(50)}
                      mt="-6px"
                      onClick={() => {
                        if (current < total - 1) {
                          goToStep(current + 1);
                        }
                      }}
                    >
                      下一步
                    </Button>
                  </>
                )}
                {current === total - 1 && (
                  <Button
                    flexShrink="0"
                    w={respDims(98)}
                    h={respDims(34)}
                    variant="solid"
                    _hover={{
                      bgColor: 'primary.400'
                    }}
                    bg="#774CFF linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)"
                    color="white"
                    fontSize={respDims('14fpx')}
                    borderRadius={respDims(50)}
                    mt="-6px"
                    onClick={handleSkip}
                  >
                    开始使用
                  </Button>
                )}
              </Flex>
            </Flex>
          )}
        />
      )}
    </ConfigProvider>
  );
};

export default CustomTour;
