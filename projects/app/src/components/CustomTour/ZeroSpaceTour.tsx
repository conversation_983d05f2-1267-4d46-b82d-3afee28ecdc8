import React, { useState, useEffect, useRef } from 'react';
import { Tour, ConfigProvider } from 'antd';
import { Box, Text, Flex, Button, BoxProps } from '@chakra-ui/react'; // 导入 BoxProps
import styles from './tour.module.scss';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { getSystemTenantGuideValidFinish, setSystemTenantGuideFinish } from '@/api/app';
import { useChatStore } from '@/store/useChatStore';
import { ClientUseGuidanceType } from '@/constants/common';
import { useRouter } from 'next/router';
import { useSystemStore } from '@/store/useSystemStore';
import { useQuery } from '@tanstack/react-query';

const ZeroSpaceTour = ({ componentStep }: { componentStep?: number }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [radius, setRadius] = useState(8);
  const [offsetX, setOffsetX] = useState(2);
  const [offsetY, setOffsetY] = useState(2);
  const [offsetDirection, setOffsetDirection] = useState<'both' | 'individual'>('individual');
  const { getSystemGuideMap, systemGuideMap, setSystemGuideMap } = useSystemStore();
  useQuery(['getSystemGuideMap'], () => getSystemGuideMap(false));

  const router = useRouter();

  useEffect(() => {
    if (systemGuideMap[ClientUseGuidanceType.ZeroSpace] === false) {
      setTimeout(() => {
        setIsVisible(true);
      }, 500);
    } else {
      setIsVisible(false);
    }
  }, [systemGuideMap]);

  useEffect(() => {
    if (isVisible) {
      const targetFn = defaultSteps[currentStep]?.target;
      // 检查 target 是否为函数，如果不是则直接返回
      if (typeof targetFn !== 'function') return;

      const currentTarget = targetFn() as HTMLElement | null;
      if (currentTarget) {
        currentTarget.style.pointerEvents = 'none';
        currentTarget.style.opacity = '0.7';

        return () => {
          currentTarget.style.pointerEvents = '';
          currentTarget.style.opacity = '';
        };
      }
    }
  }, [isVisible, currentStep]);

  const handleTourClose = () => {
    const currentTarget = defaultSteps[currentStep]?.target() as HTMLElement | null;
    if (currentTarget) {
      currentTarget.style.pointerEvents = '';
      currentTarget.style.opacity = '';
    }
    // setCurrentStep(0);
    setIsVisible(false);
  };

  const commonBoxStyle: BoxProps = {
    backgroundImage: '/imgs/app/tips_bg.png',
    backgroundRepeat: 'cover',
    backgroundSize: '100% 100%',
    p: '0px 28px 0px',
    // h: respDims(530),
    borderRadius: '20px',
    display: 'flex',
    flexDir: 'column',
    justifyContent: 'space-between'
  };

  const createStep = (targetSelector: string, coverContent: React.ReactNode) => ({
    title: '',
    target: () => document.querySelector(targetSelector),
    cover: <Box {...commonBoxStyle}>{coverContent}</Box>,
    nextButtonProps: {
      style: {
        display: 'none'
      }
    },
    prevButtonProps: {
      style: {
        display: 'none'
      }
    }
  });

  const defaultSteps = [
    createStep(
      '#zero_code_space',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          新增「零代码空间」
          <img
            src="/imgs/app/welcome.gif"
            style={{ width: '49px', height: '49px', marginTop: '-10px' }}
          />
        </Text>
        <Text display="flex" alignItems="center" mb={respDims(17)}>
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            快速创建个人应用与专属知识库链接，享受专属智能助手带来的个性化服务
          </Box>
        </Text>
      </>
    ),
    createStep(
      '#zero_code_space1',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          创建应用
        </Text>
        <Text mb={respDims(17)} display="flex">
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            “创建应用”功能，能快速打造个人专属应用，关联个性化知识，定制属于自己的智能助手。
          </Box>
        </Text>
      </>
    ),
    createStep(
      '#zero_code_space2',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          我的应用
        </Text>
        <Text mb={respDims(17)} display="flex">
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            “我的应用”板块，在这里，显示个人打造专属应用，可编辑调整专属个人的智能助手。
          </Box>
        </Text>
      </>
    ),
    createStep(
      '#zero_code_space3',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          创建专属你的知识库吧～
        </Text>
        <Text mb={respDims(17)} display="flex">
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            若想让我的应用掌握个性化知识，可将相关内容上传至知识库，系统会自动处理，使其能被我的应用读取。
          </Box>
        </Text>
      </>
    )
  ];

  const isTargetAvailable = defaultSteps?.some((step) => step.target() !== null);

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
    if (stepIndex === 1) {
      router.push('/zeroCode?tab=create');
    }
    if (stepIndex === 2) {
      router.push('/zeroCode?tab=my');
    }
    if (stepIndex === 3) {
      router.push('/zeroCode?tab=dataset');
    }
    if (stepIndex === 1 || stepIndex === 3) {
      setIsVisible(false);

      setTimeout(() => {
        setIsVisible(true);
      }, 1200);
    }
  };

  const handleSkip = () => {
    setSystemTenantGuideFinish(ClientUseGuidanceType.ZeroSpace).then((res) => {
      handleTourClose();
      setSystemGuideMap(ClientUseGuidanceType.ZeroSpace, true);
    });
  };

  const offsetGap: { offset: number | [number, number] } =
    offsetDirection === 'both' ? { offset: 2 } : { offset: [offsetX, offsetY] };

  return (
    <ConfigProvider
      theme={{
        token: {
          padding: 0,
          colorIcon: 'rgba(0, 0, 0, 0)',
          colorIconHover: 'rgba(0, 0, 0, 0)',
          colorPrimaryBorder: 'rgba(0, 0, 0, 0)',
          borderRadiusLG: 20
        },
        components: {
          Tour: {
            closeBtnSize: -999,
            colorWhite: '#fff'
          }
        }
      }}
    >
      {isVisible && isTargetAvailable && (
        <Tour
          placement={currentStep === 0 ? 'right' : 'bottom'}
          className={`${styles['custom-tour-zero-space']}`}
          steps={defaultSteps as any[]}
          open={isVisible}
          onClose={handleTourClose}
          current={currentStep}
          disabledInteraction
          onChange={(current) => {}}
          closeIcon
          mask={{
            style: {
              zIndex: 5000
            }
          }}
          zIndex={5001}
          arrow={true}
          gap={{ ...offsetGap, radius }}
          indicatorsRender={(current, total) => (
            <Flex
              w="520px"
              p="0 28px 0px 28px"
              borderRadius="0 0 20px 20px"
              zIndex={9}
              h="34px"
              mb={respDims(22, 18)}
              justifyContent="space-between"
              color="#909399"
            >
              <Flex alignItems="center">
                <SvgIcon
                  name="chevronLeft"
                  w="16px"
                  h="16px"
                  color="#909399"
                  cursor="pointer"
                  onClick={() => {
                    if (current > 0) {
                      goToStep(current - 1);
                    }
                  }}
                />
                <Box>
                  {current + 1} / {total}
                </Box>
                <SvgIcon
                  name="chevronLeft"
                  transform="rotate(180deg)"
                  w="16px"
                  h="16px"
                  cursor="pointer"
                  onClick={() => {
                    if (current < total - 1) {
                      goToStep(current + 1);
                    }
                  }}
                />
              </Flex>
              <Flex alignItems="center" gap={'14px'}>
                {current < total - 1 && (
                  <>
                    <Box
                      fontSize={respDims('14fpx')}
                      cursor="pointer"
                      onClick={handleSkip}
                      alignItems="center"
                    >
                      跳过
                    </Box>
                  </>
                )}
                {
                  <>
                    {current > 0 && (
                      <Button
                        flexShrink="0"
                        w={respDims(98)}
                        h={'34px'}
                        lineHeight="60px"
                        variant={'grayBase'}
                        fontWeight="bold"
                        fontSize={respDims('14fpx')}
                        borderRadius={respDims(50)}
                        mt="-6px"
                        onClick={() => {
                          if (current > 0) {
                            goToStep(current - 1);
                          }
                        }}
                      >
                        上一步
                      </Button>
                    )}
                    {current < total - 1 && (
                      <Button
                        flexShrink="0"
                        w={respDims(98)}
                        h={'34px'}
                        lineHeight="60px"
                        variant="solid"
                        _hover={{
                          bgColor: 'primary.400'
                        }}
                        bg="#774CFF linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)"
                        color="white"
                        fontWeight="bold"
                        fontSize={respDims('14fpx')}
                        borderRadius={respDims(50)}
                        mt="-6px"
                        onClick={() => {
                          if (current < total - 1) {
                            goToStep(current + 1);
                          }
                        }}
                      >
                        下一步
                      </Button>
                    )}
                    {current === total - 1 && (
                      <Button
                        flexShrink="0"
                        w={respDims(98)}
                        h={'34px'}
                        variant="solid"
                        _hover={{
                          bgColor: 'primary.400'
                        }}
                        bg="#774CFF linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)"
                        color="white"
                        fontSize={respDims('14fpx')}
                        borderRadius={respDims(50)}
                        onClick={handleSkip}
                      >
                        知道了
                      </Button>
                    )}
                  </>
                }
              </Flex>
            </Flex>
          )}
        />
      )}
    </ConfigProvider>
  );
};

export default ZeroSpaceTour;
