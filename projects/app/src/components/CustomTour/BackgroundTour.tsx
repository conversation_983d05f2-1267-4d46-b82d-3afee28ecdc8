import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Tour, ConfigProvider } from 'antd';
import { Box, Text, Flex, Button, BoxProps } from '@chakra-ui/react'; // 导入 BoxProps
import styles from './tour.module.scss';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import {
  getSystemTenantGuideValidFinish,
  setClientUseGuidanceCreate,
  setSystemTenantGuideFinish
} from '@/api/app';
import { useChatStore } from '@/store/useChatStore';
import { ClientUseGuidanceType } from '@/constants/common';
import { useRouter } from 'next/router';
import Lottie from '../Lottie';
import { useSystemStore } from '@/store/useSystemStore';
import { useQuery } from '@tanstack/react-query';
const BackgroundTour = ({
  componentStep,
  nextHandler
}: {
  componentStep?: number;
  nextHandler?: () => void;
}) => {
  const { currentStep, setCurrentStep } = useChatStore();
  const [radius, setRadius] = useState(8);
  const [offsetX, setOffsetX] = useState(2);
  const [offsetY, setOffsetY] = useState(2);
  const [offsetDirection, setOffsetDirection] = useState<'both' | 'individual'>('individual');
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const { getSystemGuideMap, systemGuideMap, setSystemGuideMap } = useSystemStore();
  useQuery(['getSystemGuideMap'], () => getSystemGuideMap(false));
  useEffect(() => {
    componentStep == 0 && setCurrentStep(0);
    // 调用接口判断是否需要显示引导
  }, [componentStep, setCurrentStep]);

  useEffect(() => {
    if (
      systemGuideMap[ClientUseGuidanceType.Background] === false &&
      componentStep == currentStep
    ) {
      setTimeout(() => {
        setIsVisible(true);
      }, 1000);
    } else {
      setIsVisible(false);
    }
  }, [componentStep, systemGuideMap]);

  useEffect(() => {
    if (isVisible) {
      const targetFn = defaultSteps[currentStep]?.target;
      // 检查 target 是否为函数，如果不是则直接返回
      if (typeof targetFn !== 'function') return;

      const currentTarget = targetFn() as HTMLElement | null;
      if (currentTarget) {
        currentTarget.style.pointerEvents = 'none';
        currentTarget.style.opacity = '0.7';

        return () => {
          currentTarget.style.pointerEvents = '';
          currentTarget.style.opacity = '';
        };
      }
    }
  }, [isVisible, currentStep]);

  const handleTourClose = () => {
    const currentTarget = defaultSteps[currentStep]?.target() as HTMLElement | null;
    if (currentTarget) {
      currentTarget.style.pointerEvents = '';
      currentTarget.style.opacity = '';
    }
    // setCurrentStep(0);
    setIsVisible(false);
  };

  const commonBoxStyle: BoxProps = {
    backgroundImage: '/imgs/app/tips_bg.png',
    backgroundRepeat: 'cover',
    backgroundSize: '100% 100%',
    p: '0px 28px 0px',
    // h: respDims(530),
    borderRadius: '20px',
    display: 'flex',
    flexDir: 'column',
    justifyContent: 'space-between'
  };

  const createStep = (targetSelector: string, coverContent: React.ReactNode) => ({
    title: '',
    target: () => document.querySelector(targetSelector),
    cover: <Box {...commonBoxStyle}>{coverContent}</Box>,
    nextButtonProps: {
      style: {
        display: 'none'
      }
    },
    prevButtonProps: {
      style: {
        display: 'none'
      }
    }
  });

  const defaultSteps = [
    createStep(
      '#bg_knowledge_tour',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
          marginTop="-5px"
        >
          「背景知识」换位置啦
          <img
            src="/imgs/app/welcome.gif"
            style={{ width: '49px', height: '49px', marginTop: '-10px', marginLeft: '10px' }}
          />
        </Text>
        <Text display="flex" alignItems="center" mb={respDims(17)}>
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            您曾经在智能应用设置的背景知识文件将在此处进行维护和查看～
          </Box>
        </Text>
        <Box w="100%" borderRadius="9px" flexGrow={1} overflow="hidden" border="1px solid #f6f6f6">
          <Lottie name="bgKnowledgeStep1" w="100%" h="100%" display="inline" />
        </Box>
      </>
    ),
    createStep(
      '#bg_knowledge_tour',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          「背景知识」开关设置
        </Text>
        <Text mb={respDims(17)} display="flex">
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            根据您的业务场景，可进行「打开」或「关闭」是否在对话过程中，进行背景知识引用
          </Box>
        </Text>
        <Box w="100%" borderRadius="9px" flexGrow={1} overflow="hidden" border="1px solid #f6f6f6">
          <Lottie name="bgKnowledgeStep2" w="100%" h="100%" display="inline" />
        </Box>
      </>
    ),
    createStep(
      '#bg_knowledge_tour2',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          「背景知识」文件引用方式
        </Text>
        <Text mb={respDims(17)} display="flex">
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            原背景知识添加的文档将在此处进行维护，文档将以长文本的形式被引用，每一轮AI对话都将参照此文档内容。
          </Box>
        </Text>
        <Box w="100%" borderRadius="9px" flexGrow={1} overflow="hidden" border="1px solid #f6f6f6">
          <Lottie name="bgKnowledgeStep3" w="100%" h="100%" display="inline" />
        </Box>
      </>
    ),
    createStep(
      '#bg_knowledge_tour3',
      <>
        <Text
          fontSize={respDims('22fpx')}
          mb={respDims(17)}
          fontWeight="bold"
          color="#000"
          textAlign="left"
          paddingTop="30px"
          display="flex"
        >
          「背景知识」添加检索引用方式
        </Text>
        <Text mb={respDims(17)} display="flex">
          <Box fontWeight="400" fontSize={respDims('16fpx')} color="#606266" textAlign="left">
            <Box
              css={{
                '&::before': {
                  content: '""',
                  display: 'inline-block',
                  width: '5px',
                  lineHeight: '30px',
                  marginBottom: '2px',
                  height: '5px',
                  marginRight: '5px',
                  backgroundColor: '#606266',
                  borderRadius: '50%'
                }
              }}
            >
              你可以检索引用数据空间内容，系统会根据您的问题，在每轮对话中查找并引用数据空间里的相关文档片段，来帮助构建对话内容。
            </Box>
            <Box
              css={{
                '&::before': {
                  content: '""',
                  display: 'inline-block',
                  width: '5px',
                  lineHeight: '30px',
                  height: '5px',
                  marginRight: '5px',
                  marginBottom: '2px',
                  backgroundColor: '#606266',
                  borderRadius: '50%'
                }
              }}
            >
              选择检索范围越小，引用内容越准确
            </Box>
          </Box>
        </Text>
        <Box w="100%" borderRadius="9px" flexGrow={1} overflow="hidden" border="1px solid #f6f6f6">
          <Lottie name="bgKnowledgeStep4" w="100%" h="100%" display="inline" />
        </Box>
      </>
    )
  ];

  const isTargetAvailable = defaultSteps?.some((step) => step.target() !== null);

  const goToStep = (stepIndex: number) => {
    if (stepIndex === 2) {
      const modal = document.querySelector('.bg_knowledge_tour_container');
      if (!modal) {
        nextHandler?.();
      }
    }
    setTimeout(() => {
      setCurrentStep(stepIndex);
    }, 300);
  };

  const handleSkip = () => {
    setSystemTenantGuideFinish(ClientUseGuidanceType.Background).then((res) => {
      setSystemGuideMap(ClientUseGuidanceType.Background, true);
      handleTourClose();
    });
  };

  const offsetGap: { offset: number | [number, number] } =
    offsetDirection === 'both' ? { offset: 2 } : { offset: [offsetX, offsetY] };

  return (
    <ConfigProvider
      theme={{
        token: {
          padding: 0,
          colorIcon: 'rgba(0, 0, 0, 0)',
          colorIconHover: 'rgba(0, 0, 0, 0)',
          colorPrimaryBorder: 'rgba(0, 0, 0, 0)',
          borderRadiusLG: 20
        },
        components: {
          Tour: {
            closeBtnSize: -999,
            colorWhite: '#fff'
          }
        }
      }}
    >
      {isVisible && isTargetAvailable && (
        <Tour
          placement={currentStep === 0 || currentStep === 1 ? 'top' : 'right'}
          steps={defaultSteps as any[]}
          open={isVisible}
          onClose={handleTourClose}
          current={currentStep}
          disabledInteraction
          onChange={(current) => {}}
          closeIcon
          mask={{
            style: {
              zIndex: 5000
            }
          }}
          rootClassName={'tour-zero-space-background'}
          zIndex={5001}
          arrow={true}
          gap={{ ...offsetGap, radius }}
          indicatorsRender={(current, total) => (
            <Flex
              w="450px"
              p="0 28px 0px 28px"
              borderRadius="0 0 20px 20px"
              zIndex={9}
              h="34px"
              mb={respDims(22, 18)}
              justifyContent="space-between"
              color="#909399"
            >
              <Flex alignItems="center">
                <SvgIcon
                  name="chevronLeft"
                  w="16px"
                  h="16px"
                  color="#909399"
                  cursor="pointer"
                  onClick={() => {
                    if (current > 0) {
                      goToStep(current - 1);
                    }
                  }}
                />
                <Box>
                  {current + 1} / {total}
                </Box>
                <SvgIcon
                  name="chevronLeft"
                  transform="rotate(180deg)"
                  w="16px"
                  h="16px"
                  cursor="pointer"
                  onClick={() => {
                    if (current < total - 1) {
                      goToStep(current + 1);
                    }
                  }}
                />
              </Flex>
              <Flex alignItems="center" gap={'14px'}>
                {current < total - 1 && (
                  <>
                    <Box
                      fontSize={respDims('14fpx')}
                      cursor="pointer"
                      onClick={handleSkip}
                      alignItems="center"
                    >
                      跳过
                    </Box>
                  </>
                )}
                {
                  <>
                    {current > 0 && (
                      <Button
                        flexShrink="0"
                        w={respDims(98)}
                        h={'34px'}
                        lineHeight="60px"
                        variant={'grayBase'}
                        fontWeight="bold"
                        fontSize={respDims('14fpx')}
                        borderRadius={respDims(50)}
                        mt="-6px"
                        onClick={() => {
                          if (current > 0) {
                            goToStep(current - 1);
                          }
                        }}
                      >
                        上一步
                      </Button>
                    )}
                    {current < total - 1 && (
                      <Button
                        flexShrink="0"
                        w={respDims(98)}
                        h={'34px'}
                        lineHeight="60px"
                        variant="solid"
                        _hover={{
                          bgColor: 'primary.400'
                        }}
                        bg="#774CFF linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)"
                        color="white"
                        fontWeight="bold"
                        fontSize={respDims('14fpx')}
                        borderRadius={respDims(50)}
                        mt="-6px"
                        onClick={() => {
                          if (current < total - 1) {
                            goToStep(current + 1);
                          }
                        }}
                      >
                        下一步
                      </Button>
                    )}
                    {current === total - 1 && (
                      <Button
                        flexShrink="0"
                        w={respDims(98)}
                        h={'34px'}
                        variant="solid"
                        _hover={{
                          bgColor: 'primary.400'
                        }}
                        bg="#774CFF linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)"
                        color="white"
                        fontSize={respDims('14fpx')}
                        borderRadius={respDims(50)}
                        onClick={handleSkip}
                      >
                        知道了
                      </Button>
                    )}
                  </>
                }
              </Flex>
            </Flex>
          )}
        />
      )}
    </ConfigProvider>
  );
};

export default BackgroundTour;
