import { Box, Flex, Image, Button, Container } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { GLOBAL_DIMS_MIN_SCALE } from '@/constants/common';
import { AppProps } from 'next/app';
import { useState, useEffect } from 'react';
import OfficialHome from '@/pages/officialHome/dianhua';
import AgentCenter from '@/pages/officialHome/dianhua/agentCenter';
import Guide from '@/pages/officialHome/dianhua/guide';
// 导航菜单项
const navItems = [
  {
    name: '首页',
    path: '/officialHome/dianhua',
    bgColor: '#F6FCFD',
    bgImage: '/imgs/officialHome/home/<USER>'
  },
  {
    name: '智能体中心',
    path: '/officialHome/dianhua/agentCenter',
    bgImage: '/imgs/officialHome/agentcenter/banner.png',
    bgColor: '#F6FCFD'
  },
  { name: '智能体创造指南', path: '/officialHome/dianhua/guide' }
];

export default function DianHuaLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const query = router.query;
  const currentPath = query.path || '/officialHome/dianhua';
  const currentItem = navItems.find((item) => item.path === currentPath);

  return (
    <Box w="100%" overflowY="auto" bg={currentItem?.bgColor} h="100%" position="relative">
      {/* 顶部导航 */}
      {currentItem?.bgImage && (
        <Image
          src={currentItem?.bgImage}
          alt="banner"
          w="100%"
          objectFit="cover"
          position="absolute"
        />
      )}
      <Flex
        bgImage={currentItem?.bgImage}
        css={{
          width: 'calc(100% - 4px)',
          ...(currentItem?.bgImage ? { bgImage: currentItem?.bgImage } : {})
        }}
        zIndex="1000"
        bgSize="cover"
        bgPosition="left top"
        h="60px"
        justifyContent="space-between"
        position="fixed"
        top="0"
        left="0"
        bgRepeat="no-repeat"
        bgColor={'#fff'}
        borderBottom={currentPath === '/officialHome/dianhua/guide' ? '1px solid #F3F3F3' : 'none'}
      >
        <Box w="100%" px="8" h="100%">
          <Flex h="100%" alignItems="center" justifyContent="space-between">
            {/* Logo区域 */}
            <Flex alignItems="center" gap="1">
              <Image src="/imgs/officialHome/nav/dianhua/logo.png" alt="logo" w="52px" h="52px" />
              <Flex alignItems="center" gap="1" display={{ base: 'none', sm: 'flex' }}>
                <Box w="1px" h="15.5px" bg="#29A9FD" mx="2.5" />
                <Box fontSize="21px" fontWeight="bold" color="black" lineHeight="37.5px">
                  广州智教云脑
                </Box>
              </Flex>
            </Flex>

            {/* 导航菜单 */}
            <Flex
              gap="3"
              alignItems="center"
              position="absolute"
              left="50%"
              transform="translateX(-50%)"
            >
              {navItems.map((item) => (
                <Box
                  key={item.path}
                  px="2"
                  py="1"
                  bg={
                    item.path === currentPath
                      ? currentPath === '/officialHome/dianhua' ||
                        currentPath === '/' ||
                        currentPath === '/officialHome/dianhua/agentCenter'
                        ? 'rgba(255, 255, 255, 0.80)'
                        : currentPath === '/officialHome/dianhua/guide'
                          ? 'rgba(211, 211, 211, 0.46)'
                          : 'transparent'
                      : 'transparent'
                  }
                  borderRadius="3px"
                  cursor="pointer"
                  onClick={() =>
                    router.push({
                      pathname: '/',
                      query: {
                        path: item.path
                      }
                    })
                  }
                >
                  <Box fontSize="17px" fontWeight="bold" color="#1F2329">
                    {item.name}
                  </Box>
                </Box>
              ))}
            </Flex>

            {/* 创建按钮 */}
            <Box h="36px" position="relative">
              <Button
                h="36px"
                w="100px"
                bg="#2468F2"
                color="white"
                fontSize="14px"
                borderRadius="50px"
                _hover={{ bg: '#2468F2' }}
                onClick={() => router.push('/zeroCode?tab=my')}
              >
                创建智能体
              </Button>
            </Box>
          </Flex>
        </Box>
      </Flex>

      {/* 内容区域 */}
      <Box pt="60px" position="relative">
        {(currentPath === '/officialHome/dianhua' || currentPath === '/') && <OfficialHome />}
        {currentPath === '/officialHome/dianhua/agentCenter' && <AgentCenter />}
        {currentPath === '/officialHome/dianhua/guide' && <Guide />}
      </Box>

      {/* 页脚 */}
      {/* <Flex
        direction="column"
        alignItems="center"
        py="20px"
        color="#909AAA"
        fontSize="10.5px"
        lineHeight="22.5px"
        textAlign="center"
      >
        <Box>主办单位：深圳市光明区教科院</Box>
        <Flex gap="1">
          站点建设与维护：网络信息中心 粤ICP备06008037号-5 粤公网安备61010302001223
        </Flex>
      </Flex> */}
    </Box>
  );
}
