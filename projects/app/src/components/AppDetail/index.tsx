import { AuthTypeEnum, PageTypeEnum } from '@/components/FastGPT/constants';
import FastGPTWrapper from '@/components/FastGPTWrapper';
import Loading from '@/components/Loading';
import { DataSource } from '@/constants/common';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { serviceSideProps } from '@/utils/i18n';
import dynamic from 'next/dynamic';
import AppContextProvider, { SimpleAppPerm } from './components/context';
import { useMemo } from 'react';
import { useRouter } from 'next/router';
import { AppListItemType } from '@/types/api/app';
const SimpleEdit = dynamic(() => import('./components/SimpleApp'), {
  ssr: false,
  loading: () => <Loading fixed={false} />
});
const AdvanceApp = dynamic(() => import('./components/AdvanceApp'), {
  ssr: false,
  loading: () => <Loading fixed={false} />
});
const AppDetail = ({
  appType,
  finalAppId,
  isAdmin
}: {
  appType?: AppTypeEnum;
  finalAppId: string;
  isAdmin: '1' | '0';
}) => {
  const router = useRouter();

  const appDetail = useMemo(() => {
    try {
      return JSON.parse(decodeURIComponent(router.query.appDetail as string)) as AppListItemType;
    } catch (error) {
      return {} as AppListItemType;
    }
  }, [router.query]);

  return (
    <>
      {/* {appType === AppTypeEnum.simple && (
        <AppContextProvider
          finalAppId={finalAppId}
          isAdmin={isAdmin}
          id={appDetail.id}
          settingMode="fullSetting"
          permission={SimpleAppPerm.Self}
        >
          <SimpleEdit />
        </AppContextProvider>
      )} */}
      {
        <AdvanceApp
          appType={appType}
          finalAppId={finalAppId}
          authType={isAdmin === '1' ? AuthTypeEnum.admin : AuthTypeEnum.user}
        />
      }
    </>
  );
};

export default AppDetail;
