import React, { useEffect } from 'react';
import { Box, useCardStyles } from '@chakra-ui/react';
import { useMount } from 'ahooks';

import ChatTest from './ChatTest';
import AppCard from './AppCard';
import EditForm from './EditForm';
import { AppContext } from '@/components/AppDetail/components/context';
import { useContextSelector } from 'use-context-selector';

import styles from './styles.module.scss';
import { useDatasetStore } from '@/store/useDatasetStore';
import { cardStyles } from '../constants';
import { useSystemStore } from '@/store/useSystemStore';
import { appWorkflow2Form } from '@/fastgpt/global/core/app/utils';
import { v1Workflow2V2 } from '@/fastgpt/web/core/workflow/adapt';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { serviceSideProps } from '@/utils/i18n';

const Edit = ({
  appForm,
  setAppForm
}: {
  appForm: AppSimpleEditFormTypeMegre;
  setAppForm: React.Dispatch<React.SetStateAction<AppSimpleEditFormTypeMegre>>;
}) => {
  const { isPc } = useSystemStore();
  const { loadAllDatasets } = useDatasetStore();
  const { originAppDetail, clientAppDetail } = useContextSelector(AppContext, (v) => v);

  // show selected dataset
  useEffect(() => {
    loadAllDatasets();
    setAppForm({
      ...appWorkflow2Form({
        nodes: originAppDetail.modules,
        chatConfig: originAppDetail.chatConfig
      }),
      promptList: clientAppDetail.promptList || [],
      isStructuredPrompt: clientAppDetail.isStructuredPrompt,
      filesList: clientAppDetail.filesList || []
    });

    if (originAppDetail.version !== 'v2') {
      setAppForm({
        ...appWorkflow2Form({
          nodes: v1Workflow2V2((originAppDetail.modules || []) as any)?.nodes,
          chatConfig: originAppDetail.chatConfig
        }),
        promptList: clientAppDetail.promptList || [],
        isStructuredPrompt: clientAppDetail.isStructuredPrompt,
        filesList: clientAppDetail.filesList || []
      });
    }
  }, [originAppDetail]);

  return (
    <Box
      display={['block', 'flex']}
      flex={'1 0 0'}
      h={0}
      pt={[2, 1.5]}
      gap={1}
      borderRadius={'lg'}
      overflowY={['auto', 'unset']}
    >
      <Box
        className={styles.EditAppBox}
        pr={[0, 1]}
        overflowY={'auto'}
        minW={['auto', '580px']}
        flex={'1'}
      >
        <Box {...cardStyles} boxShadow={'2'}>
          <AppCard appForm={appForm} setAppForm={setAppForm} />
        </Box>

        <Box mt={4} {...cardStyles} boxShadow={'3.5'}>
          <EditForm appForm={appForm} setAppForm={setAppForm} />
        </Box>
      </Box>
      {isPc && (
        <Box {...cardStyles} boxShadow={'3'} flex={'2 0 0'} w={0} h={'100%'}>
          <ChatTest appForm={appForm} />
        </Box>
      )}
    </Box>
  );
};

export default React.memo(Edit);
