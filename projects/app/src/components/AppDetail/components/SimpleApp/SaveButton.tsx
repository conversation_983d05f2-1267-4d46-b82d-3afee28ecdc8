import React, { useCallback, useMemo } from 'react';
import { useContextSelector } from 'use-context-selector';
import { AppContext } from '../context';
import { Button } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { form2AppWorkflow } from '@/fastgpt/web/core/app/utils';

const SaveButton = ({
  appForm,
  setAppForm
}: {
  appForm: AppSimpleEditFormTypeMegre;
  setAppForm: React.Dispatch<React.SetStateAction<AppSimpleEditFormTypeMegre>>;
}) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { appId, originAppDetail, clientAppDetail, onPublish, isPublished } = useContextSelector(
    AppContext,
    (v) => v
  );

  const onSubmitPublish = useCallback(
    async (data: AppSimpleEditFormTypeMegre) => {
      const { nodes, edges } = form2AppWorkflow(data, t);

      await onPublish({
        id: clientAppDetail.id as string,
        nodes,
        edges,
        chatConfig: data.chatConfig,
        type: AppTypeEnum.simple,
        name: originAppDetail.name,
        isStructuredPrompt: data.isStructuredPrompt,
        promptList: data.promptList,
        filesList: data.filesList
      });
    },
    [onPublish, t, originAppDetail]
  );

  return (
    <Button isDisabled={isPublished} onClick={() => onSubmitPublish(appForm)}>
      {t('common.Save')}
    </Button>
  );
};

export default SaveButton;
