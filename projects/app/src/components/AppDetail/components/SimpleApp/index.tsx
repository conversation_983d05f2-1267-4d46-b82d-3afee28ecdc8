import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import Header from './Header';
import Edit from './Edit';
import { useContextSelector } from 'use-context-selector';
import { AppContext, TabEnum } from '../context';
import { Box, Flex } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useBeforeunload } from '@/hooks/useBeforeunload';

const SimpleEdit = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { currentTab, appForm, setAppForm, isPublished } = useContextSelector(AppContext, (v) => ({
    currentTab: v.currentTab,
    appForm: v.appForm,
    setAppForm: v.setAppForm,
    isPublished: v.isPublished
  }));

  return (
    <Flex h={'100%'} flexDirection={'column'} px={[3, 3]} pr={[3, 3]} pb={3}>
      <Header appForm={appForm} setAppForm={setAppForm} />
      {currentTab === TabEnum.appEdit ? (
        <Edit appForm={appForm} setAppForm={setAppForm} />
      ) : (
        <Box flex={'1 0 0'} h={0} mt={4}>
          {/* {currentTab === TabEnum.publish && <PublishChannel />} */}
          {/* {currentTab === TabEnum.logs && <Logs />} */}
        </Box>
      )}
    </Flex>
  );
};

export default React.memo(SimpleEdit);
