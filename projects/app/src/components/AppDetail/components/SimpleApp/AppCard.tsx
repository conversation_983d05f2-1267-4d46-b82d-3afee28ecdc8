import React, { useState } from 'react';
import {
  Box,
  Flex,
  Button,
  IconButton,
  HStack,
  Modal,
  ModalBody,
  Checkbox,
  ModalFooter
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { AppSchema } from '@/fastgpt/global/core/app/type.d';
import { useTranslation } from 'next-i18next';
import { AppContext } from '@/components/AppDetail/components/context';
import { useContextSelector } from 'use-context-selector';
import { postTransition2Workflow } from '@/api/app';
import Avatar from '@/components/Avatar';
import { useSystemStore } from '@/store/useSystemStore';
import MyModal from '@/components/MyModal';
import { useRequest2 } from '@/hooks/useRequest';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import MyMenu from '@/components/MyMenu';
import MyIcon from '@/components/LegacyIcon';
import SaveButton from './SaveButton';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import AppModal from '@/components/AppModal/simple';
import AppTenantModal from '@/components/AppTenantModal';
import { getSampleAppInfo } from '@/utils/app';

const AppCard = ({
  appForm,
  setAppForm
}: {
  appForm: AppSimpleEditFormTypeMegre;
  setAppForm: React.Dispatch<React.SetStateAction<AppSimpleEditFormTypeMegre>>;
}) => {
  const router = useRouter();
  const { t } = useTranslation();
  const { isPc } = useSystemStore();
  const { openOverlay } = useOverlayManager();
  const { originAppDetail, setOriginAppDetail, clientAppDetail, onDelApp, isAdmin } =
    useContextSelector(AppContext, (v) => v);
  const { feConfigs } = useSystemStore();

  // transition to workflow
  const [transitionCreateNew, setTransitionCreateNew] = useState<boolean>();
  const { runAsync: onTransition, loading: transiting } = useRequest2(
    () =>
      postTransition2Workflow({ id: clientAppDetail.id!, createNew: transitionCreateNew }, isAdmin),
    {
      onSuccess: (data) => {
        if (data && data.finalAppId) {
          router.replace({
            pathname: '/app/detail',
            query: {
              ...router.query,
              finalAppId: data.finalAppId,
              appType: AppTypeEnum.workflow,
              appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(clientAppDetail)))
            }
          });
        } else {
          router.replace({
            pathname: '/app/detail',

            query: {
              ...router.query,
              appType: AppTypeEnum.workflow,
              timestamp: new Date().getTime(), // 添加唯一的查询参数
              appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(clientAppDetail)))
            }
          });
        }
      },
      successToast: t('操作成功')
    }
  );

  const onOpenInfoEdit = () => {
    if (isAdmin) {
      openOverlay({
        Overlay: AppTenantModal,
        props: {
          mode: 1,
          onSuccess: (data) => {
            if (data) {
              setOriginAppDetail((state) => ({
                ...state,
                ...data
              }));
            }
          },
          appModalParams: {
            id: clientAppDetail.id,
            name: originAppDetail.name,
            intro: originAppDetail.intro,
            avatarUrl: clientAppDetail.avatarUrl,
            tenantSceneIds: clientAppDetail.tenantSceneIds,
            tenantLabelIds: clientAppDetail.tenantLabelIds,
            source: clientAppDetail.source,
            labelList: clientAppDetail.labelList
          }
        }
      });
    } else {
      openOverlay({
        Overlay: AppModal,
        props: {
          appModalParams: {
            id: clientAppDetail.id,
            name: originAppDetail.name,
            intro: originAppDetail.intro,
            avatarUrl: clientAppDetail.avatarUrl
          },
          onSuccess: (data) => {
            if (data) {
              setOriginAppDetail((state) => ({
                ...state,
                ...data
              }));
            }
          }
        }
      });
    }
  };

  return (
    <>
      {/* basic info */}
      <Box px={[4, 6]} py={4} position={'relative'}>
        <Flex alignItems={'center'}>
          <Avatar src={originAppDetail.avatar} borderRadius={'md'} w={'28px'} />
          <Box ml={3} fontWeight={'bold'} fontSize={'md'} flex={'1 0 0'} color={'myGray.900'}>
            {originAppDetail.name}
          </Box>
        </Flex>
        <Box
          flex={1}
          mt={3}
          mb={4}
          className={'textEllipsis3'}
          wordBreak={'break-all'}
          color={'myGray.600'}
          fontSize={'xs'}
          minH={'46px'}
        >
          {originAppDetail.intro || t('common:core.app.tip.Add a intro to app')}
        </Box>
        <HStack alignItems={'flex-end'}>
          <Button
            size={['sm', 'md']}
            variant={'whitePrimary'}
            leftIcon={<MyIcon name={'core/chat/chatLight'} w={'16px'} />}
            onClick={() => router.push(`/home?appId=${clientAppDetail.id}`)}
          >
            {t('common:core.Chat')}
          </Button>
          {
            <Button
              size={['sm', 'md']}
              variant={'whitePrimary'}
              leftIcon={<MyIcon name={'common/settingLight'} w={'16px'} />}
              onClick={onOpenInfoEdit}
            >
              {t('common:common.Setting')}
            </Button>
          }

          {
            <MyMenu
              Button={
                <IconButton
                  variant={'whiteBase'}
                  size={['smSquare', 'mdSquare']}
                  icon={<MyIcon name={'more'} w={'1rem'} />}
                  aria-label={''}
                />
              }
              menuList={[
                {
                  icon: 'core/app/type/workflow',
                  label: t('转成工作流'),
                  onClick: () => setTransitionCreateNew(true)
                },
                {
                  icon: 'delete',
                  label: t('common:common.Delete'),
                  onClick: onDelApp
                }
              ]}
            />
          }
          <Box flex={1} />
          <SaveButton appForm={appForm} setAppForm={setAppForm}></SaveButton>
        </HStack>
      </Box>
      {transitionCreateNew !== undefined && (
        <MyModal isOpen title={t('转成工作流')} iconSrc="core/app/type/workflow">
          <ModalBody>
            <Box mb={3}>{t('转化成工作流后，将无法转化回简易模式，请确认！')}</Box>
            <HStack cursor={'pointer'} onClick={() => setTransitionCreateNew((state) => !state)}>
              <Checkbox isChecked={transitionCreateNew} />
              <Box>{t('创建一个新的应用，而不是修改当前应用')}</Box>
            </HStack>
          </ModalBody>
          <ModalFooter>
            <Button variant={'whiteBase'} onClick={() => setTransitionCreateNew(undefined)} mr={3}>
              {t('common:common.Close')}
            </Button>
            <Button variant={'primary'} isLoading={transiting} onClick={() => onTransition()}>
              {t('common:common.Confirm')}
            </Button>
          </ModalFooter>
        </MyModal>
      )}
    </>
  );
};

export default React.memo(AppCard);
