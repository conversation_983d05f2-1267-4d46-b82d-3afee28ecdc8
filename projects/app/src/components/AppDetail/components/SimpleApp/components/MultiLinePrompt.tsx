import React, { useEffect, useState } from 'react';
import { Box, Button, Flex, IconButton, Input, Textarea, VStack } from '@chakra-ui/react';
import { EditIcon, DeleteIcon, AddIcon, CheckIcon } from '@chakra-ui/icons';
import { SimpleAppPrompt } from '@/types/api/app';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';

interface MultiLinePromptProps {
  promptList: SimpleAppPrompt[];
  disabled?: boolean;
  onChange: (updatedPrompts: SimpleAppPrompt[]) => void;
}

const MultiLinePrompt: React.FC<MultiLinePromptProps> = ({
  promptList,
  onChange,
  disabled = false
}) => {
  const [prompts, setPrompts] = useState<SimpleAppPrompt[]>([]);

  useEffect(() => {
    // Initialize prompts with promptList and add isEditing property
    setPrompts(promptList.map((prompt) => ({ ...prompt })));
  }, [promptList]);

  const handleAddPrompt = () => {
    const newPrompt = {
      title: '',
      content: '',

      isEditing: true
    };
    const updatedPrompts = [...prompts, newPrompt];
    setPrompts(updatedPrompts);
    onChange(updatedPrompts);
  };

  const handleEditPrompt = (index: number, field: keyof SimpleAppPrompt, value: string) => {
    const updatedPrompts = [...prompts];
    updatedPrompts[index] = {
      ...updatedPrompts[index],
      [field]: value
    };
    setPrompts(updatedPrompts);
    onChange(updatedPrompts);
  };

  const handleDeletePrompt = (index: number) => {
    const updatedPrompts = prompts.filter((_, i) => i !== index);
    setPrompts(updatedPrompts);
    onChange(updatedPrompts);
  };

  const handleToggleEdit = (index: number) => {
    const updatedPrompts = [...prompts];
    updatedPrompts[index].isEditing = !updatedPrompts[index].isEditing;
    setPrompts(updatedPrompts);
    onChange(updatedPrompts);
  };

  const handleBlurOrEnter = (index: number) => {
    const updatedPrompts = [...prompts];
    updatedPrompts[index].isEditing = false;
    setPrompts(updatedPrompts);
    onChange(updatedPrompts);
  };

  return (
    <VStack spacing={4} align="stretch">
      {prompts.map((prompt, index) => (
        <Box key={prompt.id} borderRadius="lg" py={1}>
          <Flex justifyContent="space-between" alignItems="center">
            {prompt.isEditing ? (
              <Input
                value={prompt.title}
                onChange={(e) => handleEditPrompt(index, 'title', e.target.value)}
                onBlur={() => handleBlurOrEnter(index)}
                bg="#F2F3F5"
                onKeyDown={(e) => e.key === 'Enter' && handleBlurOrEnter(index)}
                placeholder="标题"
              />
            ) : (
              <Box color="#4E5969">{prompt.title}</Box>
            )}
            {!disabled && (
              <Flex flex="1" justifyContent="space-between" alignItems="center">
                {!prompt.isEditing && (
                  <SvgIcon
                    name="edit"
                    ml={respDims(10)}
                    cursor="pointer"
                    aria-label="Edit"
                    onClick={() => handleToggleEdit(index)}
                    mr={2}
                  />
                )}
                <SvgIcon
                  name="trash"
                  color="#F53F3F"
                  cursor="pointer"
                  ml={respDims(10)}
                  aria-label="Delete"
                  onClick={() => handleDeletePrompt(index)}
                />
              </Flex>
            )}
          </Flex>
          <Textarea
            mt={2}
            value={prompt.content}
            bg="#f5f5f5"
            onChange={(e) => handleEditPrompt(index, 'content', e.target.value)}
            onBlur={() => handleBlurOrEnter(index)}
            onKeyDown={(e) => e.key === 'Enter' && handleBlurOrEnter(index)}
            placeholder="内容"
          />
        </Box>
      ))}
      {!disabled && (
        <Button
          leftIcon={<AddIcon />}
          onClick={handleAddPrompt}
          variant="outline"
          colorScheme="primary"
        >
          添加
        </Button>
      )}
    </VStack>
  );
};

export default MultiLinePrompt;
