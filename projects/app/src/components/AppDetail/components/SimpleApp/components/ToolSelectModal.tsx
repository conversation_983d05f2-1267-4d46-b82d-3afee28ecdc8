import React, { use<PERSON>allback, useEffect, useState } from 'react';

import { useTranslation } from 'next-i18next';
import {
  Box,
  Button,
  Flex,
  HStack,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  ModalBody,
  Modal<PERSON>ooter,
  NumberDecrementStepper,
  NumberIncrementStepper,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  Switch,
  Textarea
} from '@chakra-ui/react';
import {
  FlowNodeTemplateType,
  NodeTemplateListItemType
} from '@/fastgpt/global/core/workflow/type/node.d';
import { AddIcon } from '@chakra-ui/icons';
import { getPreviewPluginNode, getSystemPlugTemplates, getTeamPlugTemplates } from '@/api/plugin';
import { Controller, useForm } from 'react-hook-form';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import CostTooltip from '@/components/core/app/plugin/CostTooltip';
import RenderPluginInput from '@/components/core/chat/ChatContainer/PluginRunBox/components/renderPluginInput';
import { WorkflowIOValueTypeEnum } from '@/fastgpt/global/core/workflow/constants';
import MyModal from '@/components/MyModal';
import MyIcon from '@/components/LegacyIcon';
import MyTooltip from '@/components/MyTooltip';
import { useSystemStore } from '@/store/useSystemStore';
import MyBox from '@/components/common/MyBox';
import { useRequest, useRequest2 } from '@/hooks/useRequest';
import FillRowTabs from '@/components/common/Tabs/FillRowTabs';
import EmptyTip from '@/components/EmptyTip';
import Avatar from '@/components/Avatar';
import { ParentIdType } from '@/fastgpt/global/common/parentFolder/type';
import SvgIcon from '@/components/SvgIcon';

type Props = {
  selectedTools: FlowNodeTemplateType[];
  onAddTool: (tool: FlowNodeTemplateType) => void;
  onRemoveTool: (tool: NodeTemplateListItemType) => void;
};

enum TemplateTypeEnum {
  'systemPlugin' = 'systemPlugin',
  'teamPlugin' = 'teamPlugin'
}

const ToolSelectModal = ({ onClose, ...props }: Props & { onClose: () => void }) => {
  const { t } = useTranslation();

  const [templateType, setTemplateType] = useState(TemplateTypeEnum.systemPlugin);
  const [parentId, setParentId] = useState<ParentIdType>('');
  const [searchKey, setSearchKey] = useState('');

  const { data: templates = [], loading: isLoading } = useRequest2(
    async () => {
      if (templateType === TemplateTypeEnum.systemPlugin) {
        return getSystemPlugTemplates({ parentId, searchKey });
      }
    },
    {
      manual: false,
      throttleWait: 300,
      refreshDeps: [templateType, searchKey, parentId],
      errorToast: t('common:core.module.templates.Load plugin error')
    }
  );

  // const { data: paths = [] } = useRequest2(
  //   () => {
  //     if (templateType === TemplateTypeEnum.teamPlugin) return getAppFolderPath(parentId);
  //     return getSystemPluginPaths(parentId);
  //   },
  //   {
  //     manual: false,
  //     refreshDeps: [parentId]
  //   }
  // );

  useEffect(() => {
    setParentId('');
  }, [templateType, searchKey]);

  return (
    <MyModal
      isOpen
      title={t('工具调用')}
      iconSrc="core/app/toolCall"
      onClose={onClose}
      maxW={['90vw', '700px']}
      w={'700px'}
      h={['90vh', '80vh']}
    >
      {/* Header: row and search */}
      <Box px={[3, 6]} pt={4} display={'flex'} justifyContent={'space-between'} w={'full'}>
        {/* <FillRowTabs
          list={[
            {
              icon: 'core/modules/systemPlugin',
              label: t('系统'),
              value: TemplateTypeEnum.systemPlugin
            }
          ]}
          py={'5px'}
          px={'15px'}
          value={templateType}
          onChange={(e) => setTemplateType(e as TemplateTypeEnum)}
        /> */}
        <InputGroup w="100%">
          <InputRightElement h={'full'} alignItems={'center'} display={'flex'}>
            <MyIcon name={'common/searchLight'} w={'16px'} color={'myGray.500'} mr={3} />
          </InputRightElement>
          <Input
            bg={'myGray.50'}
            placeholder={t('请输入关键词，搜索工具')}
            onChange={(e) => setSearchKey(e.target.value)}
          />
        </InputGroup>
      </Box>
      {/* route components */}
      {/* {!searchKey && parentId && (
        <Flex mt={2} px={[3, 6]}>
          <FolderPath
            paths={paths}
            FirstPathDom={null}
            onClick={() => {
              setParentId(null);
            }}
          />
        </Flex>
      )} */}
      <MyBox isLoading={isLoading} mt={2} px={[3, 6]} pb={3} flex={'1 0 0'} overflowY={'auto'}>
        <RenderList
          templates={templates}
          isLoadingData={isLoading}
          setParentId={setParentId}
          showCost={templateType === TemplateTypeEnum.systemPlugin}
          {...props}
        />
      </MyBox>
    </MyModal>
  );
};

export default React.memo(ToolSelectModal);

const RenderList = React.memo(function RenderList({
  templates,
  selectedTools,
  isLoadingData,
  onAddTool,
  onRemoveTool,
  setParentId,
  showCost
}: Props & {
  templates: NodeTemplateListItemType[];
  isLoadingData: boolean;
  setParentId: React.Dispatch<React.SetStateAction<ParentIdType>>;
  showCost?: boolean;
}) {
  const { t } = useTranslation();
  const { feConfigs } = useSystemStore();
  const [configTool, setConfigTool] = useState<FlowNodeTemplateType>();
  const onCloseConfigTool = useCallback(() => setConfigTool(undefined), []);

  const {
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm();

  useEffect(() => {
    if (configTool) {
      const defaultValues = configTool.inputs.reduce(
        (acc, input) => {
          acc[input.key] = input.defaultValue;
          return acc;
        },
        {} as Record<string, any>
      );
      reset(defaultValues);
    }
  }, [configTool, reset]);

  const { mutate: onClickAdd, isLoading } = useRequest({
    mutationFn: async (template: FlowNodeTemplateType) => {
      const res = await getPreviewPluginNode({ appId: template.id });

      // All input is tool params
      if (res.inputs.every((input: any) => input.toolDescription)) {
        onAddTool(res);
      } else {
        reset();
        setConfigTool(res);
      }
    },
    errorToast: t('加载工具失败')
  });

  return templates.length === 0 && !isLoadingData ? (
    <EmptyTip text={t('没有可用的工具')} />
  ) : (
    <MyBox>
      {templates?.map((item, i) => {
        const selected = selectedTools?.some((tool) => tool.pluginId === item.id);

        return (
          <MyTooltip
            key={item.id}
            placement={'bottom'}
            shouldWrapChildren={false}
            label={
              <Box>
                <Flex alignItems={'center'}>
                  <MyIcon
                    name={item.avatar as any}
                    w={'1.75rem'}
                    objectFit={'contain'}
                    borderRadius={'sm'}
                  />
                  <Box fontWeight={'bold'} ml={2} color={'myGray.900'}>
                    {t(item.name as any)}
                  </Box>
                </Flex>
                <Box mt={2} color={'myGray.500'}>
                  {t(item.intro as any) || t('这个节点没有介绍~')}
                </Box>
                {showCost && <CostTooltip cost={item.currentCost} />}
              </Box>
            }
          >
            <Flex
              alignItems={'center'}
              position={'relative'}
              p={[4, 5]}
              _notLast={{
                borderBottomWidth: '1px',
                borderBottomColor: 'myGray.150'
              }}
              _hover={{
                bg: 'myGray.50'
              }}
            >
              <MyIcon
                name={item.avatar as any}
                w={'2rem'}
                objectFit={'contain'}
                borderRadius={'md'}
              />

              <Box ml={3} flex={'1 0 0'} color={'myGray.900'}>
                {t(item.name as any)}
              </Box>
              {/* {item.author !== undefined && (
                <Box fontSize={'xs'} mr={3}>
                  {`by ${item.author || feConfigs.systemTitle}`}
                </Box>
              )} */}
              {selected ? (
                <Button
                  size={'sm'}
                  variant={'grayBase'}
                  leftIcon={<MyIcon name={'delete'} w={'14px'} />}
                  onClick={() => onRemoveTool(item)}
                >
                  {t('移除')}
                </Button>
              ) : item.isFolder ? (
                <Button
                  size={'sm'}
                  variant={'whiteBase'}
                  leftIcon={<SvgIcon name="file" w={'14px'} />}
                  onClick={() => setParentId(item.id)}
                >
                  {t('打开')}
                </Button>
              ) : (
                <Button
                  size={'sm'}
                  variant={'whiteBase'}
                  leftIcon={<AddIcon fontSize={'10px'} />}
                  isLoading={isLoading}
                  onClick={() => onClickAdd(item as any)}
                >
                  {t('common:common.Add')}
                </Button>
              )}
            </Flex>
          </MyTooltip>
        );
      })}

      {/* Plugin input config */}
      {!!configTool && (
        <MyModal isOpen title={t('输入参数')} iconSrc="core/app/toolCall" overflow={'auto'}>
          <ModalBody>
            <HStack mb={4} spacing={1} fontSize={'sm'}>
              <MyIcon name={'common/info'} w={'1.25rem'} />
              <Box flex={1}>{t('该工具正常运行需要配置相关信息')}</Box>
              {configTool.inputExplanationUrl && (
                <Box
                  cursor={'pointer'}
                  color={'primary.500'}
                  onClick={() => window.open(configTool.inputExplanationUrl, '_blank')}
                >
                  {t('填写说明')}
                </Box>
              )}
            </HStack>
            {configTool.inputs
              .filter((item) => !item.toolDescription)
              .map((input) => {
                return (
                  <Controller
                    key={input.key}
                    control={control}
                    name={input.key}
                    rules={{
                      validate: (value) => {
                        if (input.valueType === WorkflowIOValueTypeEnum.boolean) {
                          return value !== undefined;
                        }
                        return !!value;
                      }
                    }}
                    render={({ field: { onChange, value } }) => {
                      return (
                        <RenderPluginInput
                          value={value}
                          onChange={onChange}
                          label={input.label}
                          description={input.description}
                          valueType={input.valueType}
                          placeholder={input.placeholder}
                          required={input.required}
                          min={input.min}
                          max={input.max}
                          isInvalid={errors && Object.keys(errors).includes(input.key)}
                        />
                      );
                    }}
                  />
                );
              })}
          </ModalBody>
          <ModalFooter gap={6}>
            <Button onClick={onCloseConfigTool} variant={'whiteBase'}>
              {t('common:common.Cancel')}
            </Button>
            <Button
              variant={'primary'}
              onClick={handleSubmit((data) => {
                onAddTool({
                  ...configTool,
                  inputs: configTool.inputs.map((input) => ({
                    ...input,
                    value: data[input.key] ?? input.value
                  }))
                });
                onCloseConfigTool();
              })}
            >
              {t('common:common.Confirm')}
            </Button>
          </ModalFooter>
        </MyModal>
      )}
    </MyBox>
  );
});
