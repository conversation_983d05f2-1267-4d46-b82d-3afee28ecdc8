import React, { useEffect, useMemo, useState, useTransition } from 'react';
import {
  Box,
  Flex,
  BoxProps,
  Accordion,
  AccordionItem,
  AccordionPanel,
  AccordionButton,
  AccordionIcon
} from '@chakra-ui/react';
import { SmallAddIcon } from '@chakra-ui/icons';

import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import dynamic from 'next/dynamic';

import VariableEdit from '@/components/core/app/VariableEdit';
import SettingLLMModel from '@/components/core/ai/SettingLLMModel';
import type { SettingAIDataType } from '@/fastgpt/global/core/app/type.d';
import { useContextSelector } from 'use-context-selector';
import { AppContext, SimpleAppPerm } from '@/components/AppDetail/components/context';
import { useSystemStore } from '@/store/useSystemStore';
import { useDatasetStore } from '@/store/useDatasetStore';
import MyIcon from '@/components/LegacyIcon';
import FormLabel from '@/components/common/MyBox/FormLabel';

import { TTSTypeEnum } from '@/fastgpt/web/core/app/constants';

import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { getWebLLMModel } from '@/utils/dataset';
import { respDims } from '@/utils/chakra';
import useAppSelect from '../AdvanceApp/hooks/useAppSelect';

const TTSSelect = dynamic(() => import('@/components/core/app/TTSSelect'));
const ToolChoice = dynamic(() => import('@/components/core/app/ToolChoice'));
const QGSwitch = dynamic(() => import('@/components/core/app/QGSwitch'));
const WhisperConfig = dynamic(() => import('@/components/core/app/WhisperConfig'));
const InputGuideConfig = dynamic(() => import('@/components/core/app/InputGuideConfig'));
const PromptConfig = dynamic(() => import('@/components/core/app/PromptConfig'));
const DatasetConfig = dynamic(() => import('@/components/core/app/DatasetConfig'));
const ScheduledTriggerConfig = dynamic(
  () => import('@/components/core/app/ScheduledTriggerConfig')
);
const WelcomeTextConfig = dynamic(() => import('@/components/core/app/WelcomeTextConfig'));
const FileSelectConfig = dynamic(() => import('@/components/core/app/FileSelect'));

const BoxStyles: BoxProps = {
  px: [4, 6],
  py: '16px',
  borderBottomWidth: '1px',
  borderBottomColor: '#E5E7EB'
};
const LabelStyles: BoxProps = {
  w: ['60px', '100px'],
  whiteSpace: 'nowrap',
  flexShrink: 0,
  fontSize: 'sm',
  color: 'myGray.900'
};

const EditForm = ({
  appForm,
  setAppForm
}: {
  appForm: AppSimpleEditFormTypeMegre;
  setAppForm: React.Dispatch<React.SetStateAction<AppSimpleEditFormTypeMegre>>;
}) => {
  const { t } = useTranslation();

  const { originAppDetail, permission, isAdmin } = useContextSelector(AppContext, (v) => v);
  const selectedModel = getWebLLMModel(appForm.aiSettings.model);
  return (
    <>
      <Box>
        {/* ai */}
        <Box {...BoxStyles}>
          <Flex alignItems={'center'}>
            <MyIcon name={'core/app/simpleMode/ai'} w={'20px'} color="primary.500" />
            <FormLabel ml={2} flex={1} fontWeight="500">
              {t('AI 配置')}
            </FormLabel>
          </Flex>
          <Flex alignItems={'center'} mt={5}>
            <Box {...LabelStyles}>{t('common:core.ai.Model')}</Box>
            <Box flex={'1 0 0'}>
              <SettingLLMModel
                bg="myGray.50"
                llmModelType={'all'}
                defaultData={{
                  model: appForm.aiSettings.model,
                  temperature: appForm.aiSettings.temperature,
                  maxToken: appForm.aiSettings.maxToken,
                  maxHistories: appForm.aiSettings.maxHistories
                }}
                onChange={({ model, temperature, maxToken, maxHistories }: SettingAIDataType) => {
                  setAppForm((state) => ({
                    ...state,
                    aiSettings: {
                      ...state.aiSettings,
                      model,
                      temperature,
                      maxToken,
                      maxHistories: maxHistories ?? 6
                    }
                  }));
                }}
              />
            </Box>
          </Flex>
          <Box pt={4}>
            <PromptConfig
              value={appForm}
              originAppDetail={originAppDetail}
              onChange={(update) => setAppForm(update)}
            />
          </Box>
        </Box>

        {/* File select */}
        <Box {...BoxStyles}>
          <FileSelectConfig
            forbidVision={!selectedModel?.vision}
            value={appForm.filesList}
            onChange={(e) => {
              setAppForm((state) => ({
                ...state,
                filesList: e
              }));
            }}
          />
        </Box>
        {/* dataset */}
        <Box {...BoxStyles}>
          <DatasetConfig value={appForm} onChange={(update) => setAppForm(update)} />
        </Box>
        {/* tool choice */}
        <Box {...BoxStyles}>
          <ToolChoice
            selectedTools={appForm.selectedTools}
            onChange={(update) => setAppForm(update)}
          />
        </Box>
        <Accordion
          allowToggle
          borderRadius="12px"
          borderTopColor="transparent"
          borderBottomColor="transparent"
        >
          <AccordionItem>
            {({ isExpanded }) => (
              <>
                <AccordionButton display="flex" justifyContent="space-between" px={[4, 6]}>
                  <FormLabel fontWeight="500" py={respDims(10)}>
                    高级配置
                  </FormLabel>
                  <AccordionIcon
                    transform={isExpanded ? 'rotate(0deg)' : 'rotate(-90deg)'}
                    transition="transform 0.2s"
                    color="#A8ABB2"
                    fontSize={respDims(32)}
                  />
                </AccordionButton>
                <AccordionPanel p={0}>
                  {/* variable */}
                  <Box {...BoxStyles}>
                    <VariableEdit
                      variables={appForm.chatConfig.variables}
                      onChange={(e) => {
                        setAppForm((state) => ({
                          ...state,
                          chatConfig: {
                            ...state.chatConfig,
                            variables: e
                          }
                        }));
                      }}
                    />
                  </Box>
                  {/* welcome */}
                  <Box {...BoxStyles}>
                    <WelcomeTextConfig
                      value={appForm.chatConfig.welcomeText}
                      onChange={(e) => {
                        setAppForm((state) => ({
                          ...state,
                          chatConfig: {
                            ...state.chatConfig,
                            welcomeText: e.target.value
                          }
                        }));
                      }}
                    />
                  </Box>
                  {/* tts */}
                  <Box {...BoxStyles}>
                    <TTSSelect
                      value={appForm.chatConfig.ttsConfig}
                      onChange={(e) => {
                        setAppForm((state) => ({
                          ...state,
                          chatConfig: {
                            ...state.chatConfig,
                            ttsConfig: e
                          }
                        }));
                      }}
                    />
                  </Box>
                  {/* whisper */}
                  <Box {...BoxStyles}>
                    <WhisperConfig
                      isOpenAudio={appForm.chatConfig.ttsConfig?.type !== TTSTypeEnum.none}
                      value={appForm.chatConfig.whisperConfig}
                      onChange={(e) => {
                        setAppForm((state) => ({
                          ...state,
                          chatConfig: {
                            ...state.chatConfig,
                            whisperConfig: e
                          }
                        }));
                      }}
                    />
                  </Box>
                  {/* question guide */}
                  <Box {...BoxStyles}>
                    <QGSwitch
                      isChecked={appForm.chatConfig.questionGuide}
                      onChange={(e) => {
                        setAppForm((state) => ({
                          ...state,
                          chatConfig: {
                            ...state.chatConfig,
                            questionGuide: e.target.checked
                          }
                        }));
                      }}
                    />
                  </Box>
                  {/* question tips */}
                  <Box {...BoxStyles}>
                    <InputGuideConfig
                      appId={originAppDetail._id}
                      value={appForm.chatConfig.chatInputGuide}
                      onChange={(e) => {
                        setAppForm((state) => ({
                          ...state,
                          chatConfig: {
                            ...state.chatConfig,
                            chatInputGuide: e
                          }
                        }));
                      }}
                    />
                  </Box>
                  {/* timer trigger */}
                  <Box {...BoxStyles} borderBottom={'none'}>
                    <ScheduledTriggerConfig
                      value={appForm.chatConfig.scheduledTriggerConfig}
                      onChange={(e) => {
                        setAppForm((state) => ({
                          ...state,
                          chatConfig: {
                            ...state.chatConfig,
                            scheduledTriggerConfig: e
                          }
                        }));
                      }}
                    />
                  </Box>
                </AccordionPanel>
              </>
            )}
          </AccordionItem>
        </Accordion>
      </Box>
    </>
  );
};

export default React.memo(EditForm);
