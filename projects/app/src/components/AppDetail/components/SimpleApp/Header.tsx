import React, { useCallback, useMemo, useState } from 'react';
import { useContextSelector } from 'use-context-selector';
import { AppContext } from '../context';
import { Box, Button, Flex, IconButton } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { AppSimpleEditFormType } from '@/fastgpt/global/core/app/type';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { TabEnum } from '../context';
import { compareWorkflow, form2AppWorkflow } from '@/fastgpt/web/core/app/utils';
import { useSystemStore } from '@/store/useSystemStore';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { compareCustomAttrs } from '@/utils/app';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import SaveButton from './SaveButton';
import { MessageBox, promisifyConfirm, promisifyWarning } from '@/utils/ui/messageBox';

const Header = ({
  appForm,
  setAppForm
}: {
  appForm: AppSimpleEditFormTypeMegre;
  setAppForm: React.Dispatch<React.SetStateAction<AppSimpleEditFormTypeMegre>>;
}) => {
  const router = useRouter();
  const { isPublished } = useContextSelector(AppContext, (v) => v);

  // 只保存
  return (
    <Box display={['block', 'flex']} p="8px 0" gap={1}>
      <Box pr={[0, 1]} minW={['auto', '580px']} flex={'1'}>
        <Flex
          alignItems="center"
          border=" 1px solid #E5E7EB"
          cursor="pointer"
          borderRadius={respDims(8)}
          justifyContent="center"
          w={respDims(36)}
          h={respDims(36)}
          bg="#fff"
          color="#303133"
          onClick={() => {
            !isPublished
              ? promisifyConfirm({
                  title: '未保存数据',
                  content: '您的更新未保存，确认放弃当前更改？'
                }).then(() => {
                  router.back();
                })
              : router.back();
          }}
        >
          <SvgIcon name="chevronLeft" w={respDims('16fpx')} h={respDims('16fpx')} />
        </Flex>
      </Box>
      {
        <Flex
          flex={'2 0 0'}
          w={0}
          fontSize={respDims(20, 18)}
          color="#030712"
          fontWeight="600"
          alignItems="center"
        >
          编辑简易应用
        </Flex>
      }
    </Box>
    // <Flex justify="space-between" p="8px 0">

    //   {/* <SaveButton appForm={appForm} setAppForm={setAppForm}></SaveButton> */}
    // </Flex>
  );
};

export default Header;
