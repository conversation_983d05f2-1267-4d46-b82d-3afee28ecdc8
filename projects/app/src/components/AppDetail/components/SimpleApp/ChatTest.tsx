import { Box, Flex, Icon<PERSON>utton } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React, { useEffect } from 'react';
import { useSafeState } from 'ahooks';
import { useContextSelector } from 'use-context-selector';
import { AppContext } from '../context';
import { useChatTest } from '../useChatTest';
import MyTooltip from '@/components/MyTooltip';
import MyIcon from '@/components/LegacyIcon';
import { AppSimpleEditFormType } from '@/fastgpt/global/core/app/type';
import { useDatasetStore } from '@/store/useDatasetStore';
import { form2AppWorkflow } from '@/fastgpt/web/core/app/utils';
import ChatFunctionTip from '@/components/core/app/Tip';
import { AppSimpleEditFormTypeMegre } from '@/types/app';

const ChatTest = ({ appForm }: { appForm: AppSimpleEditFormTypeMegre }) => {
  const { t } = useTranslation();

  const { originAppDetail } = useContextSelector(AppContext, (v) => v);
  // form2AppWorkflow dependent myDatasets
  const { myDatasets } = useDatasetStore();

  const [workflowData, setWorkflowData] = useSafeState({
    nodes: originAppDetail.modules || [],
    edges: originAppDetail.edges || []
  });

  useEffect(() => {
    const { nodes, edges } = form2AppWorkflow(appForm, t);
    setWorkflowData({ nodes, edges });
  }, [appForm, setWorkflowData, myDatasets, t]);

  const { chatBoxRef, ChatContainer } = useChatTest({
    ...workflowData,
    chatConfig: appForm.chatConfig,
    filesList: appForm.filesList
  });
  return (
    <Flex position={'relative'} flexDirection={'column'} h={'100%'} py={4}>
      <Flex px={[2, 5]}>
        <Box fontSize={['md', 'lg']} fontWeight={'bold'} flex={1} color={'myGray.900'}>
          {t('调试预览')}
          {/* <ChatFunctionTip type={'test'} /> */}
        </Box>

        <MyTooltip label={t('common:core.chat.Restart')}>
          <IconButton
            className="chat"
            size={'smSquare'}
            icon={<MyIcon name={'common/clearLight'} w={'14px'} />}
            variant={'whiteDanger'}
            borderRadius={'md'}
            aria-label={'delete'}
            onClick={(e) => {
              e.stopPropagation();

              chatBoxRef.current?.resetHistory([]);
              chatBoxRef.current?.resetVariables();
            }}
          />
        </MyTooltip>
      </Flex>
      <Box flex={1} overflow="hidden">
        <ChatContainer />
      </Box>
    </Flex>
  );
};

export default React.memo(ChatTest);
