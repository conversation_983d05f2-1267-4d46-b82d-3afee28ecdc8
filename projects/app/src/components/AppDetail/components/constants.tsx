import { respDims } from '@/utils/chakra';
import { i18nT } from '@/utils/tools';
import { BoxProps, FlexProps } from '@chakra-ui/react';
export const cardStyles: BoxProps = {
  borderRadius: 'lg',
  // overflow: 'hidden',
  bg: 'white'
};

export const itemStyles: BoxProps = {
  alignItems: 'center',
  bg: '#FFFFFF',
  borderRadius: respDims(8),
  position: 'relative',
  border: ' 1px solid #E5E7EB',
  py: respDims(10),
  px: respDims(12),
  css: {
    '&:hover': {
      '.close_icon': {
        display: 'block'
      }
    }
  }
};

export const workflowBoxStyles: FlexProps = {
  position: 'fixed',
  top: 0,
  right: 0,
  bottom: 0,
  left: 0,
  flexDirection: 'column',
  zIndex: 200,
  bg: 'myGray.100'
};

export const publishStatusStyle = {
  unPublish: {
    colorSchema: 'adora' as any,
    text: i18nT('common:core.app.not_published')
  },
  published: {
    colorSchema: 'green' as any,
    text: i18nT('common:core.app.have_publish')
  }
};

export const actionButtonStyle: BoxProps = {
  justifyContent: 'center',
  alignItems: 'center',
  bg: '#F2F3F5',
  borderRadius: '50px',
  color: '#4E5969',
  cursor: 'pointer',
  px: respDims(17),
  py: respDims(5),
  border: '1px solid transparent',
  fontSize: respDims(12, 10),
  _hover: {
    color: 'primary.500',
    bgColor: '#E5EDFF'
  }
};
export const closeIconStyle = {
  position: 'absolute',
  right: '-10px',
  top: '-10px',
  cursor: 'pointer',
  w: respDims(30),
  display: 'none',
  h: respDims(30)
};

export default function Dom() {
  return <></>;
}
