import { AuthTypeEnum, PageTypeEnum } from '@/components/FastGPT/constants';
import FastGPTWrapper from '@/components/FastGPTWrapper';
import { DataSource } from '@/constants/common';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { getToken } from '@/utils/auth';
import { serviceSideProps } from '@/utils/i18n';

const AdvanceApp = ({
  appType,
  finalAppId,
  authType = AuthTypeEnum.user
}: {
  appType?: AppTypeEnum;
  finalAppId: string;
  authType?: AuthTypeEnum;
}) => {
  const huayunToken = getToken();
  return (
    <FastGPTWrapper
      fullscreen={appType === AppTypeEnum.workflow}
      options={{
        pageType: PageTypeEnum.appDetail,
        authType: authType,
        appId: finalAppId,
        huayunToken: huayunToken
      }}
    />
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      appType: context.query?.appType || '',
      finalAppId: context.query?.finalAppId || '',
      ...(await serviceSideProps(context))
    }
  };
}

export default AdvanceApp;
