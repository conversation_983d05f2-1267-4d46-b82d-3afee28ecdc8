import React, { useState, useMemo } from 'react';
import {
  Box,
  Flex,
  Button,
  InputGroup,
  Input,
  InputRightElement,
  Image,
  Center,
  ModalFooter,
  ModalBody,
  Spinner,
  Text,
  Grid
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';

import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { DataSource, DataSourceMap, LOGO_ICON } from '@/constants/common';
import { AppListItemType } from '@/types/api/app';
import { Toast } from '@/utils/ui/toast';

import MyModal from '@/components/MyModal';
import { useToast } from '@/hooks/useToast';
import { getAppList } from '@/api/workflow';
import MyTooltip from '@/components/MyTooltip';
import { getSelectAppList } from '@/api/app';

const AppListModal = ({
  onClose,
  onSelect,
  resolve,
  reject
}: {
  onClose?: () => void;
  onSuccess?: () => void;
  onSelect: (selectApp: AppListItemType) => void;
  resolve?: (res: any) => void;
  reject?: (rej?: any) => void;
}) => {
  const router = useRouter();
  const [filterSource, setFilterSource] = useState<DataSource | 'all'>('all');
  const [filterText, setFilterText] = useState('');
  const [selectApp, setSelectApp] = useState<AppListItemType | null>(null);
  const { toast } = useToast();
  const appDetail = useMemo(() => {
    try {
      return JSON.parse(decodeURIComponent(router.query.appDetail as string)) as AppListItemType;
    } catch (error) {
      return {} as AppListItemType;
    }
  }, [router.query]);

  const isManange = useMemo(() => router.query.isAdmin == '1', [router.query]);

  /* 加载模型 */
  const { isFetching, data: appList } = useQuery(
    ['loadApps'],
    () => {
      return getSelectAppList(
        {
          tenantId: appDetail.tenantId,
          tmbId: appDetail.tmbId
        },
        appDetail.source,
        isManange
      );
    },
    {}
  );

  const presentApps = useMemo(() => {
    return (
      appList?.filter((app) => {
        let textFilterFlag = !filterText || app.name.includes(filterText);

        if (filterSource === 'all') {
          return textFilterFlag;
        }
        return app.source === filterSource && textFilterFlag;
      }) || []
    );
  }, [appList, filterSource, filterText]);

  const presentSources: { name: string; source: DataSource | 'all' }[] = useMemo(() => {
    const sources: { name: string; source: DataSource | 'all' }[] = [
      { name: '全部', source: 'all' }
    ];
    if (appDetail.source === DataSource.Offical) {
      sources.push({ name: DataSourceMap[DataSource.Offical].label, source: DataSource.Offical });
    } else if (appDetail.source === DataSource.Tenant) {
      sources.push(
        { name: DataSourceMap[DataSource.Offical].label, source: DataSource.Offical },
        { name: DataSourceMap[DataSource.Tenant].label, source: DataSource.Tenant }
      );
    } else if (appDetail.source === DataSource.Personal) {
      sources.push(
        { name: DataSourceMap[DataSource.Offical].label, source: DataSource.Offical },
        { name: DataSourceMap[DataSource.Tenant].label, source: DataSource.Tenant },
        { name: DataSourceMap[DataSource.Personal].label, source: DataSource.Personal }
      );
    }
    return sources;
  }, [appDetail.source]);

  return (
    <MyModal
      title={
        <Flex justifyContent="space-between" alignItems="center" w="100%" mt={respDims(20)}>
          <Box>{'应用中心'}</Box>
          <Flex alignItems="center" justifyContent="space-between">
            <InputGroup mr={respDims(10)}>
              <Input
                bgColor="rgba(255,255,255,0.55)"
                placeholder="搜索应用"
                value={filterText}
                h={respDims(36, 34)}
                onChange={(e) => setFilterText(e.target.value)}
              />
              <InputRightElement h="100%">
                <SvgIcon name="search" w={respDims(20)} h={respDims(20)} />
              </InputRightElement>
            </InputGroup>
          </Flex>
        </Flex>
      }
      isOpen
      onClose={onClose}
      isCentered={true}
      minW={['50vw']}
      borderRadius="18px"
      hideCloseButton
      h={respDims(650)}
      bgImage="/imgs/workflow/modal_bg.png"
      bgSize="100% 100%"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent',
        borderBottom: 'none'
      }}
    >
      <ModalBody padding={respDims(0)}>
        <Flex h={respDims(480)} flexDir="column">
          <Flex
            px={respDims(32)}
            direction="row"
            align="center"
            w="100%"
            justifyContent="flex-start"
          >
            <Flex flex="1" justifyContent="flex-start" align="center">
              {presentSources.map((it) => (
                <Flex
                  key={it.source}
                  mr={respDims(32)}
                  py={respDims(4)}
                  px={respDims(0)}
                  position="relative"
                  alignItems="center"
                  justifyContent="center"
                  {...(it.source === filterSource
                    ? {
                        color: 'primary.500',
                        _after: {
                          position: 'absolute',
                          content: '""',
                          left: '0',
                          right: '0',
                          bottom: '-1px',
                          w: '100%',
                          height: '2px',
                          bgColor: 'primary.500'
                        }
                      }
                    : {
                        color: '#606266'
                      })}
                  fontSize="14px"
                  fontWeight="bold"
                  cursor="pointer"
                  onClick={() => setFilterSource(it.source)}
                >
                  {it.name}
                </Flex>
              ))}
            </Flex>
          </Flex>
          <Box height={respDims(1)} w="100%" bg="rgba(250, 250, 251, 1)" my={respDims(10)}></Box>

          <Box overflowY="auto" flex="1" h="0" position="relative">
            <Grid
              p={respDims(32)}
              pt={respDims(10)}
              gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)']}
              gridGap={respDims(16)}
            >
              {isFetching ? (
                <Center h="100%">
                  <Spinner />
                </Center>
              ) : (
                presentApps.map((app) => (
                  <Flex
                    key={app.id}
                    justifyContent="flex-start"
                    p={respDims(16)}
                    borderRadius="md"
                    border="1px solid"
                    borderColor={selectApp?.id === app?.id ? 'primary.600' : '#fff'}
                    bg="#fff"
                    onClick={() => setSelectApp(app)}
                  >
                    <Image
                      w={respDims(42)}
                      h={respDims(42)}
                      src={app.avatarUrl || LOGO_ICON}
                      alt=""
                    />

                    <Box ml={respDims(20)}>
                      <Text fontWeight="medium" mb={respDims(10)}>
                        {app.name}
                      </Text>
                      <Box
                        flexDirection="column"
                        flex="1"
                        overflow="hidden"
                        w="100%"
                        whiteSpace="nowrap"
                        width={respDims(200)}
                      >
                        <MyTooltip overflowOnly>
                          <Box
                            color="gray.500"
                            textOverflow="ellipsis"
                            overflow="hidden"
                            fontSize="sm"
                          >
                            {app.intro || '暂无介绍'}
                          </Box>
                        </MyTooltip>
                      </Box>

                      <Flex alignItems="center" mt={respDims(6)}>
                        {DataSourceMap[app.source].icon && (
                          <SvgIcon
                            mr="6px"
                            name={DataSourceMap[app.source].icon!}
                            w={respDims(14)}
                            h={respDims(14)}
                          />
                        )}
                        <Box fontSize={respDims(14, 12)} color="#909399" fontWeight="400">
                          {DataSourceMap[app.source].label}
                        </Box>
                      </Flex>
                    </Box>
                  </Flex>
                ))
              )}
            </Grid>
          </Box>
        </Flex>
      </ModalBody>
      <ModalFooter>
        <Button
          variant={'grayBase'}
          mr={3}
          onClick={() => {
            reject && reject();
            onClose && onClose();
          }}
        >
          取消
        </Button>
        <Button
          colorScheme="blue"
          onClick={() => {
            if (selectApp) {
              onSelect && onSelect(selectApp);
              resolve && resolve(selectApp);
              onClose && onClose();
            } else {
              toast({
                status: 'warning',
                title: '请选择一个应用'
              });
            }
          }}
        >
          选择
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default AppListModal;
