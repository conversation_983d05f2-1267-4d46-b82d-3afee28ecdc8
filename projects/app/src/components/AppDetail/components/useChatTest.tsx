import React, { use<PERSON>allback, useEffect, useRef } from 'react';
import { ChatBoxRef, StartChatFnProps } from '@/components/ChatBox/type';

import {
  getDefaultEntryNodeIds,
  getMaxHistoryLimitFromNodes,
  initWorkflowEdgeStatus,
  storeNodes2RuntimeNodes
} from '@/fastgpt/global/core/workflow/runtime/utils';
import { useMemoizedFn } from 'ahooks';
import { useContextSelector } from 'use-context-selector';
import { AppContext } from './context';
import { StoreNodeItemType } from '@/fastgpt/global/core/workflow/type/node';
import { StoreEdgeItemType } from '@/fastgpt/global/core/workflow/type/edge';
import { FlowNodeTypeEnum } from '@/fastgpt/global/core/workflow/node/constant';
import { AppChatConfigType } from '@/fastgpt/global/core/app/type';
import { useUserStore } from '@/store/useUserStore';
import { streamFetch } from '@/utils/fetch';
import { ChatBoxMode } from '@/components/ChatBox/constant';
import ChatBox from '@/components/ChatBox';
import { TenantAppKnowledgeFile } from '@/types/api/app';
import { getChatBackGroundFiles } from '@/utils/app';

export const useChatTest = ({
  nodes,
  edges,
  chatConfig,
  filesList
}: {
  nodes: StoreNodeItemType[];
  edges: StoreEdgeItemType[];
  chatConfig: AppChatConfigType;
  filesList: TenantAppKnowledgeFile[];
}) => {
  const { userInfo } = useUserStore();
  const { originAppDetail, isAdmin, clientAppDetail } = useContextSelector(AppContext, (v) => v);
  const chatBoxRef = useRef<ChatBoxRef>(null);
  const startChat = useMemoizedFn(
    async ({ messages, controller, generatingMessage, variables }: StartChatFnProps) => {
      /* get histories */
      const historyMaxLen = getMaxHistoryLimitFromNodes(nodes);

      // 流请求，获取数据
      const { responseText, responseData } = await streamFetch({
        url: '/huayun-ai/system/fast/chatTest',
        data: {
          // Send histories and user messages
          messages: messages.slice(-historyMaxLen - 2),
          nodes: storeNodes2RuntimeNodes(nodes, getDefaultEntryNodeIds(nodes)),
          edges: initWorkflowEdgeStatus(edges),
          variables,
          appId: originAppDetail._id,
          appName: `调试-${originAppDetail.name}`,
          files: await getChatBackGroundFiles(filesList),
          chatConfig
        },
        onMessage: generatingMessage,
        abortCtrl: controller
      });

      return { responseText, responseData };
    }
  );

  const pluginInputs =
    nodes.find((node) => node.flowNodeType === FlowNodeTypeEnum.pluginInput)?.inputs || [];

  // chatHistories={chatRecords}
  // setChatHistories={setChatRecords}
  // variablesForm={variablesForm}
  const CustomChatContainer = useCallback(
    () => (
      <ChatBox
        ref={chatBoxRef}
        height="100%"
        w="100%"
        overflow="hidden"
        showEmptyIntro
        mode={ChatBoxMode.Test}
        appId={clientAppDetail.id}
        appAvatar={originAppDetail.avatar}
        chatConfig={chatConfig}
        userAvatar={userInfo?.avatar}
        showMarkIcon
        onStartChat={startChat}
        onDelMessage={(e: { contentId: string }): any => {}}
      />
    ),
    [originAppDetail, userInfo, chatConfig]
  );

  return {
    chatBoxRef,
    ChatContainer: CustomChatContainer
  };
};

export default function Dom() {
  return <></>;
}
