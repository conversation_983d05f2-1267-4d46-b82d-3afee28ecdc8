import { Di<PERSON>atch, ReactN<PERSON>, SetStateAction, use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { createContext } from 'use-context-selector';
import {
  getAppDetailById,
  getAppLatestVersion,
  deleteClientApp,
  updateClientApp,
  updateApp,
  deleteAppModel,
  getAppDetail,
  getOriginAppDetail,
  publishApp
} from '@/api/app';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { AppChatConfigType, AppDetailType } from '@/fastgpt/global/core/app/type';
import {
  AppDetailInfo,
  AppDetailMerge,
  AppUpdateParams,
  PostPublishAppProps
} from '@/types/api/app';
import dynamic from 'next/dynamic';
import { useDisclosure } from '@chakra-ui/react';
import { getDefaultAppForm } from '@/fastgpt/global/core/app/utils';

import type { StoreNodeItemType } from '@/fastgpt/global/core/workflow/type/node';
import type { StoreEdgeItemType } from '@/fastgpt/global/core/workflow/type/edge';
import { useRequest2 } from '@/hooks/useRequest';
import { useConfirm } from '@/hooks/useConfirm';
import { defaultApp } from '@/fastgpt/web/core/app/constants';
import { IsStructuredPrompt } from '@/constants/api/app';
import { AppSimpleEditFormTypeMegre } from '@/types/app';
import { compareWorkflow } from '@/fastgpt/web/core/workflow/utils';
import { compareCustomAttrs } from '@/utils/app';
import { TenantAppUpdateParams } from '@/types/api/tenant/app';
import { form2AppWorkflow } from '@/fastgpt/web/core/app/utils';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { DataSource } from '@/constants/common';
import { useBeforeunload } from '@/hooks/useBeforeunload';
// import { form2AppWorkflow } from '@/fastgpt/web/core/app/utils';

export enum TabEnum {
  'appEdit' = 'appEdit',
  'publish' = 'publish',
  'logs' = 'logs'
}

export enum SimpleAppPerm {
  UnPublish = 'unpublish',
  Publish = 'publish',
  Self = 'Self'
}

export type SettingMode = 'sideSetting' | 'fullSetting';

type AppContextType = {
  appId: string;
  currentTab: TabEnum;
  route2Tab: (currentTab: TabEnum) => void;
  originAppDetail: AppDetailType;
  setOriginAppDetail: Dispatch<SetStateAction<AppDetailType>>;
  clientAppDetail: Partial<AppDetailInfo>;
  setClientAppDetail: Dispatch<SetStateAction<Partial<AppDetailInfo>>>;

  loadingApp: boolean;
  updateAppDetail: (data: AppUpdateParams) => Promise<void>;
  onDelApp: () => void;
  permission: SimpleAppPerm;
  onPublish: (data: AppUpdateParams & TenantAppUpdateParams) => Promise<void>;
  appLatestVersion:
    | {
        nodes: StoreNodeItemType[];
        edges: StoreEdgeItemType[];
        chatConfig: AppChatConfigType;
      }
    | undefined;
  reloadAppLatestVersion: () => void;
  reloadApp: () => void;
  isAdmin: boolean;
  appForm: AppSimpleEditFormTypeMegre;
  setAppForm: Dispatch<SetStateAction<AppSimpleEditFormTypeMegre>>;
  isPublished: boolean;
};

export const AppContext = createContext<AppContextType>({
  appId: '',
  isAdmin: false,
  currentTab: TabEnum.appEdit,
  route2Tab: function (currentTab: TabEnum): void {
    throw new Error('Function not implemented.');
  },
  originAppDetail: defaultApp,
  loadingApp: false,
  updateAppDetail: function (data: AppUpdateParams): Promise<void> {
    throw new Error('Function not implemented.');
  },
  setOriginAppDetail: function (value: SetStateAction<AppDetailType>): void {
    throw new Error('Function not implemented.');
  },
  setClientAppDetail: function (value: SetStateAction<Partial<AppDetailInfo>>): void {
    throw new Error('Function not implemented.');
  },
  clientAppDetail: {},
  onDelApp: function (): void {
    throw new Error('Function not implemented.');
  },
  onPublish: function (data: AppUpdateParams & TenantAppUpdateParams): Promise<void> {
    throw new Error('Function not implemented.');
  },
  appLatestVersion: undefined,
  permission: SimpleAppPerm.Self,
  reloadAppLatestVersion: function (): void {
    throw new Error('Function not implemented.');
  },
  reloadApp: function (): void {
    throw new Error('Function not implemented.');
  },
  appForm: {
    ...getDefaultAppForm(),
    promptList: [],
    isStructuredPrompt: IsStructuredPrompt.StructuredPrompt,
    filesList: []
  },
  setAppForm: function (value: SetStateAction<AppSimpleEditFormTypeMegre>): void {
    throw new Error('Function not implemented.');
  },
  isPublished: false
});

const AppContextProvider = ({
  children,
  finalAppId: appId,
  isAdmin,
  permission,
  id,
  settingMode
}: {
  children: ReactNode;
  finalAppId: string;
  isAdmin: '1' | '0';
  settingMode?: SettingMode;
  id: string;
  permission: SimpleAppPerm;
}) => {
  const { t } = useTranslation();
  const router = useRouter();

  const route2Tab = useCallback((currentTab: `${TabEnum}`) => {}, [router]);

  const [originAppDetail, setOriginAppDetail] = useState<AppDetailType>({
    ...defaultApp
  });

  const [clientAppDetail, setClientAppDetail] = useState<Partial<AppDetailInfo>>({
    promptList: [],
    isStructuredPrompt: IsStructuredPrompt.StructuredPrompt
  });

  const [appForm, setAppForm] = useState<AppSimpleEditFormTypeMegre>({
    ...getDefaultAppForm(),
    promptList: [],
    isStructuredPrompt: IsStructuredPrompt.StructuredPrompt,
    filesList: []
  });

  const { loading: loadingApp, runAsync: reloadApp } = useRequest2(
    async () => {
      if (appId) {
        const [clientDetail, originDetail] = await Promise.all([
          getAppDetail(id, isAdmin == '1'),
          getOriginAppDetail(appId)
        ]);
        setClientAppDetail(clientDetail);
        setOriginAppDetail(originDetail);
      } else {
        setClientAppDetail(defaultApp);
        setOriginAppDetail(defaultApp);
      }
    },
    {
      manual: false,
      refreshDeps: [appId],
      errorToast: t('common:core.app.error.Get app failed'),
      onError(err: any) {
        router.replace('/app/list');
      }
    }
  );
  const { data: appLatestVersion, run: reloadAppLatestVersion } = useRequest2(
    () => getAppLatestVersion({ appId }),
    {
      manual: false
    }
  );

  const { runAsync: updateAppDetail } = useRequest2(async (data: AppUpdateParams) => {
    // await ()=>{}();
    // setAppDetail((state) => ({
    //   ...state,
    //   ...data,
    //   modules: data.nodes || state.modules
    // }));
  });

  const { runAsync: onPublish } = useRequest2(
    async (data: AppUpdateParams & TenantAppUpdateParams) => {
      await publishApp(data, isAdmin === '1');
      // setOriginAppDetail((state) => ({
      //   ...state,
      //   ...data,
      //   modules: data.nodes || state.modules
      // }));
      setClientAppDetail((state) => ({
        ...state,
        promptList: data.promptList,
        isStructuredPrompt: data.isStructuredPrompt,
        filesList: data.filesList
      }));
      reloadAppLatestVersion();
    },
    {
      successToast: settingMode == 'fullSetting' ? t('保存成功') : undefined
    }
  );

  const { openConfirm: openConfirmDel, ConfirmModal: ConfirmDelModal } = useConfirm({
    content: t('确认删除该应用及其所有聊天记录？'),
    type: 'delete'
  });
  const { runAsync: deleteApp } = useRequest2(
    async () => {
      if (!clientAppDetail) return Promise.reject('Not load app');
      return deleteAppModel(
        { id: clientAppDetail.id!, tmbId: clientAppDetail.tmbId! },
        isAdmin == '1'
      );
    },
    {
      onSuccess() {
        router.replace(`/app/list`);
      },
      successToast: t('common:common.Delete Success'),
      errorToast: t('common:common.Delete Failed')
    }
  );
  const onDelApp = useCallback(() => openConfirmDel(deleteApp)(), [deleteApp, openConfirmDel]);

  const isPublished = useMemo(() => {
    const data = form2AppWorkflow(appForm, t);

    return (
      compareWorkflow(
        {
          nodes: originAppDetail.modules,
          edges: [],
          chatConfig: originAppDetail.chatConfig
        },
        {
          nodes: data.nodes,
          edges: [],
          chatConfig: data.chatConfig
        }
      ) && compareCustomAttrs(clientAppDetail, appForm)
    );
  }, [originAppDetail, clientAppDetail, appForm, t]);

  useBeforeunload({
    callback: () => {},
    tip: t('您的更新未保存，确认放弃当前更改？'),
    enabled: !isPublished
  });

  const contextValue: AppContextType = {
    appId,
    permission,
    currentTab: TabEnum.appEdit,
    route2Tab,
    originAppDetail,
    setOriginAppDetail,
    clientAppDetail,
    setClientAppDetail,
    loadingApp,
    appLatestVersion,
    updateAppDetail,
    onDelApp,
    onPublish,
    reloadAppLatestVersion,
    reloadApp,
    isAdmin: isAdmin === '1',
    appForm,
    setAppForm,
    isPublished
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}

      <ConfirmDelModal />
    </AppContext.Provider>
  );
};

export default AppContextProvider;
