import React, { Fragment, useEffect, useState } from 'react';
import { Box } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import MenuItem from './MenuItem'; // 导入 MenuItem 组件
import { MenuProps } from './type';

const Menu: React.FC<MenuProps> = ({ groups, activeKey, expandedKeys, onExpand, onClickItem }) => {
  const [innerExpandedKeys, setInnerExpandedKeys] = useState(expandedKeys || []);

  const onInnerExpand = (keys: string[]) => {
    setInnerExpandedKeys(keys);
    onExpand?.(keys);
  };

  useEffect(() => setInnerExpandedKeys(expandedKeys || []), [expandedKeys]);

  return (
    <Box>
      {groups.map((group, index) => (
        <Fragment key={index}>
          {!!group.name && (
            <Box py={respDims(16)} color="#909399" fontSize="12px">
              {group.name}
            </Box>
          )}

          {group.items.map((item) => (
            <MenuItem
              key={item.key}
              item={item}
              activeKey={activeKey}
              expandedKeys={innerExpandedKeys}
              onExpand={onInnerExpand}
              onClickItem={onClickItem}
            />
          ))}
        </Fragment>
      ))}
    </Box>
  );
};

export default Menu;
