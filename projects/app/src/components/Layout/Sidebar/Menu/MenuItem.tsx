import React, { useMemo } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { MenuItemProps, MenuItemType } from './type';
import AdvIcon from '@/components/AdvIcon';

interface ExtendedMenuItemProps extends MenuItemProps {
  level?: number;
}

const MenuItem: React.FC<ExtendedMenuItemProps> = ({
  level = 0,
  item,
  activeKey,
  activePathKeys,
  expandedKeys,
  onExpand,
  onClickItem
}) => {
  const isActive = item.key === activeKey;

  const hasActiveChild = useMemo(
    () => !!activePathKeys?.includes(item.key),
    [activePathKeys, item.key]
  );

  const isExpanded = useMemo(() => expandedKeys.includes(item.key), [expandedKeys, item.key]);

  const hasChildren = !!item.children?.length;

  const icon = (isActive && item.activeIcon) || item.icon;

  // 根据层级设置颜色和字体大小
  const textColor =
    level === 0 ? '#000000' : isActive ? 'primary.500' : hasActiveChild ? '#1D2129' : '#7D7B7B';
  const fontSize = level === 0 ? respDims(18, 14) : respDims(16, 14);

  return (
    <>
      <Flex
        px={respDims(16)}
        py={respDims(12)}
        mt={respDims(4, 2)}
        alignItems="center"
        fontSize={fontSize}
        w="100%"
        borderRadius={respDims(8)}
        cursor="pointer"
        bgColor={isActive ? 'primary.50' : 'transparent'}
        color={textColor}
        _hover={{
          bgColor: 'primary.50'
        }}
        onClick={() => {
          if (hasChildren) {
            onExpand(
              isExpanded
                ? expandedKeys.filter((key) => key !== item.key)
                : [...expandedKeys, item.key]
            );
          } else {
            onClickItem?.(item);
          }
        }}
        pl={respDims(38 * level || '14fpx')}
      >
        {!!icon && <AdvIcon name={icon} w={respDims(24, 18)} h={respDims(24, 18)} />}

        <Box ml={respDims(12)}>{item.name}</Box>

        {hasChildren && (
          <SvgIcon
            name="chevronDown"
            w={respDims(16, 14)}
            h={respDims(16, 14)}
            ml="auto"
            style={{
              transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease'
            }}
          />
        )}
      </Flex>

      {isExpanded && hasChildren && (
        <Box>
          {item.children!.map((childItem: MenuItemType) => (
            <MenuItem
              key={childItem.key}
              level={level + 1} // 递增层级
              item={childItem}
              activeKey={activeKey}
              activePathKeys={activePathKeys}
              expandedKeys={expandedKeys}
              onExpand={onExpand}
              onClickItem={onClickItem}
            />
          ))}
        </Box>
      )}
    </>
  );
};

export default MenuItem;
