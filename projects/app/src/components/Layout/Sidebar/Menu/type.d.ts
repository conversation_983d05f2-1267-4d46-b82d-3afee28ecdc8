export type MenuItemType = {
  key: string;
  name: string;
  path?: string;
  icon?: string;
  activeIcon?: string;
  children?: MenuItemType[];
};

export type MenuItemProps = {
  item: MenuItemType;
  activeKey?: string;
  activePathKeys?: string[];
  expandedKeys: string[];
  onExpand: (keys: string[]) => void;
  onClickItem?: (item: MenuItemType) => void;
};

export type MenuProps = {
  groups: { name: string; items: MenuItemType[] }[];
  activeKey?: string;
  activePathKeys?: string[];
  expandedKeys?: string[];
  onExpand?: (keys: string[]) => void;
  onClickItem?: (item: MenuItemType) => void;
};
