import { Box, Flex, Image } from '@chakra-ui/react';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import { useSystemStore } from '@/store/useSystemStore';
import { LayoutContext } from '../../../LayoutProvider';
import { respDims } from '@/utils/chakra';
import { useRoutes } from '@/hooks/useRoutes';
import { traverseRouteTop } from '@/routes';
import { MenuItemType } from '../Menu/type';
import Menu from '../Menu';
import Footer from '../Footer';
import { DimsMinScale } from '../constants';
import { useTenantStore } from '@/store/useTenantStore';

const MenuSidebar = () => {
  const router = useRouter();

  const { isPc } = useSystemStore();

  const { tenant } = useTenantStore();

  const { closeSidebar } = useContext(LayoutContext);

  const { routeGroup } = useRoutes();

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  const items: MenuItemType[] = routeGroup?.navRoutes || [];

  const { activeKey, activePathKeys } = useMemo(() => {
    const activeKey = routeGroup?.navActiveRoute?.key;
    if (!activeKey) {
      return {};
    }

    const activePathKeys: string[] = [];
    traverseRouteTop(routeGroup.navActiveRoute!, (it) => {
      activePathKeys.unshift(it.key);
    });

    return {
      activeKey,
      activePathKeys
    };
  }, [routeGroup?.navActiveRoute]);
  const onClickMenuItem = (nav: MenuItemType) => {
    nav.path && router.push(nav.path);
    !isPc && closeSidebar();
  };

  useEffect(() => {
    activePathKeys &&
      setExpandedKeys((state) => {
        const keys = activePathKeys.filter((it) => it !== activeKey && !state.includes(it));
        return keys.length ? [...state, ...keys] : state;
      });
  }, [activeKey, activePathKeys]);

  return (
    <Flex
      flexDir="column"
      backgroundColor="#fff"
      w={respDims('576rpx', 257, DimsMinScale)}
      h="100%"
      pt={respDims('32rpx', 22, DimsMinScale)}
      tabIndex={0}
    >
      <Image
        src={tenant?.avatarUrl}
        h={respDims(58, DimsMinScale)}
        w="auto"
        maxW="100%"
        objectFit="contain"
        alt=""
      />

      <Box
        mt={respDims(12, DimsMinScale)}
        color="#303133"
        fontSize={respDims('17fpx')}
        fontWeight="bold"
        lineHeight={respDims('20fpx')}
        textAlign="center"
        whiteSpace="pre-wrap"
      >
        {tenant?.fullName || tenant?.name}
      </Box>

      <Box
        flex="1 0 0"
        px={respDims(20, DimsMinScale)}
        overflowX="hidden"
        overflowY="scroll"
        pt={respDims(10, DimsMinScale)}
      >
        <Menu
          groups={[{ name: '', items }]}
          activeKey={activeKey}
          activePathKeys={activePathKeys}
          expandedKeys={expandedKeys}
          onExpand={(keys) => setExpandedKeys(keys)}
          onClickItem={onClickMenuItem}
        />
      </Box>

      <Footer flexShrink={0} />
    </Flex>
  );
};

export default React.memo(MenuSidebar);
