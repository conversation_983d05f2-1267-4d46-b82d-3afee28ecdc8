import { BoxProps, Center, Flex } from '@chakra-ui/react';
import UserMenu from '../../components/UserMenu';
import { respDims } from '@/utils/chakra';
import Notice from '../../components/Notice';
import { CollapsedDimsMinScale, DimsMinScale } from '../constants';
import { useMemo } from 'react';
import { useRoutes } from '@/hooks/useRoutes';
import { traverseRoutes } from '@/routes';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { useUserStore } from '@/store/useUserStore';

const Footer = ({
  isChat,
  isCollapsed,
  ...props
}: { isChat?: boolean; isCollapsed?: boolean } & BoxProps) => {
  const { routeGroupMap } = useRoutes();

  const showNotice = useMemo(
    () =>
      !!traverseRoutes(
        routeGroupMap[RouteGroupTypeEnum.Chat].navRoutes,
        (route) => route.code === 'cloud_library'
      ),
    [routeGroupMap]
  );

  const dimsMinScale = isCollapsed ? CollapsedDimsMinScale : DimsMinScale;

  const px = respDims(isCollapsed ? 16 : 20, dimsMinScale);

  const py = respDims(14, dimsMinScale);

  return (
    <Flex
      direction={isCollapsed ? 'column' : 'row'}
      align="center"
      px={px}
      py={py}
      {...(!isCollapsed && { borderTop: '1px solid #E5E7EB' })}
      pos="relative"
      {...props}
    >
      {isCollapsed ? (
        <>
          {showNotice && <Notice />}

          <UserMenu mt={py} w="100%" isCollapsed={isCollapsed} />
        </>
      ) : (
        <>
          <UserMenu h="100%" flex="1" isCollapsed={isCollapsed} />

          {showNotice && <Notice ml={respDims(4, dimsMinScale)} />}
        </>
      )}
    </Flex>
  );
};

export default Footer;
