import React from 'react';
import { NavItemType } from '../type';
import { Box, Center, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import AdvIcon from '@/components/AdvIcon';
import MyTooltip from '@/components/MyTooltip';
import { CollapsedDimsMinScale, DimsMinScale } from '../../constants';
import { useRouter } from 'next/router';
import { trackAppNavItemClick } from '../../../../../api/app';

const NavItem = ({
  isPc,
  item,
  isActive,
  isCollapsed,
  rightMenu,
  onClick
}: {
  isPc?: boolean;
  item: NavItemType;
  isActive?: boolean;
  isCollapsed?: boolean;
  rightMenu?: React.ReactNode;
  onClick?: () => void;
}) => {
  const router = useRouter();
  const activeRoute = router.query.activeRoute as string;
  if (activeRoute) {
    isActive = activeRoute === item.path;
  }
  const icon = (isActive && router.query.recentlyUsed !== 'true' && item.activeIcon) || item.icon;

  const dimsMinScale = isCollapsed ? CollapsedDimsMinScale : DimsMinScale;

  const iconSize = respDims('60rpx', isCollapsed ? 28 : 26, dimsMinScale);

  const trackClick = (code: string) => {
    console.log(`NavItem clicked: ${code}`);
    trackAppNavItemClick({ menuCode: code || 'home_index' });
  };

  return (
    <Flex
      alignItems="center"
      my="1px"
      id={item.code}
      px={respDims('20rpx', isCollapsed ? 8 : 10, dimsMinScale)}
      py={respDims('20rpx', isCollapsed ? 8 : 12, dimsMinScale)}
      borderRadius={respDims('16rpx', 8, dimsMinScale)}
      {...(isActive &&
        router.query.recentlyUsed !== 'true' && {
          bgColor: 'primary.25'
        })}
      _hover={{
        bgColor: 'primary.25'
      }}
      cursor="pointer"
      onClick={onClick}
      css={{
        opacity: '1!important'
      }}
    >
      {!!icon && (
        <MyTooltip
          label={item.name}
          placement="right"
          isDisabled={!isCollapsed}
          shouldWrapChildren={false}
          color="#FFFFFF"
          bg="rgba(0,0,0,0.9)"
          fontSize={respDims('14fpx')}
          lineHeight={respDims('22fpx')}
        >
          <Center>
            <AdvIcon
              boxShadow={
                isActive && router.query.recentlyUsed !== 'true'
                  ? '0px 3px 4px 0px rgba(200,190,255,0.6)'
                  : 'none'
              }
              name={icon}
              w={iconSize}
              h={iconSize}
              borderRadius={respDims('16rpx', 10, dimsMinScale)}
            />
          </Center>
        </MyTooltip>
      )}

      {!isCollapsed && (
        <>
          <Box
            flex="1"
            ml={respDims('20rpx', 10, dimsMinScale)}
            color={'#303133'}
            fontSize={respDims('28rpx', '15fpx')}
            fontWeight="bold"
            onClick={() => trackClick(item.code || '')}
          >
            {item.name}
          </Box>

          {rightMenu}
        </>
      )}
    </Flex>
  );
};

export default React.memo(NavItem);
