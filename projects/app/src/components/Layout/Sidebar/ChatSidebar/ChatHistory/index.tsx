import { respDims, rpxDim } from '@/utils/chakra';
import {
  Box,
  Center,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  MenuButton,
  Tooltip
} from '@chakra-ui/react';
import MyMenu from '@/components/MyMenu';
import {
  ForwardedRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import dayjs from 'dayjs';
import { useChatStore } from '@/store/useChatStore';
import { useRouter } from 'next/router';
import ChatSettingsModal from '@/components/ChatSettingsModal';
import SvgIcon from '@/components/SvgIcon';
import Lottie from '@/components/Lottie';
import { useSystemStore } from '@/store/useSystemStore';
import { ChatHistoryItemType } from '@/fastgpt/global/core/chat/type';
import MyTooltip from '@/components/MyTooltip';
import History from '@/components/Layout/components/History';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { CollapsedDimsMinScale } from '../../constants';
import { tenantAppDetail } from '@/api/scene';
import { TenantAppDetailType } from '@/types/api/scene';
import { MessageBox } from '@/utils/ui/messageBox';
import useHistoryData from '@/hooks/useHistoryData';
import { useDeepEditStore } from '@/store/useDeepEdit';

export type ChatHistoryRef = {
  loadMore: () => void;
};

const ChatHistory = (
  { isCollapsed, onDone }: { isCollapsed?: boolean; onDone?: () => void },
  ref: ForwardedRef<ChatHistoryRef>
) => {
  const { openOverlay } = useOverlayManager();

  const router = useRouter();
  const { isPc } = useSystemStore();
  const { lastChatId } = useChatStore();
  const [queryKeyword, setQueryKeyword] = useState('');
  const [inputKeyword, setInputKeyword] = useState('');
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { data, loadMoreData, deleteData, refetch } = useHistoryData(
    { size: 20, current: 1, keyword: queryKeyword },
    'histories'
  );
  const [settingsChatId, setSettingsChatId] = useState('');
  const [currentItemId, setCurrentItemId] = useState<string>();
  const [isItemTouching, setIsItemTouching] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const { setGenerateFormData } = useDeepEditStore();

  const list = useMemo(() => {
    const list = data && data.length ? data.map((it) => ({ ...it, title: it.title })) : [];
    if (!list.length) {
      return list;
    }
    const today = dayjs().startOf('day');
    let lastType = '';
    const result: (ChatHistoryItemType | string)[] = [];
    list
      .sort((l, r) => dayjs(r.updateTime).valueOf() - dayjs(l.updateTime).valueOf())
      .forEach((it) => {
        const t = dayjs(it.updateTime).startOf('day');
        const d = today.diff(t, 'day');
        let type = '';
        if (d === 0) {
          type = '今天';
        } else if (d == 1) {
          type = '昨天';
        } else if (d <= 7 && (t.day() ? t.day() : 7) < (today.day() ? today.day() : 7)) {
          type = '本周';
        } else if (t.year() === today.year()) {
          type = `${t.month() + 1}月`;
        } else {
          type = `${t.year()}年`;
        }
        if (type !== lastType) {
          result.push(type);
          lastType = type;
        }
        result.push(it);
      });
    return result;
  }, [data]);

  const onRemoveItem = (item: ChatHistoryItemType) => {
    MessageBox.confirm({
      title: '操作确认',
      content: '确定删除该记录？',
      onOk: async () => {
        await deleteData(item.chatId, item.id || '');
      }
    });
  };

  useImperativeHandle(ref, () => ({
    loadMore: async () => {
      if (!isLoadingMore) {
        setIsLoadingMore(true);
        await loadMoreData();
        setIsLoadingMore(false);
      }
    }
  }));

  const onSearch = () => {
    setQueryKeyword(inputKeyword);
  };

  const onClickItem = async (item: ChatHistoryItemType) => {
    setGenerateFormData({});
    try {
      const data: TenantAppDetailType | null = await tenantAppDetail({ id: item.tenantAppId! });
      if (data && data.sceneList.length > 0) {
        const sceneId = data.sceneList[0].tenantSceneId;
        router.push(
          `/home?appId=${item.tenantAppId}&chatId=${item.chatId}&sceneId=${sceneId || ''}&isBack=1`
        );
        onDone?.();
      } else {
        router.push(`/home?appId=${item.tenantAppId}&chatId=${item.chatId}&sceneId=`);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (!isInitialLoad) {
      refetch();
    } else {
      setIsInitialLoad(false);
    }
  }, [queryKeyword, refetch]);

  useEffect(() => {
    if (!router.query.sceneId && router.query.chatId) {
      tenantAppDetail({ id: String(router.query.appId) }).then((data: TenantAppDetailType) => {
        if (data && data.sceneList.length > 0) {
          const sceneId = data.sceneList[0].tenantSceneId;
          router.push(
            `/home?appId=${String(router.query.appId)}&chatId=${String(router.query.chatId)}&sceneId=${sceneId || ''}`
          );
          onDone?.();
        } else {
          router.push(
            `/home?appId=${String(router.query.appId)}&chatId=${String(router.query.chatId)}&sceneId=`
          );
        }
      });
    }
  }, [router.query.chatId, router.query.sceneId]);

  return (
    <>
      <Box mt={respDims('28rpx', 12)} w="100%" h="1px" bgColor={isPc ? '#E5E7EB' : '#F3F4F6'} />

      {isCollapsed ? (
        <MyTooltip
          label="查看全部历史会话"
          placement="right"
          shouldWrapChildren={false}
          color="#FFFFFF"
          bg="rgba(0,0,0,0.9)"
          fontSize={respDims('14fpx')}
          lineHeight={respDims('22fpx')}
        >
          <Center
            mt={respDims(12, CollapsedDimsMinScale)}
            w={respDims(44, CollapsedDimsMinScale)}
            h={respDims(44, CollapsedDimsMinScale)}
            cursor="pointer"
            color="#858585"
            borderRadius={respDims(8, CollapsedDimsMinScale)}
            _hover={{
              color: '#7D4DFF',
              bgColor: '#F9FAFB'
            }}
            onClick={() => {
              openOverlay({
                name: 'history',
                Overlay: History,
                props: {}
              });
            }}
          >
            <SvgIcon name="chatHistoryConversation" w="20px" h="20px" />
          </Center>
        </MyTooltip>
      ) : (
        <>
          <Box pt={respDims('24rpx', 12)} pos="sticky" top={respDims('-28rpx', -12)} zIndex="1">
            <InputGroup bgColor="#f7f7f7" borderRadius={respDims(8)}>
              <InputLeftElement
                {...(isPc
                  ? { w: respDims('46fpx'), h: respDims('36fpx') }
                  : {
                      w: rpxDim(100),
                      h: rpxDim(72)
                    })}
                onClick={onSearch}
              >
                <SvgIcon
                  name="search"
                  {...(isPc ? { w: '14px', h: '14px' } : { w: rpxDim(36), h: rpxDim(36) })}
                  color="#4E5969"
                />
              </InputLeftElement>
              <Input
                placeholder="搜索历史记录"
                border="none"
                autoFocus={false}
                style={{
                  paddingLeft: 45
                }}
                {...(isPc
                  ? {
                      h: respDims('36fpx'),
                      fontSize: respDims('14fpx'),
                      lineHeight: respDims('22fpx'),
                      _placeholder: {
                        fontsize: 'inherit',
                        lineHeight: 'inherit',
                        color: '#A8ABB2'
                      }
                    }
                  : {
                      h: rpxDim(72),
                      bgColor: 'rgba(0,0,0,0.03)',
                      fontSize: rpxDim(28),
                      lineHeight: rpxDim(44),
                      borderRadius: rpxDim(100)
                    })}
                value={inputKeyword}
                onChange={(e) => setInputKeyword(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    onSearch();
                    if (!isPc) {
                      e.currentTarget.blur();
                      e.currentTarget.scrollIntoView({
                        block: 'start'
                      });
                    }
                  }
                }}
              />
              <MyTooltip
                label="查看全部历史会话"
                shouldWrapChildren={false}
                color="#FFFFFF"
                bg="rgba(0,0,0,0.9)"
                fontSize={respDims('14fpx')}
                lineHeight={respDims('22fpx')}
              >
                <InputRightElement
                  {...(isPc
                    ? { pb: '6px' }
                    : { display: 'flex', alignItems: 'center', h: rpxDim(72) })}
                >
                  <SvgIcon
                    mr={respDims(20)}
                    name="chatHistoryConversation"
                    cursor="pointer"
                    color="#858585"
                    _hover={{
                      color: '#7D4DFF'
                    }}
                    onClick={() => {
                      openOverlay({
                        name: 'history',
                        Overlay: History,
                        props: {}
                      });
                    }}
                    {...(isPc ? { w: '20px', h: '20px' } : { w: rpxDim(36), h: rpxDim(36) })}
                  />
                </InputRightElement>
              </MyTooltip>
            </InputGroup>
          </Box>

          <Box
            position="relative"
            {...(!isPc && {
              userSelect: 'none'
            })}
          >
            {list?.map((item, index) => {
              if (typeof item === 'string') {
                return (
                  <Box
                    key={`${item}-${index}`}
                    mt={respDims(24)}
                    mb={respDims(16)}
                    color="#909399"
                    fontSize={respDims('28rpx', '16fpx')}
                    lineHeight={respDims('33rpx', '19fpx')}
                    fontWeight="bold"
                  >
                    {item}
                  </Box>
                );
              }
              return (
                <Flex
                  key={item.id}
                  position="relative"
                  alignItems="center"
                  h={respDims('88rpx', '44fpx')}
                  px={respDims('24rpx', 12)}
                  color="#303133"
                  fontSize={respDims('28rpx', '14fpx')}
                  borderRadius={respDims('16rpx', 8)}
                  bgColor={
                    item.chatId === lastChatId ? (isPc ? '#F8FAFC' : '#F8FAFC') : 'transparent'
                  }
                  cursor="pointer"
                  {...(isPc && {
                    _hover: {
                      bgColor: 'primary.50',
                      '.nav-chat-menu': {
                        display: 'block'
                      }
                    }
                  })}
                  onClick={() => onClickItem(item)}
                  onContextMenu={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setCurrentItemId(item.chatId);
                  }}
                  onTouchStart={() => setIsItemTouching(true)}
                  onTouchEnd={() => setIsItemTouching(false)}
                  onTouchCancel={() => setIsItemTouching(false)}
                >
                  <Tooltip label={item.title} hasArrow isDisabled={item.title.length <= 10}>
                    <Box flex="1" overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap">
                      {item.title}
                    </Box>
                  </Tooltip>

                  <MyMenu
                    trigger="click"
                    offset={[20, 0]}
                    width={20}
                    isOpen={item.chatId === currentItemId}
                    outsideClosable={!isItemTouching}
                    onClose={() => setCurrentItemId(undefined)}
                    Button={
                      <MenuButton
                        className="nav-chat-menu"
                        borderRadius="4px"
                        {...(isPc
                          ? { display: 'none' }
                          : {
                              visibility: 'hidden'
                            })}
                        flexGrow="0"
                        flexShrink="0"
                        flexBasis={respDims('48rpx', 30)}
                        w={respDims('48rpx', 30)}
                        h={respDims('48rpx', 30)}
                        _hover={{
                          bg: 'myWhite.600'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Center>
                          <SvgIcon
                            name="more"
                            w={respDims('36rpx', '16fpx')}
                            h={respDims('36rpx', '16fpx')}
                          />
                        </Center>
                      </MenuButton>
                    }
                    menuList={[
                      {
                        label: '重命名',
                        icon: (
                          <SvgIcon
                            name="edit"
                            w={respDims('36rpx', '16fpx')}
                            h={respDims('36rpx', '16fpx')}
                          />
                        ),
                        onClick: () => {
                          setSettingsChatId(item.chatId);
                        }
                      },
                      {
                        label: '删除',
                        icon: (
                          <SvgIcon
                            name="trash"
                            w={respDims('36rpx', '16fpx')}
                            h={respDims('36rpx', '16fpx')}
                          />
                        ),
                        onClick: () => onRemoveItem(item)
                      }
                    ]}
                  />
                </Flex>
              );
            })}
          </Box>
        </>
      )}

      {settingsChatId && (
        <ChatSettingsModal chatId={settingsChatId} onClose={() => setSettingsChatId('')} />
      )}

      {isLoadingMore && (
        <Center>
          <Lottie name="Loading" w={respDims('54rpx', 24, 24)} h={respDims('54rpx', 24, 24)} />
        </Center>
      )}
    </>
  );
};

export default forwardRef(ChatHistory);
