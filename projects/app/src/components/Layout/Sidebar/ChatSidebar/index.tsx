import { Box, Button, Center, Flex, Image } from '@chakra-ui/react';
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import ChatHistory, { ChatHistoryRef } from './ChatHistory';
import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import styleVariables from '@/styles/variable.module.scss';
import { respDims, rpxDim } from '@/utils/chakra';
import { useAppStore } from '@/store/useAppStore';
import { APP_ICON } from '@/constants/common';
import { useUserStore } from '@/store/useUserStore';
import { useSystemStore } from '@/store/useSystemStore';
import { LayoutContext } from '../../../LayoutProvider';
import MobileSetting from '../../components/mobile/MobileSetting';
import { useCopyData } from '@/hooks/useCopyData';
import { useTenantStore } from '@/store/useTenantStore';
import { tenantSceneNavbar, navbarCommonAppList } from '@/api/scene';
import { useQuery } from '@tanstack/react-query';
import { NavItemType, SceneNavItemType } from './type';
import { useRoutes } from '@/hooks/useRoutes';
import { RouteGroupTypeEnum } from '@/constants/routes';
import NavItem from './NavItem';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { CollapsedDimsMinScale, DimsMinScale, isBaoAnDomain } from '../constants';
import Toggle from '../Toggle';
import MyTooltip from '@/components/MyTooltip';
import Footer from '../Footer';
import RecentlyUsed from './RecentlyUsed/index';
import { useChatStore } from '@/store/useChatStore';
import ZeroSpaceTour from '@/components/CustomTour/ZeroSpaceTour';
import { CommonAppType } from '@/types/api/scene';
import { EventNameEnum, multiEventBus } from '@/utils/eventbus';

const ChatSidebar = () => {
  const router = useRouter();

  const { userInfo } = useUserStore();

  const { tenant } = useTenantStore();

  const { isPc } = useSystemStore();

  const { isOpenSidebar, closeSidebar } = useContext(LayoutContext);

  const { generalAppId, myApps, loadMyApps } = useAppStore();

  const { chatData } = useChatStore();

  const [showBackToTop, setShowBackToTop] = useState(false);

  const [activeSceneId, setActiveSceneId] = useState('');

  const scrollRef = useRef<HTMLDivElement>(null);

  const chatHistoryRef = useRef<ChatHistoryRef>(null);

  const { copyData } = useCopyData();

  const { openOverlay } = useOverlayManager();

  const [commonAppListData, setCommonAppListData] = useState<CommonAppType[]>([]);

  const isCollapsed = isPc && !isOpenSidebar;

  const websiteUrl = `${window.location.hostname}${window.location.port ? ':' + window.location.port : ''}`;

  const { routeGroupMap, routeGroup } = useRoutes();

  const [isDomain, setIsDomain] = useState(!!isBaoAnDomain());

  const { chatNavs, demoNavs } = useMemo(() => {
    const routeGroup = routeGroupMap[RouteGroupTypeEnum.Chat];
    const chatNavs: NavItemType[] = [];
    const demoNavs: NavItemType[] = [];

    // generalAppId &&
    // chatNavs.push({
    //   key: 'general_chat',
    //   code: 'general_chat',
    //   name: '通用对话',
    //   icon: 'navGeneralChat',
    //   activeIcon: 'navGeneralChat2'
    // });

    routeGroup.navRoutes.forEach((route) => {
      if (route.code?.startsWith('demo')) {
        demoNavs.push(route);
      } else {
        chatNavs.push(route);
      }
    });
    return { chatNavs, demoNavs };
  }, [routeGroupMap, generalAppId]);

  // 演示导航
  const isDemo = useMemo(
    () => isPc && myApps?.some((it) => it.name.includes('演示应用')),
    [isPc, myApps]
  );

  const activeNavKey =
    routeGroup?.navActiveRoute?.key || (chatData.appId == generalAppId ? 'general_chat' : '');

  const { data: sceneData } = useQuery(['sceneNavbar'], () => tenantSceneNavbar());

  const sceneNavs: SceneNavItemType[] = useMemo(
    () =>
      sceneData
        ?.filter((it) => Array.isArray(it.labels) && it.labels.length > 0)
        .map((it, index) => ({
          key: `${it.id}-${index}`,
          name: it.name,
          icon: it.avatarUrl || APP_ICON,
          scene: it
        })) || [],
    [sceneData]
  );

  const onNavDone = () => !isPc && closeSidebar();

  const onClickNewChat = () => {
    router.replace({
      pathname: '/home',
      query: {
        appId: router.query.appId || undefined,
        sceneId: activeSceneId,
        activeRoute: router.query.activeRoute,
        recentlyUsed: router.query.recentlyUsed || undefined
      }
    });
    onNavDone();
  };

  const onClickNav = (nav: NavItemType) => {
    if (nav.code == 'general_chat') {
      router.push({
        pathname: '/home',
        query: {
          appId: generalAppId || router.query.appId || undefined,
          sceneId:
            myApps.find((it) => it.id === generalAppId)?.labelList?.[0]?.tenantSceneId ||
            activeSceneId
        }
      });
    }
    if (nav.onClick) {
      nav.onClick();
    } else if (nav.path) {
      router.push(nav.path);
    }
    onNavDone();
  };

  const onClickSceneNav = ({ scene }: SceneNavItemType) => {
    setActiveSceneId(scene.id);
    const appId = scene.labels[0]?.apps[0]?.id;
    if (appId) {
      router.push({
        pathname: '/home',
        query: {
          appId: appId,
          sceneId: scene.id
        }
      });
      onNavDone();
    }
  };

  const checkScroll = useCallback(() => {
    if (!scrollRef.current) {
      return;
    }
    setShowBackToTop(scrollRef.current.scrollTop > scrollRef.current.clientHeight);
    if (
      scrollRef.current.scrollTop + scrollRef.current.clientHeight >=
      scrollRef.current.scrollHeight - 10
    ) {
      chatHistoryRef.current?.loadMore();
    }
  }, []);

  const backToTop = () => {
    scrollRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const onOpenMobileSetting = () => {
    openOverlay({
      Overlay: MobileSetting,
      props: {}
    });
  };

  useQuery(['loadMyApps'], () => loadMyApps());

  const { refetch } = useQuery(['navbarCommonAppList'], () => navbarCommonAppList(), {
    onSuccess: (data) => {
      if (data) {
        setCommonAppListData(data);
      }
    }
  });

  useEffect(() => {
    const handleRefresh = () => {
      refetch();
    };
    multiEventBus.on(EventNameEnum.commonAppListToNavbar, handleRefresh);
    return () => {
      multiEventBus.off(EventNameEnum.commonAppListToNavbar, handleRefresh);
    };
  }, [refetch]);

  useEffect(() => {
    if (router.query.sceneId) {
      setActiveSceneId(router.query.sceneId as string);
    }
  }, [router.query.sceneId]);

  const dimsMinScale = isCollapsed ? CollapsedDimsMinScale : DimsMinScale;

  const sidebarW = isCollapsed ? respDims(64, dimsMinScale) : respDims('576rpx', 240, dimsMinScale);

  const sidebarPx = isCollapsed ? respDims(10, dimsMinScale) : respDims('40rpx', 26, dimsMinScale);

  useEffect(() => {
    setIsDomain(!!isBaoAnDomain());
  }, [window.location]);

  return (
    <Flex flexDir="column" w={sidebarW} h="100%" tabIndex={0} bgColor="rgba(255,255,255,0.75)">
      {isPc && (
        <Flex
          flex="0 0 auto"
          flexDir="column"
          px={sidebarPx}
          pt={respDims('32rpx', 50, dimsMinScale)}
          pb={respDims('26rpx', 20, dimsMinScale)}
        >
          {isPc ? (
            <>
              <Image
                src={tenant?.avatarUrl}
                h={respDims(isCollapsed ? 32 : 58, dimsMinScale)}
                w="auto"
                maxW="100%"
                cursor={tenant?.portalUrl ? 'pointer' : 'default'}
                objectFit="contain"
                alt=""
                onClick={() => {
                  if (tenant?.portalUrl) {
                    window.open(tenant?.portalUrl, '_blank');
                  }
                }}
              />

              {isCollapsed ? (
                <>
                  <MyTooltip
                    label="新建对话"
                    placement="right"
                    isDisabled={!isCollapsed}
                    shouldWrapChildren={false}
                    color="#FFFFFF"
                    bg="rgba(0,0,0,0.9)"
                    fontSize={respDims('14fpx')}
                    lineHeight={respDims('22fpx')}
                  >
                    <Center
                      alignSelf="center"
                      mt={respDims(16, dimsMinScale)}
                      w={respDims(32, dimsMinScale)}
                      h={respDims(32, dimsMinScale)}
                      color="#4E5969"
                      bgColor="#F2F3F5"
                      borderRadius="50%"
                      _hover={{
                        color: 'primary.500',
                        bgColor: 'primary.50'
                      }}
                      cursor="pointer"
                      onClick={onClickNewChat}
                    >
                      <SvgIcon name="plus" w="12px" h="12px" />
                    </Center>
                  </MyTooltip>
                </>
              ) : (
                <>
                  <Box
                    mt={respDims(12, dimsMinScale)}
                    color="#303133"
                    fontSize={respDims('18fpx')}
                    fontWeight="bold"
                    lineHeight={respDims('20fpx')}
                    textAlign="center"
                    whiteSpace="pre-wrap"
                  >
                    {tenant?.fullName || tenant?.name}
                  </Box>

                  <Button
                    mt={respDims(16, dimsMinScale)}
                    variant="primaryLinearGradient"
                    bg="linear-gradient( 90deg, #774CFF 0%, #AC51FF 100%)"
                    borderRadius={respDims(50, dimsMinScale)}
                    alignSelf="stretch"
                    h={respDims(32, dimsMinScale)}
                    color="white"
                    fontSize={respDims('14fpx')}
                    fontWeight="bold"
                    onClick={onClickNewChat}
                    _hover={{
                      background: 'linear-gradient(90deg, #774CFF 0%, #AC51FF 100%)',
                      boxShadow: '4px 4px 10px 0px rgba(137, 108, 255, 0.38)'
                    }}
                  >
                    新建对话
                  </Button>
                </>
              )}
            </>
          ) : (
            <Box pl={rpxDim(20)}>
              <Flex alignItems="center">
                <Image w="auto" h={rpxDim(64)} src={tenant?.avatarUrl} alt="" />
                <Box
                  ml={rpxDim(16)}
                  color="#303133"
                  fontSize={rpxDim(32)}
                  lineHeight={rpxDim(38)}
                  fontWeight="600"
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  {tenant?.name}AI平台
                </Box>
              </Flex>

              <Flex
                alignItems="center"
                mt={rpxDim(16)}
                fontSize={rpxDim(28)}
                lineHeight={rpxDim(33)}
              >
                <Box
                  flex="1"
                  color="#909399"
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  {websiteUrl}
                </Box>
                <Box
                  flexShrink="0"
                  px={rpxDim(18)}
                  py={rpxDim(4)}
                  color="#303133"
                  bgColor="rgba(0,0,0,0.03)"
                  borderRadius={rpxDim(68)}
                  cursor="pointer"
                  onClick={() => copyData(websiteUrl)}
                >
                  复制
                </Box>
              </Flex>
            </Box>
          )}
        </Flex>
      )}

      <Box
        mx={sidebarPx}
        mb={respDims('28rpx', 12, dimsMinScale)}
        alignSelf="stretch"
        h="1px"
        bgColor={isPc ? '#E5E7EB' : '#F3F4F6'}
      />

      <Box
        ref={scrollRef}
        flex="1 0 0"
        px={sidebarPx}
        overflowX="hidden"
        overflowY="scroll"
        onScroll={checkScroll}
      >
        <Box mr={[`-${styleVariables.scrollbarSmWidth}`, `-${styleVariables.scrollbarWidth}`]}>
          {isPc &&
            chatNavs.map((item) => (
              <NavItem
                key={item.key}
                isPc={isPc}
                item={item}
                isActive={item.key === activeNavKey}
                isCollapsed={isCollapsed}
                onClick={() => onClickNav(item)}
              />
            ))}

          {sceneNavs.length > 0 && (
            <>
              {isPc && (
                <Box key="divider" w="100%" h="1px" my={respDims('28rpx', 12, dimsMinScale)} />
              )}

              {/* {sceneNavs.map((item) => (
                <NavItem
                  key={item.key}
                  item={item}
                  isActive={item.scene.id === activeSceneId && !activeNavKey}
                  isCollapsed={isCollapsed}
                  onClick={() => onClickSceneNav(item)}
                />
              ))}

              {isDemo &&
                demoNavs.map((item) => (
                  <NavItem
                    key={item.key}
                    item={item}
                    isActive={item.key === activeNavKey}
                    isCollapsed={isCollapsed}
                    onClick={() => onClickNav(item)}
                  />
                ))} */}
            </>
          )}
          {/* <Box
            mx={sidebarPx}
            mb={respDims('28rpx', 12, dimsMinScale)}
            alignSelf="stretch"
            h="1px"
            bgColor={isPc ? '#E5E7EB' : '#F3F4F6'}
          /> */}

          {commonAppListData && commonAppListData.length > 0 && (
            <>
              <Box
                borderTop={isPc ? '1px solid #E5E7EB' : 'none'}
                pt={respDims('28rpx', 12, dimsMinScale)}
              >
                <RecentlyUsed isCollapsed={!!isCollapsed} onDone={onNavDone} />
              </Box>
            </>
          )}

          <ChatHistory ref={chatHistoryRef} isCollapsed={isCollapsed} onDone={onNavDone} />
        </Box>

        {showBackToTop && (
          <Center
            position="sticky"
            bottom={respDims('20rpx', 48, dimsMinScale)}
            w={respDims('80rpx', '36fpx')}
            h={respDims('80rpx', '36fpx')}
            mb={respDims('20rpx', 48, dimsMinScale)}
            ml="auto"
            {...(isCollapsed && { mr: 'auto' })}
            bgColor="#FFF"
            boxShadow="0px 0px 16px 0px rgba(92,92,92,0.11)"
            borderRadius="50%"
            cursor="pointer"
            onClick={backToTop}
          >
            <SvgIcon name="alignTop" w={respDims('32rpx', 14, 14)} h={respDims('32rpx', 14, 14)} />
          </Center>
        )}
      </Box>

      {isPc ? (
        <Footer flexShrink={0} isChat isCollapsed={isCollapsed} />
      ) : (
        <>
          <Flex
            h={rpxDim(128)}
            px={rpxDim(40)}
            alignItems="center"
            boxShadow={`0 ${rpxDim(-2)} ${rpxDim(4)} 0 rgba(229,230,232,0.5)`}
            onClick={onOpenMobileSetting}
          >
            <Image
              flexShrink="0"
              w={rpxDim(64)}
              h={rpxDim(64)}
              borderRadius="50%"
              src={userInfo?.avatar || '/imgs/layout/avatar.svg'}
              alt=""
            />
            <Box
              flex="1"
              ml={rpxDim(20)}
              color="#303133"
              fontSize={rpxDim(32)}
              fontWeight="bold"
              overflow="hidden"
            >
              {userInfo?.username}
            </Box>
            <SvgIcon
              cursor="pointer"
              flexShrink="0"
              name="settings"
              w={rpxDim(48)}
              h={rpxDim(48)}
              color="#303133"
            />
          </Flex>
        </>
      )}

      <Toggle left={sidebarW} />

      {(router.pathname.includes('zeroCode') ||
        router.pathname.includes('/home/<USER>') ||
        router.pathname.includes('/home/<USER>') ||
        router.pathname.includes('/home/<USER>')) && <ZeroSpaceTour componentStep={0} />}
    </Flex>
  );
};

export default React.memo(ChatSidebar);
