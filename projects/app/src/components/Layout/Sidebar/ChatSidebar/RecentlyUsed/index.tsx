import { useQuery } from '@tanstack/react-query';
import { AppListItemType } from '@/types/api/app';
import {
  getAppCenterRecentlyUsedList,
  tenantAppDetail,
  navbarCommonAppList,
  sortNavCommonApp,
  removeFromNavbarCommonAppList
} from '@/api/scene';
import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { Avatar, Tooltip, Box, Flex } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import MyTooltip from '@/components/MyTooltip';
import { useRouter } from 'next/router';
import { TenantAppDetailType, CommonAppType } from '@/types/api/scene';
import { useChatStore } from '@/store/useChatStore';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { useTenantStore } from '@/store/useTenantStore';
import { DataSource, APP_ICON } from '@/constants/common';
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { cloneDeep } from 'lodash';
import { Toast } from '@/utils/ui/toast';
import { EventNameEnum, multiEventBus } from '@/utils/eventbus';

const RecentlyUsed = (props: {
  isCollapsed: boolean;
  refresh?: () => void;
  onDone?: () => void;
}) => {
  const router = useRouter();
  const [commonAppListData, setCommonAppListData] = useState<CommonAppType[]>([]);
  const { setGenerateFormData } = useDeepEditStore();
  const { isCollapsed } = props;
  const { lastChatId } = useChatStore();
  const { redirectHomePath } = useTenantStore();
  const { tenant } = useTenantStore();
  // console.log('tenant', tenant);

  const [isDomain, setIsDomain] = useState(false);

  useEffect(() => {
    if (!!isBaoAnDomain()) {
      setIsDomain(true);
    } else {
      setIsDomain(false);
    }
  }, [window.location]);

  const { refetch } = useQuery(['navbarCommonAppList'], () => navbarCommonAppList(), {
    onSuccess: (data) => {
      if (data) {
        setCommonAppListData(data);
      }
    }
  });

  useEffect(() => {
    const handleRefresh = () => {
      refetch();
    };
    multiEventBus.on(EventNameEnum.commonAppListToNavbar, handleRefresh);
    return () => {
      multiEventBus.off(EventNameEnum.commonAppListToNavbar, handleRefresh);
    };
  }, [refetch]);

  const handelDetail = useCallback(
    (app: AppListItemType) => {
      if (app.appTaskTypeId) {
        return window.open(
          `/deepeditor?appId=${app.id}&appTaskTypeId=${app.appTaskTypeId}&init=1`,
          '_blank'
        );
      }
      setGenerateFormData({});
      tenantAppDetail({ id: String(app.id) }).then((res: TenantAppDetailType) => {
        router.push({
          pathname: '/home',
          query: {
            appId: app.id,
            activeRoute: redirectHomePath,
            sceneId:
              res.source === DataSource.Personal
                ? ''
                : app?.labelList?.find((item) => item.isDisplayed === 1)?.tenantSceneId ||
                  app?.labelList?.[0]?.tenantSceneId,
            recentlyUsed: true
          }
        });
      });
      props.onDone?.();
    },
    [props, redirectHomePath, router, setGenerateFormData]
  );

  const onRmNavCommonApp = useCallback(
    (e: React.MouseEvent, id: string) => {
      e.stopPropagation();
      removeFromNavbarCommonAppList({ id }).then((res) => {
        Toast.success('已移除');
        refetch();
        multiEventBus.emit(EventNameEnum.navbarToCommonAppList);
      });
    },
    [refetch]
  );

  const onDragEnd = useCallback(
    (result: DropResult) => {
      if (!result.destination) return;

      const items = cloneDeep(commonAppListData);
      const [reorderedItem] = items.splice(result.source.index, 1);
      items.splice(result.destination.index, 0, reorderedItem);
      const param = [...items].map((item, index) => ({
        id: item.id,
        navbarOrder: index
      }));
      sortNavCommonApp({ param }).then(() => refetch());
    },
    [commonAppListData, refetch]
  );

  const Item = React.memo(({ item, index }: { item: any; index: number }) => {
    const app = item.tenantApp;
    return (
      <Draggable draggableId={String(app.id)} index={index}>
        {(provided, snapshot) => (
          <Flex
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            alignItems="center"
            borderRadius={respDims('7rpx', 7)}
            pt={respDims('10rpx', 12)}
            pb={respDims('10rpx', 12)}
            px={respDims('10rpx', 10)}
            bgColor={
              snapshot.isDragging
                ? '#F8FAFC'
                : router.query.appId === app.id && router.query.recentlyUsed === 'true'
                  ? 'primary.50'
                  : 'transparent'
            }
            _hover={{
              bgColor: 'primary.50',
              '.fixed-icon': {
                display: 'flex'
              }
            }}
            style={{
              ...provided.draggableProps.style
            }}
            onClick={() => handelDetail(app)}
          >
            <Avatar
              width={respDims('64rpx', 32)}
              height={respDims('64rpx', 32)}
              src={app.avatarUrl || APP_ICON}
            />
            <Box
              ml={respDims('14rpx', 7)}
              lineHeight={respDims('64rpx', 42)}
              whiteSpace="nowrap"
              overflow="hidden"
              textOverflow="ellipsis"
              maxWidth="calc(100% - 40px)"
              title={app.name}
            >
              {app.name}
            </Box>
            <Box
              ml={'auto'}
              display={'none'}
              className="fixed-icon"
              cursor={'pointer'}
              alignItems={'center'}
              bg={'white'}
              p={'3px'}
              borderRadius={'4px'}
            >
              <MyTooltip label={'从导航栏移除'} placement="top">
                <SvgIcon
                  name={item.isNavbar ? 'anchorCancel' : 'anchor'}
                  onClick={(e) => onRmNavCommonApp(e, item.id)}
                />
              </MyTooltip>
            </Box>
          </Flex>
        )}
      </Draggable>
    );
  });
  Item.displayName = 'RecentlyUsedItem';

  const List = useMemo(() => {
    const ListComponent = () => (
      <Droppable droppableId="sidebar-recently-used">
        {(provided) => (
          <Box ref={provided.innerRef} {...provided.droppableProps}>
            {commonAppListData.map((app: CommonAppType, index) => {
              return <Item key={app.id} index={index} item={app} />;
            })}
            {provided.placeholder}
          </Box>
        )}
      </Droppable>
    );
    ListComponent.displayName = 'RecentlyUsedList';
    return ListComponent;
  }, [commonAppListData]);

  const isCollapsedBar = useCallback(() => {
    return (
      <>
        <Flex justify="space-between" align="center" mb={respDims(10)}>
          <Box fontWeight={400} fontSize={respDims('14fpx')}>
            常用应用
          </Box>
          <Box
            p={respDims(4)}
            borderRadius={respDims(4)}
            display="flex"
            alignItems="center"
            _hover={{ background: '#ddd' }}
          >
            <MyTooltip label="应用中心" placement="right">
              <SvgIcon
                onClick={() => {
                  props.onDone?.();
                  if (isDomain) {
                    router.push('/baTeach');
                  } else {
                    if (tenant?.industry === 7) {
                      router.push('/app/list/educationAdvanced');
                    } else if (tenant?.industry === 6) {
                      router.push('/app/list/enterprise');
                    } else {
                      router.push('/app/list');
                    }
                  }
                }}
                name="navAppContent"
              />
            </MyTooltip>
          </Box>
        </Flex>
        <DragDropContext onDragEnd={onDragEnd}>
          <List />
        </DragDropContext>
      </>
    );
  }, [List, isDomain, onDragEnd, props, router]);

  const isUnCollapsedBar = useCallback(() => {
    return (
      <Box display="flex" flexDirection="column" margin="0 auto" gap={2} alignItems="center">
        <Box display="flex" flexDirection="column" alignItems="center">
          {commonAppListData.map((app: CommonAppType, index) => {
            return (
              <React.Fragment key={app.id}>
                <MyTooltip label={app.tenantApp.name} placement="right">
                  <Avatar
                    mb={respDims(18)}
                    width={respDims(32)}
                    height={respDims(32)}
                    src={app.tenantApp.avatarUrl}
                    onClick={() => handelDetail(app.tenantApp)}
                    cursor="pointer"
                  />
                </MyTooltip>
              </React.Fragment>
            );
          })}
        </Box>
        <Box
          borderRadius={respDims(4)}
          w={respDims(28)}
          h={respDims(28)}
          display="flex"
          alignItems="center"
          justifyContent="center"
          _hover={{ background: '#ddd' }}
        >
          <MyTooltip label="应用中心" placement="right">
            <SvgIcon
              onClick={() => {
                if (tenant?.industry === 7) {
                  router.push('/app/list/educationAdvanced');
                } else if (tenant?.industry === 6) {
                  router.push('/app/list/enterprise');
                } else {
                  router.push('/app/list');
                }
              }}
              name="navAppContent"
            />
          </MyTooltip>
        </Box>
      </Box>
    );
  }, [commonAppListData, handelDetail, router]);

  return <Box>{!isCollapsed ? isCollapsedBar() : isUnCollapsedBar()}</Box>;
};

export default React.memo(RecentlyUsed);
