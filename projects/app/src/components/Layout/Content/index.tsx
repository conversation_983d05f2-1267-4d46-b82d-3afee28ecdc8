import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import { useQuery } from '@tanstack/react-query';
import { useRoutes } from '@/hooks/useRoutes';
import { useContext, useEffect } from 'react';
import { Toast } from '@/utils/ui/toast';
import { Box, BoxProps } from '@chakra-ui/react';
import { LayoutContext } from '../../LayoutProvider';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { respDims } from '@/utils/chakra';
import { useAppStore } from '@/store/useAppStore';
import { AppStatus } from '@/constants/api/app';
import { thirdLogin, getXauatLoginToken, getPrivateLoginToken, getOauth2Token } from '@/api/auth';
import { UserInfoType } from '@/types/store/useUserStore';
import { LoginRes } from '@/types/api/auth';
import { clearToken, setToken } from '@/utils/auth';

const Content = ({
  unauth,
  children,
  ...props
}: { unauth: boolean; children: JSX.Element } & BoxProps) => {
  const router = useRouter();

  const { userInfo, initUserInfo, setUserInfo } = useUserStore();

  const { setGeneralAppId: setGeneraAppId } = useAppStore();

  const { routeGroup, isAccessDenied } = useRoutes();

  const { setContentSize } = useContext(LayoutContext);

  const { setElement, offsetWidth, offsetHeight } = useResizeObserver();

  const { code, orgCode } = router.query; // 获取 URL 参数

  const domain = window.location.hostname;
  const path = window.location.pathname;

  useEffect(() => {}, [domain, path, code]);

  const isPrivateDeployment = ({ code, orgCode }: { code: string; orgCode: string }) => {
    if (domain === 'owner-b.xauat.edu.cn') {
      return getXauatLoginToken({ code });
    } else if (domain === 'ai.snsy.edu.cn') {
      return getPrivateLoginToken({ code });
    }
    return thirdLogin({ code, orgCode });
  };

  useQuery(
    [router.pathname, code, orgCode],
    async () => {
      // 检测特定域名和路径模式，确保无code参数时重定向
      if (domain === 'owner-b.xauat.edu.cn' && path.startsWith('/xajz')) {
        try {
          const authUrl =
            'http://authserver.xauat.edu.cn/authserver/oauth2.0/authorize?response_type=code&client_id=1358843530389090304&redirect_uri=https://owner-b.xauat.edu.cn';
          window.location.href = authUrl;
          // 返回一个 never-resolving promise 来阻止后续逻辑执行
          return new Promise(() => {});
        } catch (error) {
          console.error('重定向到认证页面失败:', error);
          return Promise.reject(error);
        }
      }
      // 跳转到学前师院
      if (domain === 'ai.snsy.edu.cn' && path.startsWith('/xqsy')) {
        try {
          const authUrl =
            'https://authserver.snsy.edu.cn/authserver/oauth2.0/authorize?response_type=code&client_id=1357284271260286976&redirect_uri=https://ai.snsy.edu.cn';
          window.location.href = authUrl;
          // 返回一个 never-resolving promise 来阻止后续逻辑执行
          return new Promise(() => {});
        } catch (error) {
          console.error('重定向到认证页面失败:', error);
          return Promise.reject(error);
        }
      }

      if (domain === 'owner-b.xauat.edu.cn' || domain === 'ai.snsy.edu.cn') {
        // 私有部署域名只需要校验code
        if (code && typeof code === 'string') {
          try {
            const api = isPrivateDeployment({ code, orgCode: '' });
            const res = await api;
            setUserInfo(res);
            setToken(res.accessToken);

            // 移除 URL 中的 code 和 orgCode 参数
            const { pathname, query } = router;
            delete query.code;
            delete query.orgCode;
            await router.replace({ pathname, query }, undefined, { shallow: true });

            return res;
          } catch (error) {
            console.error('Third-party login failed:', error);
            Toast.error('第三方登录失败');
            clearToken().finally(() => {
              router.replace('/login').finally(() => {
                setUserInfo(null);
              });
            });
            router.replace('/login');
            return Promise.reject('第三方登录失败');
          }
        }
      } else if (code && orgCode && typeof code === 'string' && typeof orgCode === 'string') {
        // 普通第三方登录需要校验code和orgCode
        try {
          const api = isPrivateDeployment({ code, orgCode });
          const res = await api;
          setUserInfo(res);
          setToken(res.accessToken);

          // 移除 URL 中的 code 和 orgCode 参数
          const { pathname, query } = router;
          delete query.code;
          delete query.orgCode;
          await router.replace({ pathname, query }, undefined, { shallow: true });

          return res;
        } catch (error) {
          console.error('Third-party login failed:', error);
          Toast.error('第三方登录失败');
          clearToken().finally(() => {
            router.replace('/login').finally(() => {
              setUserInfo(null);
            });
          });
          router.replace('/login');
          return Promise.reject('第三方登录失败');
        }
      }

      // oauth测试

      // 默认初始化用户信息
      return initUserInfo();
    },
    {
      enabled: !unauth && !userInfo,
      onSuccess(res: LoginRes | UserInfoType) {
        setGeneraAppId(
          res.defaultApp?.status === AppStatus.Online ? res.defaultApp?.id : undefined
        );
      },
      onError() {
        // 如果是认证重定向的路径，不触发登录提示
        if (
          (domain === 'owner-b.xauat.edu.cn' && path.startsWith('/xajz')) ||
          (domain === 'ai.snsy.edu.cn' && path.startsWith('/xqsy'))
        ) {
          return;
        }

        // 根据不同域名判断登录失败的提示条件
        if (domain === 'owner-b.xauat.edu.cn' || domain === 'ai.snsy.edu.cn') {
          // 私有部署域名只需要code
          if (!code) {
            router.replace(
              `/login?lastRoute=${encodeURIComponent(location.pathname + location.search)}`
            );
            Toast.warning('请先登录');
          }
        } else if (!code || !orgCode) {
          // 其他第三方登录需要code和orgCode
          router.replace(
            `/login?lastRoute=${encodeURIComponent(location.pathname + location.search)}`
          );
          Toast.warning('请先登录');
        }
      }
    }
  );
  useEffect(() => {
    setContentSize(
      offsetWidth && offsetHeight
        ? {
            width: offsetWidth,
            height: offsetHeight
          }
        : undefined
    );
  }, [offsetWidth, offsetHeight, setContentSize]);

  useEffect(() => {
    if (isAccessDenied) {
      const path = getTheFirstRoute();
      router.push(path || '/home');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAccessDenied]);

  // 若页面没有权限，则获取权限路由列表的第一个路由
  const getTheFirstRoute = () => {
    const chatNavs =
      routeGroup?.navRoutes
        .map((route) => {
          if (!route.code?.startsWith('demo')) {
            return route;
          }
        })
        .filter(Boolean) || [];
    return chatNavs?.length > 0 ? chatNavs[0]?.path : '/home';
  };

  return (
    <Box
      ref={setElement}
      h="100%"
      overflow="hidden"
      {...(routeGroup?.type === RouteGroupTypeEnum.Admin && { px: respDims(24), py: respDims(16) })}
      {...props}
    >
      {unauth || (!!userInfo && !isAccessDenied) ? children : null}
    </Box>
  );
};

export default Content;
