import { Box, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import LayoutOverlay from '@/components/LayoutOverlay';
import PersonalCenter from './components/PersonalCenter';

const UserInfo = ({ onClose }: { onClose: () => void }) => {
  return (
    <LayoutOverlay backdropFilter="blur(10px) hue-rotate(90deg)" onClose={onClose}>
      <Flex w="100%" h="100%" position="relative">
        <Box
          w={respDims(36)}
          h={respDims(36)}
          bg="#CC525F"
          borderRadius="8px"
          position="absolute"
          top="12px"
          right="24px"
          display="flex"
          justifyContent="center"
          alignItems="center"
          cursor="pointer"
          onClick={onClose}
        >
          <SvgIcon name="close" w="12px" h="12px" color="#fff"></SvgIcon>
        </Box>
        <Flex
          flex="1"
          h="100%"
          display="flex"
          justifyContent="center"
          alignItems="center"
          px={respDims(24)}
          pt="0"
          pb={respDims(24)}
          bgColor="#f6f9fa"
          borderRadius={respDims(20)}
        >
          <PersonalCenter />
        </Flex>
      </Flex>
    </LayoutOverlay>
  );
};

export default UserInfo;
