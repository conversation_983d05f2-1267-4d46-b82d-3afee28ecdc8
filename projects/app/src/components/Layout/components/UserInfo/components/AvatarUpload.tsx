import React, { useState } from 'react';
import { Box, Center, Image } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';

interface AvatarUploadProps {
  getValues: (key: string) => string;
  onOpenSelectFile: () => void;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({ getValues, onOpenSelectFile }) => {
  const [isHovered, setIsHovered] = useState<boolean>(false);

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation(); // 阻止事件冒泡
    onOpenSelectFile();
  };

  return (
    <Center pb="16px">
      <Box
        position="relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        w="80px"
        h="80px"
      >
        <Image
          src={getValues('avatarUrl')}
          w="100%"
          h="100%"
          borderRadius="50%"
          objectFit="cover" // 确保图像覆盖整个圆形区域
          cursor="pointer"
          border="1px solid rgba(255,255,255,0.31)"
          onClick={onOpenSelectFile}
        />
        {isHovered && (
          <Box
            w="100%"
            h="100%"
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bg="rgba(0,0,0,0.6)"
            borderRadius="50%" // 确保覆盖层也是圆形的
            display="flex"
            alignItems="center"
            justifyContent="center"
            cursor="pointer"
            onClick={handleClick}
          >
            <SvgIcon name="avatarBg" w="26px" h="26px" color="white" />
          </Box>
        )}
      </Box>
    </Center>
  );
};

export default AvatarUpload;
