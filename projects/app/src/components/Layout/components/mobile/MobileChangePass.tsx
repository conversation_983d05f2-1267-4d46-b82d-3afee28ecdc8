import { rpxDim } from '@/utils/chakra';
import { Box, Button, Center, Flex, Input, ModalBody } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import MyModal from '@/components/MyModal';
import SvgIcon from '@/components/SvgIcon';
import { useSystemStore } from '@/store/useSystemStore';
import { updateUser } from '@/api/user';
import { useToast } from '@/hooks/useToast';

type FormType = {
  oldPsw: string;
  newPsw: string;
  confirmPsw: string;
};

const MobileChangePass = ({ onClose }: { onClose: () => void }) => {
  const { t } = useTranslation();
  const { isPc } = useSystemStore();
  const { register, handleSubmit } = useForm<FormType>({
    defaultValues: {
      oldPsw: '',
      newPsw: '',
      confirmPsw: ''
    }
  });

  const { toast } = useToast();

  const { mutate: onSubmit, isLoading } = useRequest({
    mutationFn: (data: FormType) => {
      if (data.newPsw !== data.confirmPsw) {
        toast({
          title: '新密码和确认密码不一致',
          status: 'error'
        });
        return Promise.reject();
      }
      return updateUser({
        oldPassword: data.oldPsw,
        password: data.newPsw
      });
    },
    onSuccess() {},
    successToast: '修改密码成功'
  });

  return (
    <MyModal
      minH="100%"
      minW="100%"
      {...(!isPc && { borderRadius: '0' })}
      title={
        <>
          <Flex flex="1">
            <SvgIcon
              name="chevronLeft"
              w={rpxDim(48)}
              h={rpxDim(48)}
              cursor="pointer"
              onClick={onClose}
              alignSelf="start"
            />
          </Flex>
          <Center color="rgba(0,0,0,0.9)" fontSize={rpxDim(36)} fontWeight="600">
            修改密码
          </Center>
        </>
      }
      isOpen
      overflow="auto"
    >
      <ModalBody>
        <Box mt={rpxDim(30)} mr={rpxDim(20)} ml={rpxDim(20)}>
          <Box>
            <Box fontSize={rpxDim(32)} mb={rpxDim(14)}>
              <Box mr={rpxDim(10)} display="inline-block" color="#F53F3F">
                *
              </Box>
              旧密码
            </Box>
            <Input
              backgroundColor="#F9F9F9"
              _placeholder={{ color: '#86909C', fontSize: rpxDim(28) }}
              placeholder="请输入"
              flex={1}
              type={'password'}
              {...register('oldPsw', { required: true })}
            ></Input>
          </Box>
          <Box mt={rpxDim(32)}>
            <Box fontSize={rpxDim(32)} mb={rpxDim(14)}>
              <Box mr={rpxDim(10)} display="inline-block" color="#F53F3F">
                *
              </Box>
              新密码
            </Box>
            <Input
              backgroundColor="#F9F9F9"
              _placeholder={{ color: '#86909C', fontSize: rpxDim(28) }}
              flex={1}
              placeholder="请输入"
              type={'password'}
              {...register('newPsw', {
                required: true,
                minLength: { value: 8, message: '密码最少8位' },
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d\W_]{8,16}$/,
                  message: '密码需包含大小写字母和数字的组合，可以使用特殊字符'
                }
              })}
            ></Input>
          </Box>

          <Box mt={rpxDim(32)}>
            <Box fontSize={rpxDim(32)} mb={rpxDim(14)}>
              <Box mr={rpxDim(10)} display="inline-block" color="#F53F3F">
                *
              </Box>
              确认密码
            </Box>
            <Input
              backgroundColor="#F9F9F9"
              _placeholder={{ color: '#86909C', fontSize: rpxDim(28) }}
              flex={1}
              placeholder="请输入"
              type={'password'}
              {...register('confirmPsw', {
                required: true,
                maxLength: {
                  value: 20,
                  message: '密码最少 4 位最多 20 位'
                }
              })}
            ></Input>
          </Box>

          <Flex position="fixed" bottom={rpxDim(40)} alignItems="center" w="100%">
            <Button
              fontSize={rpxDim(32)}
              color="#3D7FFF"
              mr={rpxDim(26)}
              backgroundColor="#EDF3FF"
              h={rpxDim(80)}
              w={rpxDim(320)}
              onClick={() => {
                onClose();
              }}
            >
              取消
            </Button>
            <Button
              fontSize={rpxDim(32)}
              color="#fff"
              isLoading={isLoading}
              onClick={handleSubmit((data) => onSubmit(data))}
              h={rpxDim(80)}
              w={rpxDim(320)}
            >
              确认
            </Button>
          </Flex>
        </Box>
      </ModalBody>
    </MyModal>
  );
};
export default MobileChangePass;
