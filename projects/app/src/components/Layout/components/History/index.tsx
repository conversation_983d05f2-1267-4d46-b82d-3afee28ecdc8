import { Box, Center, Flex, Image, Input, InputGroup, InputRightElement } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useSystemStore } from '@/store/useSystemStore';
import { useMemo, useState, useRef, useEffect, useCallback } from 'react';
import dayjs from 'dayjs';
import { ChatHistoryItemType } from '@/fastgpt/global/core/chat/type';
import { LOGO_ICON } from '@/constants/common';
import { MessageBox } from '@/utils/ui/messageBox';
import ChatSettingsModal from '@/components/ChatSettingsModal';
import FileIcon from './components/FileIcon';
import { formatFileSize } from '@/utils/tools';
import LayoutOverlay from '@/components/LayoutOverlay';
import DialogueList from './components/DialogueList';
import Lottie from '@/components/Lottie';
import useHistoryData from '@/hooks/useHistoryData';

export type ChatHistoryRef = {
  loadMore: () => void;
};

const History = ({ onClose }: { onClose: () => void }) => {
  const { isPc } = useSystemStore();
  const [inputKeyword, setInputKeyword] = useState('');
  const [queryKeyword, setQueryKeyword] = useState('');
  const [settingsChatId, setSettingsChatId] = useState('');
  const { data, loadMoreData, deleteData, refetch } = useHistoryData(
    { size: 20, current: 1, keyword: queryKeyword },
    'historyPage'
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const [expanded, setExpanded] = useState(false);
  const [isHistoryList, setIsHistoryList] = useState(true);
  const [getChatId, setGetChatId] = useState('');
  const [getTenantAppId, setGetTenantAppId] = useState('');
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);

  const handleLoadMore = useCallback(async () => {
    if (!isLoadingMore) {
      setIsLoadingMore(true);
      await loadMoreData();
      setIsLoadingMore(false);
    }
  }, [loadMoreData, isLoadingMore]);

  const switchDialogueList = (chatId: string, tenantAppId: string) => {
    setScrollPosition(containerRef.current?.scrollTop || 0);
    setGetChatId(chatId);
    setGetTenantAppId(tenantAppId);
    setIsHistoryList(false);
  };

  const list = useMemo(() => {
    const list = data && data.length ? data.map((it) => ({ ...it, title: it.title })) : [];
    if (!list.length) {
      return list;
    }
    const today = dayjs().startOf('day');
    let lastType = '';
    const result: (ChatHistoryItemType | string)[] = [];
    list
      .sort((l, r) => dayjs(r.updateTime).valueOf() - dayjs(l.updateTime).valueOf())
      .forEach((it) => {
        const t = dayjs(it.updateTime).startOf('day');
        const d = today.diff(t, 'day');
        let type = '';
        if (d === 0) {
          type = '今天';
        } else if (d == 1) {
          type = '昨天';
        } else if (d <= 7 && (t.day() ? t.day() : 7) < (today.day() ? today.day() : 7)) {
          type = '本周';
        } else if (t.year() === today.year()) {
          type = `${t.month() + 1}月`;
        } else {
          type = `${t.year()}年`;
        }
        if (type !== lastType) {
          result.push(type);
          lastType = type;
        }
        result.push(it);
      });
    return result;
  }, [data]);

  const formatTime = (updateTime: Date) => {
    const today = dayjs().startOf('day');
    const t = dayjs(updateTime).startOf('day');
    const d = today.diff(t, 'day');
    if (d === 0) {
      return dayjs(updateTime).format('HH:mm');
    } else if (d <= 7) {
      return `周${'日一二三四五六'.charAt(t.day())}`;
    } else if (t.year() === today.year()) {
      return dayjs(updateTime).format('MM-DD');
    } else {
      return dayjs(updateTime).format('YYYY-MM-DD');
    }
  };

  const onChatDelete = (chatId: string, id: string) => {
    MessageBox.confirm({
      title: '操作确认',
      content: '确定删除该记录？',
      onOk: async () => {
        await deleteData(chatId, id);
      }
    });
  };

  const onSearch = () => {
    setQueryKeyword(inputKeyword);
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  };

  useEffect(() => {
    if (!isInitialLoad) {
      refetch();
    } else {
      setIsInitialLoad(false);
    }
  }, [queryKeyword, refetch]);

  useEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
        if (scrollTop + clientHeight >= scrollHeight - 10) {
          if (loadingTimeoutRef.current) {
            clearTimeout(loadingTimeoutRef.current);
          }
          loadingTimeoutRef.current = setTimeout(() => {
            handleLoadMore();
          }, 200);
        }
      }
    };

    if (containerRef.current) {
      containerRef.current.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('scroll', handleScroll);
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [handleLoadMore]);

  useEffect(() => {
    if (isHistoryList && containerRef.current) {
      containerRef.current.scrollTop = scrollPosition;
    }
  }, [isHistoryList, scrollPosition]);

  return (
    <LayoutOverlay backgroundColor="rgba(248,250,252,0.92)" onClose={onClose}>
      {isHistoryList ? (
        <Flex w="100%" h="100%" position="relative" justifyContent="center">
          <Box
            w={respDims(36)}
            h={respDims(36)}
            bg="#CC525F"
            borderRadius="8px"
            position="absolute"
            top="12px"
            right="24px"
            display="flex"
            justifyContent="center"
            alignItems="center"
            cursor="pointer"
            onClick={onClose}
          >
            <SvgIcon name="close" w="12px" h="12px" color="#fff"></SvgIcon>
          </Box>
          <Flex h="100%" mt="12px" w="768px" direction="column">
            <Center fontSize="24px" color=" #030712" fontWeight="500">
              历史会话
            </Center>

            <InputGroup alignSelf="stretch" px={respDims(8)} mt={respDims(32)} mb={respDims(11)}>
              <Input
                placeholder="搜索历史会话记录，请输入会话标题、应用名称"
                border="1px solid #DCDCDC"
                autoFocus={false}
                borderRadius="8px"
                fontWeight="400"
                pl={respDims(24)}
                pr={respDims(60)}
                color="rgba(0,0,0,0.4)"
                {...(isPc
                  ? {
                      h: respDims('52fpx'),
                      bgColor: '#fff',
                      fontSize: respDims('16fpx'),
                      lineHeight: respDims('22fpx'),
                      _placeholder: {
                        fontsize: 'inherit',
                        lineHeight: 'inherit'
                      }
                    }
                  : {
                      h: rpxDim(72),
                      bgColor: '#fff',
                      fontSize: rpxDim(28),
                      lineHeight: rpxDim(44)
                    })}
                value={inputKeyword}
                onChange={(e) => setInputKeyword(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    onSearch();
                    if (!isPc) {
                      e.currentTarget.blur();
                      e.currentTarget.scrollIntoView({
                        block: 'start'
                      });
                    }
                  }
                }}
              />
              <InputRightElement mt={respDims(4)} mr={respDims(24)} onClick={onSearch}>
                <SvgIcon
                  name="search"
                  {...(isPc ? { w: '20px', h: '20px' } : { w: rpxDim(36), h: rpxDim(36) })}
                  color="#4E5969"
                />
              </InputRightElement>
            </InputGroup>

            <Box
              ref={containerRef}
              position="relative"
              overflowY="auto"
              overflowX="hidden"
              w="100%"
              h="100%"
              {...(!isPc && {
                userSelect: 'none'
              })}
              mb="10px"
              px={respDims(8)}
            >
              {list.length > 0 ? (
                list?.map((item, index) => {
                  if (typeof item === 'string') {
                    return (
                      <Box
                        key={`${item}-${index}`}
                        mt={respDims(24)}
                        mb={respDims(16)}
                        color="#909399"
                        fontSize={respDims('28rpx', '16fpx')}
                        lineHeight={respDims('33rpx', '19fpx')}
                        fontWeight="bold"
                      >
                        {item}
                      </Box>
                    );
                  }
                  return (
                    <Flex
                      key={item.id}
                      direction="column"
                      color="#303133"
                      fontSize={respDims('28rpx', '14fpx')}
                      borderRadius={respDims('16rpx', 8)}
                      px={respDims(24)}
                      py={respDims(12)}
                      mt={respDims(12)}
                      bgColor="#fff"
                      cursor="pointer"
                      onClick={() => {
                        switchDialogueList(item.chatId, item.tenantAppId!);
                      }}
                      {...(isPc && {
                        _hover: {
                          boxShadow:
                            '0px 0px 8px 0px rgba(0,0,0,0.07), 0px 2px 4px 0px rgba(0,0,0,0.03)',
                          '.controls-box': {
                            display: 'flex'
                          },
                          '.time-text': {
                            display: 'none'
                          }
                        }
                      })}
                    >
                      <Box w="100%" display="flex" alignItems="center">
                        <Image
                          src={item.appAvatarUrl || LOGO_ICON}
                          alt=""
                          w={rpxDim(26)}
                          h={rpxDim(26)}
                        />

                        <Box
                          color="#303133"
                          fontWeight="600"
                          fontSize={respDims('14rpx', '15fpx')}
                          style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: '1',
                            whiteSpace: 'normal',
                            wordBreak: 'break-word',
                            wordWrap: 'break-word'
                          }}
                          m="0 8px"
                        >
                          {item.appName}
                        </Box>
                        <Flex flex="1" justifyContent="space-between" alignItems="center">
                          <Box
                            flex="1"
                            fontSize={respDims('14rpx', '14fpx')}
                            fontWeight="400"
                            style={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitBoxOrient: 'vertical',
                              WebkitLineClamp: '1',
                              whiteSpace: 'normal',
                              wordBreak: 'break-word',
                              wordWrap: 'break-word'
                            }}
                          >
                            {item.title}
                          </Box>
                          <Box
                            className="time-text"
                            fontSize="13px"
                            color="#606266"
                            fontWeight="400"
                          >
                            {formatTime(item.updateTime!)}
                          </Box>
                          <Flex alignItems="center" className="controls-box" display="none">
                            <SvgIcon
                              name="chatEdit"
                              mr="16px"
                              w="22px"
                              h="22px"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSettingsChatId(item.chatId);
                              }}
                            ></SvgIcon>
                            <SvgIcon
                              name="chatDelete"
                              w="22px"
                              h="22px"
                              onClick={(e) => {
                                e.stopPropagation();
                                onChatDelete(item.chatId, item.id as string);
                              }}
                            ></SvgIcon>
                          </Flex>
                        </Flex>
                      </Box>
                      {!!item.files?.length && (
                        <Flex direction="column" mt="8px">
                          <Flex wrap="wrap">
                            {item.files?.slice(0, expanded ? item.files.length : 6).map((file) => (
                              <Box
                                key={file.fileKey}
                                border="1px solid #E8E8E8"
                                borderRadius="8px"
                                p="14px 12px"
                                m="4px"
                                w="calc(33% - 10px)"
                                display="flex"
                                alignItems="center"
                              >
                                <Box mr="12px">
                                  <FileIcon
                                    {...{
                                      fileUrl: file.fileUrl,
                                      fileType: Number(file.fileType),
                                      fileName: file.fileName || ''
                                    }}
                                  />
                                </Box>
                                <Box
                                  flex="1"
                                  overflow="hidden"
                                  textOverflow="ellipsis"
                                  whiteSpace="nowrap"
                                >
                                  <Box
                                    color="#1D2129"
                                    fontSize={respDims('14rpx', '14fpx')}
                                    overflow="hidden"
                                    textOverflow="ellipsis"
                                    whiteSpace="nowrap"
                                  >
                                    {file.fileName}
                                  </Box>

                                  <Box
                                    ml="8px"
                                    color="#909399"
                                    fontSize={respDims('12rpx', '12fpx')}
                                  >
                                    {file.fileSize ? formatFileSize(file.fileSize!) : ''}
                                  </Box>
                                </Box>
                              </Box>
                            ))}
                          </Flex>
                          {item?.files && item.files.length > 6 && (
                            <Box
                              mt="8px"
                              color="#909399"
                              cursor="pointer"
                              onClick={() => setExpanded(!expanded)}
                              display="flex"
                              justifyContent="center"
                              alignItems="center"
                            >
                              {expanded ? '收起' : '查看更多'}
                              <SvgIcon
                                name={expanded ? 'chevronUp' : 'chevronDown'}
                                w="18px"
                                h="18px"
                                color="#909399"
                              ></SvgIcon>
                            </Box>
                          )}
                        </Flex>
                      )}
                    </Flex>
                  );
                })
              ) : (
                <Box
                  position="absolute"
                  top="50%"
                  left="50%"
                  transform="translate(-50%, -50%)"
                  color="#a9a9ac"
                >
                  暂无数据
                </Box>
              )}
            </Box>
          </Flex>

          {settingsChatId && (
            <ChatSettingsModal chatId={settingsChatId} onClose={() => setSettingsChatId('')} />
          )}
        </Flex>
      ) : (
        <DialogueList
          chatId={getChatId}
          tenantAppId={getTenantAppId}
          onBack={() => setIsHistoryList(true)}
        />
      )}

      {isLoadingMore && (
        <Center>
          <Lottie name="Loading" w={respDims('54rpx', 24, 24)} h={respDims('54rpx', 24, 24)} />
        </Center>
      )}
    </LayoutOverlay>
  );
};

export default History;
