import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { getFile2SvgIcon } from '@/components/SvgIcon/utils';
import { FileTypeEnum } from '@/constants/api/cloud';
import { FileInfo } from '@/types/api/cloud';
import { respDims } from '@/utils/chakra';
import { isImageFile } from '@/utils/file/format';
import { ChakraProps, Image } from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';

const FileIcon = ({
  fileType,
  fileName,
  fileUrl,
  file,
  files,
  localFile,
  w,
  h
}: {
  fileType?: FileTypeEnum;
  fileName?: string;
  fileUrl?: string;
  file?: FileInfo;
  files?: FileInfo;
  localFile?: File;
  w?: ChakraProps['w'];
  h?: ChakraProps['h'];
}) => {
  const [localUrl, setLocalUrl] = useState<string>();

  const name = fileName || file?.fileName || files?.fileName || localFile?.name || '';

  const url = fileUrl || file?.fileUrl || files?.fileUrl || localUrl || '';

  const { svgIcon, imgIcon }: { svgIcon?: SvgIconNameType; imgIcon?: string } = useMemo(() => {
    if (fileType === FileTypeEnum.Folder || fileType === FileTypeEnum.Space) {
      return { svgIcon: 'file2Folder' };
    }
    if (url && isImageFile(name)) {
      return { imgIcon: url };
    }
    return { svgIcon: getFile2SvgIcon(name) };
  }, [fileType, name, url]);

  w = w ?? respDims(40);
  h = h ?? respDims(40);

  useEffect(() => {
    if (!localFile || !isImageFile(localFile)) {
      return;
    }
    const reader = new FileReader();
    reader.onload = function (e) {
      setLocalUrl(e.target?.result as string);
    };
    reader.readAsDataURL(localFile);
  }, [localFile]);

  return svgIcon ? (
    <SvgIcon name={svgIcon} w={w} h={h} />
  ) : (
    <Image src={imgIcon} w={w} h={h} alt="" objectFit="fill" px={respDims(5)} py={respDims(7)} />
  );
};

export default FileIcon;
