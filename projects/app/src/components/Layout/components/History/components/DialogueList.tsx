import { Box, BoxProps, Button, Center, Flex, Image, Textarea } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { ChatSiteItemType, UserChatItemValueItemType } from '@/fastgpt/global/core/chat/type';
import { useMemo, useRef, useState } from 'react';
import Markdown, { CodeClassName } from '@/components/Markdown';
const ImageBlock = dynamic(() => import('@/components/Markdown/img/Image'));
import { useQuery } from '@tanstack/react-query';
import { getInitChatInfo } from '@/api/chat';
import { ChatFileType, InitChatResponse } from '@/types/api/chat';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  ChatStatusEnum
} from '@/fastgpt/global/core/chat/constants';
import dynamic from 'next/dynamic';
import { useSystemStore } from '@/store/useSystemStore';
import { getFileList } from '@/api/file';
import { fileTypeInfos } from '@/components/ChatBox/MessageInput';
import { formatFileSize, getNanoid } from '@/utils/tools';
import { useChatStore } from '@/store/useChatStore';
import { useUserStore } from '@/store/useUserStore';
import Loading from '@/components/Loading';
import AdvIcon from '@/components/AdvIcon';
import { useRouter } from 'next/router';

const messageCardStyle: BoxProps = {
  px: respDims('32rpx', 32),
  py: respDims('24rpx', 20),
  maxW: '100%',
  display: 'inline-block',
  borderRadius: respDims('40rpx', 24),
  fontSize: respDims('28rpx', '14fpx')
};

const aiMessageCardStyle: BoxProps = {
  ...messageCardStyle,
  bgColor: '#fff!important',
  borderRadius: '3px 24px 24px 24px',
  boxShadow: '0px 3px 13px 0px rgba(0,0,0,0.04)',
  _hover: {
    '& .chat-controller': {
      visibility: 'visible'
    }
  }
};

const humanMessageCardStyle: BoxProps = {
  ...messageCardStyle,
  color: '#303133',
  bgColor: '#E7F0FF !important',
  borderRadius: '24px 3px 24px 24px',
  boxShadow: '0px 3px 13px 0px rgba(0,0,0,0.04)',
  _hover: {
    '& .chat-controller': {
      visibility: 'visible'
    }
  }
};
const fullscreenKey = 'lk-fullscreen';
const DialogueListModal = ({
  chatId,
  tenantAppId,
  onBack
}: {
  chatId: string;
  tenantAppId: string;
  onBack: () => void;
}) => {
  const chatItemEditRef = useRef<HTMLTextAreaElement>(null);
  const [chatItemEditDataId, setChatItemEditDataId] = useState<string>();
  const [chatItemEditValue, setChatItemEditValue] = useState<string>();
  const [chatHistory, setChatHistory] = useState<ChatSiteItemType[]>([]);
  const [questionGuides, setQuestionGuide] = useState<string[]>([]);
  const { isPc } = useSystemStore();
  const [chatFiles, setChatFiles] = useState<ChatFileType[]>([]);
  const { userInfo } = useUserStore();
  const router = useRouter();
  const [fullscreen, setFullscreen] = useState(
    () => localStorage.getItem(fullscreenKey) === 'true'
  );
  const maxWidth = fullscreen ? '100%' : '768px';
  const getChatFilesByIds = (ids: string[]) => {
    return ids.map((it) => chatFileMap[it]).filter((it) => it);
  };

  const {
    chatData,
    setChatFiles: setStoreChatFiles,
    setChatHistory: setStoreChatHistory
  } = useChatStore();

  const chatFileMap = useMemo(
    () =>
      chatFiles.reduce((map, it) => ((map[it.id] = it), map), {} as Record<string, ChatFileType>),
    [chatFiles]
  );

  const initChatFileIds = useMemo(() => {
    const ids = [] as string[];
    chatHistory.forEach((it) => {
      it.value?.forEach((it) => {
        it.type;
        if (
          it.type === ChatItemValueTypeEnum.file &&
          it.file?.type === 'file' &&
          !chatFileMap[it.file?.fileId!]
        ) {
          ids.push(it.file?.fileId!);
        }
      });
    });
    return ids;
  }, [chatHistory]);

  const { data: historiesList = {} as InitChatResponse, isLoading: isLoadingHistories } = useQuery(
    ['histories'],
    () => getInitChatInfo({ chatId, tenantAppId }).then((res) => res),
    {
      onSuccess(res) {
        const history = res.history.map(
          (item) =>
            ({
              ...item,
              dataId: item.dataId || getNanoid(),
              status: ChatStatusEnum.finish,
              value: item.value || [],
              responseData: (item.obj === ChatRoleEnum.AI && item.responseData) || []
            }) as ChatSiteItemType
        );

        setChatHistory(history);
      }
    }
  );

  const loading = isLoadingHistories;

  const onContinueChat = () => {
    if (historiesList.sceneList.length) {
      const sceneId = historiesList.sceneList[0].tenantSceneId;
      router.push(
        `/home?appId=${historiesList.appId}&chatId=${historiesList.chatId}&sceneId=${sceneId}`
      );
    } else {
      router.push(`/home?appId=${historiesList.appId}&chatId=${historiesList.chatId}`);
    }
  };

  useQuery(['getChatFiles', initChatFileIds], () => getFileList(initChatFileIds), {
    enabled: initChatFileIds?.length > 0,
    onSuccess: (res) => {
      let data =
        res?.map((it) => {
          const type = it.fileName.substring(it.fileName.lastIndexOf('.') + 1).toLocaleLowerCase();
          const typeInfo = fileTypeInfos.find((it) => it.name === type);
          return {
            id: it.fileKey,
            name: it.fileName,
            svgIcon: typeInfo?.svgIcon,
            imgIcon: it.fileUrl,
            type,
            sizeText: formatFileSize(it.fileSize).replaceAll(' ', '')
          };
        }) || [];
      setChatFiles((state) => [...state, ...data]);
      setStoreChatFiles(data);
    }
  });

  return (
    <Flex w="100%" h="100%" overflow="hidden">
      <Flex p="16px 0 0 24px" w="100%" h="100%" overflow="hidden" bgColor="#f9fafc">
        <SvgIcon
          name="chevronLeft"
          w={respDims(36)}
          h={respDims(36)}
          p={respDims(6)}
          cursor="pointer"
          onClick={onBack}
          borderRadius="8px"
          bgColor="#e6e7eb"
        />
        <Flex flex="1" alignItems="center" flexDirection="column" h="100%">
          <Flex align="center">
            <AdvIcon
              name={historiesList.appAvatarUrl}
              w={respDims(28)}
              h={respDims(28)}
              mr="12px"
            />
            <Box fontSize={respDims(16, 14)} color="#030712" fontWeight="500">
              {historiesList?.appName}
            </Box>
          </Flex>

          <Box w="50%" flex="1" overflow="auto" {...(isPc && { maxW: maxWidth })}>
            <Box alignSelf="center" id={'history'} pt={respDims('32rpx', 32)} w="100%">
              {historiesList?.history &&
                historiesList?.history.map((item, index) => (
                  <Box id={item.dataId} key={index}>
                    {item.obj === 'Human' && (
                      <>
                        <Flex
                          w="100%"
                          justifyContent="flex-end"
                          pr="10px"
                          sx={{
                            '& .chakra-link': {
                              color: 'white !important'
                            }
                          }}
                        >
                          <Box
                            minW="280px"
                            ml={respDims('0rpx', 100, 10)}
                            pb={respDims('32rpx', 32)}
                            textAlign="right"
                          >
                            <Box
                              className="markdown"
                              pos="relative"
                              textAlign="left"
                              {...humanMessageCardStyle}
                            >
                              {item.value.map((subItem: UserChatItemValueItemType, subIndex) => (
                                <Box key={subIndex} {...subItem}>
                                  {chatItemEditDataId === item.dataId ? (
                                    <Textarea
                                      ref={chatItemEditRef}
                                      value={chatItemEditValue}
                                      p="0"
                                      borderRadius="0"
                                      maxW="100%"
                                      minW="10em"
                                      minH="1em"
                                      border="none"
                                      outline="none"
                                      boxShadow="none"
                                      resize="none"
                                      autoFocus
                                      overflow="hidden"
                                      _focus={{
                                        bgColor: 'transparent',
                                        border: 'none',
                                        outline: 'none',
                                        boxShadow: 'none'
                                      }}
                                      onChange={(e) => setChatItemEditValue(e.target.value)}
                                    />
                                  ) : subItem.type === ChatItemValueTypeEnum.text &&
                                    subItem.text ? (
                                    <Markdown source={subItem.text.content} isChatting={false} />
                                  ) : subItem.type === ChatItemValueTypeEnum.file &&
                                    subItem.file?.type === ChatFileTypeEnum.image ? (
                                    <ImageBlock src={subItem.file?.url} />
                                  ) : null}
                                </Box>
                              ))}
                            </Box>

                            {!!item.value &&
                              ((files) =>
                                !!files.length && (
                                  <Flex
                                    {...(isPc
                                      ? { flexWrap: 'wrap' }
                                      : {
                                          flexDir: 'column',
                                          mt: rpxDim(16),
                                          alignItems: 'flex-end'
                                        })}
                                  >
                                    {files.map((it, index) => (
                                      <Flex
                                        key={index}
                                        {...it}
                                        mt={respDims('16rpx', 16)}
                                        ml={respDims('0rpx', 16)}
                                        alignItems="center"
                                        w={respDims('404rpx', 234, 200)}
                                        px={respDims('12rpx', 12)}
                                        py={respDims('12rpx', 10)}
                                        bgColor="#FFFFFF"
                                        boxShadow="0px 0px 10px 0px rgba(62,71,83,0.12)"
                                        borderRadius={respDims('16rpx', 8)}
                                      >
                                        {it.svgIcon ? (
                                          <SvgIcon
                                            flexShrink="0"
                                            name={it.svgIcon}
                                            w={respDims('88rpx', '40fpx')}
                                            h={respDims('88rpx', '40fpx')}
                                          />
                                        ) : it.imgIcon ? (
                                          <Image
                                            flexShrink="0"
                                            w={respDims('88rpx', '40fpx')}
                                            h={respDims('88rpx', '40fpx')}
                                            src={it.imgIcon}
                                            alt=""
                                          />
                                        ) : undefined}

                                        <Box
                                          flex="1"
                                          ml={respDims('12rpx', 12)}
                                          whiteSpace="nowrap"
                                          overflow="hidden"
                                          textOverflow="ellipsis"
                                          textAlign="left"
                                        >
                                          <Box
                                            color="#1D2129"
                                            fontSize={respDims('24rpx', '14fpx')}
                                            lineHeight={respDims('44rpx', '22fpx')}
                                            overflowX="scroll"
                                            css={{
                                              '&::-webkit-scrollbar': {
                                                display: 'none'
                                              },
                                              scrollbarWidth: 'none'
                                            }}
                                          >
                                            {it.name}
                                          </Box>
                                          <Box
                                            mt={respDims('0rpx', 4)}
                                            color="#909399"
                                            fontSize={respDims('24rpx', '13fpx')}
                                            lineHeight={respDims('44rpx', '22fpx')}
                                          >
                                            {it.type}, {it.sizeText}
                                          </Box>
                                        </Box>
                                      </Flex>
                                    ))}
                                  </Flex>
                                ))(
                                getChatFilesByIds(
                                  item.value
                                    ?.filter((it) => it.type === ChatItemValueTypeEnum.file)
                                    ?.map((it) => it.file?.fileId!)
                                )
                              )}
                          </Box>

                          {isPc && (
                            <ChatAvatar
                              src={userInfo?.avatar}
                              type="Human"
                              flexShrink="0"
                              ml={respDims(14, 4)}
                            />
                          )}
                        </Flex>
                      </>
                    )}
                    {item.obj === 'AI' && (
                      <>
                        <Flex w="100%">
                          {isPc && (
                            <ChatAvatar
                              src={item.chatAppAvatarUrl}
                              type="AI"
                              flexShrink="0"
                              mr={respDims(14, 4)}
                            />
                          )}

                          <Flex
                            flexDir="column"
                            alignItems="flex-start"
                            minW="280px"
                            mr={respDims('0rpx', 100, 10)}
                            pb={respDims('32rpx', 32)}
                          >
                            <Box pos="relative" {...aiMessageCardStyle}>
                              <Markdown
                                source={(() => {
                                  let text = '';
                                  if (item.value && Array.isArray(item.value)) {
                                    text = item.value
                                      .map((it) => {
                                        if (typeof it.text?.content === 'string') {
                                          return it.text.content;
                                        }
                                        return '';
                                      })
                                      .join('');
                                  }
                                  // replace quote tag: [source1] 标识第一个来源，需要提取数字1，从而去数组里查找来源
                                  const quoteReg = /\[source:(.+)\]/g;
                                  const replaceText = text
                                    ? text.replace(quoteReg, `[QUOTE SIGN]($1)`)
                                    : '';

                                  if (
                                    index === chatHistory.length - 1 &&
                                    questionGuides.length > 0
                                  ) {
                                    return `${replaceText}\n\`\`\`${
                                      CodeClassName.questionGuide
                                    }\n${JSON.stringify(questionGuides)}`;
                                  }
                                  return replaceText;
                                })()}
                              />
                            </Box>
                          </Flex>
                        </Flex>
                      </>
                    )}
                  </Box>
                ))}
            </Box>
          </Box>

          <Flex
            w="100%"
            justifyContent="center"
            px={respDims('0rpx', 32)}
            pt={respDims('0rpx', 24)}
            mb="32px"
          >
            <Button
              w="50%"
              h={respDims(46, 36)}
              ml={respDims(10)}
              variant="primary"
              fontSize={respDims(14, 12)}
              onClick={() => onContinueChat()}
              borderRadius="50px"
            >
              继续聊天
            </Button>
          </Flex>
        </Flex>
      </Flex>
      {loading && <Loading fixed={false} />}
    </Flex>
  );
};

function ChatAvatar({ src, type, ...props }: { src?: string; type: 'Human' | 'AI' } & BoxProps) {
  return (
    <Image
      w={respDims(40)}
      h={respDims(40)}
      borderRadius="50%"
      overflow="hidden"
      src={src}
      alt=""
      objectFit="cover"
      {...props}
    />
  );
}

export default DialogueListModal;
