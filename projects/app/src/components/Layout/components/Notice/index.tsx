import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, HStack, Spinner, Center, ChakraProps } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { Badge, Popover } from 'antd';
import { respDims } from '@/utils/chakra';
import { getCloudNoticeListPage } from '@/api/cloud';
import { useNotificationStore } from '@/store/useTificationContext';
import styles from './notice.module.scss';

interface Notification {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: boolean | null;
  tenantId: number | null;
  noticeType: number;
  fileType: number;
  fileId: number;
  senderTmbId: number;
  receiverTmbId: number;
  content: string;
  auditorId: number;
  auditRemark: string;
  oldFolderId: number;
  fileName: string;
  parentId: number;
  parentName: string;
  oldFolderName: string;
  createrId: number;
  creater: string;
  operator: string;
}

const Notice = ({ ...props }: ChakraProps) => {
  const [selectedType, setSelectedType] = useState<string>('数据空间通知');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [isFetched, setIsFetched] = useState(false); // 添加 isFetched 状态
  const [isIconSelected, setIsIconSelected] = useState(false); // 添加 isIconSelected 状态
  const { tipsCount, markAllAsRead, fetchUnreadCount } = useNotificationStore();

  const fetchNotifications = useCallback(async () => {
    if (isFetched) return; // 如果已经获取过通知列表，则不再获取
    setLoading(true);
    try {
      const response = await getCloudNoticeListPage({ current: 1, size: 99999 });
      if (response.records.length) {
        await markAllAsRead();
      }
      setNotifications(response.records);
      setIsFetched(true); // 设置 isFetched 为 true
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [isFetched, markAllAsRead]);

  useEffect(() => {
    fetchUnreadCount();
  }, [fetchUnreadCount]);

  const notificationTitles: { [key: number]: string } = {
    1: '资料待审核通知',
    2: '资料审核结果通知',
    3: '资料审核结果通知',
    4: '资料被共享通知',
    5: '文件变更通知',
    6: '文件变更通知',
    7: '文件删除通知'
  };

  const renderNotificationContent = (notification: Notification) => {
    switch (notification.noticeType) {
      case 1:
        return (
          <React.Fragment>
            {notification.operator}在{' '}
            <Text as="span" color="primary.500" cursor="pointer">
              {notification.parentName}
            </Text>{' '}
            文件夹中上传了文件{' '}
            <Text as="span" color="primary.500" cursor="pointer">
              {notification.fileName}
            </Text>
          </React.Fragment>
        );
      case 2:
        return `${notification.operator}上传的文件 ${notification.fileName} 审核通过`;
      case 3:
        return `${notification.operator}上传的文件 ${notification.fileName} 审核不通过（原因：${notification.auditRemark}）`;
      case 4:
        return `${notification.operator}与你分享了文件 ${notification.fileName}`;
      case 5:
        return `${notification.operator}修改了文件 ${notification.fileName} 的名称`;
      case 6:
        return `${notification.operator}调整了文件 ${notification.fileName} 的位置`;
      case 7:
        return `${notification.operator}删除了文件 ${notification.fileName}`;
      default:
        return notification.content;
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      fetchNotifications();
    }
  };

  const handleIconClick = () => {
    setIsIconSelected(!isIconSelected);
    fetchNotifications();
  };

  const handleVisibleChange = (visible: boolean) => {
    if (!visible) {
      setIsIconSelected(false);
    }
  };

  const content = (
    <Box display="flex" w={respDims('580fpx')}>
      <Box w={respDims('198fpx')} borderRight="1px solid #E5E7EB" pr={respDims(12)}>
        <HStack
          align="center"
          spacing={respDims(3)}
          cursor="pointer"
          onClick={() => setSelectedType('数据空间通知')}
          bg={selectedType === '数据空间通知' ? '#F8FAFC' : 'transparent'}
          p={respDims(8, 10)}
          borderRadius="md"
        >
          <SvgIcon name="navCloud" w={respDims('32fpx')} h={respDims('32fpx')} />
          <Text
            fontSize={respDims('15fpx')}
            color="#303133"
            fontWeight={selectedType === '数据空间通知' ? '500' : '400'}
          >
            数据空间通知
          </Text>
        </HStack>
      </Box>
      <Box flex="1" pl={respDims(16)} h={respDims(300)} overflowY="auto">
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <Spinner size="lg" />
          </Box>
        ) : notifications.length === 0 ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <Text fontSize="md" color="gray.500">
              暂无数据
            </Text>
          </Box>
        ) : (
          notifications.map((notification, index) => (
            <Box
              key={index}
              width="100%"
              pt={index === 0 ? respDims(6) : respDims(16)}
              borderBottom={index === notifications.length - 1 ? 'none' : '1px solid #E5E7EB'}
            >
              <Text fontSize={respDims('14fpx')} fontWeight="bold" mb={respDims(4)}>
                {notificationTitles[notification.noticeType]}
              </Text>
              <Text fontSize={respDims('14fpx')} lineHeight={respDims(28)} pb={respDims(6)}>
                {renderNotificationContent(notification)}
              </Text>
              <Text fontSize={respDims('12fpx')} color="gray.500" pb={respDims(9)}>
                {notification.createTime}
              </Text>
            </Box>
          ))
        )}
      </Box>
    </Box>
  );

  return (
    <Popover
      placement="bottomRight"
      content={content}
      trigger="click"
      overlayClassName={styles['custom-popover']}
      onVisibleChange={handleVisibleChange} // 添加 onVisibleChange 回调
    >
      <Center
        cursor="pointer"
        display="flex"
        justifyContent="center"
        alignItems="center"
        borderRadius={respDims(8)}
        p={respDims(8)}
        _hover={{
          bgColor: '#f6f6f6'
        }}
        onClick={handleIconClick}
        onKeyDown={handleKeyDown}
        {...props}
      >
        <Badge size="small" dot count={tipsCount}>
          <SvgIcon
            name="navNotice"
            w={respDims(22)}
            h={respDims(22)}
            color={isIconSelected ? '#3366ff' : '#606266'}
          />
        </Badge>
      </Center>
    </Popover>
  );
};

export default Notice;
