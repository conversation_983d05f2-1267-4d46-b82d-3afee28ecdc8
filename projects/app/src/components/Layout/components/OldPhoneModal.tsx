import React, { useEffect, useState } from 'react';
import {
  ModalBody,
  Box,
  Flex,
  Input,
  ModalFooter,
  Button,
  FormControl,
  FormErrorMessage,
  Center,
  useDisclosure
} from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { getClientUserSmsCode, getClientUserValid, getUpdateUserPhoneNum } from '@/api/user';
import { Toast } from '@/utils/ui/toast';
import { respDims } from '@/utils/chakra';
import ReplacePhoneModal from './ReplacePhoneModal';
import { useUserStore } from '@/store/useUserStore';
import SliderCaptchaModal from './SliderCaptchaModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';

type FormType = {
  phone: string;
  code: string;
};

const OldPhoneModal = ({ onClose }: { onClose: () => void }) => {
  const { userInfo } = useUserStore();
  const [countdown, setCountdown] = useState(0);
  const [isCodeSent, setIsCodeSent] = useState(false);
  const { openOverlay } = useOverlayManager();
  const [requesting, setRequesting] = useState(false);

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    formState: { errors }
  } = useForm<FormType>({
    defaultValues: {
      phone: '',
      code: ''
    }
  });

  const {
    isOpen: isOpenReplacePhone,
    onClose: onCloseReplacePhone,
    onOpen: onOpenReplacePhone
  } = useDisclosure();

  const onSendCode = async () => {
    const isPhoneValid = await trigger('phone');
    if (!isPhoneValid) {
      return;
    }
    const phoneNumber = getValues('phone');

    setRequesting(true);

    // 打开滑动验证码弹窗
    openOverlay({
      Overlay: SliderCaptchaModal,
      props: {
        mobile: phoneNumber,
        onClose: () => {
          setRequesting(false);
        },
        requesting,
        setRequesting,
        startCountdown: () => {
          setIsCodeSent(true);
          setCountdown(60); // 设置倒计时为 60 秒
        }
      }
    });
  };

  const { mutate: onSubmit, isLoading } = useRequest({
    mutationFn: (data: FormType) => {
      return getClientUserValid(6, data.phone, Number(data.code));
    },
    onSuccess: async () => {
      Toast.success('操作成功');
      onOpenReplacePhone();
    }
  });

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    } else if (countdown === 0 && isCodeSent) {
      setIsCodeSent(false);
    }
    return () => clearInterval(timer!);
  }, [countdown, isCodeSent]);

  return (
    <MyModal closeOnOverlayClick={false} isOpen onClose={onClose} title="验证手机号">
      <ModalBody>
        <FormControl isInvalid={!!errors.phone}>
          <Flex flexDir="column" position="relative">
            <Center>
              <Box
                flex={'0 0 40px'}
                fontSize={respDims(14, 12)}
                color="#1D2129"
                fontWeight="400"
                position="absolute"
                left="16px"
                top="11px"
                zIndex="999"
              >
                +86
              </Box>
              <Input
                placeholder="请输旧手机号"
                flex={1}
                pl="52px"
                _placeholder={{
                  color: '#86909C'
                }}
                type={'text'}
                disabled={isCodeSent}
                {...register('phone', {
                  required: '请输旧手机号',
                  pattern: {
                    value: /^1[3-9]\d{9}$/,
                    message: '请输入正确的手机号'
                  },
                  validate: {
                    checkOldPhone: (value) => {
                      if (value !== userInfo?.phone) {
                        return '当前手机号与登录手机号不一致';
                      }
                    }
                  }
                })}
              />
            </Center>
            <Box ml="40px">
              <FormErrorMessage>{errors.phone && errors.phone.message}</FormErrorMessage>
            </Box>
          </Flex>
        </FormControl>

        <FormControl isInvalid={!!errors.code}>
          <Flex flexDir="column" mt={5} pos="relative">
            <Center>
              <Input
                flex={1}
                type={'text'}
                placeholder="请输入验证码"
                _placeholder={{
                  color: '#86909C'
                }}
                {...register('code', {
                  required: '请输入验证码',
                  maxLength: {
                    value: 20,
                    message: '密码最少 4 位最多 20 位'
                  }
                })}
              ></Input>
              <Button
                pos="absolute"
                zIndex="999"
                variant="link"
                right="16px"
                color="#0052D9"
                onClick={() => {
                  countdown <= 0 && onSendCode();
                }}
                disabled={countdown > 0}
              >
                {countdown > 0 ? `${countdown}s` : '发送验证码'}
              </Button>
            </Center>
            <Box>
              <FormErrorMessage>{errors.code && errors.code.message}</FormErrorMessage>
            </Box>
          </Flex>
        </FormControl>
      </ModalBody>
      <ModalFooter>
        <Button mr={3} variant={'grayBase'} onClick={onClose}>
          取消
        </Button>
        <Button isLoading={isLoading} onClick={handleSubmit((data) => onSubmit(data))}>
          确认
        </Button>
      </ModalFooter>

      {isOpenReplacePhone && <ReplacePhoneModal onClose={onCloseReplacePhone} />}
    </MyModal>
  );
};

export default OldPhoneModal;
