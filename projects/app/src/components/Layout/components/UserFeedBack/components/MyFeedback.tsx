import { Controller, useForm } from 'react-hook-form';
import { Box, Button, ButtonGroup, Textarea } from '@chakra-ui/react';
import { Image, Skeleton } from 'antd';
import { useCallback, useState, useEffect } from 'react';
import { useSelectFile } from '@/hooks/useSelectFile';
import { Toast } from '@/utils/ui/toast';
import { uploadImage } from '@/utils/file';
import { t } from 'i18next';
import { getErrText } from '@/utils/string';
import SvgIcon from '@/components/SvgIcon';
import { nanoid } from 'nanoid';
import logo from '@/../public/imgs/common/hua_yun_tian_tu_logo.png';
import { dictSublist, feedbackDetail, feedbackSubmit, feedbackUpdate } from '@/api/feedback';

// 首先定义表单数据类型
interface FeedbackForm {
  /** 反馈类型 */
  dictId: string;
  /** 反馈内容 */
  feedbackContent: string;
}

interface MyFeedbackProps {
  editFeedbackId: string | null;
  gotoFeedBackRecord: () => void;
}

function MyFeedback({ editFeedbackId, gotoFeedBackRecord }: MyFeedbackProps) {
  const {
    control,
    formState: { errors },
    handleSubmit,
    setValue,
    getValues,
    watch,
    register,
    reset
  } = useForm<FeedbackForm>({
    mode: 'onChange',
    defaultValues: {
      dictId: '',
      feedbackContent: ''
    }
  });

  const [isContentFocus, setIsContentFocus] = useState<boolean>(false);
  // 图片列表
  const [imageList, setImageList] = useState<{ id: string; url: string; fileKey: string }[]>([]);
  useEffect(() => {
    console.log('imageList', imageList);
  }, [imageList]);
  // 图片上传 loading
  const [isUploading, setIsUploading] = useState<boolean>(false);
  // 反馈类型
  const [feedbackType, setFeedbackType] = useState<{ key: string; name: string }[]>([
    { key: '42', name: '功能问题' },
    { key: '43', name: '产品建议' },
    { key: '44', name: '问答效果' },
    { key: '45', name: '其他' }
  ]);

  function fetchDictSublist() {
    dictSublist({ code: 'feedback_type' }).then((res) => {
      console.log('feedback_type', res);
      setFeedbackType(res.map((item) => ({ key: item.id, name: item.dictValue })));
    });
  }

  useEffect(() => {
    fetchDictSublist();
  }, []);

  // 添加提交处理函数
  const onSubmit = (data: FeedbackForm) => {
    // 组装提交参数
    const submitData = {
      dictId: data.dictId,
      feedbackContent: data.feedbackContent,
      fileKeys: imageList.length > 0 ? imageList.map((item) => item.fileKey) : undefined
    };
    if (editFeedbackId) {
      //编辑
      console.log('编辑');
      feedbackUpdate({
        ...submitData,
        id: editFeedbackId
      })
        .then((res) => {
          Toast.success({
            title: '修改成功'
          });
        })
        .catch((err: any) => {
          Toast.warning({
            title: '提交失败'
          });
        })
        .finally(() => {
          handleReset();
          gotoFeedBackRecord();
        });
    } else {
      // 新增
      feedbackSubmit(submitData)
        .then((res) => {
          Toast.success({
            title: '提交成功'
          });
        })
        .catch((err: any) => {
          Toast.warning({
            title: '提交失败'
          });
        })
        .finally(() => {
          handleReset();
          gotoFeedBackRecord();
        });
    }
  };

  async function fetchFeedbackDetail(editFeedbackId: string) {
    const res = await feedbackDetail({ id: editFeedbackId });
    console.log('回填数据，反馈ID:', res);
    setValue('dictId', res.dictId);
    setValue('feedbackContent', res.feedbackContent);
    setImageList(
      res.feedbackFiles?.map((item, index) => ({
        id: item.id,
        url: item.fileUrl,
        fileKey: item.fileKey
      })) || []
    );
  }

  // 监听editFeedbackId变化，实现数据回填
  useEffect(() => {
    if (editFeedbackId) {
      console.log('回填数据，反馈ID:', editFeedbackId);
      fetchFeedbackDetail(editFeedbackId);
    }
  }, [editFeedbackId, setValue]);

  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false,
    maxSingleSize: 1024 * 1024 * 5,
    oversizedFilesToastTitle: '仅支持上传大小不超过5MB的jpg、jpeg、png格式文件'
  });

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;

      // 检查文件类型
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!validTypes.includes(file.type)) {
        Toast.warning({
          title: '仅支持上传jpg、jpeg、png格式文件'
        });
        return;
      }
      // 图片上传限制
      if (imageList.length >= 3) {
        console.log('图片上传限制', imageList);
        Toast.warning({
          title: '仅支持上传3张图片'
        });
        return;
      }

      setIsUploading(true);
      try {
        const data = await uploadImage(file, {
          isPublic: true
        });
        setImageList((prev) => [
          ...prev,
          {
            id: nanoid(),
            url: data.fileUrl,
            fileKey: data.fileKey
          }
        ]);
      } catch (err: any) {
        Toast.warning({
          title: getErrText(err, t('common.error.Select avatar failed'))
        });
      } finally {
        setIsUploading(false);
      }
    },
    [setValue, t, imageList]
  );

  function handleCloseImage(id: string) {
    setImageList((prev) => prev.filter((item) => item.id !== id));
  }

  function handleReset() {
    reset();
    setImageList([]);
  }

  return (
    <>
      <style global jsx>{`
        .ant-image-preview-switch-left,
        .ant-image-preview-switch-right,
        .ant-image-preview-close {
          background-color: #fff;
          color: #000;
          &:hover {
            background-color: #fff;
            color: #000;
            opacity: 0.5;
          }
        }
      `}</style>
      <Box height={'642px'} padding={'20px 17px 0 17px'}>
        <Box>
          <Image height={26} src={logo.src} alt="" preview={false} />
        </Box>
        <Box marginTop={'34px'} marginLeft={'19px'}>
          <Box color={'#303133'} fontSize={'26px'} fontWeight={'600'}>
            感谢您的反馈！
          </Box>
          <Box color={'#6a6a6a'} fontSize={'16px'}>
            您的反馈是我们一直前进的动力 😉
          </Box>
        </Box>

        <Box marginTop={'34px'} position={'relative'}>
          <Box fontSize={'15px'} fontWeight={600} color={'#000'} marginBottom={'12px'}>
            反馈类型
          </Box>
          <Controller
            name="dictId"
            control={control}
            rules={{ required: '请选择反馈类型' }}
            render={({ field: { onChange, value } }) => (
              <>
                {feedbackType.map((type, index) => (
                  <Button
                    w={'88px'}
                    height={'32px'}
                    key={type?.key}
                    variant="outline"
                    bg={value === type?.key ? '#F2F3FF' : '#F3F3F3'}
                    color={value === type?.key ? '#7D4DFF' : '#000'}
                    borderColor={value === type?.key ? '#6B48FF' : 'gray.200'}
                    _hover={{}}
                    borderRadius={'100px'}
                    onClick={() => onChange(type?.key)}
                    marginLeft={index === 0 ? '0' : '12px'}
                  >
                    {type?.name}
                  </Button>
                ))}
              </>
            )}
          />
          {errors.dictId && (
            <Box
              color="red.500"
              fontSize={'12px'}
              mt={2}
              position={'absolute'}
              bottom={'-18px'}
              left={'0'}
            >
              {errors.dictId.message}
            </Box>
          )}
        </Box>

        <Box marginTop={'14px'} position={'relative'}>
          <Box fontSize={'15px'} fontWeight={600} color={'#000'} marginBottom={'10px'}>
            详细描述
          </Box>
          <Box
            position={'relative'}
            w={'388px'}
            h={'135px'}
            borderRadius={'8px'}
            overflow={'hidden'}
            border={isContentFocus ? '1px solid #7D4DFF' : '1px solid #E5E7EB'}
            background={isContentFocus ? 'var(--chakra-colors-white)' : '#f6f6f6'}
            boxShadow={isContentFocus ? '0px 0px 0px 2.4px rgba(51, 112, 255, 0.15)' : 'none'}
            onFocus={() => {
              setIsContentFocus(true);
            }}
            onBlur={() => {
              setIsContentFocus(false);
            }}
          >
            <Controller
              name="feedbackContent"
              control={control}
              defaultValue=""
              rules={{ required: '请输入您的意见和建议' }}
              render={({ field: { onChange, value } }) => (
                <Textarea
                  h={'108px'} //135-108
                  placeholder="请输入您的意见和建议"
                  rows={4}
                  resize={'none'}
                  border={'none'}
                  borderRadius={'8px'}
                  maxLength={500}
                  _placeholder={{
                    color: '#606266',
                    fontSize: '16px'
                  }}
                  _focus={{
                    borderColor: 'transparent',
                    boxShadow: 'none',
                    background: 'transparent'
                  }}
                  value={value}
                  {...register('feedbackContent', {
                    required: '请输入您的反馈内容'
                  })}
                  onChange={onChange}
                />
              )}
            />
            <Box
              position={'absolute'}
              right={'10px'}
              bottom={'5px'}
              fontSize={'14px'}
              color={'#4E5969'}
              marginTop={'4px'}
            >
              {watch('feedbackContent').length} / 500
            </Box>
          </Box>
          {errors.feedbackContent && (
            <Box
              color="red.500"
              fontSize={'12px'}
              mt={2}
              position={'absolute'}
              bottom={'-18px'}
              left={'0'}
            >
              {errors.feedbackContent.message}
            </Box>
          )}
        </Box>

        <Box marginTop={'14px'}>
          <Box
            display={'flex'}
            alignItems={'center'}
            fontSize={'15px'}
            fontWeight={600}
            color={'#000'}
            marginBottom={'10px'}
            height={'22px'}
          >
            <Box height={'100%'}>上传图片</Box>
            <Box
              height={'100%'}
              lineHeight={'23px'}
              fontSize={'12px'}
              color={'#606266'}
              marginLeft={'14px'}
            >
              支持上传3张图片，单张图片大小不超5M
            </Box>
          </Box>
          <Box display={'flex'}>
            <Box>
              <Button
                display={'flex'}
                justifyContent={'center'}
                alignItems={'center'}
                w={'89px'}
                h={'80px'}
                padding={'27px 0'}
                borderRadius={'8px'}
                bg={'#f9f9f9'}
                onClick={onOpenSelectFile}
                isLoading={isUploading}
                _hover={{
                  border: '1px solid #9067fa'
                }}
              >
                <SvgIcon name="add" w={'25px'} h={'25px'} />
              </Button>
            </Box>

            <Image.PreviewGroup
              preview={{
                scaleStep: 0.5,
                onVisibleChange(value, prevValue) {
                  const isOpen = value;
                  const element: HTMLElement | null =
                    document.querySelector('.user-feedback-modal');
                  if (element) {
                    (element as HTMLElement).style.display = isOpen ? 'none' : 'block';
                  }
                }
              }}
            >
              {imageList.map((item, index) => (
                <Box
                  key={item.id}
                  position={'relative'}
                  display={'flex'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  w={'89px'}
                  h={'80px'}
                  borderRadius={'8px'}
                  bg={'#f9f9f9'}
                  overflow={'hidden'}
                  marginLeft={'10px'}
                  _hover={{
                    '.close-box': {
                      display: 'block'
                    }
                  }}
                >
                  <Image
                    src={item.url}
                    alt=""
                    width={'100%'}
                    height={'100%'}
                    style={{
                      objectFit: 'cover'
                    }}
                  />
                  <Box
                    className="close-box"
                    display={'none'}
                    position={'absolute'}
                    top={'0'}
                    right={'0'}
                    w={'15.4px'}
                    h={'15.4px'}
                    borderRadius={'0px 0px 0px 5.766px'}
                    bg={'rgba(0, 0, 0, 0.4)'}
                    cursor={'pointer'}
                    onClick={() => handleCloseImage(item.id)}
                  >
                    <SvgIcon name="close" w={'15px'} h={'15px'} color={'#fff'} />
                  </Box>
                </Box>
              ))}
            </Image.PreviewGroup>
          </Box>
        </Box>

        <Box marginTop={'26px'} textAlign={'right'}>
          <Button
            background={'#F2F3F5'}
            color={'#4E5969'}
            onClick={handleReset}
            marginRight={'16px'}
            _hover={{
              opacity: 0.8
            }}
          >
            清空
          </Button>
          <Button onClick={handleSubmit(onSubmit)}>提交</Button>
        </Box>

        <File onSelect={onSelectFile} />
      </Box>
    </>
  );
}
export default MyFeedback;
