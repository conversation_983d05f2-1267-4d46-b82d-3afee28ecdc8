import SvgIcon from '@/components/SvgIcon';
import { Box } from '@chakra-ui/react';
import { ConfigProvider, Tabs, TabsProps } from 'antd';
import { useState, useEffect } from 'react';
import MyFeedback from './MyFeedback';
import FeedBackRecord from './FeedBackRecord';

export type ActiveKey = 'myFeedback' | 'feedBackRecord';
export interface FeedBackCenterProps {
  activeKey?: ActiveKey | undefined;
  onHandleDetail?: (id: string) => void;
}
function FeedBackCenter(props: FeedBackCenterProps) {
  const { onHandleDetail } = props;
  const [activeKey, setActiveKey] = useState(props.activeKey ?? 'myFeedback');
  const [editFeedbackId, setEditFeedbackId] = useState<string | null>(null);

  const onChange = (key: ActiveKey) => {
    setEditFeedbackId(null);
    setActiveKey(key);
  };

  // 接收编辑反馈ID并切换到我的反馈页面
  const handleEditFeedback = (id: string) => {
    setEditFeedbackId(id);
    setActiveKey('myFeedback');
  };

  function gotoFeedBackRecord() {
    setActiveKey('feedBackRecord');
  }

  const items: TabsProps['items'] = [
    {
      key: 'myFeedback',
      label: (
        <>
          <Box display={'flex'} alignItems={'center'} flexDirection={'column'}>
            {activeKey === 'myFeedback' ? (
              <SvgIcon name="myFeedBackActive" w={'22px'} h={'22px'} />
            ) : (
              <SvgIcon name="myFeedBack" w={'22px'} h={'22px'} />
            )}
            <span>我的反馈</span>
          </Box>
        </>
      ),
      children: (
        <MyFeedback editFeedbackId={editFeedbackId} gotoFeedBackRecord={gotoFeedBackRecord} />
      )
    },
    {
      key: 'feedBackRecord',
      label: (
        <>
          <Box display={'flex'} alignItems={'center'} flexDirection={'column'}>
            {activeKey === 'feedBackRecord' ? (
              <SvgIcon name="feedBackRecordActive" w={'22px'} h={'22px'} />
            ) : (
              <SvgIcon name="feedBackRecord" w={'22px'} h={'22px'} />
            )}
            <span>反馈记录</span>
          </Box>
        </>
      ),
      children: (
        <FeedBackRecord onEditFeedback={handleEditFeedback} onHandleDetail={onHandleDetail} />
      )
    }
  ];

  return (
    <>
      <ConfigProvider
        theme={{
          components: {
            Tabs: {
              itemHoverColor: '#000',
              itemSelectedColor: '#000',
              inkBarColor: 'transparent',
              verticalItemMargin: '0'
            }
          }
        }}
      >
        <style jsx global>{`
          .feedback-tabs {
            .ant-tabs-nav {
              margin-top: 0;
              height: 68px;
              .ant-tabs-tab {
                padding: 0;
              }
            }

            .ant-tabs-nav-list {
              width: 100%;
              .ant-tabs-tab {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                margin: 0;
              }
              .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                color: #000 !important;
              }
              .ant-tabs-tab-btn:hover {
                color: #000 !important;
              }
            }
          }
        `}</style>
        <Tabs
          activeKey={activeKey}
          // activeKey={''}
          className="feedback-tabs"
          destroyInactiveTabPane
          defaultActiveKey="myFeedback"
          tabPosition="bottom"
          items={items}
          onChange={(key) => onChange(key as ActiveKey)}
        />
      </ConfigProvider>
    </>
  );
}

export default FeedBackCenter;
