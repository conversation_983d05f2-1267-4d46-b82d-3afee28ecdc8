import SvgIcon from '@/components/SvgIcon';

import { Box } from '@chakra-ui/react';
interface NotFeedBackProps {
  text?: string;
}
function NotFeedBack(props: NotFeedBackProps) {
  const { text = '暂无反馈' } = props;
  return (
    <Box
      className="not-data"
      display={'flex'}
      flexDirection={'column'}
      alignItems={'center'}
      justifyContent={'center'}
      w={'100%'}
      h={'100%'}
      transform={'translateY(-14px)'}
    >
      <SvgIcon name="notFeedBack" w={'77px'} h={'80px'} />
      <Box
        marginTop={'14px'}
        color={'#626262'}
        fontSize={'14px'}
        fontWeight={400}
        textAlign={'center'}
      >
        {text}
      </Box>
    </Box>
  );
}
export default NotFeedBack;
