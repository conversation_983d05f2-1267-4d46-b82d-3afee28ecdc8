import { deleteFeedback, dictSublist, feedbackPage } from '@/api/feedback';
import SvgIcon from '@/components/SvgIcon';
import { Box } from '@chakra-ui/react';
import styled from '@emotion/styled';
import { List, Popconfirm, Skeleton } from 'antd';
import { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import NotFeedBack from './NotFeedBack';
import { FeedbackPageResponse } from '@/types/api/feedback';
import dayjs from 'dayjs';
import { Toast } from '@/utils/ui/toast';

const Pending = styled.div`
  width: 58px;
  height: 24px;
  border-radius: 8px;
  background-color: #fff7e8;
  color: #ff7d00;
  font-size: 14px;
  text-align: center;
`;

const Solved = styled.div`
  width: 58px;
  height: 24px;
  border-radius: 8px;
  background-color: #e8ffea;
  color: #00b42a;
  font-size: 14px;
  text-align: center;
`;

// 自定义滚动条样式
const ScrollContainer = styled.div`
  width: 394px;
  height: 580px;
  overflow-y: auto;
  overflow-x: hidden;
`;

interface FeedBackRecordProps {
  onEditFeedback: (id: string) => void;
  onHandleDetail?: (id: string) => void;
}

function FeedBackRecord({ onEditFeedback, onHandleDetail }: FeedBackRecordProps) {
  const current = useRef(1);
  const total = useRef(0);
  const [feedbackList, setFeedbackList] = useState<FeedbackPageResponse['records']>([]);

  function handleEditFeedback(id: string) {
    // 调用父组件传递的方法，跳转到我的反馈回填数据
    onEditFeedback(id);
  }

  function handleDeleteFeedback(id: string) {
    deleteFeedback({ id })
      .then((res) => {
        if (res) {
          Toast.success({
            title: '删除成功'
          });
        }
      })
      .catch((err: any) => {
        Toast.error({
          title: '删除失败'
        });
      })
      .finally(() => {
        fetchReloadingFeedBackPage();
      });
  }

  function fetchReloadingFeedBackPage() {
    feedbackPage({
      current: 1,
      pageSize: 10
    }).then((res) => {
      setFeedbackList(res.records);
      current.current = 1;
    });
  }

  function fetchFeedbackPage(current: number) {
    feedbackPage({
      current,
      pageSize: 10,
      feedbackContent: '',
      dictId: ''
    }).then((res) => {
      console.log(res);
      setFeedbackList([...feedbackList, ...res.records]);
      total.current = res.total;
    });
  }

  useEffect(() => {
    fetchFeedbackPage(current.current);
  }, []);

  return (
    <>
      <Box height={'642px'} padding={'22.5px 17px 0 17px'}>
        <Box color={'#303133'} fontSize={'18px'} fontWeight={600}>
          记录列表
        </Box>
        <Box
          id="feedback_record_list"
          className="record-list"
          width={'400px'}
          height={'580px'}
          marginTop={'14.5px'}
          overflowY={'auto'}
          overflowX={'hidden'}
        >
          {feedbackList.length === 0 ? (
            <NotFeedBack></NotFeedBack>
          ) : (
            <>
              <InfiniteScroll
                dataLength={feedbackList.length}
                next={() => {
                  current.current++;
                  fetchFeedbackPage(current.current);
                }}
                hasMore={feedbackList.length < total.current}
                loader={<>{feedbackList.length > 5 ? <Skeleton /> : null}</>}
                endMessage={
                  <Box padding={'10px 0'} color={'#626262'} fontSize={'14px'} textAlign={'center'}>
                    没有更多数据了
                  </Box>
                }
                scrollableTarget="feedback_record_list"
              >
                <List
                  dataSource={feedbackList}
                  renderItem={(item, index) => {
                    return (
                      <>
                        <>
                          <Box
                            className="record-item"
                            w={'388px'}
                            h={'122px'}
                            padding={'14px'}
                            borderRadius={'12px'}
                            border={'1px solid #E5E7EB'}
                            background={'#FFF'}
                            boxShadow={'0px 2px 7.7px 0px rgba(179, 179, 179, 0.22)'}
                            marginTop={index === 0 ? '0' : '14px'}
                          >
                            <Box
                              className="record-item-introduce"
                              overflow={'hidden'}
                              textOverflow={'ellipsis'}
                              color={'#1D2129'}
                              fontSize={'14px'}
                              fontWeight={500}
                              display={'-webkit-box'}
                              style={{
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical'
                              }}
                              cursor={'pointer'}
                              onClick={() => onHandleDetail?.(item.id)}
                              height={'44px'}
                            >
                              {item.feedbackContent}
                            </Box>
                            <Box
                              className="record-item-status"
                              display={'flex'}
                              fontSize={'13px'}
                              color={'#626262'}
                              alignItems={'center'}
                              marginTop={'20px'}
                            >
                              <Box className="record-item-status-text">
                                {item.status === 0 ? (
                                  <Pending>待处理</Pending>
                                ) : (
                                  <Solved>已解决</Solved>
                                )}
                              </Box>
                              <Box marginLeft={'14px'} w={'52px'} textAlign={'center'}>
                                {item.feedbackType}
                              </Box>
                              <Box margin={'0 8px'}>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="2"
                                  height="16"
                                  viewBox="0 0 2 16"
                                  fill="none"
                                >
                                  <path d="M1 1V15" stroke="#BABABA" stroke-linecap="round" />
                                </svg>
                              </Box>
                              <Box className="record-item-status-time">
                                {dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}
                              </Box>
                              <Box className="record-item-status-button" display={'flex'}>
                                <SvgIcon
                                  name="editFeedback"
                                  cursor={'pointer'}
                                  w={'24px'}
                                  h={'24px'}
                                  margin={'0 16px 0 37px'}
                                  visibility={item.status === 1 ? 'hidden' : 'visible'}
                                  onClick={
                                    item.status === 1
                                      ? undefined
                                      : () => handleEditFeedback(item.id)
                                  }
                                />
                                <Popconfirm
                                  title="确定删除吗？"
                                  onConfirm={() => handleDeleteFeedback(item.id)}
                                >
                                  <SvgIcon
                                    name="deleteFeedback"
                                    cursor={'pointer'}
                                    w={'24px'}
                                    h={'24px'}
                                  />
                                </Popconfirm>
                              </Box>
                            </Box>
                          </Box>
                        </>
                      </>
                    );
                  }}
                ></List>
              </InfiniteScroll>
            </>
          )}
        </Box>
      </Box>
    </>
  );
}
export default FeedBackRecord;
