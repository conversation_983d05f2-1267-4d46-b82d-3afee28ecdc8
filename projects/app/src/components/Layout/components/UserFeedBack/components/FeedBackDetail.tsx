import SvgIcon from '@/components/SvgIcon';
import { Box, Button } from '@chakra-ui/react';
import styled from '@emotion/styled';
import { Image } from 'antd';
import NotFeedBack from './NotFeedBack';
import { useEffect, useState } from 'react';
import { feedbackDetail } from '@/api/feedback';
import { FeedbackDetailResponse } from '@/types/api/feedback';
import dayjs from 'dayjs';

const LabelTitle = styled.div`
  color: #000;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
`;

const LabelContent = styled.div`
  color: #000;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  width: 308px;
  white-space: pre-wrap;
`;

interface FeedBackDetailProps {
  onHandleGoBack?: () => void;
  detailId?: string;
}

function FeedBackDetail({ onHandleGoBack, detailId }: FeedBackDetailProps) {
  const [detalData, setDetailData] = useState<FeedbackDetailResponse>();

  function handleVisibleChange(visible: boolean) {
    const feedbackReplyContainer = document.querySelector('.user-feedback-modal');
    if (feedbackReplyContainer) {
      (feedbackReplyContainer as HTMLElement).style.display = visible ? 'none' : 'block';
    }
  }
  function fetchFeedbackDetail() {
    if (detailId) {
      feedbackDetail({ id: detailId }).then((res) => {
        console.log('反馈详情', res);
        setDetailData(res);
      });
    }
  }

  useEffect(() => {
    if (detailId) {
      console.log('detailId', detailId);
      fetchFeedbackDetail();
    }
  }, [detailId]);

  return (
    <>
      <style global jsx>{`
        .ant-image-preview-switch-left,
        .ant-image-preview-switch-right,
        .ant-image-preview-close {
          background-color: #fff;
          color: #000;
          &:hover {
            background-color: #fff;
            color: #000;
            opacity: 0.5;
          }
        }
      `}</style>
      <Box padding={'20px 17px 0 17px'}>
        <Box display={'flex'} alignItems={'center'} color={'#303133'} fontSize={'18px'}>
          <Button
            w={'32px'}
            h={'32px'}
            minW={'32px'}
            background={'#eeeeee'}
            onClick={onHandleGoBack}
            padding={'0'}
            _hover={{ opacity: 0.8 }}
          >
            <SvgIcon name="taskLeft" w={'16px'} h={'16px'} color={'#4E5969'} />
          </Button>
          <Box marginLeft={'11px'} fontWeight={'600'}>
            记录列表/反馈详情
          </Box>
        </Box>

        <Box
          className="feedback-detail-container"
          height={'638px'}
          overflowY={'auto'}
          overflowX={'hidden'}
        >
          <Box className="feedback-info-container">
            <Box display={'flex'} alignItems={'center'} marginTop={'20px'}>
              <Box w={'5px'} height={'18px'} borderRadius={'50px'} bgColor={'#7f4dff'}></Box>
              <Box fontWeight={500} color={'#1D2129'} fontSize={'16px'} marginLeft={'8px'}>
                反馈信息
              </Box>
            </Box>

            <Box display={'flex'} margin={'16px 0'}>
              <LabelTitle>反馈类型：</LabelTitle>
              <LabelContent>{detalData?.feedbackType}</LabelContent>
            </Box>

            <Box display={'flex'} marginBottom={'10px'}>
              <LabelTitle>反馈内容：</LabelTitle>
              <LabelContent>{detalData?.feedbackContent}</LabelContent>
            </Box>

            <Box display={'flex'}>
              <Image.PreviewGroup
                preview={{
                  onChange: (current, prev) =>
                    console.log(`current index: ${current}, prev index: ${prev}`),
                  onVisibleChange: (visible) => handleVisibleChange(visible)
                }}
              >
                {detalData?.feedbackFiles?.map((item, index) => (
                  <Box
                    key={item.id}
                    w={'122px'}
                    h={'84px'}
                    borderRadius={'12px'}
                    overflow={'hidden'}
                    margin={index === 1 ? '0 10px' : undefined}
                  >
                    <Image
                      width={'100%'}
                      height={'100%'}
                      src={item.fileUrl}
                      style={{ objectFit: 'cover' }}
                    />
                  </Box>
                ))}
              </Image.PreviewGroup>
            </Box>

            <Box display={'flex'} marginTop={'16px'}>
              <LabelTitle>反馈时间：</LabelTitle>
              <LabelContent>
                {' '}
                {dayjs(detalData?.feedbackTime).format('YYYY-MM-DD HH:mm')}
              </LabelContent>
            </Box>

            <Box margin={'20px 0 23px 0'}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="388"
                height="2"
                viewBox="0 0 388 2"
                fill="none"
              >
                <path d="M0 1H388" stroke="#E0E0E0" />
              </svg>
            </Box>
          </Box>

          <Box className="feedback-reply-container" paddingBottom={'20px'}>
            <Box>
              <Box display={'flex'} alignItems={'center'} marginTop={'20px'}>
                <Box w={'5px'} height={'18px'} borderRadius={'50px'} bgColor={'#7f4dff'}></Box>
                <Box fontWeight={500} color={'#1D2129'} fontSize={'16px'} marginLeft={'8px'}>
                  处理回复
                </Box>
              </Box>
            </Box>

            {detalData?.replyContent ? (
              <>
                <Box display={'block'}>
                  <Box display={'flex'} marginTop={'16px'} marginBottom={'10px'}>
                    <LabelTitle>回复内容：</LabelTitle>
                    <LabelContent>{detalData?.replyContent}</LabelContent>
                  </Box>

                  <Box display={'flex'}>
                    <Image.PreviewGroup
                      preview={{
                        onChange: (current, prev) =>
                          console.log(`current index: ${current}, prev index: ${prev}`),
                        onVisibleChange: (visible) => handleVisibleChange(visible)
                      }}
                    >
                      {detalData?.replyFiles?.map((item, index) => (
                        <Box
                          key={item.id}
                          w={'122px'}
                          h={'84px'}
                          borderRadius={'12px'}
                          overflow={'hidden'}
                          margin={index === 1 ? '0 10px' : undefined}
                        >
                          <Image
                            width={'100%'}
                            height={'100%'}
                            src={item.fileUrl}
                            style={{ objectFit: 'cover' }}
                          />
                        </Box>
                      ))}
                    </Image.PreviewGroup>
                  </Box>

                  <Box display={'flex'} marginTop={'16px'}>
                    <LabelTitle>处理时间：</LabelTitle>
                    <LabelContent>
                      {dayjs(detalData?.replyTime).format('YYYY-MM-DD HH:mm')}
                    </LabelContent>
                  </Box>
                </Box>
              </>
            ) : (
              <>
                <Box marginTop={'75px'}>
                  <NotFeedBack text="暂无处理数据"></NotFeedBack>
                </Box>
              </>
            )}
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default FeedBackDetail;
