import LayoutOverlay from '@/components/LayoutOverlay';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import FeedBackCenter, { ActiveKey } from './components/FeedBackCenter';
import { useRef, useState } from 'react';
import { Modal } from 'antd';
import FeedBackDetail from './components/FeedBackDetail';

export interface UserFeedBackProps {
  onClose: () => void;
  isOpen: boolean;
}
function UserFeedBack(props: UserFeedBackProps) {
  const { onClose, isOpen } = props;

  // 创建变量控制显示 FeedBackCenter FeedBackDetail
  const [isFeedBackCenterVisible, setIsFeedBackCenterVisible] = useState(true);
  const [activeKey, setActiveKey] = useState<ActiveKey>();

  // 详情id
  const [detailId, setDetailId] = useState<string>();
  function onHandleDetailCenter(id: string) {
    console.log('点击了详情', { id });
    setIsFeedBackCenterVisible(false);
    setDetailId(id);
  }
  function onHandleFeedBackDeitalGoBack() {
    console.log('点击了返回');
    setIsFeedBackCenterVisible(true);
    setActiveKey('feedBackRecord');
  }

  return (
    <>
      <Modal
        open={isOpen}
        onCancel={onClose}
        footer={null}
        mask={false}
        maskClosable={false}
        width={422}
        style={{
          position: 'fixed',
          top: '18%',
          right: '26px',
          left: 'auto',
          height: '710px'
        }}
        wrapClassName="user-feedback-modal"
        destroyOnClose
      >
        <style jsx global>{`
          .user-feedback-modal.ant-modal-wrap {
            width: 422px !important;
            height: 0 !important;
            overflow: visible !important;
            pointer-events: none;
          }
          .user-feedback-modal .ant-modal {
            pointer-events: auto;
          }
          /* 入场动画 */
          .user-feedback-modal .ant-modal.ant-zoom-enter,
          .user-feedback-modal .ant-modal.ant-zoom-appear {
            transform: translate(100%, 100%);
            opacity: 0;
          }
          .user-feedback-modal .ant-modal.ant-zoom-enter-active,
          .user-feedback-modal .ant-modal.ant-zoom-appear-active {
            transform: translate(0, 0);
            opacity: 1;
            transition:
              transform 300ms cubic-bezier(0, 1.2, 1, 1),
              opacity 83ms ease-out;
          }
          /* 出场动画 */
          .user-feedback-modal .ant-modal.ant-zoom-leave,
          .user-feedback-modal .ant-modal.ant-zoom-leave-active {
            transform: translate(100%, 100%);
            opacity: 0;
            transition:
              transform 300ms cubic-bezier(0, 1.2, 1, 1),
              opacity 83ms ease-out;
          }
          .user-feedback-modal .ant-modal-content {
            height: 710px;
            border-radius: 20px;
            padding: 0;
            box-shadow:
              0px 4px 66.2px 0px rgba(14, 19, 31, 0.2),
              0px 0px 17.5px 0px rgba(7, 13, 27, 0.08);
            transform-origin: bottom right;
          }
          .user-feedback-modal .ant-modal-close {
            color: #000;
            top: 20px;
          }
          /* 移除默认动画 */
          .user-feedback-modal .ant-modal {
            animation: none !important;
          }
        `}</style>
        {isFeedBackCenterVisible ? (
          <FeedBackCenter activeKey={activeKey} onHandleDetail={onHandleDetailCenter} />
        ) : (
          <FeedBackDetail onHandleGoBack={onHandleFeedBackDeitalGoBack} detailId={detailId} />
        )}
      </Modal>
    </>
  );
}

export default UserFeedBack;
