import SvgIcon from '@/components/SvgIcon';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useRoutes } from '@/hooks/useRoutes';
import { traverseRoutes } from '@/routes';
import { useUserStore } from '@/store/useUserStore';
import { clearToken } from '@/utils/auth';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex, Image } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import UserInfo from '../UserInfo';
import { Toast } from '@/utils/ui/toast';
import MyMenu from '@/components/MyMenu';
import { ReactNode } from 'react';
import { DimsMinScale } from '../../Sidebar/constants';
import { useTenantStore } from '@/store/useTenantStore';
import UserFeedBack from '../UserFeedBack';

const UserMenu = ({ isCollapsed, ...props }: { isCollapsed?: boolean } & ChakraProps) => {
  const router = useRouter();

  const { industryAlias, redirectHomePath } = useTenantStore();

  const { userInfo, setUserInfo } = useUserStore();

  const { routeGroup, routeGroupMap } = useRoutes();

  const adminRouteGroup = routeGroupMap[RouteGroupTypeEnum.Admin];

  const { openOverlay, isOverlayOpen, closeOverlay } = useOverlayManager();

  const menus: { label: string; icon: ReactNode; onClick: () => void }[] =
    userInfo?.isSso === 1
      ? [
          {
            label: '退出登录',
            icon: <SvgIcon name="power" />,
            onClick: () => {
              clearToken().finally(() => {
                router.replace('/login').finally(() => {
                  setUserInfo(null);
                  Toast.success('退出成功');
                });
              });
            }
          }
        ]
      : [
          ...(isCollapsed && routeGroup?.type === RouteGroupTypeEnum.Admin
            ? [
                {
                  label: 'AI平台',
                  icon: <SvgIcon name="navHomeLine" />,
                  onClick: () => {
                    router.push('/home');
                  }
                }
              ]
            : []),
          ...(isCollapsed &&
          routeGroup?.type === RouteGroupTypeEnum.Chat &&
          adminRouteGroup.navRoutes.length
            ? [
                {
                  label: `${industryAlias}管理`,
                  icon: <SvgIcon name="navSchoolLine" />,
                  onClick: () => {
                    const path = traverseRoutes(adminRouteGroup?.navRoutes, (it) => it.path);
                    path && router.push(path);
                  }
                }
              ]
            : []),
          {
            label: '个人中心',
            icon: <SvgIcon name="user" />,
            onClick: () => {
              openOverlay({
                name: 'userInfo',
                Overlay: UserInfo,
                props: {}
              });
            }
          },
          {
            label: '我要反馈',
            icon: <SvgIcon name="useFeedBack" />,
            onClick: () => {
              openOverlay({
                name: 'UserFeedBack',
                Overlay: UserFeedBack,
                props: {
                  isOpen: isOverlayOpen('UserFeedBack')
                }
                // openProp: 'open'
              });
            }
          },
          {
            label: '用户协议',
            icon: <SvgIcon name="userAgreement" />,
            onClick: () => {
              window.open(
                'https://privacy.huayuntiantu.com/kit-privacy.html',
                '_blank',
                'noopener,noreferrer'
              );
            }
          },
          {
            label: '退出登录',
            icon: <SvgIcon name="power" />,
            onClick: () => {
              clearToken().finally(() => {
                router.replace('/login').finally(() => {
                  setUserInfo(null);
                  Toast.success('退出成功');
                });
              });
            }
          }
        ];

  return (
    <Flex align="center" justify="space-between" {...props}>
      <Box flex="1" overflow="hidden">
        <MyMenu
          menuList={menus}
          Button={
            <Flex align="center" display="flex" cursor="pointer">
              <Image
                w={respDims('32fpx')}
                h={respDims('32fpx')}
                borderRadius="50%"
                src={userInfo?.avatar || '/imgs/layout/avatar.svg'}
                alt=""
                objectFit="cover"
              />

              {!isCollapsed && (
                <Box
                  mx={respDims(10)}
                  maxWidth="7ch"
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  {userInfo?.username}
                </Box>
              )}
            </Flex>
          }
          menuListStyles={{
            borderRadius: '8px',
            padding: '10px'
          }}
        />
      </Box>

      {!isCollapsed &&
        (routeGroup?.type === RouteGroupTypeEnum.Admin ||
          (routeGroup?.type === RouteGroupTypeEnum.Chat && !!adminRouteGroup.navRoutes.length)) && (
          <Box
            ml={respDims(4, DimsMinScale)}
            px={respDims(12, DimsMinScale)}
            py={respDims(5, DimsMinScale)}
            color="#606266"
            bgColor="#F8FAFC"
            fontSize={respDims('13fpx')}
            lineHeight={respDims('22fpx')}
            borderRadius={respDims(8, DimsMinScale)}
            cursor="pointer"
            _hover={{
              bgColor: '#E2E8F0'
            }}
            onClick={() => {
              if (routeGroup?.type === RouteGroupTypeEnum.Admin) {
                router.push(redirectHomePath);
              } else {
                const path = traverseRoutes(adminRouteGroup?.navRoutes, (it) => it.path);
                path && router.push(path);
              }
            }}
          >
            {routeGroup?.type === RouteGroupTypeEnum.Admin ? 'AI平台' : `${industryAlias}管理`}
          </Box>
        )}
    </Flex>
  );
};

export default UserMenu;
