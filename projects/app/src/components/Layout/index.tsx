import React from 'react';
import { Box, Flex, Text } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useLoading } from '@/hooks/useLoading';
import { useSystemStore } from '@/store/useSystemStore';

import Content from './Content';

import Sidebar from './Sidebar';
import MobileNavbar from '../MobileNavbar';
import { respDims, rpxDim } from '@/utils/chakra';
import SvgIcon from '../SvgIcon';
import LayoutOfficialHome from '../LayoutOfficialHome/layout';
import GuangMingLayout from '../LayoutOfficialHome/guangming';
import DianHuaLayout from '../LayoutOfficialHome/dianhua';

type LayoutConfigType = {
  fullpage?: boolean;
  hideSidebar?: boolean;
  unauth?: boolean;

  // 移动端配置
  mobile?: {
    autoback?: boolean;
    sidebarButton?: boolean;
    title?: string;
  };
};

const layoutConfigs: Record<string, LayoutConfigType> = {
  '/': { fullpage: true, unauth: true },
  '/login': { fullpage: true, unauth: true },
  '/error': { fullpage: true, unauth: true },
  '/thirdlogin/qywx': { fullpage: true, unauth: true },
  '/thirdlogin/ding': { fullpage: true, unauth: true },
  '/thirdlogin/qywxauth': { fullpage: true, unauth: true },
  '/oauth': { fullpage: true, unauth: true },
  '/noPermission': { fullpage: true, unauth: true }
};

const Layout = ({ children }: { children: JSX.Element }) => {
  const router = useRouter();

  const { Loading } = useLoading();

  const { loading, isPc } = useSystemStore();

  const layoutConfig = layoutConfigs[router.pathname];

  if (isPc === undefined) {
    return <></>;
  }

  if (router.pathname.startsWith('/officialHome/huashi')) {
    return <LayoutOfficialHome>{children}</LayoutOfficialHome>;
  }

  if (router.pathname.startsWith('/officialHome/guangming')) {
    return <GuangMingLayout>{children}</GuangMingLayout>;
  }

  if (router.pathname.startsWith('/officialHome/dianhua')) {
    return <DianHuaLayout>{children}</DianHuaLayout>;
  }

  return (
    <>
      <Flex flexDir="column" h="100%" bgColor="#F8FAFC">
        {!isPc && layoutConfig?.mobile && (
          <>
            <MobileNavbar
              autoback={layoutConfig.mobile?.autoback}
              sidebarButton={layoutConfig.mobile?.sidebarButton}
              title={layoutConfig.mobile?.title}
            />
          </>
        )}

        <Flex flex="1" overflow="hidden">
          {!(layoutConfig?.fullpage || layoutConfig?.hideSidebar) && <Sidebar />}

          <Content flex="1" unauth={layoutConfig?.unauth === true}>
            {children}
          </Content>
        </Flex>
      </Flex>
      <Loading loading={loading} zIndex={999999} />
    </>
  );
};

export default Layout;
