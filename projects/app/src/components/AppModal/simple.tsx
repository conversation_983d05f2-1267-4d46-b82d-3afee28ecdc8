import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  ModalBody,
  Input,
  Textarea,
  FormErrorMessage,
  FormControl,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Spinner,
  Image,
  Icon,
  Tooltip
} from '@chakra-ui/react';
import { useSelectFile } from '@/hooks/useSelectFile';
import { useForm } from 'react-hook-form';
import { uploadImage } from '@/utils/file';
import { getErrText } from '@/utils/string';
import { Toast } from '@/utils/ui/toast';
import { appCenterCopy, createApp, updateClientApp } from '@/api/app';
import { useSystemStore } from '@/store/useSystemStore';
import { useRequest } from '@/hooks/useRequest';
import Avatar from '@/components/Avatar';
import MyTooltip from '@/components/MyTooltip';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { APP_ICON } from '@/constants/common';
import { AppModalDataType, CreateAppParams } from '@/types/api/app';
import { NodeInputKeyEnum } from '@/fastgpt/global/core/workflow/constants';
import { defaultAppTemplates, simpleTemplate } from '@/fastgpt/web/core/app/templates';
import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { ModeTypeEnum } from '@/constants/api/app';
import { useRouter } from 'next/router';
import { getSampleAppInfo } from '@/utils/app';
import { respDims } from '@/utils/chakra';
import { Box } from '@chakra-ui/react';
import SvgIcon from '../SvgIcon';
import { streamFetch } from '@/utils/fetch';
import { nanoid } from 'nanoid';
import Lottie, { LottieNameType } from '../Lottie';
import dayjs from 'dayjs';
import { useUnmount } from 'ahooks';
import {
  AvatarCreatedLoading,
  GradientText,
  TextareaCount,
  VerticalLine,
  AnimationStyles,
  TextJump
} from './style';
import { checkJsonFields, getJsonFromText } from '@/utils/chat';
import { aicreateChatTemplate } from '@/utils/chatTemplate';

type FormType = {
  avatarUrl: string;
  name: string;
  templateId: string;
  intro?: string;
  /** 值为1就是AI创建，为0就是标准创建  */
  isAICreated?: number;
};

interface AppModalProps {
  onClose: () => void;
  onSuccess: (data: AppModalDataType & { appId: string }) => void;
  isCopy?: boolean;
  appModalParams?: AppModalDataType;
}

const AppModal = ({ onClose, onSuccess, isCopy, appModalParams }: AppModalProps) => {
  const isAdd = !appModalParams?.id; // true 为新增，false 为编辑
  const { t } = useTranslation();
  const [refresh, setRefresh] = useState(false);
  const [generateJson, setGenerateJson] = useState('');
  const { isPc, llmModelList } = useSystemStore();
  const [tabIndex, setTabIndex] = useState(isAdd ? 0 : 1); // 默认选中第一个tab
  const [aiContent, setAiContent] = useState('');
  /** AI 文本动画 */
  const [isLoadingAITextareaAnimation, setIsLoadingAITextareaAnimation] = useState(false);
  const progressTexts = [
    'AI正在生成应用名称、介绍',
    'AI正在生成应用头像',
    'AI正在生成应用提示词',
    'AI正在匹配模型能力，配置模型参数',
    'AI正在选择技能工具',
    'AI正在生成一段合适的开场白',
    'AI正在选择语音音色'
  ];
  /** AI进度文本 */
  const [aiProgressText, setAiProgressText] = useState(progressTexts[0]);

  // AI生成头像数据
  const [aiAvatarData, setAiAvatarData] = useState<Array<{ id: string; avatar: string }>>([]);
  // AI生成头像loading
  const [isLoadingAiAvatar, setIsLoadingAiAvatar] = useState(false);
  // 标准创建未输入名称控制 AI生成头像按钮置灰和保存按钮置灰
  const [isAIAvatarButtonDisabled, setIsAIAvatarButtonDisabled] = useState(true);
  // 控制AI创建按钮置灰
  const [isAICreatedButtonDisabled, setIsAICreatedButtonDisabled] = useState(true);
  const aiCreatedAbortRef = useRef<AbortController>(new AbortController());
  const resetAICreatedAbortController = (): void => {
    aiCreatedAbortRef.current = new AbortController();
  };
  // 中断AI创建应用请求的函数
  const abortAiCreatedRequest = () => {
    aiCreatedAbortRef.current.abort();
    resetAICreatedAbortController();
  };
  const aiAvatarAbortRef = useRef<AbortController>(new AbortController());
  const resetAiAvatarAbortController = () => {
    aiAvatarAbortRef.current = new AbortController();
  };
  // 中断AI生成头像请求的函数
  const abortAiAvatarRequest = () => {
    aiAvatarAbortRef.current.abort();
    resetAiAvatarAbortController();
  };
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    formState: { errors },
    trigger,
    watch
  } = useForm<FormType>({
    defaultValues: {
      avatarUrl: appModalParams?.avatarUrl || APP_ICON,
      name: isCopy ? appModalParams?.name + '_1' : appModalParams?.name || '',
      templateId: defaultAppTemplates[0].id,
      intro: appModalParams?.intro || '',
      isAICreated: 0
    }
  });
  // 标准创建的name和intro改为受控组件记得同步更新 setValue("name", name) setValue("intro", intro)
  const [name, setName] = useState(appModalParams?.name || '');
  const [intro, setIntro] = useState(appModalParams?.intro || '');
  const router = useRouter();
  const [isIntroFocus, setIsIntroFocus] = useState(false);

  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false,
    maxSingleSize: 1024 * 1024 * 1,
    oversizedFilesToastTitle: '仅支持上传大小不超过1MB的jpg、jpeg、png格式文件'
  });

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;

      // 检查文件类型
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!validTypes.includes(file.type)) {
        Toast.warning({
          title: t('仅支持上传jpg、jpeg、png格式文件')
        });
        return;
      }
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300,
          isPublic: true
        });
        setAiAvatarData((prev) => [
          ...prev,
          {
            id: nanoid(),
            avatar: data.fileUrl
          }
        ]);
      } catch (err: any) {
        Toast.warning({
          title: getErrText(err, t('common.error.Select avatar failed'))
        });
      }
    },
    [setValue, t]
  );

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      const template = simpleTemplate;
      if (!template) {
        return Promise.reject(t('core.dataset.error.Template does not exist'));
      }
      llmModelList.length &&
        template.modules.forEach((it) =>
          it.inputs.forEach((input) => {
            if (
              input.key === NodeInputKeyEnum.aiModel &&
              !llmModelList?.some((it) => it.model === input.value)
            ) {
              const suffix = input.value.replace(/^[^-]+/, '');
              const model = llmModelList.find((it) => it.model.endsWith(suffix));
              if (model) {
                input.value = model.model;
              }
            }
          })
        );
      const appData: CreateAppParams = {
        avatarUrl: data.avatarUrl,
        name: data.name,
        intro: data.intro || '',
        type: AppTypeEnum.simple,
        mode: ModeTypeEnum.simple,
        isAICreated: data.isAICreated || 0
      };
      const appId = isAdd ? undefined : { id: appModalParams?.id };
      return isAdd
        ? createApp({
            ...appData,
            edges: template.edges || [],
            modules: template.modules || [],
            chatConfig: {
              fileSelectConfig: {
                maxFiles: 20,
                canSelectFile: true,
                canSelectImg: true,
                canAutoParse: false,
                canParseORC: false
              }
            }
          }).then((res) => {
            return { ...appData, ...res };
          })
        : isCopy
          ? appCenterCopy({
              avatarUrl: data.avatarUrl || '',
              name: data.name || '',
              intro: data.intro || '',
              id: appModalParams?.id || ''
            }).then((res) => {
              return { ...appData };
            })
          : updateClientApp({
              ...appData,
              ...appId
            }).then((res) => {
              return { ...appData };
            });
    },
    onSuccess(data) {
      if (isAdd && generateJson && tabIndex === 0) {
        localStorage.setItem(`simpleAppTemplate-${data.finalAppId}`, generateJson);
      }

      isAdd &&
        router.push({
          pathname: '/app/detail',
          query: {
            appType: data.type,
            finalAppId: data.finalAppId,
            isAdmin: '0',
            init: '1',
            appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(data)))
          }
        });
      onSuccess(data);
      onClose();
    },
    successToast: '操作成功'
  });

  function onTabsChange(index: number) {
    setTabIndex(index);
  }

  async function handleAISubmit() {
    if (aiContent.trim().length < 4) {
      Toast.error('创建应用描述不得少于4个字');
      return;
    }

    try {
      const result = await hangleAICreatedApplition();
      const aiParmas = result ? JSON?.parse(result.responseText) : {};

      const parseResult = await getJsonFromText(result?.responseText || '');

      if (!checkJsonFields(parseResult, aicreateChatTemplate)) {
        Toast.error('生成应用失败,请检查返回内容');
        return;
      }

      if (parseResult) {
        setGenerateJson(JSON.stringify(parseResult));
      }

      onClickConfirm({
        avatarUrl: aiParmas?.avatar || '',
        name: aiParmas?.name || '',
        intro: aiParmas?.intro || '',
        templateId: defaultAppTemplates[0].id,
        isAICreated: 1
      });
    } catch (error) {
      console.log(error);
    } finally {
    }
  }

  async function hangleAICreatedApplition() {
    setIsLoadingAITextareaAnimation(true);

    try {
      const result = await streamFetch({
        url: '/huayun-ai/client/chat/once',
        data: {
          messages: [
            {
              role: 'user',
              content: JSON.stringify({
                creation_requirement: aiContent
              })
            }
          ],
          type: 8, // AI创建简易应用
          variables: {
            cTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
          },
          stream: true
        },
        onMessage: (message) => {
          if (message.event === 'flowNodeStatus') {
            setAiProgressText(message.name || '');
          }
          if (message.event === 'fastAnswer') {
            const data = message.text ? JSON.parse(message.text) : {};
          }
        },
        abortCtrl: aiCreatedAbortRef.current
      });
      return result;
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingAITextareaAnimation(false);
      setAiProgressText('AI正在生成应用名称、介绍');
    }
  }

  useUnmount(() => {
    abortAiCreatedRequest();
    abortAiAvatarRequest();
  });

  async function generateAvatar() {
    setIsLoadingAiAvatar(true);
    const result = await trigger('name');
    console.log(result);
    if (!result) {
      setIsLoadingAiAvatar(false);
      return;
    }
    try {
      const result = await streamFetch({
        url: '/huayun-ai/client/chat/once',
        data: {
          messages: [
            {
              role: 'user',
              content: JSON.stringify({
                name: getValues('name'),
                intro: getValues('intro')
              })
            }
          ],
          type: 9, // 头像生成
          variables: {
            cTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
          },
          stream: true
        },
        onMessage: (message) => {
          if (message.event === 'fastAnswer') {
            let data: any = {};
            try {
              data = message.text ? JSON.parse(message.text) : {};
            } catch (err) {
              console.warn('JSON parse error:', err);
              data = {};
            }
            const id = nanoid();
            setAiAvatarData((prev) => [
              ...prev,
              {
                id: id,
                avatar: data?.avatar ?? '/imgs/v2/ai_avatar.svg'
              }
            ]);
            console.log({ data });
          }
        },
        abortCtrl: aiAvatarAbortRef.current
      });
      console.log(result);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingAiAvatar(false);
    }
  }

  /**  校验头像上传数量-包含候选和默认头像不能超过10个*/
  const checkAvatarCount = () => {
    const avatarCount = aiAvatarData.length + 1;
    return avatarCount >= 10;
  };

  function RenderAICreatedLoading() {
    return (
      <>
        {progressTexts.map((text, index) => (
          <Lottie
            key={`AICreatedLoading${index + 1}`}
            // @ts-ignore
            name={`AICreatedLoading${index + 1}`}
            borderRadius={'6px'}
            overflow={'hidden'}
            display={aiProgressText === text ? 'block' : 'none'}
          />
        ))}
      </>
    );
  }

  function RenderAIProgressText() {
    return (
      <>
        {progressTexts.map((text, index) => {
          return (
            <TextJump
              key={text}
              style={{
                // 预载动画,不然跳动节奏会出错
                display: aiProgressText === text ? 'block' : 'none'
              }}
            >
              {text.split('').map((item, index) => {
                return (
                  <>
                    <span
                      key={item + index}
                      style={{
                        // @ts-ignore
                        '--i': index * 100 + 'ms'
                      }}
                    >
                      {item}
                    </span>
                  </>
                );
              })}
            </TextJump>
          );
        })}
      </>
    );
  }

  // 如果AI创建输入框有内容，则启用创建按钮
  useEffect(() => {
    if (aiContent.trim().length > 0) {
      setIsAICreatedButtonDisabled(false);
    } else {
      setIsAICreatedButtonDisabled(true);
    }
  }, [aiContent]);

  // 标准创建,应用名称不为空启用 ai生成 保存按钮
  useEffect(() => {
    if (getValues('name').trim().length > 0) {
      setIsAIAvatarButtonDisabled(false);
    }
    const subscription = watch((value, { name }) => {
      if (name === 'name') {
        setIsAIAvatarButtonDisabled(!value.name?.trim());
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  return (
    <MyModal
      iconSrc="/imgs/app/agentTitle.svg"
      title={`${isAdd && !isCopy ? '创建属于你的' : isCopy ? '复制为我的' : '编辑属于你的'}应用`}
      bgImage="/imgs/app/agentBg.png"
      bgSize="100%"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'url(/imgs/v2/gradient_bg7.png) no-repeat 0 0/100% 100%',
        borderBottom: '1px solid #E7E7E7'
      }}
      isOpen
      isCentered
      minW={respDims(567)}
      height={isAdd ? '654px' : '576px'}
      borderRadius={'14px'}
    >
      <style>{AnimationStyles}</style>
      <ModalBody padding={'0 32px'}>
        <Tabs index={tabIndex} onChange={onTabsChange}>
          <TabList
            display={isAdd ? 'flex' : 'none'}
            justifyContent={'center'}
            alignItems={'center'}
            width={'503px'}
            height={'36px'}
            padding={'2px 4px 4px 4px'}
            background={'#f3f4f6'}
            borderRadius={'8px'}
            borderBottom={'none'}
            marginTop={'24px'}
          >
            <Tab
              flex={1}
              borderRadius={'6px'}
              color={'#4E5969'}
              height={'30px'}
              _selected={{
                background: '#fff',
                color: '#303133',
                fontWeight: 500
              }}
            >
              AI 创建
            </Tab>
            <Tab
              flex={1}
              borderRadius={'6px'}
              color={'#4E5969'}
              height={'30px'}
              _selected={{
                background: '#fff',
                color: '#303133',
                fontWeight: 500
              }}
              isDisabled={isLoadingAITextareaAnimation}
            >
              标准创建
            </Tab>
          </TabList>

          <TabPanels>
            <TabPanel padding={0}>
              <Box
                w={'503px'}
                h={'63px'}
                padding={'9px 16px'}
                marginTop={'16px'}
                borderRadius={'8px'}
                backgroundColor={'#FFFCEE'}
                fontSize={'14px'}
                color={'#5C542C'}
              >
                💡
                <span style={{ color: '#342B00', fontWeight: 700 }}>示例：</span>
                你是语文作文批改助手，专注于语文作文批改，能从语法、结构、文采等多方面给出详细评语和改进建议
              </Box>
              <Box marginTop={'14px'} position={'relative'}>
                <Textarea
                  placeholder="请描述你希望创建一个什么样的应用"
                  w={'503px'}
                  h={'351px'}
                  background={'#f6f6f6'}
                  fontSize={'14px'}
                  value={aiContent}
                  isDisabled={isLoadingAITextareaAnimation}
                  resize={'none'}
                  maxLength={500}
                  _placeholder={{
                    fontSize: '14px',
                    color: '#86909C'
                  }}
                  onChange={(e) => {
                    const value = e.target.value;
                    setAiContent(value);
                  }}
                  onBlur={(e) => {
                    const value = e.target.value;
                    if (value.length > 500) {
                      setAiContent(value.slice(0, 500));
                    }
                  }}
                />
                {!isLoadingAITextareaAnimation && (
                  <Box position={'absolute'} right={'12px'} bottom={'4px'} zIndex={999}>
                    <TextareaCount>
                      {aiContent.length > 500 ? 500 : aiContent.length}/{500}
                    </TextareaCount>
                  </Box>
                )}
                {
                  // AI创建动画
                  <Box display={isLoadingAITextareaAnimation ? 'block' : 'none'}>
                    <Box
                      position={'absolute'}
                      top={'10px'}
                      left={'50%'}
                      transform={'translate(-50%,0)'}
                      w={'483px'}
                      h={'294px'}
                    >
                      {RenderAICreatedLoading()}
                    </Box>
                    <Box
                      position={'absolute'}
                      left={'50%'}
                      bottom={0}
                      transform="translate(-50%, 0)"
                      fontSize={'14px'}
                      color={'#303133'}
                    >
                      {RenderAIProgressText()}
                    </Box>
                  </Box>
                }
              </Box>
            </TabPanel>
            <TabPanel padding={0}>
              <Box
                fontWeight="400"
                fontSize="14px"
                color="#4E5969"
                my="8px"
                mt={isAdd ? '20px' : '24px'}
              >
                应用名称 <span style={{ color: '#F53F3F' }}>*</span>
              </Box>
              <Flex mt={3} alignItems={'center'}>
                <FormControl isInvalid={!!errors.name} ml={4} flex={1} marginLeft={0}>
                  <Input
                    {...register('name', {
                      required: '请输入应用名称'
                    })}
                    w="100%"
                    autoFocus
                    overflow="hidden"
                    maxLength={20}
                    value={name}
                    placeholder="给应用取个独一无二的名字吧，如：语文作文批改助手"
                    background={'#f6f6f6'}
                    _placeholder={{
                      color: '#86909C',
                      fontSize: '14px',
                      textShadow: 'none'
                    }}
                    onChange={(e) => {
                      const value = e.target.value;
                      setName(value);
                      setValue('name', value);
                    }}
                    onBlur={(e) => {
                      const value = e.target.value;
                      if (value.length > 20) {
                        setName(value.slice(0, 20));
                        setValue('name', value.slice(0, 20));
                      }
                    }}
                  />
                  <Box
                    position={'absolute'}
                    right={'16px'}
                    bottom={'50%'}
                    transform={'translateY(50%)'}
                    zIndex={999}
                  >
                    <TextareaCount>
                      {name.length > 20 ? 20 : name.length}/{20}
                    </TextareaCount>
                  </Box>
                  {errors.name && (
                    <FormErrorMessage ml="20px">{errors.name.message}</FormErrorMessage>
                  )}
                </FormControl>
              </Flex>
              <>
                <Box mt={4}>
                  <Box fontWeight="400" fontSize="14px" color="#4E5969" mb="10px" mt="16px">
                    应用介绍
                  </Box>
                  <Box
                    w="100%"
                    position={'relative'}
                    height={'153px'}
                    border={isIntroFocus ? '1px solid #7D4DFF' : '1px solid #E5E7EB'}
                    borderRadius={'8px'}
                    background={isIntroFocus ? 'var(--chakra-colors-white)' : '#f6f6f6'}
                    boxShadow={isIntroFocus ? '0px 0px 0px 2.4px rgba(51, 112, 255, 0.15)' : 'none'}
                    onFocus={() => {
                      setIsIntroFocus(true);
                    }}
                    onBlur={() => {
                      setIsIntroFocus(false);
                    }}
                  >
                    <Textarea
                      {...register('intro')}
                      rows={6}
                      maxLength={500}
                      placeholder="请输入应用介绍，描述该应用的功能，将用于应用提示词自动生成，如：专注于语文作文批改，能从语法、结构、文采等多方面给出详细评语和改进建议"
                      height={'126px'}
                      background={'#f6f6f6'}
                      resize={'none'}
                      value={intro}
                      border={'none'}
                      _placeholder={{
                        color: '#86909C',
                        fontSize: '14px'
                      }}
                      _focus={{
                        borderColor: 'transparent',
                        boxShadow: 'none',
                        background: 'transparent'
                      }}
                      onChange={(e) => {
                        const value = e.target.value;
                        setIntro(value);
                        setValue('intro', value);
                      }}
                      onBlur={(e) => {
                        const value = e.target.value;
                        if (value.length > 500) {
                          setIntro(value.slice(0, 500));
                          setValue('intro', value.slice(0, 500));
                        }
                      }}
                    />
                    <Box position={'absolute'} right={'12px'} bottom={'4px'} zIndex={999}>
                      <TextareaCount>
                        {intro.length > 500 ? 500 : intro.length}/{500}
                      </TextareaCount>
                    </Box>
                  </Box>
                </Box>
                <Box mt={4}>
                  <Box fontWeight="400" fontSize="14px" color="#4E5969" mb="16px" display={'flex'}>
                    <Box>
                      应用头像 <span style={{ color: '#F53F3F' }}>*</span>
                    </Box>
                    <Button
                      marginLeft={'auto'}
                      marginRight={'10px'}
                      display={'flex'}
                      alignItems={'center'}
                      fontSize={'12px'}
                      h={'24px'}
                      // w={'55px'}
                      padding={'2px 12px'}
                      background={'#fff'}
                      color={'#4E5969'}
                      _hover={{}}
                      boxShadow={'none'}
                      onClick={onOpenSelectFile}
                      isDisabled={checkAvatarCount() || isLoadingAiAvatar}
                      _disabled={{
                        opacity: 0.4,
                        cursor: 'not-allowed'
                      }}
                    >
                      <SvgIcon name="aliginArrowUpLine" w={'14px'} h={'14px'} marginRight={'4px'} />
                      <Box>本地上传</Box>
                    </Button>

                    <Tooltip hasArrow label="输入名称和介绍后， 点击可自动生成头像" placement="top">
                      <Button
                        w={checkAvatarCount() ? '79px' : '89px'}
                        h={'24px'}
                        padding={'2px 12px'}
                        background={
                          'linear-gradient(90deg, rgba(236, 240, 255, 0.95) 0%, rgba(245, 235, 255, 0.90) 100%)'
                        }
                        _hover={{}}
                        onClick={generateAvatar}
                        isLoading={isLoadingAiAvatar}
                        isDisabled={checkAvatarCount() || isAIAvatarButtonDisabled}
                        _disabled={{
                          opacity: 0.5,
                          cursor: 'not-allowed'
                        }}
                        _loading={{
                          opacity: 1
                        }}
                        spinner={
                          <>
                            <Box
                              display={'flex'}
                              justifyContent={'center'}
                              alignItems={'center'}
                              overflow={'hidden'}
                              cursor={'not-allowed'}
                            >
                              <AvatarCreatedLoading className="button">
                                <SvgIcon
                                  name="AI_generationLoading"
                                  w={'14px'}
                                  h={'14px'}
                                  marginRight={'4px'}
                                  color={'#fff'}
                                />
                                AI生成中
                                <div className="hoverEffect">
                                  <div></div>
                                </div>
                              </AvatarCreatedLoading>
                            </Box>
                          </>
                        }
                      >
                        <SvgIcon
                          color={'red'}
                          name="AI_generation"
                          w={'14px'}
                          h={'14px'}
                          marginRight={'4px'}
                        />
                        <Box>
                          <GradientText>AI生成</GradientText>
                        </Box>
                      </Button>
                    </Tooltip>
                  </Box>
                  {/* <MyTooltip overflow={'hidden'} label={t('common.Set Avatar')}> */}
                  <Box display={'flex'} alignItems={'center'}>
                    <Box w="64px" h="64px" borderRadius={'50%'} overflow={'hidden'}>
                      <Avatar
                        flexShrink={0}
                        src={getValues('avatarUrl')}
                        w={'100%'}
                        h={'100%'}
                        objectFit={'cover'}
                        p={'0px'}
                      />
                    </Box>
                    <VerticalLine />
                    <Box display={'flex'} w={'406px'} overflowX={'scroll'} whiteSpace={'nowrap'}>
                      {aiAvatarData.map((it, index) => (
                        <>
                          <Box
                            key={it.id}
                            w={'41px'}
                            h={'42px'}
                            borderRadius={'50%'}
                            border={'1px solid #E5E7EB'}
                            marginRight={'11px'}
                            flexBasis={'41px'}
                            flexShrink={0}
                            cursor={'pointer'}
                            overflow={'hidden'}
                            onClick={() => {
                              setValue('avatarUrl', it.avatar);
                              setRefresh((state) => !state);
                            }}
                          >
                            <Image src={it.avatar} alt="" width={'100%'} height={'100%'}></Image>
                          </Box>
                        </>
                      ))}
                    </Box>
                  </Box>
                  {/* </MyTooltip> */}
                </Box>
              </>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </ModalBody>
      <ModalFooter marginBottom={'10px'}>
        <Button
          variant={'grayBase'}
          mr={4}
          onClick={() => {
            onClose();
          }}
        >
          {t('common.Close')}
        </Button>
        {tabIndex === 0 ? (
          <>
            {/* AI创建按钮 */}
            <Button
              onClick={handleAISubmit}
              isDisabled={isLoadingAITextareaAnimation || isAICreatedButtonDisabled}
            >
              <SvgIcon name="confirmStart" marginRight={'6px'} />
              {isLoadingAITextareaAnimation ? '生成中' : '确认创建'}
            </Button>
          </>
        ) : (
          <>
            {/* 标准创建 */}
            <Button
              isDisabled={isAIAvatarButtonDisabled}
              isLoading={creating}
              onClick={handleSubmit((data) => onClickConfirm(data))}
            >
              保存
            </Button>
          </>
        )}
      </ModalFooter>
      <File onSelect={onSelectFile} />
    </MyModal>
  );
};

export default AppModal;
