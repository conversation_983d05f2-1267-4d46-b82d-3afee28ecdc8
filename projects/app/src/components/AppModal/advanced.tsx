import React, { useCallback, useState } from 'react';
import {
  Box,
  Flex,
  Button,
  ModalFooter,
  ModalBody,
  Input,
  Grid,
  useTheme,
  Card,
  Textarea,
  FormErrorMessage,
  FormControl
} from '@chakra-ui/react';
import { useSelectFile } from '@/hooks/useSelectFile';
import { useForm } from 'react-hook-form';
import { uploadImage } from '@/utils/file';
import { getErrText } from '@/utils/string';
import { Toast } from '@/utils/ui/toast';
import { createApp, updateClientApp } from '@/api/app';
import { useSystemStore } from '@/store/useSystemStore';
import { useRequest } from '@/hooks/useRequest';
import Avatar from '@/components/Avatar';
import MyTooltip from '@/components/MyTooltip';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { APP_ICON } from '@/constants/common';
import { AppModalDataType, AppUpdateParams, CreateAppParams } from '@/types/api/app';
import { NodeInputKeyEnum } from '@/fastgpt/global/core/workflow/constants';
import { defaultAppTemplates } from '@/fastgpt/web/core/app/templates';
import { getSampleAppInfo } from '@/utils/app';
import { useRouter } from 'next/router';

type FormType = {
  avatarUrl: string;
  name: string;
  templateId: string;
  intro?: string;
};
const AppTenantModal = ({
  onClose,
  onSuccess,
  isCopy,
  appModalParams
}: {
  onClose: () => void;
  onSuccess: (data: any) => void;
  isCopy?: boolean;
  appModalParams?: AppModalDataType;
}) => {
  const isAdd = !appModalParams?.id;
  const { t } = useTranslation();
  const [refresh, setRefresh] = useState(false);
  const theme = useTheme();
  const { isPc, feConfigs, llmModelList } = useSystemStore();
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm<FormType>({
    defaultValues: {
      avatarUrl: appModalParams?.avatarUrl || APP_ICON,
      name: appModalParams?.name || '',
      templateId: defaultAppTemplates[0].id,
      intro: appModalParams?.intro || ''
    }
  });
  const router = useRouter();

  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false
  });

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300,
          isPublic: true
        });
        setValue('avatarUrl', data.fileUrl);
        setRefresh((state) => !state);
      } catch (err: any) {
        Toast.warning({
          title: getErrText(err, t('common.error.Select avatar failed'))
        });
      }
    },
    [setValue, t]
  );

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      const template = defaultAppTemplates.find((item) => item.id === data.templateId);
      if (!template) {
        return Promise.reject(t('core.dataset.error.Template does not exist'));
      }
      // 模型名称替换，defaultAppTemplates中的模型名可能与llmModelList模型名使用了不同的前缀，
      // 例：huayun-3.5-turbo 对应 gpt-3.5-turbo
      llmModelList.length &&
        template.modules.forEach((it) =>
          it.inputs.forEach((input) => {
            if (
              input.key === NodeInputKeyEnum.aiModel &&
              !llmModelList?.some((it) => it.model === input.value)
            ) {
              const suffix = input.value.replace(/^[^-]+/, '');
              const model = llmModelList.find((it) => it.model.endsWith(suffix));
              if (model) {
                input.value = model.model;
              }
            }
          })
        );
      const appData: CreateAppParams | AppUpdateParams = {
        avatarUrl: data.avatarUrl,
        name: data.name,
        intro: data.intro || ''
      };
      const appId = isAdd ? undefined : { id: appModalParams.id };
      return isAdd
        ? createApp({
            ...appData,
            type: template.type,
            modules: template.modules || [],
            edges: template.edges || [],
            chatConfig: template.chatConfig || {}
          }).then((res) => {
            return { ...res, ...appData };
          })
        : updateClientApp({ ...appData, ...appId }).then(() => {
            return appData;
          });
    },
    onSuccess(data) {
      isAdd &&
        router.push({
          pathname: '/app/detail',
          query: {
            appType: data.type,
            finalAppId: data.finalAppId,
            isAdmin: '0',
            appDetail: encodeURIComponent(JSON.stringify(getSampleAppInfo(data)))
          }
        });
      onSuccess(data);
      onClose();
    },
    successToast: isAdd ? t('common.Create Success') : t('common.Update Success')
  });

  return (
    <MyModal
      iconSrc="/imgs/app/agentTitle.svg"
      title={`${isAdd && !isCopy ? '创建属于你的' : isCopy ? '复制为我的' : '编辑属于你的'}应用`}
      headerStyle={{
        background: 'url(/imgs/v2/gradient_bg7.png) no-repeat 0 0/100% 100%',
        borderBottom: '1px solid #E7E7E7'
      }}
      isOpen
      isCentered
    >
      <ModalBody>
        <Box fontWeight="400" fontSize="14px" color="#4E5969" my="8px">
          取个响亮的名字
        </Box>
        <Flex mt={3} alignItems={'center'}>
          <MyTooltip label={t('common.Set Avatar')}>
            <Avatar
              flexShrink={0}
              src={getValues('avatarUrl')}
              w={['28px', '32px']}
              h={['28px', '32px']}
              cursor={'pointer'}
              borderRadius={'md'}
              onClick={onOpenSelectFile}
            />
          </MyTooltip>
          <FormControl isInvalid={!!errors.name}>
            <Input
              flex={1}
              ml={4}
              autoFocus
              w="510px"
              placeholder="请输入应用名称"
              _placeholder={{
                fontSize: '14px'
              }}
              bg={'myWhite.600'}
              {...register('name', {
                required: '请输入应用名称'
              })}
            />
            {errors.name && <FormErrorMessage ml="20px">{errors.name.message}</FormErrorMessage>}
          </FormControl>
        </Flex>
        {
          <>
            <Box mt={4}>
              <Box fontWeight="400" fontSize="14px" color="#4E5969" my="8px">
                应用介绍
              </Box>
              <Box w="100%">
                <Textarea
                  rows={4}
                  maxLength={500}
                  placeholder="请输入应用介绍，描述该应用的功能，将用于应用提示词自动生成，如：专注于根据最新的教育标准设计有效的教学计划"
                  bg={'myWhite.600'}
                  {...register('intro')}
                  _placeholder={{
                    fontSize: '14px'
                  }}
                />
              </Box>
            </Box>
          </>
        }
        {
          /*!feConfigs?.hide_app_flow && TODO */ isAdd && (
            <>
              <Box fontWeight="400" fontSize="14px" color="#4E5969" my="8px">
                {t('core.app.Select app from template')}
              </Box>
              <Grid
                userSelect={'none'}
                gridTemplateColumns={['repeat(1,1fr)', 'repeat(2,1fr)']}
                gridGap={[2, 4]}
              >
                {defaultAppTemplates.map((item) => (
                  <Card
                    key={item.id}
                    position="relative"
                    border={theme.borders.base}
                    p={3}
                    borderRadius="md"
                    cursor="pointer"
                    boxShadow="sm"
                    _before={
                      getValues('templateId') === item.id
                        ? {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            borderRadius: 'inherit',
                            padding: '2px', // 调整边框宽度
                            background: 'primary.500',
                            WebkitMask:
                              'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                            WebkitMaskComposite: 'xor',
                            maskComposite: 'exclude'
                          }
                        : {}
                    }
                    _hover={{
                      boxShadow: 'md'
                    }}
                    onClick={() => {
                      setValue('templateId', item.id);
                      setRefresh((state) => !state);
                    }}
                  >
                    <Flex alignItems="center">
                      <Avatar src={item.avatar} borderRadius="md" w="20px" />
                      <Box ml={3} fontWeight="bold">
                        {t(item.name)}
                      </Box>
                    </Flex>
                    <Box fontSize="sm" mt={4}>
                      {t(item.intro)}
                    </Box>
                  </Card>
                ))}
              </Grid>
            </>
          )
        }
      </ModalBody>

      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          {t('common.Close')}
        </Button>
        <Button isLoading={creating} onClick={handleSubmit((data) => onClickConfirm(data))}>
          {`确认${isAdd ? '创建' : '编辑'}`}
        </Button>
      </ModalFooter>

      <File onSelect={onSelectFile} />
    </MyModal>
  );
};

export default AppTenantModal;
