import styled from '@emotion/styled';

export const AvatarCreatedLoading = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 89px;
  height: 24px;
  padding: 0 12px;
  border: 0;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.02s;
  cursor: pointer;
  color: white;
  z-index: 1;
  box-shadow: 0 0px 7px -5px rgba(0, 0, 0, 0.5);
  cursor: not-allowed;

  .hoverEffect {
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: -1;
  }
  .hoverEffect div {
    background: linear-gradient(90deg, #58aeff 0%, #9f71ff 46.5%, #ff9ed4 100%);
    border-radius: 40rem;
    width: 10rem;
    height: 10rem;
    transition: 0.4s;
    filter: blur(20px);
    animation: effect infinite 3s linear;
    opacity: 1;
    cursor: not-allowed;
  }
`;

export const AnimationStyles = `
  @keyframes effect {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes textJump {
    0% { transform: translateY(0); }
    10% { transform: translateY(-3px); } 
    20% { transform: translateY(0); }
    /* 20%-100% 是等待时间，占总时长的80% */
    100% { transform: translateY(0); }
  }
}
`;

export const GradientText = styled.span`
  background: linear-gradient(315deg, #752dff 7.47%, #be60ff 81.47%) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
`;

export const VerticalLine = styled.div`
  width: 1px;
  height: 33px;
  background: #e5e7eb;
  margin: 0 16px;
`;

// 文字跳动组件
export const TextJump = styled.div`
  position: relative;
  height: 22px;
  margin-bottom: 10px;

  span {
    position: relative;
    display: inline-block;
    background: linear-gradient(270deg, #b67eff -19.39%, #601cff 103%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    animation: textJump 3.75s ease-in-out infinite;
    animation-delay: var(--i);
  }
`;

// 文本框字符统计样式
export const TextareaCount = styled.div`
  display: inline-block;
  font-size: 14px;
  color: #4e5969;
  pointer-events: none;
`;
