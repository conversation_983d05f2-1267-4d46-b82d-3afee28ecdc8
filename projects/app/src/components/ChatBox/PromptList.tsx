import SvgIcon from '@/components/SvgIcon';
import { respDims, rpxDim } from '@/utils/chakra';
import { getPromptCenterList, promptCenterDelete } from '@/api/prompt';
import {
  Box,
  Center,
  ChakraProps,
  Flex,
  Grid,
  keyframes,
  useDisclosure,
  useOutsideClick
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import React, {
  ForwardedRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import PromptModal, { PromptDataType } from './PromptModal';
import { useUserStore } from '@/store/useUserStore';
import styles from '@/styles/variable.module.scss';
import { useChatStore } from '@/store/useChatStore';
import PromptSetting from './PromptSetting';
import { useSystemStore } from '@/store/useSystemStore';
import MobilePromptModal from './MobilePromptModal';
import { UserRoleEnum } from '@/constants/api/auth';
import { PromptType } from '@/types/api/prompt';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { SvgIconNameType } from '../SvgIcon/data';
import { DataSource } from '@/constants/common';
import { useRouter } from 'next/router';

export type PromptListRef = {
  addPrompt: (val: string) => void;
  selectPrompt: (promptId?: string) => void;
};

const animIn = keyframes`
  from { visibility: hidden; max-height: 0; }
  to { visibility: visible; max-height: 800px; }
`;

const animOut = keyframes`
  from { visibility: visible; max-height: 800px; }
  to { visibility: hidden; max-height: 0; }
`;

const animOpen = keyframes`
  from { visibility: visible; max-height: 800px; }
  to { visibility: visible; max-height: 0; }
`;

const animClose = keyframes`
  from { visibility: hidden; max-height: 0; }
  to { visibility: hidden; max-height: 0; }
`;

const animUp = keyframes`
  from { max-height: 0; }
  to { max-height: 800px; }
`;

const animDown = keyframes`
  from { max-height: 800px; }
  to { max-height: 0; }
`;

const tabs: { name: string; value: string; svg: SvgIconNameType }[] = [
  {
    name: '指令中心',
    svg: 'promptCenter',
    value: '0'
  },
  {
    name: '我的',
    svg: 'promptUser',
    value: '1'
  }
];

const PromptList = (
  {
    isOpen,
    filterText,
    onSelectPrompt,
    onClose,
    ...props
  }: {
    isOpen: boolean;
    filterText?: string;
    onSelectPrompt?: (prompt: PromptType) => boolean;
    onClose?: () => void;
  } & ChakraProps,
  ref: ForwardedRef<PromptListRef>
) => {
  const router = useRouter();
  const { chatData } = useChatStore();

  const { userInfo } = useUserStore();

  const [prompts, setPrompts] = useState<PromptType[]>([]);

  const [currentPromptId, setCurrentPromptId] = useState<string>();

  const [isEdit, setIsEdit] = useState(false);

  const [currentTab, setCurrentTab] = useState('0');

  const [modalPromptId, setModalPromptId] = useState<PromptDataType>();

  const animated = useRef(false);

  const { isPc } = useSystemStore();

  const boxRef = useRef<HTMLDivElement>(null);

  const {
    isOpen: isOpenPromptModal,
    onOpen: onOpenPromptModal,
    onClose: onClosePromptModal
  } = useDisclosure();

  const {
    isOpen: isOpenMobilePromptModal,
    onOpen: onOpenMobilePromptModal,
    onClose: onCloseMobilePromptModal
  } = useDisclosure();

  const {
    isOpen: isOpenPromptSetting,
    onOpen: onOpenPromptSetting,
    onClose: onClosePromptSetting
  } = useDisclosure();

  const appId = chatData.appId;

  const gridRef = useRef<HTMLDivElement>(null);
  const acitonRef = useRef<'edit' | 'copy' | null>(null);
  const isRoot = userInfo?.roleType === UserRoleEnum.Admin;

  // 检测是否在temp_chat页面
  const isTempChatPage = router.pathname.includes('/temp_chat');

  const anims = useMemo(() => {
    let anims = { inOut: '', upDown: '' };
    if (isOpen) {
      if (animated.current) {
        anims.inOut = animIn;
        anims.upDown = animUp;
      } else {
        anims.inOut = animOpen;
        anims.upDown = '';
      }
    } else {
      if (animated.current) {
        anims.inOut = animOut;
        anims.upDown = animDown;
      } else {
        anims.inOut = animClose;
        anims.upDown = '';
      }
    }
    animated.current = true;
    return anims;
  }, [isOpen]);

  const onCurrentTab = (key: string) => {
    setCurrentTab(key);
  };

  const presentPrompts = useMemo(() => {
    let filteredPrompts = filterText
      ? prompts.filter((it) => it.promptTitle.includes(filterText))
      : prompts;
    if (isEdit) {
      filteredPrompts = filteredPrompts.filter((prompt) => prompt.permission == 1);
    }
    return filteredPrompts;
  }, [prompts, filterText, isEdit]);

  const onAddPrompt = (val?: string) => {
    setModalPromptId({ inputContent: val, appId: appId });
    if (!isPc) {
      onOpenMobilePromptModal();
      return;
    }
    onOpenPromptModal();
  };

  const handlePromptAction = (prompt: PromptType, action: 'copy' | 'edit') => {
    let modalPromptData: Partial<PromptType> = {
      appId: appId,
      promptTitle: prompt?.promptTitle,
      description: prompt?.description,
      inputContent: prompt?.inputContent,
      proContent: prompt?.proContent,
      hiddenContent: prompt?.hiddenContent,
      type: prompt?.type
    };

    if (action === 'edit') {
      modalPromptData.id = prompt?.id;
    }

    acitonRef.current = action;
    setModalPromptId(modalPromptData);

    if (!isPc) {
      onOpenMobilePromptModal();
    } else {
      onOpenPromptModal();
    }
  };

  // 使用方式
  const onCopyPrompt = (prompt: PromptType) => {
    handlePromptAction(prompt, 'copy');
  };

  const onEditPrompt = (prompt: PromptType) => {
    handlePromptAction(prompt, 'edit');
  };

  const { refetch: refetchPrompts } = useQuery(
    [appId, currentTab],
    () =>
      appId
        ? getPromptCenterList({ permission: Number(currentTab), tenantAppId: appId })
        : Promise.resolve([]),
    {
      enabled: !isTempChatPage,
      onSuccess(data) {
        setPrompts(data);
      }
    }
  );

  const onRemovePrompt = (id: string) => {
    MessageBox.confirm({
      title: '删除',
      content: '确定删除该快捷指令？',
      onOk: async () => {
        promptCenterDelete({ id }).then(() => {
          refetchPrompts();
          Toast.success('删除成功');
        });
      }
    });
  };

  useImperativeHandle(ref, () => ({
    addPrompt: (val: string) => onAddPrompt(val),
    selectPrompt: (promptId?: string) => {
      setCurrentPromptId(promptId);
    }
  }));

  useOutsideClick({
    enabled: isOpen,
    ref: boxRef,
    handler: () =>
      !isOpenPromptModal && !isOpenMobilePromptModal && !isOpenPromptSetting && onClose?.()
  });

  useEffect(() => {
    if (isOpen) {
      setIsEdit(false);
    }
    if (gridRef.current) {
      gridRef.current.scrollTop = 0;
    }
    setCurrentTab('0');
  }, [currentPromptId, isOpen]);

  return (
    <Box
      ref={boxRef}
      onClick={(e) => e.stopPropagation()}
      {...props}
      overflow="hidden"
      animation={`${anims.inOut} .3s forwards`}
      borderRadius={respDims('16fpx')}
    >
      <Flex
        h="100%"
        flexDir="column"
        bgColor="#F9F9F9"
        position="relative"
        overflow="hidden"
        {...(isPc ? { minH: '200px', maxH: '285px' } : { maxH: '73vh' })}
        pos="relative"
        zIndex="2"
        {...(anims.upDown ? { animation: `${anims.upDown} .1s forwards` } : {})}
      >
        <Flex justifyContent="space-between" mt={respDims('32rpx', 13)} ml={respDims('48rpx', 24)}>
          <Flex alignItems="stretch" flexShrink="0" ml={respDims(10)}>
            {tabs.map((tab) => (
              <Flex
                key={tab.value}
                alignItems="center"
                pb="3px"
                m="10px 32px 16px 0"
                position="relative"
                {...(tab.value === currentTab
                  ? {
                      color: 'primary.500',
                      _after: {
                        position: 'absolute',
                        content: '""',
                        left: '0',
                        right: '0',
                        bottom: '-1px',
                        w: '100%',
                        height: '2px',
                        bgColor: 'primary.500'
                      }
                    }
                  : {
                      color: '#4E5969'
                    })}
                fontSize="14px"
                fontWeight="bold"
                cursor="pointer"
                onClick={() => onCurrentTab(tab.value)}
              >
                <SvgIcon mr="8px" name={tab.svg} w={rpxDim(16)} h={rpxDim(19)} />
                <Box> {tab.name}</Box>
              </Flex>
            ))}
          </Flex>

          <Flex px={respDims('24rpx', 24)} alignItems="center" userSelect="none" mb="10px">
            {!isPc && isEdit && (
              <Center
                mb={rpxDim(24)}
                pos="absolute"
                left={rpxDim(32)}
                fontSize={rpxDim(30)}
                lineHeight={rpxDim(40)}
                color="#606266"
                onClick={() => {
                  setIsEdit(false);
                }}
              >
                取消
              </Center>
            )}
            <Flex>
              {!isPc && !isEdit && (
                <Flex
                  pos="absolute"
                  alignItems="center"
                  bgColor="rgba(255,255,255,0.3)"
                  boxShadow="0rpx 2rpx 16rpx 0rpx rgba(125,128,143,0.06)"
                  borderRadius={rpxDim(8)}
                  onClick={() => onAddPrompt()}
                >
                  <SvgIcon color="#753CFF" name="plus" w={rpxDim(19)} h={rpxDim(19)} />
                  <Box color="#753CFF" fontSize={rpxDim(24)} ml={rpxDim(18)} fontWeight="400">
                    添加
                  </Box>
                </Flex>
              )}
              {isPc ? (
                currentTab == '1' && (
                  <>
                    <Center
                      px={respDims(14, 4)}
                      py={respDims(5)}
                      fontSize={respDims(14, 12)}
                      lineHeight={respDims(22, 16)}
                      borderRadius={respDims(8)}
                      color="primary.500"
                      mr="4px"
                      cursor="pointer"
                      p={respDims('8fpx')}
                      onClick={() => onAddPrompt()}
                      _hover={{
                        bg: 'rgba(0,0,0,0.03)'
                      }}
                    >
                      <SvgIcon name="plus" w="14px" h="14px" />
                      <Box ml={respDims(10)} whiteSpace="nowrap">
                        添加
                      </Box>
                    </Center>

                    <Center
                      ml="auto"
                      p={respDims('8fpx')}
                      borderRadius="8px"
                      cursor="pointer"
                      onClick={onClose}
                      _hover={{
                        bg: 'rgba(0,0,0,0.03)'
                      }}
                    >
                      <SvgIcon name="close" w="20px" h="20px" color="#606266" />
                    </Center>
                  </>
                )
              ) : !isEdit ? (
                <Flex mb={rpxDim(24)} pos="absolute" right={rpxDim(32)}>
                  <Center
                    fontSize={rpxDim(24)}
                    lineHeight={rpxDim(40)}
                    borderRadius={rpxDim(8)}
                    pt={rpxDim(4)}
                    pr={rpxDim(20)}
                    pb={rpxDim(4)}
                    pl={rpxDim(20)}
                    color="#4E5969"
                    mr={rpxDim(28)}
                    bgColor="rgba(255,255,255,0.6)"
                    _hover={{
                      bgColor: '#ffffff'
                    }}
                    cursor="pointer"
                    onClick={() => setIsEdit(true)}
                  >
                    <Box>编辑</Box>
                  </Center>

                  <Center ml="auto" p={respDims(6)} cursor="pointer" onClick={onClose}>
                    <SvgIcon name="close" w={rpxDim(40)} h={rpxDim(40)} color="#606266" />
                  </Center>
                </Flex>
              ) : (
                <Flex mb={rpxDim(24)} pos="absolute" right={rpxDim(32)}>
                  <Center
                    fontSize={rpxDim(24)}
                    lineHeight={rpxDim(40)}
                    borderRadius={rpxDim(8)}
                    pt={rpxDim(4)}
                    pb={rpxDim(4)}
                    pl={rpxDim(20)}
                    color="#4E5969"
                    cursor="pointer"
                    onClick={() => setIsEdit(false)}
                  >
                    <Box fontSize={rpxDim(30)} color="primary.500" lineHeight={rpxDim(40)}>
                      完成
                    </Box>
                  </Center>
                </Flex>
              )}
            </Flex>
          </Flex>
        </Flex>

        {presentPrompts.length > 0 ? (
          <Box
            flex="1 0 0"
            mt={respDims(6)}
            mb="15px"
            mr={[`-${styles.scrollbarSmWidth}`, `-${styles.scrollbarWidth}`]}
            px={respDims('20rpx', 12)}
            overflow="hidden"
          >
            <Box h="100%" overflowY="scroll" px={respDims(12)}>
              <Grid
                ref={gridRef}
                gridTemplateColumns={['repeat(2,2fr)', 'repeat(2,2fr)', 'repeat(3,2fr)']}
                gridGap={respDims('28rpx', 16)}
              >
                {presentPrompts.map((prompt) => (
                  <Box
                    key={prompt.id}
                    w="100"
                    pt={respDims('32rpx', 16)}
                    pl={respDims('32rpx', 16)}
                    pb={respDims('32rpx', 16)}
                    borderRadius={respDims(8)}
                    cursor="pointer"
                    overflow="hidden"
                    boxShadow="0px 0px 4px 0px rgba(0,0,0,0.03)"
                    {...(isPc
                      ? {
                          ...(prompt.id === currentPromptId
                            ? {
                                bgColor: 'primary.50',
                                border: '1px solid primary.500',
                                _hover: {
                                  '& .prompt-toolbar': {
                                    display: 'flex',
                                    pointerEvents: 'auto'
                                  }
                                }
                              }
                            : {
                                bgColor: '#ffffff',
                                border: '1px solid #ffffff',
                                _hover: {
                                  boxShadow: '0px 0px 10px 0px rgba(195,216,255,0.27)',
                                  border: '1px solid primary.500',
                                  '& .prompt-toolbar': {
                                    display: 'flex',
                                    pointerEvents: 'auto'
                                  }
                                }
                              })
                        }
                      : {
                          ...(prompt.id === currentPromptId
                            ? {
                                bgColor: 'primary.50',
                                border: '1px solid primary.500'
                              }
                            : {
                                bgColor: '#ffffff',
                                border: '1px solid #ffffff'
                              })
                        })}
                    style={{
                      breakInside: 'avoid'
                    }}
                    onClick={() =>
                      !isEdit && onSelectPrompt?.(prompt) && setCurrentPromptId(prompt.id)
                    }
                  >
                    <Flex overflow="hidden">
                      <Box
                        color="#303133"
                        fontSize={respDims('28rpx', 16)}
                        lineHeight={respDims('40rpx', 20)}
                        fontWeight="500"
                        style={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitBoxOrient: 'vertical',
                          WebkitLineClamp: '1',
                          whiteSpace: 'normal'
                        }}
                      >
                        {prompt.promptTitle}
                      </Box>
                    </Flex>
                    <Flex
                      {...(!isPc
                        ? { flexDir: 'column' }
                        : { alignItems: 'center', pos: 'relative' })}
                    >
                      <Box
                        style={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitBoxOrient: 'vertical',
                          WebkitLineClamp: '2',
                          marginBottom: '15px',
                          whiteSpace: 'normal',
                          minHeight: '42px'
                        }}
                        pr={respDims('32rpx', 16)}
                        mt={respDims('20rpx', 10)}
                        color="#909399"
                        fontSize={respDims(14, 12)}
                        flex="1"
                        {...(!isPc && { minH: rpxDim(65) })}
                      >
                        {prompt.description}
                      </Box>
                      {(prompt.permission == 1 || userInfo?.roleType === UserRoleEnum.Admin) &&
                        !isPc &&
                        isEdit && (
                          <Flex
                            flexShrink="0"
                            color="#A8ABB2"
                            onClick={(e) => e.stopPropagation()}
                            justifyContent="space-between"
                            mt={rpxDim(20)}
                          >
                            <SvgIcon
                              name="trash"
                              w={rpxDim(40)}
                              h={rpxDim(40)}
                              cursor="pointer"
                              color="#eb473e"
                              onClick={() => onRemovePrompt(prompt.id)}
                            />
                            <SvgIcon
                              name="editLine"
                              w={rpxDim(40)}
                              h={rpxDim(40)}
                              color="#303133"
                              cursor="pointer"
                              onClick={() => {
                                onEditPrompt(prompt);
                              }}
                            />
                          </Flex>
                        )}
                      {prompt.source !== DataSource.Personal && (
                        <Box
                          pos="absolute"
                          fontSize="12px"
                          color="#909399"
                          fontWeight="400"
                          bottom="-2px"
                          left="0"
                        >
                          来自：
                          {prompt.source === DataSource.Tenant
                            ? '专属'
                            : prompt.source === DataSource.Offical
                              ? '官方'
                              : ''}
                        </Box>
                      )}

                      <Flex
                        flexShrink="0"
                        className="prompt-toolbar"
                        display="none"
                        color="#A8ABB2"
                        pos="absolute"
                        right="0"
                        left="0"
                        style={{ zIndex: 1 }}
                        h="100%"
                        p="80px 12px 66px 0px"
                        ml="-16px"
                        justifyContent="end"
                        pointerEvents="none"
                      >
                        {prompt.source !== DataSource.Personal ? (
                          <Flex mt="15px">
                            <SvgIcon
                              name="promptCopy"
                              w={respDims(18)}
                              h={respDims(18)}
                              cursor="pointer"
                              color="#616266"
                              onClick={(e) => {
                                e.stopPropagation();
                                onCopyPrompt(prompt);
                              }}
                              pointerEvents="auto"
                            />
                          </Flex>
                        ) : (
                          <Flex mt="12px">
                            <SvgIcon
                              name="editLine"
                              w={respDims(18, 14)}
                              h={respDims(18, 14)}
                              ml={respDims(9)}
                              cursor="pointer"
                              color="#616266"
                              onClick={(e) => {
                                e.stopPropagation();
                                onEditPrompt(prompt);
                              }}
                              pointerEvents="auto"
                            />
                            <SvgIcon
                              name="trash"
                              color="#616266"
                              w={respDims(18, 14)}
                              h={respDims(18, 14)}
                              ml={respDims(9)}
                              cursor="pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                onRemovePrompt(prompt.id);
                              }}
                              pointerEvents="auto"
                            />
                          </Flex>
                        )}
                      </Flex>
                    </Flex>
                  </Box>
                ))}
              </Grid>
            </Box>
          </Box>
        ) : (
          <Flex p="60px 0" direction="column" alignItems="center">
            <Box color="#303133" fontSize="16px" fontWeight="500" mb="8px">
              暂无快捷指令
            </Box>
            {currentTab == '1' && (
              <Center>
                <Box color="#606266" fontSize="14px" fontWeight="400" mr="6px">
                  请点击
                </Box>
                <Box
                  color="primary.500"
                  fontSize="14px"
                  fontWeight="400"
                  onClick={() => onAddPrompt()}
                  cursor="pointer"
                >
                  添加快捷指令
                </Box>
              </Center>
            )}
          </Flex>
        )}

        {isOpenPromptModal && (
          <PromptModal
            prompt={modalPromptId!}
            onClose={onClosePromptModal}
            onSuccess={() => {
              refetchPrompts();
              if (acitonRef.current == 'copy') {
                onCurrentTab('1');
              }
            }}
          />
        )}

        {isOpenMobilePromptModal && (
          <MobilePromptModal
            prompt={modalPromptId!}
            isRoot={isRoot}
            onClose={onCloseMobilePromptModal}
            onSuccess={() => {
              if (acitonRef.current == 'copy') {
                onCurrentTab('1');
              }
              refetchPrompts();
            }}
          />
        )}

        {isOpenPromptSetting && (
          <PromptSetting
            onClose={() => {
              onClosePromptSetting();
            }}
            onSuccess={() => {
              refetchPrompts();
            }}
            appId={appId}
          />
        )}
      </Flex>
    </Box>
  );
};

export default React.memo(forwardRef(PromptList));
