import { ChatItemValueTypeEnum, ChatRoleEnum } from '@/fastgpt/global/core/chat/constants';
import Markdown from '../Markdown';
import { AIChatItemType, ChatSiteItemType } from '@/fastgpt/global/core/chat/type';

const ChatItemWordTemplate = ({ chatItem }: { chatItem: ChatSiteItemType }) => {
  return (
    <>
      {chatItem.value.map((subItem, subIndex) => {
        // Skip reasoning content
        if (subItem.type === ChatItemValueTypeEnum.reasoning) {
          return null;
        }

        if (subItem.type === ChatItemValueTypeEnum.text && subItem.text?.content) {
          return (
            <Markdown
              key={subIndex}
              obj={chatItem.obj}
              source={(() => {
                const text = subItem.text?.content;
                const quoteReg = /\[QUOTE SIGN\]\((.+)\)/g;
                const replaceText = text ? text.replace(quoteReg, `[QUOTE SIGN]($1)`) : '';
                return replaceText;
              })()}
            />
          );
        }
      })}
    </>
  );
};

export default ChatItemWordTemplate;
