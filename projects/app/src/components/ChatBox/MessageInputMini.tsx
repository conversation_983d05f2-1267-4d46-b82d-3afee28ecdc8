import { useSpeech } from '@/hooks/useSpeech';
import {
  Box,
  Center,
  ChakraProps,
  Flex,
  Grid,
  Image,
  Spinner,
  keyframes,
  useDisclosure,
  useToast,
  useUpdateEffect,
  Button,
  Text
} from '@chakra-ui/react';
import React, {
  useEffect,
  useCallback,
  useState,
  ForwardedRef,
  useImperativeHandle,
  forwardRef,
  useRef,
  useMemo,
  RefObject,
  Fragment,
  useContext
} from 'react';
import MyTooltip from '../MyTooltip';
import { useRouter } from 'next/router';
import { useSelectFile } from '@/hooks/useSelectFile';
import { customAlphabet } from 'nanoid';
import { respDims, rpxDim } from '@/utils/chakra';
import { useRequest } from '@/hooks/useRequest';
import { SvgIconNameType } from '../SvgIcon/data';
import SvgIcon from '../SvgIcon';
const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyz1234567890', 6);
import Lot<PERSON> from '../Lottie';
import PromptList, { PromptListRef } from './PromptList';
import InteractEditor, {
  EditorModeEnum,
  EditorRef,
  extractPlainText,
  getJsonFromText,
  getTextFromJson
} from '@/components/InteractEditor';
import { PromptExternalTypeEnum } from '@/constants/api/prompt';
import { tocWxIsTocTenant, tocWxNeedPay } from '@/api/auth';
import PayModal from './PayModal';
import { useSystemStore } from '@/store/useSystemStore';
import { useQuery } from '@tanstack/react-query';
import { InputTypeEnum } from '@/components/InteractEditor/nodes/InputNode';
import { AutoComplete, Input } from 'antd';
import elementResizeDetectorMaker from 'element-resize-detector';
import { downloadFile, uploadFile, uploadImage } from '@/utils/file';
import { formatFileSize } from '@/utils/tools';
import { IMG_BLOCK_KEY } from '@/fastgpt/global/core/chat/constants';
import AppList, { AppListRef } from './AppList';
import { APP_ICON } from '@/constants/common';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { ChatBoxMode } from './constant';
import { AppChatConfigType } from '@/fastgpt/global/core/app/type';
import { useChatStore } from '@/store/useChatStore';
import { setSystemTenantGuideFinish } from '@/api/app';
import { useAppStore } from '@/store/useAppStore';
import { Component } from '@/types/api/app';
import CustomTour from '@/components/CustomTour';
import { useDeepEditStore } from '@/store/useDeepEdit';
import MyMenu from '../MyMenu';
import Chooser, { ChooserFileType, ChooserModeEnum } from '@/pages/cloud/list/components/Chooser';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { imgFileType, imgMimeType } from '@/constants/cloud';
import { downloadFile as downloadFileBlob } from '@/api/file';
import { Toast } from '@/utils/ui/toast';
import MyBox from '../common/MyBox';
// import { CollapsedDimsMinScale, DimsMinScale } from '../constants';
import Toggle from '@/components/ToggleMini';
import { LayoutContext } from '@/components/LayoutProvider';
export enum UploadStatusEnum {
  waiting = 'waiting',
  uploading = 'uploading',
  success = 'success',
  error = 'error'
}

export type MessageFileType = {
  name?: string;
  fileKey?: string;
  fileUrl: string;
  fileContent?: string;
};

export type ImageType = {
  icon: string;
  src?: string;
  rawFile?: File;
  key: string;
  uploadStatus: UploadStatusEnum;
  sort: number;
} & MessageFileType;

export interface FileData {
  url: string;
  fileName: string;
  fileKey?: string;
  fileType: string;
  fileSize: number;
  isCallOcr?: boolean;
}

export type FileType = {
  svgIcon?: SvgIconNameType;
  imgIcon?: string;
  type: string;
  sizeText: string;
  rawFile: File;
  key: string;
  uploadStatus: UploadStatusEnum;
  percent: number;
  sort: number;
  isCallOcr?: boolean;
} & MessageFileType;

export type MessagePromptType = {
  id?: string;
  promptTitle?: string;
  description?: string;
  inputContent?: string;
  proContent?: string;
  hiddenContent?: string;
  externalType?: `${PromptExternalTypeEnum}`;
  workflowStepId?: string;
};

export type ChatAppType = {
  id: string;
  name: string;
  avatarUrl?: string;
  prompt?: MessagePromptType;
  workflowStepId?: string;
};

export type RawInputType = {
  inputVal?: string;
  images?: ImageType[];
  files?: FileType[];
  prompt?: MessagePromptType;
  chatApp?: ChatAppType;
};

export type SendMessageType = {
  ocrFileKey?: string;
  chatApp?: ChatAppType;
  inputVal: string;
  files?: MessageFileType[];
  images?: MessageFileType[];
  prompt?: MessagePromptType;
  rawInput: RawInputType | undefined;
  fileKeys?: string[];
};

export type MessageInputRef = {
  focus: () => void;
  addFile: (rawFiles: File[]) => void;
  setRawInput: (e: RawInputType) => void;
  addPrompt: (val: string) => void;
};

type PromptStateType = {
  mode: 'simple' | 'pro';
  backValue?: string;
};

type InnerMessageInputProps = {
  useVision?: boolean;
  useInternet?: boolean;

  editorRef: RefObject<EditorRef>;
  editorValue: string;
  setEditorValue: (value: string) => void;
  insertEditorValue: (value: string) => void;
  setRawInput: (rawInput?: RawInputType) => void;

  isSpeaking: boolean;
  isTransCription: boolean;
  startSpeak: (onFinish: (text: string) => void) => void;
  stopSpeak: () => void;
  mode: ChatBoxMode;
  promptListRef: RefObject<PromptListRef>;
  isOpenPromptList: boolean;
  onOpenPromptList: () => void;
  onSelectPrompt: (prompt: MessagePromptType) => boolean;
  onClosePromptList: () => void;

  prompt?: MessagePromptType;
  promptState?: PromptStateType;
  setPromptState: (state?: PromptStateType) => void;

  appListRef: RefObject<AppListRef>;
  isOpenAppList: boolean;
  onOpenAppList: () => void;
  onCloseAppList: () => void;

  chatApp?: ChatAppType;
  setChatApp: (app?: ChatAppType) => void;

  images: ImageType[];
  addImage: (files: File[]) => void;
  addImageUrl: (url: string[]) => void;
  removeImage: (image: ImageType) => void;

  files: FileType[];
  addFile: (files: File[]) => void;
  setFiles?: React.Dispatch<React.SetStateAction<FileType[]>>;
  removeFile: (file: FileType) => void;
  onFileSelect: (files: File[]) => void;

  unsendableReason: string;
  sendable: boolean;
  handleSend: () => Promise<void>;

  chatConfig?: AppChatConfigType;

  props: ChakraProps;
};

const rotate = keyframes`
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
`;

export const fileTypeInfos: { name: string; svgIcon?: SvgIconNameType; type: 'image' | 'file' }[] =
  [
    {
      name: 'jpg',
      type: 'image'
    },
    {
      name: 'jpeg',
      type: 'image'
    },
    {
      name: 'png',
      type: 'image'
    },
    {
      name: 'txt',
      svgIcon: 'fileTxt',
      type: 'file'
    },
    {
      name: 'sql',
      svgIcon: 'fileTxt',
      type: 'file'
    },
    {
      name: 'md',
      svgIcon: 'fileMd',
      type: 'file'
    },
    {
      name: 'pdf',
      svgIcon: 'filePdf',
      type: 'file'
    },
    {
      name: 'doc',
      svgIcon: 'fileDoc',
      type: 'file'
    },
    {
      name: 'docx',
      svgIcon: 'fileDoc',
      type: 'file'
    },
    {
      name: 'ppt',
      svgIcon: 'filePpt',
      type: 'file'
    },
    {
      name: 'pptx',
      svgIcon: 'filePpt',
      type: 'file'
    },
    {
      name: 'xls',
      svgIcon: 'fileXlsx',
      type: 'file'
    },
    {
      name: 'xlsx',
      svgIcon: 'fileXlsx',
      type: 'file'
    }
  ];

enum ViewType {
  Agent = 'Agent',
  Workflow = 'Workflow'
}

const fileAcceptText = fileTypeInfos.map((it) => `.${it.name}`).join(',');
const docFileAcceptText = fileTypeInfos
  .filter((it) => it.svgIcon)
  .map((it) => `.${it.name}`)
  .join(',');

const imageFileAcceptText = 'image/*';

const fileUploadLimit = 5;
const imageUploadLimit = 5;

const MessageInput = (
  {
    appId,
    chatId,
    onSendMessage,
    isChatting,
    mode,
    useVision = false,
    useInternet = false,
    chatConfig,
    setIsStartedChat = () => {},
    ...props
  }: {
    appId?: string;
    chatId?: string;
    onSendMessage: (e: SendMessageType) => Promise<void>;
    isChatting: boolean;
    useVision?: boolean;
    useInternet?: boolean;
    mode: ChatBoxMode;
    chatConfig?: AppChatConfigType;
    setIsStartedChat?: (bool: boolean) => void;
  } & ChakraProps,
  ref: ForwardedRef<MessageInputRef>
) => {
  const { shareId } = useRouter().query as { shareId?: string };

  const promptListRef = useRef<PromptListRef>(null);

  const appListRef = useRef<AppListRef>(null);

  const [editorValue, setEditorValue] = useState('');

  const editorRef = useRef<EditorRef>(null);

  const toast = useToast({ position: 'top' });

  const [images, setImages] = useState<ImageType[]>([]);

  const [files, setFiles] = useState<FileType[]>([]);

  const sortRef = useRef(0);

  const [prompt, setPrompt] = useState<MessagePromptType>();

  const [promptState, setPromptState] = useState<PromptStateType>();

  const checkPayRef = useRef<boolean>();

  const { isPc } = useSystemStore();

  const [chatApp, setChatApp] = useState<ChatAppType>();

  const { selectStep: workflowStep, setSelectStep: setWorkflowStep } = useWorkflowStore();

  const {
    isSpeaking,
    isTransCription,
    stopSpeak,
    startSpeak,
    speakingTimeString,
    renderAudioGraph,
    stream
  } = useSpeech({ shareId });

  const inputValue = useMemo(
    () => (promptState?.mode === 'pro' ? extractPlainText(editorValue, false) : editorValue),
    [editorValue, promptState?.mode]
  );

  const fileMaxCount = chatConfig?.fileSelectConfig?.maxFiles || 0;
  const fileCount = images.length + files.length;

  const filesStatus = useMemo(() => {
    let uploading = false;
    for (let file of files) {
      if (file.uploadStatus === UploadStatusEnum.error) {
        return UploadStatusEnum.error;
      }
      if (
        file.uploadStatus === UploadStatusEnum.waiting ||
        file.uploadStatus === UploadStatusEnum.uploading
      ) {
        uploading = true;
      }
    }
    return uploading ? UploadStatusEnum.uploading : undefined;
  }, [files]);

  const imagesStatus = useMemo(() => {
    let uploading = false;
    for (let image of images) {
      if (image.uploadStatus === UploadStatusEnum.error) {
        return UploadStatusEnum.error;
      }
      if (
        image.uploadStatus === UploadStatusEnum.waiting ||
        image.uploadStatus === UploadStatusEnum.uploading
      ) {
        uploading = true;
      }
    }
    return uploading ? UploadStatusEnum.uploading : undefined;
  }, [images]);

  const unsendableReason = useMemo(() => {
    if (filesStatus === UploadStatusEnum.uploading) {
      return '请等待文件上传完成';
    }
    if (filesStatus === UploadStatusEnum.error) {
      return '请先删除错误文件';
    }
    if (imagesStatus === UploadStatusEnum.uploading) {
      return '请等待图片上传完成';
    }
    if (imagesStatus === UploadStatusEnum.error) {
      return '请先删除错误图片';
    }
    if (promptState?.mode === 'pro' && editorValue && !inputValue) {
      return '存在内容为空的输入框';
    }
    if (!inputValue && !files.length && !images.length) {
      return '请输入内容';
    }
    if (!inputValue && (files.length || images.length)) {
      return '请输入内容';
    }
    if (isSpeaking) {
      return '请等待语音结束';
    }
    return '';
  }, [filesStatus, imagesStatus, promptState, editorValue, inputValue, files, images, isSpeaking]);

  const sendable = !unsendableReason;

  const {
    isOpen: isOpenPromptList,
    onOpen: onOpenPromptList,
    onClose: onClosePromptList
  } = useDisclosure();

  const {
    isOpen: isOpenPayModal,
    onOpen: onOpenPayModal,
    onClose: onClosePayModal
  } = useDisclosure();

  const { isOpen: isOpenAppList, onOpen: onOpenAppList, onClose: onCloseAppList } = useDisclosure();

  const { mutate: doUploadImage } = useRequest({
    mutationFn: async (image: ImageType) => {
      try {
        setImages((state) =>
          state.map((it) =>
            it.key === image.key ? { ...it, uploadStatus: UploadStatusEnum.uploading } : it
          )
        );
        const data = await uploadImage(
          image.rawFile!,
          {
            maxWidthOrHeight: 4329,
            maxSizeMB: 20
          },
          true
        );
        setImages((state) =>
          state.map((it) =>
            it.key === image.key
              ? {
                  ...it,
                  src: data.fileUrl,
                  fileUrl: data.fileUrl,
                  fileKey: data.fileKey,
                  uploadStatus: UploadStatusEnum.success
                }
              : it
          )
        );
      } catch (error) {
        setImages((state) => state.filter((it) => it.key !== image.key));
        return Promise.reject(error);
      }
    }
  });

  const addImage = useCallback(
    async (rawFiles: File[]) => {
      console.log(rawFiles);

      if (!rawFiles.length) {
        return;
      }
      const images = (
        await Promise.all(
          rawFiles.map(async (rawFile) => {
            const image = {
              name: rawFile.name,
              icon: '',
              rawFile,
              key: nanoid(),
              uploadStatus: UploadStatusEnum.waiting,
              sort: sortRef.current++,
              fileUrl: ''
            };
            return new Promise<ImageType | undefined>((resolve, reject) => {
              const reader = new FileReader();
              reader.readAsDataURL(rawFile);
              reader.onload = () => {
                image.icon = reader.result as string;
                resolve(image);
              };
              reader.onerror = () => {
                resolve(undefined);
              };
            });
          })
        )
      ).filter((it) => it) as ImageType[];
      setImages((state) => {
        const n = fileMaxCount - fileCount;
        if (n < images.length) {
          toast({
            title: `最多只能上传${fileMaxCount}个文件`,
            status: 'warning'
          });
          return n <= 0 ? state : [...state, ...images.slice(0, n)];
        } else {
          return [...state, ...images];
        }
      });
    },
    [toast, fileMaxCount, fileCount]
  );

  const addImageUrl = useCallback(
    (url: string[]) => {
      const images: ImageType[] = url.map((url) => ({
        name: '',
        icon: url,
        src: url,
        fileUrl: url,
        rawFile: undefined,
        key: nanoid(),
        uploadStatus: UploadStatusEnum.success,
        sort: sortRef.current++
      }));
      setImages((state) => [...state, ...images]);
    },
    [setImages]
  );

  const removeImage = useCallback((image: ImageType) => {
    setImages((state) => state.filter((it) => it.key !== image.key));
  }, []);

  useEffect(() => {
    const n = images.reduce(
      (n, it) => (it.uploadStatus === UploadStatusEnum.uploading ? n + 1 : n),
      0
    );
    if (n >= imageUploadLimit) {
      return;
    }
    const image = images.find((it) => it.uploadStatus === UploadStatusEnum.waiting);
    if (image) {
      doUploadImage(image);
    }
  }, [images, doUploadImage]);

  const { mutate: doUploadFile } = useRequest({
    mutationFn: async (file: FileType) => {
      try {
        setFiles((state) => {
          return state.map((it) =>
            it.key === file.key ? { ...it, uploadStatus: UploadStatusEnum.uploading } : it
          );
        });

        const res = await uploadFile(file.rawFile, {
          onProgress: (progress) => {
            setFiles((state) =>
              state.map((it) => (it.key === file.key ? { ...it, percent: progress } : it))
            );
          }
        });

        setFiles((state) =>
          state.map((it) =>
            it.key === file.key
              ? {
                  ...it,
                  id: res.id,
                  fileUrl: res.fileUrl,
                  fileKey: res.fileKey,
                  uploadStatus: UploadStatusEnum.success
                }
              : it
          )
        );
      } catch (error) {
        setFiles((state) =>
          state.map((it) =>
            it.key === file.key ? { ...it, uploadStatus: UploadStatusEnum.error } : it
          )
        );
        return Promise.reject(error);
      }
    }
  });

  const addFile = useCallback(
    async (rawFiles: File[]) => {
      if (!rawFiles.length) {
        return;
      }
      const files = (
        await Promise.all(
          rawFiles.map(async (rawFile) => {
            const type = rawFile.name
              .substring(rawFile.name.lastIndexOf('.') + 1)
              .toLocaleLowerCase();
            const info = fileTypeInfos.find((it) => it.name === type);
            if (!info) {
              return null;
            }
            const file: FileType = {
              name: rawFile.name,
              svgIcon: info.svgIcon,
              type,
              sizeText: formatFileSize(rawFile.size).replaceAll(' ', ''),
              uploadStatus: UploadStatusEnum.waiting,
              percent: 0,
              rawFile,
              key: nanoid(),
              sort: sortRef.current++,
              fileUrl: ''
            };
            return new Promise<FileType>((resolve, reject) => {
              if (!file.svgIcon) {
                const reader = new FileReader();
                reader.readAsDataURL(rawFile);
                reader.onload = () => {
                  file.imgIcon = reader.result as string;
                  resolve(file);
                };
                reader.onerror = () => {
                  resolve(file);
                };
              } else {
                resolve(file);
              }
            });
          })
        )
      ).filter((it) => it) as FileType[];
      setFiles((state) => {
        const n = fileMaxCount - fileCount;
        if (n < files.length) {
          toast({
            title: `最多只能上传${fileMaxCount}个文件`,
            status: 'warning'
          });
          return n <= 0 ? state : [...state, ...files.slice(0, n)];
        } else {
          return [...state, ...files];
        }
      });
    },
    [toast, fileMaxCount, fileCount]
  );

  const removeFile = (file: FileType) => {
    setFiles((state) => state.filter((it) => it.key !== file.key));
  };

  const onFileSelect = useCallback(
    async (rawFiles: File[]) => {
      if (!rawFiles?.length) {
        return;
      }
      console.log(rawFiles[0].type);

      addFile(rawFiles.filter((it) => !it.type.includes('image')));
      addImage(rawFiles.filter((it) => it.type.includes('image')));
    },
    [addFile, addImage]
  );

  useEffect(() => {
    const n = files.reduce(
      (n, it) => (it.uploadStatus === UploadStatusEnum.uploading ? n + 1 : n),
      0
    );
    if (n >= fileUploadLimit) {
      return;
    }
    const file = files.find((it) => it.uploadStatus === UploadStatusEnum.waiting);
    if (file) {
      doUploadFile(file);
    }
  }, [files, doUploadFile]);

  useQuery(['IsTocTenant'], () => tocWxIsTocTenant(), {
    onSuccess: (res) => {
      if (res.data && checkPayRef.current === undefined) {
        checkPayRef.current = true;
      }
    }
  });

  const handleSend = useCallback(async () => {
    console.log(112121);
    setIsStartedChat(true);
    if (unsendableReason) {
      toast({ title: unsendableReason, status: 'error' });
      return Promise.reject();
    }

    try {
      if (checkPayRef.current === true) {
        if (await tocWxNeedPay()) {
          onOpenPayModal();
          return Promise.reject();
        }
        checkPayRef.current = false;
      }
    } catch {}

    onSendMessage({
      chatApp: chatApp,
      inputVal: inputValue,
      files,
      prompt: prompt,
      images: images,
      rawInput: {
        inputVal: inputValue,
        images,
        files,
        prompt,
        chatApp
      }
    });

    onClosePromptList();
    onCloseAppList();
  }, [
    toast,
    unsendableReason,
    prompt,
    chatApp,
    inputValue,
    images,
    files,
    onSendMessage,
    onClosePromptList,
    onOpenPayModal,
    onCloseAppList
  ]);

  const insertEditorValue = useCallback((val?: string, insert?: boolean) => {
    if (insert) {
      val && editorRef.current?.insertText(val);
    } else {
      setEditorValue(val || '');
    }
  }, []);

  const setRawInput = useCallback(
    (rawInput?: RawInputType) => {
      setChatApp(() => rawInput?.chatApp);
      setImages(() => rawInput?.images || []);
      setFiles(() => rawInput?.files || []);
      setPrompt(() => rawInput?.prompt);
      if (!rawInput?.inputVal && rawInput?.prompt) {
        if (rawInput.prompt.proContent) {
          setPromptState({ mode: 'pro' });
          insertEditorValue(rawInput.prompt.proContent);
        } else {
          setPromptState({ mode: 'simple' });
          insertEditorValue(rawInput.prompt.inputContent);
        }
      } else {
        setPromptState(rawInput?.prompt ? { mode: 'simple' } : undefined);
        insertEditorValue(rawInput?.inputVal);
      }
    },
    [setImages, setFiles, insertEditorValue, setPromptState, setPrompt]
  );

  const addPrompt = (val: string) => {
    promptListRef.current?.addPrompt(val);
  };

  const onSelectPrompt = (pt: MessagePromptType) => {
    setRawInput({
      prompt: pt,
      chatApp: chatApp?.workflowStepId === pt?.workflowStepId ? chatApp : undefined
    });
    onClosePromptList();
    return true;
  };

  const onSetChatApp = (app?: ChatAppType) => {
    if (prompt?.workflowStepId) {
      setPrompt(undefined);
      setPromptState(undefined);
      setEditorValue((state) => extractPlainText(state, true));
    }

    if (app) {
      onCloseAppList();
      app.prompt && onSelectPrompt(app.prompt);
    }

    setChatApp(app);
  };

  useImperativeHandle(ref, () => ({
    focus: () => {
      editorRef.current?.focus();
    },
    addFile: onFileSelect,
    setRawInput,
    addPrompt
  }));

  useEffect(() => {
    !chatApp?.workflowStepId && !prompt?.workflowStepId && setWorkflowStep(null);
  }, [chatApp?.workflowStepId, prompt?.workflowStepId, setWorkflowStep]);

  useEffect(() => {
    if (!stream) {
      return;
    }
    const audioContext = new AudioContext();
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 4096;
    analyser.smoothingTimeConstant = 1;
    const source = audioContext.createMediaStreamSource(stream);
    source.connect(analyser);
  }, [renderAudioGraph, stream]);

  useEffect(() => {
    setRawInput();
    onClosePromptList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chatId]);

  useEffect(() => {
    promptListRef.current?.selectPrompt(prompt?.id);
  }, [prompt]);

  useEffect(() => {
    isOpenPromptList && onCloseAppList();
  }, [isOpenPromptList, onCloseAppList]);

  useEffect(() => {
    isOpenAppList && onClosePromptList();
  }, [isOpenAppList, onClosePromptList]);

  useUpdateEffect(() => {
    if (!workflowStep) {
      return;
    }
    const prompt = workflowStep.promptTitle
      ? {
          promptTitle: workflowStep.promptTitle,
          inputContent: workflowStep.inputContent,
          proContent: workflowStep.proContent,
          hiddenContent: workflowStep.hiddenContent,
          workflowStepId: workflowStep.id
        }
      : undefined;

    const app =
      workflowStep.tenantAppId && workflowStep.tenantAppId != '0'
        ? {
            id: workflowStep.tenantAppId,
            name: workflowStep.appName!,
            avatarUrl: workflowStep.appAvatarUrl,
            prompt,
            workflowStepId: workflowStep.id
          }
        : undefined;
    if (app) {
      onSetChatApp(app);
    } else if (prompt) {
      onSetChatApp(undefined);
      onSelectPrompt(prompt);
    }
  }, [workflowStep]);

  const getFileIcon = (fileExtension: string) => {
    const fileInfo = fileTypeInfos.find((info) => info.name === fileExtension.toLowerCase());
    return fileInfo?.svgIcon || 'defaultIcon'; // 如果找不到对应的图标，返回一个默认图标
  };

  useEffect(() => {
    setWorkflowStep(null);
  }, []);

  return (
    <>
      {isPc ? (
        <PcMessageInput
          mode={mode}
          useVision={useVision}
          useInternet={useInternet}
          editorRef={editorRef}
          editorValue={editorValue}
          setEditorValue={setEditorValue}
          insertEditorValue={insertEditorValue}
          setRawInput={setRawInput}
          isSpeaking={isSpeaking}
          isTransCription={isTransCription}
          startSpeak={startSpeak}
          stopSpeak={stopSpeak}
          promptListRef={promptListRef}
          isOpenPromptList={isOpenPromptList}
          onOpenPromptList={onOpenPromptList}
          onSelectPrompt={onSelectPrompt}
          onClosePromptList={onClosePromptList}
          prompt={prompt}
          promptState={promptState}
          setPromptState={setPromptState}
          appListRef={appListRef}
          isOpenAppList={isOpenAppList}
          onOpenAppList={onOpenAppList}
          onCloseAppList={onCloseAppList}
          chatApp={chatApp}
          setChatApp={onSetChatApp}
          images={images}
          addImage={addImage}
          addImageUrl={addImageUrl}
          removeImage={removeImage}
          files={files}
          addFile={addFile}
          setFiles={setFiles}
          removeFile={removeFile}
          onFileSelect={onFileSelect}
          unsendableReason={unsendableReason}
          sendable={sendable}
          handleSend={handleSend}
          chatConfig={chatConfig}
          props={props}
        />
      ) : (
        <MobileMessageInput
          mode={mode}
          useVision={useVision}
          useInternet={useInternet}
          editorRef={editorRef}
          editorValue={editorValue}
          setEditorValue={setEditorValue}
          insertEditorValue={insertEditorValue}
          setRawInput={setRawInput}
          isSpeaking={isSpeaking}
          isTransCription={isTransCription}
          startSpeak={startSpeak}
          stopSpeak={stopSpeak}
          promptListRef={promptListRef}
          isOpenPromptList={isOpenPromptList}
          onOpenPromptList={onOpenPromptList}
          onSelectPrompt={onSelectPrompt}
          onClosePromptList={onClosePromptList}
          prompt={prompt}
          promptState={promptState}
          setPromptState={setPromptState}
          appListRef={appListRef}
          isOpenAppList={isOpenAppList}
          onOpenAppList={onOpenAppList}
          onCloseAppList={onCloseAppList}
          chatApp={chatApp}
          setChatApp={onSetChatApp}
          images={images}
          addImage={addImage}
          addImageUrl={addImageUrl}
          removeImage={removeImage}
          files={files}
          addFile={addFile}
          removeFile={removeFile}
          onFileSelect={onFileSelect}
          unsendableReason={unsendableReason}
          sendable={sendable}
          handleSend={handleSend}
          chatConfig={chatConfig}
          props={props}
        />
      )}
      {isOpenPayModal && (
        <>
          <PayModal onClose={onClosePayModal} />
        </>
      )}
    </>
  );
};

export default React.memo(forwardRef(MessageInput));

const pcImageWidth = 80;
const pcImageHeight = 80;

const toolIconStyle = {
  w: respDims('24fpx'),
  h: respDims('24fpx'),
  cursor: 'pointer'
};

const shine = keyframes`
  0% {
    left: -130%;
  }
  100% {
    left: 130%;
  }
`;
const PcMessageInput = React.memo(function PcMessageInput({
  useVision,
  useInternet,

  editorRef,
  editorValue,
  setEditorValue,
  insertEditorValue,
  setRawInput,

  isSpeaking,
  isTransCription,
  startSpeak,
  stopSpeak,

  promptListRef,
  isOpenPromptList,
  onOpenPromptList,
  onSelectPrompt,
  onClosePromptList,

  prompt,
  promptState,
  setPromptState,

  appListRef,
  isOpenAppList,
  onOpenAppList,
  onCloseAppList,

  chatApp,
  setChatApp,

  images,
  addImage,
  addImageUrl,
  removeImage,

  files,
  addFile,
  setFiles,
  removeFile,
  onFileSelect,
  mode: chatBoxMode,
  unsendableReason,
  sendable,
  handleSend,

  chatConfig,

  props
}: InnerMessageInputProps) {
  const promptTitleRef = useRef<HTMLDivElement>(null);

  const promptToolbarRef = useRef<HTMLDivElement>(null);

  const toolbarRef = useRef<HTMLDivElement>(null);
  const { openOverlay } = useOverlayManager();

  const [toolbarExtraWidth, setToolbarExtraWidth] = useState(0);

  const forceToolbarNewline = useMemo(
    () => extractPlainText(editorValue, false).includes('\n'),
    [editorValue]
  );

  const { chatId } = useChatStore();

  const [isToolbarNewline, setIsToolbarNewline] = useState(false);

  const atDetector = useRef(new AtDetector(editorValue));

  const [atName, setAtName] = useState('');

  const [downloadLoading, setDownloadLoading] = useState(false);

  const promptFilterText = useMemo(
    () => (isOpenPromptList && editorValue[0] === '/' ? editorValue.substring(1) : undefined),
    [editorValue, isOpenPromptList]
  );

  const appFilterText = useMemo(
    () => (isOpenAppList ? atName.substring(1) : undefined),
    [isOpenAppList, atName]
  );

  const {
    canSelectFile = false,
    canSelectImg = false,
    maxFiles = 0
  } = chatConfig?.fileSelectConfig || {};

  const { File: FileSelect, onOpen: openFileSelect } = useSelectFile({
    fileType:
      canSelectFile && canSelectImg
        ? fileAcceptText
        : canSelectFile
          ? docFileAcceptText
          : imageFileAcceptText,
    multiple: true,
    maxSingleSize: 100 * 1024 * 1024
  });

  const openFileCloudSelect = () => {
    const accept: string[] = [];
    fileTypeInfos.forEach((item) => {
      if (canSelectFile && item.type == 'file') {
        accept.push(item.name);
      }
      if (canSelectImg && item.type == 'image') {
        accept.push(item.name);
      }
    });
    const currentFileLength = files.length + images.length;
    openOverlay({
      Overlay: Chooser,
      props: {
        // files: files as any, // 回显已选文件
        mode: ChooserModeEnum.Self,
        accept: accept,
        maxCount: maxFiles - currentFileLength,
        selectTips: `支持上传文件(最多${maxFiles}个,每个100MB)接受pdf、doc、xls、ppt、txt、md、图片、音频和视频等`,
        selectValidator({ selectFiles, selectedFiles }) {
          if (selectFiles.length > maxFiles - currentFileLength) {
            Toast.warning(
              `每轮对话资料最多不能超过${maxFiles}个，当前可以添加最多${maxFiles - currentFileLength}个文件`
            );
            return false;
          } else {
            return true;
          }
        },
        onSuccess(newFiles) {
          let downLoadPromises: Array<Promise<File>> = [];
          if (newFiles)
            newFiles.forEach((item) => {
              const extensionQuery = item.fileName?.split('.').pop()?.toLowerCase() || '';

              if (imgFileType.includes(extensionQuery)) {
                downLoadPromises.push(handleAddCloudImage(item, extensionQuery));
              } else {
                handleAddCloudFile(item, extensionQuery);
              }
            });
          setDownloadLoading(true);
          Promise.all(downLoadPromises)
            .then((res) => {
              res.forEach((item) => {
                addImage([item]);
              });
            })
            .finally(() => setDownloadLoading(false));
        }
      }
    });
  };

  const handleAddCloudImage = async (item: ChooserFileType, extensionQuery: string) => {
    const res = await downloadFileBlob(item.fileKey);

    const filename = item.fileName;
    const file = new File([res.data], filename, { type: imgMimeType[extensionQuery] });
    return file;
  };
  const handleAddCloudFile = (item: ChooserFileType, extensionQuery: string) => {
    const info = fileTypeInfos.find((it) => it.name === extensionQuery);

    const newFiles: FileType = {
      svgIcon: info?.svgIcon,
      type: extensionQuery!,
      sizeText: formatFileSize(item.fileSize!).replaceAll(' ', ''),
      rawFile: new File([], 'template'),
      key: nanoid(),
      uploadStatus: UploadStatusEnum.success,
      percent: 100,
      sort: 1,
      name: item.fileName,
      fileKey: item.fileKey,
      fileUrl: item.fileUrl!
    };
    setFiles?.((state) => {
      return [...state, newFiles];
    });
  };

  const onEditorValueChange = (val: string) => {
    if (chatBoxMode === ChatBoxMode.Chat) {
      const newAtName = atDetector.current.update(val);
      setAtName(newAtName);
      if (newAtName) {
        !isOpenAppList && onOpenAppList();
      } else {
        isOpenAppList && onCloseAppList();
      }
    }

    if (val.includes(IMG_BLOCK_KEY)) {
      const images: string[] = [];
      val = val
        .replaceAll(
          new RegExp(`\`\`\`(${IMG_BLOCK_KEY})\\n([\\s\\S]*?)\`\`\``, 'g'),
          (m, g1, g2: string) => {
            g2.split('\n').forEach((it) => {
              try {
                const json = JSON.parse(it);
                if (json.src) {
                  images.push(json.src);
                }
              } catch {}
            });
            return '';
          }
        )
        .trim();

      addImageUrl(images);
    }
    setEditorValue(val);
  };

  useEffect(() => {
    const toolbarDom = toolbarRef.current;
    const editorDom = editorRef.current?.getDom();
    if (!toolbarDom || !editorDom) {
      setIsToolbarNewline(false);
      return;
    }
    const detector = elementResizeDetectorMaker();
    detector.listenTo(editorDom, () => {
      const toolbarNewline =
        toolbarDom.offsetTop > editorDom.offsetTop + editorDom.offsetHeight / 2;
      setIsToolbarNewline(toolbarNewline);
      const promptStubWidth =
        (promptTitleRef.current?.offsetWidth || 0) + (promptToolbarRef.current?.offsetWidth || 0);
      setToolbarExtraWidth(toolbarNewline && promptStubWidth ? promptStubWidth + 0.5 : 0);
    });
    return () => detector.uninstall(editorDom);
  }, [editorRef]);

  const editorPrefix = useMemo(
    () =>
      prompt?.promptTitle && (
        <Flex
          display="inline-flex"
          h="28px"
          fontSize="14px"
          lineHeight="28px"
          alignItems="center"
          bgColor="#FFFFFF"
          userSelect="none"
        >
          <Flex
            ref={promptTitleRef}
            display="inline-flex"
            h="28px"
            fontSize="14px"
            lineHeight="28px"
            alignItems="center"
            bgColor="#FFFFFF"
            userSelect="none"
          >
            <Box
              mr="8px"
              px="8px"
              borderRadius="4px"
              bg="linear-gradient( 90deg, rgba(235,253,255,0.85) 0%, rgba(236,240,255,0.76) 100%)"
            >
              <Box
                bg="linear-gradient(228.23395011634184deg, #1A5EFF 0%, #2194FF 100%)"
                bgClip="text"
                bgSize="100% 100%"
                maxW="12em"
                whiteSpace="nowrap"
                overflow="hidden"
                textOverflow="ellipsis"
              >
                {prompt.promptTitle}
              </Box>
            </Box>
          </Flex>
        </Flex>
      ),
    [prompt?.promptTitle]
  );

  const { isPc } = useSystemStore();

  return (
    <Box maxW="768px" w="100%">
      <MyBox
        isLoading={downloadLoading}
        bgColor="#F3F4F6"
        boxShadow="0px 0px 18px -4px rgba(0,0,0,0.08)"
        borderRadius={respDims('16fpx')}
        {...props}
        p="1px"
        _focusWithin={{
          bgImage: 'linear-gradient( 270deg, #774CFF 0%, #AC51FF 100%)'
        }}
        position="relative"
        tabIndex={0}
        onClick={() => editorRef.current?.focus()}
      >
        <Box
          borderRadius={respDims('15fpx')}
          bgColor="#ffffff"
          onKeyDownCapture={(e) => {
            if (!isOpenAppList || !['ArrowDown', 'ArrowUp', 'Enter'].includes(e.key)) {
              return;
            }
            appListRef.current?.onKeyEvent(e.key);
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <Box bgColor="#ffffff" borderRadius={respDims('16fpx')} p="10px 17px 10px 14px">
            {/* 已上传文件 */}
            {!!images.length && (
              <Flex
                flexWrap="wrap"
                maxH={(respDims((pcImageHeight + 12) * 2), (pcImageHeight + 8) * 2)}
                overflowX="hidden"
                overflowY="auto"
              >
                {images.map((image) => (
                  <ImageItem key={image.key} isPc={true} image={image} removeImage={removeImage} />
                ))}
              </Flex>
            )}

            <Flex flexWrap="wrap" pos="relative" flexDirection="column" w="100%">
              <InteractEditor
                w="100%"
                whiteSpace="normal"
                ref={editorRef}
                flex="1 1 auto"
                mode={promptState?.mode === 'pro' ? EditorModeEnum.interact : EditorModeEnum.plain}
                prefix={editorPrefix}
                p="0"
                {...(forceToolbarNewline && { w: '100%' })}
                border="none"
                boxShadow="none !important"
                placeholder="Enter发送，Shift+Enter换行"
                height="56px"
                maxHeight="140px"
                fontSize="14px"
                lineHeight="28px"
                value={editorValue}
                onChange={onEditorValueChange}
                onKeyDownCapture={(e) => {
                  if (e.key === 'Enter' && !e.altKey && !e.ctrlKey && !e.shiftKey) {
                    if (sendable) {
                      handleSend();
                    }
                    e.stopPropagation();
                    e.preventDefault();
                  }
                }}
                onPaste={(e) => {
                  const clipboardData = e.clipboardData;
                  if (clipboardData && canSelectImg) {
                    const items = clipboardData.items;
                    const files = Array.from(items)
                      .map((item) => (item.kind === 'file' ? item.getAsFile() : undefined))
                      .filter(Boolean) as File[];
                    addImage(files);
                  }
                }}
                mb="6px"
              />

              {/* 工具栏 */}
              <Flex
                ref={toolbarRef}
                mt={isToolbarNewline ? respDims(10) : 0}
                ml="auto"
                h={respDims('32fpx')}
                flexShrink="0"
                alignSelf="flex-end"
                alignItems="center"
                bgColor="#FFFFFF"
                zIndex="2"
              >
                {toolbarExtraWidth > 0 && <Box w={`${toolbarExtraWidth}px`} />}

                {prompt && (
                  <Flex
                    ref={promptToolbarRef}
                    h={respDims('32fpx')}
                    alignItems="center"
                    {...(isToolbarNewline
                      ? {
                          pos: 'absolute',
                          left: '0',
                          bottom: '0'
                        }
                      : {})}
                  >
                    {!!prompt.proContent && (
                      <Center
                        mr={respDims(14)}
                        w={respDims(59, (20 / 24) * 59)}
                        h={respDims(24, 20)}
                        borderRadius={respDims(12, 10)}
                        color="#909399"
                        bgColor="rgba(243,244,246,0.6)"
                        border="1px solid #ECECEC"
                        fontSize={respDims('14fpx')}
                        cursor="pointer"
                        userSelect="none"
                        _hover={{
                          bgColor: '#EEEEEE'
                        }}
                        onClick={() => {
                          if (promptState?.mode === 'pro') {
                            setEditorValue(promptState?.backValue || prompt.inputContent || '');
                            setPromptState({ mode: 'simple', backValue: editorValue });
                          } else {
                            setEditorValue(promptState?.backValue || prompt.proContent || '');
                            setPromptState({ mode: 'pro', backValue: editorValue });
                          }
                        }}
                      >
                        <SvgIcon
                          name="transferUpDown"
                          w={respDims('14fpx')}
                          h={respDims('14fpx')}
                        />
                        <Box ml={respDims(3)}>{promptState?.mode === 'pro' ? '简' : '专'}</Box>
                      </Center>
                    )}

                    <Box
                      mr={respDims(16)}
                      color="#606266"
                      fontSize={respDims('14fpx')}
                      cursor="pointer"
                      userSelect="none"
                      onClick={() => {
                        setRawInput();
                      }}
                    >
                      清空
                    </Box>
                  </Flex>
                )}

                {/* 文件上传 */}
                {(canSelectImg || canSelectFile) && (
                  <MyMenu
                    menuListStyles={{
                      mb: respDims(8)
                    }}
                    menuList={[
                      {
                        label: <Box fontWeight="500">从数据空间选择</Box>,
                        icon: <SvgIcon name="box" {...toolIconStyle} />,
                        onClick: () => openFileCloudSelect()
                      },
                      {
                        label: (
                          <MyTooltip
                            label={
                              canSelectImg && canSelectFile
                                ? `支持上传文件 (最多${maxFiles}个,每个100MB) 接受pdf、doc、xls、ppt、txt、md、图片等`
                                : canSelectImg
                                  ? `支持上传图片 (最多${maxFiles}个)`
                                  : `支持上传文档 (最多${maxFiles}个,每个100MB) 接受pdf、doc、xls、ppt、txt、md等`
                            }
                          >
                            <Flex alignItems="center" justifyContent="center">
                              <Box fontWeight="500">从本地上传</Box>
                            </Flex>
                          </MyTooltip>
                        ),
                        icon: <SvgIcon name="folder2" {...toolIconStyle} />,
                        onClick: () => openFileSelect()
                      }
                    ]}
                    Button={
                      <>
                        <Box
                          style={{
                            cursor: 'pointer',
                            transition: 'background-color 0.3s',
                            padding: '8px',
                            borderRadius: '8px', // 设置圆角
                            width: '32px', // 确保背景不超过 32px
                            height: '32px', // 确保背景不超过 32px
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                          ml={respDims(16)}
                          position="relative"
                          // onClick={openFileSelect}
                          onMouseEnter={(e) =>
                            (e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.1)')
                          }
                          onMouseLeave={(e) =>
                            (e.currentTarget.style.backgroundColor = 'transparent')
                          }
                        >
                          <SvgIcon
                            mr={respDims(chatBoxMode == ChatBoxMode.Test ? 16 : 0)}
                            name="chatUpload"
                            {...toolIconStyle}
                          />
                        </Box>
                        <FileSelect onSelect={onFileSelect} />
                      </>
                    }
                  ></MyMenu>
                )}

                {chatBoxMode == ChatBoxMode.Chat && (
                  <>
                    {useInternet && (
                      <MyTooltip label="联网">
                        <SvgIcon name="chatNetwork2" ml={respDims(16)} {...toolIconStyle} />
                      </MyTooltip>
                    )}

                    <MyTooltip
                      ml={respDims(16)}
                      label={isTransCription ? '语音解析中' : isSpeaking ? '结束语音' : '语音输入'}
                    >
                      <Flex
                        borderRadius={rpxDim(8)}
                        ml="12px"
                        style={{ position: 'relative' }} // 确保父容器相对定位
                      >
                        {isTransCription ? (
                          <Center ml={respDims(16)}>
                            <Box color="#1A5EFF" fontSize={respDims('14fpx')}>
                              解析中...
                            </Box>
                            <Lottie
                              ml={respDims(8)}
                              name="processing"
                              w={respDims('24fpx')}
                              h={respDims('24fpx')}
                            />
                          </Center>
                        ) : isSpeaking ? (
                          <Center
                            ml={respDims(16)}
                            cursor="pointer"
                            onClick={() => stopSpeak()}
                            style={{
                              cursor: 'pointer',
                              transition: 'background-color 0.3s',
                              padding: '8px',
                              borderRadius: '8px', // 设置圆角
                              width: '32px', // 确保背景不超过 32px
                              height: '32px', // 确保背景不超过 32px
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                            onMouseEnter={(e) =>
                              (e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.1)')
                            }
                            onMouseLeave={(e) =>
                              (e.currentTarget.style.backgroundColor = 'transparent')
                            }
                          >
                            <Lottie
                              name="waveform"
                              w={respDims(`${700 / (300 / 24)}fpx`)}
                              h={respDims('24fpx')}
                            />
                            <SvgIcon name="stop" {...toolIconStyle} color="#1A5EFF" />
                          </Center>
                        ) : (
                          <Box
                            style={{
                              cursor: 'pointer',
                              transition: 'background-color 0.3s',
                              borderRadius: '8px', // 设置圆角
                              padding: '4px', // 增加点击区域
                              width: '32px', // 确保背景不超过 32px
                              height: '32px', // 确保背景不超过 32px
                              margin: 0
                              // display: 'flex',
                              // alignItems: 'center',
                              // justifyContent: 'center',
                              // textAlign: 'center'
                            }}
                            onClick={() => startSpeak((val) => insertEditorValue(val))}
                            onMouseEnter={(e) =>
                              (e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.1)')
                            }
                            onMouseLeave={(e) =>
                              (e.currentTarget.style.backgroundColor = 'transparent')
                            }
                          >
                            <SvgIcon name="microphone" {...toolIconStyle} />
                          </Box>
                        )}
                      </Flex>
                    </MyTooltip>

                    <Box mx={respDims(16)} w="1px" h={respDims(18)} bgColor="#D1D5DB" />
                  </>
                )}

                <MyTooltip label={unsendableReason || '发送'}>
                  <SvgIcon
                    name="chatSend"
                    flexShrink="0"
                    w={respDims('32fpx')}
                    h={respDims('32fpx')}
                    color={sendable ? 'primary.500' : '#C0C4CC'}
                    borderRadius="50%"
                    cursor="pointer"
                    onClick={() => sendable && handleSend()}
                  />
                </MyTooltip>
              </Flex>
            </Flex>

            {!!files.length && (
              <>
                <Box w="100%" h="1px" mt={respDims(16)} bgColor="#E7E7E7" />
                {/* 上传文件列表 */}
                <Grid
                  gridTemplateColumns={['1fr']}
                  maxH={respDims(176)}
                  overflowX="hidden"
                  overflowY="auto"
                >
                  {files.map((file) => (
                    <FileItem key={file.key} isPc={true} file={file} removeFile={removeFile} />
                  ))}
                </Grid>
              </>
            )}
          </Box>
        </Box>
      </MyBox>
    </Box>
  );
});

const MobileMessageInput = React.memo(function MobileMessageInput({
  useVision,
  useInternet,

  editorRef,
  editorValue,
  setEditorValue,
  insertEditorValue,
  setRawInput,

  isSpeaking,
  isTransCription,
  startSpeak,
  stopSpeak,

  promptListRef,
  isOpenPromptList,
  onOpenPromptList,
  onSelectPrompt,
  onClosePromptList,

  prompt,
  promptState,
  setPromptState,

  images,
  addImage,
  removeImage,

  files,
  addFile,
  removeFile,
  onFileSelect,

  unsendableReason,
  sendable,
  handleSend,

  chatConfig,

  props
}: InnerMessageInputProps) {
  const refRef = useRef<HTMLDivElement>(null);

  const [mode, setMode] = useState<'normal' | 'zoom' | 'prompt'>('normal');

  const [lineCount, setLineCount] = useState(1);

  const [showFileSelect, setShowFileSelect] = useState(false);

  const editorJson = useMemo(
    () => (promptState?.mode === 'pro' && editorValue ? getJsonFromText(editorValue) : undefined),
    [editorValue, promptState?.mode]
  );

  const { File: CameraFileSelect, onOpen: openCameraFileSelect } = useSelectFile({
    fileType: imageFileAcceptText,
    capture: 'user',
    multiple: false,
    maxSingleSize: 100 * 1024 * 1024
  });

  const { File: ImageFileSelect, onOpen: openImageFileSelect } = useSelectFile({
    fileType: imageFileAcceptText,
    multiple: true,
    maxSingleSize: 100 * 1024 * 1024
  });

  const { File: DocFileSelect, onOpen: openDocFileSelect } = useSelectFile({
    fileType: docFileAcceptText,
    multiple: true,
    maxSingleSize: 100 * 1024 * 1024
  });

  const uploads: { image?: ImageType; file?: FileType; sort: number }[] = useMemo(
    () =>
      [
        ...images.map((image) => ({ image, sort: image.sort })),
        ...files.map((file) => ({ file, sort: file.sort }))
      ].sort((l, r) => l.sort - r.sort),
    [images, files]
  );

  const upudateLineCount = useCallback(() => {
    const refDom = refRef.current;
    const editorDom = editorRef.current?.getDom();
    if (!refDom || !editorDom) {
      setLineCount(1);
      return;
    }
    setLineCount(Math.ceil(editorDom.scrollHeight / refDom.offsetHeight));
  }, [editorRef]);

  const onPromptInputChange = (index: number, text: string) => {
    if (!editorJson) {
      return;
    }
    const newJson = editorJson.map((it, idx) =>
      idx === index
        ? {
            ...it,
            payload: it.payload && { ...it.payload, text }
          }
        : it
    );
    const newText = getTextFromJson(newJson);
    setEditorValue(newText);
  };

  const onSend = () => {
    handleSend().then(() => {
      setMode('normal');
      setShowFileSelect(false);
    });
  };

  useEffect(
    () => setMode((state) => (prompt ? 'prompt' : state === 'prompt' ? 'normal' : state)),
    [prompt]
  );

  useEffect(() => {
    const editorDom = editorRef.current?.getDom();
    if (mode !== 'normal' || !editorDom) {
      return;
    }
    const detector = elementResizeDetectorMaker();
    detector.listenTo(editorDom, () => {
      upudateLineCount();
    });
    return () => detector.uninstall(editorDom);
  }, [mode, editorRef, upudateLineCount]);

  const editorProps = {
    ref: editorRef,
    mode: EditorModeEnum.plain,
    border: 'none',
    placeholder: '你想问什么？',
    boxShadow: 'none !important',
    fontSize: rpxDim(24),
    lineHeight: rpxDim(40),
    value: editorValue,
    onChange: (val: string) => setEditorValue(val),
    onFocus: () => setShowFileSelect(false)
  };

  return (
    <>
      {mode !== 'normal' && (
        <Box pos="fixed" left="0" top="0" right="0" bottom="0" bgColor="rgba(0,0,0,0.6)" />
      )}
      <Flex
        pos="relative"
        flexDir="column"
        py={rpxDim(28)}
        bgColor="#FFFFFF"
        {...props}
        {...(mode !== 'normal' && {
          pos: 'fixed',
          left: '0',
          top: rpxDim(160),
          right: '0',
          bottom: '0',
          bgColor: '#FFFFFF',
          borderTopLeftRadius: rpxDim(40),
          borderTopRightRadius: rpxDim(40)
        })}
      >
        {/* 常规模式显示打开快捷指令按钮 */}
        {mode === 'normal' && (
          <Center
            pos="absolute"
            top={rpxDim(-108)}
            right={rpxDim(32)}
            w={rpxDim(80)}
            h={rpxDim(80)}
            bgColor="#FFFFFF"
            borderRadius="50%"
            boxShadow="0 4px 32px 0 rgba(125,128,143,0.2)"
            cursor="pointer"
            onClick={onOpenPromptList}
          >
            <SvgIcon name="chatMagicWand2" w={rpxDim(44)} h={rpxDim(44)} />
          </Center>
        )}

        {/* 大屏缩小按钮 */}
        {mode === 'zoom' && (
          <SvgIcon
            flexShrink="0"
            ml="auto"
            mr={rpxDim(32)}
            mb={rpxDim(20)}
            name="exitFullscreen"
            w={rpxDim(48)}
            h={rpxDim(48)}
            cursor="pointer"
            onClick={() => setMode('normal')}
          />
        )}

        {/* 快捷指令输入标题 */}
        {mode === 'prompt' && (
          <>
            <Center mx={rpxDim(32)} pos="relative">
              <SvgIcon name="inspiration" w={rpxDim(44)} h={rpxDim(44)} />
              <Box
                display="inline-block"
                ml={rpxDim(8)}
                px={rpxDim(16)}
                py={rpxDim(4)}
                maxW={rpxDim(280)}
                whiteSpace="nowrap"
                overflow="auto"
                fontSize={rpxDim(28)}
                lineHeight={rpxDim(40)}
                borderRadius={rpxDim(8)}
                bg="linear-gradient( 90deg, rgba(235,253,255,0.85) 0%, rgba(236,240,255,0.76) 100%)"
              >
                <Box
                  display="inline-block"
                  bg="linear-gradient(228.23395011634184deg, #1A5EFF 0%, #2194FF 100%)"
                  bgClip="text"
                  bgSize="100% 100%"
                >
                  {prompt?.promptTitle}
                </Box>
              </Box>

              <SvgIcon
                pos="absolute"
                left="0"
                top="0"
                bottom="0"
                my="auto"
                name="chevronLeft"
                w={rpxDim(48)}
                h={rpxDim(48)}
                cursor="pointer"
                onClick={() => {
                  setRawInput();
                  onOpenPromptList();
                }}
              />

              {!!prompt?.proContent && (
                <Center
                  pos="absolute"
                  top="0"
                  right="0"
                  bottom="0"
                  my="auto"
                  w={rpxDim(118)}
                  h={rpxDim(48)}
                  borderRadius={rpxDim(458)}
                  color="#909399"
                  bgColor="rgba(243,244,246,0.6)"
                  border="1px solid #ECECEC"
                  fontSize={rpxDim(25)}
                  cursor="pointer"
                  userSelect="none"
                  _hover={{
                    bgColor: '#EEEEEE'
                  }}
                  onClick={() => {
                    if (promptState?.mode === 'pro') {
                      setEditorValue(promptState?.backValue || prompt.inputContent || '');
                      setPromptState({ mode: 'simple', backValue: editorValue });
                    } else {
                      setEditorValue(promptState?.backValue || prompt.proContent || '');
                      setPromptState({ mode: 'pro', backValue: editorValue });
                    }
                  }}
                >
                  <SvgIcon name="transferUpDown" w={rpxDim(28)} h={rpxDim(28)} />
                  <Box ml={rpxDim(8)}>{promptState?.mode === 'pro' ? '简' : '专'}</Box>
                </Center>
              )}
            </Center>
            <Box my={rpxDim(24)} w="100%" h="1px" bgColor="#F3F4F6" />

            {/* 专业版快捷指令编辑*/}
            {promptState?.mode === 'pro' && !!editorJson?.length && (
              <Box px={rpxDim(32)} overflow="auto">
                {editorJson.map((json, index) => (
                  <Fragment key={index}>
                    {json.text && (
                      <Box
                        mt={index > 0 ? rpxDim(28) : 0}
                        fontSize={rpxDim(28)}
                        lineHeight={rpxDim(48)}
                        color="#1F2937"
                        whiteSpace="pre-wrap"
                      >
                        {json.text}
                      </Box>
                    )}

                    {json.payload?.type === InputTypeEnum.text && (
                      <Input
                        placeholder={json.payload.placeholder}
                        value={json.payload.text || ''}
                        style={{
                          width: '100%',
                          marginTop: index > 0 ? rpxDim(14) : 0,
                          padding: `${rpxDim(14)} ${rpxDim(32)}`,
                          backgroundColor: '#F9F9F9',
                          fontSize: rpxDim(28),
                          lineHeight: rpxDim(44),
                          borderRadius: rpxDim(16),
                          border: 'none'
                        }}
                        onChange={(e) => onPromptInputChange(index, e.target.value)}
                      />
                    )}

                    {json.payload?.type === InputTypeEnum.select && (
                      <AutoComplete
                        value={json.payload.text || ''}
                        options={json.payload.options?.map((it) => ({ value: it }))}
                        onChange={(value) => onPromptInputChange(index, value)}
                        onSelect={(value) => onPromptInputChange(index, value)}
                        style={{
                          width: '100%'
                        }}
                      >
                        <Input
                          placeholder={json.payload.placeholder || ''}
                          style={{
                            width: '100%',
                            marginTop: index > 0 ? rpxDim(14) : 0,
                            padding: `${rpxDim(14)} ${rpxDim(32)}`,
                            backgroundColor: '#F9F9F9',
                            fontSize: rpxDim(28),
                            lineHeight: rpxDim(44),
                            borderRadius: rpxDim(16),
                            border: 'none'
                          }}
                        />
                      </AutoComplete>
                    )}
                  </Fragment>
                ))}
              </Box>
            )}

            {/* 简版快捷指令编辑 */}
            {promptState?.mode === 'simple' && (
              <InteractEditor
                {...editorProps}
                minH={rpxDim(266)}
                mx={rpxDim(32)}
                px={rpxDim(32)}
                py={rpxDim(8)}
                bgColor="#F9F9F9"
              />
            )}

            <Box
              m={rpxDim(32)}
              color="#909399"
              fontSize={rpxDim(28)}
              lineHeight={rpxDim(40)}
              cursor="pointer"
              userSelect="none"
              onClick={() => setRawInput()}
            >
              清空
            </Box>
          </>
        )}

        {/* 大屏编辑 */}
        {mode === 'zoom' && (
          <InteractEditor {...editorProps} flex="1" px={rpxDim(32)} py="0" mb={rpxDim(24)} />
        )}

        {/* 上传文件、图片列表 */}
        {uploads.length > 0 && (
          <>
            <Box
              mx={rpxDim(32)}
              pb={rpxDim(10)}
              whiteSpace="nowrap"
              // overflowX="auto"
            >
              {uploads.map((it) =>
                it.image ? (
                  <ImageItem
                    key={it.image.key}
                    isPc={false}
                    image={it.image}
                    removeImage={removeImage}
                  />
                ) : (
                  it.file && (
                    <FileItem
                      key={it.file.key}
                      isPc={false}
                      file={it.file}
                      removeFile={removeFile}
                    />
                  )
                )
              )}
            </Box>
            <Box mb={rpxDim(24)} w="100%" h="1px" bgColor="#F2F8FF" />
          </>
        )}

        {/* 常规模式编辑 */}
        {mode === 'normal' && (
          <Flex flex="1" w="100%" px={rpxDim(32)} alignItems="flex-end" overflow="hidden">
            <SvgIcon
              name={showFileSelect ? 'chatFillPlus' : 'chatFillPlus'}
              w={rpxDim(80)}
              h={rpxDim(80)}
              flexShrink="0"
              borderRadius="50%"
              cursor="pointer"
              onClick={() => setShowFileSelect((state) => !state)}
            />

            {/* 用这个参照元素的高度判断编辑框是否多行 */}
            <Box ref={refRef} h={editorProps.lineHeight} w="0" />

            <Flex
              alignSelf="stretch"
              flex="1"
              alignItems="center"
              mx={rpxDim(20)}
              px={rpxDim(24)}
              py={rpxDim(16)}
              bgColor={'#F3F6F6'}
              borderRadius={rpxDim(lineCount > 1 ? 8 : 264)}
            >
              <InteractEditor {...editorProps} flex="1" p="0" maxH={rpxDim(40 * 3)} />

              {!editorValue && !isSpeaking && (
                <SvgIcon
                  name="microphone"
                  w={rpxDim(48)}
                  h={rpxDim(48)}
                  cursor="pointer"
                  onClick={() => {
                    startSpeak((val) => insertEditorValue(val));
                    setShowFileSelect(false);
                  }}
                />
              )}

              {isSpeaking && !isTransCription && (
                <SvgIcon
                  name="stop"
                  w={rpxDim(48)}
                  h={rpxDim(48)}
                  color="primary.500"
                  cursor="pointer"
                  onClick={() => stopSpeak()}
                />
              )}
            </Flex>

            <Flex
              ml="auto"
              minH="100%"
              flexDir="column"
              alignItems="center"
              justifyContent="space-between"
            >
              {lineCount > 2 && (
                <SvgIcon
                  name="fullscreen"
                  w={rpxDim(48)}
                  h={rpxDim(48)}
                  mb={rpxDim(30)}
                  cursor="pointer"
                  onClick={() => setMode('zoom')}
                />
              )}

              <SvgIcon
                mt="auto"
                flexShrink="0"
                name="chatSend"
                w={rpxDim(80)}
                h={rpxDim(80)}
                color="primary.500"
                borderRadius="50%"
                cursor="pointer"
                onClick={onSend}
              />
            </Flex>
          </Flex>
        )}

        {/* 非常规时显示发送 */}
        {mode !== 'normal' && (
          <Flex
            mt="auto"
            px={rpxDim(32)}
            flexShrink="0"
            alignItems="center"
            justifyContent="space-between"
          >
            <SvgIcon
              name={showFileSelect ? 'chatFillPlus' : 'chatFillPlus'}
              w={rpxDim(80)}
              h={rpxDim(80)}
              flexShrink="0"
              borderRadius="50%"
              cursor="pointer"
              onClick={() => setShowFileSelect((state) => !state)}
            />

            <SvgIcon
              flexShrink="0"
              name="chatSend"
              w={rpxDim(80)}
              h={rpxDim(80)}
              color="primary.500"
              borderRadius="50%"
              cursor="pointer"
              onClick={onSend}
            />
          </Flex>
        )}

        {/*上传按钮 */}
        {showFileSelect && (
          <Flex px={rpxDim(32)} mt={rpxDim(40)}>
            <Center
              flexDir="column"
              px={rpxDim(32)}
              cursor="pointer"
              onClick={openCameraFileSelect}
            >
              <SvgIcon name="chatFillCamera" w={rpxDim(72)} h={rpxDim(72)} />
              <Box mt={rpxDim(16)} fontSize={rpxDim(28)} lineHeight={rpxDim(39)}>
                相机
              </Box>
            </Center>

            <Center flexDir="column" px={rpxDim(32)} cursor="pointer" onClick={openImageFileSelect}>
              <SvgIcon name="chatFillAlbum" w={rpxDim(72)} h={rpxDim(72)} />
              <Box mt={rpxDim(16)} fontSize={rpxDim(28)} lineHeight={rpxDim(39)}>
                图库
              </Box>
            </Center>

            <Center flexDir="column" px={rpxDim(32)} cursor="pointer" onClick={openDocFileSelect}>
              <SvgIcon name="chatFillFile" w={rpxDim(72)} h={rpxDim(72)} />
              <Box mt={rpxDim(16)} fontSize={rpxDim(28)} lineHeight={rpxDim(39)}>
                文件
              </Box>
            </Center>
          </Flex>
        )}

        {/* 录音状态 */}
        {isSpeaking && (
          <Lottie
            mt={rpxDim(28)}
            alignSelf="center"
            name="waveform2"
            w={rpxDim(486)}
            h={rpxDim(92)}
          />
        )}
      </Flex>

      <PromptList
        ref={promptListRef}
        isOpen={isOpenPromptList}
        pos="fixed"
        bottom="0"
        left="0"
        right="0"
        w="100%"
        maxH="80vh"
        onSelectPrompt={onSelectPrompt}
        onClose={onClosePromptList}
      />

      <CameraFileSelect onSelect={onFileSelect} />
      <ImageFileSelect onSelect={onFileSelect} />
      <DocFileSelect onSelect={onFileSelect} />
    </>
  );
});

const ImageItem = React.memo(function ImageItem({
  isPc,
  image,
  removeImage
}: {
  isPc: boolean;
  image: ImageType;
  removeImage: (image: ImageType) => void;
}) {
  return (
    <Box
      display="inline-block"
      mt={respDims('14rpx', 12, 8)}
      mr={respDims('14rpx', 12, 8)}
      w={respDims('112rpx', pcImageWidth)}
      h={respDims('112rpx', pcImageHeight)}
      position="relative"
      _hover={{
        '.remove-image': {
          display: 'block'
        }
      }}
    >
      {!image.src ? (
        <Spinner
          pos="absolute"
          left="0"
          right="0"
          top="0"
          bottom="0"
          m="auto"
          w={respDims('30rpx', '20fpx')}
          h={respDims('30rpx', '20fpx')}
        />
      ) : (
        <Image
          w="100%"
          h="100%"
          src={image.icon}
          alt=""
          objectFit="cover"
          borderRadius={respDims('8rpx', 4)}
          border="1px solid #E5E7EB"
        />
      )}
      <SvgIcon
        name="circleClose"
        className="remove-image"
        display={isPc ? 'none' : 'block'}
        position="absolute"
        top={respDims('-14rpx', -12, -8)}
        right={respDims('-14rpx', -12, -8)}
        w={respDims('40rpx', 24, 16)}
        h={respDims('40rpx', 24, 16)}
        flexGrow="0"
        flexShrink="0"
        cursor="pointer"
        onClick={() => removeImage(image)}
      />
    </Box>
  );
});

const FileItem = React.memo(function FileItem({
  isPc,
  file,
  removeFile
}: {
  isPc: boolean;
  file: FileType;
  removeFile: (file: FileType) => void;
}) {
  return (
    <Box
      display="inline-block"
      overflow="hidden"
      pt={respDims('14rpx', 20)}
      pr={respDims('14rpx', 20)}
      _hover={{
        '.remove-file': {
          display: 'block'
        }
      }}
    >
      <Flex
        position="relative"
        alignItems="center"
        borderRadius={respDims('8rpx', 8)}
        bgColor="#F7F8FA"
        px={respDims('12rpx', 12)}
        h={respDims('112rpx', 68)}
      >
        <Box w={respDims('88rpx', 40)} h={respDims('88rpx', 40)} position="relative">
          {[UploadStatusEnum.waiting, UploadStatusEnum.uploading, UploadStatusEnum.error].includes(
            file.uploadStatus
          ) ? (
            <SvgIcon
              name={file.uploadStatus == UploadStatusEnum.error ? 'fileError' : 'fileBlank'}
              w="100%"
              h="100%"
            />
          ) : !!file.imgIcon ? (
            <Image w="100%" h="100%" src={file.imgIcon} alt="" objectFit="cover" />
          ) : (
            !!file.svgIcon && <SvgIcon name={file.svgIcon} w="100%" h="100%" />
          )}
          {[UploadStatusEnum.waiting, UploadStatusEnum.uploading].includes(file.uploadStatus) && (
            <Box
              position="absolute"
              w="30%"
              h="30%"
              left="0"
              top="0"
              right="0"
              bottom="0"
              m="auto"
              borderLeft="2px solid #ffffff"
              borderTop="2px solid #ffff"
              borderRadius="50%"
              animation={`${rotate} infinite 1s linear`}
            />
          )}
        </Box>

        <Box ml={respDims('12rpx', 12)} flex="1" overflow="hidden">
          <Box
            color="#1D2129"
            fontSize={respDims('26rpx', 14, 12)}
            whiteSpace="nowrap"
            overflow="hidden"
            textOverflow="ellipsis"
          >
            {file.name}
          </Box>
          <Box mt={respDims('4rpx', 4)} color="#909399" fontSize={respDims('24rpx', 13, 12)}>
            {(() => {
              if (
                [UploadStatusEnum.waiting, UploadStatusEnum.uploading].includes(file.uploadStatus)
              ) {
                return file.percent === 100 ? '解析中...' : '上传中...';
              }
              if (file.uploadStatus === UploadStatusEnum.error) {
                return '上传失败';
              }
              return `${file.type}, ${file.sizeText}`;
            })()}
          </Box>
        </Box>

        <SvgIcon
          name="circleClose"
          className="remove-file"
          display={isPc ? 'none' : 'block'}
          position="absolute"
          top={respDims('-14rpx', -12, -8)}
          right={respDims('-14rpx', -12, -8)}
          w={respDims('40rpx', 24, 16)}
          h={respDims('40rpx', 24, 16)}
          flexGrow="0"
          flexShrink="0"
          cursor="pointer"
          onClick={() => removeFile(file)}
        />
      </Flex>
    </Box>
  );
});

class AtDetector {
  private startText = '';

  private lastText = '';

  public name = '';

  public index = -1;

  constructor(startText = '') {
    this.startText = startText;
  }

  private findAtIndex(text: string, lastText: string) {
    if (text.length - lastText.length !== 1) {
      return -1;
    }
    let startIndex = 0;
    let stopIndex = text.length - 1;
    while (startIndex < lastText.length && text[startIndex] === lastText[startIndex]) {
      startIndex++;
    }
    if (text[startIndex] !== '@') {
      return -1;
    }
    while (stopIndex > startIndex && text[stopIndex] === lastText[stopIndex - 1]) {
      stopIndex--;
    }
    return startIndex === stopIndex ? startIndex : -1;
  }

  update(text: string) {
    if (!text.includes('@')) {
      this.startText = text;
      this.lastText = text;
      this.name = '';
      this.index = -1;
      return '';
    }

    if (
      this.index >= 0 &&
      text[this.index] === '@' &&
      text.length > this.startText.length &&
      this.startText.substring(0, this.index) === text.substring(0, this.index) &&
      text.endsWith(this.startText.substring(this.index))
    ) {
      this.lastText = text;
      this.name = text.substring(this.index, text.length - (this.startText.length - this.index));
      if (this.name.endsWith('@')) {
        this.index += this.name.length - 1;
        this.name = '@';
        this.startText = text.substring(0, this.index) + text.substring(this.index + 1);
      }
      return this.name;
    }

    this.index = this.findAtIndex(text, this.lastText);

    if (this.index >= 0) {
      this.startText = this.lastText;
      this.lastText = text;
      this.name = '@';
    } else {
      this.startText = text;
      this.lastText = text;
      this.name = '';
    }

    return this.name;
  }
}
