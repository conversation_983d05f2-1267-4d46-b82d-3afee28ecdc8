import { useOverlayManager } from '@/hooks/useOverlayManager';
import { Box, BoxProps, Center } from '@chakra-ui/react';
import { useState } from 'react';
import SvgIcon from '../SvgIcon';
import Chooser, { ModalModeEnum } from '@/pages/cloud/list/components/Chooser';
import { respDims } from '@/utils/chakra';
import { ChatItemType } from '@/fastgpt/global/core/chat/type';
import { formatChatValue2InputType } from '@/utils/chat';
import { parse } from 'marked';
const saveToCloudStyle: BoxProps = {
  height: respDims('34rpx'),
  fontWeight: 500,
  px: respDims('24rpx', 20),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  background: '#F2EDFF',
  borderRadius: respDims('50rpx', 25),
  fontSize: respDims('24rpx', '12fpx'),
  color: 'primary.500'
};
export const Chat2Cloud = ({ chatItem }: { chatItem: ChatItemType }) => {
  const { openOverlay } = useOverlayManager();
  const [title, setTitle] = useState('');
  const onSaveToCloud = async () => {
    const { text }: any = formatChatValue2InputType(chatItem.value);
    const htmlContent = (await parse(text)) || '';
    console.log(htmlContent);

    openOverlay({
      Overlay: Chooser,
      props: {
        title: '将文件保存至数据空间',
        modalMode: ModalModeEnum.Save,
        showCreateFolderBtn: true,
        filename: title,
        htmlStr: htmlContent,
        setTitle,
        onSuccess(files, inputFileName) {}
      }
    });
  };
  return (
    <Box position="relative">
      <Center
        id="step2"
        className="chat-edit-mode"
        {...saveToCloudStyle}
        onClick={onSaveToCloud}
        whiteSpace="nowrap"
        _hover={
          {
            // background: 'linear-gradient(0deg, #FFD97D 0%, #FFEBBA 100%)',
            // boxShadow: '0px 4px 10px 0px rgba(255, 229, 142, 0.38)'
          }
        }
      >
        <SvgIcon name="download2" mr={respDims(8)} w={respDims(22, 18)} h={respDims(22, 18)} />
        <Box fontWeight="bold" fontSize={respDims('14fpx')}>
          保存至数据空间
        </Box>
      </Center>
    </Box>
  );
};

export default Chat2Cloud;
