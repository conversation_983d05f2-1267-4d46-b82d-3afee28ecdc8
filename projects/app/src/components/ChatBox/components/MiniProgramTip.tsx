import { Box, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useState } from 'react';
import { useSystemStore } from '@/store/useSystemStore';

export const MiniProgramTip = () => {
  const [refresh, setRefresh] = useState(false);
  const { isPc, feConfigs } = useSystemStore();
  const shouldShowAppTip =
    feConfigs.show_appTip !== false && window.location.href.includes('huayuntiantu.com'); // 默认显示，除非明确设置为false

  if (isPc) return null;
  if (!shouldShowAppTip) return null;

  return (
    <Flex
      bottom={'12%'}
      left={'50%'}
      transform="translateX(-50%)"
      position="fixed"
      height={respDims('88rpx')}
      width={'95%'}
      bg="white"
      alignItems="center"
      borderRadius={'8px'}
      boxShadow="0px 1px 10px 0px rgba(0, 0, 0, 0.05), 0px 4px 5px 0px rgba(0, 0, 0, 0.08), 0px 2px 4px -1px rgba(0, 0, 0, 0.12)"
      px={respDims('32rpx')}
      gap={respDims('16rpx')}
      zIndex={999}
      display={localStorage.getItem('hideAppTip') === 'true' ? 'none' : 'flex'}
    >
      <Flex flex={1} alignItems="center" justifyContent="space-between">
        <Box color="#303133" fontSize={respDims('28rpx')}>
          使用「华云天图」小程序体验更多功能
        </Box>
        <Flex alignItems="center">
          <Box
            as="button"
            color="#7D4DFF"
            fontSize={respDims('28rpx')}
            onClick={() => {
              window.location.href =
                'weixin://dl/business/?appid=wxd32f7040ae577a88&path=pages/chat/chat';
            }}
            style={{
              background: 'none',
              border: 'none',
              padding: 0,
              cursor: 'pointer'
            }}
          >
            点击使用
          </Box>
          <Box
            pos="absolute"
            right={-2}
            top={respDims('-14rpx')}
            cursor="pointer"
            // ml={respDims('24rpx')}
            onClick={(e) => {
              e.stopPropagation();
              localStorage.setItem('hideAppTip', 'true');
              setRefresh((prev) => !prev);
            }}
          >
            <SvgIcon name="xCircle" w={respDims('32rpx')} h={respDims('32rpx')} color="#909399" />
          </Box>
        </Flex>
      </Flex>
    </Flex>
  );
};
