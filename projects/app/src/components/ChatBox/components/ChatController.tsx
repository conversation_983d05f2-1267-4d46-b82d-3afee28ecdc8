import React from 'react';
import { Box, Flex, FlexProps, Center, Image } from '@chakra-ui/react';
import { ChatSiteItemType } from '@/fastgpt/global/core/chat/type';
import { useSystemStore } from '@/store/useSystemStore';
import { useCopyData } from '@/hooks/useCopyData';
import { useChatStore } from '@/store/useChatStore';
import useCompDom from '@/hooks/useCompDom';
import { useAudioPlay } from '@/utils/voice';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { useMindMapStore } from '@/store/useMindMapStore';
import { useRouter } from 'next/router';
import { respDims } from '@/utils/chakra';
import { ChatBoxMode } from '../constant';
import { AppTTSConfigType } from '@/fastgpt/global/core/app/type';
import { ChatRoleEnum, FeedbackTypeEnum } from '@/fastgpt/global/core/chat/constants';
import {
  convertHtmlToPdf,
  convertHtmlToWord,
  convertMdToHtml,
  covertHtmlToMarkdown
} from '@/api/chat';
import { htmlTemplate } from '@/constants/chat';
import { formatChatValue2InputType, replacePreWithDiv } from '@/utils/chat';
import { convertHtmlToXhtml } from '@/utils/html';
import MyTooltip from '../../MyTooltip';
import SvgIcon from '../../SvgIcon';
import Lottie from '../../Lottie';
import MyMenu from '../../MyMenu';
import CustomTour from '../../CustomTour';
import ChatItemWordTemplate from '../ChatItemWordTemplate';
import { marked } from 'marked';
import { handleFormula } from '@/utils/export';

export type ChatControllerProps = {
  chat: ChatSiteItemType;
  isLastAIMessage: boolean;
  setChatHistory?: React.Dispatch<React.SetStateAction<ChatSiteItemType[]>>;
  showCopy?: boolean;
  ttsConfig?: AppTTSConfigType;
  alwaysShow?: boolean;
  isFlat?: boolean;
  onRetry?: () => void;
  onDelete?: () => void;
  onAddPrompt?: () => void;
  onEdit?: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
  onAddUserLike?: () => void;
  onAddUserDislike?: () => void;
  chatMode: ChatBoxMode;
} & FlexProps;

const ChatController = ({
  chat,
  isLastAIMessage,
  setChatHistory,
  showCopy = true,
  ttsConfig,
  alwaysShow,
  isFlat,
  onRetry,
  onDelete,
  onAddPrompt,
  onEdit,
  onConfirm,
  onCancel,
  onAddUserLike,
  onAddUserDislike,
  chatMode,
  ...props
}: ChatControllerProps) => {
  const { isPc, setLoading } = useSystemStore();
  const { copyData } = useCopyData();
  const { chatData } = useChatStore();
  const { getCompDom } = useCompDom();

  const { audioLoading, audioPlaying, hasAudio, playAudio, cancelAudio } = useAudioPlay({
    ttsConfig
  });
  const { setDeepEditChatItem, setEditType } = useDeepEditStore();
  const { setSelectedChat } = useMindMapStore();
  const router = useRouter();
  const controlIconStyle = {
    w: respDims('36rpx', '24fpx'),
    h: respDims('36rpx', '24fpx'),
    cursor: 'pointer',
    color: '#909399'
  };

  const aloneItemStyle = isPc
    ? {
        w: respDims('32fpx'),
        h: respDims('32fpx'),
        backgroundColor: !isLastAIMessage ? '#ffffff' : '',
        border: !isLastAIMessage ? '1px solid #F3F4F6' : 'none',
        borderRadius: respDims(4)
      }
    : {};

  const onCopy = async () => {
    const { text } = formatChatValue2InputType(chat.value);
    let html = await convertMdToHtml({
      htmlStr: text || ''
    });

    const markdownContainer = document
      .getElementById(chat.dataId!)
      ?.getElementsByClassName('markdown')?.[0];
    if (!markdownContainer) return;
    // 创建一个临时的 div 来处理格式化内容
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = markdownContainer.innerHTML;

    // 移除所有 chat-controller 类的元素
    const controllers = tempDiv.getElementsByClassName('chat-controller');
    while (controllers.length > 0) {
      controllers[0].remove();
    }
    // 移除所有 reasoning-content 类的元素
    const reasoningContents = tempDiv.getElementsByClassName('reasoning-content');
    while (reasoningContents.length > 0) {
      reasoningContents[0].remove();
    }

    // 创建剪贴板数据
    const clipboardData = new ClipboardItem({
      'text/html': new Blob([html], { type: 'text/html' }),
      'text/plain': new Blob([tempDiv.textContent || ''], { type: 'text/plain' })
    });

    // 使用新的 Clipboard API
    navigator.clipboard
      .write([clipboardData])
      .then(() => {
        console.log('复制成功');
      })
      .catch(() => {
        // 如果新 API 失败，回退到选区方式
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(markdownContainer);

        selection?.removeAllRanges();
        selection?.addRange(range);

        try {
          document.execCommand('copy');
          console.log('复制成功');
        } catch (err) {
          console.error('复制失败:', err);
        }

        window.setTimeout(() => {
          selection?.removeAllRanges();
        }, 0);
      });
  };

  const onDeepEdit = () => {
    setEditType('edit');
    setDeepEditChatItem(chat);
    window.open(`/deepeditor?appId=${chatData?.appId}&mode=chat&init=1`, '_blank');
  };

  const onExportMindMap = async () => {
    setSelectedChat(chat);
    window.open(`/MindMapPage?appId=${chatData?.appId}&mode=chat&init=1`, '_blank');
  };

  const onExportPDF = async () => {
    setLoading(true);
    try {
      const chatItemHtml = await (async () => {
        const historyDom = await getCompDom(() => {
          return <ChatItemWordTemplate chatItem={chat} />;
        });
        if (!historyDom) return;
        replacePreWithDiv(historyDom);
        const html = htmlTemplate.replace('{{CHAT_CONTENT}}', historyDom.outerHTML);
        const domParser = new DOMParser();
        const htmlDocument = domParser.parseFromString(html, 'text/html');
        // 设置表格样式
        htmlDocument.body.querySelectorAll('table').forEach((table) => {
          table.style.borderCollapse = 'collapse';
          table.style.border = '1px solid #000';
          table.querySelectorAll('th, td').forEach((cell) => {
            const htmlCell = cell as HTMLTableCellElement;
            if (!htmlCell.hasAttribute('style')) {
              htmlCell.style.padding = '3px 8px';
              htmlCell.style.border = '1px solid #000';
            }
          });
        });

        return htmlDocument.body.innerHTML;
      })();
      await convertHtmlToPdf({
        filename: `当轮聊天记录`,
        htmlStr: chatItemHtml || ''
      });
    } catch (error) {}
    setLoading(false);
  };
  const onExportWord = async () => {
    setLoading(true);
    const { text } = formatChatValue2InputType(chat.value);
    const html = await marked.parse(text || '');
    try {
      const chatItemHtml = await (async () => {
        const historyDom = await getCompDom(() => {
          return <ChatItemWordTemplate chatItem={chat} />;
        });
        if (!historyDom) return;
        replacePreWithDiv(historyDom);
        const html = htmlTemplate.replace('{{CHAT_CONTENT}}', historyDom.outerHTML);
        const domParser = new DOMParser();
        const htmlDocument = domParser.parseFromString(html, 'text/html');
        // 设置表格样式
        htmlDocument.body.querySelectorAll('table').forEach((table) => {
          table.style.borderCollapse = 'collapse';
          table.style.border = '1px solid #000';
          table.querySelectorAll('th, td').forEach((cell) => {
            const htmlCell = cell as HTMLTableCellElement;
            if (!htmlCell.hasAttribute('style')) {
              htmlCell.style.padding = '3px 8px';
              htmlCell.style.border = '1px solid #000';
            }
          });
        });
        return htmlDocument.body.innerHTML;
      })();

      await convertHtmlToWord({
        filename: `当轮聊天记录`,
        htmlStr: convertHtmlToXhtml(chatItemHtml || '')
      });
    } catch (error) {}
    setLoading(false);
  };

  const { chatId, getCurrentStep } = useChatStore();

  return (
    <Flex
      className="chat-controller"
      alignItems="center"
      justifyContent="space-between"
      visibility={alwaysShow || audioLoading || isLastAIMessage ? 'visible' : 'hidden'}
      {...props}
    >
      <Flex
        alignItems="center"
        h={respDims('48rpx', '52fpx')}
        {...(!isFlat &&
          !isLastAIMessage && {
            px: respDims('20rpx', 16),
            boxShadow: '0px 1px 8px 0px rgba(0,0,0,0.12)',
            borderRadius: respDims('16rpx', 8)
          })}
        bgColor={isPc ? '#ffffff' : 'rgba(255,255,255,0.9)'}
        borderRadius={'50px'}
        css={{
          '&>:not(:first-child)': {
            marginLeft: respDims('20rpx', 16)
          }
        }}
      >
        {!onEdit ? (
          <Box>
            <Box position="relative">
              <MyTooltip label="深入编辑">
                <SvgIcon onClick={onDeepEdit} name="quill_pen_ai_line" {...controlIconStyle} />
              </MyTooltip>
              {
                <CustomTour
                  chat={chat}
                  chatId={chatId}
                  maskExclude={['.exclude']}
                  targetIdentifier="custom-target"
                />
              }
            </Box>
          </Box>
        ) : null}
        {!onEdit && (
          <MyMenu
            Button={<SvgIcon name="classify_add_2_line" {...controlIconStyle} />}
            menuList={[
              {
                icon: <SvgIcon name="mindMap" {...controlIconStyle} />,
                label: '思维导图',
                onClick: onExportMindMap
              },
              {
                icon: <SvgIcon name="doc_line" {...controlIconStyle} />,
                label: '导出Word',
                onClick: onExportWord
              },
              {
                icon: <SvgIcon name="pdf_line" {...controlIconStyle} />,
                label: '导出PDF',
                onClick: onExportPDF
              }
            ]}
          />
        )}

        {getCurrentStep() === 1 && (
          <CustomTour
            chat={chat}
            chatId={chatId}
            maskExclude={['.exclude']}
            targetIdentifier="custom-target"
          />
        )}
        {!onEdit && isLastAIMessage && <Box w={respDims(2)} h="50%" bg="#E5E7EB"></Box>}

        {showCopy && (
          <MyTooltip label="复制">
            <SvgIcon name="copy" {...controlIconStyle} onClick={onCopy} />
          </MyTooltip>
        )}
        {hasAudio &&
          (audioLoading || audioPlaying ? (
            <MyTooltip label="停止播报">
              <Lottie name="waveform" {...controlIconStyle} onClick={cancelAudio} />
            </MyTooltip>
          ) : (
            <MyTooltip label="语音播报">
              <SvgIcon
                name="voice"
                {...controlIconStyle}
                onClick={async () => {
                  const { text } = formatChatValue2InputType(chat.value);
                  const html = await convertMdToHtml({
                    htmlStr: text || ''
                  });
                  const domParser = new DOMParser();
                  const htmlDocument = domParser.parseFromString(html, 'text/html');
                  const textContent = htmlDocument.body.textContent || '';
                  console.log(textContent, 'textContent');

                  const response = await playAudio({
                    buffer: chat.ttsBuffer,
                    chatItemId: chat.dataId,
                    appId: chatData.finalAppId,
                    text: textContent
                  });
                  if (!setChatHistory || !response.buffer) return;
                  setChatHistory((state) =>
                    state.map((item) =>
                      item.dataId === chat.dataId
                        ? {
                            ...item,
                            ttsBuffer: response.buffer
                          }
                        : item
                    )
                  );
                }}
              ></SvgIcon>
            </MyTooltip>
          ))}
        {onEdit && (
          <MyTooltip label="编辑">
            <SvgIcon name="edit" {...controlIconStyle} onClick={onEdit} />
          </MyTooltip>
        )}

        {onDelete && (
          <MyTooltip label="删除">
            <SvgIcon name="trash" {...controlIconStyle} onClick={onDelete} />
          </MyTooltip>
        )}

        {onConfirm && (
          <MyTooltip label="确定">
            <SvgIcon name="check" {...controlIconStyle} onClick={onConfirm} />
          </MyTooltip>
        )}

        {onCancel && (
          <MyTooltip label="取消">
            <SvgIcon name="close" {...controlIconStyle} onClick={onCancel} />
          </MyTooltip>
        )}

        {onRetry && (
          <MyTooltip label="重新生成">
            <SvgIcon name="repeat" {...controlIconStyle} onClick={onRetry} />
          </MyTooltip>
        )}

        {isPc && onAddPrompt && chatMode == ChatBoxMode.Chat && (
          <MyTooltip label="添加快捷指令">
            <SvgIcon name="circlePlus" {...controlIconStyle} onClick={onAddPrompt} />
          </MyTooltip>
        )}
      </Flex>
      {(onAddUserLike || onAddUserDislike) && (
        <Flex
          h={respDims('48rpx', '34fpx')}
          ml={respDims('20rpx', 16)}
          {...(!isPc &&
            !isFlat && {
              px: respDims('20rpx', 16),
              boxShadow: '1px 1px 5px rgba(0,0,0,0.12)',
              borderRadius: respDims('16rpx', 8),
              bgColor: 'rgba(255,255,255,0.9)'
            })}
          css={{
            '&>:not(:first-child)': {
              marginLeft: respDims('20rpx', 16)
            }
          }}
        >
          {onAddUserLike && (
            <Center {...aloneItemStyle}>
              <SvgIcon
                name={
                  chat.obj === ChatRoleEnum.AI && chat.feedbackType == FeedbackTypeEnum.Upvote
                    ? 'upvoteFill'
                    : 'upvote'
                }
                {...controlIconStyle}
                onClick={onAddUserLike}
                lineHeight="60px"
              />
            </Center>
          )}

          {onAddUserDislike && (
            <Center {...aloneItemStyle}>
              <SvgIcon
                name={
                  chat.obj === ChatRoleEnum.AI && chat.feedbackType == FeedbackTypeEnum.Downvote
                    ? 'downvoteFill'
                    : 'downvote'
                }
                {...controlIconStyle}
                onClick={onAddUserDislike}
              />
            </Center>
          )}
        </Flex>
      )}
    </Flex>
  );
};

export default React.memo(ChatController);
