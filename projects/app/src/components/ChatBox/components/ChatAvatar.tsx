import React from 'react';
import { Image, BoxProps } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { APP_ICON } from '@/constants/common';

interface ChatAvatarProps extends BoxProps {
  src?: string;
  type: 'Human' | 'AI';
}

const ChatAvatar = ({ src, type, ...props }: ChatAvatarProps) => {
  return (
    <Image
      w={respDims(40)}
      h={respDims(40)}
      borderRadius="50%"
      overflow="hidden"
      src={src}
      alt=""
      objectFit="cover"
      fallbackSrc={APP_ICON}
      {...props}
      border="2px solid #FFF"
      boxShadow="0px 4px 4px 0px rgba(0, 0, 0, 0.07)"
    />
  );
};

export default ChatAvatar;
