import React from 'react';
import { Box, Card, Input, Textarea } from '@chakra-ui/react';
import { Controller, FieldValues, UseFormReturn } from 'react-hook-form';
import { VariableItemType } from '@/fastgpt/global/core/app/type';
import { VariableInputEnum } from '@/fastgpt/global/core/workflow/constants';
import { respDims } from '@/utils/chakra';
import MySelect from '../../MySelect';
import ChatAvatar from './ChatAvatar';

interface Props {
  appAvatar?: string;
  variableList: VariableItemType[];
  variablesForm: UseFormReturn<FieldValues, any>;
}

const VariableInput = ({ appAvatar, variableList, variablesForm }: Props) => {
  const { register, setValue, control } = variablesForm;

  return (
    <Box mt={respDims(32)}>
      <Box textAlign={'left'}>
        <Card order={2} mt={2} w={'400px'} bg={'white'} boxShadow={'0 0 8px rgba(0,0,0,0.15)'}>
          {variableList.map((item) => (
            <Box key={item.id} mb={4}>
              <Box as={'label'} display={'inline-block'} position={'relative'} mb={1}>
                {item.label}
                {item.required && (
                  <Box
                    position={'absolute'}
                    top={'-2px'}
                    right={'-10px'}
                    color={'red.500'}
                    fontWeight={'bold'}
                  >
                    *
                  </Box>
                )}
              </Box>
              {item.type === VariableInputEnum.input && (
                <Input
                  bg={'myWhite.400'}
                  {...register(item.key, {
                    required: item.required
                  })}
                />
              )}
              {item.type === VariableInputEnum.textarea && (
                <Textarea
                  bg={'myWhite.400'}
                  {...register(item.key, {
                    required: item.required
                  })}
                  rows={5}
                  maxLength={4000}
                />
              )}
              {item.type === VariableInputEnum.select && (
                <Controller
                  key={item.key}
                  control={control}
                  name={item.key}
                  rules={{ required: item.required }}
                  render={({ field: { ref, value } }) => {
                    return (
                      <MySelect
                        ref={ref}
                        width={'100%'}
                        list={(item.enums || []).map((item) => ({
                          label: item.value,
                          value: item.value
                        }))}
                        value={value}
                        onchange={(e) => setValue(item.key, e)}
                      />
                    );
                  }}
                />
              )}
            </Box>
          ))}
        </Card>
      </Box>
    </Box>
  );
};

export default VariableInput;
