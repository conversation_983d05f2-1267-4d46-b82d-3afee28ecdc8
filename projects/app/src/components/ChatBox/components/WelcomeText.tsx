import React from 'react';
import { Box, BoxProps, Flex } from '@chakra-ui/react';
import { useSystemStore } from '@/store/useSystemStore';
import { respDims } from '@/utils/chakra';
import { ChatRoleEnum } from '@/fastgpt/global/core/chat/constants';
import Markdown from '../../Markdown';
import ChatAvatar from './ChatAvatar';

interface Props {
  appAvatar?: string;
  welcomeText: string;
  onClickAvatar?: () => void;
}

const WelcomeText = ({ appAvatar, welcomeText, onClickAvatar }: Props) => {
  const { isPc } = useSystemStore();
  const messageCardStyle: BoxProps = {
    px: respDims('32rpx', 32),
    py: respDims('28rpx', 20),
    paddingBottom: respDims('28rpx'),
    maxW: '100%',
    display: 'inline-block',
    borderRadius: respDims('40rpx', 24),
    fontSize: respDims('28rpx', '14fpx')
  };
  const aiMessageCardStyle: BoxProps = {
    ...messageCardStyle,
    color: '#1D2129',
    bgColor: '#ffffff !important',
    borderTopLeftRadius: respDims('6rpx', 3),
    boxShadow: '0px 1px 8px 0px rgba(0,0,0,0.12)',
    _hover: {
      '& .chat-controller': {
        visibility: 'visible'
      }
    }
  };
  return (
    <Flex mt={respDims(32)} pr={respDims(48)}>
      {isPc && (
        <ChatAvatar
          src={appAvatar}
          type="AI"
          flexShrink="0"
          mr={respDims(14, 4)}
          cursor={onClickAvatar ? 'pointer' : 'default'}
          onClick={onClickAvatar}
        />
      )}

      <Box pr={respDims(100, 10)} overflow="hidden" {...aiMessageCardStyle}>
        <Markdown source={'~~~guide \n' + welcomeText} isChatting={false} obj={ChatRoleEnum.AI} />
      </Box>
    </Flex>
  );
};

export default React.memo(WelcomeText);
