import { updateChatItem } from '@/api/chat';
import Markdown, { CodeClassName } from '@/components/Markdown';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatStatusEnum,
  FeedbackTypeEnum,
  IMG_BLOCK_KEY
} from '@/fastgpt/global/core/chat/constants';
import {
  ChatHistoryItemType,
  ChatItemValueItemType,
  ChatSiteItemType,
  UserChatItemValueItemType
} from '@/fastgpt/global/core/chat/type';
import { respDims, rpxDim } from '@/utils/chakra';
import {
  Box,
  BoxProps,
  Checkbox,
  Flex,
  Image,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Textarea,
  useDisclosure
} from '@chakra-ui/react';
import ChatController from './ChatController';
import SvgIcon from '@/components/SvgIcon';
import { Spin } from 'antd';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { ChatFileType } from '@/types/api/chat';
import { Toast } from '@/utils/ui/toast';
import { ChatBoxMode } from '../constant';
import useFilePreview from '@/hooks/useFilePreview';
import { Dispatch, SetStateAction } from 'react';
import { parseFileAndInput } from '..';
import FileInCloud from '../FileInCloud';
import ChatAvatar from './ChatAvatar';
import { useAppStore } from '@/store/useAppStore';
import Lottie from '@/components/Lottie';
import { useChatStore } from '@/store/useChatStore';
import AIResponseBox from './AIResponseBox';
import ResponseTags from '../ResponseTags';
import { VariableInputEnum } from '@/fastgpt/global/core/workflow/constants';
import { AppChatConfigType } from '@/fastgpt/global/core/app/type';
import { useSystemStore } from '@/store/useSystemStore';
import FilePreviewModal from '@/components/FilePreviewModal';

const messageCardStyle: BoxProps = {
  px: respDims('32rpx', 32),
  py: respDims('28rpx', 20),
  paddingBottom: respDims('28rpx'),
  maxW: '100%',
  display: 'inline-block',
  borderRadius: respDims('40rpx', 24),
  fontSize: respDims('28rpx', '14fpx')
};

const humanMessageCardStyle: BoxProps = {
  ...messageCardStyle,
  color: '##303133',
  bgColor: '#E7F0FF !important',
  borderTopRightRadius: respDims('40rpx', 3),
  borderBottomRightRadius: respDims('6rpx', 24),
  boxShadow: '0px 3px 13px 0px rgba(0,0,0,0.04)',
  paddingBottom: respDims('28rpx', 18),
  _hover: {
    '& .chat-controller': {
      visibility: 'visible'
    }
  }
};

const aiMessageCardStyle: BoxProps = {
  ...messageCardStyle,
  color: '#1D2129',
  bgColor: '#ffffff !important',
  borderTopLeftRadius: respDims('6rpx', 3),
  boxShadow: '0px 1px 8px 0px rgba(0,0,0,0.12)',
  _hover: {
    '& .chat-controller': {
      visibility: 'visible'
    }
  }
};

type Props = {
  chatFiles: ChatFileType[];
  chatHistory: ChatSiteItemType[];
  userAvatar?: string;
  appAvatar?: string;
  appId?: string;
  chatId?: string;
  fileContentLoading: boolean;
  parseLoading: boolean;
  chatConfig?: AppChatConfigType;
  questionGuides: string[];
  setChatHistory: Dispatch<SetStateAction<ChatSiteItemType[]>>;
  isShare?: boolean;
  shareId?: string;
  onDelMessage?: (e: { contentId: string }) => Promise<boolean>;
  item: ChatSiteItemType;
  index: number;
  mode: ChatBoxMode;
  isChatting: boolean;
  chatItemEditDataId: string | undefined;
  setChatItemEditDataId: Dispatch<SetStateAction<string | undefined>>;
  resendMessage: (dataId: string, inputVal?: string) => void;
  abortSendMessage: (type?: 'stop' | 'cancel') => void;
  onAddPrompt: (val: string) => void;
  setFeedbackId: Dispatch<SetStateAction<string | undefined>>;
};

export default function ChatItem({
  chatFiles,
  chatHistory,
  userAvatar,
  appAvatar,
  appId,
  chatId,
  parseLoading,
  fileContentLoading,
  questionGuides,
  setChatHistory,
  chatConfig,
  isShare,
  shareId,
  onDelMessage,
  item,
  index,
  mode,
  isChatting,
  chatItemEditDataId,
  setChatItemEditDataId,
  resendMessage,
  setFeedbackId,
  abortSendMessage,
  onAddPrompt
}: Props) {
  const [fileLoadingMap, setFileLoadingMap] = useState<Record<string, boolean>>({});
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const { previewFile, modalVisible, iframeSrc, modalTitle, closeModal } = useFilePreview();
  const chatItemEditRef = useRef<HTMLTextAreaElement>(null);
  const [chatItemEditValue, setChatItemEditValue] = useState<string>();
  const { setViewApp } = useAppStore();
  const chatGenerating = useMemo(
    () => isChatting && chatHistory[chatHistory.length - 1]?.status !== ChatStatusEnum.finish,
    [isChatting, chatHistory]
  );
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [scale, setScale] = useState(1);
  const { isPc } = useSystemStore();
  const { welcomeText, variableList, questionGuide, ttsConfig, allVariableList } = useMemo(
    () => ({
      welcomeText: chatConfig?.welcomeText || '',
      variableList:
        chatConfig?.variables?.filter((it) => it.type !== VariableInputEnum.custom) || [],
      allVariableList: chatConfig?.variables || [],

      questionGuide: '',
      ttsConfig: chatConfig?.ttsConfig
    }),
    [chatConfig]
  );

  const { chatData } = useChatStore();
  const onStartChatItemEdit = (item: ChatSiteItemType) => {
    setChatItemEditDataId(item.dataId);
    let text =
      item.text ||
      item.value
        .filter((it) => it.type === ChatItemValueTypeEnum.text && it.text?.content)
        .map((it) => it.text?.content)
        .join('\n');
    text = text ? text.replace(new RegExp(`\`\`\`${IMG_BLOCK_KEY}.*\`\`\`\\s?`, 'gs'), '') : '';

    // 解析内容
    // 使用正则表达式解析内容
    const { filesResult, userChatInput } = parseFileAndInput(text);
    text = userChatInput.trim();

    setChatItemEditValue(userChatInput);
  };
  const isLastFn = (index: number) => {
    let lastAIIndex = -1;
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].obj === 'AI') {
        lastAIIndex = i;
        break;
      }
    }
    const isLastAIMessage = index === lastAIIndex;
    return isLastAIMessage;
  };

  const getChatFilesByIds = (ids: string[]) => {
    return ids.map((it) => chatFileMap[it]).filter((it) => it);
  };

  const chatFileMap = useMemo(
    () =>
      chatFiles.reduce((map, it) => ((map[it.id] = it), map), {} as Record<string, ChatFileType>),
    [chatFiles]
  );

  const removeMessageById = useCallback(
    (dataId?: string) => {
      if (!dataId || !onDelMessage) {
        return;
      }
      // 在删除未结束对话的记录时可能出现先调了删除接口但后端completions未结束，
      // 在completions结束时会保存记录，所以导致未实际删除
      let count = 0;
      const tryRemove = (): any =>
        mode == ChatBoxMode.Chat &&
        ++count < 10 &&
        onDelMessage({ contentId: dataId }).then((res) => {
          !res && setTimeout(tryRemove, count * 1000);
        });
      tryRemove();
    },
    [onDelMessage]
  );

  const removeMessageAt = useCallback(
    (index: number) => {
      // 同时删除用户和AI的聊天记录

      let humanDataId: string | undefined;
      let aiDataId: string | undefined;
      if (chatHistory[index]?.obj === 'Human') {
        humanDataId = chatHistory[index].dataId;
        if (chatHistory[index + 1]?.obj === 'AI') {
          aiDataId = chatHistory[index + 1].dataId;
          if (chatHistory[index + 1].status !== 'finish') {
            abortSendMessage('cancel');
          }
        }
      } else if (chatHistory[index]?.obj === 'AI') {
        if (chatHistory[index - 1]?.obj === 'Human') {
          humanDataId = chatHistory[index - 1].dataId;
        }
        if (chatHistory[index].status !== 'finish') {
          abortSendMessage('cancel');
        }
        aiDataId = chatHistory[index].dataId;
      }

      if (!humanDataId && !aiDataId) {
        return;
      }

      removeMessageById(humanDataId);
      removeMessageById(aiDataId);

      setChatHistory((state) =>
        state.filter((chat) => chat.dataId !== humanDataId && chat.dataId !== aiDataId)
      );
    },
    [chatHistory, removeMessageById, abortSendMessage]
  );

  const handleFile = async (file: any, type: string) => {
    const fileUrl = decodeURIComponent(type === 'image' ? file.url : file.imgIcon) || '';
    const res = {
      fileUrl,
      fileType: 1,
      searchContent: '',
      useModal: true,
      title: file.name,
      fileKey: file.id,
      updateFileUrl: true
    };

    try {
      setFileLoadingMap((prev) => ({ ...prev, [file.id]: true })); // 开始加载
      if (type === 'image' && file?.url) {
        setSelectedImage(file.url);
        onOpen();
      } else {
        await previewFile(res); // 假设 previewFile 是一个异步函数
      }
    } catch (error) {
      Toast.error('文件预览失败，请稍后重试');
    } finally {
      setFileLoadingMap((prev) => ({ ...prev, [file.id]: false })); // 结束加载
    }
  };

  const onConfirmChatItemEdit = () => {
    if (!chatItemEditDataId || !chatItemEditValue?.trim()) return;
    const index = chatHistory.findIndex((it) => it.dataId === chatItemEditDataId);
    if (index < 0) {
      return;
    }
    const history = chatHistory[index];
    const m = history.text
      ? history.text.match(new RegExp(`\`\`\`${IMG_BLOCK_KEY}.*\`\`\``, 'gs'))
      : '';
    resendMessage(
      chatItemEditDataId,
      m?.[0] ? `${m?.[0]}\n${chatItemEditValue}` : chatItemEditValue
    );
    setChatItemEditDataId(undefined);
    setChatItemEditValue(undefined);
  };

  const onCancelChatItemEdit = () => {
    setChatItemEditDataId(undefined);
    setChatItemEditValue(undefined);
  };

  return (
    <>
      <FilePreviewModal
        visible={modalVisible}
        onClose={closeModal}
        iframeSrc={iframeSrc}
        title={modalTitle} // 传递标题
      />
      <Modal isOpen={isOpen} onClose={onClose} isCentered>
        <ModalOverlay />
        <ModalContent boxShadow={'none'} maxW={'auto'} w="auto" bg={'transparent'}>
          <Image
            transform={`scale(${scale})`}
            borderRadius={'md'}
            src={selectedImage || ''}
            alt={''}
            w={'100%'}
            maxH={'80vh'}
            referrerPolicy="no-referrer"
            fallbackSrc={'/imgs/errImg.png'}
            fallbackStrategy={'onError'}
            objectFit={'contain'}
            // onWheel={handleWheel}
          />
        </ModalContent>
        <ModalCloseButton bg={'myWhite.500'} zIndex={999999} />
      </Modal>
      <Box id={item.dataId} key={index}>
        {item.obj === 'Human' && (
          <>
            <Flex
              w="100%"
              justifyContent="flex-end"
              sx={{
                '& .chakra-link': {
                  color: '#3366ff !important'
                }
              }}
            >
              {isShare && (
                <Checkbox
                  mb="28px"
                  flex="1"
                  colorScheme="primary"
                  className={item.isShareContent ? 'select-share' : ''}
                  isChecked={item.isShareContent}
                  onChange={() =>
                    setChatHistory((prevState) => {
                      const newState = [...prevState];
                      newState[index] = {
                        ...newState[index],
                        isShareContent: !item.isShareContent
                      };
                      return newState;
                    })
                  }
                  size="lg"
                />
              )}
              <Box
                minW="280px"
                ml={respDims('0rpx', 100, 10)}
                pb={respDims('32rpx', 32)}
                textAlign="right"
              >
                <Box
                  className="markdown"
                  pos="relative"
                  textAlign="left"
                  {...humanMessageCardStyle}
                >
                  {chatItemEditDataId === item.dataId ? (
                    <Textarea
                      ref={chatItemEditRef}
                      value={chatItemEditValue}
                      p="0"
                      borderRadius="0"
                      maxW="100%"
                      minW="10em"
                      minH="1em"
                      border="none"
                      outline="none"
                      boxShadow="none"
                      resize="none"
                      autoFocus
                      overflow="hidden"
                      _focus={{
                        bgColor: 'transparent',
                        border: 'none',
                        outline: 'none',
                        boxShadow: 'none'
                      }}
                      onChange={(e) => setChatItemEditValue(e.target.value)}
                    />
                  ) : (
                    item.value.map((subItem: UserChatItemValueItemType, subIndex) => (
                      <Box key={subIndex} {...subItem}>
                        {subItem.type === ChatItemValueTypeEnum.text && subItem.text ? (
                          <Markdown source={subItem.text.content} isChatting={false} />
                        ) : null}
                      </Box>
                    ))
                  )}
                  {!isShare && (
                    <ChatController
                      chat={item}
                      isLastAIMessage={isLastFn(index)}
                      pos="absolute"
                      right={respDims('16rpx', 24)}
                      // bottom={respDims('-24rpx', -17)}
                      chatMode={mode}
                      showCopy={chatItemEditDataId !== item.dataId}
                      onDelete={
                        chatItemEditDataId !== item.dataId && onDelMessage
                          ? () => {
                              removeMessageAt(index);
                            }
                          : undefined
                      }
                      onAddPrompt={
                        chatItemEditDataId !== item.dataId
                          ? () => {
                              onAddPrompt(item.text!);
                            }
                          : undefined
                      }
                      onEdit={
                        chatItemEditDataId !== item.dataId
                          ? () => onStartChatItemEdit(item)
                          : undefined
                      }
                      onConfirm={
                        chatItemEditDataId === item.dataId
                          ? () => onConfirmChatItemEdit()
                          : undefined
                      }
                      onCancel={
                        chatItemEditDataId === item.dataId
                          ? () => onCancelChatItemEdit()
                          : undefined
                      }
                    />
                  )}
                </Box>

                {!!item.value &&
                  ((files) =>
                    !!files.length && (
                      <Flex
                        {...(isPc
                          ? { flexWrap: 'wrap', justifyContent: 'end' }
                          : {
                              flexDir: 'column',
                              mt: rpxDim(16),
                              alignItems: 'flex-end'
                            })}
                      >
                        {files.map((it, index) => (
                          <Flex
                            key={index}
                            onClick={() => handleFile(it, 'file')}
                            mt={respDims('16rpx', 16)}
                            ml={respDims('0rpx', 16)}
                            alignItems="center"
                            w={respDims('404rpx', 234, 200)}
                            px={respDims('12rpx', 12)}
                            py={respDims('10rpx', 10)}
                            bgColor="#FFFFFF"
                            boxShadow="0px 0px 10px 0px rgba(62,71,83,0.12)"
                            borderRadius={respDims('16rpx', 8)}
                          >
                            {it.svgIcon ? (
                              <SvgIcon
                                flexShrink="0"
                                name={it.svgIcon}
                                w={respDims('88rpx', '40fpx')}
                                h={respDims('88rpx', '40fpx')}
                              />
                            ) : it.imgIcon ? (
                              <Image
                                flexShrink="0"
                                w={respDims('88rpx', '40fpx')}
                                h={respDims('88rpx', '40fpx')}
                                src={it.imgIcon}
                                alt=""
                              />
                            ) : undefined}

                            {/* 统一容器 */}
                            <Box
                              flex="1"
                              ml={respDims('12rpx', 12)}
                              minW="200px" // 固定最小宽度
                              minH="54px" // 固定最小高度
                              display="flex"
                              alignItems="center"
                              justifyContent="center"
                              my={respDims('-6rpx', -6)}
                            >
                              {fileLoadingMap[it.id] ? ( // 加载中显示动画
                                <Flex color="#7D4DFF" textAlign="left" pr={respDims(82)}>
                                  <Box pr={respDims(22)}>正在打开</Box>
                                  <Box>
                                    <Spin></Spin>
                                  </Box>
                                </Flex>
                              ) : (
                                <Box
                                  w="100%"
                                  h="100%"
                                  whiteSpace="nowrap"
                                  overflow="hidden"
                                  textOverflow="ellipsis"
                                  textAlign="left"
                                  css={{
                                    '&:hover': {
                                      color: '#7D4DFF' // 悬停时的文字颜色
                                    },
                                    '&:hover .name, &:hover .details': {
                                      color: '#7D4DFF' // 悬停时的文字颜色
                                    }
                                  }}
                                >
                                  <Box
                                    className="name"
                                    color="#1D2129"
                                    fontSize={respDims('24rpx', '14fpx')}
                                    lineHeight={respDims('44rpx', '22fpx')}
                                    overflow="hidden"
                                    whiteSpace="nowrap"
                                    textOverflow="ellipsis"
                                    pr={respDims(42)}
                                    css={{
                                      '&::-webkit-scrollbar': {
                                        display: 'none'
                                      },
                                      scrollbarWidth: 'none'
                                    }}
                                  >
                                    {it.name}
                                  </Box>
                                  <Box
                                    className="details"
                                    mt={respDims('0rpx', 4)}
                                    color="#909399"
                                    fontSize={respDims('24rpx', '13fpx')}
                                    lineHeight={respDims('44rpx', '22fpx')}
                                  >
                                    {it.type}, {it.sizeText}
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          </Flex>
                        ))}
                      </Flex>
                    ))(
                    getChatFilesByIds(
                      item.value
                        ?.filter((it) => it.type === ChatItemValueTypeEnum.file)
                        ?.map((it) => it.file?.fileId!)
                    )
                  )}
                {!!item.value &&
                  ((images) =>
                    !!images.length && (
                      <Flex
                        {...(isPc
                          ? { flexWrap: 'wrap', justifyContent: 'end' }
                          : {
                              flexDir: 'column',
                              mt: rpxDim(16),
                              alignItems: 'flex-end'
                            })}
                      >
                        {images.map((it, index) => (
                          <Image
                            key={index}
                            {...it}
                            src={it.file?.url}
                            onClick={() => handleFile(it.file, 'image')}
                            alt=""
                            objectFit="cover"
                            mt={respDims('16rpx', 16)}
                            ml={respDims('0rpx', 16)}
                            alignItems="center"
                            w={respDims('404rpx', 150, 200)}
                            px={respDims('12rpx', 12)}
                            py={respDims('12rpx', 10)}
                            bgColor="#FFFFFF"
                            boxShadow="0px 0px 10px 0px rgba(62,71,83,0.12)"
                            borderRadius={respDims('16rpx', 8)}
                          ></Image>
                        ))}
                      </Flex>
                    ))(
                    item.value?.filter(
                      (it) =>
                        it.type === ChatItemValueTypeEnum.file &&
                        it.file?.type === ChatFileTypeEnum.image
                    )
                  )}

                <FileInCloud
                  files={item.value
                    ?.filter((it) => it.type === ChatItemValueTypeEnum.file)
                    .map((it) => it.file)}
                />
              </Box>

              {isPc && (
                <ChatAvatar src={userAvatar} type="Human" flexShrink="0" ml={respDims(14, 4)} />
              )}
            </Flex>
          </>
        )}
        {item.obj === 'AI' && (
          <>
            <Flex w="100%" pt={respDims(8)}>
              {isShare && (
                <Checkbox
                  alignSelf="flex-start"
                  mr="16px"
                  mt="10px"
                  colorScheme="primary"
                  className={item.isShareContent ? 'select-share' : ''}
                  isChecked={item.isShareContent}
                  onChange={() =>
                    setChatHistory((prevState) => {
                      const newState = [...prevState];
                      newState[index] = {
                        ...newState[index],
                        isShareContent: !item.isShareContent
                      };
                      return newState;
                    })
                  }
                  size="lg"
                />
              )}
              {isPc && (
                <ChatAvatar
                  src={item.chatAppAvatarUrl || appAvatar}
                  type="AI"
                  flexShrink="0"
                  box-shadow="0px 4px 4px 0px rgba(0, 0, 0, 0.07)"
                  mr={respDims(14, 4)}
                  cursor="pointer"
                  onClick={() => setViewApp(item.chatAppId || appId)}
                />
              )}

              <Flex
                flexDir="column"
                alignItems="flex-start"
                minW="280px"
                mr={respDims('0rpx', 62, 10)}
                pb={respDims('46rpx', 46)}
              >
                <Box
                  pos="relative"
                  className="markdown"
                  {...aiMessageCardStyle}
                  pb={
                    !chatGenerating && index === chatHistory.length - 1 ? respDims('10rpx', 20) : ''
                  }
                  {...(!isPc && {
                    pt: respDims('36rpx'),
                    pb: respDims('36rpx')
                  })}
                >
                  {chatGenerating && index === chatHistory.length - 1 && (
                    <Flex alignItems="center">
                      <Lottie
                        name="chating"
                        w={respDims(`${(130 / 3) * 2}rpx`, 130 / 3)}
                        h={respDims(`${(45 / 3) * 2}rpx`, 45 / 3)}
                      />

                      {fileContentLoading ? (
                        <Box
                          ml={respDims('12rpx', 6)}
                          color="#606266"
                          fontSize={respDims('28rpx', '14fpx')}
                        >
                          {fileContentLoading ? '背景知识引用中' : ''}
                        </Box>
                      ) : (
                        <Box
                          ml={respDims('12rpx', 6)}
                          color="#606266"
                          fontSize={respDims('28rpx', '14fpx')}
                        >
                          {parseLoading
                            ? '解析中...'
                            : chatData?.app?.type === 'advanced'
                              ? item.moduleName || '生成中...'
                              : '生成中...'}
                        </Box>
                      )}
                    </Flex>
                  )}
                  {!item.hideInUI && (
                    <AIContentCard
                      index={index}
                      chatValue={item.value}
                      isLastChild={isLastFn(index)}
                      isChatting={isChatting}
                      questionGuides={questionGuides}
                      dataId={item.dataId}
                    />
                  )}

                  <ResponseTags responseData={item.responseData} isShare={!!shareId} />
                  {!(index === chatHistory.length - 1 && isChatting) && !isShare && (
                    <ChatController
                      chat={item}
                      isLastAIMessage={isLastFn(index)}
                      {...(!isPc && index === chatHistory.length - 1
                        ? {
                            alwaysShow: true,
                            isFlat: true,
                            mt: rpxDim(28)
                          }
                        : isLastFn(index)
                          ? {
                              paddingTop: respDims(16)
                            }
                          : {
                              pos: 'absolute',
                              left: 0,
                              bottom: respDims('-34rpx', -22),
                              minW: '100%',
                              paddingLeft: respDims('16rpx', 24),
                              paddingRight: respDims('16rpx', 24)
                            })}
                      setChatHistory={setChatHistory}
                      ttsConfig={ttsConfig}
                      chatMode={mode}
                      onRetry={() => resendMessage(item.dataId!)}
                      onAddUserLike={
                        item.feedbackType == FeedbackTypeEnum.Downvote
                          ? undefined
                          : () => {
                              if (!item.dataId || !chatId || !appId) return;

                              const feedbackType =
                                item.feedbackType == FeedbackTypeEnum.Upvote
                                  ? FeedbackTypeEnum.None
                                  : FeedbackTypeEnum.Upvote;

                              setChatHistory((state) =>
                                state.map((chatItem) =>
                                  chatItem.dataId === item.dataId
                                    ? {
                                        ...chatItem,
                                        feedbackType
                                      }
                                    : chatItem
                                )
                              );

                              updateChatItem({
                                dataId: item.dataId,
                                feedbackType
                              });
                            }
                      }
                      onAddUserDislike={(() => {
                        if (item.feedbackType == FeedbackTypeEnum.Upvote) {
                          return;
                        }
                        if (item.feedbackType == FeedbackTypeEnum.Downvote) {
                          return () => {
                            if (!item.dataId || !chatId || !appId) return;
                            setChatHistory((state) =>
                              state.map((chatItem) =>
                                chatItem.dataId === item.dataId
                                  ? {
                                      ...chatItem,
                                      feedbackType: FeedbackTypeEnum.None,
                                      customFeedback: ''
                                    }
                                  : chatItem
                              )
                            );
                            updateChatItem({
                              dataId: item.dataId,
                              feedbackType: FeedbackTypeEnum.None,
                              customFeedback: ''
                            });
                          };
                        } else {
                          return () => setFeedbackId(item.dataId);
                        }
                      })()}
                    />
                  )}
                </Box>

                {chatGenerating && index === chatHistory.length - 1 && (
                  <>
                    {parseLoading ? (
                      <Box
                        visibility="hidden"
                        ml={respDims('8rpx', 4)}
                        mt={respDims('32rpx', 16)}
                        px={respDims('36rpx', 18)}
                        py={respDims('6rpx', 3)}
                        color="#606266"
                        fontSize={respDims('28rpx', '14fpx')}
                        lineHeight={respDims('44rpx', '22fpx')}
                        bgColor="#F2F3F5"
                        cursor="pointer"
                        borderRadius={respDims('16rpx', 8)}
                      ></Box>
                    ) : (
                      <Box
                        ml={respDims('8rpx', 4)}
                        mt={respDims('32rpx', 16)}
                        px={respDims('36rpx', 18)}
                        py={respDims('6rpx', 3)}
                        color="#606266"
                        fontSize={respDims('28rpx', '14fpx')}
                        lineHeight={respDims('44rpx', '22fpx')}
                        bgColor="#F2F3F5"
                        cursor="pointer"
                        borderRadius={respDims('16rpx', 8)}
                        onClick={() => abortSendMessage()}
                      >
                        停止生成
                      </Box>
                    )}
                  </>
                )}
              </Flex>
            </Flex>
          </>
        )}
      </Box>
    </>
  );
}

function AIContentCard({
  chatValue,
  dataId,
  isLastChild,
  isChatting,
  questionGuides,
  index
}: {
  dataId: string;
  chatValue: ChatItemValueItemType[];
  isLastChild: boolean;
  isChatting: boolean;
  questionGuides: string[];
  index: number;
}) {
  const RenderQuestionGuide = ({ questionGuides }: { questionGuides: string[] }) => {
    return (
      <Markdown
        source={`\`\`\`${CodeClassName.questionGuide}
  ${JSON.stringify(questionGuides)}`}
      />
    );
  };

  return (
    <Flex flexDirection={'column'} gap={2}>
      {chatValue.map((value, i) => {
        const key = `${dataId}-ai-${i}`;

        return (
          <AIResponseBox
            key={key}
            value={value}
            isLastResponseValue={isLastChild && i === chatValue.length - 1}
            isChatting={isChatting}
            index={index}
          />
        );
      })}
      {isLastChild && questionGuides.length > 0 && (
        <RenderQuestionGuide questionGuides={questionGuides} />
      )}
    </Flex>
  );
}
