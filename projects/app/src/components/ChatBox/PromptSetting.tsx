import React, { useContext, useMemo, useRef, useState } from 'react';
import { Box, Button, Flex, ModalFooter } from '@chakra-ui/react';
import { getPromptList, reorderPrompt } from '@/api/prompt';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { TableProps } from 'antd/es/table';
import { HolderOutlined } from '@ant-design/icons';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext } from '@dnd-kit/core';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Table } from 'antd';
import { useMutation, useQuery } from '@tanstack/react-query';
import { PromptType } from '@/types/api/prompt';

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      variant="link"
      leftIcon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

const Row: React.FC<RowProps> = (props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: props['data-row-key'] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {})
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners]
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

const PromptSetting = ({
  onClose,
  onSuccess,
  appId
}: {
  appId?: string;
  onClose: () => void;
  onSuccess: () => void;
}) => {
  const { t } = useTranslation();

  const [prompts, setPrompts] = React.useState<PromptType[]>([]);

  const sortMapRef = useRef<Record<string, number>>({});

  const { isFetching: isPromptLoading } = useQuery(
    ['prompts'],
    () => getPromptList({ tenantAppId: appId || '' }),
    {
      onSuccess: (data) => {
        const listData = data
          .map((item: any) => ({
            ...item,
            sort: sortMapRef.current[item.id] ?? item.sort
          }))
          .sort((l, r) => l.sort - r.sort);
        setPrompts(listData);
      }
    }
  );

  const { mutate: onSubmit, isLoading: isSubmiting } = useMutation({
    mutationFn: () => {
      const items = Object.entries(sortMapRef.current).map(([id, sort]) => ({ id, sort }));

      return items.length
        ? reorderPrompt({ param: items }).then(() => true)
        : Promise.resolve(false);
    },
    onSuccess(changed) {
      changed && onSuccess();
      onClose();
    }
  });

  const loading = isPromptLoading || isSubmiting;

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setPrompts((prevState) => {
        const newData = arrayMove(
          prevState,
          prevState.findIndex((record) => record.promptId === active.id),
          prevState.findIndex((record) => record.promptId === over?.id)
        );
        newData.forEach((item, index) => {
          sortMapRef.current[item.id] = index;
        });
        return newData;
      });
    }
  };

  const columns: TableProps['columns'] = [
    {
      title: '快捷指令',
      key: 'promptTitle',
      dataIndex: 'promptTitle',
      width: 250,
      render: (_, record: PromptType) => (
        <>
          <DragHandle />
          {record.promptTitle}
        </>
      )
    },
    {
      title: '介绍',
      key: 'description',
      dataIndex: 'description'
    }
  ];

  return (
    <MyModal
      iconSrc="/imgs/app/home_settingScene.svg"
      title={'快捷指令顺序'}
      isOpen
      w="800px"
      maxW="800px"
      h="75vh"
      onClose={onClose}
      isCentered
    >
      {prompts.length > 0 ? (
        <Box
          p="0 23px 23px 23px"
          marginTop="23px"
          h="100%"
          overflowX="hidden"
          overflowY="auto"
          css={{
            '& .ant-table-thead': {
              position: 'sticky',
              top: 0,
              background: '#F9FAFB',
              zIndex: 3
            },
            '& .ant-table-cell': {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
            <SortableContext
              items={prompts.map((i) => i.promptId)}
              strategy={verticalListSortingStrategy}
            >
              <Table
                columns={columns}
                components={{ body: { row: Row } }}
                dataSource={prompts}
                loading={loading}
                pagination={false}
                rowKey={(record) => record.promptId}
              />
            </SortableContext>
          </DndContext>
        </Box>
      ) : (
        <Flex h="75vh" alignItems="center" justifyContent="center">
          <Box color="#7e7f80">暂无数据</Box>
        </Flex>
      )}

      <ModalFooter>
        <Button mr={3} onClick={() => onSubmit()}>
          {t('common.Finish')}
        </Button>

        <Button variant={'grayBase'} onClick={onClose}>
          {t('common.Close')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default PromptSetting;
