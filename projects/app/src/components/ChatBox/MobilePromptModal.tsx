import React from 'react';
import {
  Button,
  Input,
  FormControl,
  FormLabel,
  Textarea,
  Flex,
  Box,
  ModalBody,
  Center
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { rpxDim } from '@/utils/chakra';
import { promptCenterCreate, promptCenterUpdate } from '@/api/prompt';
import { useRequest } from '@/hooks/useRequest';
import { serviceSideProps } from '@/utils/i18n';
import MyModal from '../MyModal';
import SvgIcon from '../SvgIcon';
import { useSystemStore } from '@/store/useSystemStore';

export type PromptDataType = {
  id?: string;
  appId?: string;
  promptTitle?: string;
  inputContent?: string;
  description?: string;
  proContent?: string;
  hiddenContent?: string;
};

type FormType = {
  promptTitle: string;
  description: string;
  inputContent: string;
  proContent: string;
  hiddenContent: string;
};

const MobilePromptModal = ({
  prompt,
  onClose,
  onSuccess
}: {
  prompt: PromptDataType;
  isRoot: boolean;
  onClose: () => void;
  onSuccess: () => void;
}) => {
  const { register, handleSubmit } = useForm<FormType>({
    defaultValues: {
      promptTitle: prompt.promptTitle || '快捷指令1',
      inputContent: prompt.inputContent || '',
      proContent: prompt.proContent || '',
      description: prompt.description || '',
      hiddenContent: prompt.hiddenContent || ''
    }
  });
  const { isPc } = useSystemStore();
  const isAdd = !prompt.id;
  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      const params = {
        id: prompt.id || '',
        tenantAppId: prompt.appId || '',
        promptTitle: data.promptTitle,
        description: data.description,
        inputContent: data.inputContent || '',
        proContent: data.proContent || '',
        hiddenContent: data.hiddenContent || ''
      };
      return isAdd ? promptCenterCreate(params) : promptCenterUpdate(params);
    },
    onSuccess() {
      onSuccess();
      onClose();
    },
    successToast: '操作成功'
  });

  return (
    <MyModal
      minH="100%"
      minW="100%"
      {...(!isPc && { borderRadius: '0' })}
      title={
        <>
          <Flex flex="1">
            <SvgIcon
              name="chevronLeft"
              w={rpxDim(48)}
              h={rpxDim(48)}
              cursor="pointer"
              onClick={onClose}
              alignSelf="start"
            />
          </Flex>
          <Center color="rgba(0,0,0,0.9)" fontSize="18px" fontWeight="bold">
            {!isAdd ? '编辑' : '添加'}快捷指令
          </Center>
        </>
      }
      isOpen
      isCentered={!isPc}
      overflow="hidden"
      headerStyle={{
        backgroundImage: '/imgs/home/<USER>'
      }}
    >
      <ModalBody>
        <Box
          h="85vh"
          overflow="auto"
          pt={rpxDim(30)}
          pr={rpxDim(18)}
          pb={rpxDim(88)}
          pl={rpxDim(18)}
        >
          <FormControl>
            <FormLabel color="#4E5969">
              <Box
                fontSize={rpxDim(32)}
                pos="relative"
                _after={{
                  content: '"*"',
                  color: '#F53F3F',
                  ml: rpxDim(8),
                  pos: 'absolute',
                  top: rpxDim(8)
                }}
              >
                指令名称
              </Box>
            </FormLabel>
            <Input
              bgColor="#F6F6F6"
              fontSize={rpxDim(28)}
              _placeholder={{
                color: '#86909C',
                fontSize: rpxDim(28)
              }}
              autoFocus
              autoComplete="off"
              placeholder="如：诗意创作"
              {...register('promptTitle', {
                required: '请输入名称'
              })}
            />
          </FormControl>

          <FormControl mt="22px">
            <FormLabel fontWeight="400" color="#4E5969" fontSize="14px">
              指令介绍
            </FormLabel>
            <Textarea
              bgColor="#F6F6F6"
              rows={2}
              placeholder="请输入指令介绍,如“为文创作品项目推荐合适的选址”"
              {...register('description')}
            />
          </FormControl>

          <FormControl mt={rpxDim(32)}>
            <Flex alignItems="center">
              <FormLabel color="#4E5969">
                <Box
                  fontSize={rpxDim(32)}
                  pos="relative"
                  _after={{
                    content: '"*"',
                    color: '#F53F3F',
                    ml: rpxDim(8),
                    pos: 'absolute',
                    top: rpxDim(8)
                  }}
                >
                  指令内容
                </Box>
              </FormLabel>
            </Flex>
            <Textarea
              bgColor="#F6F6F6"
              _placeholder={{
                color: '#86909C',
                fontSize: rpxDim(28)
              }}
              rows={2}
              placeholder="如：请创作诗歌,形式：[], 主题：[],人物:[]"
              {...register('inputContent', { required: '请输入引导' })}
            />
          </FormControl>

          <FormControl mt={rpxDim(32)}>
            <FormLabel color="#4E5969" fontSize={rpxDim(32)} mb={rpxDim(14)}>
              增强提示词
            </FormLabel>
            <Textarea
              bgColor="#F6F6F6"
              _placeholder={{
                color: '#86909C',
                fontSize: rpxDim(28)
              }}
              rows={9}
              placeholder={
                '如：你是一个创作诗人，擅长通过诗歌来表达情感、描绘景象、讲述故事，具有丰富的想象力和对文字的独特驾驭能力。\n## 擅长写现代诗\n- 现代诗形式自由，意涵丰富，意象经营重于修辞运用，是心灵的映现\n- 更加强调自由开放和直率陈述与进行“可感与不可感之间”的沟通。'
              }
              {...register('hiddenContent')}
            />
          </FormControl>

          <Flex position="fixed" bottom={rpxDim(40)} alignItems="center" w="100%">
            <Button
              fontSize={rpxDim(32)}
              color="#3D7FFF"
              mr={rpxDim(26)}
              backgroundColor="#EDF3FF"
              h={rpxDim(80)}
              w={rpxDim(320)}
              onClick={onClose}
            >
              取消
            </Button>
            <Button
              fontSize={rpxDim(32)}
              color="#fff"
              isLoading={creating}
              onClick={handleSubmit((data) => onClickConfirm(data))}
              h={rpxDim(80)}
              w={rpxDim(320)}
            >
              确认
            </Button>
          </Flex>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export async function getServerSideProps(context: any) {
  const prompt = context?.query?.prompt;
  const isRoot = context?.query?.isRoot;

  return {
    props: { prompt, isRoot, ...(await serviceSideProps(context)) }
  };
}

export default React.memo(MobilePromptModal);
