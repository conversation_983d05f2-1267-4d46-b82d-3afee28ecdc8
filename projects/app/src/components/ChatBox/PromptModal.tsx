import React, { useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>oot<PERSON>,
  ModalBody,
  Input,
  FormControl,
  FormLabel,
  Textarea,
  Flex,
  Box
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import MyModal from '@/components/MyModal';
import { useSystemStore } from '@/store/useSystemStore';
import { promptCenterCreate, promptCenterUpdate } from '@/api/prompt';
import { useRequest } from '@/hooks/useRequest';
import SvgIcon from '../SvgIcon';
import InteractEditor, { EditorModeEnum, EditorRef } from '@/components/InteractEditor';
import { SvgIconNameType } from '../SvgIcon/data';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { TenantWorkflowProcess } from '@/types/api/workflow';
import { Toast } from '@/utils/ui/toast';
import MyTooltip from '../MyTooltip';

export type PromptDataType = {
  id?: string;
  appId?: string;
  promptTitle?: string;
  description?: string;
  inputContent?: string;
  proContent?: string;
  hiddenContent?: string;
  type?: number;
};

type FormType = {
  promptTitle: string;
  description: string;
  inputContent: string;
  proContent: string;
  hiddenContent: string;
  type: number;
};

enum ContentTypeEnum {
  simple = 'simple',
  pro = 'pro'
}

const contentTypes = [
  {
    label: '简洁',
    value: ContentTypeEnum.simple
  },
  {
    label: '专业',
    value: ContentTypeEnum.pro
  }
];

const PromptModal = ({
  prompt,
  onClose,
  onSuccess
}: {
  prompt: PromptDataType;
  onClose: () => void;
  onSuccess: () => void;
}) => {
  const { register, setValue, getValues, handleSubmit } = useForm<FormType>({
    defaultValues: {
      promptTitle: prompt.promptTitle || '快捷指令1',
      inputContent: prompt.inputContent || '',
      proContent: prompt.proContent || '',
      description: prompt.description || '',
      hiddenContent: prompt.hiddenContent || '',
      type: prompt.type
    }
  });
  const isAdd = !prompt.id;
  const [contentType, setContentType] = useState(
    prompt.type === 1 ? ContentTypeEnum.simple : ContentTypeEnum.pro
  );

  const editorRef = useRef<EditorRef>(null);
  const { steps, updateWorkflowProcess } = useWorkflowStore();
  const editorTools: { icon: SvgIconNameType; label: string; callback: () => void }[] = [
    {
      icon: 'plus',
      label: '文本框',
      callback: () => editorRef.current?.insertTextInput({})
    },
    {
      icon: 'plus',
      label: '单选输入框',
      callback: () => editorRef.current?.insertSelectInput({})
    }
  ];

  const onSwitchContentType = (value: ContentTypeEnum) => {
    setContentType(value);
  };

  const { mutate: onClickConfirm, isLoading: creating } = useRequest({
    mutationFn: async (data: FormType) => {
      if (contentType === ContentTypeEnum.pro && !data.proContent) {
        Toast.warning('请输入引导');
        return Promise.reject();
      }
      const params = {
        id: prompt.id || '',
        tenantAppId: prompt.appId || '',
        promptTitle: data.promptTitle,
        description: data.description,
        inputContent: data.inputContent || '',
        proContent: data.proContent || '',
        hiddenContent: data.hiddenContent || '',
        type: contentType === ContentTypeEnum.pro ? 2 : 1
      };
      return isAdd ? promptCenterCreate(params) : promptCenterUpdate(params).then((res) => params);
    },
    onSuccess(res) {
      if (!isAdd) {
        updateWorkflowProcess(res);
      }
      onSuccess();
      onClose();
    },
    successToast: '操作成功'
  });

  return (
    <MyModal
      title={
        <Flex alignItems="center">
          <SvgIcon name="chatMagicWand2" w="26px" h="26px" />
          <Box ml="4px" color="rgba(0,0,0,0.9)" fontSize="18px" fontWeight="bold">
            {!isAdd ? '编辑' : '添加'}快捷指令
          </Box>
        </Flex>
      }
      isOpen
      w="600px"
      // isCentered={!isPc}
      bgImage="/imgs/dataset/createModalBg.svg"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent',
        borderBottom: '1px solid #E7E7E7',
        padding: '20px 32px'
      }}
    >
      <ModalBody>
        <FormControl>
          <FormLabel fontWeight="400" color="#4E5969" fontSize="14px" mt="22px">
            <Box
              pos="relative"
              _after={{
                content: '"*"',
                color: '#F53F3F',
                pos: 'absolute',
                top: '0'
              }}
            >
              指令名称
            </Box>
          </FormLabel>
          <Input
            autoFocus
            autoComplete="off"
            placeholder="如:诗意创作"
            bgColor="#F6F6F6"
            {...register('promptTitle', {
              required: '请输入名称'
            })}
          />
        </FormControl>

        <FormControl mt="22px">
          <FormLabel fontWeight="400" color="#4E5969" fontSize="14px">
            指令介绍
          </FormLabel>
          <Textarea
            bgColor="#F6F6F6"
            rows={2}
            placeholder="请输入指令介绍,如“为文创作品项目推荐合适的选址”"
            {...register('description')}
          />
        </FormControl>

        <FormControl mt="22px">
          <Flex pb="10px" alignItems="center">
            <FormLabel fontWeight="400" color="#4E5969" fontSize="14px">
              <Flex alignItems="center">
                <Box
                  pos="relative"
                  mr="12px"
                  _after={{
                    content: '"*"',
                    color: '#F53F3F',
                    pos: 'absolute',
                    top: '0'
                  }}
                >
                  指令内容
                </Box>
                <MyTooltip label="通过操作简洁和专业开关，以控制所使用的快捷指令的类型，以满足您的不同的需求。">
                  <SvgIcon mt="6px" color="#909399" name="file2Query" w="18px" h="18px" />
                </MyTooltip>
              </Flex>
            </FormLabel>

            <Box flex="1"></Box>

            {contentTypes.map((it, index) => (
              <Button
                key={it.value}
                w="71px"
                h="26px"
                colorScheme={it.value === contentType ? 'primary' : 'gray'}
                variant="solid"
                fontSize="14px"
                borderRadius={
                  index === 0 ? '50px' : index === contentTypes.length - 1 ? '50px' : '0 0 0 0'
                }
                onClick={() => onSwitchContentType(it.value)}
                sx={{
                  bg: it.value === contentType ? 'primary.500' : '#F3F4F6',
                  _hover: {
                    bg: it.value === contentType ? 'primary.600' : '#E2E8F0'
                  }
                }}
              >
                {it.label}
              </Button>
            ))}
          </Flex>
          {contentType === ContentTypeEnum.simple && (
            <Textarea
              rows={2}
              bgColor="#F6F6F6"
              placeholder="如：诗歌主题"
              borderRadius="8px"
              {...register('inputContent', { required: '请输入引导' })}
            />
          )}
          {contentType === ContentTypeEnum.pro && (
            <Box>
              <InteractEditor
                placeholder="如:请创作诗歌,形式：[], 主题：[],人物:[]"
                ref={editorRef}
                mode={EditorModeEnum.design}
                fontSize="14px"
                borderRadius="8px"
                minH="100px"
                maxH="200px"
                bgColor="#F6F6F6"
                value={getValues('proContent')}
                onChange={(val) => setValue('proContent', val)}
              />
              <Flex mt="10px" alignItems="center">
                {editorTools.map((it) => (
                  <Button
                    key={it.label}
                    h="28px"
                    mr="12px"
                    colorScheme="gray"
                    variant="solid"
                    color="#4E5969"
                    bg="#F6F6F6"
                    borderRadius="4px"
                    onClick={it.callback}
                  >
                    <SvgIcon name={it.icon} w="12px" h="12px" />
                    <Box ml="8px">{it.label}</Box>
                  </Button>
                ))}
              </Flex>
            </Box>
          )}
        </FormControl>

        <FormControl mt="22px">
          <FormLabel fontWeight="400" color="#4E5969" fontSize="14px">
            增强提示词
          </FormLabel>
          <Textarea
            rows={3}
            bgColor="#F6F6F6"
            placeholder={
              '如:你是一个创作诗人，擅长通过诗歌来表达情感、描绘景象、讲述故事，具有丰富的想象力和对文字的独特驾驭能力。\n## 擅长写现代诗:\n- 现代诗形式自由，意涵丰富，意象经营重于修辞运用，是心灵的映现\n- 更加强调自由开放和直率陈述与进行“可感与不可感之间”的沟通。'
            }
            {...register('hiddenContent')}
          />
        </FormControl>
      </ModalBody>

      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          关闭
        </Button>
        <Button isLoading={creating} onClick={handleSubmit((data) => onClickConfirm(data))}>
          完成
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default PromptModal;
