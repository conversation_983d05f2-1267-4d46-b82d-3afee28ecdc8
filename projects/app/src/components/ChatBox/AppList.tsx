import { APP_ICON, DataSourceMap } from '@/constants/common';
import { useAppStore } from '@/store/useAppStore';
import { AppListItemType } from '@/types/api/app';
import { respDims } from '@/utils/chakra';
import {
  Box,
  Center,
  ChakraProps,
  Flex,
  Image,
  keyframes,
  useOutsideClick
} from '@chakra-ui/react';
import {
  ForwardedRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import SvgIcon from '../SvgIcon';
import styles from '@/styles/variable.module.scss';

export type AppListRef = {
  onKeyEvent: (key: string) => void;
};

const animIn = keyframes`
  from { visibility: hidden; max-height: 0; }
  to { visibility: visible; max-height: 800px; }
`;

const animOut = keyframes`
  from { visibility: visible; max-height: 800px; }
  to { visibility: hidden; max-height: 0; }
`;

const animOpen = keyframes`
  from { visibility: visible; max-height: 800px; }
  to { visibility: visible; max-height: 0; }
`;

const animClose = keyframes`
  from { visibility: hidden; max-height: 0; }
  to { visibility: hidden; max-height: 0; }
`;

const animUp = keyframes`
  from { max-height: 0; }
  to { max-height: 800px; }
`;

const animDown = keyframes`
  from { max-height: 800px; }
  to { max-height: 0; }
`;

export const AppList = (
  {
    isOpen,
    currentId,
    filterText,
    onClose,
    onSelect,
    ...props
  }: {
    isOpen?: boolean;
    currentId?: string;
    filterText?: string;
    onClose?: () => void;
    onSelect?: (app: AppListItemType) => void;
  } & ChakraProps,
  ref: ForwardedRef<AppListRef>
) => {
  const { myApps } = useAppStore();

  const boxRef = useRef<HTMLDivElement>(null);

  const [selectedIndex, setSelectedIndex] = useState(0);

  const animated = useRef(false);

  const anims = useMemo(() => {
    let anims = { inOut: '', upDown: '' };
    if (isOpen) {
      if (animated.current) {
        anims.inOut = animIn;
        anims.upDown = animUp;
      } else {
        anims.inOut = animOpen;
        anims.upDown = '';
      }
    } else {
      if (animated.current) {
        anims.inOut = animOut;
        anims.upDown = animDown;
      } else {
        anims.inOut = animClose;
        anims.upDown = '';
      }
    }
    animated.current = true;
    return anims;
  }, [isOpen]);

  const apps = useMemo(() => {
    const lowerFilterText = filterText?.toLowerCase();
    const apps = lowerFilterText
      ? myApps.filter((it) => it.name.toLocaleLowerCase().includes(lowerFilterText))
      : myApps;
    return apps
      .map((it) => {
        const nameSpans: { text: string; matched?: boolean }[] = [];
        if (lowerFilterText) {
          let start = 0;
          let index = 0;
          const lowerName = it.name.toLocaleLowerCase();
          while ((index = lowerName.indexOf(lowerFilterText, start)) >= 0) {
            start < index && nameSpans.push({ text: lowerName.substring(start, index) });
            nameSpans.push({
              text: lowerName.substring(index, index + lowerFilterText.length),
              matched: true
            });
            start = index + lowerFilterText.length;
          }
          start < lowerName.length && nameSpans.push({ text: lowerName.substring(start) });
        } else {
          nameSpans.push({ text: it.name });
        }
        return { ...it, nameSpans };
      })
      .sort((l, r) =>
        l.source !== r.source ? l.source - r.source : r.updateTime.localeCompare(l.updateTime)
      );
  }, [myApps, filterText]);

  useOutsideClick({ enabled: isOpen, ref: boxRef, handler: onClose });

  const scrollItemIntoView = (index: number) => {
    const elem = document.getElementById(`chat-app-list-${apps[index].id}`);
    const parent = elem?.parentElement;
    if (!parent) {
      return;
    }
    const top = elem.offsetTop - parent.offsetTop;
    if (top < parent.scrollTop) {
      parent.scrollTop = top;
    } else if (top + elem.offsetHeight > parent.scrollTop + parent.clientHeight) {
      parent.scrollTop = top + elem.offsetHeight - parent.clientHeight;
    }
  };

  const onKeyEvent = (key: string) => {
    if (key === 'ArrowDown' || key === 'ArrowUp') {
      const index =
        key === 'ArrowDown'
          ? Math.min(selectedIndex + 1, apps.length - 1)
          : Math.max(selectedIndex - 1, 0);
      setSelectedIndex(index);
      scrollItemIntoView(index);
    } else if (key === 'Enter' && selectedIndex >= 0 && selectedIndex < apps.length) {
      onSelect?.(apps[selectedIndex]);
    }
  };

  useImperativeHandle(ref, () => ({
    onKeyEvent
  }));

  useEffect(() => {
    const index = apps.findIndex((it) => it.id === currentId);
    setSelectedIndex(index > 0 ? index : 0);
  }, [apps, currentId]);

  return (
    <Box
      ref={boxRef}
      onClick={(e) => e.stopPropagation()}
      {...props}
      overflow="hidden"
      animation={`${anims.inOut} .3s forwards`}
      bgColor="#F9F9F9"
      tabIndex={0}
      onKeyDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onKeyEvent(e.key);
      }}
      borderRadius={respDims('16fpx')}
    >
      <Flex align="center" px={respDims('24fpx')} py={respDims('14fpx')}>
        <SvgIcon name="chatApp" w={respDims('16fpx')} h={respDims('16fpx')} />

        <Box
          ml={respDims('8fpx')}
          color="#303133"
          fontSize={respDims('15fpx')}
          lineHeight={respDims('22fpx')}
          fontWeight="bold"
        >
          应用中心
        </Box>

        <SvgIcon
          ml="auto"
          name="close"
          w={respDims('22fpx')}
          h={respDims('22fpx')}
          cursor="pointer"
          _hover={{
            color: '#3366ff'
          }}
          onClick={onClose}
        />
      </Flex>

      {apps.length > 0 ? (
        <Box
          flex="1 0 0"
          mr={[`-${styles.scrollbarSmWidth}`, `-${styles.scrollbarWidth}`]}
          px={respDims('20rpx', '12fpx')}
          overflow="hidden"
        >
          <Box maxH={respDims('240fpx')} px={respDims('12fpx')} overflowY="scroll">
            {apps.map((it, index) => (
              <Flex
                id={`chat-app-list-${it.id}`}
                key={it.id}
                mb="4px"
                align="center"
                h={respDims('48fpx')}
                px={respDims('24fpx')}
                borderRadius={respDims(8, 8)}
                whiteSpace="nowrap"
                cursor="pointer"
                {...(index === selectedIndex
                  ? {
                      bgColor: '#f1f1f1',
                      border: '1px solid #F8FAFC'
                    }
                  : {
                      border: '1px solid transparent'
                    })}
                _hover={{ bgColor: '#f1f1f1', border: '1px solid #F8FAFC' }}
                onClick={() => it.appId != currentId && onSelect?.(it)}
              >
                <Image
                  src={it.avatarUrl || APP_ICON}
                  w={respDims('30fpx')}
                  h={respDims('30fpx')}
                  alt=""
                  mr={respDims(10)}
                  borderRadius="50%"
                />
                {it.nameSpans.map((it, nameIndex) => (
                  <Box
                    key={nameIndex}
                    color={it.matched ? '#3366ff' : '#303133'}
                    fontSize={respDims('16fpx')}
                    {...(index === selectedIndex && { fontWeight: 'bold' })}
                  >
                    {it.text}
                  </Box>
                ))}

                <Box
                  ml={respDims('14fpx')}
                  color="#838383"
                  fontSize={respDims('14fpx')}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  mr={respDims('16fpx')}
                >
                  {it.intro}
                </Box>

                <Box ml="auto" color="#303133" fontSize={respDims('13fpx')}>
                  {DataSourceMap[it.source]?.label}
                </Box>
              </Flex>
            ))}
          </Box>
        </Box>
      ) : (
        <Center>
          <Box color="#303133" fontSize="16px" fontWeight="500" py="32px">
            未找到应用
          </Box>
        </Center>
      )}
    </Box>
  );
};

export default forwardRef(AppList);
