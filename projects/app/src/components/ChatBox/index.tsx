import React, {
  useCallback,
  useRef,
  useState,
  useMemo,
  forwardRef,
  useImperativeHandle,
  ForwardedRef,
  useEffect,
  DragEvent
} from 'react';
import Script from 'next/script';
import { throttle } from 'lodash';
import type { ExportChatType, ParseResultProps } from '@/types/chat.d';
import { useAudioPlay } from '@/utils/voice';
import { getErrText } from '@/utils/string';
import { useCopyData } from '@/hooks/useCopyData';
import { useMindMapStore } from '@/store/useMindMapStore';
import { Spin } from 'antd';
import {
  Box,
  Flex,
  BoxProps,
  FlexProps,
  Textarea,
  ChakraProps,
  Center,
  Image,
  Card,
  Input,
  Checkbox,
  useDisclosure,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  keyframes
} from '@chakra-ui/react';
import { EventNameEnum, eventBus } from '@/utils/eventbus';
import { Controller, FieldValues, UseFormReturn, useForm } from 'react-hook-form';
import { htmlTemplate } from '@/constants/chat';
import { useRouter } from 'next/router';
import { useSystemStore } from '@/store/useSystemStore';
import { useDeepEditStore } from '@/store/useDeepEdit';
import { useTranslation } from 'next-i18next';
import { customAlphabet } from 'nanoid';
import {
  chatSearchDataset,
  convertHtmlToPdf,
  convertHtmlToPdfOld,
  convertHtmlToWord,
  fetchChatPptUrl,
  parseUploadFile,
  updateChatItem,
  updateChatUserFeedback
} from '@/api/chat';
import { streamFetch, type StreamResponseType } from '@/utils/fetch';
import Markdown, { CodeClassName } from '@/components/Markdown';
import MyTooltip from '../MyTooltip';
import dynamic from 'next/dynamic';
import PDFTemplate from './PDFTemplate';
import FileInCloud from './FileInCloud';
const FeedbackModal = dynamic(() => import('./FeedbackModal'));
const ReadFeedbackModal = dynamic(() => import('./ReadFeedbackModal'));
const ResponseTags = dynamic(() => import('./ResponseTags'));

import MessageInput, {
  ChatAppType,
  MessageFileType,
  MessageInputRef,
  MessagePromptType,
  RawInputType,
  fileTypeInfos
} from './MessageInput';
import { respDims, rpxDim } from '@/utils/chakra';
import Lottie from '@/components/Lottie';
import { getDropFiles } from '@/utils/drop';
import SvgIcon from '../SvgIcon';
import { PromptExternalTypeEnum } from '@/constants/api/prompt';
import styles from '@/styles/variable.module.scss';
import { formatFileSize } from '@/utils/tools';
import { useChatStore } from '@/store/useChatStore';
import { Toast } from '@/utils/ui/toast';
import { cloneWithStyles, convertHtmlToXhtml } from '@/utils/html';
import { ChatBoxRef, StartChatFnProps, generatingMessageProps } from './type';
import { chats2GPTMessages, updateChatListWithParseResult } from '@/fastgpt/global/core/chat/adapt';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  ChatStatusEnum,
  FeedbackTypeEnum,
  IMG_BLOCK_KEY
} from '@/fastgpt/global/core/chat/constants';
import { SseResponseEventEnum } from '@/fastgpt/global/core/workflow/runtime/constants';
import {
  AIChatItemValueItemType,
  ChatSiteItemType,
  UserChatItemValueItemType
} from '@/fastgpt/global/core/chat/type';
import {
  AppChatConfigType,
  AppTTSConfigType,
  VariableItemType
} from '@/fastgpt/global/core/app/type';
import {
  accessFileUrlWithFilename,
  checkIsInteractiveByHistories,
  getParseResult,
  processImageAndFileType,
  replacePreWithDiv,
  setUserSelectResultToHistories
} from '@/utils/chat';
import { useQuery } from '@tanstack/react-query';
import { getFileList } from '@/api/file';
import useCompDom from '@/hooks/useCompDom';
import { ChatFileType } from '@/types/api/chat';
import { downloadFile } from '@/utils/file';
import { VariableInputEnum } from '@/fastgpt/global/core/workflow/constants';
import MySelect from '../MySelect';
import { APP_ICON } from '@/constants/common';
import { useAppStore } from '@/store/useAppStore';
import { ChatBoxMode } from './constant';
import CustomTour from '@/components/CustomTour';
import useFilePreview from '@/hooks/useFilePreview';
import FilePreviewModal from '@/components/FilePreviewModal';
import AIResponseBox, { RenderResoningContent } from './components/AIResponseBox';
import ChatItemPDFTemplate from './ChatItemPDFTemplate';
import ChatItemWordTemplate from './ChatItemWordTemplate';
const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyz1234567890', 24);
import { isBaoAnDomain } from '@/components/Layout/Sidebar/constants';
import MyMenu from '../MyMenu';

// Import new components
import ChatController from './components/ChatController';
import Empty from './components/Empty';
import ChatAvatar from './components/ChatAvatar';
import VariableInput from './components/VariableInput';
import WelcomeText from './components/WelcomeText';
import ChatItem from './components/ChatItem';

// 添加微信 JS-SDK 的类型声明
declare global {
  interface Window {
    wx: {
      config: (config: {
        debug: boolean;
        appId: string;
        timestamp: string | number;
        nonceStr: string;
        signature: string;
        jsApiList: string[];
      }) => void;
      ready: (callback: () => void) => void;
      error: (callback: (err: any) => void) => void;
    };
  }
}
import { AppContextDetailType } from '@/fastgpt/global/core/dataset/type';
import { MiniProgramTip } from './components/MiniProgramTip';

const shine = keyframes`
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
`;

enum AuthorityTypeEnum {
  Invalid = 0,
  Valid = 1
}

const parseFileRegex = /``` parseFiles([\s\S]*?)```([\s\S]*)/;

const chatEditModeStyle: BoxProps = {
  width: respDims('180rpx', 119),
  height: respDims('34rpx'),
  fontWeight: 500,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  background: 'linear-gradient(90deg, #FFDE8D 0%, #FCE5AD 100%)',
  borderRadius: respDims('50rpx', 25),
  fontSize: respDims('24rpx', '12fpx'),
  color: '#9F642D'
};

const fullscreenKey = 'lk-fullscreen';

type ChatCompleteItemType = {
  type: ChatItemValueTypeEnum;
  reasoning?: {
    content: string;
  };
  text?: {
    content: string;
  };
};
type Props = {
  showMarkIcon?: boolean; // admin mark dataset
  showEmptyIntro?: boolean;
  appAvatar?: string;
  userAvatar?: string;
  useVision?: boolean;
  useInternet?: boolean;
  active?: boolean; // can use
  chatConfig?: AppChatConfigType;
  isShare?: boolean;
  onShare?: () => void;
  onCancelShare?: () => void;
  mode?: ChatBoxMode;
  // not chat test params
  appId?: string;
  chatId?: string;
  content?: string;
  shareId?: string;
  outLinkUid?: string;
  onUpdateVariable?: (e: Record<string, any>) => void;

  onStartChat?: (e: StartChatFnProps) => Promise<
    StreamResponseType & {
      isNewChat?: boolean;
    }
  >;
  onDelMessage?: (e: { contentId: string }) => Promise<boolean>;
  onUpdateChatItem?: (e: {
    responseChatItemId: string;
    value: string;
    responseData: string;
  }) => Promise<any>;
};

export const parseFileAndInput = (
  text: string
): {
  filesResult: ParseResultProps;
  userChatInput: string;
} => {
  // 使用正则表达式解析内容
  const match = text?.match(parseFileRegex);
  let filesResult = [] as any;
  let userChatInput = text;
  if (match) {
    if (match[1].trim()) {
      filesResult = JSON.parse(match[1].trim() || '{}');
    }
    userChatInput = match[2].trim(); // 提取后面的用户输入
  } else {
    console.log('没有匹配到相应的内容');
  }
  return {
    filesResult,
    userChatInput
  };
};

const ChatBox = (
  {
    showMarkIcon = false,
    showEmptyIntro = false,
    appAvatar,
    userAvatar,
    useVision,
    useInternet,
    active = true,
    chatConfig,
    appId,
    chatId,
    content,
    shareId,
    outLinkUid,
    onUpdateVariable,
    onStartChat,
    onDelMessage,
    onUpdateChatItem,
    isShare,
    onShare,
    onCancelShare: onCancelShare,
    mode = ChatBoxMode.Chat,
    ...props
  }: Props & ChakraProps,
  ref: ForwardedRef<ChatBoxRef>
) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const lastScrollTopRef = useRef(0);
  const [fileLoadingMap, setFileLoadingMap] = useState<Record<string, boolean>>({});
  const router = useRouter();
  const { copyData } = useCopyData();
  const { t } = useTranslation();
  const { isPc, setLoading, feConfigs } = useSystemStore();
  const messageInputRef = useRef<MessageInputRef>(null);
  const chatController = useRef<AbortController>();
  const questionGuideController = useRef(new AbortController());
  const isNewChatReplace = useRef(false);
  const {
    chatData,
    setChatFiles: setChatFileStore,
    setChatHistory: setChatHistoryStore,
    initChatInputs,
    appContextDetail,
    setAppContextDetail
  } = useChatStore();

  const setStoreChatHistory = mode === ChatBoxMode.Chat ? setChatHistoryStore : () => {};
  const setStoreChatFiles = mode === ChatBoxMode.Chat ? setChatFileStore : () => {};
  // Workflow running, there are user input or selection

  const { previewFile, modalVisible, iframeSrc, modalTitle, closeModal } = useFilePreview();

  const { isOpen, onOpen, onClose } = useDisclosure();

  const [, setRefresh] = useState(false);
  const [isSelectAllShare, setIsSelectAllShare] = useState(false);
  const [chatHistory, setChatHistory] = useState<ChatSiteItemType[]>([]);
  const [feedbackId, setFeedbackId] = useState<string>();
  const [readFeedbackData, setReadFeedbackData] = useState<{
    chatItemId: string;
    content: string;
  }>();
  const [questionGuides, setQuestionGuide] = useState<string[]>([]);

  const [draggingFile, setDraggingFile] = useState(false);
  const draggingFileCounter = useRef(0);

  const [chatItemEditDataId, setChatItemEditDataId] = useState<string>();
  const [chatItemEditValue, setChatItemEditValue] = useState<string>();
  const [parseLoading, setParseLoading] = useState(false);
  const [fileContentLoading, setFileContentLoading] = useState(false);
  const [isDomain, setIsDomain] = useState(false);

  const chatItemEditRef = useRef<HTMLTextAreaElement>(null);

  const pendingResendData = useRef<{ dataId: string; inputVal?: string }>();

  const isChatting = useMemo(
    () =>
      chatHistory[chatHistory.length - 1] &&
      chatHistory[chatHistory.length - 1]?.status !== 'finish',
    [chatHistory]
  );

  const isInteractive = useMemo(() => checkIsInteractiveByHistories(chatHistory), [chatHistory]);

  const { setViewApp } = useAppStore();

  const { onExportChat } = useChatBox();

  // const { getFastGPTData, getSystemConfig } = useSystemStore();
  // const { data: fastGPTData } = useQuery(['fastGPTData'], () => getFastGPTData(true));

  const [fullscreen, setFullscreen] = useState(
    () => localStorage.getItem(fullscreenKey) === 'true'
  );

  const maxWidth = fullscreen ? '100%' : '768px';

  useEffect(() => {
    if (initChatInputs && router.query.init && messageInputRef.current) {
      const query = { ...router.query };
      delete query.init;
      router.replace({
        pathname: '/home',
        query
      });
      messageInputRef.current?.setRawInput(initChatInputs as RawInputType);
    }
  }, [initChatInputs, messageInputRef, router]);

  useEffect(() => {
    if (isBaoAnDomain()) {
      setIsDomain(true);
    } else {
      setIsDomain(false);
    }
  }, [window.location]);

  // 存到store
  useEffect(() => {
    setStoreChatHistory(chatHistory);
  }, [chatHistory]);

  const { welcomeText, variableList, questionGuide, ttsConfig, allVariableList } = useMemo(
    () => ({
      welcomeText: chatConfig?.welcomeText || '',
      variableList:
        chatConfig?.variables?.filter((it) => it.type !== VariableInputEnum.custom) || [],
      allVariableList: chatConfig?.variables || [],

      questionGuide: '',
      ttsConfig: chatConfig?.ttsConfig
    }),
    [chatConfig]
  );

  const variablesForm = useForm();

  const [chatFiles, setChatFiles] = useState<ChatFileType[]>([]);

  const chatFileMap = useMemo(
    () =>
      chatFiles.reduce((map, it) => ((map[it.id] = it), map), {} as Record<string, ChatFileType>),
    [chatFiles]
  );

  const initChatFileIds = useMemo(() => {
    const ids = [] as string[];
    chatHistory.forEach((it) => {
      it.value?.forEach((it) => {
        it.type;
        if (
          it.type === ChatItemValueTypeEnum.file &&
          it.file?.type === ChatFileTypeEnum.file &&
          !chatFileMap[it.file?.fileId!]
        ) {
          ids.push(it.file?.fileId!);
        }
      });
    });
    return ids;
  }, [chatHistory, chatFileMap]);

  // 滚动到底部
  const scrollToBottom = (behavior: 'smooth' | 'auto' = 'smooth') => {
    if (!contentRef.current) return;
    if (behavior === 'auto') {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    } else {
      contentRef.current.scrollTo({
        top: contentRef.current.scrollHeight,
        behavior
      });
    }
    lastScrollTopRef.current = contentRef.current.scrollTop;
  };

  // 聊天信息生成中……获取当前滚动条位置，判断是否需要滚动到底部
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const generatingScroll = useCallback(
    throttle(() => {
      if (!contentRef.current) return;
      const isBottom = contentRef.current.scrollTop >= lastScrollTopRef.current - 100;
      isBottom && scrollToBottom('auto');
    }, 50),
    []
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const generatingMessage = useCallback(
    ({
      event,
      text = '',
      reasoningText,
      status,
      name,
      tool,
      interactive,
      variables
    }: generatingMessageProps) => {
      setChatHistory((state) =>
        state.map((item, index) => {
          if (index !== state.length - 1) return item;
          if (item.obj !== ChatRoleEnum.AI) return item;
          const lastValue: AIChatItemValueItemType =
            item.value && item.value.length
              ? JSON.parse(JSON.stringify(item.value![item.value!.length - 1]))
              : '';
          if (event === SseResponseEventEnum.flowNodeStatus && status) {
            return {
              ...item,
              status,
              moduleName: name
            };
          } else if (event === SseResponseEventEnum.answer && reasoningText) {
            if (lastValue.type === ChatItemValueTypeEnum.reasoning && lastValue.reasoning) {
              lastValue.reasoning.content += reasoningText;
              return {
                ...item,
                value: item.value.slice(0, -1).concat(lastValue)
              };
            } else {
              const val: AIChatItemValueItemType = {
                type: ChatItemValueTypeEnum.reasoning,
                reasoning: {
                  content: reasoningText
                }
              };
              return {
                ...item,
                value: item.value.concat(val)
              };
            }
          } else if (
            (event === SseResponseEventEnum.answer || event === SseResponseEventEnum.fastAnswer) &&
            text
          ) {
            if (!lastValue || !lastValue.text) {
              const newValue: AIChatItemValueItemType = {
                type: ChatItemValueTypeEnum.text,
                text: {
                  content: text
                }
              };
              return {
                ...item,
                value: item.value.concat(newValue)
              };
            } else {
              lastValue.text.content += text;
              return {
                ...item,
                value: item.value.slice(0, -1).concat(lastValue)
              };
            }
          } else if (event === SseResponseEventEnum.toolCall && tool) {
            const val: AIChatItemValueItemType = {
              type: ChatItemValueTypeEnum.tool,
              tools: [tool]
            };
            return {
              ...item,
              value: item.value.concat(val)
            };
          } else if (
            event === SseResponseEventEnum.toolParams &&
            tool &&
            lastValue.type === ChatItemValueTypeEnum.tool &&
            lastValue?.tools
          ) {
            lastValue.tools = lastValue.tools.map((item) => {
              if (item.id === tool.id) {
                item.params += tool.params;
              }
              return item;
            });
            return {
              ...item,
              value: item.value.slice(0, -1).concat(lastValue)
            };
          } else if (event === SseResponseEventEnum.toolResponse && tool) {
            // replace tool response
            return {
              ...item,
              value: item.value.map((val) => {
                if (val.type === ChatItemValueTypeEnum.tool && val.tools) {
                  const tools = val.tools.map((item) =>
                    item.id === tool.id ? { ...item, response: tool.response } : item
                  );
                  return {
                    ...val,
                    tools
                  };
                }
                return val;
              })
            };
          } else if (event === SseResponseEventEnum.updateVariables && variables) {
            variablesForm.setValue('variables', variables);
          } else if (event === SseResponseEventEnum.interactive && interactive) {
            const val: AIChatItemValueItemType = {
              type: ChatItemValueTypeEnum.interactive,
              interactive
            };

            return {
              ...item,
              value: item.value.concat(val)
            };
          }

          return item;
        })
      );
      generatingScroll();
    },
    [generatingScroll, setChatHistory, variablesForm]
  );

  const getHistory = useCallback(() => {
    return chatHistory.map((item) => {
      return {
        ...item
      };
    });
  }, [chatHistory]);

  // 重置输入内容
  const resetInputVal = useCallback((val: string | RawInputType) => {
    setTimeout(() => {
      messageInputRef.current?.setRawInput(
        typeof val === 'string'
          ? {
              inputVal: val
            }
          : val
      );
      setRefresh((state) => !state);
    }, 100);
  }, []);

  const abortSendMessage = useCallback((reason: 'stop' | 'cancel' = 'stop') => {
    chatController.current?.abort(reason);
    chatController.current = undefined;
  }, []);

  const removeMessageById = useCallback(
    (dataId?: string) => {
      if (!dataId || !onDelMessage) {
        return;
      }
      // 在删除未结束对话的记录时可能出现先调了删除接口但后端completions未结束，
      // 在completions结束时会保存记录，所以导致未实际删除
      let count = 0;
      const tryRemove = (): any =>
        mode == ChatBoxMode.Chat &&
        ++count < 10 &&
        onDelMessage({ contentId: dataId }).then((res) => {
          !res && setTimeout(tryRemove, count * 1000);
        });
      tryRemove();
    },
    [onDelMessage]
  );

  const removeMessageAt = useCallback(
    (index: number) => {
      // 同时删除用户和AI的聊天记录

      let humanDataId: string | undefined;
      let aiDataId: string | undefined;
      if (chatHistory[index]?.obj === 'Human') {
        humanDataId = chatHistory[index].dataId;
        if (chatHistory[index + 1]?.obj === 'AI') {
          aiDataId = chatHistory[index + 1].dataId;
          if (chatHistory[index + 1].status !== 'finish') {
            abortSendMessage('cancel');
          }
        }
      } else if (chatHistory[index]?.obj === 'AI') {
        if (chatHistory[index - 1]?.obj === 'Human') {
          humanDataId = chatHistory[index - 1].dataId;
        }
        if (chatHistory[index].status !== 'finish') {
          abortSendMessage('cancel');
        }
        aiDataId = chatHistory[index].dataId;
      }

      if (!humanDataId && !aiDataId) {
        return;
      }

      removeMessageById(humanDataId);
      removeMessageById(aiDataId);

      setChatHistory((state) =>
        state.filter((chat) => chat.dataId !== humanDataId && chat.dataId !== aiDataId)
      );
    },
    [chatHistory, removeMessageById, abortSendMessage]
  );

  const sendMessage = useCallback(
    ({
      ocrFileKey,
      chatApp,
      inputVal = '',
      files,
      prompt,
      images,
      rawInput,
      history = chatHistory,
      isInteractivePrompt = false,
      hideInUI = false
    }: {
      ocrFileKey?: string;
      chatApp?: ChatAppType;
      inputVal?: string;
      prompt?: MessagePromptType;
      files?: MessageFileType[];
      images?: MessageFileType[];
      rawInput?: RawInputType;
      history?: ChatSiteItemType[];
      hideInUI?: boolean;
      isInteractivePrompt?: boolean;
      appContextDetail?: AppContextDetailType;
    }): Promise<void> => {
      // 处理图片和文件类型，经常文件当做图片处理，没找到具体原因，先处理下
      const { newImages, newFiles } = processImageAndFileType(files || [], images || []);
      files = newFiles;
      images = newImages;

      if (inputVal === '蓝青666进入全屏') {
        if (
          window.origin.includes('learnking') ||
          window.origin.includes('-pre') ||
          window.origin.includes('-test') ||
          window.origin.includes('localhost')
        ) {
          setFullscreen(true);
          localStorage.setItem(fullscreenKey, 'true');
        }
      } else if (inputVal === '蓝青666退出全屏') {
        setFullscreen(false);
        localStorage.removeItem(fullscreenKey);
      }

      if (prompt?.externalType === PromptExternalTypeEnum.PPT) {
        fetchChatPptUrl({ topic: inputVal })
          .then((res) => {
            resetInputVal('');
            window.open(res, '_ppt');
          })
          .catch(() => {
            resetInputVal(rawInput || inputVal);
            Toast.error({
              title: '发送失败'
            });
          });
        return Promise.resolve();
      }

      return variablesForm.handleSubmit(async (variables) => {
        if (!onStartChat) return;
        if (isChatting) {
          !hideInUI &&
            Toast.warning({
              title: '正在聊天中...请等待结束'
            });
          return;
        }
        questionGuideController.current?.abort('stop');
        // get input value
        const val = inputVal.trim();

        if (!val && !images?.length && !files?.length) {
          Toast.warning({
            title: '内容为空'
          });
          return;
        }

        const humanDataId = nanoid();
        const aiDataId = nanoid();
        let newChatList: ChatSiteItemType[] = [
          ...history,
          {
            dataId: humanDataId,
            obj: ChatRoleEnum.Human,
            isShareContent: false,
            ocrFileKey: ocrFileKey,
            chatAppId: chatApp?.id,
            chatAppName: chatApp?.name,
            chatAppAvatarUrl: chatApp?.avatarUrl,
            hideInUI: hideInUI,
            value: [
              ...(files?.map((file) => ({
                type: ChatItemValueTypeEnum.file,
                file: {
                  type: ChatFileTypeEnum.file,
                  name: file.name || '',
                  url: accessFileUrlWithFilename(file.fileUrl!, file.name!),
                  fileId: file.fileKey || ''
                }
              })) || []),
              ...(images?.map((image) => ({
                type: ChatItemValueTypeEnum.file,
                file: {
                  type: ChatFileTypeEnum.image,
                  url: accessFileUrlWithFilename(image.fileUrl!, image.name!)
                }
              })) || []),
              ...(prompt && prompt.hiddenContent
                ? [
                    {
                      type: ChatItemValueTypeEnum.prompt,
                      prompt: {
                        content: prompt.hiddenContent
                      }
                    }
                  ]
                : []),
              ...(val
                ? [
                    {
                      type: ChatItemValueTypeEnum.text,
                      text: {
                        content: val
                      }
                    }
                  ]
                : [])
            ] as UserChatItemValueItemType[],
            status: 'finish'
          },
          {
            dataId: aiDataId,
            obj: ChatRoleEnum.AI,
            ocrFileKey: ocrFileKey,
            isShareContent: false,
            chatAppId: chatApp?.id,
            chatAppName: chatApp?.name,
            chatAppAvatarUrl: chatApp?.avatarUrl,
            value: [
              {
                type: ChatItemValueTypeEnum.text,
                text: {
                  content: ''
                }
              }
            ],
            status: 'loading'
          }
        ];
        // 插入内容

        setChatHistory(
          isInteractivePrompt
            ? // 把交互的结果存储到对话记录中，交互模式下，不需要新的会话轮次
              setUserSelectResultToHistories(newChatList.slice(0, -2), inputVal)
            : newChatList
        );

        // 清空输入内容
        resetInputVal('');
        setQuestionGuide([]);
        setTimeout(() => {
          scrollToBottom();
        }, 100);

        // Only declared variables are kept
        const requestVariables: Record<string, any> = {};
        let parseResult: ParseResultProps | undefined | null;
        allVariableList.forEach((item) => {
          requestVariables[item.key] = variables[item.key] || '';
        });
        let sendMessage = inputVal;
        try {
          // create abort obj
          const abortSignal = new AbortController();
          chatController.current = abortSignal;
          // 用户输入value,不用带文件解析，后端会查出来没有带的
          const originValue = JSON.stringify(newChatList[newChatList.length - 2].value);
          // 解析文件 start
          try {
            setParseLoading(true);
            parseResult = await getParseResult({
              files: files || [],
              images: images || [],
              ocrFileKey,
              appId: appId || '',
              chatId: chatId || '',
              chatConfig
            });

            newChatList = await updateChatListWithParseResult({
              parseResult: parseResult!,
              newChatList,
              humanDataId,
              inputVal: inputVal.trim(),
              ocrFileKey: ocrFileKey || ''
            });
          } catch (error) {
            console.log(error);
          } finally {
            setParseLoading(false);
          }
          // 解析文件 end
          const messages = chats2GPTMessages({
            messages: newChatList,
            reserveId: true
          });
          setFileContentLoading(true);
          const fileContentListPromise = appContextDetail?.files.length
            ? Promise.all(
                appContextDetail.files
                  .filter((item) => item.authority !== AuthorityTypeEnum.Invalid)
                  .map(async (item) => {
                    try {
                      const res = await parseUploadFile({ fileKey: item.fileKey });
                      return {
                        fileContent: res.fileContent,
                        fileName: item.fileName!
                      };
                    } catch (error) {
                      console.error('Error parsing file:', error);
                      return { fileContent: '', fileName: '' };
                    }
                  })
              )
            : Promise.resolve([]);

          const spaceContentListPromise =
            appContextDetail?.spaces.length &&
            appContextDetail.spaces.filter((item) => item.authority !== AuthorityTypeEnum.Invalid)
              .length
              ? chatSearchDataset({
                  messages: [
                    {
                      dataId: nanoid(),
                      content: inputVal,
                      role: 'user'
                    }
                  ],
                  spaceIds:
                    appContextDetail.spaces
                      .filter((item) => item.authority !== AuthorityTypeEnum.Invalid)
                      .map((item) => item.spaceId) || []
                }).catch((error) => {
                  console.error('Error searching dataset:', error);
                  return [];
                })
              : Promise.resolve([]);

          const [fileContentList, spaceContentList] = await Promise.all([
            fileContentListPromise,
            spaceContentListPromise.then((res) => res || [])
          ]);

          setFileContentLoading(false);

          const { responseData, isNewChat = false } = await onStartChat({
            ocrFileKey: ocrFileKey,
            chatAppId: chatApp?.id,
            value: originValue,
            content: sendMessage,
            quotedRef:
              fileContentList.length &&
              fileContentList.every((item) => item.fileContent && item.fileName)
                ? fileContentList
                : undefined,
            searchSelectedRef: spaceContentList.length ? spaceContentList : undefined,
            fileKeys: [
              ...(files?.map((file) => file.fileKey!) || []),
              ...(images?.filter((image) => image.fileKey)?.map((image) => image.fileKey!) || [])
            ],
            messages: messages.slice(0, -1),
            responseChatItemId: aiDataId,
            controller: abortSignal,
            getHistory: getHistory,
            generatingMessage: (e) => {
              generatingMessage({ ...e });
            },
            variables: requestVariables,
            inputVal: sendMessage,
            rawInput: rawInput,
            rawParseResult: parseResult,
            // 不产生新的聊天记录
            single: isInteractivePrompt ? 1 : undefined
          });

          if (responseData?.[responseData.length - 1]?.error) {
            Toast.error({
              title: t(responseData[responseData.length - 1].error?.message)
            });
          }

          isNewChatReplace.current = isNewChat;

          // set finish status
          setChatHistory((state) =>
            state.map((item, index) => {
              if (index !== state.length - 1) return item;
              onUpdateChatItem?.({
                responseChatItemId: item.dataId,
                value: JSON.stringify(item.value),
                responseData: JSON.stringify(responseData)
              });

              return {
                ...item,
                status: 'finish',
                responseData
              };
            })
          );
        } catch (err: any) {
          err &&
            Toast.error({
              title: t(getErrText(err, 'core.chat.error.Chat error')),
              duration: 5000,
              isClosable: true
            });

          if (ocrFileKey) {
            resetInputVal('');
            setChatHistory(newChatList.slice(0, newChatList.length - 2));
          } else if (!err?.responseText) {
            resetInputVal(rawInput || inputVal);
            setChatHistory(newChatList.slice(0, newChatList.length - 2));
          }

          // set finish status
          setChatHistory((state) =>
            state.map((item, index) => {
              if (index !== state.length - 1) return item;
              return {
                ...item,
                status: 'finish'
              };
            })
          );
        }
      })();
    },
    [
      chatHistory,
      generatingMessage,
      isChatting,
      onStartChat,
      resetInputVal,
      allVariableList,
      t,
      variablesForm,
      chatConfig,
      generatingScroll,
      isChatting,
      isPc,
      onStartChat,
      resetInputVal,
      scrollToBottom,
      appId,
      chatId
    ]
  );

  // useFormCreate(sendMessage);

  const resendMessage = useCallback(
    async (dataId: string, inputVal?: string) => {
      if (!onDelMessage) return;

      if (isChatting) {
        abortSendMessage('cancel');
        pendingResendData.current = { dataId, inputVal };
        return;
      }

      let index = chatHistory.findIndex((it) => it.dataId === dataId);
      while (index >= 0 && chatHistory[index].obj !== 'Human') {
        index--;
      }
      if (index < 0) {
        return;
      }

      const delHistory = chatHistory.slice(index);

      setLoading(true);

      try {
        ChatBoxMode.Chat == mode && delHistory.forEach((item) => removeMessageById(item.dataId));
        setChatHistory((state) => (index === 0 ? [] : state.slice(0, index)));

        const item = delHistory[0];

        if (item?.obj !== ChatRoleEnum.Human) {
          return;
        }

        const promptValue = item.value
          .filter((it) => it.type === ChatItemValueTypeEnum.prompt)
          .map((it) => ({
            hiddenContent: it.prompt?.content || ''
          }));

        const files = item.value
          .filter(
            (it) =>
              it.type === ChatItemValueTypeEnum.file && it.file?.type === ChatFileTypeEnum.file
          )
          .map((it) => ({
            name: it.file?.name || '',
            fileUrl: it.file?.url || '',
            fileKey: it.file?.fileId || ''
          }));

        const images = item.value
          .filter(
            (it) =>
              it.type === ChatItemValueTypeEnum.file && it.file?.type === ChatFileTypeEnum.image
          )
          .map((it) => ({
            fileUrl: it.file?.url || ''
          }));
        const { filesResult, userChatInput } = parseFileAndInput(
          item.value
            .map((it) =>
              it.type === ChatItemValueTypeEnum.text
                ? it.text?.content.replace(parseFileRegex, '$2')
                : ''
            )
            .join('')
        );

        sendMessage({
          ocrFileKey: item.ocrFileKey,
          chatApp: item.chatAppId
            ? { id: item.chatAppId, name: item.chatAppName!, avatarUrl: item.chatAppAvatarUrl! }
            : undefined,
          inputVal: inputVal ?? userChatInput,
          files,
          images,
          ...(promptValue && {}),
          history: chatHistory.slice(0, index)
        });
      } catch (error) {
        console.log(error);
      }
      setLoading(false);
    },
    [
      mode,
      chatHistory,
      onDelMessage,
      removeMessageById,
      sendMessage,
      setLoading,
      abortSendMessage,
      isChatting
    ]
  );

  const onStartChatItemEdit = (item: ChatSiteItemType) => {
    setChatItemEditDataId(item.dataId);
    let text =
      item.text ||
      item.value
        .filter((it) => it.type === ChatItemValueTypeEnum.text && it.text?.content)
        .map((it) => it.text?.content)
        .join('\n');
    text = text ? text.replace(new RegExp(`\`\`\`${IMG_BLOCK_KEY}.*\`\`\`\\s?`, 'gs'), '') : '';

    // 解析内容
    // 使用正则表达式解析内容
    const { filesResult, userChatInput } = parseFileAndInput(text);
    text = userChatInput.trim();

    setChatItemEditValue(userChatInput);
  };

  const onConfirmChatItemEdit = () => {
    if (!chatItemEditDataId || !chatItemEditValue?.trim()) return;
    const index = chatHistory.findIndex((it) => it.dataId === chatItemEditDataId);
    if (index < 0) {
      return;
    }
    const history = chatHistory[index];
    const m = history.text
      ? history.text.match(new RegExp(`\`\`\`${IMG_BLOCK_KEY}.*\`\`\``, 'gs'))
      : '';
    resendMessage(
      chatItemEditDataId,
      m?.[0] ? `${m?.[0]}\n${chatItemEditValue}` : chatItemEditValue
    );
    setChatItemEditDataId(undefined);
    setChatItemEditValue(undefined);
  };

  const onCancelChatItemEdit = () => {
    setChatItemEditDataId(undefined);
    setChatItemEditValue(undefined);
  };

  const onAddPrompt = (val: string) => {
    messageInputRef.current?.addPrompt(val);
  };

  const onDragEnter = () => {
    draggingFileCounter.current++;
  };

  const onDragLeave = () => {
    if (--draggingFileCounter.current === 0) {
      setDraggingFile(false);
    }
  };

  const onDragOver = (e: DragEvent) => {
    e.preventDefault();
    const accepted = e?.dataTransfer?.items?.[0]?.kind === 'file';
    e.dataTransfer.dropEffect = accepted ? 'copy' : 'none';
    setDraggingFile(accepted);
  };

  const onDrop = (e: DragEvent) => {
    e.preventDefault();
    draggingFileCounter.current = 0;
    if (e.dataTransfer?.items?.length) {
      setDraggingFile(false);
      getDropFiles(Array.from(e.dataTransfer.items)).then((files) => {
        messageInputRef.current?.addFile(files.map((it) => it.rawFile));
      });
    }
  };

  // output data
  useImperativeHandle(ref, () => ({
    getChatHistories: () => chatHistory,
    resetVariables(variables) {
      const data = variablesForm.getValues();
      for (const key in data) {
        data[key] = '';
      }
      variablesForm.reset({
        ...data,
        ...variables
      });
    },
    resetHistory(e) {
      setChatHistory(e);
    },
    scrollToBottom,
    resetInputVal // 新增这一行
  }));

  /* style start */
  const showEmpty = useMemo(
    () =>
      feConfigs?.show_emptyChat &&
      showEmptyIntro &&
      chatHistory.length === 0 &&
      !variableList?.length &&
      !welcomeText,
    [
      chatHistory.length,
      feConfigs?.show_emptyChat,
      showEmptyIntro,
      variableList?.length,
      welcomeText
    ]
  );

  const cancelShare = () => {
    setChatHistory((state) => state.map((item) => ({ ...item, isShareContent: false })));
    setIsSelectAllShare(false);
    if (onCancelShare) {
      onCancelShare();
    }
  };

  const onCopyContent = () => {
    const filteredData = chatHistory.filter((item) => item.isShareContent);
    if (filteredData.length === 0) {
      Toast.error('请选择对话内容');
      return;
    }
    const dataToCopy = filteredData.map((item) => {
      if (!item.text && !item.value) {
        return '';
      }

      const texts = item.text
        ? [item.text]
        : item.value
            .filter((it) => it.type === 'text' && it.text?.content)
            .map((it) => it.text?.content);

      return texts.join('\n\n');
    });

    copyData(dataToCopy.map((item) => item).join('\n\n'));
  };

  const handleExportChat = async (type: 'pdf' | 'md') => {
    const filteredData = chatHistory.filter((item) => item.isShareContent);
    if (filteredData.length === 0) {
      Toast.error('请选择对话内容');
      return;
    }
    setLoading(true);
    try {
      await onExportChat({
        type,
        history: filteredData,
        filename: chatData.customTitle || chatData.title
      });
    } finally {
      setLoading(false);
    }
  };

  useQuery(['getChatFiles', initChatFileIds], () => getFileList(initChatFileIds), {
    enabled: initChatFileIds?.length > 0,
    onSuccess: (res) => {
      let data =
        res?.map((it) => {
          const type = it.fileName.substring(it.fileName.lastIndexOf('.') + 1).toLocaleLowerCase();
          const typeInfo = fileTypeInfos.find((it) => it.name === type);
          return {
            id: it.fileKey,
            name: it.fileName,
            svgIcon: typeInfo?.svgIcon,
            imgIcon: it.fileUrl,
            type,
            sizeText: formatFileSize(it.fileSize).replaceAll(' ', '')
          };
        }) || [];
      if (onCancelShare) {
        onCancelShare();
      }
      setChatFiles((state) => [...state, ...data]);
      setStoreChatFiles(data);
    }
  });

  // page change and abort request
  useEffect(() => {
    isNewChatReplace.current = false;
    setQuestionGuide([]);
    return () => {
      chatController.current?.abort('leave');
      if (!isNewChatReplace.current) {
        questionGuideController.current?.abort('leave');
      }
    };
  }, [router.query]);

  // add listener
  useEffect(() => {
    const windowMessage = ({ data }: MessageEvent<{ type: 'sendPrompt'; text: string }>) => {
      if (data?.type === 'sendPrompt' && data?.text) {
        sendMessage({
          inputVal: data.text
        });
      }
    };
    window.addEventListener('message', windowMessage);

    eventBus.on(
      EventNameEnum.sendQuestion,
      ({
        text,
        files,
        images,
        ocrFileKey,
        isInteractivePrompt,
        chatApp
      }: {
        text: string;
        files: any;
        images: any;
        ocrFileKey: any;
        chatApp: any;
        isInteractivePrompt: boolean;
      }) => {
        if (!text) return;
        sendMessage({
          inputVal: text,
          files,
          images,
          ocrFileKey,
          isInteractivePrompt
        });
      }
    );
    eventBus.on(EventNameEnum.editQuestion, ({ text }: { text: string }) => {
      if (!text) return;
      resetInputVal(text);
    });

    return () => {
      window.removeEventListener('message', windowMessage);
      eventBus.off(EventNameEnum.sendQuestion);
      eventBus.off(EventNameEnum.editQuestion);
    };
  }, [resetInputVal, sendMessage]);

  useEffect(() => {
    if (!chatItemEditRef.current) {
      return;
    }
    const pos = chatItemEditRef.current.value.length;
    chatItemEditRef.current.selectionStart = pos;
    chatItemEditRef.current.selectionEnd = pos;
  }, [chatItemEditDataId]);

  useEffect(() => {
    if (!chatItemEditRef.current) {
      return;
    }
    chatItemEditRef.current.style.width = '1em';
    chatItemEditRef.current.style.whiteSpace = 'nowrap';

    const style = getComputedStyle(chatItemEditRef.current);

    chatItemEditRef.current.style.width =
      chatItemEditRef.current.offsetHeight -
      chatItemEditRef.current.clientHeight +
      parseInt(style.getPropertyValue('padding-right')) +
      chatItemEditRef.current.scrollWidth +
      2 +
      'px';
    chatItemEditRef.current.style.whiteSpace = 'normal';

    chatItemEditRef.current.style.height = '1em';
    chatItemEditRef.current.style.height =
      chatItemEditRef.current.offsetHeight -
      chatItemEditRef.current.clientHeight +
      chatItemEditRef.current.scrollHeight +
      'px';
  }, [chatItemEditDataId, chatItemEditValue]);

  useEffect(() => {
    if (!isChatting && pendingResendData.current) {
      const data = pendingResendData.current;
      pendingResendData.current = undefined;
      resendMessage(data.dataId, data.inputVal);
    }
  }, [isChatting, resendMessage]);

  useEffect(() => {
    setChatHistory((state) =>
      state.map((item) => ({ ...item, isShareContent: isSelectAllShare ? true : false }))
    );
  }, [isSelectAllShare]);

  useEffect(() => {
    content && chatData.appId === appId && sendMessage({ inputVal: content });
  }, [content, chatData.appId]);

  return (
    <Flex
      position="relative"
      flexDir="column"
      alignItems="center"
      {...props}
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      onDragOver={onDragOver}
      onDrop={onDrop}
    >
      <FilePreviewModal
        visible={modalVisible}
        onClose={closeModal}
        iframeSrc={iframeSrc}
        title={modalTitle} // 传递标题
      />
      <Script src="/js/html2pdf.bundle.min.js" strategy="lazyOnload"></Script>

      <Box
        ref={contentRef}
        flex="1"
        w="100%"
        overflow="scroll"
        pl={respDims(32)}
        pr={'30px!important'}
        {...(!isPc && {
          pb: rpxDim(108),
          pr: respDims('50rpx', 50)
        })}
      >
        <Flex
          id="chat-container"
          flexDir="column"
          alignItems="center"
          mr={[`-${styles.scrollbarSmWidth}`, `-${styles.scrollbarWidth}`]}
        >
          <Box w="100%" {...(isPc && { maxW: maxWidth })}>
            {showEmpty && <Empty />}
            {/* 用户引导对话开场白 */}

            {!!welcomeText && (
              <WelcomeText
                appAvatar={appAvatar}
                welcomeText={welcomeText}
                onClickAvatar={() => setViewApp(appId)}
              />
            )}

            {!!variableList?.length && (
              <VariableInput
                appAvatar={appAvatar}
                variableList={variableList}
                variablesForm={variablesForm}
              />
            )}

            {/* 用户聊天消息列表 */}
            <Box id={'history'} pt={respDims('32rpx', 32)}>
              {chatHistory.map((item, index) => (
                <>
                  <ChatItem
                    key={item.dataId || index}
                    item={item}
                    chatId={chatId}
                    appId={appId}
                    chatConfig={chatConfig}
                    setFeedbackId={setFeedbackId}
                    chatFiles={chatFiles}
                    chatHistory={chatHistory}
                    userAvatar={userAvatar}
                    appAvatar={appAvatar}
                    onDelMessage={onDelMessage}
                    setChatHistory={setChatHistory}
                    fileContentLoading={fileContentLoading}
                    parseLoading={parseLoading}
                    questionGuides={questionGuides}
                    isShare={isShare}
                    shareId={shareId}
                    index={index}
                    mode={mode}
                    isChatting={isChatting}
                    chatItemEditDataId={chatItemEditDataId}
                    setChatItemEditDataId={setChatItemEditDataId}
                    resendMessage={resendMessage}
                    abortSendMessage={abortSendMessage}
                    onAddPrompt={onAddPrompt}
                  />
                </>
              ))}
            </Box>
          </Box>
        </Flex>
      </Box>
      <MiniProgramTip />

      {/* message input */}
      {onStartChat && active && (
        <Flex
          flexDir="column"
          alignItems="center"
          w="100%"
          px={respDims('0rpx', 32)}
          // pt={respDims('0rpx', 24)}
        >
          {!isShare ? (
            <>
              <MessageInput
                ref={messageInputRef}
                w="100%"
                {...(isPc && { maxW: maxWidth })}
                appId={appId}
                chatId={chatId}
                mode={mode}
                onSendMessage={sendMessage}
                isChatting={isChatting}
                useVision={useVision}
                useInternet={useInternet}
                chatConfig={chatConfig}
              />

              {isPc && (
                <Box
                  py={respDims(6)}
                  color="#A8ABB2"
                  fontSize={respDims('14fpx')}
                  lineHeight={respDims('28fpx')}
                >
                  内容由AI生成,请核查重要信息
                </Box>
              )}
            </>
          ) : (
            <Flex mb="31px" direction="column" w="80%" pl="32px" pr="32px">
              <Box mx={respDims(16)} w="100%" h={respDims(1)} bgColor="#D1D5DB" />
              <Flex mt="30px" justifyContent="space-around" alignItems="center">
                <Flex alignItems="center">
                  <Checkbox
                    colorScheme="primary"
                    isChecked={isSelectAllShare}
                    onChange={() => {
                      setIsSelectAllShare(!isSelectAllShare);
                    }}
                    size="lg"
                  />
                  <Box
                    color="#303133"
                    fontSize="15px"
                    fontWeight="400"
                    ml="16px"
                    onClick={() => {
                      setIsSelectAllShare(!isSelectAllShare);
                    }}
                    cursor="pointer"
                  >
                    全选
                  </Box>
                </Flex>
                <Flex>
                  <Flex direction="column" alignItems="center" cursor="pointer" mr="32px">
                    <Box
                      padding="6px 8px 4px 8px"
                      backgroundColor="#F3F4F6"
                      mb="8px"
                      borderRadius="50%"
                      onClick={() => onCopyContent()}
                    >
                      <SvgIcon name="chatCopyText" w="24px" h="24px" />
                    </Box>
                    <Box color="#303133" fontSize="14px" fontWeight="400">
                      复制文本
                    </Box>
                  </Flex>
                  <Flex direction="column" alignItems="center" cursor="pointer" mr="32px">
                    <Box
                      padding="8px 8px 4px 8px"
                      backgroundColor="#F3F4F6"
                      mb="8px"
                      borderRadius="50%"
                      onClick={() => handleExportChat('md')}
                    >
                      <SvgIcon name="chatExportMD" w="24px" h="24px" />
                    </Box>
                    <Box color="#303133" fontSize="14px" fontWeight="400">
                      导出md
                    </Box>
                  </Flex>
                  <Flex direction="column" alignItems="center" cursor="pointer" mr="32px">
                    <Box
                      padding="8px 8px 4px 8px"
                      backgroundColor="#F3F4F6"
                      mb="8px"
                      borderRadius="50%"
                      onClick={() => handleExportChat('pdf')}
                    >
                      <SvgIcon name="chatExportPDF" w="24px" h="24px" />
                    </Box>
                    <Box color="#303133" fontSize="14px" fontWeight="400">
                      导出pdf
                    </Box>
                  </Flex>
                </Flex>

                <Flex
                  direction="column"
                  alignItems="center"
                  cursor="pointer"
                  mr="32px"
                  onClick={() => {
                    cancelShare();
                  }}
                >
                  <Box
                    padding="8px 11px 6px 11px"
                    backgroundColor="#F3F4F6"
                    mb="8px"
                    borderRadius="50%"
                  >
                    <SvgIcon name="close" w="14px" h="14px" color="#505968" />
                  </Box>
                  <Box color="#303133" fontSize="15px" cursor="pointer">
                    取消
                  </Box>
                </Flex>
              </Flex>
            </Flex>
          )}
        </Flex>
      )}
      {/* user feedback modal */}
      {!!feedbackId && chatId && appId && (
        <FeedbackModal
          appId={appId}
          chatId={chatId}
          chatItemId={feedbackId}
          shareId={shareId}
          outLinkUid={outLinkUid}
          onClose={() => setFeedbackId(undefined)}
          onSuccess={(content: string) => {
            setChatHistory((state) =>
              state.map((item) =>
                item.dataId === feedbackId
                  ? { ...item, feedbackType: FeedbackTypeEnum.Downvote, customFeedback: content }
                  : item
              )
            );
            setFeedbackId(undefined);
          }}
        />
      )}
      {/* admin read feedback modal */}
      {!!readFeedbackData && (
        <ReadFeedbackModal
          content={readFeedbackData.content}
          onClose={() => setReadFeedbackData(undefined)}
          onCloseFeedback={() => {
            setChatHistory((state) =>
              state.map((chatItem) =>
                chatItem.dataId === readFeedbackData.chatItemId
                  ? { ...chatItem, userBadFeedback: undefined }
                  : chatItem
              )
            );
            try {
              if (!chatId || !appId) return;
              updateChatUserFeedback({
                tenantAppId: appId,
                chatId,
                chatItemId: readFeedbackData.chatItemId
              });
            } catch (error) {}
            setReadFeedbackData(undefined);
          }}
        />
      )}
      {draggingFile && (
        <Center
          flexDir="column"
          position="absolute"
          left="0"
          top="0"
          right="0"
          bottom="0"
          color="#303133"
          bgColor="primary.50"
          fontSize="14px"
          border="2px dotted primary.500"
          borderRadius={respDims(20)}
          zIndex="999"
        >
          <SvgIcon name="chatDrop" w={respDims(118)} h={respDims(80)} />
          <Box mt="21px">释放鼠标，上传文件到输入框</Box>
          <Box mt="10px">最多上传10个文件，每个文件不超过50M</Box>
        </Center>
      )}
    </Flex>
  );
};

export default React.memo(forwardRef(ChatBox));

export const useChatBox = () => {
  const { getCompDom } = useCompDom();

  const onExportChat = useCallback(
    async ({
      type,
      history,
      filename
    }: {
      type: ExportChatType;
      history: ChatSiteItemType[];
      filename?: string;
    }) => {
      const getHistoryHtml = async () => {
        const historyDom = await getCompDom(() => {
          return (
            <>
              <PDFTemplate history={history} />
            </>
          );
        });
        if (!historyDom) return;

        replacePreWithDiv(historyDom);
        const html = htmlTemplate.replace('{{CHAT_CONTENT}}', historyDom.outerHTML);
        const domParser = new DOMParser();
        const htmlDocument = domParser.parseFromString(html, 'text/html');
        // 设置表格样式
        htmlDocument.body.querySelectorAll('table').forEach((table) => {
          table.style.borderCollapse = 'collapse';
          table.style.border = '1px solid #000';
          table.querySelectorAll('th, td').forEach((cell) => {
            const htmlCell = cell as HTMLTableCellElement;
            if (!htmlCell.hasAttribute('style')) {
              htmlCell.style.padding = '3px 8px';
              htmlCell.style.border = '1px solid #000';
            }
          });
        });
        return htmlDocument.body.innerHTML;
      };

      const html = await getHistoryHtml();

      if (!html) return;

      const payload = {
        filename: `聊天记录-${filename || ''}`,
        htmlStr: html
      };

      const map: Record<ExportChatType, () => Promise<void> | void> = {
        md: () => {
          let content = history
            .map((item) => {
              let role = `${item.obj}:\n`;
              return (
                role +
                item.value
                  .map((item) => {
                    return item.text?.content;
                  })
                  .join('\n\n')
              );
            })
            .join('\n\n');
          downloadFile({
            content,
            type: 'text/markdown',
            filename: `聊天记录-${filename || ''}.md`
          });
        },
        html: async () => {
          const blob = new Blob([html], { type: 'text/html' });
          const url = window.URL.createObjectURL(blob);

          const a = document.createElement('a');
          a.href = url;
          a.download = `${payload.filename}.html`;
          a.click();
          window.URL.revokeObjectURL(url);
        },
        pdf: async () => {
          try {
            console.log('payload', payload);
            await convertHtmlToPdfOld(payload);
          } catch (error) {
            console.error('Error exporting to PDF:', error);
          }
        }
      };

      await map[type]();
    },
    []
  );

  return {
    onExportChat
  };
};
