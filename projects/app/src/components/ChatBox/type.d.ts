import { ChatCompletionMessageParam } from '@/fastgpt/global/core/ai/type';
import { ChatSiteItemType } from '@/fastgpt/global/core/chat/type';
import { SseResponseEventEnum } from '@/fastgpt/global/core/workflow/runtime/constants';
import { RawInputType } from './MessageInput';
import { ChatSearchDatasetResponse, ParseResultProps } from '@/types/chat';
import { WorkflowInteractiveResponseType } from '@/fastgpt/global/core/chat/interactive';

export type generatingMessageProps = {
  event: SseResponseEventEnum;
  text?: string;
  reasoningText?: string;
  name?: string;
  status?: 'running' | 'finish';
  tool?: ToolModuleResponseItemType;
  variables?: Record<string, any>;
  isAnswerDone?: boolean;
  interactive?: WorkflowInteractiveResponseType;
};

export type UserInputFileItemType = {
  url: string;
  content: string;
  fileId: string;
};

export type ChatBoxInputFormType = {
  input: string;
  files: UserInputFileItemType[];
  variables: Record<string, any>;
  chatStarted: boolean;
};

export type ChatBoxInputType = {
  text?: string;
  files?: UserInputFileItemType[];
  isInteractivePrompt?: boolean;
  hideInUI?: boolean;
};
export type ChatBoxCopyType = {
  text?: string;
  files?: string;
};

export type StartChatFnProps = {
  chatAppId?: string;
  ocrFileKey?: string;
  value: string;
  content: string;
  fileKeys: string[];
  messages: ChatCompletionMessageParam[];
  responseChatItemId: string;
  controller: AbortController;
  variables: Record<string, any>;
  inputVal: string;
  rawInput?: string | RawInputType;
  rawParseResult?: ParseResultProps | null;
  single?: number;
  reasoningText?: string;
  quotedRef?: { fileContent: string; fileName: string }[] | undefined;
  searchSelectedRef?: ChatSearchDatasetResponse[];
  generatingMessage: (e: generatingMessageProps) => void;
  getHistory?: () => ChatSiteItemType[];
};

export type ChatBoxRef = {
  getChatHistories: () => ChatSiteItemType[];
  resetVariables: (data?: Record<string, any>) => void;
  resetHistory: (history: ChatSiteItemType[]) => void;
  scrollToBottom: (behavior?: 'smooth' | 'auto') => void;
  resetInputVal: (rawInput: string | RawInputType) => void;
};

export type SendPromptFnType = (
  e: ChatBoxInputType & {
    autoTTSResponse?: boolean;
    history?: ChatSiteItemType[];
  }
) => void;

export type ComponentRef = {
  restartChat: () => void;
  scrollToBottom: (behavior?: 'smooth' | 'auto') => void;
};
