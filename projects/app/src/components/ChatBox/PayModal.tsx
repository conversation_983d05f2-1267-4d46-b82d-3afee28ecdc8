import React, { useEffect, useState } from 'react';
import { Box, Flex, Image } from '@chakra-ui/react';
import MyModal from '../MyModal';
import { useSystemStore } from '@/store/useSystemStore';
import { tocWxPayNative, tocWxPayResult } from '@/api/auth';
import QRCode from 'qrcode';
import { useToast } from '@/hooks/useToast';
import { respDims } from '@/utils/chakra';

const PayModal = ({ onClose }: { onClose: () => void }) => {
  const { isPc } = useSystemStore();
  const { toast } = useToast();
  const [codeUrl, setCodeUrl] = useState<string>('');
  const [amount, setAmount] = useState<number>(0);
  const [productName, setProductName] = useState<string>('');
  const [timer, setTimer] = useState(null as any);

  useEffect(() => {
    if (timer) {
      clearTimeout(timer);
      setTimer(null);
    }
    handlPayNative();
  }, []);

  const handlPayNative = () => {
    tocWxPayNative().then(async (res: any) => {
      const options = {
        width: 218,
        height: 219
      };
      const url = await QRCode.toDataURL(res.codeUrl, options);
      setCodeUrl(url);
      setAmount(res.amount / 100);
      setProductName(res.productName);
      if (url) {
        handlPayResult(res.codeUrl);
      }
    });
  };
  const handlPayResult = (codeUrl: string) => {
    tocWxPayResult(codeUrl).then((res: boolean) => {
      if (res) {
        if (timer) {
          clearTimeout(timer);
          setTimer(null);
        }
        toast({
          title: '支付成功',
          status: 'success'
        });
        onClose();
      } else {
        const newTimer = setTimeout(() => handlPayResult(codeUrl), 2000);
        setTimer(newTimer);
      }
    });
  };

  const handleClose = () => {
    if (timer) {
      clearTimeout(timer);
      setTimer(null);
    }
    onClose();
  };

  return (
    <MyModal isOpen={true} onClose={handleClose} isCentered={!isPc} w="100%">
      <Box
        px={['20px', '5vw']}
        py={['60px', '60px']}
        backgroundSize="cover"
        backgroundRepeat="no-repeat"
        backgroundImage="/imgs/home/<USER>"
        pos="relative"
        overflowY="auto"
        overflowX="hidden"
        borderTopLeftRadius="8px"
        borderTopRightRadius="8px"
      >
        <Box
          fontSize={respDims(22, 18)}
          color="#000A3E"
          lineHeight="32px"
          pos="absolute"
          top="30%"
          left="50%"
          transform="translate(-50%, -50%)"
        >
          {productName || ''}
        </Box>
        <Box
          fontSize={respDims(14, 12)}
          color=" #606266;"
          lineHeight="20px"
          mt="32px"
          textAlign="left"
          pos="absolute"
          top="49%"
          left="60%"
          w="100%"
          overflowX="hidden"
          transform="translate(-50%, -50%)"
          display="inline-block"
          paddingRight="40px"
        >
          当前聊天次数已使用完 支付成功当月有效，可当月无限使用
        </Box>
      </Box>

      <Flex alignItems="center" justifyContent="center" mb="20px" mt="24px" flexDir="column">
        <Box
          boxShadow="0px 0px 10px 0px rgba(48,55,88,0.05), 0px 0px 28px 0px rgba(44,54,66,0.05);"
          borderRadius="15px"
          backgroundColor="#fff"
          textAlign="center"
          p="6px 10px"
        >
          {codeUrl ? (
            <Image w="218px" h="219px" src={codeUrl} alt="" objectFit="fill" />
          ) : (
            <Box w="218px" h="219px"></Box>
          )}

          <Flex m="0 0 4px 0" alignItems="center" justifyContent="center">
            <Image
              w="24px"
              h="24px"
              src={'/imgs/home/<USER>'}
              alt=""
              objectFit="cover"
              mr="10px"
            />
            <Box fontSize={respDims(15, 13)} color="#606266">
              微信支付
            </Box>
          </Flex>
        </Box>
        <Box
          fontWeight="600"
          fontSize={respDims(22, 20)}
          color="#D54941"
          lineHeight="32px"
          m="20px 0 14px 0"
        >
          需支付: {amount + '元' || 0}
        </Box>

        <Box fontWeight="400" fontSize={respDims(14, 12)} color="#606266" lineHeight="32px">
          打开<span style={{ color: '#6a87f8' }}>「微信」</span>扫描二维码支付
        </Box>
      </Flex>
    </MyModal>
  );
};

export default PayModal;
