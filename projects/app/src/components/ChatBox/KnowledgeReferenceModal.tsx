import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Text,
  HStack,
  VStack,
  Alert,
  Flex,
  Icon,
  Link,
  Grid,
  Image
} from '@chakra-ui/react';
import MyModal from '../MyModal';
import MyTooltip from '../MyTooltip';
import SvgIcon from '../SvgIcon';
import { fileTypeInfos } from './MessageInputMini';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import Chooser from '@/pages/cloud/list/components/Chooser';
import { Toast } from '@/utils/ui/toast';
import { useEffect, useState } from 'react';
import { useSelectFile } from '@/hooks/useSelectFile';
import SelectCloudModal from './SelectCloudModal';
import { FileTypeEnum } from '@/constants/api/cloud';
import { useRequest } from '@/hooks/useRequest';
import { uploadFile } from '@/utils/file';
import { FileMetaType } from '@/types/api/file';
import { respDims } from '@/utils/chakra';
import { itemStyles } from '../AppDetail/components/constants';
import FileIcon from '@/pages/cloud/components/FileIcon';
import { getAppContextDetail, submitAppContext } from '@/api/dataset';
import { TenantAppKnowledgeFile } from '@/types/api/app';
import { formatFileSize } from '@/utils/tools';

interface KnowledgeReferenceModalProps {
  onClose: () => void;
  appId: string;
  onSuccess: () => void;
}
interface SelectedCloudProps {
  key: string;
  id?: string;
  title: string;
  description?: string;
  authority?: AuthorityTypeEnum;
  children?: SelectedCloudProps[];
}

enum AuthorityTypeEnum {
  Invalid = 0,
  Valid = 1
}
const MAX_FILES = 8;

const KnowledgeReferenceModal = ({ appId, onClose, onSuccess }: KnowledgeReferenceModalProps) => {
  const [filesList, setFilesList] = useState<TenantAppKnowledgeFile[]>([]);
  const [selectedCloudList, setSelectedCloudList] = useState<SelectedCloudProps[]>([]);
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const { File: FileSelect, onOpen: openFileSelect } = useSelectFile({
    fileType: '*',
    multiple: true
  });
  const { mutate: onSelectFiles, isLoading: isUploadLoading } = useRequest({
    mutationFn: (files: File[]) => {
      if (!files || files.length === 0) return Promise.resolve(null);
      if (filesList.length + files.length > MAX_FILES) {
        Toast.error(
          `背景知识的资料最多不能超过${MAX_FILES}个, 当前可以添加${MAX_FILES - filesList.length}个文件`
        );
        return Promise.reject();
      }
      return Promise.all(
        files.map((file) =>
          uploadFile(file, {
            onProgress(number) {}
          })
        )
      );
    },
    onSuccess(files: FileMetaType[]) {
      if (files) {
        let newValues: TenantAppKnowledgeFile[] = files.map((res) => {
          return {
            fileName: res.fileName,
            fileKey: res.fileKey,
            fileId: res.id,
            fileSize: res.fileSize,
            type: 1,
            fileUrl: res.fileUrl
          };
        });
        setFilesList([...filesList, ...newValues]);
      }
    }
  });

  const openFileCloudSelect = () => {
    let files = filesList
      .map((item) => {
        return {
          fileId: item.fileId!,
          fileKey: item.fileKey!,
          fileName: item.fileName!,
          fileUrl: item.fileUrl,
          rowKey: `${FileTypeEnum.File}-${item.fileId}`
        };
      })
      .filter((item) => item.fileId);
    const accept: string[] = [];

    fileTypeInfos.forEach((item) => {
      accept.push(item.name);
    });
    openOverlay({
      Overlay: Chooser,
      props: {
        files: files,
        accept: accept,
        maxCount: 8,

        onSuccess(files) {
          const newFiles = files.map((item) => {
            const matchedItem = filesList.find((file) => file.fileId === item.fileId);
            return matchedItem ? { ...matchedItem } : item;
          });
          console.log(newFiles, filesList, 'newFiles');

          let newValue = newFiles.map((item) => {
            return {
              fileId: item.fileId,
              fileKey: item.fileKey,
              fileName: item.fileName,
              fileSize: item.fileSize,
              fileUrl: item.fileUrl,
              authority: item.authority,
              type: item.type || 2
            };
          });
          if (filesList.filter((file) => !file.fileId).length + newValue.length > MAX_FILES) {
            Toast.error(
              `背景知识的资料最多不能超过${MAX_FILES}个, 当前可以添加${MAX_FILES - filesList.length}个文件`
            );
            return;
          }
          setFilesList([...filesList.filter((file) => !file.fileId), ...newValue]);
        }
      }
    });
  };

  const onRemoveFile = (file: TenantAppKnowledgeFile) => {
    const updatedFiles = filesList.filter((item) => file.fileKey !== item.fileKey);
    setFilesList(updatedFiles);
  };

  const openSelectCloudModal = () => {
    openOverlay({
      Overlay: SelectCloudModal,
      props: {
        initialSelectedKeys: selectedCloudList,
        onComplete: (selectedKeys: SelectedCloudProps[]) => {
          if (selectedKeys.length > 1000) {
            Toast.error('最大支持检索引用1000个知识库');
            return;
          }
          const notInModalItems = selectedCloudList.filter(
            (item) => item.authority === AuthorityTypeEnum.Invalid
          );

          const updatedSelectedCloudList = [
            ...notInModalItems,
            ...selectedKeys.map((selectedKey) => {
              const matchedItem = selectedCloudList.find((item) => item.key === selectedKey.key);
              return matchedItem ? { ...matchedItem } : selectedKey;
            })
          ];

          setSelectedCloudList(updatedSelectedCloudList);
        },
        onClose: () => {}
      }
    });
  };

  const onSave = async () => {
    setIsSaveLoading(true);
    const spaceIds = selectedCloudList.map((item) => item.key);
    const res = await submitAppContext({
      tenantAppId: appId,
      spaceIds: spaceIds.filter((item) => item !== undefined) as string[],
      files: filesList.map((item) => ({
        fileId: item.fileId!,
        fileKey: item.fileKey!,
        fileSource: item.type!
      }))
    });
    if (res) {
      Toast.success('保存成功');
      onSuccess();
      onClose();
    }
    setIsSaveLoading(false);
  };

  useEffect(() => {
    getAppContextDetail({ tenantAppId: appId }).then((res) => {
      if (res) {
        if (res?.files?.length > 0) {
          setFilesList(
            res.files.map((item) => ({
              fileId: item.fileId,
              fileKey: item.fileKey,
              type: item.fileSource,
              fileName: item.fileName,
              fileUrl: item.fileUrl,
              fileSize: item.fileSize,
              authority: item.authority
            }))
          );
        }
        if (res?.spaces?.length > 0) {
          setSelectedCloudList(
            res?.spaces?.map((item) => ({
              key: String(item.spaceId),
              title: item.spaceName,
              description: item.description,
              authority: item.authority
            }))
          );
        }
      }
    });
  }, [appId]);

  return (
    <MyModal isOpen={true} onClose={onClose} title="背景知识引用" minW="910px" h="710px">
      <ModalBody py={6} className="bg_knowledge_tour_container">
        <VStack spacing={6} align="stretch">
          <Alert status="warning" bg="orange.50" borderRadius="md">
            <Text fontSize="sm">
              💡
              背景知识是智能应用执行任务的基础知识。提供相关知识，可助其更好理解并完成任务。如教学设计时，上传新课标资料，应用便能设计出更贴合的教学方案。
            </Text>
          </Alert>
          {/* <BackgroundTour componentStep={2} /> */}
          <Box id="bg_knowledge_tour2">
            <HStack spacing={2} mb={4}>
              <HStack
                spacing={2}
                px={2}
                borderRadius="10px 0"
                display="inline-flex"
                alignItems="center"
                bg="#E0FFFC"
              >
                <HStack
                  px={3}
                  py={1}
                  borderRadius="10px 0px"
                  bg="linear-gradient(90deg, #DEFFFD 0%, #A1EFEA 100%);"
                >
                  <Text fontSize="16px" color="#000" fontWeight="600">
                    文件引用
                  </Text>
                  <MyTooltip
                    w="260px"
                    label="文档将以长文本的形式被引用,每一轮AI对话都将参照此文档内容。"
                    hasArrow
                    placement="top"
                    textAlign="left"
                  >
                    <Box>
                      <Icon w={4} h={4} color="#000" />
                    </Box>
                  </MyTooltip>
                </HStack>
              </HStack>
              <Text color="#000" fontSize="14px">
                ({filesList.length}/{MAX_FILES})
              </Text>
              <HStack spacing={2} ml="auto">
                <Box
                  color="#4E5969"
                  borderRadius="50px"
                  p="5px 16px"
                  bgColor="#F6F6F6"
                  fontSize="14px"
                  fontWeight="400"
                  cursor="pointer"
                  onClick={() => openFileCloudSelect()}
                >
                  从数据空间选择
                </Box>

                <Box
                  color="#4E5969"
                  borderRadius="50px"
                  p="5px 16px"
                  bgColor="#F6F6F6"
                  fontSize="14px"
                  fontWeight="400"
                  cursor="pointer"
                  onClick={() => openFileSelect()}
                >
                  从本地上传
                </Box>
              </HStack>
            </HStack>

            {filesList.length > 0 ? (
              <Grid
                w="100%"
                mt={respDims(16, 14)}
                gridTemplateColumns={'repeat(4, minmax(0, 1fr))'}
                gridGap={respDims('28rpx', 16)}
              >
                {filesList.map((item) => (
                  <Flex key={item.fileKey as string} {...itemStyles} position="relative">
                    <FileIcon
                      fileType={item.fileType}
                      fileName={item.fileName}
                      w={respDims(40)}
                      h={respDims(40)}
                    ></FileIcon>
                    <Flex direction="column" ml={respDims(10, 8)} w="100%">
                      <Box
                        color="#1D2129"
                        fontSize={respDims(15, 13)}
                        className={'textEllipsis'}
                        title={item.fileName}
                        textOverflow="ellipsis"
                        overflow="hidden"
                        w="140px"
                        whiteSpace="nowrap"
                      >
                        {item.fileName?.split('.')[0]}
                      </Box>
                      {item.authority === AuthorityTypeEnum.Invalid ? (
                        <HStack mt="2px">
                          <Box mr="1px" fontSize="12px">
                            ⚠️
                          </Box>
                          <Box color="#FF7D00" fontSize="12px" fontWeight="400">
                            权限失效
                          </Box>
                        </HStack>
                      ) : (
                        <HStack mt="2px">
                          <Box fontSize="12px" fontWeight="400" color="#A8ABB2">
                            {item.fileName?.split('.')[1]}
                          </Box>
                          {item.fileSize && (
                            <Box fontSize="12px" fontWeight="400" color="#A8ABB2">
                              , {formatFileSize(item.fileSize!).replaceAll(' ', '')}
                            </Box>
                          )}
                        </HStack>
                      )}
                    </Flex>
                    <SvgIcon
                      name="circleClose"
                      position="absolute"
                      className="close_icon"
                      right="-10px"
                      top="-10px"
                      cursor="pointer"
                      w={respDims(30)}
                      display="none"
                      h={respDims(30)}
                      onClick={() => onRemoveFile(item)}
                    ></SvgIcon>
                  </Flex>
                ))}
              </Grid>
            ) : (
              <Flex
                direction="column"
                justify="center"
                borderStyle="dashed"
                borderColor="gray.200"
                borderRadius="md"
              >
                <Flex
                  color="#4E5969"
                  direction="column"
                  align="center"
                  fontSize="16px"
                  fontWeight="400"
                >
                  <Image src="/imgs/common/empty_bg.png" w="69px" h="69px" mb="19px" />
                  <Box>
                    当前未添加文件引用的文档，请
                    <Link
                      color="#7D4DFF"
                      fontSize="16px"
                      fontWeight="500"
                      cursor="pointer"
                      onClick={() => openFileCloudSelect()}
                    >
                      从数据空间选择
                    </Link>
                    &nbsp;或&nbsp;
                    <Link
                      color="#7D4DFF"
                      fontSize="16px"
                      fontWeight="500"
                      cursor="pointer"
                      onClick={() => openFileSelect()}
                    >
                      从本地上传
                    </Link>
                  </Box>
                </Flex>
              </Flex>
            )}
            {filesList.filter((item) => item.authority === AuthorityTypeEnum.Invalid).length >
              0 && (
              <Box color="#FF7D00" fontSize="14px" fontWeight="400" mt="12px">
                ⚠️ 权限失效的文档将无法引用，请联系空间管理员开放【可查看权限】
              </Box>
            )}
            {isUploadLoading && '文件上传中...'}
          </Box>
          {/* <BackgroundTour componentStep={3} /> */}
          <Box id="bg_knowledge_tour3">
            <HStack spacing={2} mb={4}>
              <HStack
                spacing={2}
                px={2}
                borderRadius="10px 0"
                display="inline-flex"
                alignItems="center"
                bg="#E0FFFC"
              >
                <HStack
                  px={3}
                  py={1}
                  borderRadius="10px 0px"
                  bg="linear-gradient(90deg, #DEFFFD 0%, #A1EFEA 100%);"
                >
                  <Text fontSize="16px" color="#000" fontWeight="600">
                    检索引用
                  </Text>
                  <MyTooltip
                    w="260px"
                    label={`· 在每轮AI 对话过程中，系统会根据您的提问，检索并引用数据空间中的文档相关片段，作为构建本次对话的参考依据.${'\n'}·选择检索范围越小，引用内容越准确。`}
                    hasArrow
                    placement="top"
                    textAlign="left"
                  >
                    <Box>
                      <Icon w={4} h={4} color="#000" />
                    </Box>
                  </MyTooltip>
                </HStack>
              </HStack>
              <Text color="#000" fontSize="14px" fontWeight="400">
                推荐引用空间数量：20以内
              </Text>

              <Flex
                align="center"
                ml="auto"
                borderRadius="50px"
                p="5px 16px"
                bgColor="#F6F6F6"
                fontSize="14px"
                fontWeight="400"
                cursor="pointer"
                onClick={() => openSelectCloudModal()}
              >
                <SvgIcon color="#4E5969" name="plus" w="14px" h="14px" />
                <Box color="#4E5969">选择</Box>
              </Flex>
            </HStack>

            {selectedCloudList && selectedCloudList.length > 0 ? (
              <Grid
                w="100%"
                mt={respDims(16, 14)}
                gridTemplateColumns={'repeat(4, minmax(0, 1fr))'}
                gridGap={respDims('28rpx', 16)}
                maxH="200px"
              >
                {selectedCloudList.map((item) => (
                  <Flex key={item.key} position="relative" {...itemStyles} w="100%" h="100%">
                    <SvgIcon name="box3" w="41px" h="41px" />
                    <Flex direction="column" w="100%">
                      <Box
                        color="#1D2129"
                        fontSize={respDims(15, 13)}
                        ml={respDims(10, 8)}
                        className={'textEllipsis'}
                        title={item.title}
                        textOverflow="ellipsis"
                        overflow="hidden"
                        w="140px"
                        whiteSpace="nowrap"
                      >
                        {item.title}
                      </Box>
                      <Box ml={respDims(10, 8)}>
                        {item.authority === AuthorityTypeEnum.Invalid ? (
                          <HStack mt="2px">
                            <Box mr="1px" fontSize="12px">
                              ⚠️
                            </Box>
                            <Box color="#FF7D00" fontSize="12px" fontWeight="400">
                              权限失效
                            </Box>
                          </HStack>
                        ) : (
                          <Box
                            color="#A8ABB2"
                            fontWeight="400"
                            fontSize="12px"
                            textOverflow="ellipsis"
                            overflow="hidden"
                            whiteSpace="nowrap"
                            w="140px"
                            className={'textEllipsis'}
                          >
                            {item.description}
                          </Box>
                        )}
                      </Box>
                    </Flex>

                    <SvgIcon
                      name="circleClose"
                      position="absolute"
                      className="close_icon"
                      right="-10px"
                      top="-10px"
                      cursor="pointer"
                      display="none"
                      w={respDims(30)}
                      h={respDims(30)}
                      onClick={() => {
                        const updatedList = selectedCloudList.filter(
                          (selected) => selected.key !== item.key
                        );
                        setSelectedCloudList(updatedList);
                      }}
                    />
                  </Flex>
                ))}
              </Grid>
            ) : (
              <Flex
                direction="column"
                align="center"
                justify="center"
                py={8}
                borderStyle="dashed"
                borderColor="gray.200"
                borderRadius="md"
                fontWeight="400"
                fontSize="16px"
                color="#4E5969"
              >
                <Image src="/imgs/common/empty_bg.png" w="69px" h="69px" mb="19px" />
                <Box>
                  当前未添加检索引用的数据空间，请点击{' '}
                  <Link
                    fontSize="16px"
                    onClick={() => openSelectCloudModal()}
                    fontWeight="500"
                    color="#7D4DFF"
                  >
                    +选择
                  </Link>
                </Box>
              </Flex>
            )}
            {selectedCloudList.filter((item) => item.authority === AuthorityTypeEnum.Invalid)
              .length > 0 && (
              <Box color="#FF7D00" fontSize="14px" fontWeight="400" mt="12px">
                ⚠️ 权限失效的数据空间将无法引用，请联系空间管理员开放【可查看权限】
              </Box>
            )}
          </Box>
        </VStack>

        <FileSelect onSelect={(files) => onSelectFiles([...files])} />
        <OverlayContainer></OverlayContainer>
      </ModalBody>

      <ModalFooter borderTopWidth="1px">
        <Button
          variant="ghost"
          mr={3}
          _hover={{ bgColor: '#f1edfe', color: '#7D4DFF' }}
          bgColor="#F2F3F5"
          color="#4E5969"
          onClick={onClose}
        >
          取消
        </Button>
        <Button colorScheme="purple" isLoading={isSaveLoading} onClick={onSave}>
          保存
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default KnowledgeReferenceModal;
