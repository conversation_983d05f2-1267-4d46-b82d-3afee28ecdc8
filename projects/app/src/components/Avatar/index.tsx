import React from 'react';
import { Box, Image } from '@chakra-ui/react';
import type { BoxProps, ImageProps } from '@chakra-ui/react';
import { LOGO_ICON } from '@/constants/common';

interface AvatarProps extends Omit<ImageProps, 'src'> {
  src?: string;
  isAddIcon?: boolean;
  onAddClick?: () => void;
}

const Avatar: React.FC<AvatarProps> = ({
  w = '30px',
  src,
  fallbackSrc,
  isAddIcon = false,
  onAddClick,
  ...props
}) => {
  if (isAddIcon) {
    return (
      <Box
        as="button"
        w={w}
        h={w}
        mt="6px"
        borderRadius="50%"
        border="2px dashed primary.500"
        color="primary.500"
        cursor="pointer"
        onClick={onAddClick}
        _hover={{ boxShadow: 'md' }}
        {...(props as BoxProps)}
      >
        +
      </Box>
    );
  }
  return (
    <Image
      fallbackSrc={fallbackSrc || LOGO_ICON}
      fallbackStrategy={'onError'}
      borderRadius={'md'}
      objectFit={'contain'}
      alt=""
      w={w}
      h={w}
      p={'1px'}
      src={src || LOGO_ICON}
      {...props}
    />
  );
};

export default Avatar;
