import { NodeOutputItemType } from './type.d';
import { ChatCompletionMessageParam } from '../ai/type';
import { WorkflowIOValueTypeEnum } from '../workflow/constants';
import { FlowNodeInputTypeEnum } from '../workflow/node/constant';
import { RuntimeEdgeItemType } from '../workflow/type/edge';

export type UserSelectOptionItemType = {
  key: string;
  value: string;
};

export type UserInputFormItemType = {
  type: FlowNodeInputTypeEnum;
  key: string;
  label: string;
  value: any;
  valueType: WorkflowIOValueTypeEnum;
  description?: string;
  defaultValue?: any;
  required: boolean;

  // input & textarea
  maxLength?: number;
  // numberInput
  max?: number;
  min?: number;
  // select
  list?: { label: string; value: string }[];
};

export type InteractiveBasicType = {
  entryNodeIds: string[];
  memoryEdges: RuntimeEdgeItemType[];
  nodeOutputs: NodeOutputItemType[];

  toolParams?: {
    entryNodeIds: string[]; // 记录工具中，交互节点的 Id，而不是起始工作流的入口
    memoryMessages: ChatCompletionMessageParam[]; // 这轮工具中，产生的新的 messages
    toolCallId: string; // 记录对应 tool 的id，用于后续交互节点可以替换掉 tool 的 response
  };
};

export type UserSelectInteractive = {
  type: 'userSelect';
  params: {
    description: string;
    userSelectOptions: UserSelectOptionItemType[];
    userSelectedVal?: string;
  };
};

export type UserInputInteractive = {
  type: 'userInput';
  params: {
    description: string;
    inputForm: UserInputFormItemType[];
    submitted?: boolean;
  };
};

export type InteractiveNodeResponseType = UserSelectInteractive | UserInputInteractive;
export type WorkflowInteractiveResponseType = InteractiveBasicType & InteractiveNodeResponseType;
