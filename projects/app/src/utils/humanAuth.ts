import { HumanType } from '@/types/api/human';

const humanInfoKey = 'humanInfo';

export const getToken = () => {
  if (typeof window === 'undefined') return '';
  const humanInfo = localStorage.getItem(humanInfoKey);
  return humanInfo ? 'Bearer ' + JSON.parse(humanInfo).accessToken : '';
};

export const getEnterpriseId = () => {
  if (typeof window === 'undefined') return '';
  const humanInfo = localStorage.getItem(humanInfoKey);
  return humanInfo ? JSON.parse(humanInfo).enterpriseId : '';
};

export const setHumanInfo = (humanInfo: HumanType) => {
  if (typeof window === 'undefined') return '';
  localStorage.setItem(humanInfoKey, JSON.stringify(humanInfo));
};

export const getHumanInfo = () => {
  if (typeof window === 'undefined') return '';
  return localStorage.getItem(humanInfoKey) || '';
};
