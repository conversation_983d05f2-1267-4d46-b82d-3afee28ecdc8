import OfficialHome from '@/pages/officialHome/dianhua';
import DianHuaLayout from '@/components/LayoutOfficialHome/dianhua';
export const getRedirectUrl = (url: string, host?: string) => {
  if (typeof window !== 'undefined') {
    if (window.location.host.includes('localhost:8088')) {
      return '/';
    }
    if (window.location.host.includes('gpt-pre-test08.hwzxs.com')) {
      return '/';
    }
    if (window.location.host.includes('zjyn-user.gzdjg.edu.cn')) {
      return '/';
    }
    if (window.location.host.includes('zjyn.gzdjg.edu.cn')) {
      return '/';
    }
  } else {
    if (host?.includes('localhost:8088')) {
      return '/';
    }
    if (host?.includes('gpt-pre-test08.hwzxs.com')) {
      return '/';
    }
    if (host?.includes('zjyn-user.gzdjg.edu.cn')) {
      return '/';
    }
    if (host?.includes('zjyn.gzdjg.edu.cn')) {
      return '/';
    }
  }

  return url;
};

export const getHomeComponent = (host: string) => {
  const DianhuaHome = (
    <DianHuaLayout>
      <OfficialHome />
    </DianHuaLayout>
  );

  if (typeof window !== 'undefined') {
    if (window.location.host.includes('localhost:8088')) {
      return DianhuaHome;
    }
    if (window.location.host.includes('gpt-pre-test08.hwzxs.com')) {
      return DianhuaHome;
    }
    if (window.location.host.includes('zjyn-user.gzdjg.edu.cn')) {
      return DianhuaHome;
    }
    if (window.location.host.includes('zjyn.gzdjg.edu.cn')) {
      return DianhuaHome;
    }
  } else {
    if (host.includes('localhost:8088')) {
      return DianhuaHome;
    }
    if (host.includes('gpt-pre-test08.hwzxs.com')) {
      return DianhuaHome;
    }
    if (host.includes('zjyn-user.gzdjg.edu.cn')) {
      return DianhuaHome;
    }
    if (host.includes('zjyn.gzdjg.edu.cn')) {
      return DianhuaHome;
    }
  }
  return null;
};
