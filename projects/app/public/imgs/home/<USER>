<svg width="81" height="69" viewBox="0 0 81 69" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Paper Plus">
<g id="Bg">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M32.784 10.0281L16.1213 15.7425C16.1039 15.7485 16.0865 15.7545 16.0692 15.7605C10.5086 17.6717 7.58441 23.7613 9.53684 29.3673L17.3617 51.8347L17.3621 51.8349L18.0432 53.7904C20.0909 59.6701 26.5101 62.8102 32.3445 60.8093L33.4987 60.4135L33.4989 60.4136L50.1617 54.6991C50.1793 54.6931 50.1969 54.687 50.2145 54.6809C55.7746 52.7694 58.6985 46.6801 56.7461 41.0743L48.9213 18.6069L48.9208 18.6067L48.2398 16.6512C46.192 10.7715 39.7729 7.63141 33.9385 9.63231L32.7842 10.0282L32.784 10.0281Z" fill="url(#paint0_linear_83582_9853)"/>
<g id="Path" opacity="0.5" filter="url(#filter0_f_83582_9853)">
<path d="M43.544 46.7928L39.3563 48.2327C35.8822 49.4273 32.0599 49.0932 28.8396 47.3134C26.0321 45.7617 23.897 43.229 22.8461 40.2035L19.8085 31.4583C18.6427 28.1022 20.3088 24.4844 23.5302 23.3766L33.87 19.8212C37.2473 18.6599 40.993 20.5684 42.2151 24.0869L47.2824 38.6751C48.4481 42.0312 46.7784 45.6806 43.544 46.7928Z" fill="#5700E4"/>
</g>
</g>
<g id="Icon">
<g id="Path_2" filter="url(#filter1_b_83582_9853)">
<path d="M37.923 18.6621L56.1513 18.6621L69.0014 32.4149V56.9238C69.0014 63.0417 64.0671 68.0001 57.9791 68.0001H38.4385C32.0559 68.0001 26.8516 62.795 26.8516 56.381L26.8516 29.7878C26.8516 23.6699 31.8104 18.6621 37.923 18.6621Z" fill="#A190FF" fill-opacity="0.35"/>
<path d="M56.5166 18.3207C56.4221 18.2196 56.2898 18.1621 56.1513 18.1621L37.923 18.1621C31.5312 18.1621 26.3516 23.3968 26.3516 29.7878L26.3516 56.381C26.3516 63.0702 31.7788 68.5001 38.4385 68.5001H57.9791C64.3456 68.5001 69.5014 63.3155 69.5014 56.9238V32.4149C69.5014 32.2882 69.4533 32.1661 69.3667 32.0735L56.5166 18.3207Z" stroke="url(#paint1_linear_83582_9853)" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g id="Path_3" filter="url(#filter2_bd_83582_9853)">
<mask id="path-5-inside-1_83582_9853" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M52.5113 47.6313H48.5921V51.5696C48.5921 52.503 47.8445 53.2543 46.9157 53.2543C45.9869 53.2543 45.2166 52.503 45.2166 51.5696V47.6313H41.3201C40.3913 47.6313 39.6211 46.88 39.6211 45.9466C39.6211 44.9905 40.3913 44.2392 41.3201 44.2392H45.2166V40.3008C45.2166 39.3675 45.9869 38.6162 46.9157 38.6162C47.8445 38.6162 48.5921 39.3675 48.5921 40.3008V44.2392H52.5113C53.4401 44.2392 54.2103 44.9905 54.2103 45.9466C54.2103 46.88 53.4401 47.6313 52.5113 47.6313Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M52.5113 47.6313H48.5921V51.5696C48.5921 52.503 47.8445 53.2543 46.9157 53.2543C45.9869 53.2543 45.2166 52.503 45.2166 51.5696V47.6313H41.3201C40.3913 47.6313 39.6211 46.88 39.6211 45.9466C39.6211 44.9905 40.3913 44.2392 41.3201 44.2392H45.2166V40.3008C45.2166 39.3675 45.9869 38.6162 46.9157 38.6162C47.8445 38.6162 48.5921 39.3675 48.5921 40.3008V44.2392H52.5113C53.4401 44.2392 54.2103 44.9905 54.2103 45.9466C54.2103 46.88 53.4401 47.6313 52.5113 47.6313Z" fill="url(#paint2_linear_83582_9853)"/>
<path d="M48.5921 47.6313V47.2313H48.1921V47.6313H48.5921ZM45.2166 47.6313H45.6166V47.2313H45.2166V47.6313ZM45.2166 44.2392V44.6392H45.6166V44.2392H45.2166ZM48.5921 44.2392H48.1921V44.6392H48.5921V44.2392ZM52.5113 47.2313H48.5921V48.0313H52.5113V47.2313ZM48.1921 47.6313V51.5696H48.9921V47.6313H48.1921ZM48.1921 51.5696C48.1921 52.2839 47.6218 52.8543 46.9157 52.8543V53.6543C48.0673 53.6543 48.9921 52.7221 48.9921 51.5696H48.1921ZM46.9157 52.8543C46.2026 52.8543 45.6166 52.277 45.6166 51.5696H44.8166C44.8166 52.729 45.7711 53.6543 46.9157 53.6543V52.8543ZM45.6166 51.5696V47.6313H44.8166V51.5696H45.6166ZM45.2166 47.2313H41.3201V48.0313H45.2166V47.2313ZM41.3201 47.2313C40.6071 47.2313 40.0211 46.654 40.0211 45.9466H39.2211C39.2211 47.106 40.1756 48.0313 41.3201 48.0313V47.2313ZM40.0211 45.9466C40.0211 45.213 40.6106 44.6392 41.3201 44.6392V43.8392C40.172 43.8392 39.2211 44.7679 39.2211 45.9466H40.0211ZM41.3201 44.6392H45.2166V43.8392H41.3201V44.6392ZM45.6166 44.2392V40.3008H44.8166V44.2392H45.6166ZM45.6166 40.3008C45.6166 39.5935 46.2026 39.0162 46.9157 39.0162V38.2162C45.7711 38.2162 44.8166 39.1414 44.8166 40.3008H45.6166ZM46.9157 39.0162C47.6218 39.0162 48.1921 39.5865 48.1921 40.3008H48.9921C48.9921 39.1484 48.0673 38.2162 46.9157 38.2162V39.0162ZM48.1921 40.3008V44.2392H48.9921V40.3008H48.1921ZM48.5921 44.6392H52.5113V43.8392H48.5921V44.6392ZM52.5113 44.6392C53.2208 44.6392 53.8103 45.213 53.8103 45.9466H54.6103C54.6103 44.7679 53.6594 43.8392 52.5113 43.8392V44.6392ZM53.8103 45.9466C53.8103 46.654 53.2243 47.2313 52.5113 47.2313V48.0313C53.6558 48.0313 54.6103 47.106 54.6103 45.9466H53.8103Z" fill="url(#paint3_linear_83582_9853)" mask="url(#path-5-inside-1_83582_9853)"/>
</g>
<g id="Fill 15" filter="url(#filter3_bd_83582_9853)">
<path d="M58.4241 34.384C60.1351 34.4012 62.5139 34.4086 64.5318 34.4012C65.5653 34.3988 66.0906 33.1579 65.3738 32.4104C62.7815 29.7018 58.1442 24.8543 55.4905 22.0815C54.7565 21.3143 53.4727 21.8422 53.4727 22.9055V29.4082C53.4727 32.1366 55.709 34.384 58.4241 34.384Z" fill="url(#paint4_linear_83582_9853)"/>
<path d="M58.4261 34.184V34.184H58.4241C55.8204 34.184 53.6727 32.0271 53.6727 29.4082V22.9055C53.6727 22.0175 54.7401 21.5865 55.346 22.2198L55.3461 22.2198C56.6743 23.6076 58.5011 25.517 60.3194 27.4174C62.1319 29.3118 63.9359 31.1973 65.2293 32.5487L65.2294 32.5489C65.8253 33.1702 65.3877 34.1992 64.5313 34.2012L64.531 34.2012C62.514 34.2086 60.1362 34.2012 58.4261 34.184Z" stroke="url(#paint5_linear_83582_9853)" stroke-width="0.4"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_83582_9853" x="0.4375" y="0.481445" width="66.2168" height="67.4561" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9.5" result="effect1_foregroundBlur_83582_9853"/>
</filter>
<filter id="filter1_b_83582_9853" x="1.85156" y="-6.33789" width="92.1504" height="99.3379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_83582_9853"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_83582_9853" result="shape"/>
</filter>
<filter id="filter2_bd_83582_9853" x="24.6211" y="23.6162" width="44.5898" height="44.6377" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_83582_9853"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.577356 0 0 0 0 0.359375 0 0 0 0 0.9375 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_83582_9853" result="effect2_dropShadow_83582_9853"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_83582_9853" result="shape"/>
</filter>
<filter id="filter3_bd_83582_9853" x="38.4727" y="6.71777" width="42.2324" height="42.6865" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_83582_9853"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.372549 0 0 0 0 0.247059 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_83582_9853" result="effect2_dropShadow_83582_9853"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_83582_9853" result="shape"/>
</filter>
<linearGradient id="paint0_linear_83582_9853" x1="25.2815" y1="12.5927" x2="40.86" y2="57.8976" gradientUnits="userSpaceOnUse">
<stop stop-color="#A994FF"/>
<stop offset="1" stop-color="#9966FF"/>
</linearGradient>
<linearGradient id="paint1_linear_83582_9853" x1="33.5611" y1="24.4101" x2="65.7052" y2="58.421" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_83582_9853" x1="53.176" y1="41.2596" x2="36.9248" y2="41.7876" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint3_linear_83582_9853" x1="41.9435" y1="40.3216" x2="51.2875" y2="51.8558" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_83582_9853" x1="64.8372" y1="24.0087" x2="51.2112" y2="24.437" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint5_linear_83582_9853" x1="55.4198" y1="23.1957" x2="63.5695" y2="32.9278" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
