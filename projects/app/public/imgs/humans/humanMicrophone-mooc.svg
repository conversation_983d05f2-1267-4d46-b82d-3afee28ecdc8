<svg width="140" height="140" viewBox="0 0 140 140" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path d="M70 140C108.66 140 140 108.66 140 70C140 31.34 108.66 0 70 0C31.3401 0 0 31.34 0 70C0 108.66 31.3401 140 70 140Z" fill="#1464DD" fill-opacity="0.3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 140C108.659 140 140 108.661 140 70C140 31.3408 108.659 0 70 0C31.3406 0 0 31.3408 0 70C0 108.661 31.3406 140 70 140ZM70 1.95258C107.425 1.95258 138.056 32.4187 138.056 70C138.056 107.743 107.586 138.047 70 138.047C32.5753 138.047 1.94444 107.587 1.94444 70C1.94444 32.2566 32.4139 1.95258 70 1.95258Z" fill="#087DD7"/>
<g opacity="0.58">
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 140C108.659 140 140 108.661 140 70C140 31.3408 108.659 0 70 0C31.3406 0 0 31.3408 0 70C0 108.661 31.3406 140 70 140ZM70 1.95258C107.425 1.95258 138.056 32.4187 138.056 70C138.056 107.743 107.586 138.047 70 138.047C32.5753 138.047 1.94444 107.587 1.94444 70C1.94444 32.2566 32.4139 1.95258 70 1.95258Z" fill="#23C0FF"/>
</g>
<path d="M70.0006 116.609C95.7417 116.609 116.609 95.7416 116.609 70.0007C116.609 44.2597 95.7417 23.3926 70.0006 23.3926C44.2595 23.3926 23.3923 44.2597 23.3923 70.0007C23.3923 95.7416 44.2595 116.609 70.0006 116.609Z" fill="black" fill-opacity="0.42"/>
<path d="M70.0394 128.284C102.202 128.284 128.275 102.207 128.275 70.0385C128.275 37.8704 102.202 11.793 70.0394 11.793C37.8764 11.793 11.8032 37.8704 11.8032 70.0385C11.8032 102.207 37.8764 128.284 70.0394 128.284Z" fill="#1072CE" fill-opacity="0.12"/>
<path d="M130.103 9.7627H11.4919V126.918H130.103V9.7627Z" fill="url(#pattern0_293_35)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70.0394 128.284C102.202 128.284 128.275 102.198 128.275 70.0385C128.275 37.8697 102.202 11.793 70.0394 11.793C37.8763 11.793 11.8032 37.8697 11.8032 70.0385C11.8032 102.198 37.8763 128.284 70.0394 128.284ZM70.0394 13.7456C100.999 13.7456 126.331 38.9495 126.331 70.0385C126.331 101.26 101.129 126.331 70.0394 126.331C39.0799 126.331 13.7477 101.124 13.7477 70.0385C13.7477 38.8187 38.9496 13.7456 70.0394 13.7456Z" fill="#087DD7"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70.0394 128.284C102.202 128.284 128.275 102.198 128.275 70.0385C128.275 37.8697 102.202 11.793 70.0394 11.793C37.8763 11.793 11.8032 37.8697 11.8032 70.0385C11.8032 102.198 37.8763 128.284 70.0394 128.284ZM70.0394 13.7456C100.999 13.7456 126.331 38.9495 126.331 70.0385C126.331 101.26 101.129 126.331 70.0394 126.331C39.0799 126.331 13.7477 101.124 13.7477 70.0385C13.7477 38.8187 38.9496 13.7456 70.0394 13.7456Z" fill="#37C6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70.0394 128.284C102.202 128.284 128.275 102.198 128.275 70.0385C128.275 37.8697 102.202 11.793 70.0394 11.793C37.8763 11.793 11.8032 37.8697 11.8032 70.0385C11.8032 102.198 37.8763 128.284 70.0394 128.284ZM70.0394 15.6981C99.9255 15.6981 124.387 40.0273 124.387 70.0385C124.387 100.186 100.054 124.379 70.0394 124.379C40.1532 124.379 15.6921 100.05 15.6921 70.0385C15.6921 39.8985 40.0249 15.6981 70.0394 15.6981Z" fill="#F3FCFF"/>
<g clip-path="url(#clip0_293_35)">
<g filter="url(#filter0_d_293_35)">
<path d="M84.893 70.3138C85.1673 70.3527 85.4312 70.4455 85.6697 70.587C85.9081 70.7284 86.1165 70.9157 86.2828 71.1381C86.4491 71.3605 86.5701 71.6137 86.6389 71.8831C86.7076 72.1526 86.7228 72.433 86.6835 72.7083C86.1555 76.3763 84.4436 79.7689 81.8104 82.3658C79.1773 84.9626 75.7683 86.62 72.1067 87.0838V89.3303C72.1067 89.8913 71.8847 90.4293 71.4897 90.826C71.0947 91.2227 70.5589 91.4456 70.0002 91.4456C69.4415 91.4456 68.9057 91.2227 68.5107 90.826C68.1156 90.4293 67.8937 89.8913 67.8937 89.3303V87.0838C64.2326 86.6193 60.8243 84.9615 58.1916 82.3648C55.5589 79.7681 53.8472 76.3758 53.319 72.7083C53.2396 72.1529 53.3833 71.5886 53.7183 71.1395C54.0532 70.6905 54.5522 70.3934 55.1053 70.3138C55.6583 70.2341 56.2203 70.3783 56.6675 70.7147C57.1147 71.0511 57.4105 71.5521 57.4898 72.1075C57.9255 75.1273 59.4297 77.8885 61.7272 79.8854C64.0247 81.8823 66.9616 82.9814 70.0002 82.9814C73.0388 82.9814 75.9757 81.8823 78.2732 79.8854C80.5706 77.8885 82.0749 75.1273 82.5106 72.1075C82.5498 71.8325 82.6426 71.568 82.7837 71.329C82.9248 71.09 83.1114 70.8812 83.3328 70.7146C83.5543 70.5481 83.8062 70.4269 84.0743 70.3581C84.3423 70.2894 84.6191 70.2743 84.893 70.3138ZM70.0002 49.1396C72.7936 49.1396 75.4725 50.254 77.4477 52.2374C79.4229 54.2209 80.5326 56.9111 80.5326 59.7161V70.2926C80.5326 73.0977 79.4229 75.7878 77.4477 77.7713C75.4725 79.7548 72.7936 80.8691 70.0002 80.8691C67.2068 80.8691 64.5279 79.7548 62.5527 77.7713C60.5774 75.7878 59.4678 73.0977 59.4678 70.2926V59.7161C59.4678 56.9111 60.5774 54.2209 62.5527 52.2374C64.5279 50.254 67.2068 49.1396 70.0002 49.1396Z" fill="url(#paint0_linear_293_35)"/>
</g>
</g>
<defs>
<pattern id="pattern0_293_35" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_293_35" transform="scale(0.0163934 0.0166667)"/>
</pattern>
<filter id="filter0_d_293_35" x="50.4976" y="46.3396" width="39.0072" height="47.9057" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.259393 0 0 0 0 0.48764 0 0 0 0 0.745474 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_293_35"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_293_35" result="shape"/>
</filter>
<linearGradient id="paint0_linear_293_35" x1="70.0012" y1="49.1396" x2="70.0012" y2="91.4456" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#8CCBEC"/>
</linearGradient>
<clipPath id="clip0_293_35">
<rect width="46.6667" height="46.862" fill="white" transform="translate(46.667 46.8613)"/>
</clipPath>
<image id="image0_293_35" width="61" height="60" preserveAspectRatio="none" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
