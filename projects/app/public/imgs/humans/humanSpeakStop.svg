<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="stop_circle_fill" clip-path="url(#clip0_87477_8405)">
<g id="Group">
<path id="Vector" d="M16 2.66669C23.364 2.66669 29.3334 8.63602 29.3334 16C29.3334 23.364 23.364 29.3334 16 29.3334C8.63602 29.3334 2.66669 23.364 2.66669 16C2.66669 8.63602 8.63602 2.66669 16 2.66669ZM18.6667 10.6667H13.3334C12.6261 10.6667 11.9478 10.9476 11.4477 11.4477C10.9476 11.9478 10.6667 12.6261 10.6667 13.3334V18.6667C10.6667 19.3739 10.9476 20.0522 11.4477 20.5523C11.9478 21.0524 12.6261 21.3334 13.3334 21.3334H18.6667C19.3739 21.3334 20.0522 21.0524 20.5523 20.5523C21.0524 20.0522 21.3334 19.3739 21.3334 18.6667V13.3334C21.3334 12.6261 21.0524 11.9478 20.5523 11.4477C20.0522 10.9476 19.3739 10.6667 18.6667 10.6667Z" fill="url(#paint0_linear_87477_8405)"/>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_87477_8405" x1="16" y1="2.66669" x2="16" y2="29.3334" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.66"/>
</linearGradient>
<clipPath id="clip0_87477_8405">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
