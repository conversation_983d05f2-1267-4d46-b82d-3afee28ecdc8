<svg width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Ellipse 173" filter="url(#filter0_d_86984_67376)">
<circle cx="39" cy="37" r="30" fill="url(#paint0_linear_86984_67376)"/>
<circle cx="39" cy="37" r="31" stroke="white" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d_86984_67376" x="0.6" y="0.6" width="76.8" height="76.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.118904 0 0 0 0 0.070112 0 0 0 0 0.242318 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_86984_67376"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_86984_67376" result="shape"/>
</filter>
<linearGradient id="paint0_linear_86984_67376" x1="42" y1="67" x2="42" y2="7" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D1BEFF"/>
</linearGradient>
</defs>
</svg>
