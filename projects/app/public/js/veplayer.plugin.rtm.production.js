!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).__VEPLAYER_PLUGIN_RTM__=t()}(this,(function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var r=function(e){return e&&e.Math==Math&&e},n=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||function(){return this}()||e||Function("return this")(),o=function(e){try{return!!e()}catch(t){return!0}},i=!o((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),a=i,u=Function.prototype,s=u.apply,c=u.call,l="object"==typeof Reflect&&Reflect.apply||(a?c.bind(s):function(){return c.apply(s,arguments)}),f=i,p=Function.prototype,h=p.call,d=f&&p.bind.bind(h,h),v=f?d:function(e){return function(){return h.apply(e,arguments)}},g=v,y=g({}.toString),m=g("".slice),_=function(e){return m(y(e),8,-1)},b=_,w=v,x=function(e){if("Function"===b(e))return w(e)},S="object"==typeof document&&document.all,k={all:S,IS_HTMLDDA:void 0===S&&void 0!==S},T=k.all,O=k.IS_HTMLDDA?function(e){return"function"==typeof e||e===T}:function(e){return"function"==typeof e},E={},L=!o((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),R=i,C=Function.prototype.call,P=R?C.bind(C):function(){return C.apply(C,arguments)},A={},j={}.propertyIsEnumerable,I=Object.getOwnPropertyDescriptor,M=I&&!j.call({1:2},1);A.f=M?function(e){var t=I(this,e);return!!t&&t.enumerable}:j;var N,F,D=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},U=o,B=_,H=Object,z=v("".split),q=U((function(){return!H("z").propertyIsEnumerable(0)}))?function(e){return"String"==B(e)?z(e,""):H(e)}:H,G=function(e){return null==e},$=G,V=TypeError,Y=function(e){if($(e))throw V("Can't call method on "+e);return e},W=q,K=Y,X=function(e){return W(K(e))},J=O,Q=k.all,Z=k.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:J(e)||e===Q}:function(e){return"object"==typeof e?null!==e:J(e)},ee={},te=ee,re=n,ne=O,oe=function(e){return ne(e)?e:void 0},ie=function(e,t){return arguments.length<2?oe(te[e])||oe(re[e]):te[e]&&te[e][t]||re[e]&&re[e][t]},ae=v({}.isPrototypeOf),ue="undefined"!=typeof navigator&&String(navigator.userAgent)||"",se=n,ce=ue,le=se.process,fe=se.Deno,pe=le&&le.versions||fe&&fe.version,he=pe&&pe.v8;he&&(F=(N=he.split("."))[0]>0&&N[0]<4?1:+(N[0]+N[1])),!F&&ce&&(!(N=ce.match(/Edge\/(\d+)/))||N[1]>=74)&&(N=ce.match(/Chrome\/(\d+)/))&&(F=+N[1]);var de=F,ve=de,ge=o,ye=n.String,me=!!Object.getOwnPropertySymbols&&!ge((function(){var e=Symbol();return!ye(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&ve&&ve<41})),_e=me&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,be=ie,we=O,xe=ae,Se=Object,ke=_e?function(e){return"symbol"==typeof e}:function(e){var t=be("Symbol");return we(t)&&xe(t.prototype,Se(e))},Te=String,Oe=function(e){try{return Te(e)}catch(t){return"Object"}},Ee=O,Le=Oe,Re=TypeError,Ce=function(e){if(Ee(e))return e;throw Re(Le(e)+" is not a function")},Pe=Ce,Ae=G,je=function(e,t){var r=e[t];return Ae(r)?void 0:Pe(r)},Ie=P,Me=O,Ne=Z,Fe=TypeError,De={exports:{}},Ue=n,Be=Object.defineProperty,He=function(e,t){try{Be(Ue,e,{value:t,configurable:!0,writable:!0})}catch(r){Ue[e]=t}return t},ze="__core-js_shared__",qe=n[ze]||He(ze,{}),Ge=qe;(De.exports=function(e,t){return Ge[e]||(Ge[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.30.2",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.30.2/LICENSE",source:"https://github.com/zloirock/core-js"});var $e=De.exports,Ve=Y,Ye=Object,We=function(e){return Ye(Ve(e))},Ke=We,Xe=v({}.hasOwnProperty),Je=Object.hasOwn||function(e,t){return Xe(Ke(e),t)},Qe=v,Ze=0,et=Math.random(),tt=Qe(1..toString),rt=function(e){return"Symbol("+(void 0===e?"":e)+")_"+tt(++Ze+et,36)},nt=$e,ot=Je,it=rt,at=me,ut=_e,st=n.Symbol,ct=nt("wks"),lt=ut?st.for||st:st&&st.withoutSetter||it,ft=function(e){return ot(ct,e)||(ct[e]=at&&ot(st,e)?st[e]:lt("Symbol."+e)),ct[e]},pt=P,ht=Z,dt=ke,vt=je,gt=function(e,t){var r,n;if("string"===t&&Me(r=e.toString)&&!Ne(n=Ie(r,e)))return n;if(Me(r=e.valueOf)&&!Ne(n=Ie(r,e)))return n;if("string"!==t&&Me(r=e.toString)&&!Ne(n=Ie(r,e)))return n;throw Fe("Can't convert object to primitive value")},yt=TypeError,mt=ft("toPrimitive"),_t=function(e,t){if(!ht(e)||dt(e))return e;var r,n=vt(e,mt);if(n){if(void 0===t&&(t="default"),r=pt(n,e,t),!ht(r)||dt(r))return r;throw yt("Can't convert object to primitive value")}return void 0===t&&(t="number"),gt(e,t)},bt=ke,wt=function(e){var t=_t(e,"string");return bt(t)?t:t+""},xt=Z,St=n.document,kt=xt(St)&&xt(St.createElement),Tt=function(e){return kt?St.createElement(e):{}},Ot=Tt,Et=!L&&!o((function(){return 7!=Object.defineProperty(Ot("div"),"a",{get:function(){return 7}}).a})),Lt=L,Rt=P,Ct=A,Pt=D,At=X,jt=wt,It=Je,Mt=Et,Nt=Object.getOwnPropertyDescriptor;E.f=Lt?Nt:function(e,t){if(e=At(e),t=jt(t),Mt)try{return Nt(e,t)}catch(r){}if(It(e,t))return Pt(!Rt(Ct.f,e,t),e[t])};var Ft=o,Dt=O,Ut=/#|\.prototype\./,Bt=function(e,t){var r=zt[Ht(e)];return r==Gt||r!=qt&&(Dt(t)?Ft(t):!!t)},Ht=Bt.normalize=function(e){return String(e).replace(Ut,".").toLowerCase()},zt=Bt.data={},qt=Bt.NATIVE="N",Gt=Bt.POLYFILL="P",$t=Bt,Vt=Ce,Yt=i,Wt=x(x.bind),Kt=function(e,t){return Vt(e),void 0===t?e:Yt?Wt(e,t):function(){return e.apply(t,arguments)}},Xt={},Jt=L&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Qt=Z,Zt=String,er=TypeError,tr=function(e){if(Qt(e))return e;throw er(Zt(e)+" is not an object")},rr=L,nr=Et,or=Jt,ir=tr,ar=wt,ur=TypeError,sr=Object.defineProperty,cr=Object.getOwnPropertyDescriptor,lr="enumerable",fr="configurable",pr="writable";Xt.f=rr?or?function(e,t,r){if(ir(e),t=ar(t),ir(r),"function"==typeof e&&"prototype"===t&&"value"in r&&pr in r&&!r[pr]){var n=cr(e,t);n&&n[pr]&&(e[t]=r.value,r={configurable:fr in r?r[fr]:n[fr],enumerable:lr in r?r[lr]:n[lr],writable:!1})}return sr(e,t,r)}:sr:function(e,t,r){if(ir(e),t=ar(t),ir(r),nr)try{return sr(e,t,r)}catch(n){}if("get"in r||"set"in r)throw ur("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var hr=Xt,dr=D,vr=L?function(e,t,r){return hr.f(e,t,dr(1,r))}:function(e,t,r){return e[t]=r,e},gr=n,yr=l,mr=x,_r=O,br=E.f,wr=$t,xr=ee,Sr=Kt,kr=vr,Tr=Je,Or=function(e){var t=function(r,n,o){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(r);case 2:return new e(r,n)}return new e(r,n,o)}return yr(e,this,arguments)};return t.prototype=e.prototype,t},Er=function(e,t){var r,n,o,i,a,u,s,c,l,f=e.target,p=e.global,h=e.stat,d=e.proto,v=p?gr:h?gr[f]:(gr[f]||{}).prototype,g=p?xr:xr[f]||kr(xr,f,{})[f],y=g.prototype;for(i in t)n=!(r=wr(p?i:f+(h?".":"#")+i,e.forced))&&v&&Tr(v,i),u=g[i],n&&(s=e.dontCallGetSet?(l=br(v,i))&&l.value:v[i]),a=n&&s?s:t[i],n&&typeof u==typeof a||(c=e.bind&&n?Sr(a,gr):e.wrap&&n?Or(a):d&&_r(a)?mr(a):a,(e.sham||a&&a.sham||u&&u.sham)&&kr(c,"sham",!0),kr(g,i,c),d&&(Tr(xr,o=f+"Prototype")||kr(xr,o,{}),kr(xr[o],i,a),e.real&&y&&(r||!y[i])&&kr(y,i,a)))},Lr=_,Rr=Array.isArray||function(e){return"Array"==Lr(e)},Cr=Math.ceil,Pr=Math.floor,Ar=Math.trunc||function(e){var t=+e;return(t>0?Pr:Cr)(t)},jr=function(e){var t=+e;return t!=t||0===t?0:Ar(t)},Ir=jr,Mr=Math.min,Nr=function(e){return e>0?Mr(Ir(e),9007199254740991):0},Fr=Nr,Dr=function(e){return Fr(e.length)},Ur=TypeError,Br=function(e){if(e>9007199254740991)throw Ur("Maximum allowed index exceeded");return e},Hr=wt,zr=Xt,qr=D,Gr=function(e,t,r){var n=Hr(t);n in e?zr.f(e,n,qr(0,r)):e[n]=r},$r={};$r[ft("toStringTag")]="z";var Vr="[object z]"===String($r),Yr=Vr,Wr=O,Kr=_,Xr=ft("toStringTag"),Jr=Object,Qr="Arguments"==Kr(function(){return arguments}()),Zr=Yr?Kr:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(r){}}(t=Jr(e),Xr))?r:Qr?Kr(t):"Object"==(n=Kr(t))&&Wr(t.callee)?"Arguments":n},en=O,tn=qe,rn=v(Function.toString);en(tn.inspectSource)||(tn.inspectSource=function(e){return rn(e)});var nn=tn.inspectSource,on=v,an=o,un=O,sn=Zr,cn=nn,ln=function(){},fn=[],pn=ie("Reflect","construct"),hn=/^\s*(?:class|function)\b/,dn=on(hn.exec),vn=!hn.exec(ln),gn=function(e){if(!un(e))return!1;try{return pn(ln,fn,e),!0}catch(t){return!1}},yn=function(e){if(!un(e))return!1;switch(sn(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return vn||!!dn(hn,cn(e))}catch(t){return!0}};yn.sham=!0;var mn=!pn||an((function(){var e;return gn(gn.call)||!gn(Object)||!gn((function(){e=!0}))||e}))?yn:gn,_n=Rr,bn=mn,wn=Z,xn=ft("species"),Sn=Array,kn=function(e){var t;return _n(e)&&(t=e.constructor,(bn(t)&&(t===Sn||_n(t.prototype))||wn(t)&&null===(t=t[xn]))&&(t=void 0)),void 0===t?Sn:t},Tn=function(e,t){return new(kn(e))(0===t?0:t)},On=o,En=de,Ln=ft("species"),Rn=function(e){return En>=51||!On((function(){var t=[];return(t.constructor={})[Ln]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Cn=Er,Pn=o,An=Rr,jn=Z,In=We,Mn=Dr,Nn=Br,Fn=Gr,Dn=Tn,Un=Rn,Bn=de,Hn=ft("isConcatSpreadable"),zn=Bn>=51||!Pn((function(){var e=[];return e[Hn]=!1,e.concat()[0]!==e})),qn=function(e){if(!jn(e))return!1;var t=e[Hn];return void 0!==t?!!t:An(e)};Cn({target:"Array",proto:!0,arity:1,forced:!zn||!Un("concat")},{concat:function(e){var t,r,n,o,i,a=In(this),u=Dn(a,0),s=0;for(t=-1,n=arguments.length;t<n;t++)if(qn(i=-1===t?a:arguments[t]))for(o=Mn(i),Nn(s+o),r=0;r<o;r++,s++)r in i&&Fn(u,s,i[r]);else Nn(s+1),Fn(u,s++,i);return u.length=s,u}});var Gn=Zr,$n=String,Vn=function(e){if("Symbol"===Gn(e))throw TypeError("Cannot convert a Symbol value to a string");return $n(e)},Yn={},Wn=jr,Kn=Math.max,Xn=Math.min,Jn=function(e,t){var r=Wn(e);return r<0?Kn(r+t,0):Xn(r,t)},Qn=X,Zn=Jn,eo=Dr,to=function(e){return function(t,r,n){var o,i=Qn(t),a=eo(i),u=Zn(n,a);if(e&&r!=r){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((e||u in i)&&i[u]===r)return e||u||0;return!e&&-1}},ro={includes:to(!0),indexOf:to(!1)},no={},oo=Je,io=X,ao=ro.indexOf,uo=no,so=v([].push),co=function(e,t){var r,n=io(e),o=0,i=[];for(r in n)!oo(uo,r)&&oo(n,r)&&so(i,r);for(;t.length>o;)oo(n,r=t[o++])&&(~ao(i,r)||so(i,r));return i},lo=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],fo=co,po=lo,ho=Object.keys||function(e){return fo(e,po)},vo=L,go=Jt,yo=Xt,mo=tr,_o=X,bo=ho;Yn.f=vo&&!go?Object.defineProperties:function(e,t){mo(e);for(var r,n=_o(t),o=bo(t),i=o.length,a=0;i>a;)yo.f(e,r=o[a++],n[r]);return e};var wo,xo=ie("document","documentElement"),So=rt,ko=$e("keys"),To=function(e){return ko[e]||(ko[e]=So(e))},Oo=tr,Eo=Yn,Lo=lo,Ro=no,Co=xo,Po=Tt,Ao="prototype",jo="script",Io=To("IE_PROTO"),Mo=function(){},No=function(e){return"<"+jo+">"+e+"</"+jo+">"},Fo=function(e){e.write(No("")),e.close();var t=e.parentWindow.Object;return e=null,t},Do=function(){try{wo=new ActiveXObject("htmlfile")}catch(o){}var e,t,r;Do="undefined"!=typeof document?document.domain&&wo?Fo(wo):(t=Po("iframe"),r="java"+jo+":",t.style.display="none",Co.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(No("document.F=Object")),e.close(),e.F):Fo(wo);for(var n=Lo.length;n--;)delete Do[Ao][Lo[n]];return Do()};Ro[Io]=!0;var Uo=Object.create||function(e,t){var r;return null!==e?(Mo[Ao]=Oo(e),r=new Mo,Mo[Ao]=null,r[Io]=e):r=Do(),void 0===t?r:Eo.f(r,t)},Bo={},Ho=co,zo=lo.concat("length","prototype");Bo.f=Object.getOwnPropertyNames||function(e){return Ho(e,zo)};var qo={},Go=Jn,$o=Dr,Vo=Gr,Yo=Array,Wo=Math.max,Ko=function(e,t,r){for(var n=$o(e),o=Go(t,n),i=Go(void 0===r?n:r,n),a=Yo(Wo(i-o,0)),u=0;o<i;o++,u++)Vo(a,u,e[o]);return a.length=u,a},Xo=_,Jo=X,Qo=Bo.f,Zo=Ko,ei="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];qo.f=function(e){return ei&&"Window"==Xo(e)?function(e){try{return Qo(e)}catch(t){return Zo(ei)}}(e):Qo(Jo(e))};var ti={};ti.f=Object.getOwnPropertySymbols;var ri=vr,ni=function(e,t,r,n){return n&&n.enumerable?e[t]=r:ri(e,t,r),e},oi=Xt,ii=function(e,t,r){return oi.f(e,t,r)},ai={},ui=ft;ai.f=ui;var si,ci,li,fi=ee,pi=Je,hi=ai,di=Xt.f,vi=function(e){var t=fi.Symbol||(fi.Symbol={});pi(t,e)||di(t,e,{value:hi.f(e)})},gi=P,yi=ie,mi=ft,_i=ni,bi=function(){var e=yi("Symbol"),t=e&&e.prototype,r=t&&t.valueOf,n=mi("toPrimitive");t&&!t[n]&&_i(t,n,(function(e){return gi(r,this)}),{arity:1})},wi=Zr,xi=Vr?{}.toString:function(){return"[object "+wi(this)+"]"},Si=Vr,ki=Xt.f,Ti=vr,Oi=Je,Ei=xi,Li=ft("toStringTag"),Ri=function(e,t,r,n){if(e){var o=r?e:e.prototype;Oi(o,Li)||ki(o,Li,{configurable:!0,value:t}),n&&!Si&&Ti(o,"toString",Ei)}},Ci=O,Pi=n.WeakMap,Ai=Ci(Pi)&&/native code/.test(String(Pi)),ji=n,Ii=Z,Mi=vr,Ni=Je,Fi=qe,Di=To,Ui=no,Bi="Object already initialized",Hi=ji.TypeError,zi=ji.WeakMap;if(Ai||Fi.state){var qi=Fi.state||(Fi.state=new zi);qi.get=qi.get,qi.has=qi.has,qi.set=qi.set,si=function(e,t){if(qi.has(e))throw Hi(Bi);return t.facade=e,qi.set(e,t),t},ci=function(e){return qi.get(e)||{}},li=function(e){return qi.has(e)}}else{var Gi=Di("state");Ui[Gi]=!0,si=function(e,t){if(Ni(e,Gi))throw Hi(Bi);return t.facade=e,Mi(e,Gi,t),t},ci=function(e){return Ni(e,Gi)?e[Gi]:{}},li=function(e){return Ni(e,Gi)}}var $i={set:si,get:ci,has:li,enforce:function(e){return li(e)?ci(e):si(e,{})},getterFor:function(e){return function(t){var r;if(!Ii(t)||(r=ci(t)).type!==e)throw Hi("Incompatible receiver, "+e+" required");return r}}},Vi=Kt,Yi=q,Wi=We,Ki=Dr,Xi=Tn,Ji=v([].push),Qi=function(e){var t=1==e,r=2==e,n=3==e,o=4==e,i=6==e,a=7==e,u=5==e||i;return function(s,c,l,f){for(var p,h,d=Wi(s),v=Yi(d),g=Vi(c,l),y=Ki(v),m=0,_=f||Xi,b=t?_(s,y):r||a?_(s,0):void 0;y>m;m++)if((u||m in v)&&(h=g(p=v[m],m,d),e))if(t)b[m]=h;else if(h)switch(e){case 3:return!0;case 5:return p;case 6:return m;case 2:Ji(b,p)}else switch(e){case 4:return!1;case 7:Ji(b,p)}return i?-1:n||o?o:b}},Zi={forEach:Qi(0),map:Qi(1),filter:Qi(2),some:Qi(3),every:Qi(4),find:Qi(5),findIndex:Qi(6),filterReject:Qi(7)},ea=Er,ta=n,ra=P,na=v,oa=L,ia=me,aa=o,ua=Je,sa=ae,ca=tr,la=X,fa=wt,pa=Vn,ha=D,da=Uo,va=ho,ga=Bo,ya=qo,ma=ti,_a=E,ba=Xt,wa=Yn,xa=A,Sa=ni,ka=ii,Ta=$e,Oa=no,Ea=rt,La=ft,Ra=ai,Ca=vi,Pa=bi,Aa=Ri,ja=$i,Ia=Zi.forEach,Ma=To("hidden"),Na="Symbol",Fa="prototype",Da=ja.set,Ua=ja.getterFor(Na),Ba=Object[Fa],Ha=ta.Symbol,za=Ha&&Ha[Fa],qa=ta.TypeError,Ga=ta.QObject,$a=_a.f,Va=ba.f,Ya=ya.f,Wa=xa.f,Ka=na([].push),Xa=Ta("symbols"),Ja=Ta("op-symbols"),Qa=Ta("wks"),Za=!Ga||!Ga[Fa]||!Ga[Fa].findChild,eu=oa&&aa((function(){return 7!=da(Va({},"a",{get:function(){return Va(this,"a",{value:7}).a}})).a}))?function(e,t,r){var n=$a(Ba,t);n&&delete Ba[t],Va(e,t,r),n&&e!==Ba&&Va(Ba,t,n)}:Va,tu=function(e,t){var r=Xa[e]=da(za);return Da(r,{type:Na,tag:e,description:t}),oa||(r.description=t),r},ru=function(e,t,r){e===Ba&&ru(Ja,t,r),ca(e);var n=fa(t);return ca(r),ua(Xa,n)?(r.enumerable?(ua(e,Ma)&&e[Ma][n]&&(e[Ma][n]=!1),r=da(r,{enumerable:ha(0,!1)})):(ua(e,Ma)||Va(e,Ma,ha(1,{})),e[Ma][n]=!0),eu(e,n,r)):Va(e,n,r)},nu=function(e,t){ca(e);var r=la(t),n=va(r).concat(uu(r));return Ia(n,(function(t){oa&&!ra(ou,r,t)||ru(e,t,r[t])})),e},ou=function(e){var t=fa(e),r=ra(Wa,this,t);return!(this===Ba&&ua(Xa,t)&&!ua(Ja,t))&&(!(r||!ua(this,t)||!ua(Xa,t)||ua(this,Ma)&&this[Ma][t])||r)},iu=function(e,t){var r=la(e),n=fa(t);if(r!==Ba||!ua(Xa,n)||ua(Ja,n)){var o=$a(r,n);return!o||!ua(Xa,n)||ua(r,Ma)&&r[Ma][n]||(o.enumerable=!0),o}},au=function(e){var t=Ya(la(e)),r=[];return Ia(t,(function(e){ua(Xa,e)||ua(Oa,e)||Ka(r,e)})),r},uu=function(e){var t=e===Ba,r=Ya(t?Ja:la(e)),n=[];return Ia(r,(function(e){!ua(Xa,e)||t&&!ua(Ba,e)||Ka(n,Xa[e])})),n};ia||(Ha=function(){if(sa(za,this))throw qa("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?pa(arguments[0]):void 0,t=Ea(e),r=function(e){this===Ba&&ra(r,Ja,e),ua(this,Ma)&&ua(this[Ma],t)&&(this[Ma][t]=!1),eu(this,t,ha(1,e))};return oa&&Za&&eu(Ba,t,{configurable:!0,set:r}),tu(t,e)},Sa(za=Ha[Fa],"toString",(function(){return Ua(this).tag})),Sa(Ha,"withoutSetter",(function(e){return tu(Ea(e),e)})),xa.f=ou,ba.f=ru,wa.f=nu,_a.f=iu,ga.f=ya.f=au,ma.f=uu,Ra.f=function(e){return tu(La(e),e)},oa&&ka(za,"description",{configurable:!0,get:function(){return Ua(this).description}})),ea({global:!0,constructor:!0,wrap:!0,forced:!ia,sham:!ia},{Symbol:Ha}),Ia(va(Qa),(function(e){Ca(e)})),ea({target:Na,stat:!0,forced:!ia},{useSetter:function(){Za=!0},useSimple:function(){Za=!1}}),ea({target:"Object",stat:!0,forced:!ia,sham:!oa},{create:function(e,t){return void 0===t?da(e):nu(da(e),t)},defineProperty:ru,defineProperties:nu,getOwnPropertyDescriptor:iu}),ea({target:"Object",stat:!0,forced:!ia},{getOwnPropertyNames:au}),Pa(),Aa(Ha,Na),Oa[Ma]=!0;var su=me&&!!Symbol.for&&!!Symbol.keyFor,cu=Er,lu=ie,fu=Je,pu=Vn,hu=$e,du=su,vu=hu("string-to-symbol-registry"),gu=hu("symbol-to-string-registry");cu({target:"Symbol",stat:!0,forced:!du},{for:function(e){var t=pu(e);if(fu(vu,t))return vu[t];var r=lu("Symbol")(t);return vu[t]=r,gu[r]=t,r}});var yu=Er,mu=Je,_u=ke,bu=Oe,wu=su,xu=$e("symbol-to-string-registry");yu({target:"Symbol",stat:!0,forced:!wu},{keyFor:function(e){if(!_u(e))throw TypeError(bu(e)+" is not a symbol");if(mu(xu,e))return xu[e]}});var Su=v([].slice),ku=Rr,Tu=O,Ou=_,Eu=Vn,Lu=v([].push),Ru=Er,Cu=ie,Pu=l,Au=P,ju=v,Iu=o,Mu=O,Nu=ke,Fu=Su,Du=function(e){if(Tu(e))return e;if(ku(e)){for(var t=e.length,r=[],n=0;n<t;n++){var o=e[n];"string"==typeof o?Lu(r,o):"number"!=typeof o&&"Number"!=Ou(o)&&"String"!=Ou(o)||Lu(r,Eu(o))}var i=r.length,a=!0;return function(e,t){if(a)return a=!1,t;if(ku(this))return t;for(var n=0;n<i;n++)if(r[n]===e)return t}}},Uu=me,Bu=String,Hu=Cu("JSON","stringify"),zu=ju(/./.exec),qu=ju("".charAt),Gu=ju("".charCodeAt),$u=ju("".replace),Vu=ju(1..toString),Yu=/[\uD800-\uDFFF]/g,Wu=/^[\uD800-\uDBFF]$/,Ku=/^[\uDC00-\uDFFF]$/,Xu=!Uu||Iu((function(){var e=Cu("Symbol")();return"[null]"!=Hu([e])||"{}"!=Hu({a:e})||"{}"!=Hu(Object(e))})),Ju=Iu((function(){return'"\\udf06\\ud834"'!==Hu("\udf06\ud834")||'"\\udead"'!==Hu("\udead")})),Qu=function(e,t){var r=Fu(arguments),n=Du(t);if(Mu(n)||void 0!==e&&!Nu(e))return r[1]=function(e,t){if(Mu(n)&&(t=Au(n,this,Bu(e),t)),!Nu(t))return t},Pu(Hu,null,r)},Zu=function(e,t,r){var n=qu(r,t-1),o=qu(r,t+1);return zu(Wu,e)&&!zu(Ku,o)||zu(Ku,e)&&!zu(Wu,n)?"\\u"+Vu(Gu(e,0),16):e};Hu&&Ru({target:"JSON",stat:!0,arity:3,forced:Xu||Ju},{stringify:function(e,t,r){var n=Fu(arguments),o=Pu(Xu?Qu:Hu,null,n);return Ju&&"string"==typeof o?$u(o,Yu,Zu):o}});var es=ti,ts=We;Er({target:"Object",stat:!0,forced:!me||o((function(){es.f(1)}))},{getOwnPropertySymbols:function(e){var t=es.f;return t?t(ts(e)):[]}}),vi("asyncIterator"),vi("hasInstance"),vi("isConcatSpreadable"),vi("iterator"),vi("match"),vi("matchAll"),vi("replace"),vi("search"),vi("species"),vi("split");var rs=bi;vi("toPrimitive"),rs();var ns=ie,os=Ri;vi("toStringTag"),os(ns("Symbol"),"Symbol"),vi("unscopables"),Ri(n.JSON,"JSON",!0);var is,as,us,ss=ee.Symbol,cs={},ls=L,fs=Je,ps=Function.prototype,hs=ls&&Object.getOwnPropertyDescriptor,ds=fs(ps,"name"),vs={EXISTS:ds,PROPER:ds&&"something"===function(){}.name,CONFIGURABLE:ds&&(!ls||ls&&hs(ps,"name").configurable)},gs=!o((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),ys=Je,ms=O,_s=We,bs=gs,ws=To("IE_PROTO"),xs=Object,Ss=xs.prototype,ks=bs?xs.getPrototypeOf:function(e){var t=_s(e);if(ys(t,ws))return t[ws];var r=t.constructor;return ms(r)&&t instanceof r?r.prototype:t instanceof xs?Ss:null},Ts=o,Os=O,Es=Z,Ls=Uo,Rs=ks,Cs=ni,Ps=ft("iterator"),As=!1;[].keys&&("next"in(us=[].keys())?(as=Rs(Rs(us)))!==Object.prototype&&(is=as):As=!0);var js=!Es(is)||Ts((function(){var e={};return is[Ps].call(e)!==e}));Os((is=js?{}:Ls(is))[Ps])||Cs(is,Ps,(function(){return this}));var Is={IteratorPrototype:is,BUGGY_SAFARI_ITERATORS:As},Ms=Is.IteratorPrototype,Ns=Uo,Fs=D,Ds=Ri,Us=cs,Bs=function(){return this},Hs=function(e,t,r,n){var o=t+" Iterator";return e.prototype=Ns(Ms,{next:Fs(+!n,r)}),Ds(e,o,!1,!0),Us[o]=Bs,e},zs=v,qs=Ce,Gs=O,$s=String,Vs=TypeError,Ys=function(e,t,r){try{return zs(qs(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(n){}},Ws=tr,Ks=function(e){if("object"==typeof e||Gs(e))return e;throw Vs("Can't set "+$s(e)+" as a prototype")},Xs=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=Ys(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(n){}return function(r,n){return Ws(r),Ks(n),t?e(r,n):r.__proto__=n,r}}():void 0),Js=Er,Qs=P,Zs=vs,ec=Hs,tc=ks,rc=Ri,nc=ni,oc=cs,ic=Is,ac=Zs.PROPER,uc=ic.BUGGY_SAFARI_ITERATORS,sc=ft("iterator"),cc="keys",lc="values",fc="entries",pc=function(){return this},hc=function(e,t,r,n,o,i,a){ec(r,t,n);var u,s,c,l=function(e){if(e===o&&v)return v;if(!uc&&e in h)return h[e];switch(e){case cc:case lc:case fc:return function(){return new r(this,e)}}return function(){return new r(this)}},f=t+" Iterator",p=!1,h=e.prototype,d=h[sc]||h["@@iterator"]||o&&h[o],v=!uc&&d||l(o),g="Array"==t&&h.entries||d;if(g&&(u=tc(g.call(new e)))!==Object.prototype&&u.next&&(rc(u,f,!0,!0),oc[f]=pc),ac&&o==lc&&d&&d.name!==lc&&(p=!0,v=function(){return Qs(d,this)}),o)if(s={values:l(lc),keys:i?v:l(cc),entries:l(fc)},a)for(c in s)(uc||p||!(c in h))&&nc(h,c,s[c]);else Js({target:t,proto:!0,forced:uc||p},s);return a&&h[sc]!==v&&nc(h,sc,v,{name:o}),oc[t]=v,s},dc=function(e,t){return{value:e,done:t}},vc=X,gc=cs,yc=$i;Xt.f;var mc=hc,_c=dc,bc="Array Iterator",wc=yc.set,xc=yc.getterFor(bc);mc(Array,"Array",(function(e,t){wc(this,{type:bc,target:vc(e),index:0,kind:t})}),(function(){var e=xc(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,_c(void 0,!0)):_c("keys"==r?n:"values"==r?t[n]:[n,t[n]],!1)}),"values"),gc.Arguments=gc.Array;var Sc={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},kc=n,Tc=Zr,Oc=vr,Ec=cs,Lc=ft("toStringTag");for(var Rc in Sc){var Cc=kc[Rc],Pc=Cc&&Cc.prototype;Pc&&Tc(Pc)!==Lc&&Oc(Pc,Lc,Rc),Ec[Rc]=Ec.Array}var Ac=ss;vi("dispose");var jc=Ac;vi("asyncDispose");var Ic=Er,Mc=v,Nc=ie("Symbol"),Fc=Nc.keyFor,Dc=Mc(Nc.prototype.valueOf);Ic({target:"Symbol",stat:!0},{isRegistered:function(e){try{return void 0!==Fc(Dc(e))}catch(t){return!1}}});for(var Uc=Er,Bc=$e,Hc=ie,zc=v,qc=ke,Gc=ft,$c=Hc("Symbol"),Vc=$c.isWellKnown,Yc=Hc("Object","getOwnPropertyNames"),Wc=zc($c.prototype.valueOf),Kc=Bc("wks"),Xc=0,Jc=Yc($c),Qc=Jc.length;Xc<Qc;Xc++)try{var Zc=Jc[Xc];qc($c[Zc])&&Gc(Zc)}catch(EP){}Uc({target:"Symbol",stat:!0,forced:!0},{isWellKnown:function(e){if(Vc&&Vc(e))return!0;try{for(var t=Wc(e),r=0,n=Yc(Kc),o=n.length;r<o;r++)if(Kc[n[r]]==t)return!0}catch(EP){}return!1}}),vi("matcher"),vi("metadataKey"),vi("observable"),vi("metadata"),vi("patternMatch"),vi("replaceAll");var el=jc,tl=t(el),rl=v,nl=jr,ol=Vn,il=Y,al=rl("".charAt),ul=rl("".charCodeAt),sl=rl("".slice),cl=function(e){return function(t,r){var n,o,i=ol(il(t)),a=nl(r),u=i.length;return a<0||a>=u?e?"":void 0:(n=ul(i,a))<55296||n>56319||a+1===u||(o=ul(i,a+1))<56320||o>57343?e?al(i,a):n:e?sl(i,a,a+2):o-56320+(n-55296<<10)+65536}},ll={codeAt:cl(!1),charAt:cl(!0)},fl=ll.charAt,pl=Vn,hl=$i,dl=hc,vl=dc,gl="String Iterator",yl=hl.set,ml=hl.getterFor(gl);dl(String,"String",(function(e){yl(this,{type:gl,string:pl(e),index:0})}),(function(){var e,t=ml(this),r=t.string,n=t.index;return n>=r.length?vl(void 0,!0):(e=fl(r,n),t.index+=e.length,vl(e,!1))}));var _l=ai.f("iterator"),bl=t(_l);function wl(e){return(wl="function"==typeof tl&&"symbol"==typeof bl?function(e){return typeof e}:function(e){return e&&"function"==typeof tl&&e.constructor===tl&&e!==tl.prototype?"symbol":typeof e})(e)}var xl=We,Sl=ho;Er({target:"Object",stat:!0,forced:o((function(){Sl(1)}))},{keys:function(e){return Sl(xl(e))}});var kl=t(ee.Object.keys),Tl=t(ee.Object.getOwnPropertySymbols),Ol=Zi.filter;Er({target:"Array",proto:!0,forced:!Rn("filter")},{filter:function(e){return Ol(this,e,arguments.length>1?arguments[1]:void 0)}});var El=ee,Ll=function(e){return El[e+"Prototype"]},Rl=Ll("Array").filter,Cl=ae,Pl=Rl,Al=Array.prototype,jl=t((function(e){var t=e.filter;return e===Al||Cl(Al,e)&&t===Al.filter?Pl:t})),Il={exports:{}},Ml=Er,Nl=o,Fl=X,Dl=E.f,Ul=L;Ml({target:"Object",stat:!0,forced:!Ul||Nl((function(){Dl(1)})),sham:!Ul},{getOwnPropertyDescriptor:function(e,t){return Dl(Fl(e),t)}});var Bl=ee.Object,Hl=Il.exports=function(e,t){return Bl.getOwnPropertyDescriptor(e,t)};Bl.getOwnPropertyDescriptor.sham&&(Hl.sham=!0);var zl=t(Il.exports),ql=o,Gl=function(e,t){var r=[][e];return!!r&&ql((function(){r.call(null,t||function(){return 1},1)}))},$l=Zi.forEach,Vl=Gl("forEach")?[].forEach:function(e){return $l(this,e,arguments.length>1?arguments[1]:void 0)};Er({target:"Array",proto:!0,forced:[].forEach!=Vl},{forEach:Vl});var Yl=Ll("Array").forEach,Wl=Zr,Kl=Je,Xl=ae,Jl=Yl,Ql=Array.prototype,Zl={DOMTokenList:!0,NodeList:!0},ef=t((function(e){var t=e.forEach;return e===Ql||Xl(Ql,e)&&t===Ql.forEach||Kl(Zl,Wl(e))?Jl:t})),tf=ie,rf=Bo,nf=ti,of=tr,af=v([].concat),uf=tf("Reflect","ownKeys")||function(e){var t=rf.f(of(e)),r=nf.f;return r?af(t,r(e)):t},sf=uf,cf=X,lf=E,ff=Gr;Er({target:"Object",stat:!0,sham:!L},{getOwnPropertyDescriptors:function(e){for(var t,r,n=cf(e),o=lf.f,i=sf(n),a={},u=0;i.length>u;)void 0!==(r=o(n,t=i[u++]))&&ff(a,t,r);return a}});var pf=t(ee.Object.getOwnPropertyDescriptors),hf={exports:{}},df=Er,vf=L,gf=Yn.f;df({target:"Object",stat:!0,forced:Object.defineProperties!==gf,sham:!vf},{defineProperties:gf});var yf=ee.Object,mf=hf.exports=function(e,t){return yf.defineProperties(e,t)};yf.defineProperties.sham&&(mf.sham=!0);var _f=t(hf.exports),bf={exports:{}},wf=Er,xf=L,Sf=Xt.f;wf({target:"Object",stat:!0,forced:Object.defineProperty!==Sf,sham:!xf},{defineProperty:Sf});var kf=ee.Object,Tf=bf.exports=function(e,t,r){return kf.defineProperty(e,t,r)};kf.defineProperty.sham&&(Tf.sham=!0);var Of=t(bf.exports),Ef=t(el);Er({target:"Object",stat:!0,sham:!L},{create:Uo});var Lf=ee.Object,Rf=t((function(e,t){return Lf.create(e,t)})),Cf=We,Pf=ks,Af=gs;Er({target:"Object",stat:!0,forced:o((function(){Pf(1)})),sham:!Af},{getPrototypeOf:function(e){return Pf(Cf(e))}});var jf=t(ee.Object.getPrototypeOf);Er({target:"Object",stat:!0},{setPrototypeOf:Xs});var If=t(ee.Object.setPrototypeOf),Mf=Je,Nf=uf,Ff=E,Df=Xt,Uf=Z,Bf=vr,Hf=Error,zf=v("".replace),qf=String(Hf("zxcasd").stack),Gf=/\n\s*at [^:]*:[^\n]*/,$f=Gf.test(qf),Vf=D,Yf=!o((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",Vf(1,7)),7!==e.stack)})),Wf=vr,Kf=function(e,t){if($f&&"string"==typeof e&&!Hf.prepareStackTrace)for(;t--;)e=zf(e,Gf,"");return e},Xf=Yf,Jf=Error.captureStackTrace,Qf=cs,Zf=ft("iterator"),ep=Array.prototype,tp=function(e){return void 0!==e&&(Qf.Array===e||ep[Zf]===e)},rp=Zr,np=je,op=G,ip=cs,ap=ft("iterator"),up=function(e){if(!op(e))return np(e,ap)||np(e,"@@iterator")||ip[rp(e)]},sp=P,cp=Ce,lp=tr,fp=Oe,pp=up,hp=TypeError,dp=function(e,t){var r=arguments.length<2?pp(e):t;if(cp(r))return lp(sp(r,e));throw hp(fp(e)+" is not iterable")},vp=P,gp=tr,yp=je,mp=function(e,t,r){var n,o;gp(e);try{if(!(n=yp(e,"return"))){if("throw"===t)throw r;return r}n=vp(n,e)}catch(EP){o=!0,n=EP}if("throw"===t)throw r;if(o)throw n;return gp(n),r},_p=Kt,bp=P,wp=tr,xp=Oe,Sp=tp,kp=Dr,Tp=ae,Op=dp,Ep=up,Lp=mp,Rp=TypeError,Cp=function(e,t){this.stopped=e,this.result=t},Pp=Cp.prototype,Ap=function(e,t,r){var n,o,i,a,u,s,c,l=r&&r.that,f=!(!r||!r.AS_ENTRIES),p=!(!r||!r.IS_RECORD),h=!(!r||!r.IS_ITERATOR),d=!(!r||!r.INTERRUPTED),v=_p(t,l),g=function(e){return n&&Lp(n,"normal",e),new Cp(!0,e)},y=function(e){return f?(wp(e),d?v(e[0],e[1],g):v(e[0],e[1])):d?v(e,g):v(e)};if(p)n=e.iterator;else if(h)n=e;else{if(!(o=Ep(e)))throw Rp(xp(e)+" is not iterable");if(Sp(o)){for(i=0,a=kp(e);a>i;i++)if((u=y(e[i]))&&Tp(Pp,u))return u;return new Cp(!1)}n=Op(e,o)}for(s=p?e.next:n.next;!(c=bp(s,n)).done;){try{u=y(c.value)}catch(EP){Lp(n,"throw",EP)}if("object"==typeof u&&u&&Tp(Pp,u))return u}return new Cp(!1)},jp=Vn,Ip=Er,Mp=ae,Np=ks,Fp=Xs,Dp=function(e,t,r){for(var n=Nf(t),o=Df.f,i=Ff.f,a=0;a<n.length;a++){var u=n[a];Mf(e,u)||r&&Mf(r,u)||o(e,u,i(t,u))}},Up=Uo,Bp=vr,Hp=D,zp=function(e,t){Uf(t)&&"cause"in t&&Bf(e,"cause",t.cause)},qp=function(e,t,r,n){Xf&&(Jf?Jf(e,t):Wf(e,"stack",Kf(r,n)))},Gp=Ap,$p=function(e,t){return void 0===e?arguments.length<2?"":t:jp(e)},Vp=ft("toStringTag"),Yp=Error,Wp=[].push,Kp=function(e,t){var r,n=Mp(Xp,this);Fp?r=Fp(Yp(),n?Np(this):Xp):(r=n?this:Up(Xp),Bp(r,Vp,"Error")),void 0!==t&&Bp(r,"message",$p(t)),qp(r,Kp,r.stack,1),arguments.length>2&&zp(r,arguments[2]);var o=[];return Gp(e,Wp,{that:o}),Bp(r,"errors",o),r};Fp?Fp(Kp,Yp):Dp(Kp,Yp,{name:!0});var Xp=Kp.prototype=Up(Yp.prototype,{constructor:Hp(1,Kp),message:Hp(1,""),name:Hp(1,"AggregateError")});Ip({global:!0,constructor:!0,arity:2},{AggregateError:Kp});var Jp,Qp,Zp,eh,th="undefined"!=typeof process&&"process"==_(process),rh=ie,nh=ii,oh=L,ih=ft("species"),ah=function(e){var t=rh(e);oh&&t&&!t[ih]&&nh(t,ih,{configurable:!0,get:function(){return this}})},uh=ae,sh=TypeError,ch=function(e,t){if(uh(t,e))return e;throw sh("Incorrect invocation")},lh=mn,fh=Oe,ph=TypeError,hh=function(e){if(lh(e))return e;throw ph(fh(e)+" is not a constructor")},dh=tr,vh=hh,gh=G,yh=ft("species"),mh=function(e,t){var r,n=dh(e).constructor;return void 0===n||gh(r=dh(n)[yh])?t:vh(r)},_h=TypeError,bh=function(e,t){if(e<t)throw _h("Not enough arguments");return e},wh=/(?:ipad|iphone|ipod).*applewebkit/i.test(ue),xh=n,Sh=l,kh=Kt,Th=O,Oh=Je,Eh=o,Lh=xo,Rh=Su,Ch=Tt,Ph=bh,Ah=wh,jh=th,Ih=xh.setImmediate,Mh=xh.clearImmediate,Nh=xh.process,Fh=xh.Dispatch,Dh=xh.Function,Uh=xh.MessageChannel,Bh=xh.String,Hh=0,zh={},qh="onreadystatechange";Eh((function(){Jp=xh.location}));var Gh=function(e){if(Oh(zh,e)){var t=zh[e];delete zh[e],t()}},$h=function(e){return function(){Gh(e)}},Vh=function(e){Gh(e.data)},Yh=function(e){xh.postMessage(Bh(e),Jp.protocol+"//"+Jp.host)};Ih&&Mh||(Ih=function(e){Ph(arguments.length,1);var t=Th(e)?e:Dh(e),r=Rh(arguments,1);return zh[++Hh]=function(){Sh(t,void 0,r)},Qp(Hh),Hh},Mh=function(e){delete zh[e]},jh?Qp=function(e){Nh.nextTick($h(e))}:Fh&&Fh.now?Qp=function(e){Fh.now($h(e))}:Uh&&!Ah?(eh=(Zp=new Uh).port2,Zp.port1.onmessage=Vh,Qp=kh(eh.postMessage,eh)):xh.addEventListener&&Th(xh.postMessage)&&!xh.importScripts&&Jp&&"file:"!==Jp.protocol&&!Eh(Yh)?(Qp=Yh,xh.addEventListener("message",Vh,!1)):Qp=qh in Ch("script")?function(e){Lh.appendChild(Ch("script"))[qh]=function(){Lh.removeChild(this),Gh(e)}}:function(e){setTimeout($h(e),0)});var Wh={set:Ih,clear:Mh},Kh=function(){this.head=null,this.tail=null};Kh.prototype={add:function(e){var t={item:e,next:null},r=this.tail;r?r.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}};var Xh,Jh,Qh,Zh,ed,td=Kh,rd=/ipad|iphone|ipod/i.test(ue)&&"undefined"!=typeof Pebble,nd=/web0s(?!.*chrome)/i.test(ue),od=n,id=Kt,ad=E.f,ud=Wh.set,sd=td,cd=wh,ld=rd,fd=nd,pd=th,hd=od.MutationObserver||od.WebKitMutationObserver,dd=od.document,vd=od.process,gd=od.Promise,yd=ad(od,"queueMicrotask"),md=yd&&yd.value;if(!md){var _d=new sd,bd=function(){var e,t;for(pd&&(e=vd.domain)&&e.exit();t=_d.get();)try{t()}catch(EP){throw _d.head&&Xh(),EP}e&&e.enter()};cd||pd||fd||!hd||!dd?!ld&&gd&&gd.resolve?((Zh=gd.resolve(void 0)).constructor=gd,ed=id(Zh.then,Zh),Xh=function(){ed(bd)}):pd?Xh=function(){vd.nextTick(bd)}:(ud=id(ud,od),Xh=function(){ud(bd)}):(Jh=!0,Qh=dd.createTextNode(""),new hd(bd).observe(Qh,{characterData:!0}),Xh=function(){Qh.data=Jh=!Jh}),md=function(e){_d.head||Xh(),_d.add(e)}}var wd=md,xd=function(e){try{return{error:!1,value:e()}}catch(EP){return{error:!0,value:EP}}},Sd=n.Promise,kd="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,Td=!kd&&!th&&"object"==typeof window&&"object"==typeof document,Od=n,Ed=Sd,Ld=O,Rd=$t,Cd=nn,Pd=ft,Ad=Td,jd=kd,Id=de,Md=Ed&&Ed.prototype,Nd=Pd("species"),Fd=!1,Dd=Ld(Od.PromiseRejectionEvent),Ud={CONSTRUCTOR:Rd("Promise",(function(){var e=Cd(Ed),t=e!==String(Ed);if(!t&&66===Id)return!0;if(!Md.catch||!Md.finally)return!0;if(!Id||Id<51||!/native code/.test(e)){var r=new Ed((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};if((r.constructor={})[Nd]=n,!(Fd=r.then((function(){}))instanceof n))return!0}return!t&&(Ad||jd)&&!Dd})),REJECTION_EVENT:Dd,SUBCLASSING:Fd},Bd={},Hd=Ce,zd=TypeError,qd=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw zd("Bad Promise constructor");t=e,r=n})),this.resolve=Hd(t),this.reject=Hd(r)};Bd.f=function(e){return new qd(e)};var Gd,$d,Vd=Er,Yd=th,Wd=n,Kd=P,Xd=ni,Jd=Ri,Qd=ah,Zd=Ce,ev=O,tv=Z,rv=ch,nv=mh,ov=Wh.set,iv=wd,av=function(e,t){try{1==arguments.length?console.error(e):console.error(e,t)}catch(EP){}},uv=xd,sv=td,cv=$i,lv=Sd,fv=Ud,pv=Bd,hv="Promise",dv=fv.CONSTRUCTOR,vv=fv.REJECTION_EVENT,gv=cv.getterFor(hv),yv=cv.set,mv=lv&&lv.prototype,_v=lv,bv=mv,wv=Wd.TypeError,xv=Wd.document,Sv=Wd.process,kv=pv.f,Tv=kv,Ov=!!(xv&&xv.createEvent&&Wd.dispatchEvent),Ev="unhandledrejection",Lv=function(e){var t;return!(!tv(e)||!ev(t=e.then))&&t},Rv=function(e,t){var r,n,o,i=t.value,a=1==t.state,u=a?e.ok:e.fail,s=e.resolve,c=e.reject,l=e.domain;try{u?(a||(2===t.rejection&&Iv(t),t.rejection=1),!0===u?r=i:(l&&l.enter(),r=u(i),l&&(l.exit(),o=!0)),r===e.promise?c(wv("Promise-chain cycle")):(n=Lv(r))?Kd(n,r,s,c):s(r)):c(i)}catch(EP){l&&!o&&l.exit(),c(EP)}},Cv=function(e,t){e.notified||(e.notified=!0,iv((function(){for(var r,n=e.reactions;r=n.get();)Rv(r,e);e.notified=!1,t&&!e.rejection&&Av(e)})))},Pv=function(e,t,r){var n,o;Ov?((n=xv.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),Wd.dispatchEvent(n)):n={promise:t,reason:r},!vv&&(o=Wd["on"+e])?o(n):e===Ev&&av("Unhandled promise rejection",r)},Av=function(e){Kd(ov,Wd,(function(){var t,r=e.facade,n=e.value;if(jv(e)&&(t=uv((function(){Yd?Sv.emit("unhandledRejection",n,r):Pv(Ev,r,n)})),e.rejection=Yd||jv(e)?2:1,t.error))throw t.value}))},jv=function(e){return 1!==e.rejection&&!e.parent},Iv=function(e){Kd(ov,Wd,(function(){var t=e.facade;Yd?Sv.emit("rejectionHandled",t):Pv("rejectionhandled",t,e.value)}))},Mv=function(e,t,r){return function(n){e(t,n,r)}},Nv=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,Cv(e,!0))},Fv=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw wv("Promise can't be resolved itself");var n=Lv(t);n?iv((function(){var r={done:!1};try{Kd(n,t,Mv(Fv,r,e),Mv(Nv,r,e))}catch(EP){Nv(r,EP,e)}})):(e.value=t,e.state=1,Cv(e,!1))}catch(EP){Nv({done:!1},EP,e)}}};dv&&(bv=(_v=function(e){rv(this,bv),Zd(e),Kd(Gd,this);var t=gv(this);try{e(Mv(Fv,t),Mv(Nv,t))}catch(EP){Nv(t,EP)}}).prototype,(Gd=function(e){yv(this,{type:hv,done:!1,notified:!1,parent:!1,reactions:new sv,rejection:!1,state:0,value:void 0})}).prototype=Xd(bv,"then",(function(e,t){var r=gv(this),n=kv(nv(this,_v));return r.parent=!0,n.ok=!ev(e)||e,n.fail=ev(t)&&t,n.domain=Yd?Sv.domain:void 0,0==r.state?r.reactions.add(n):iv((function(){Rv(n,r)})),n.promise})),$d=function(){var e=new Gd,t=gv(e);this.promise=e,this.resolve=Mv(Fv,t),this.reject=Mv(Nv,t)},pv.f=kv=function(e){return e===_v||undefined===e?new $d(e):Tv(e)}),Vd({global:!0,constructor:!0,wrap:!0,forced:dv},{Promise:_v}),Jd(_v,hv,!1,!0),Qd(hv);var Dv=ft("iterator"),Uv=!1;try{var Bv=0,Hv={next:function(){return{done:!!Bv++}},return:function(){Uv=!0}};Hv[Dv]=function(){return this},Array.from(Hv,(function(){throw 2}))}catch(EP){}var zv=function(e,t){if(!t&&!Uv)return!1;var r=!1;try{var n={};n[Dv]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(EP){}return r},qv=Sd,Gv=Ud.CONSTRUCTOR||!zv((function(e){qv.all(e).then(void 0,(function(){}))})),$v=P,Vv=Ce,Yv=Bd,Wv=xd,Kv=Ap;Er({target:"Promise",stat:!0,forced:Gv},{all:function(e){var t=this,r=Yv.f(t),n=r.resolve,o=r.reject,i=Wv((function(){var r=Vv(t.resolve),i=[],a=0,u=1;Kv(e,(function(e){var s=a++,c=!1;u++,$v(r,t,e).then((function(e){c||(c=!0,i[s]=e,--u||n(i))}),o)})),--u||n(i)}));return i.error&&o(i.value),r.promise}});var Xv=Er,Jv=Ud.CONSTRUCTOR;Sd&&Sd.prototype,Xv({target:"Promise",proto:!0,forced:Jv,real:!0},{catch:function(e){return this.then(void 0,e)}});var Qv=P,Zv=Ce,eg=Bd,tg=xd,rg=Ap;Er({target:"Promise",stat:!0,forced:Gv},{race:function(e){var t=this,r=eg.f(t),n=r.reject,o=tg((function(){var o=Zv(t.resolve);rg(e,(function(e){Qv(o,t,e).then(r.resolve,n)}))}));return o.error&&n(o.value),r.promise}});var ng=P,og=Bd;Er({target:"Promise",stat:!0,forced:Ud.CONSTRUCTOR},{reject:function(e){var t=og.f(this);return ng(t.reject,void 0,e),t.promise}});var ig=tr,ag=Z,ug=Bd,sg=function(e,t){if(ig(e),ag(t)&&t.constructor===e)return t;var r=ug.f(e);return(0,r.resolve)(t),r.promise},cg=Er,lg=Sd,fg=Ud.CONSTRUCTOR,pg=sg,hg=ie("Promise"),dg=!fg;cg({target:"Promise",stat:!0,forced:true},{resolve:function(e){return pg(dg&&this===hg?lg:this,e)}});var vg=P,gg=Ce,yg=Bd,mg=xd,_g=Ap;Er({target:"Promise",stat:!0,forced:Gv},{allSettled:function(e){var t=this,r=yg.f(t),n=r.resolve,o=r.reject,i=mg((function(){var r=gg(t.resolve),o=[],i=0,a=1;_g(e,(function(e){var u=i++,s=!1;a++,vg(r,t,e).then((function(e){s||(s=!0,o[u]={status:"fulfilled",value:e},--a||n(o))}),(function(e){s||(s=!0,o[u]={status:"rejected",reason:e},--a||n(o))}))})),--a||n(o)}));return i.error&&o(i.value),r.promise}});var bg=P,wg=Ce,xg=ie,Sg=Bd,kg=xd,Tg=Ap,Og="No one promise resolved";Er({target:"Promise",stat:!0,forced:Gv},{any:function(e){var t=this,r=xg("AggregateError"),n=Sg.f(t),o=n.resolve,i=n.reject,a=kg((function(){var n=wg(t.resolve),a=[],u=0,s=1,c=!1;Tg(e,(function(e){var l=u++,f=!1;s++,bg(n,t,e).then((function(e){f||c||(c=!0,o(e))}),(function(e){f||c||(f=!0,a[l]=e,--s||i(new r(a,Og)))}))})),--s||i(new r(a,Og))}));return a.error&&i(a.value),n.promise}});var Eg=Er,Lg=Sd,Rg=o,Cg=ie,Pg=O,Ag=mh,jg=sg,Ig=Lg&&Lg.prototype;Eg({target:"Promise",proto:!0,real:!0,forced:!!Lg&&Rg((function(){Ig.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=Ag(this,Cg("Promise")),r=Pg(e);return this.then(r?function(r){return jg(t,e()).then((function(){return r}))}:e,r?function(r){return jg(t,e()).then((function(){throw r}))}:e)}});var Mg=ee.Promise,Ng=Bd,Fg=xd;Er({target:"Promise",stat:!0,forced:!0},{try:function(e){var t=Ng.f(this),r=Fg(e);return(r.error?t.reject:t.resolve)(r.value),t.promise}});var Dg=t(Mg),Ug=Er,Bg=Rr,Hg=v([].reverse),zg=[1,2];Ug({target:"Array",proto:!0,forced:String(zg)===String(zg.reverse())},{reverse:function(){return Bg(this)&&(this.length=this.length),Hg(this)}});var qg=Ll("Array").reverse,Gg=ae,$g=qg,Vg=Array.prototype,Yg=t((function(e){var t=e.reverse;return e===Vg||Gg(Vg,e)&&t===Vg.reverse?$g:t})),Wg=Er,Kg=Rr,Xg=mn,Jg=Z,Qg=Jn,Zg=Dr,ey=X,ty=Gr,ry=ft,ny=Su,oy=Rn("slice"),iy=ry("species"),ay=Array,uy=Math.max;Wg({target:"Array",proto:!0,forced:!oy},{slice:function(e,t){var r,n,o,i=ey(this),a=Zg(i),u=Qg(e,a),s=Qg(void 0===t?a:t,a);if(Kg(i)&&(r=i.constructor,(Xg(r)&&(r===ay||Kg(r.prototype))||Jg(r)&&null===(r=r[iy]))&&(r=void 0),r===ay||void 0===r))return ny(i,u,s);for(n=new(void 0===r?ay:r)(uy(s-u,0)),o=0;u<s;u++,o++)u in i&&ty(n,o,i[u]);return n.length=o,n}});var sy=Ll("Array").slice,cy=ae,ly=sy,fy=Array.prototype,py=t((function(e){var t=e.slice;return e===fy||cy(fy,e)&&t===fy.slice?ly:t})),hy=v,dy=Ce,vy=Z,gy=Je,yy=Su,my=i,_y=Function,by=hy([].concat),wy=hy([].join),xy={},Sy=my?_y.bind:function(e){var t=dy(this),r=t.prototype,n=yy(arguments,1),o=function(){var r=by(n,yy(arguments));return this instanceof o?function(e,t,r){if(!gy(xy,t)){for(var n=[],o=0;o<t;o++)n[o]="a["+o+"]";xy[t]=_y("C,a","return new C("+wy(n,",")+")")}return xy[t](e,r)}(t,r.length,r):t.apply(e,r)};return vy(r)&&(o.prototype=r),o},ky=Sy;Er({target:"Function",proto:!0,forced:Function.bind!==ky},{bind:ky});var Ty=Ll("Function").bind,Oy=ae,Ey=Ty,Ly=Function.prototype,Ry=t((function(e){var t=e.bind;return e===Ly||Oy(Ly,e)&&t===Ly.bind?Ey:t})),Cy=Er,Py=l,Ay=Sy,jy=hh,Iy=tr,My=Z,Ny=Uo,Fy=o,Dy=ie("Reflect","construct"),Uy=Object.prototype,By=[].push,Hy=Fy((function(){function e(){}return!(Dy((function(){}),[],e)instanceof e)})),zy=!Fy((function(){Dy((function(){}))})),qy=Hy||zy;Cy({target:"Reflect",stat:!0,forced:qy,sham:qy},{construct:function(e,t){jy(e),Iy(t);var r=arguments.length<3?e:jy(arguments[2]);if(zy&&!Hy)return Dy(e,t,r);if(e==r){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var n=[null];return Py(By,n,t),new(Py(Ay,e,n))}var o=r.prototype,i=Ny(My(o)?o:Uy),a=Py(e,i,t);return My(a)?a:i}});var Gy=t(ee.Reflect.construct),$y=t(ai.f("toPrimitive"));function Vy(e,t){var r=kl(e);if(Tl){var n=Tl(e);t&&(n=jl(n).call(n,(function(t){return zl(e,t).enumerable}))),r.push.apply(r,n)}return r}function Yy(e){for(var t=1;t<arguments.length;t++){var r,n,o=null!=arguments[t]?arguments[t]:{};t%2?ef(r=Vy(Object(o),!0)).call(r,(function(t){em(e,t,o[t])})):pf?_f(e,pf(o)):ef(n=Vy(Object(o))).call(n,(function(t){Of(e,t,zl(o,t))}))}return e}function Wy(){Wy=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Of||function(e,t,r){e[t]=r.value},o="function"==typeof Ef?Ef:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function s(e,t,r){return Of(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(E){s=function(e,t,r){return e[t]=r}}function c(e,t,r,o){var i=t&&t.prototype instanceof p?t:p,a=Rf(i.prototype),u=new k(o||[]);return n(a,"_invoke",{value:b(e,r,u)}),a}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(E){return{type:"throw",arg:E}}}e.wrap=c;var f={};function p(){}function h(){}function d(){}var v={};s(v,i,(function(){return this}));var g=jf&&jf(jf(T([])));g&&g!==t&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Rf(v);function m(e){var t;ef(t=["next","throw","return"]).call(t,(function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function o(n,i,a,u){var s=l(e[n],e,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==wl(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){o("next",e,a,u)}),(function(e){o("throw",e,a,u)})):t.resolve(f).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,u)}))}u(s.arg)}var i;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){o(e,r,t,n)}))}return i=i?i.then(n,n):n()}})}function b(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(e,t,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function w(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var o=l(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],ef(e).call(e,x,this),this.reset(!0)}function T(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return If?If(e,d):(e.__proto__=d,s(e,u,"GeneratorFunction")),e.prototype=Rf(y),e},e.awrap=function(e){return{__await:e}},m(_.prototype),s(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,r,n,o,i){void 0===i&&(i=Dg);var a=new _(c(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(y),s(y,u,"Generator"),s(y,i,(function(){return this})),s(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return Yg(r).call(r),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=T,k.prototype={constructor:k,reset:function(e){var t;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,ef(t=this.tryEntries).call(t,S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+py(n).call(n,1))&&(this[n]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:T(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},e}function Ky(e,t,r,n,o,i,a){try{var u=e[i](a),s=u.value}catch(EP){return void r(EP)}u.done?t(s):Dg.resolve(s).then(n,o)}function Xy(e){return function(){var t=this,r=arguments;return new Dg((function(n,o){var i=e.apply(t,r);function a(e){Ky(i,n,o,a,u,"next",e)}function u(e){Ky(i,n,o,a,u,"throw",e)}a(void 0)}))}}function Jy(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qy(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Of(e,am(n.key),n)}}function Zy(e,t,r){return t&&Qy(e.prototype,t),r&&Qy(e,r),Of(e,"prototype",{writable:!1}),e}function em(e,t,r){return(t=am(t))in e?Of(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tm(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Rf(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Of(e,"prototype",{writable:!1}),t&&nm(e,t)}function rm(e){var t;return(rm=If?Ry(t=jf).call(t):function(e){return e.__proto__||jf(e)})(e)}function nm(e,t){var r;return(nm=If?Ry(r=If).call(r):function(e,t){return e.__proto__=t,e})(e,t)}function om(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function im(e){var t=function(){if("undefined"==typeof Reflect||!Gy)return!1;if(Gy.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Gy(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=rm(e);if(t){var o=rm(this).constructor;r=Gy(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===wl(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return om(e)}(this,r)}}function am(e){var t=function(e,t){if("object"!==wl(e)||null===e)return e;var r=e[$y];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==wl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===wl(t)?t:String(t)}var um=Ll("Array").concat,sm=ae,cm=um,lm=Array.prototype,fm=t((function(e){var t=e.concat;return e===lm||sm(lm,e)&&t===lm.concat?cm:t})),pm=o,hm=ft("iterator"),dm=!pm((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,r="";return e.pathname="c%20d",t.forEach((function(e,n){t.delete("b"),r+=n+e})),!e.toJSON||!t.size&&true||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[hm]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==r||"x"!==new URL("http://x",void 0).host})),vm=L,gm=v,ym=P,mm=o,_m=ho,bm=ti,wm=A,xm=We,Sm=q,km=Object.assign,Tm=Object.defineProperty,Om=gm([].concat),Em=!km||mm((function(){if(vm&&1!==km({b:1},km(Tm({},"a",{enumerable:!0,get:function(){Tm(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach((function(e){t[e]=e})),7!=km({},e)[r]||_m(km({},t)).join("")!=n}))?function(e,t){for(var r=xm(e),n=arguments.length,o=1,i=bm.f,a=wm.f;n>o;)for(var u,s=Sm(arguments[o++]),c=i?Om(_m(s),i(s)):_m(s),l=c.length,f=0;l>f;)u=c[f++],vm&&!ym(a,s,u)||(r[u]=s[u]);return r}:km,Lm=tr,Rm=mp,Cm=Kt,Pm=P,Am=We,jm=function(e,t,r,n){try{return n?t(Lm(r)[0],r[1]):t(r)}catch(EP){Rm(e,"throw",EP)}},Im=tp,Mm=mn,Nm=Dr,Fm=Gr,Dm=dp,Um=up,Bm=Array,Hm=function(e){var t=Am(e),r=Mm(this),n=arguments.length,o=n>1?arguments[1]:void 0,i=void 0!==o;i&&(o=Cm(o,n>2?arguments[2]:void 0));var a,u,s,c,l,f,p=Um(t),h=0;if(!p||this===Bm&&Im(p))for(a=Nm(t),u=r?new this(a):Bm(a);a>h;h++)f=i?o(t[h],h):t[h],Fm(u,h,f);else for(l=(c=Dm(t,p)).next,u=r?new this:[];!(s=Pm(l,c)).done;h++)f=i?jm(c,o,[s.value,h],!0):s.value,Fm(u,h,f);return u.length=h,u},zm=v,qm=2147483647,Gm=/[^\0-\u007E]/,$m=/[.\u3002\uFF0E\uFF61]/g,Vm="Overflow: input needs wider integers to process",Ym=RangeError,Wm=zm($m.exec),Km=Math.floor,Xm=String.fromCharCode,Jm=zm("".charCodeAt),Qm=zm([].join),Zm=zm([].push),e_=zm("".replace),t_=zm("".split),r_=zm("".toLowerCase),n_=function(e){return e+22+75*(e<26)},o_=function(e,t,r){var n=0;for(e=r?Km(e/700):e>>1,e+=Km(e/t);e>455;)e=Km(e/35),n+=36;return Km(n+36*e/(e+38))},i_=function(e){var t=[];e=function(e){for(var t=[],r=0,n=e.length;r<n;){var o=Jm(e,r++);if(o>=55296&&o<=56319&&r<n){var i=Jm(e,r++);56320==(64512&i)?Zm(t,((1023&o)<<10)+(1023&i)+65536):(Zm(t,o),r--)}else Zm(t,o)}return t}(e);var r,n,o=e.length,i=128,a=0,u=72;for(r=0;r<e.length;r++)(n=e[r])<128&&Zm(t,Xm(n));var s=t.length,c=s;for(s&&Zm(t,"-");c<o;){var l=qm;for(r=0;r<e.length;r++)(n=e[r])>=i&&n<l&&(l=n);var f=c+1;if(l-i>Km((qm-a)/f))throw Ym(Vm);for(a+=(l-i)*f,i=l,r=0;r<e.length;r++){if((n=e[r])<i&&++a>qm)throw Ym(Vm);if(n==i){for(var p=a,h=36;;){var d=h<=u?1:h>=u+26?26:h-u;if(p<d)break;var v=p-d,g=36-d;Zm(t,Xm(n_(d+v%g))),p=Km(v/g),h+=36}Zm(t,Xm(n_(p))),u=o_(a,f,c==s),a=0,c++}}a++,i++}return Qm(t,"")},a_=ni,u_=function(e,t,r){for(var n in t)r&&r.unsafe&&e[n]?e[n]=t[n]:a_(e,n,t[n],r);return e},s_=Ko,c_=Math.floor,l_=function(e,t){var r=e.length,n=c_(r/2);return r<8?f_(e,t):p_(e,l_(s_(e,0,n),t),l_(s_(e,n),t),t)},f_=function(e,t){for(var r,n,o=e.length,i=1;i<o;){for(n=i,r=e[i];n&&t(e[n-1],r)>0;)e[n]=e[--n];n!==i++&&(e[n]=r)}return e},p_=function(e,t,r,n){for(var o=t.length,i=r.length,a=0,u=0;a<o||u<i;)e[a+u]=a<o&&u<i?n(t[a],r[u])<=0?t[a++]:r[u++]:a<o?t[a++]:r[u++];return e},h_=l_,d_=Er,v_=n,g_=P,y_=v,m_=L,__=dm,b_=ni,w_=ii,x_=u_,S_=Ri,k_=Hs,T_=$i,O_=ch,E_=O,L_=Je,R_=Kt,C_=Zr,P_=tr,A_=Z,j_=Vn,I_=Uo,M_=D,N_=dp,F_=up,D_=bh,U_=h_,B_=ft("iterator"),H_="URLSearchParams",z_=H_+"Iterator",q_=T_.set,G_=T_.getterFor(H_),$_=T_.getterFor(z_),V_=Object.getOwnPropertyDescriptor,Y_=function(e){if(!m_)return v_[e];var t=V_(v_,e);return t&&t.value},W_=Y_("fetch"),K_=Y_("Request"),X_=Y_("Headers"),J_=K_&&K_.prototype,Q_=X_&&X_.prototype,Z_=v_.RegExp,eb=v_.TypeError,tb=v_.decodeURIComponent,rb=v_.encodeURIComponent,nb=y_("".charAt),ob=y_([].join),ib=y_([].push),ab=y_("".replace),ub=y_([].shift),sb=y_([].splice),cb=y_("".split),lb=y_("".slice),fb=/\+/g,pb=Array(4),hb=function(e){return pb[e-1]||(pb[e-1]=Z_("((?:%[\\da-f]{2}){"+e+"})","gi"))},db=function(e){try{return tb(e)}catch(EP){return e}},vb=function(e){var t=ab(e,fb," "),r=4;try{return tb(t)}catch(EP){for(;r;)t=ab(t,hb(r--),db);return t}},gb=/[!'()~]|%20/g,yb={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},mb=function(e){return yb[e]},_b=function(e){return ab(rb(e),gb,mb)},bb=k_((function(e,t){q_(this,{type:z_,iterator:N_(G_(e).entries),kind:t})}),"Iterator",(function(){var e=$_(this),t=e.kind,r=e.iterator.next(),n=r.value;return r.done||(r.value="keys"===t?n.key:"values"===t?n.value:[n.key,n.value]),r}),!0),wb=function(e){this.entries=[],this.url=null,void 0!==e&&(A_(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===nb(e,0)?lb(e,1):e:j_(e)))};wb.prototype={type:H_,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,r,n,o,i,a,u,s=F_(e);if(s)for(r=(t=N_(e,s)).next;!(n=g_(r,t)).done;){if(i=(o=N_(P_(n.value))).next,(a=g_(i,o)).done||(u=g_(i,o)).done||!g_(i,o).done)throw eb("Expected sequence with length 2");ib(this.entries,{key:j_(a.value),value:j_(u.value)})}else for(var c in e)L_(e,c)&&ib(this.entries,{key:c,value:j_(e[c])})},parseQuery:function(e){if(e)for(var t,r,n=cb(e,"&"),o=0;o<n.length;)(t=n[o++]).length&&(r=cb(t,"="),ib(this.entries,{key:vb(ub(r)),value:vb(ob(r,"="))}))},serialize:function(){for(var e,t=this.entries,r=[],n=0;n<t.length;)e=t[n++],ib(r,_b(e.key)+"="+_b(e.value));return ob(r,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var xb=function(){O_(this,Sb);var e=q_(this,new wb(arguments.length>0?arguments[0]:void 0));m_||(this.length=e.entries.length)},Sb=xb.prototype;if(x_(Sb,{append:function(e,t){D_(arguments.length,2);var r=G_(this);ib(r.entries,{key:j_(e),value:j_(t)}),m_||this.length++,r.updateURL()},delete:function(e){D_(arguments.length,1);for(var t=G_(this),r=t.entries,n=j_(e),o=0;o<r.length;)r[o].key===n?sb(r,o,1):o++;m_||(this.length=r.length),t.updateURL()},get:function(e){D_(arguments.length,1);for(var t=G_(this).entries,r=j_(e),n=0;n<t.length;n++)if(t[n].key===r)return t[n].value;return null},getAll:function(e){D_(arguments.length,1);for(var t=G_(this).entries,r=j_(e),n=[],o=0;o<t.length;o++)t[o].key===r&&ib(n,t[o].value);return n},has:function(e){D_(arguments.length,1);for(var t=G_(this).entries,r=j_(e),n=0;n<t.length;)if(t[n++].key===r)return!0;return!1},set:function(e,t){D_(arguments.length,1);for(var r,n=G_(this),o=n.entries,i=!1,a=j_(e),u=j_(t),s=0;s<o.length;s++)(r=o[s]).key===a&&(i?sb(o,s--,1):(i=!0,r.value=u));i||ib(o,{key:a,value:u}),m_||(this.length=o.length),n.updateURL()},sort:function(){var e=G_(this);U_(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){for(var t,r=G_(this).entries,n=R_(e,arguments.length>1?arguments[1]:void 0),o=0;o<r.length;)n((t=r[o++]).value,t.key,this)},keys:function(){return new bb(this,"keys")},values:function(){return new bb(this,"values")},entries:function(){return new bb(this,"entries")}},{enumerable:!0}),b_(Sb,B_,Sb.entries,{name:"entries"}),b_(Sb,"toString",(function(){return G_(this).serialize()}),{enumerable:!0}),m_&&w_(Sb,"size",{get:function(){return G_(this).entries.length},configurable:!0,enumerable:!0}),S_(xb,H_),d_({global:!0,constructor:!0,forced:!__},{URLSearchParams:xb}),!__&&E_(X_)){var kb=y_(Q_.has),Tb=y_(Q_.set),Ob=function(e){if(A_(e)){var t,r=e.body;if(C_(r)===H_)return t=e.headers?new X_(e.headers):new X_,kb(t,"content-type")||Tb(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),I_(e,{body:M_(0,j_(r)),headers:M_(0,t)})}return e};if(E_(W_)&&d_({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return W_(e,arguments.length>1?Ob(arguments[1]):{})}}),E_(K_)){var Eb=function(e){return O_(this,J_),new K_(e,arguments.length>1?Ob(arguments[1]):{})};J_.constructor=Eb,Eb.prototype=J_,d_({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:Eb})}}var Lb,Rb=Er,Cb=L,Pb=dm,Ab=n,jb=Kt,Ib=v,Mb=ni,Nb=ii,Fb=ch,Db=Je,Ub=Em,Bb=Hm,Hb=Ko,zb=ll.codeAt,qb=function(e){var t,r,n=[],o=t_(e_(r_(e),$m,"."),".");for(t=0;t<o.length;t++)r=o[t],Zm(n,Wm(Gm,r)?"xn--"+i_(r):r);return Qm(n,".")},Gb=Vn,$b=Ri,Vb=bh,Yb={URLSearchParams:xb,getState:G_},Wb=$i,Kb=Wb.set,Xb=Wb.getterFor("URL"),Jb=Yb.URLSearchParams,Qb=Yb.getState,Zb=Ab.URL,ew=Ab.TypeError,tw=Ab.parseInt,rw=Math.floor,nw=Math.pow,ow=Ib("".charAt),iw=Ib(/./.exec),aw=Ib([].join),uw=Ib(1..toString),sw=Ib([].pop),cw=Ib([].push),lw=Ib("".replace),fw=Ib([].shift),pw=Ib("".split),hw=Ib("".slice),dw=Ib("".toLowerCase),vw=Ib([].unshift),gw="Invalid scheme",yw="Invalid host",mw="Invalid port",_w=/[a-z]/i,bw=/[\d+-.a-z]/i,ww=/\d/,xw=/^0x/i,Sw=/^[0-7]+$/,kw=/^\d+$/,Tw=/^[\da-f]+$/i,Ow=/[\0\t\n\r #%/:<>?@[\\\]^|]/,Ew=/[\0\t\n\r #/:<>?@[\\\]^|]/,Lw=/^[\u0000-\u0020]+/,Rw=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,Cw=/[\t\n\r]/g,Pw=function(e){var t,r,n,o;if("number"==typeof e){for(t=[],r=0;r<4;r++)vw(t,e%256),e=rw(e/256);return aw(t,".")}if("object"==typeof e){for(t="",n=function(e){for(var t=null,r=1,n=null,o=0,i=0;i<8;i++)0!==e[i]?(o>r&&(t=n,r=o),n=null,o=0):(null===n&&(n=i),++o);return o>r&&(t=n,r=o),t}(e),r=0;r<8;r++)o&&0===e[r]||(o&&(o=!1),n===r?(t+=r?":":"::",o=!0):(t+=uw(e[r],16),r<7&&(t+=":")));return"["+t+"]"}return e},Aw={},jw=Ub({},Aw,{" ":1,'"':1,"<":1,">":1,"`":1}),Iw=Ub({},jw,{"#":1,"?":1,"{":1,"}":1}),Mw=Ub({},Iw,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Nw=function(e,t){var r=zb(e,0);return r>32&&r<127&&!Db(t,e)?e:encodeURIComponent(e)},Fw={ftp:21,file:null,http:80,https:443,ws:80,wss:443},Dw=function(e,t){var r;return 2==e.length&&iw(_w,ow(e,0))&&(":"==(r=ow(e,1))||!t&&"|"==r)},Uw=function(e){var t;return e.length>1&&Dw(hw(e,0,2))&&(2==e.length||"/"===(t=ow(e,2))||"\\"===t||"?"===t||"#"===t)},Bw=function(e){return"."===e||"%2e"===dw(e)},Hw={},zw={},qw={},Gw={},$w={},Vw={},Yw={},Ww={},Kw={},Xw={},Jw={},Qw={},Zw={},ex={},tx={},rx={},nx={},ox={},ix={},ax={},ux={},sx=function(e,t,r){var n,o,i,a=Gb(e);if(t){if(o=this.parse(a))throw ew(o);this.searchParams=null}else{if(void 0!==r&&(n=new sx(r,!0)),o=this.parse(a,null,n))throw ew(o);(i=Qb(new Jb)).bindURL(this),this.searchParams=i}};sx.prototype={type:"URL",parse:function(e,t,r){var n,o,i,a,u,s=this,c=t||Hw,l=0,f="",p=!1,h=!1,d=!1;for(e=Gb(e),t||(s.scheme="",s.username="",s.password="",s.host=null,s.port=null,s.path=[],s.query=null,s.fragment=null,s.cannotBeABaseURL=!1,e=lw(e,Lw,""),e=lw(e,Rw,"$1")),e=lw(e,Cw,""),n=Bb(e);l<=n.length;){switch(o=n[l],c){case Hw:if(!o||!iw(_w,o)){if(t)return gw;c=qw;continue}f+=dw(o),c=zw;break;case zw:if(o&&(iw(bw,o)||"+"==o||"-"==o||"."==o))f+=dw(o);else{if(":"!=o){if(t)return gw;f="",c=qw,l=0;continue}if(t&&(s.isSpecial()!=Db(Fw,f)||"file"==f&&(s.includesCredentials()||null!==s.port)||"file"==s.scheme&&!s.host))return;if(s.scheme=f,t)return void(s.isSpecial()&&Fw[s.scheme]==s.port&&(s.port=null));f="","file"==s.scheme?c=ex:s.isSpecial()&&r&&r.scheme==s.scheme?c=Gw:s.isSpecial()?c=Ww:"/"==n[l+1]?(c=$w,l++):(s.cannotBeABaseURL=!0,cw(s.path,""),c=ix)}break;case qw:if(!r||r.cannotBeABaseURL&&"#"!=o)return gw;if(r.cannotBeABaseURL&&"#"==o){s.scheme=r.scheme,s.path=Hb(r.path),s.query=r.query,s.fragment="",s.cannotBeABaseURL=!0,c=ux;break}c="file"==r.scheme?ex:Vw;continue;case Gw:if("/"!=o||"/"!=n[l+1]){c=Vw;continue}c=Kw,l++;break;case $w:if("/"==o){c=Xw;break}c=ox;continue;case Vw:if(s.scheme=r.scheme,o==Lb)s.username=r.username,s.password=r.password,s.host=r.host,s.port=r.port,s.path=Hb(r.path),s.query=r.query;else if("/"==o||"\\"==o&&s.isSpecial())c=Yw;else if("?"==o)s.username=r.username,s.password=r.password,s.host=r.host,s.port=r.port,s.path=Hb(r.path),s.query="",c=ax;else{if("#"!=o){s.username=r.username,s.password=r.password,s.host=r.host,s.port=r.port,s.path=Hb(r.path),s.path.length--,c=ox;continue}s.username=r.username,s.password=r.password,s.host=r.host,s.port=r.port,s.path=Hb(r.path),s.query=r.query,s.fragment="",c=ux}break;case Yw:if(!s.isSpecial()||"/"!=o&&"\\"!=o){if("/"!=o){s.username=r.username,s.password=r.password,s.host=r.host,s.port=r.port,c=ox;continue}c=Xw}else c=Kw;break;case Ww:if(c=Kw,"/"!=o||"/"!=ow(f,l+1))continue;l++;break;case Kw:if("/"!=o&&"\\"!=o){c=Xw;continue}break;case Xw:if("@"==o){p&&(f="%40"+f),p=!0,i=Bb(f);for(var v=0;v<i.length;v++){var g=i[v];if(":"!=g||d){var y=Nw(g,Mw);d?s.password+=y:s.username+=y}else d=!0}f=""}else if(o==Lb||"/"==o||"?"==o||"#"==o||"\\"==o&&s.isSpecial()){if(p&&""==f)return"Invalid authority";l-=Bb(f).length+1,f="",c=Jw}else f+=o;break;case Jw:case Qw:if(t&&"file"==s.scheme){c=rx;continue}if(":"!=o||h){if(o==Lb||"/"==o||"?"==o||"#"==o||"\\"==o&&s.isSpecial()){if(s.isSpecial()&&""==f)return yw;if(t&&""==f&&(s.includesCredentials()||null!==s.port))return;if(a=s.parseHost(f))return a;if(f="",c=nx,t)return;continue}"["==o?h=!0:"]"==o&&(h=!1),f+=o}else{if(""==f)return yw;if(a=s.parseHost(f))return a;if(f="",c=Zw,t==Qw)return}break;case Zw:if(!iw(ww,o)){if(o==Lb||"/"==o||"?"==o||"#"==o||"\\"==o&&s.isSpecial()||t){if(""!=f){var m=tw(f,10);if(m>65535)return mw;s.port=s.isSpecial()&&m===Fw[s.scheme]?null:m,f=""}if(t)return;c=nx;continue}return mw}f+=o;break;case ex:if(s.scheme="file","/"==o||"\\"==o)c=tx;else{if(!r||"file"!=r.scheme){c=ox;continue}if(o==Lb)s.host=r.host,s.path=Hb(r.path),s.query=r.query;else if("?"==o)s.host=r.host,s.path=Hb(r.path),s.query="",c=ax;else{if("#"!=o){Uw(aw(Hb(n,l),""))||(s.host=r.host,s.path=Hb(r.path),s.shortenPath()),c=ox;continue}s.host=r.host,s.path=Hb(r.path),s.query=r.query,s.fragment="",c=ux}}break;case tx:if("/"==o||"\\"==o){c=rx;break}r&&"file"==r.scheme&&!Uw(aw(Hb(n,l),""))&&(Dw(r.path[0],!0)?cw(s.path,r.path[0]):s.host=r.host),c=ox;continue;case rx:if(o==Lb||"/"==o||"\\"==o||"?"==o||"#"==o){if(!t&&Dw(f))c=ox;else if(""==f){if(s.host="",t)return;c=nx}else{if(a=s.parseHost(f))return a;if("localhost"==s.host&&(s.host=""),t)return;f="",c=nx}continue}f+=o;break;case nx:if(s.isSpecial()){if(c=ox,"/"!=o&&"\\"!=o)continue}else if(t||"?"!=o)if(t||"#"!=o){if(o!=Lb&&(c=ox,"/"!=o))continue}else s.fragment="",c=ux;else s.query="",c=ax;break;case ox:if(o==Lb||"/"==o||"\\"==o&&s.isSpecial()||!t&&("?"==o||"#"==o)){if(".."===(u=dw(u=f))||"%2e."===u||".%2e"===u||"%2e%2e"===u?(s.shortenPath(),"/"==o||"\\"==o&&s.isSpecial()||cw(s.path,"")):Bw(f)?"/"==o||"\\"==o&&s.isSpecial()||cw(s.path,""):("file"==s.scheme&&!s.path.length&&Dw(f)&&(s.host&&(s.host=""),f=ow(f,0)+":"),cw(s.path,f)),f="","file"==s.scheme&&(o==Lb||"?"==o||"#"==o))for(;s.path.length>1&&""===s.path[0];)fw(s.path);"?"==o?(s.query="",c=ax):"#"==o&&(s.fragment="",c=ux)}else f+=Nw(o,Iw);break;case ix:"?"==o?(s.query="",c=ax):"#"==o?(s.fragment="",c=ux):o!=Lb&&(s.path[0]+=Nw(o,Aw));break;case ax:t||"#"!=o?o!=Lb&&("'"==o&&s.isSpecial()?s.query+="%27":s.query+="#"==o?"%23":Nw(o,Aw)):(s.fragment="",c=ux);break;case ux:o!=Lb&&(s.fragment+=Nw(o,jw))}l++}},parseHost:function(e){var t,r,n;if("["==ow(e,0)){if("]"!=ow(e,e.length-1))return yw;if(t=function(e){var t,r,n,o,i,a,u,s=[0,0,0,0,0,0,0,0],c=0,l=null,f=0,p=function(){return ow(e,f)};if(":"==p()){if(":"!=ow(e,1))return;f+=2,l=++c}for(;p();){if(8==c)return;if(":"!=p()){for(t=r=0;r<4&&iw(Tw,p());)t=16*t+tw(p(),16),f++,r++;if("."==p()){if(0==r)return;if(f-=r,c>6)return;for(n=0;p();){if(o=null,n>0){if(!("."==p()&&n<4))return;f++}if(!iw(ww,p()))return;for(;iw(ww,p());){if(i=tw(p(),10),null===o)o=i;else{if(0==o)return;o=10*o+i}if(o>255)return;f++}s[c]=256*s[c]+o,2!=++n&&4!=n||c++}if(4!=n)return;break}if(":"==p()){if(f++,!p())return}else if(p())return;s[c++]=t}else{if(null!==l)return;f++,l=++c}}if(null!==l)for(a=c-l,c=7;0!=c&&a>0;)u=s[c],s[c--]=s[l+a-1],s[l+--a]=u;else if(8!=c)return;return s}(hw(e,1,-1)),!t)return yw;this.host=t}else if(this.isSpecial()){if(e=qb(e),iw(Ow,e))return yw;if(t=function(e){var t,r,n,o,i,a,u,s=pw(e,".");if(s.length&&""==s[s.length-1]&&s.length--,(t=s.length)>4)return e;for(r=[],n=0;n<t;n++){if(""==(o=s[n]))return e;if(i=10,o.length>1&&"0"==ow(o,0)&&(i=iw(xw,o)?16:8,o=hw(o,8==i?1:2)),""===o)a=0;else{if(!iw(10==i?kw:8==i?Sw:Tw,o))return e;a=tw(o,i)}cw(r,a)}for(n=0;n<t;n++)if(a=r[n],n==t-1){if(a>=nw(256,5-t))return null}else if(a>255)return null;for(u=sw(r),n=0;n<r.length;n++)u+=r[n]*nw(256,3-n);return u}(e),null===t)return yw;this.host=t}else{if(iw(Ew,e))return yw;for(t="",r=Bb(e),n=0;n<r.length;n++)t+=Nw(r[n],Aw);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"==this.scheme},includesCredentials:function(){return""!=this.username||""!=this.password},isSpecial:function(){return Db(Fw,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"==this.scheme&&1==t&&Dw(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,r=e.username,n=e.password,o=e.host,i=e.port,a=e.path,u=e.query,s=e.fragment,c=t+":";return null!==o?(c+="//",e.includesCredentials()&&(c+=r+(n?":"+n:"")+"@"),c+=Pw(o),null!==i&&(c+=":"+i)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?a[0]:a.length?"/"+aw(a,"/"):"",null!==u&&(c+="?"+u),null!==s&&(c+="#"+s),c},setHref:function(e){var t=this.parse(e);if(t)throw ew(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"==e)try{return new cx(e.path[0]).origin}catch(EP){return"null"}return"file"!=e&&this.isSpecial()?e+"://"+Pw(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(Gb(e)+":",Hw)},getUsername:function(){return this.username},setUsername:function(e){var t=Bb(Gb(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var r=0;r<t.length;r++)this.username+=Nw(t[r],Mw)}},getPassword:function(){return this.password},setPassword:function(e){var t=Bb(Gb(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var r=0;r<t.length;r++)this.password+=Nw(t[r],Mw)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?Pw(e):Pw(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,Jw)},getHostname:function(){var e=this.host;return null===e?"":Pw(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,Qw)},getPort:function(){var e=this.port;return null===e?"":Gb(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""==(e=Gb(e))?this.port=null:this.parse(e,Zw))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+aw(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,nx))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""==(e=Gb(e))?this.query=null:("?"==ow(e,0)&&(e=hw(e,1)),this.query="",this.parse(e,ax)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!=(e=Gb(e))?("#"==ow(e,0)&&(e=hw(e,1)),this.fragment="",this.parse(e,ux)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var cx=function(e){var t=Fb(this,lx),r=Vb(arguments.length,1)>1?arguments[1]:void 0,n=Kb(t,new sx(e,!1,r));Cb||(t.href=n.serialize(),t.origin=n.getOrigin(),t.protocol=n.getProtocol(),t.username=n.getUsername(),t.password=n.getPassword(),t.host=n.getHost(),t.hostname=n.getHostname(),t.port=n.getPort(),t.pathname=n.getPathname(),t.search=n.getSearch(),t.searchParams=n.getSearchParams(),t.hash=n.getHash())},lx=cx.prototype,fx=function(e,t){return{get:function(){return Xb(this)[e]()},set:t&&function(e){return Xb(this)[t](e)},configurable:!0,enumerable:!0}};if(Cb&&(Nb(lx,"href",fx("serialize","setHref")),Nb(lx,"origin",fx("getOrigin")),Nb(lx,"protocol",fx("getProtocol","setProtocol")),Nb(lx,"username",fx("getUsername","setUsername")),Nb(lx,"password",fx("getPassword","setPassword")),Nb(lx,"host",fx("getHost","setHost")),Nb(lx,"hostname",fx("getHostname","setHostname")),Nb(lx,"port",fx("getPort","setPort")),Nb(lx,"pathname",fx("getPathname","setPathname")),Nb(lx,"search",fx("getSearch","setSearch")),Nb(lx,"searchParams",fx("getSearchParams")),Nb(lx,"hash",fx("getHash","setHash"))),Mb(lx,"toJSON",(function(){return Xb(this).serialize()}),{enumerable:!0}),Mb(lx,"toString",(function(){return Xb(this).serialize()}),{enumerable:!0}),Zb){var px=Zb.createObjectURL,hx=Zb.revokeObjectURL;px&&Mb(cx,"createObjectURL",jb(px,Zb)),hx&&Mb(cx,"revokeObjectURL",jb(hx,Zb))}$b(cx,"URL"),Rb({global:!0,constructor:!0,forced:!Pb,sham:!Cb},{URL:cx});var dx=Er,vx=o,gx=bh,yx=Vn,mx=dm,_x=ie("URL");dx({target:"URL",stat:!0,forced:!(mx&&vx((function(){_x.canParse()})))},{canParse:function(e){var t=gx(arguments.length,1),r=yx(e),n=t<2||void 0===arguments[1]?void 0:yx(arguments[1]);try{return!!new _x(r,n)}catch(EP){return!1}}});var bx=t(ee.URL),wx=Er,xx=ro.indexOf,Sx=Gl,kx=x([].indexOf),Tx=!!kx&&1/kx([1],1,-0)<0;wx({target:"Array",proto:!0,forced:Tx||!Sx("indexOf")},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return Tx?kx(this,e,t)||0:xx(this,e,t)}});var Ox=Ll("Array").indexOf,Ex=ae,Lx=Ox,Rx=Array.prototype,Cx=t((function(e){var t=e.indexOf;return e===Rx||Ex(Rx,e)&&t===Rx.indexOf?Lx:t})),Px=L,Ax=Rr,jx=TypeError,Ix=Object.getOwnPropertyDescriptor,Mx=Px&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(EP){return EP instanceof TypeError}}(),Nx=Oe,Fx=TypeError,Dx=function(e,t){if(!delete e[t])throw Fx("Cannot delete property "+Nx(t)+" of "+Nx(e))},Ux=Er,Bx=We,Hx=Jn,zx=jr,qx=Dr,Gx=Mx?function(e,t){if(Ax(e)&&!Ix(e,"length").writable)throw jx("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t},$x=Br,Vx=Tn,Yx=Gr,Wx=Dx,Kx=Rn("splice"),Xx=Math.max,Jx=Math.min;Ux({target:"Array",proto:!0,forced:!Kx},{splice:function(e,t){var r,n,o,i,a,u,s=Bx(this),c=qx(s),l=Hx(e,c),f=arguments.length;for(0===f?r=n=0:1===f?(r=0,n=c-l):(r=f-2,n=Jx(Xx(zx(t),0),c-l)),$x(c+r-n),o=Vx(s,n),i=0;i<n;i++)(a=l+i)in s&&Yx(o,i,s[a]);if(o.length=n,r<n){for(i=l;i<c-n;i++)u=i+r,(a=i+n)in s?s[u]=s[a]:Wx(s,u);for(i=c;i>c-n+r;i--)Wx(s,i-1)}else if(r>n)for(i=c-n;i>l;i--)u=i+r-1,(a=i+n-1)in s?s[u]=s[a]:Wx(s,u);for(i=0;i<r;i++)s[i+l]=arguments[i+2];return Gx(s,c-n+r),o}});var Qx=Ll("Array").splice,Zx=ae,eS=Qx,tS=Array.prototype,rS=t((function(e){var t=e.splice;return e===tS||Zx(tS,e)&&t===tS.splice?eS:t}));Er({target:"Number",stat:!0},{isNaN:function(e){return e!=e}});var nS=t(ee.Number.isNaN),oS="\t\n\v\f\r                　\u2028\u2029\ufeff",iS=Y,aS=Vn,uS=oS,sS=v("".replace),cS=RegExp("^["+uS+"]+"),lS=RegExp("(^|[^"+uS+"])["+uS+"]+$"),fS=function(e){return function(t){var r=aS(iS(t));return 1&e&&(r=sS(r,cS,"")),2&e&&(r=sS(r,lS,"$1")),r}},pS={start:fS(1),end:fS(2),trim:fS(3)},hS=n,dS=o,vS=v,gS=Vn,yS=pS.trim,mS=oS,_S=hS.parseInt,bS=hS.Symbol,wS=bS&&bS.iterator,xS=/^[+-]?0x/i,SS=vS(xS.exec),kS=8!==_S(mS+"08")||22!==_S(mS+"0x16")||wS&&!dS((function(){_S(Object(wS))}))?function(e,t){var r=yS(gS(e));return _S(r,t>>>0||(SS(xS,r)?16:10))}:_S;Er({global:!0,forced:parseInt!=kS},{parseInt:kS});var TS=t(ee.parseInt),OS=Zi.map;Er({target:"Array",proto:!0,forced:!Rn("map")},{map:function(e){return OS(this,e,arguments.length>1?arguments[1]:void 0)}});var ES=Ll("Array").map,LS=ae,RS=ES,CS=Array.prototype,PS=t((function(e){var t=e.map;return e===CS||LS(CS,e)&&t===CS.map?RS:t}));Er({target:"Array",stat:!0},{isArray:Rr});var AS=t(ee.Array.isArray),jS=t(_l),IS=Je,MS=P,NS=Z,FS=tr,DS=function(e){return void 0!==e&&(IS(e,"value")||IS(e,"writable"))},US=E,BS=ks;Er({target:"Reflect",stat:!0},{get:function e(t,r){var n,o,i=arguments.length<3?t:arguments[2];return FS(t)===i?t[r]:(n=US.f(t,r))?DS(n)?n.value:void 0===n.get?void 0:MS(n.get,i):NS(o=BS(t))?e(o,r,i):void 0}});var HS=t(ee.Reflect.get),zS=t(up),qS=Hm;Er({target:"Array",stat:!0,forced:!zv((function(e){Array.from(e)}))},{from:qS});var GS=t(ee.Array.from);function $S(e,t){var r=kl(e);if(Tl){var n=Tl(e);t&&(n=jl(n).call(n,(function(t){return zl(e,t).enumerable}))),r.push.apply(r,n)}return r}function VS(e){for(var t=1;t<arguments.length;t++){var r,n,o=null!=arguments[t]?arguments[t]:{};t%2?ef(r=$S(Object(o),!0)).call(r,(function(t){JS(e,t,o[t])})):pf?_f(e,pf(o)):ef(n=$S(Object(o))).call(n,(function(t){Of(e,t,zl(o,t))}))}return e}function YS(e){return(YS="function"==typeof Ef&&"symbol"==typeof jS?function(e){return typeof e}:function(e){return e&&"function"==typeof Ef&&e.constructor===Ef&&e!==Ef.prototype?"symbol":typeof e})(e)}function WS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function KS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Of(e,ek(n.key),n)}}function XS(e,t,r){return t&&KS(e.prototype,t),r&&KS(e,r),Of(e,"prototype",{writable:!1}),e}function JS(e,t,r){return(t=ek(t))in e?Of(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function QS(e){return function(e){if(AS(e))return ZS(e)}(e)||function(e){if(void 0!==Ef&&null!=zS(e)||null!=e["@@iterator"])return GS(e)}(e)||function(e,t){var r;if(!e)return;if("string"==typeof e)return ZS(e,t);var n=py(r=Object.prototype.toString.call(e)).call(r,8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return GS(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ZS(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ZS(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ek(e){var t=function(e,t){if("object"!==wl(e)||null===e)return e;var r=e[$y];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==wl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===wl(t)?t:String(t)}var tk=Em;Er({target:"Object",stat:!0,arity:2,forced:Object.assign!==tk},{assign:tk});var rk=t(ee.Object.assign),nk={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var u=new o(n,i||e,a),s=r?r+t:t;return e._events[s]?e._events[s].fn?e._events[s]=[e._events[s],u]:e._events[s].push(u):(e._events[s]=u,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function u(){this._events=new n,this._eventsCount=0}Rf&&(n.prototype=Rf(null),(new n).__proto__||(r=!1)),u.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?py(n).call(n,1):n);return Tl?fm(o).call(o,Tl(e)):o},u.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},u.prototype.emit=function(e,t,n,o,i,a){var u=r?r+e:e;if(!this._events[u])return!1;var s,c,l=this._events[u],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,o),!0;case 5:return l.fn.call(l.context,t,n,o,i),!0;case 6:return l.fn.call(l.context,t,n,o,i,a),!0}for(c=1,s=new Array(f-1);c<f;c++)s[c-1]=arguments[c];l.fn.apply(l.context,s)}else{var p,h=l.length;for(c=0;c<h;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),f){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,n);break;case 4:l[c].fn.call(l[c].context,t,n,o);break;default:if(!s)for(p=1,s=new Array(f-1);p<f;p++)s[p-1]=arguments[p];l[c].fn.apply(l[c].context,s)}}return!0},u.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},u.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},u.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==t||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var s=0,c=[],l=u.length;s<l;s++)(u[s].fn!==t||o&&!u[s].once||n&&u[s].context!==n)&&c.push(u[s]);c.length?this._events[i]=1===c.length?c[0]:c:a(this,i)}return this},u.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(nk);var ok=t(nk.exports),ik=Zi.some;Er({target:"Array",proto:!0,forced:!Gl("some")},{some:function(e){return ik(this,e,arguments.length>1?arguments[1]:void 0)}});var ak=Ll("Array").some,uk=ae,sk=ak,ck=Array.prototype,lk=t((function(e){var t=e.some;return e===ck||uk(ck,e)&&t===ck.some?sk:t})),fk=jr,pk=Vn,hk=Y,dk=RangeError,vk=v,gk=Nr,yk=Vn,mk=Y,_k=vk((function(e){var t=pk(hk(this)),r="",n=fk(e);if(n<0||n==1/0)throw dk("Wrong number of repetitions");for(;n>0;(n>>>=1)&&(t+=t))1&n&&(r+=t);return r})),bk=vk("".slice),wk=Math.ceil,xk=function(e){return function(t,r,n){var o,i,a=yk(mk(t)),u=gk(r),s=a.length,c=void 0===n?" ":yk(n);return u<=s||""==c?a:((i=_k(c,wk((o=u-s)/c.length))).length>o&&(i=bk(i,0,o)),e?a+i:i+a)}},Sk={start:xk(!1),end:xk(!0)},kk=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(ue),Tk=Sk.start;Er({target:"String",proto:!0,forced:kk},{padStart:function(e){return Tk(this,e,arguments.length>1?arguments[1]:void 0)}});var Ok,Ek=Ll("String").padStart,Lk=ae,Rk=Ek,Ck=String.prototype,Pk=t((function(e){var t=e.padStart;return"string"==typeof e||e===Ck||Lk(Ck,e)&&t===Ck.padStart?Rk:t})),Ak="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,jk=n,Ik=l,Mk=O,Nk=Ak,Fk=ue,Dk=Su,Uk=bh,Bk=jk.Function,Hk=/MSIE .\./.test(Fk)||Nk&&((Ok=jk.Bun.version.split(".")).length<3||0==Ok[0]&&(Ok[1]<3||3==Ok[1]&&0==Ok[2])),zk=function(e,t){var r=t?2:1;return Hk?function(n,o){var i=Uk(arguments.length,1)>r,a=Mk(n)?n:Bk(n),u=i?Dk(arguments,r):[],s=i?function(){Ik(a,this,u)}:a;return t?e(s,o):e(s)}:e},qk=Er,Gk=n,$k=zk(Gk.setInterval,!0);qk({global:!0,bind:!0,forced:Gk.setInterval!==$k},{setInterval:$k});var Vk=Er,Yk=n,Wk=zk(Yk.setTimeout,!0);Vk({global:!0,bind:!0,forced:Yk.setTimeout!==Wk},{setTimeout:Wk});var Kk=t(ee.setInterval),Xk=t(ee.setTimeout),Jk=Zi.every;Er({target:"Array",proto:!0,forced:!Gl("every")},{every:function(e){return Jk(this,e,arguments.length>1?arguments[1]:void 0)}});var Qk=Ll("Array").every,Zk=ae,eT=Qk,tT=Array.prototype,rT=t((function(e){var t=e.every;return e===tT||Zk(tT,e)&&t===tT.every?eT:t})),nT=n,oT=o,iT=Vn,aT=pS.trim,uT=oS,sT=v("".charAt),cT=nT.parseFloat,lT=nT.Symbol,fT=lT&&lT.iterator,pT=1/cT(uT+"-0")!=-1/0||fT&&!oT((function(){cT(Object(fT))}))?function(e){var t=aT(iT(e)),r=cT(t);return 0===r&&"-"==sT(t,0)?-0:r}:cT;Er({global:!0,forced:parseFloat!=pT},{parseFloat:pT});var hT=t(ee.parseFloat),dT=vs.PROPER,vT=o,gT=oS,yT=pS.trim;Er({target:"String",proto:!0,forced:function(e){return vT((function(){return!!gT[e]()||"​᠎"!=="​᠎"[e]()||dT&&gT[e].name!==e}))}("trim")},{trim:function(){return yT(this)}});var mT=Ll("String").trim,_T=ae,bT=mT,wT=String.prototype,xT=t((function(e){var t=e.trim;return"string"==typeof e||e===wT||_T(wT,e)&&t===wT.trim?bT:t})),ST=ue.match(/firefox\/(\d+)/i),kT=!!ST&&+ST[1],TT=/MSIE|Trident/.test(ue),OT=ue.match(/AppleWebKit\/(\d+)\./),ET=!!OT&&+OT[1],LT=Er,RT=v,CT=Ce,PT=We,AT=Dr,jT=Dx,IT=Vn,MT=o,NT=h_,FT=Gl,DT=kT,UT=TT,BT=de,HT=ET,zT=[],qT=RT(zT.sort),GT=RT(zT.push),$T=MT((function(){zT.sort(void 0)})),VT=MT((function(){zT.sort(null)})),YT=FT("sort"),WT=!MT((function(){if(BT)return BT<70;if(!(DT&&DT>3)){if(UT)return!0;if(HT)return HT<603;var e,t,r,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)zT.push({k:t+n,v:r})}for(zT.sort((function(e,t){return t.v-e.v})),n=0;n<zT.length;n++)t=zT[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}}));LT({target:"Array",proto:!0,forced:$T||!VT||!YT||!WT},{sort:function(e){void 0!==e&&CT(e);var t=PT(this);if(WT)return void 0===e?qT(t):qT(t,e);var r,n,o=[],i=AT(t);for(n=0;n<i;n++)n in t&&GT(o,t[n]);for(NT(o,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:IT(t)>IT(r)?1:-1}}(e)),r=AT(o),n=0;n<r;)t[n]=o[n++];for(;n<i;)jT(t,n++);return t}});var KT=Ll("Array").sort,XT=ae,JT=KT,QT=Array.prototype,ZT=t((function(e){var t=e.sort;return e===QT||XT(QT,e)&&t===QT.sort?JT:t})),eO=Er,tO=Date,rO=v(tO.prototype.getTime);eO({target:"Date",stat:!0},{now:function(){return rO(new tO)}});var nO,oO=t(ee.Date.now),iO="undefined"!=typeof window&&window.location&&Cx(nO=window.location.href).call(nO,"xgplayerdebugger=1")>-1,aO="color: #525252; background-color: #90ee90;",uO="color: #525252; background-color: red;",sO="color: #525252; background-color: yellow; ",cO="%c[xgplayer]",lO={config:{debug:iO?3:0},logInfo:function(e){for(var t,r,n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];this.config.debug>=3&&(r=console).log.apply(r,fm(t=[cO,aO,e]).call(t,o))},logWarn:function(e){for(var t,r,n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];this.config.debug>=1&&(r=console).warn.apply(r,fm(t=[cO,sO,e]).call(t,o))},logError:function(e){var t,r;if(!(this.config.debug<1)){for(var n=this.config.debug>=2?"trace":"error",o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];(r=console)[n].apply(r,fm(t=[cO,uO,e]).call(t,i))}}},fO=function(){function e(t){WS(this,e),this.bufferedList=t}return XS(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}(),pO={};pO.createDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=document.createElement(t);return i.className=o,i.innerHTML=r,ef(e=kl(n)).call(e,(function(e){var r=e,o=n[e];"video"===t||"audio"===t||"live-video"===t?o&&i.setAttribute(r,o):i.setAttribute(r,o)})),i},pO.createDomFromHtml=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{var n=document.createElement("div");n.innerHTML=e;var o=n.children;if(n=null,o.length>0){var i;if(o=o[0],r&&pO.addClass(o,r),t)ef(i=kl(t)).call(i,(function(e){o.setAttribute(e,t[e])}));return o}return null}catch(a){return lO.logError("util.createDomFromHtml",a),null}},pO.hasClass=function(e,t){if(!e||!t)return!1;try{return lk(Array.prototype).call(e.classList,(function(e){return e===t}))}catch(n){var r=e.className&&"object"===YS(e.className)?e.getAttribute("class"):e.className;return r&&!!r.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}},pO.addClass=function(e,t){if(e&&t)try{var r;ef(r=t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g)).call(r,(function(t){t&&e.classList.add(t)}))}catch(n){pO.hasClass(e,t)||(e.className&&"object"===YS(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t)}},pO.removeClass=function(e,t){if(e&&t)try{var r;ef(r=t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g)).call(r,(function(t){t&&e.classList.remove(t)}))}catch(o){var n;if(pO.hasClass(e,t))ef(n=t.split(/\s+/g)).call(n,(function(t){var r=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===YS(e.className)?e.setAttribute("class",e.getAttribute("class").replace(r," ")):e.className=e.className.replace(r," ")}))}},pO.toggleClass=function(e,t){var r;e&&ef(r=t.split(/\s+/g)).call(r,(function(t){pO.hasClass(e,t)?pO.removeClass(e,t):pO.addClass(e,t)}))},pO.classNames=function(){for(var e=arguments,t=[],r=function(r){if("String"===pO.typeOf(e[r]))t.push(e[r]);else if("Object"===pO.typeOf(e[r])){var n;PS(n=kl(e[r])).call(n,(function(n){e[r][n]&&t.push(n)}))}},n=0;n<arguments.length;n++)r(n);return t.join(" ")},pO.findDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,r=arguments.length>1?arguments[1]:void 0;try{e=t.querySelector(r)}catch(n){lO.logError("util.findDom",n),0===Cx(r).call(r,"#")&&(e=t.getElementById(py(r).call(r,1)))}return e},pO.getCss=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,!1)[t]},pO.padStart=function(e,t,r){for(var n=String(r),o=t>>0,i=Math.ceil(o/n.length),a=[],u=String(e);i--;)a.push(n);return a.join("").substring(0,o-u.length)+u},pO.format=function(e){if(window.isNaN(e))return"";e=Math.round(e);var t=Pk(pO).call(pO,Math.floor(e/3600),2,0),r=Pk(pO).call(pO,Math.floor((e-3600*t)/60),2,0),n=Pk(pO).call(pO,Math.floor(e-3600*t-60*r),2,0);return("00"===t?[r,n]:[t,r,n]).join(":")},pO.event=function(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement},pO.typeOf=function(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]},pO.deepCopy=function(e,t){var r;if("Object"===pO.typeOf(t)&&"Object"===pO.typeOf(e))return ef(r=kl(t)).call(r,(function(r){if("Object"!==pO.typeOf(t[r])||t[r]instanceof Node)if("Array"===pO.typeOf(t[r])){var n;e[r]="Array"===pO.typeOf(e[r])?fm(n=e[r]).call(n,t[r]):t[r]}else e[r]=t[r];else void 0===e[r]||void 0===e[r]?e[r]=t[r]:pO.deepCopy(e[r],t[r])})),e},pO.deepMerge=function(e,t){var r;return PS(r=kl(t)).call(r,(function(r){var n;"Array"===pO.typeOf(t[r])&&"Array"===pO.typeOf(e[r])?"Array"===pO.typeOf(e[r])&&(n=e[r]).push.apply(n,QS(t[r])):pO.typeOf(e[r])!==pO.typeOf(t[r])||null===e[r]||"Object"!==pO.typeOf(e[r])||t[r]instanceof window.Node?null!==t[r]&&(e[r]=t[r]):pO.deepMerge(e[r],t[r])})),e},pO.getBgImage=function(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var r=document.createElement("a");return r.href=t.replace(/url\("|"\)/g,""),r.href},pO.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return ef(Array.prototype).call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},pO.setInterval=function(e,t,r,n){e._interval[t]||(e._interval[t]=Kk(Ry(r).call(r,e),n))},pO.clearInterval=function(e,t){clearInterval(e._interval[t]),e._interval[t]=null},pO.setTimeout=function(e,t,r){e._timers||(e._timers=[]);var n=Xk((function(){t(),pO.clearTimeout(e,n)}),r);return e._timers.push(n),n},pO.clearTimeout=function(e,t){var r=e._timers;if("Array"===pO.typeOf(r)){for(var n=0;n<r.length;n++)if(r[n]===t){rS(r).call(r,n,1),clearTimeout(t);break}}else clearTimeout(t)},pO.clearAllTimers=function(e){var t=e._timers;"Array"===pO.typeOf(t)&&(PS(t).call(t,(function(e){clearTimeout(e)})),e._timerIds=[])},pO.createImgBtn=function(e,t,r,n){var o,i,a,u,s,c,l,f,p,h,d,v,g=pO.createDom("xg-".concat(e),"",{},"xgplayer-".concat(e,"-img"));(g.style.backgroundImage='url("'.concat(t,'")'),r&&n)&&(rT(o=["px","rem","em","pt","dp","vw","vh","vm","%"]).call(o,(function(e){var t,o;return!(Cx(r).call(r,e)>-1&&Cx(n).call(n,e)>-1)||(l=hT(xT(t=py(r).call(r,0,Cx(r).call(r,e))).call(t)),f=hT(xT(o=py(n).call(n,0,Cx(n).call(n,e))).call(o)),p=e,!1)})),g.style.width=fm(i="".concat(l)).call(i,p),g.style.height=fm(a="".concat(f)).call(a,p),g.style.backgroundSize=fm(u=fm(s=fm(c="".concat(l)).call(c,p," ")).call(s,f)).call(u,p),g.style.margin="start"===e?fm(h=fm(d=fm(v="-".concat(f/2)).call(v,p," auto auto -")).call(d,l/2)).call(h,p):"auto 5px auto 5px");return g},pO.Hex2RGBA=function(e,t){var r,n=[];if(/^\#[0-9A-F]{3}$/i.test(e)){var o="#";e.replace(/[0-9A-F]/gi,(function(e){o+=e+e})),e=o}return/^#[0-9A-F]{6}$/i.test(e)?(e.replace(/[0-9A-F]{2}/gi,(function(e){n.push(TS(e,16))})),fm(r="rgba(".concat(n.join(","),", ")).call(r,t,")")):"rgba(255, 255, 255, 0.1)"},pO.getFullScreenEl=function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement},pO.checkIsFunction=function(e){return e&&"function"==typeof e},pO.checkIsObject=function(e){return null!==e&&"object"===YS(e)},pO.hide=function(e){e.style.display="none"},pO.show=function(e,t){e.style.display=t||"block"},pO.isUndefined=function(e){if(null==e)return!0},pO.isNotNull=function(e){return!(null==e)},pO.setStyleFromCsstext=function(e,t){if(t)if("String"===pO.typeOf(t)){var r=t.replace(/\s+/g,"").split(";");PS(r).call(r,(function(t){if(t){var r=t.split(":");r.length>1&&(e.style[r[0]]=r[1])}}))}else{var n;PS(n=kl(t)).call(n,(function(r){e.style[r]=t[r]}))}},pO.filterStyleFromText=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["width","height","top","left","bottom","right","position","z-index","padding","margin","transform"],n=e.style.cssText;if(!n)return{};var o=n.replace(/\s+/g,"").split(";"),i={},a={};return PS(o).call(o,(function(e){if(e){var t=e.split(":");t.length>1&&(!function(e,t){for(var r=0,n=t.length;r<n;r++)if(Cx(e).call(e,t[r])>-1)return!0;return!1}(t[0],r)?a[t[0]]=t[1]:i[t[0]]=t[1])}})),e.setAttribute("style",""),PS(t=kl(a)).call(t,(function(t){e.style[t]=a[t]})),i},pO.getStyleFromCsstext=function(e){var t=e.style.cssText;if(!t)return{};var r=t.replace(/\s+/g,"").split(";"),n={};return PS(r).call(r,(function(e){if(e){var t=e.split(":");t.length>1&&(n[t[0]]=t[1])}})),n},pO.preloadImg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){var n=new window.Image;n.onload=function(e){n=null,t&&t(e)},n.onerror=function(e){n=null,r&&r(e)},n.src=e}},pO.stopPropagation=function(e){e&&e.stopPropagation()},pO.scrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},pO.scrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},pO.checkTouchSupport=function(){return"ontouchstart"in window},pO.getBuffered2=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,r=[],n=0;n<e.length;n++)r.push({start:e.start(n)<.5?0:e.start(n),end:e.end(n)});ZT(r).call(r,(function(e,t){var r=e.start-t.start;return r||t.end-e.end}));var o=[];if(t)for(var i=0;i<r.length;i++){var a=o.length;if(a){var u=o[a-1].end;r[i].start-u<t?r[i].end>u&&(o[a-1].end=r[i].end):o.push(r[i])}else o.push(r[i])}else o=r;return new fO(o)},pO.getEventPos=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e.touches&&e.touches.length>0&&(e=e.touches[0]),{x:e.x/t,y:e.y/t,clientX:e.clientX/t,clientY:e.clientY/t,offsetX:e.offsetX/t,offsetY:e.offsetY/t,pageX:e.pageX/t,pageY:e.pageY/t}},pO.requestAnimationFrame=function(e){var t=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(t)return t(e)},pO.getHostFromUrl=function(e){if("String"!==pO.typeOf(e))return"";var t=e.split("/"),r="";return t.length>3&&t[2]&&(r=t[2]),r},pO.cancelAnimationFrame=function(e){var t=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.cancelRequestAnimationFrame;t&&t(e)},pO.isMSE=function(e){return!!(e&&e instanceof HTMLMediaElement)&&(/^blob/.test(e.currentSrc)||/^blob/.test(e.src))},pO.isBlob=function(e){return/^blob/.test(e)},pO.generateSessionId=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=(new Date).getTime();try{e=TS(e)}catch(r){e=0}return t+=e,window.performance&&"function"==typeof window.performance.now&&(t+=TS(window.performance.now())),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var r=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?r:3&r|8).toString(16)}))},pO.createEvent=function(e){var t;return"function"==typeof window.Event?t=new Event(e):(t=document.createEvent("Event")).initEvent(e,!0,!0),t},pO.adjustTimeByDuration=function(e,t,r){return t&&e&&(e>t||r&&e<t)?t:e},pO.createPositionBar=function(e,t){var r=pO.createDom("xg-bar","",{"data-index":-1},e);return t.appendChild(r),r},pO.getTransformStyle=function(){var e,t,r,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{x:0,y:0,scale:1,rotate:0};return fm(e=fm(t=fm(r="scale(".concat(n.scale||1,") translate(")).call(r,n.x||0,"%, ")).call(t,n.y||0,"%) rotate(")).call(e,n.rotate||0,"deg)")},pO.convertDeg=function(e){return Math.abs(e)<=1?360*e:e%360},pO.getIndexByTime=function(e,t){var r=t.length,n=-1;if(r<1)return n;if(e<=t[0].end||r<2)n=0;else if(e>t[r-1].end)n=r-1;else for(var o=1;o<r;o++)if(e>t[o-1].end&&e<=t[o].end){n=o;break}return n},pO.getOffsetCurrentTime=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,n=-1;if((n=r>=0&&r<t.length?r:pO.getIndexByTime(e,t))<0)return-1;var o=t.length,i=t[n],a=i.start,u=i.end,s=i.cTime,c=i.offset;return e<a?s:e>=a&&e<=u?e-c:e>u&&n>=o-1?u:-1},pO.getCurrentTimeByOffset=function(e,t){var r=-1;if(!t||t.length<0)return e;for(var n=0;n<t.length;n++)if(e<=t[n].duration){r=n;break}if(-1!==r){var o=t[r].start;return r-1<0?o+e:o+(e-t[r-1].duration)}return e};var hO="3.0.10-alpha.4",dO={1:5101,2:5102,3:5103,4:5104,5:5105,6:5106},vO=XS((function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{errorType:"",errorCode:0,errorMessage:"",originError:"",ext:{},mediaError:null};WS(this,e);var n=t&&t.i18n?t.i18n.ERROR_TYPES:null;if(t.media){var o,i=r.mediaError?r.mediaError:t.media.error||{},a=t.duration,u=t.currentTime,s=t.ended,c=t.src,l=t.currentSrc,f=t.media,p=f.readyState,h=f.networkState,d=r.errorCode||i.code;dO[d]&&(d=dO[d]);var v={playerVersion:hO,currentTime:u,duration:a,ended:s,readyState:p,networkState:h,src:c||l,errorType:r.errorType,errorCode:d,message:r.errorMessage||i.message,mediaError:i,originError:r.originError?r.originError.stack:"",host:pO.getHostFromUrl(c||l)};return r.ext&&PS(o=kl(r.ext)).call(o,(function(e){v[e]=r.ext[e]})),v}if(arguments.length>1){for(var g={playerVersion:hO,domain:document.domain},y=["errorType","currentTime","duration","networkState","readyState","src","currentSrc","ended","errd","errorCode","mediaError"],m=0;m<arguments.length;m++)g[y[m]]=arguments[m];return g.ex=n?(n[arguments[0]]||{}).msg:"",g}}));function gO(e,t,r){for(var n,o=arguments.length,i=new Array(o>3?o-3:0),a=3;a<o;a++)i[a-3]=arguments[a];var u,s=t.call.apply(t,fm(n=[e]).call(n,i));r&&"function"==typeof r&&(s&&s.then?s.then((function(){for(var t,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];r.call.apply(r,fm(t=[e]).call(t,o))})):r.call.apply(r,fm(u=[e]).call(u,i)))}function yO(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{pre:null,next:null};return this.__hooks||(this.__hooks={}),!this.__hooks[e]&&(this.__hooks[e]=null),Ry(r=function(){var r,o=arguments,i=this;if(n.pre)try{var a,u;(u=n.pre).call.apply(u,fm(a=[this]).call(a,py(Array.prototype).call(arguments)))}catch(y){var s,c;throw y.message=fm(s=fm(c="[pluginName: ".concat(this.pluginName,":")).call(c,e,":pre error] >> ")).call(s,y.message),y}if(this.__hooks&&this.__hooks[e])try{var l,f,p,h=(f=this.__hooks[e]).call.apply(f,fm(l=[this,this]).call(l,py(Array.prototype).call(arguments)));if(h)if(h.then)h.then((function(e){var r;!1!==e&&gO.apply(void 0,fm(r=[i,t,n.next]).call(r,QS(o)))})).catch((function(e){throw e}));else gO.apply(void 0,fm(p=[this,t,n.next]).call(p,py(Array.prototype).call(arguments)));else if(void 0===h){var d;gO.apply(void 0,fm(d=[this,t,n.next]).call(d,py(Array.prototype).call(arguments)))}}catch(y){var v,g;throw y.message=fm(v=fm(g="[pluginName: ".concat(this.pluginName,":")).call(g,e,"] >> ")).call(v,y.message),y}else gO.apply(void 0,fm(r=[this,t,n.next]).call(r,py(Array.prototype).call(arguments)))}).call(r,this)}function mO(e,t){var r=this.__hooks;if(r)return r.hasOwnProperty(e)?(r&&(r[e]=t),!0):(console.warn("has no supported hook which name [".concat(e,"]")),!1)}function _O(e,t){var r=this.__hooks;r&&delete r[e]}function bO(e,t){var r;lO.logError(fm(r="[".concat(e,"] event or callback cant be undefined or null when call ")).call(r,t))}var wO=function(){function e(t){WS(this,e),pO.checkIsFunction(this.beforeCreate)&&this.beforeCreate(t),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.__hooks={},t&&PS(t).call(t,(function(t){e.__hooks[t]=null})),Of(e,"hooks",{get:function(){var t;return e.__hooks&&PS(t=kl(e.__hooks)).call(t,(function(t){if(e.__hooks[t])return t}))}})}(this),this.__args=t,this.__events={},this.__onceEvents={},this.config=t.config||{},this.player=null,this.playerConfig={},this.pluginName="",this.__init(t)}return XS(e,[{key:"beforeCreate",value:function(e){}},{key:"afterCreate",value:function(){}},{key:"beforePlayerInit",value:function(){}},{key:"onPluginsReady",value:function(){}},{key:"afterPlayerInit",value:function(){}},{key:"destroy",value:function(){}},{key:"__init",value:function(e){this.player=e.player,this.playerConfig=e.player&&e.player.config,this.pluginName=e.pluginName?e.pluginName.toLowerCase():this.constructor.pluginName.toLowerCase(),this.logger=e.player&&e.player.logger}},{key:"updateLang",value:function(e){e||(e=this.lang)}},{key:"lang",get:function(){return this.player.lang}},{key:"i18n",get:function(){return this.player.i18n}},{key:"i18nKeys",get:function(){return this.player.i18nKeys}},{key:"domEventType",get:function(){var e=pO.checkTouchSupport()?"touch":"mouse";return!this.playerConfig||"touch"!==this.playerConfig.domEventType&&"mouse"!==this.playerConfig.domEventType||(e=this.playerConfig.domEventType),e}},{key:"on",value:function(e,t){var r=this;e&&t&&this.player?"string"==typeof e?(this.__events[e]=t,this.player.on(e,t)):AS(e)&&ef(e).call(e,(function(e){r.__events[e]=t,r.player.on(e,t)})):bO(this.pluginName,"plugin.on(event, callback)")}},{key:"once",value:function(e,t){var r=this;e&&t&&this.player?"string"==typeof e?(this.__onceEvents[e]=t,this.player.once(e,t)):AS(e)&&ef(e).call(e,(function(n){r.__onceEvents[n]=t,r.player.once(e,t)})):bO(this.pluginName,"plugin.once(event, callback)")}},{key:"off",value:function(e,t){var r=this;e&&t&&this.player?"string"==typeof e?(delete this.__events[e],this.player.off(e,t)):AS(e)&&ef(e).call(e,(function(n){delete r.__events[e],r.player.off(n,t)})):bO(this.pluginName,"plugin.off(event, callback)")}},{key:"offAll",value:function(){var e,t=this;ef(e=["__events","__onceEvents"]).call(e,(function(e){var r;ef(r=kl(t[e])).call(r,(function(r){t[e][r]&&t.off(r,t[e][r]),r&&delete t[e][r]}))})),this.__events={},this.__onceEvents={}}},{key:"emit",value:function(e){var t,r;if(this.player){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];(r=this.player).emit.apply(r,fm(t=[e]).call(t,o))}}},{key:"emitUserAction",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.player){var n=VS(VS({},r),{},{pluginName:this.pluginName});this.player.emitUserAction(e,t,n)}}},{key:"hook",value:function(e,t){var r;return yO.call.apply(yO,fm(r=[this]).call(r,py(Array.prototype).call(arguments)))}},{key:"useHooks",value:function(e,t){for(var r,n=arguments.length,o=new Array(n>2?n-2:0),i=2;i<n;i++)o[i-2]=arguments[i];return mO.call.apply(mO,fm(r=[this]).call(r,py(Array.prototype).call(arguments)))}},{key:"removeHooks",value:function(e,t){for(var r,n=arguments.length,o=new Array(n>2?n-2:0),i=2;i<n;i++)o[i-2]=arguments[i];return _O.call.apply(_O,fm(r=[this]).call(r,py(Array.prototype).call(arguments)))}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(this.player)return r&&(t.pluginName=r),this.player.registerPlugin({plugin:e,options:t})}},{key:"getPlugin",value:function(e){return this.player?this.player.getPlugin(e):null}},{key:"__destroy",value:function(){var e,t=this,r=this.player,n=this.pluginName;this.offAll(),pO.clearAllTimers(this),pO.checkIsFunction(this.destroy)&&this.destroy(),PS(e=["player","playerConfig","pluginName","logger","__args","__hooks"]).call(e,(function(e){t[e]=null})),r.unRegisterPlugin(n),this.__hooks=null}}],[{key:"defineGetterOrSetter",value:function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&Of(e,r,t[r])}},{key:"defaultConfig",get:function(){return{}}},{key:"pluginName",get:function(){return"pluginName"}}]),e}(),xO=ee,SO=l;xO.JSON||(xO.JSON={stringify:JSON.stringify});var kO=function(e,t,r){return SO(xO.JSON.stringify,null,arguments)},TO=t(kO),OO=Z,EO=_,LO=ft("match"),RO=function(e){var t;return OO(e)&&(void 0!==(t=e[LO])?!!t:"RegExp"==EO(e))},CO=TypeError,PO=ft("match"),AO=ro.includes;Er({target:"Array",proto:!0,forced:o((function(){return!Array(1).includes()}))},{includes:function(e){return AO(this,e,arguments.length>1?arguments[1]:void 0)}});var jO=Ll("Array").includes,IO=Er,MO=function(e){if(RO(e))throw CO("The method doesn't accept regular expressions");return e},NO=Y,FO=Vn,DO=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[PO]=!1,"/./"[e](t)}catch(n){}}return!1},UO=v("".indexOf);IO({target:"String",proto:!0,forced:!DO("includes")},{includes:function(e){return!!~UO(FO(NO(this)),FO(MO(e)),arguments.length>1?arguments[1]:void 0)}});var BO=Ll("String").includes,HO=ae,zO=jO,qO=BO,GO=Array.prototype,$O=String.prototype,VO=t((function(e){var t=e.includes;return e===GO||HO(GO,e)&&t===GO.includes?zO:"string"==typeof e||e===$O||HO($O,e)&&t===$O.includes?qO:t})),YO={exports:{}},WO=o((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),KO=o,XO=Z,JO=_,QO=WO,ZO=Object.isExtensible,eE=KO((function(){ZO(1)}))||QO?function(e){return!!XO(e)&&((!QO||"ArrayBuffer"!=JO(e))&&(!ZO||ZO(e)))}:ZO,tE=!o((function(){return Object.isExtensible(Object.preventExtensions({}))})),rE=Er,nE=v,oE=no,iE=Z,aE=Je,uE=Xt.f,sE=Bo,cE=qo,lE=eE,fE=tE,pE=!1,hE=rt("meta"),dE=0,vE=function(e){uE(e,hE,{value:{objectID:"O"+dE++,weakData:{}}})},gE=YO.exports={enable:function(){gE.enable=function(){},pE=!0;var e=sE.f,t=nE([].splice),r={};r[hE]=1,e(r).length&&(sE.f=function(r){for(var n=e(r),o=0,i=n.length;o<i;o++)if(n[o]===hE){t(n,o,1);break}return n},rE({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:cE.f}))},fastKey:function(e,t){if(!iE(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!aE(e,hE)){if(!lE(e))return"F";if(!t)return"E";vE(e)}return e[hE].objectID},getWeakData:function(e,t){if(!aE(e,hE)){if(!lE(e))return!0;if(!t)return!1;vE(e)}return e[hE].weakData},onFreeze:function(e){return fE&&pE&&lE(e)&&!aE(e,hE)&&vE(e),e}};oE[hE]=!0;var yE=YO.exports,mE=Er,_E=n,bE=yE,wE=o,xE=vr,SE=Ap,kE=ch,TE=O,OE=Z,EE=Ri,LE=Xt.f,RE=Zi.forEach,CE=L,PE=$i.set,AE=$i.getterFor,jE=Uo,IE=ii,ME=u_,NE=Kt,FE=ch,DE=G,UE=Ap,BE=hc,HE=dc,zE=ah,qE=L,GE=yE.fastKey,$E=$i.set,VE=$i.getterFor,YE={getConstructor:function(e,t,r,n){var o=e((function(e,o){FE(e,i),$E(e,{type:t,index:jE(null),first:void 0,last:void 0,size:0}),qE||(e.size=0),DE(o)||UE(o,e[n],{that:e,AS_ENTRIES:r})})),i=o.prototype,a=VE(t),u=function(e,t,r){var n,o,i=a(e),u=s(e,t);return u?u.value=r:(i.last=u={index:o=GE(t,!0),key:t,value:r,previous:n=i.last,next:void 0,removed:!1},i.first||(i.first=u),n&&(n.next=u),qE?i.size++:e.size++,"F"!==o&&(i.index[o]=u)),e},s=function(e,t){var r,n=a(e),o=GE(t);if("F"!==o)return n.index[o];for(r=n.first;r;r=r.next)if(r.key==t)return r};return ME(i,{clear:function(){for(var e=a(this),t=e.index,r=e.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete t[r.index],r=r.next;e.first=e.last=void 0,qE?e.size=0:this.size=0},delete:function(e){var t=this,r=a(t),n=s(t,e);if(n){var o=n.next,i=n.previous;delete r.index[n.index],n.removed=!0,i&&(i.next=o),o&&(o.previous=i),r.first==n&&(r.first=o),r.last==n&&(r.last=i),qE?r.size--:t.size--}return!!n},forEach:function(e){for(var t,r=a(this),n=NE(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!s(this,e)}}),ME(i,r?{get:function(e){var t=s(this,e);return t&&t.value},set:function(e,t){return u(this,0===e?0:e,t)}}:{add:function(e){return u(this,e=0===e?0:e,e)}}),qE&&IE(i,"size",{configurable:!0,get:function(){return a(this).size}}),o},setStrong:function(e,t,r){var n=t+" Iterator",o=VE(t),i=VE(n);BE(e,t,(function(e,t){$E(this,{type:n,target:e,state:o(e),kind:t,last:void 0})}),(function(){for(var e=i(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?HE("keys"==t?r.key:"values"==t?r.value:[r.key,r.value],!1):(e.target=void 0,HE(void 0,!0))}),r?"entries":"values",!r,!0),zE(t)}};(function(e,t,r){var n,o=-1!==e.indexOf("Map"),i=-1!==e.indexOf("Weak"),a=o?"set":"add",u=_E[e],s=u&&u.prototype,c={};if(CE&&TE(u)&&(i||s.forEach&&!wE((function(){(new u).entries().next()})))){var l=(n=t((function(t,r){PE(kE(t,l),{type:e,collection:new u}),null!=r&&SE(r,t[a],{that:t,AS_ENTRIES:o})}))).prototype,f=AE(e);RE(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"==e||"set"==e;!(e in s)||i&&"clear"==e||xE(l,e,(function(r,n){var o=f(this).collection;if(!t&&i&&!OE(r))return"get"==e&&void 0;var a=o[e](0===r?0:r,n);return t?this:a}))})),i||LE(l,"size",{configurable:!0,get:function(){return f(this).collection.size}})}else n=r.getConstructor(t,e,o,a),bE.enable();EE(n,e,!1,!0),c[e]=n,mE({global:!0,forced:!0},c),i||r.setStrong(n,e,o)})("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),YE);var WE=ee.Map,KE=Kt,XE=P,JE=Ce,QE=hh,ZE=G,eL=Ap,tL=[].push,rL=function(e){var t,r,n,o,i=arguments.length,a=i>1?arguments[1]:void 0;return QE(this),(t=void 0!==a)&&JE(a),ZE(e)?new this:(r=[],t?(n=0,o=KE(a,i>2?arguments[2]:void 0),eL(e,(function(e){XE(tL,r,o(e,n++))}))):eL(e,tL,{that:r}),new this(r))};Er({target:"Map",stat:!0,forced:!0},{from:rL});var nL=Su,oL=function(){return new this(nL(arguments))};Er({target:"Map",stat:!0,forced:!0},{of:oL});var iL=Oe,aL=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"get"in e&&"set"in e&&"delete"in e&&"entries"in e)return e;throw TypeError(iL(e)+" is not a map")},uL=function(e,t){return 1==t?function(t,r){return t[e](r)}:function(t,r,n){return t[e](r,n)}},sL=ie("Map"),cL={Map:sL,set:uL("set",2),get:uL("get",1),has:uL("has",1),remove:uL("delete",1),proto:sL.prototype},lL=aL,fL=cL.remove;Er({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=lL(this),r=!0,n=0,o=arguments.length;n<o;n++)e=fL(t,arguments[n]),r=r&&e;return!!r}});var pL=aL,hL=cL.get,dL=cL.has,vL=cL.set;Er({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var r,n,o=pL(this);return dL(o,e)?(r=hL(o,e),"update"in t&&(r=t.update(r,e,o),vL(o,e,r)),r):(n=t.insert(e,o),vL(o,e,n),n)}});var gL=P,yL=function(e,t,r){for(var n,o,i=r||e.next;!(n=gL(i,e)).done;)if(void 0!==(o=t(n.value)))return o},mL=function(e,t,r){return r?yL(e.entries(),(function(e){return t(e[1],e[0])})):e.forEach(t)},_L=Kt,bL=aL,wL=mL;Er({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=bL(this),r=_L(e,arguments.length>1?arguments[1]:void 0);return!1!==wL(t,(function(e,n){if(!r(e,n,t))return!1}),!0)}});var xL=Kt,SL=aL,kL=mL,TL=cL.Map,OL=cL.set;Er({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=SL(this),r=xL(e,arguments.length>1?arguments[1]:void 0),n=new TL;return kL(t,(function(e,o){r(e,o,t)&&OL(n,o,e)})),n}});var EL=Kt,LL=aL,RL=mL;Er({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=LL(this),r=EL(e,arguments.length>1?arguments[1]:void 0),n=RL(t,(function(e,n){if(r(e,n,t))return{value:e}}),!0);return n&&n.value}});var CL=Kt,PL=aL,AL=mL;Er({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=PL(this),r=CL(e,arguments.length>1?arguments[1]:void 0),n=AL(t,(function(e,n){if(r(e,n,t))return{key:n}}),!0);return n&&n.key}});var jL=Er,IL=P,ML=O,NL=Ce,FL=Ap,DL=cL.Map,UL=v([].push);jL({target:"Map",stat:!0,forced:!0},{groupBy:function(e,t){var r=new(ML(this)?this:DL);NL(t);var n=NL(r.has),o=NL(r.get),i=NL(r.set);return FL(e,(function(e){var a=t(e);IL(n,r,a)?UL(IL(o,r,a),e):IL(i,r,a,[e])})),r}});var BL=function(e,t){return e===t||e!=e&&t!=t},HL=aL,zL=mL;Er({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return!0===zL(HL(this),(function(t){if(BL(t,e))return!0}),!0)}});var qL=P,GL=Ap,$L=O,VL=Ce,YL=cL.Map;Er({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var r=new($L(this)?this:YL);VL(t);var n=VL(r.set);return GL(e,(function(e){qL(n,r,t(e),e)})),r}});var WL=aL,KL=mL;Er({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){var t=KL(WL(this),(function(t,r){if(t===e)return{key:r}}),!0);return t&&t.key}});var XL=Kt,JL=aL,QL=mL,ZL=cL.Map,eR=cL.set;Er({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=JL(this),r=XL(e,arguments.length>1?arguments[1]:void 0),n=new ZL;return QL(t,(function(e,o){eR(n,r(e,o,t),e)})),n}});var tR=Kt,rR=aL,nR=mL,oR=cL.Map,iR=cL.set;Er({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=rR(this),r=tR(e,arguments.length>1?arguments[1]:void 0),n=new oR;return nR(t,(function(e,o){iR(n,o,r(e,o,t))})),n}});var aR=aL,uR=Ap,sR=cL.set;Er({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=aR(this),r=arguments.length,n=0;n<r;)uR(arguments[n++],(function(e,r){sR(t,e,r)}),{AS_ENTRIES:!0});return t}});var cR=Ce,lR=aL,fR=mL,pR=TypeError;Er({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=lR(this),r=arguments.length<2,n=r?void 0:arguments[1];if(cR(e),fR(t,(function(o,i){r?(r=!1,n=o):n=e(n,o,i,t)})),r)throw pR("Reduce of empty map with no initial value");return n}});var hR=Kt,dR=aL,vR=mL;Er({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=dR(this),r=hR(e,arguments.length>1?arguments[1]:void 0);return!0===vR(t,(function(e,n){if(r(e,n,t))return!0}),!0)}});var gR=Ce,yR=aL,mR=TypeError,_R=cL.get,bR=cL.has,wR=cL.set;Er({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var r=yR(this),n=arguments.length;gR(t);var o=bR(r,e);if(!o&&n<3)throw mR("Updating absent value");var i=o?_R(r,e):gR(n>2?arguments[2]:void 0)(e,r);return wR(r,e,t(i,e,r)),r}});var xR=P,SR=Ce,kR=O,TR=tr,OR=TypeError,ER=function(e,t){var r,n=TR(this),o=SR(n.get),i=SR(n.has),a=SR(n.set),u=arguments.length>2?arguments[2]:void 0;if(!kR(t)&&!kR(u))throw OR("At least one callback required");return xR(i,n,e)?(r=xR(o,n,e),kR(t)&&(r=t(r),xR(a,n,e,r))):kR(u)&&(r=u(),xR(a,n,e,r)),r};Er({target:"Map",proto:!0,real:!0,forced:!0},{upsert:ER}),Er({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:ER});var LR=t(WE);function RR(e,t){var r=kl(e);if(Tl){var n=Tl(e);t&&(n=jl(n).call(n,(function(t){return zl(e,t).enumerable}))),r.push.apply(r,n)}return r}function CR(e){for(var t=1;t<arguments.length;t++){var r,n,o=null!=arguments[t]?arguments[t]:{};t%2?ef(r=RR(Object(o),!0)).call(r,(function(t){DR(e,t,o[t])})):pf?_f(e,pf(o)):ef(n=RR(Object(o))).call(n,(function(t){Of(e,t,zl(o,t))}))}return e}function PR(){PR=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Of||function(e,t,r){e[t]=r.value},o="function"==typeof Ef?Ef:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function s(e,t,r){return Of(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(E){s=function(e,t,r){return e[t]=r}}function c(e,t,r,o){var i=t&&t.prototype instanceof p?t:p,a=Rf(i.prototype),u=new k(o||[]);return n(a,"_invoke",{value:b(e,r,u)}),a}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(E){return{type:"throw",arg:E}}}e.wrap=c;var f={};function p(){}function h(){}function d(){}var v={};s(v,i,(function(){return this}));var g=jf&&jf(jf(T([])));g&&g!==t&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Rf(v);function m(e){var t;ef(t=["next","throw","return"]).call(t,(function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function o(n,i,a,u){var s=l(e[n],e,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==wl(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){o("next",e,a,u)}),(function(e){o("throw",e,a,u)})):t.resolve(f).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,u)}))}u(s.arg)}var i;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){o(e,r,t,n)}))}return i=i?i.then(n,n):n()}})}function b(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(e,t,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function w(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var o=l(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,f;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],ef(e).call(e,x,this),this.reset(!0)}function T(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return If?If(e,d):(e.__proto__=d,s(e,u,"GeneratorFunction")),e.prototype=Rf(y),e},e.awrap=function(e){return{__await:e}},m(_.prototype),s(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,r,n,o,i){void 0===i&&(i=Dg);var a=new _(c(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(y),s(y,u,"Generator"),s(y,i,(function(){return this})),s(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return Yg(r).call(r),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=T,k.prototype={constructor:k,reset:function(e){var t;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,ef(t=this.tryEntries).call(t,S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+py(n).call(n,1))&&(this[n]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:T(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},e}function AR(e){return(AR="function"==typeof Ef&&"symbol"==typeof jS?function(e){return typeof e}:function(e){return e&&"function"==typeof Ef&&e.constructor===Ef&&e!==Ef.prototype?"symbol":typeof e})(e)}function jR(e,t,r,n,o,i,a){try{var u=e[i](a),s=u.value}catch(EP){return void r(EP)}u.done?t(s):Dg.resolve(s).then(n,o)}function IR(e){return function(){var t=this,r=arguments;return new Dg((function(n,o){var i=e.apply(t,r);function a(e){jR(i,n,o,a,u,"next",e)}function u(e){jR(i,n,o,a,u,"throw",e)}a(void 0)}))}}function MR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function NR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Of(e,JR(n.key),n)}}function FR(e,t,r){return t&&NR(e.prototype,t),r&&NR(e,r),Of(e,"prototype",{writable:!1}),e}function DR(e,t,r){return(t=JR(t))in e?Of(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function UR(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Rf(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Of(e,"prototype",{writable:!1}),t&&HR(e,t)}function BR(e){var t;return(BR=If?Ry(t=jf).call(t):function(e){return e.__proto__||jf(e)})(e)}function HR(e,t){var r;return(HR=If?Ry(r=If).call(r):function(e,t){return e.__proto__=t,e})(e,t)}function zR(){if("undefined"==typeof Reflect||!Gy)return!1;if(Gy.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Gy(Boolean,[],(function(){}))),!0}catch(e){return!1}}function qR(e,t,r){var n;zR()?qR=Ry(n=Gy).call(n):qR=function(e,t,r){var n=[null];n.push.apply(n,t);var o=new(Ry(Function).apply(e,n));return r&&HR(o,r.prototype),o};return qR.apply(null,arguments)}function GR(e){var t="function"==typeof LR?new LR:void 0;return GR=function(e){if(null===e||(r=e,-1===Cx(n=Function.toString.call(r)).call(n,"[native code]")))return e;var r,n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,o)}function o(){return qR(e,arguments,BR(this).constructor)}return o.prototype=Rf(e.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),HR(o,e)},GR(e)}function $R(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=kl(e);for(n=0;n<i.length;n++)r=i[n],Cx(t).call(t,r)>=0||(o[r]=e[r]);return o}(e,t);if(Tl){var i=Tl(e);for(n=0;n<i.length;n++)r=i[n],Cx(t).call(t,r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function VR(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function YR(e){var t=zR();return function(){var r,n=BR(e);if(t){var o=BR(this).constructor;r=Gy(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===wl(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return VR(e)}(this,r)}}function WR(){var e;"undefined"!=typeof Reflect&&HS?WR=Ry(e=HS).call(e):WR=function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=BR(e)););return e}(e,t);if(n){var o=zl(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}};return WR.apply(this,arguments)}function KR(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function XR(e,t){var r=void 0!==Ef&&zS(e)||e["@@iterator"];if(!r){if(AS(e)||(r=function(e,t){var r;if(e){if("string"==typeof e)return KR(e,t);var n=py(r=Object.prototype.toString.call(e)).call(r,8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?GS(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?KR(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function JR(e){var t=function(e,t){if("object"!==wl(e)||null===e)return e;var r=e[$y];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==wl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===wl(t)?t:String(t)}var QR=Ce,ZR=We,eC=q,tC=Dr,rC=TypeError,nC=function(e){return function(t,r,n,o){QR(r);var i=ZR(t),a=eC(i),u=tC(i),s=e?u-1:0,c=e?-1:1;if(n<2)for(;;){if(s in a){o=a[s],s+=c;break}if(s+=c,e?s<0:u<=s)throw rC("Reduce of empty array with no initial value")}for(;e?s>=0:u>s;s+=c)s in a&&(o=r(o,a[s],s,i));return o}},oC={left:nC(!1),right:nC(!0)}.left;Er({target:"Array",proto:!0,forced:!th&&de>79&&de<83||!Gl("reduce")},{reduce:function(e){var t=arguments.length;return oC(this,e,t,t>1?arguments[1]:void 0)}});var iC,aC=Ll("Array").reduce,uC=ae,sC=aC,cC=Array.prototype,lC=t((function(e){var t=e.reduce;return e===cC||uC(cC,e)&&t===cC.reduce?sC:t})),fC="network",pC="network_timeout",hC="network_forbidden",dC="network_notfound",vC="network_range_not_satisfiable",gC="demux",yC="remux",mC="media",_C="drm",bC="other",wC="runtime",xC=(DR(iC={},"manifest",{HLS:1100,DASH:1200}),DR(iC,fC,2100),DR(iC,pC,2101),DR(iC,hC,2103),DR(iC,dC,2104),DR(iC,vC,2116),DR(iC,gC,{FLV:3100,HLS:3200,MP4:3300,FMP4:3400,SIDX:3410}),DR(iC,yC,{FMP4:4100,MP4:4200}),DR(iC,mC,{MEDIA_ERR_ABORTED:5101,MEDIA_ERR_NETWORK:5102,MEDIA_ERR_DECODE:5103,MEDIA_ERR_SRC_NOT_SUPPORTED:5104,MEDIA_ERR_CODEC_NOT_SUPPORTED:5105,MEDIA_ERR_URL_EMPTY:5106,MSE_ADD_SB:5200,MSE_APPEND_BUFFER:5201,MSE_OTHER:5202,MSE_FULL:5203,MSE_HIJACK:5204,EME_HIJACK:5301}),DR(iC,_C,{LICENSE:7100,CUSTOM_LICENSE:7200}),DR(iC,bC,8e3),DR(iC,wC,{NO_CANPLAY_ERROR:9001,BUFFERBREAK_ERROR:9002,WAITING_TIMEOUT_ERROR:9003}),iC),SC=function(e){UR(r,e);var t=YR(r);function r(e,n,o,i,a){var u;return MR(this,r),(u=t.call(this,a||(null==o?void 0:o.message))).errorType=e===pC?fC:e,u.originError=o,u.ext=i,u.errorCode=xC[e][n]||xC[e],u.errorMessage=u.message,u.errorCode||(u.errorType=bC,u.errorCode=xC[u.errorType]),u}return FR(r,null,[{key:"create",value:function(e,t,n,o,i){return e instanceof r?e:(e instanceof Error&&(n=e,e=""),e||(e=bC),new r(e,t,n,o,i))}},{key:"network",value:function(e){var t;return new r(null!=e&&e.isTimeout?pC:fC,null,e instanceof Error?e:null,{url:null==e?void 0:e.url,response:null==e?void 0:e.response,httpCode:null==e||null===(t=e.response)||void 0===t?void 0:t.status})}}]),r}(GR(Error)),kC=1,TC=2,OC=3,EC=4,LC=["Boolean","Number","String","Undefined","Null","Date","Object"],RC=function(){function e(t,r){MR(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),e.disabled=(null==r?void 0:r.disabled)||!0,this.logCacheLevel=(null==r?void 0:r.logCacheLevel)||3,this.logMaxSize=(null==r?void 0:r.logMaxSize)||204800,this.logSize=0,this.logTextArray=[]}return FR(e,[{key:"debug",value:function(){for(var t,r,n,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];this.logCache.apply(this,fm(t=[kC]).call(t,i)),e.disabled||(n=console).debug.apply(n,fm(r=[this._prefix,CC()]).call(r,i))}},{key:"log",value:function(){for(var t,r,n,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];this.logCache.apply(this,fm(t=[TC]).call(t,i)),e.disabled||(n=console).log.apply(n,fm(r=[this._prefix,CC()]).call(r,i))}},{key:"warn",value:function(){for(var t,r,n,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];this.logCache.apply(this,fm(t=[OC]).call(t,i)),e.disabled||(n=console).warn.apply(n,fm(r=[this._prefix,CC()]).call(r,i))}},{key:"error",value:function(){for(var t,r,n,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];this.logCache.apply(this,fm(t=[EC]).call(t,i)),e.disabled||(n=console).error.apply(n,fm(r=[this._prefix,CC()]).call(r,i))}},{key:"logCache",value:function(e){if(!(e<this.logCacheLevel)){var t="";try{for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];var i=PS(n).call(n,(function(e){return AC(e)}));t=this._prefix+CC()+TO(i)}catch(u){return}if(e>=this.logCacheLevel&&(this.logSize+=t.length,this.logTextArray.push(t)),this.logSize>this.logMaxSize){var a=this.logTextArray.shift();this.logSize-=a.length}}}},{key:"getLogCache",value:function(){var e=this.logTextArray.join("\n");return this.reset(),e}},{key:"reset",value:function(){this.logTextArray=[],this.logSize=0}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}},{key:"setLogLevel",value:function(e){this.logCacheLevel=e}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();function CC(){return(new Date).toLocaleString()}function PC(e){var t;if("object"!==AR(e))return e;var r=py(t=Object.prototype.toString.call(e)).call(t,8,-1);switch(r){case"Array":case"Uint8Array":case"ArrayBuffer":return r+"["+e.length+"]";case"Object":return"{}";default:return r}}function AC(e,t,r){var n;r||(r=1),t||(t=2);var o={};if(!e||"object"!==AR(e))return e;var i=py(n=Object.prototype.toString.call(e)).call(n,8,-1);if(!VO(LC).call(LC,i))return i;if(!(r>t)){for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(r===t?o[a]=PC(e[a]):"object"===AR(e[a])?o[a]=AC(e[a],t,r+1):o[a]=e[a]);return o}}DR(RC,"disabled",!0);var jC="fetch",IC="xhr",MC="arraybuffer",NC="text",FC="json",DC=function(e){UR(r,e);var t=YR(r);function r(e,n,o,i){var a;return MR(this,r),DR(VR(a=t.call(this,i)),"retryCount",0),DR(VR(a),"isTimeout",!1),DR(VR(a),"loaderType",jC),DR(VR(a),"startTime",0),DR(VR(a),"endTime",0),DR(VR(a),"options",{}),a.url=e,a.request=n,a.response=o,a}return FR(r)}(GR(Error)),UC=Object.prototype.toString;function BC(e){if("[object Object]"!==UC.call(e))return!1;var t=jf(e);return null===t||t===Object.prototype}function HC(e){if(e&&null!==e[0]&&void 0!==e[0]&&(0!==e[0]||null!==e[1]&&void 0!==e[1])){var t="bytes="+e[0]+"-";return e[1]&&(t+=e[1]),t}}function zC(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function qC(e,t){var r,n;if(e){if(!t)return e;var o,i=jl(r=PS(n=kl(t)).call(n,(function(e){if(null!=(o=t[e]))return AS(o)?e+="[]":o=[o],PS(o).call(o,(function(t){var r,n;return n=t,"[object Date]"===UC.call(n)?t=t.toISOString():function(e){return null!==e&&"object"===AR(e)}(t)&&(t=TO(t)),fm(r="".concat(zC(e),"=")).call(r,zC(t))})).join("&")}))).call(r,Boolean).join("&");if(i){var a=Cx(e).call(e,"#");-1!==a&&(e=py(e).call(e,0,a)),e+=(-1===Cx(e).call(e,"?")?"?":"&")+i}return e}}function GC(e,t,r,n,o,i,a,u,s,c,l){return o=null!=o?hT(o):null,n=TS(n||"0",10),nS(n)&&(n=0),{data:e,done:t,options:{range:s,vid:c,index:u,contentLength:n,age:o,startTime:i,firstByteTime:a,endTime:oO(),priOptions:l},response:r}}function $C(e,t){return Math.round(8*e*1e3/t/1024)}var VC="error",YC="core.ttfb",WC="core.loadstart",KC="core.loadresponseheaders",XC="core.loadcomplete",JC="core.loadretry",QC="core.metadataparsed",ZC="real_time_speed",eP=2097152,tP=function(e){UR(n,e);var t,r=YR(n);function n(){var e;return MR(this,n),DR(VR(e=r.call(this)),"_abortController",null),DR(VR(e),"_timeoutTimer",null),DR(VR(e),"_reader",null),DR(VR(e),"_response",null),DR(VR(e),"_aborted",!1),DR(VR(e),"_index",-1),DR(VR(e),"_range",null),DR(VR(e),"_receivedLength",0),DR(VR(e),"_running",!1),DR(VR(e),"_logger",null),DR(VR(e),"_vid",""),DR(VR(e),"_onProcessMinLen",0),DR(VR(e),"_onCancel",null),DR(VR(e),"_priOptions",null),e}return FR(n,[{key:"load",value:function(e){var t,r=this,n=e.url,o=e.vid,i=e.timeout,a=e.responseType,u=e.onProgress,s=e.index,c=e.onTimeout,l=e.onCancel,f=e.range,p=e.transformResponse,h=e.request,d=e.params,v=e.logger,g=e.method,y=e.headers,m=e.body,_=e.mode,b=e.credentials,w=e.cache,x=e.redirect,S=e.referrer,k=e.referrerPolicy,T=e.onProcessMinLen,O=e.priOptions;this._logger=v,this._aborted=!1,this._onProcessMinLen=T,this._onCancel=l,this._abortController="undefined"!=typeof AbortController&&new AbortController,this._running=!0,this._index=s,this._range=f||[0,0],this._vid=o||n,this._priOptions=O||{};var E={method:g,headers:y,body:m,mode:_,credentials:b,cache:w,redirect:x,referrer:S,referrerPolicy:k,signal:null===(t=this._abortController)||void 0===t?void 0:t.signal},L=!1;clearTimeout(this._timeoutTimer),n=qC(n,d);var R=HC(f);R&&(y=h?h.headers:E.headers=E.headers||(Headers?new Headers:{}),Headers&&y instanceof Headers?y.append("Range",R):y.Range=R),i&&(this._timeoutTimer=Xk((function(){if(L=!0,r.cancel(),c){var e=new DC(n,E,null,"timeout");e.isTimeout=!0,c(e,{index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions})}}),i));var C=oO();return this._logger.debug("[fetch load start], index,",s,",range,",f),new Dg((function(e,t){fetch(h||n,h?void 0:E).then(function(){var o=IR(PR().mark((function o(i){var c,l,h,d;return PR().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:if(clearTimeout(r._timeoutTimer),r._response=i,!r._aborted&&r._running){o.next=4;break}return o.abrupt("return");case 4:if(p&&(i=p(i,n)||i),i.ok){o.next=7;break}throw new DC(n,E,i,"bad network response");case 7:if(c=oO(),a!==NC){o.next=15;break}return o.next=11,i.text();case 11:l=o.sent,r._running=!1,o.next=37;break;case 15:if(a!==FC){o.next=22;break}return o.next=18,i.json();case 18:l=o.sent,r._running=!1,o.next=37;break;case 22:if(!u){o.next=29;break}return r.resolve=e,r.reject=t,r._loadChunk(i,u,C,c),o.abrupt("return");case 29:return o.next=31,i.arrayBuffer();case 31:l=o.sent,l=new Uint8Array(l),r._running=!1,h=oO()-C,d=$C(l.byteLength,h),r.emit(ZC,{speed:d,len:l.byteLength,time:h,vid:r._vid,index:r._index,range:r._range,priOptions:r._priOptions});case 37:r._logger.debug("[fetch load end], index,",s,",range,",f),e(GC(l,!0,i,i.headers.get("Content-Length"),i.headers.get("age"),C,c,s,f,r._vid,r._priOptions));case 39:case"end":return o.stop()}}),o)})));return function(e){return o.apply(this,arguments)}}()).catch((function(e){var o;clearTimeout(r._timeoutTimer),r._running=!1,r._aborted&&!L||((e=e instanceof DC?e:new DC(n,E,null,null===(o=e)||void 0===o?void 0:o.message)).startTime=C,e.endTime=oO(),e.isTimeout=L,e.options={index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions},t(e))}))}))}},{key:"cancel",value:(t=IR(PR().mark((function e(){return PR().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._aborted){e.next=2;break}return e.abrupt("return");case 2:if(this._aborted=!0,this._running=!1,!this._response){e.next=14;break}if(e.prev=5,!this._reader){e.next=9;break}return e.next=9,this._reader.cancel();case 9:e.next=13;break;case 11:e.prev=11,e.t0=e.catch(5);case 13:this._response=this._reader=null;case 14:if(this._abortController){try{this._abortController.abort()}catch(EP){}this._abortController=null}this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions});case 16:case"end":return e.stop()}}),e,this,[[5,11]])}))),function(){return t.apply(this,arguments)})},{key:"_loadChunk",value:function(e,t,r,n){var o=this;if(!e.body||!e.body.getReader){this._running=!1;var i=new DC(e.url,"",e,"onProgress of bad response.body.getReader");return i.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},void this.reject(i)}this._onProcessMinLen>0&&(this._cache=new Uint8Array(eP),this._writeIdx=0);var a,u,s,c=this._reader=e.body.getReader(),l=function(){var i=IR(PR().mark((function i(){var f,p,h,d,v,g,y,m;return PR().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return u=oO(),i.prev=1,i.next=4,c.read();case 4:a=i.sent,s=oO(),i.next=13;break;case 8:return i.prev=8,i.t0=i.catch(1),s=oO(),o._aborted||(o._running=!1,i.t0.options={index:o._index,range:o._range,vid:o._vid,priOptions:o._priOptions},o.reject(i.t0)),i.abrupt("return");case 13:if(p=(null===(f=o._range)||void 0===f?void 0:f.length)>0?o._range[0]:0,h=p+o._receivedLength,!o._aborted){i.next=19;break}return o._running=!1,t(void 0,!1,{range:[h,h],vid:o._vid,index:o._index,startTime:u,endTime:s,st:r,firstByteTime:n,priOptions:o._priOptions},e),i.abrupt("return");case 19:var _;if(d=a.value?a.value.byteLength:0,o._receivedLength+=d,o._logger.debug("【fetchLoader,onProgress call】,task,",o._range,", start,",h,", end,",p+o._receivedLength,", done,",a.done),o._onProcessMinLen>0){if(o._writeIdx+d>=o._onProcessMinLen||a.done)(v=new Uint8Array(o._writeIdx+d)).set(py(_=o._cache).call(_,0,o._writeIdx),0),d>0&&v.set(a.value,o._writeIdx),o._writeIdx=0,o._logger.debug("【fetchLoader,onProgress enough】,done,",a.done,",len,",v.byteLength,", writeIdx,",o._writeIdx);else if(d>0&&o._writeIdx+d<eP)o._cache.set(a.value,o._writeIdx),o._writeIdx+=d,o._logger.debug("【fetchLoader,onProgress cache】,len,",d,", writeIdx,",o._writeIdx);else if(d>0){var b;g=new Uint8Array(o._writeIdx+d+2048),o._logger.debug("【fetchLoader,onProgress extra start】,size,",o._writeIdx+d+2048,", datalen,",d,", writeIdx,",o._writeIdx),g.set(py(b=o._cache).call(b,0,o._writeIdx),0),d>0&&g.set(a.value,o._writeIdx),o._writeIdx+=d,delete o._cache,o._cache=g,o._logger.debug("【fetchLoader,onProgress extra end】,len,",d,", writeIdx,",o._writeIdx)}}else v=a.value;(v&&v.byteLength>0||a.done)&&t(v,a.done,{range:[o._range[0]+o._receivedLength-(v?v.byteLength:0),o._range[0]+o._receivedLength],vid:o._vid,index:o._index,startTime:u,endTime:s,st:r,firstByteTime:n,priOptions:o._priOptions},e),a.done?(y=oO()-r,m=$C(o._receivedLength,y),o.emit(ZC,{speed:m,len:o._receivedLength,time:y,vid:o._vid,index:o._index,range:o._range,priOptions:o._priOptions}),o._running=!1,o._logger.debug("[fetchLoader onProgress end],task,",o._range,",done,",a.done),o.resolve(GC(a,!0,e,e.headers.get("Content-Length"),e.headers.get("age"),r,n,o._index,o._range,o._vid,o._priOptions))):l();case 25:case"end":return i.stop()}}),i,null,[[1,8]])})));return function(){return i.apply(this,arguments)}}();l()}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}}],[{key:"isSupported",value:function(){return!("undefined"==typeof fetch)}}]),n}(ok);var rP=function(e){UR(r,e);var t=YR(r);function r(){var e;return MR(this,r),DR(VR(e=t.call(this)),"_xhr",null),DR(VR(e),"_aborted",!1),DR(VR(e),"_timeoutTimer",null),DR(VR(e),"_range",null),DR(VR(e),"_receivedLength",0),DR(VR(e),"_url",null),DR(VR(e),"_onProgress",null),DR(VR(e),"_index",-1),DR(VR(e),"_headers",null),DR(VR(e),"_currentChunkSizeKB",384),DR(VR(e),"_timeout",null),DR(VR(e),"_xhr",null),DR(VR(e),"_withCredentials",null),DR(VR(e),"_startTime",-1),DR(VR(e),"_loadCompleteResolve",null),DR(VR(e),"_loadCompleteReject",null),DR(VR(e),"_runing",!1),DR(VR(e),"_logger",!1),DR(VR(e),"_vid",""),DR(VR(e),"_responseType",void 0),DR(VR(e),"_credentials",void 0),DR(VR(e),"_method",void 0),DR(VR(e),"_transformResponse",void 0),DR(VR(e),"_firstRtt",void 0),DR(VR(e),"_onCancel",null),DR(VR(e),"_priOptions",null),e}return FR(r,[{key:"load",value:function(e){var t=this;clearTimeout(this._timeoutTimer),this._logger=e.logger,this._range=e.range,this._onProgress=e.onProgress,this._index=e.index,this._headers=e.headers,this._withCredentials="include"===e.credentials||"same-origin"===e.credentials,this._body=e.body||null,e.method&&(this._method=e.method),this._timeout=e.timeout||null,this._runing=!0,this._vid=e.vid||e.url,this._responseType=e.responseType,this._firstRtt=-1,this._onTimeout=e.onTimeout,this._onCancel=e.onCancel,this._request=e.request,this._priOptions=e.priOptions||{},this._logger.debug("【xhrLoader task】, range",this._range),this._url=qC(e.url,e.params);var r=oO();return new Dg((function(e,r){t._loadCompleteResolve=e,t._loadCompleteReject=r,t._startLoad()})).catch((function(e){if(clearTimeout(t._timeoutTimer),t._runing=!1,!t._aborted)throw(e=e instanceof DC?e:new DC(t._url,t._request)).startTime=r,e.endTime=oO(),e.options={index:t._index,vid:t._vid,priOptions:t._priOptions},e}))}},{key:"_startLoad",value:function(){var e=null;if(this._responseType===MC&&this._range&&this._range.length>1)if(this._onProgress){this._firstRtt=-1;var t=1024*this._currentChunkSizeKB,r=this._range[0]+this._receivedLength,n=this._range[1];t<this._range[1]-r&&(n=r+t),e=[r,n],this._logger.debug("[xhr_loader->],tast :",this._range,", SubRange, ",e)}else e=this._range,this._logger.debug("[xhr_loader->],tast :",this._range,", allRange, ",e);this._internalOpen(e)}},{key:"_internalOpen",value:function(e){var t=this;try{var r,n;this._startTime=oO();var o=this._xhr=new XMLHttpRequest;o.open(this._method||"GET",this._url,!0),o.responseType=this._responseType,this._timeout&&(o.timeout=this._timeout),o.withCredentials=this._withCredentials,o.onload=Ry(r=this._onLoad).call(r,this),o.onreadystatechange=Ry(n=this._onReadyStatechange).call(n,this),o.onerror=function(e){var r,n,o;t._running=!1;var i=new DC(t._url,t._request,null==e||null===(r=e.currentTarget)||void 0===r?void 0:r.response,"xhr.onerror.status:"+(null==e||null===(n=e.currentTarget)||void 0===n?void 0:n.status)+",statusText,"+(null==e||null===(o=e.currentTarget)||void 0===o?void 0:o.statusText));i.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(i)},o.ontimeout=function(e){t.cancel();var r=new DC(t._url,t._request,{status:408},"timeout");t._onTimeout&&(r.isTimeout=!0,t._onTimeout(r,{index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions})),r.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(r)};var i,a=this._headers||{},u=HC(e);if(u&&(a.Range=u),a)ef(i=kl(a)).call(i,(function(e){o.setRequestHeader(e,a[e])}));this._logger.debug("[xhr.send->] tast,",this._range,",load sub range, ",e),o.send(this._body)}catch(s){s.options={index:this._index,range:e,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(s)}}},{key:"_onReadyStatechange",value:function(e){2===e.target.readyState&&this._firstRtt<0&&(this._firstRtt=oO())}},{key:"_onLoad",value:function(e){var t,r=e.target.status;if(r<200||r>299){var n=new DC(this._url,null,CR(CR({},e.target.response),{},{status:r}),"bad response,status:"+r);return n.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(n)}var o,i=null,a=!1,u=(null===(t=this._range)||void 0===t?void 0:t.length)>0?this._range[0]:0;if(this._responseType===MC){var s,c=new Uint8Array(e.target.response);if(o=u+this._receivedLength,c&&c.byteLength>0){this._receivedLength+=c.byteLength;var l=oO()-this._startTime,f=$C(this._receivedLength,l);this.emit(ZC,{speed:f,len:this._receivedLength,time:l,vid:this._vid,index:this._index,range:[o,u+this._receivedLength],priOptions:this._priOptions})}i=c,a=!((null===(s=this._range)||void 0===s?void 0:s.length)>1&&this._range[1]&&this._receivedLength<this._range[1]-this._range[0]),this._logger.debug("[xhr load done->], tast :",this._range,", start",o,"end ",u+this._receivedLength,",dataLen,",c?c.byteLength:0,",receivedLength",this._receivedLength,",index,",this._index,", done,",a)}else a=!0,i=e.target.response;var p={ok:r>=200&&r<300,status:r,statusText:this._xhr.statusText,url:this._xhr.responseURL,headers:this._getHeaders(this._xhr),body:this._xhr.response};this._transformResponse&&(p=this._transformResponse(p,this._url)||p),this._onProgress&&this._onProgress(i,a,{index:this._index,vid:this._vid,range:[o,u+this._receivedLength],startTime:this._startTime,endTime:oO(),priOptions:this._priOptions},p),a?(this._runing=!1,this._loadCompleteResolve&&this._loadCompleteResolve(GC(this._onProgress?null:i,a,p,p.headers["content-length"],p.headers.age,this._startTime,this._firstRtt,this._index,this._range,this._vid,this._priOptions))):this._startLoad()}},{key:"cancel",value:function(){if(!this._aborted)return this._aborted=!0,this._runing=!1,WR(BR(r.prototype),"removeAllListeners",this).call(this),this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions}),this._xhr?this._xhr.abort():void 0}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}},{key:"_getHeaders",value:function(e){var t,r,n={},o=XR(xT(t=e.getAllResponseHeaders()).call(t).split("\r\n"));try{for(o.s();!(r=o.n()).done;){var i=r.value.split(": ");n[i[0].toLowerCase()]=py(i).call(i,1).join(": ")}}catch(a){o.e(a)}finally{o.f()}return n}}],[{key:"isSupported",value:function(){return"undefined"!=typeof XMLHttpRequest}}]),r}(ok),nP=["retry","retryDelay","onRetryError","transformError"],oP=function(){function e(t,r){var n,o,i;MR(this,e),this.promise=((i=new Dg((function(e,t){n=e,o=t}))).used=!1,i.resolve=function(){return i.used=!0,n.apply(void 0,arguments)},i.reject=function(){return i.used=!0,o.apply(void 0,arguments)},i),this.alive=!!r.onProgress,!r.logger&&(r.logger=new RC("Loader")),this._loaderType=t,this._loader=t===jC&&window.fetch?new tP:new rP,this._config=r,this._retryCount=0,this._retryTimer=null,this._canceled=!1,this._retryCheckFunc=r.retryCheckFunc,this._logger=r.logger}var t;return FR(e,[{key:"exec",value:function(){var e=this,t=this._config,r=t.retry,n=t.retryDelay,o=t.onRetryError,i=t.transformError,a=$R(t,nP),u=function(){var t=IR(PR().mark((function t(){var s,c,l;return PR().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e._loader.load(a);case 3:s=t.sent,e.promise.resolve(s),t.next=27;break;case 7:if(t.prev=7,t.t0=t.catch(0),e._loader.running=!1,e._logger.debug("[task request catch err]",t.t0),!e._canceled){t.next=13;break}return t.abrupt("return");case 13:if(t.t0.loaderType=e._loaderType,t.t0.retryCount=e._retryCount,c=t.t0,i&&(c=i(c)||c),o&&e._retryCount>0&&o(c,e._retryCount,{index:a.index,vid:a.vid,range:a.range,priOptions:a.priOptions}),e._retryCount++,l=!0,e._retryCheckFunc&&(l=e._retryCheckFunc(t.t0)),!(l&&e._retryCount<=r)){t.next=26;break}return clearTimeout(e._retryTimer),e._logger.debug("[task request setTimeout],retry",e._retryCount,",retry range,",a.range),e._retryTimer=Xk(u,n),t.abrupt("return");case 26:e.promise.reject(c);case 27:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(){return t.apply(this,arguments)}}();return u(),this.promise}},{key:"cancel",value:(t=IR(PR().mark((function e(){return PR().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return clearTimeout(this._retryTimer),this._canceled=!0,this._loader.running=!1,e.abrupt("return",this._loader.cancel());case 4:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"running",get:function(){return this._loader&&this._loader.running}},{key:"loader",get:function(){return this._loader}}]),e}();function iP(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new Dg((function(t){return Xk(t,e)}))}var aP,uP=function(e){UR(n,e);var t,r=YR(n);function n(e){var t;return MR(this,n),DR(VR(t=r.call(this,e)),"type",jC),DR(VR(t),"_queue",[]),DR(VR(t),"_alive",[]),DR(VR(t),"_currentTask",null),DR(VR(t),"_config",void 0),t._config=function(e){return CR({loaderType:jC,retry:0,retryDelay:0,timeout:0,request:null,onTimeout:void 0,onProgress:void 0,onRetryError:void 0,transformRequest:void 0,transformResponse:void 0,transformError:void 0,responseType:NC,range:void 0,url:"",params:void 0,method:"GET",headers:{},body:void 0,mode:void 0,credentials:void 0,cache:void 0,redirect:void 0,referrer:void 0,referrerPolicy:void 0,integrity:void 0,onProcessMinLen:0},e)}(e),t._config.loaderType!==IC&&tP.isSupported()||(t.type=IC),t.log=e.logger,t}return FR(n,[{key:"isFetch",value:function(){return this.type===jC}},{key:"load",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"string"!=typeof e&&e?r=e:r.url=e||r.url||this._config.url,(r=rk({},this._config,r)).params&&(r.params=rk({},r.params)),r.headers&&BC(r.headers)&&(r.headers=rk({},r.headers)),r.body&&BC(r.body)&&(r.body=rk({},r.body)),r.transformRequest&&(r=r.transformRequest(r)||r),r.logger=this.log;var n=new oP(this.type,r);return n.loader.on(ZC,(function(e){t.emit(ZC,e)})),this._queue.push(n),1!==this._queue.length||this._currentTask&&this._currentTask.running||this._processTask(),n.promise}},{key:"cancel",value:(t=IR(PR().mark((function e(){var t;return PR().wrap((function(e){for(var r,n,o;;)switch(e.prev=e.next){case 0:return t=fm(r=PS(n=this._queue).call(n,(function(e){return e.cancel()}))).call(r,PS(o=this._alive).call(o,(function(e){return e.cancel()}))),this._currentTask&&t.push(this._currentTask.cancel()),this._queue=[],this._alive=[],e.next=6,Dg.all(t);case 6:return e.next=8,iP();case 8:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"_processTask",value:function(){var e=this;if(this._currentTask=this._queue.shift(),this._currentTask){this._currentTask.alive&&this._alive.push(this._currentTask);var t=this._currentTask.exec().catch((function(e){}));t&&"function"==typeof t.finally&&t.finally((function(){var t,r,n;null!==(t=e._currentTask)&&void 0!==t&&t.alive&&(null===(r=e._alive)||void 0===r?void 0:r.length)>0&&(e._alive=jl(n=e._alive).call(n,(function(t){return t&&t!==e._currentTask})));e._processTask()}))}}}],[{key:"isFetchSupport",value:function(){return tP.isSupported()}}]),n}(ok),sP="core.rtcstatechange",cP={},lP={},fP={exports:{}},pP=fP.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v"}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",t+=null!=e.rateNumerator?" rate=%s":"",t+=null!=e.rateDenominator?"/%s":""}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};ef(aP=kl(pP)).call(aP,(function(e){var t=pP[e];ef(t).call(t,(function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))}));var hP=fP.exports;!function(e,t){var r=function(e){return String(Number(e))===e?Number(e):e},n=function(e,t,n){var o=e.name&&e.names;e.push&&!t[e.push]?t[e.push]=[]:o&&!t[e.name]&&(t[e.name]={});var i=e.push?{}:o?t[e.name]:t;!function(e,t,n,o){if(o&&!n)t[o]=r(e[1]);else for(var i=0;i<n.length;i+=1)null!=e[i+1]&&(t[n[i]]=r(e[i+1]))}(n.match(e.reg),i,e.names,e.name),e.push&&t[e.push].push(i)},o=hP,i=Ry(t=RegExp.prototype.test).call(t,/^([a-z])=(.*)/);e.parse=function(e){var t,r,a={},u=[],s=a;return ef(t=jl(r=e.split(/(\r\n|\r|\n)/)).call(r,i)).call(t,(function(e){var t=e[0],r=py(e).call(e,2);"m"===t&&(u.push({rtp:[],fmtp:[]}),s=u[u.length-1]);for(var i=0;i<(o[t]||[]).length;i+=1){var a=o[t][i];if(a.reg.test(r))return n(a,s,r)}})),a.media=u,a};var a=function(e,t){var n=t.split(/=(.+)/,2);return 2===n.length?e[n[0]]=r(n[1]):1===n.length&&t.length>1&&(e[n[0]]=void 0),e};e.parseParams=function(e){var t;return lC(t=e.split(/;\s?/)).call(t,a,{})},e.parseFmtpConfig=e.parseParams,e.parsePayloads=function(e){var t;return PS(t=e.toString().split(" ")).call(t,Number)},e.parseRemoteCandidates=function(e){for(var t,n=[],o=PS(t=e.split(" ")).call(t,r),i=0;i<o.length;i+=3)n.push({component:o[i],ip:o[i+1],port:o[i+2]});return n},e.parseImageAttributes=function(e){var t;return PS(t=e.split(" ")).call(t,(function(e){var t;return lC(t=e.substring(1,e.length-1).split(",")).call(t,a,{})}))},e.parseSimulcastStreamList=function(e){var t;return PS(t=e.split(";")).call(t,(function(e){var t;return PS(t=e.split(",")).call(t,(function(e){var t,n=!1;return"~"!==e[0]?t=r(e):(t=r(e.substring(1,e.length)),n=!0),{scid:t,paused:n}}))}))}}(lP);var dP=hP,vP=/%[sdv%]/g,gP=function(e){var t=1,r=arguments,n=r.length;return e.replace(vP,(function(e){if(t>=n)return e;var o=r[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(o);case"%d":return Number(o);case"%v":return""}}))},yP=function(e,t,r){var n=[e+"="+(t.format instanceof Function?t.format(t.push?r:r[t.name]):t.format)];if(t.names)for(var o=0;o<t.names.length;o+=1){var i=t.names[o];t.name?n.push(r[t.name][i]):n.push(r[t.names[o]])}else n.push(r[t.name]);return gP.apply(null,n)},mP=["v","o","s","i","u","e","p","c","b","t","r","z","a"],_P=["i","c","b","a"],bP=lP,wP=function(e,t){var r,n;t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),ef(r=e.media).call(r,(function(e){null==e.payloads&&(e.payloads="")}));var o=t.outerOrder||mP,i=t.innerOrder||_P,a=[];return ef(o).call(o,(function(t){var r;ef(r=dP[t]).call(r,(function(r){if(r.name in e&&null!=e[r.name])a.push(yP(t,r,e));else if(r.push in e&&null!=e[r.push]){var n;ef(n=e[r.push]).call(n,(function(e){a.push(yP(t,r,e))}))}}))})),ef(n=e.media).call(n,(function(e){a.push(yP("m",dP.m[0],e)),ef(i).call(i,(function(t){var r;ef(r=dP[t]).call(r,(function(r){if(r.name in e&&null!=e[r.name])a.push(yP(t,r,e));else if(r.push in e&&null!=e[r.push]){var n;ef(n=e[r.push]).call(n,(function(e){a.push(yP(t,r,e))}))}}))}))})),a.join("\r\n")+"\r\n"};function xP(e){return new Dg((function(t){var r;null===(r=e.getStats(null))||void 0===r||r.then((function(e){var r,n;t((n=[],ef(r=e).call(r,(function(e){"inbound-rtp"===e.type&&n.push(e)})),PS(n).call(n,(function(e){var t=r.get(e.codecId),n=r.get(e.trackId);return{type:e.mediaType,inboundRtp:e,codec:t,track:n}}))))}))})).catch((function(){}))}cP.write=wP,cP.parse=bP.parse,cP.parseParams=bP.parseParams,cP.parseFmtpConfig=bP.parseFmtpConfig,cP.parsePayloads=bP.parsePayloads,cP.parseRemoteCandidates=bP.parseRemoteCandidates,cP.parseImageAttributes=bP.parseImageAttributes,cP.parseSimulcastStreamList=bP.parseSimulcastStreamList;var SP=new RC("rts"),kP=function(e){tm(o,e);var t,r,n=im(o);function o(e){var t;return Jy(this,o),em(om(t=n.call(this)),"_url",""),em(om(t),"_pc",null),em(om(t),"_audioTransceicer",null),em(om(t),"_videoTransceicer",null),em(om(t),"_mediaStream",null),em(om(t),"_media",null),em(om(t),"_opts",null),em(om(t),"_loader",null),em(om(t),"_retry",0),em(om(t),"_waitingTimer",0),em(om(t),"_rctConnectStartTs",0),em(om(t),"_onTrack",(function(e){if(SP.log("addTrack: ",e.track,e.streams),t["_".concat(e.track.kind)]=e.track,!t._mediaStream){t._mediaStream=new MediaStream,t._media.srcObject=t._mediaStream;var r=t._media.play();r&&r.catch&&r.catch((function(e){}))}t._mediaStream.addTrack(e.track)})),em(om(t),"_mockWaitingByTimeupdate",(function(){var e,r;"connected"!==(null===(e=t._pc)||void 0===e?void 0:e.connectionState)&&"connected"!==(null===(r=t._pc)||void 0===r?void 0:r.iceConnectionState)||(clearTimeout(t._waitingTimer),t._waitingTimer=Xk((function(){var e;null!==(e=t._media)&&void 0!==e&&e.paused||t.emit("waiting")}),t._opts.stallInterval))})),em(om(t),"_onLoadedData",(function(){SP.log("loadeddata emit"),t.getStatsSnapshoot().then((function(e){t.emit(QC,{type:"video",elapsed:oO()-t._rctConnectStartTs,meta:e.video}),t.emit(QC,{type:"audio",elapsed:oO()-t._rctConnectStartTs,meta:e.audio})}))})),em(om(t),"_onLoaderRetry",(function(e,r){t.emit(JC,{error:SC.network(e),retryTime:r})})),t._opts=function(e){return Yy({retryCount:0,retryDelay:1e3,loadTimeout:5e3,stallInterval:400,delayHint:0},e)}(e),t._media=t._opts.media,t._loader=new uP(Yy(Yy({},e.fetchOptions),{},{responseType:"json",method:"POST",retry:t._opts.retryCount,retryDelay:t._opts.retryDelay,timeout:t._opts.loadTimeout,onRetryError:t._onLoaderRetry,logger:SP})),t._retry=t._opts.retryCount,t._bindMediaEvent(),t}return Zy(o,[{key:"pc",get:function(){return this._pc}},{key:"audioTrack",get:function(){return this._audio}},{key:"videoTack",get:function(){return this._video}},{key:"loader",get:function(){return this._loader}},{key:"getStats",value:function(){return xP(this._pc)}},{key:"getStatsSnapshoot",value:function(){return this.getStats().then((function(e){return function(e){var t,r,n,o,i,a,u,s,c,l,f,p,h,d,v,g,y,m,_,b,w,x,S,k,T,O,E,L,R,C=null==e?void 0:jl(e).call(e,(function(e){return"video"===e.type}))[0],P=null==e?void 0:jl(e).call(e,(function(e){return"audio"===e.type}))[0];return{video:C?{codec:null===(t=C.codec)||void 0===t?void 0:t.mimeType,payloadType:null===(r=C.codec)||void 0===r?void 0:r.payloadType,sdpFmtpLine:null===(n=C.codec)||void 0===n?void 0:n.sdpFmtpLine,bytesReceived:null===(o=C.inboundRtp)||void 0===o?void 0:o.bytesReceived,firCount:null===(i=C.inboundRtp)||void 0===i?void 0:i.firCount,pliCount:null===(a=C.inboundRtp)||void 0===a?void 0:a.pliCount,frameHeight:null===(u=C.inboundRtp)||void 0===u?void 0:u.frameHeight,frameWidth:null===(s=C.inboundRtp)||void 0===s?void 0:s.frameWidth,framesDecoded:null===(c=C.inboundRtp)||void 0===c?void 0:c.framesDecoded,framesDropped:null===(l=C.inboundRtp)||void 0===l?void 0:l.framesDropped,framesPerSecond:null===(f=C.inboundRtp)||void 0===f?void 0:f.framesPerSecond,framesReceived:null===(p=C.inboundRtp)||void 0===p?void 0:p.framesReceived,jitter:null===(h=C.inboundRtp)||void 0===h?void 0:h.jitter,jitterBufferDelay:null===(d=C.inboundRtp)||void 0===d?void 0:d.jitterBufferDelay,keyFramesDecoded:null===(v=C.inboundRtp)||void 0===v?void 0:v.keyFramesDecoded,nackCount:null===(g=C.inboundRtp)||void 0===g?void 0:g.nackCount,packetsLost:null===(y=C.inboundRtp)||void 0===y?void 0:y.packetsLost,packetsReceived:null===(m=C.inboundRtp)||void 0===m?void 0:m.packetsReceived}:void 0,audio:P?{codec:null===(_=P.codec)||void 0===_?void 0:_.mimeType,payloadType:null===(b=P.codec)||void 0===b?void 0:b.payloadType,sdpFmtpLine:null===(w=P.codec)||void 0===w?void 0:w.sdpFmtpLine,bytesReceived:null===(x=P.inboundRtp)||void 0===x?void 0:x.bytesReceived,audioLevel:null===(S=P.inboundRtp)||void 0===S?void 0:S.audioLevel,jitter:null===(k=P.inboundRtp)||void 0===k?void 0:k.jitter,jitterBufferDelay:null===(T=P.inboundRtp)||void 0===T?void 0:T.jitterBufferDelay,packetsLost:null===(O=P.inboundRtp)||void 0===O?void 0:O.packetsLost,packetsReceived:null===(E=P.inboundRtp)||void 0===E?void 0:E.packetsReceived,totalSamplesDuration:null===(L=P.inboundRtp)||void 0===L?void 0:L.totalSamplesDuration,totalSamplesReceived:null===(R=P.inboundRtp)||void 0===R?void 0:R.totalSamplesReceived}:void 0}}(e)}))}},{key:"load",value:(r=Xy(Wy().mark((function e(t){var r,n=this;return Wy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._disconnect(),this._url=t,this._retry=this._opts.retryCount,(r=this._pc)&&r.close(),e.prev=5,this._pc=new RTCPeerConnection,this._bindRTCEvents(),e.next=10,this._connect(t);case 10:e.next=15;break;case 12:e.prev=12,e.t0=e.catch(5),Xk((function(){return n._emitError(SC.create(bC,null,e.t0))}));case 15:case"end":return e.stop()}}),e,this,[[5,12]])}))),function(e){return r.apply(this,arguments)})},{key:"switchURL",value:function(e){this.load(e||this._url)}},{key:"_bindRTCEvents",value:function(){var e=this,t=this._pc;t.addEventListener("track",this._onTrack),t.addEventListener("connectionstatechange",(function(){if(SP.warn("onconnectionstatechange:",t.connectionState),e.emit(sP,{state:t.connectionState,url:e._url}),"disconnected"!==t.connectionState&&"failed"!==t.connectionState||e.emit("waiting"),"failed"===t.connectionState){if(0===e._retry)return void e._emitError(SC.network(new Error("rtc connect failed")));e._retry--,e.load(e._url)}"connected"===t.connectionState&&e.emit(YC,{url:e._url,responseUrl:e._url,elapsed:oO()-e._rctConnectStartTs})}))}},{key:"_bindMediaEvent",value:function(){this._media.addEventListener("timeupdate",this._mockWaitingByTimeupdate),this._media.addEventListener("loadeddata",this._onLoadedData)}},{key:"_connect",value:(t=Xy(Wy().mark((function e(){var t,r,n,o,i,a,u,s,c,l,f,p,h,d;return Wy().wrap((function(e){for(var v,g,y,m;;)switch(e.prev=e.next){case 0:if(!(t=this._pc).addTransceiver){e.next=11;break}return this._audioTransceicer=t.addTransceiver("audio",{direction:"recvonly"}),this._videoTransceicer=t.addTransceiver("video",{direction:"recvonly"}),(n=this._opts.delayHint)&&(this._audioTransceicer.receiver.playoutDelayHint=n,this._audioTransceicer.receiver.jitterBufferDelayHint=n,this._videoTransceicer.receiver.playoutDelayHint=n,this._videoTransceicer.receiver.jitterBufferDelayHint=n),e.next=8,t.createOffer();case 8:r=e.sent,e.next=14;break;case 11:return e.next=13,t.createOffer({iceRestart:!0,offerToReceiveAudio:!0,offerToReceiveVideo:!0});case 13:r=e.sent;case 14:return SP.log("local offer"),SP.log(r.sdp),o=cP.parse(r.sdp),ef(v=jl(g=o.media).call(g,(function(e){return"audio"===e.type}))).call(v,(function(e){var t,r,n,o=null===(n=jl(t=e.rtp).call(t,(function(e){return"opus"===e.codec}))[0])||void 0===n?void 0:n.payload;o&&ef(r=e.fmtp).call(r,(function(e){e.payload===o&&(e.config="minptime=10;stereo=1;useinbandfec=1")}))})),r.sdp=cP.write(o),SP.log("local offer modified:\n",r.sdp),e.next=22,t.setLocalDescription(r);case 22:return i=this._url,e.prev=23,this._opts.preProcessUrl&&(i=this._opts.preProcessUrl(i).url),this._loader.finnalUrl=i,s=/_?session_id=([^&$]+)/.exec(i),c=s?s[1]:oO()+"666",this.emit(WC,{url:i,offersdp:r.sdp,sessionId:c}),l=oO(),e.next=32,this._loader.load(i,{body:TO({sessionId:c,version:"1.0-html",localSdp:r})});case 32:if(f=e.sent,this.emit(KC,{headers:f.response.headers}),404!==(null==(p=null==f?void 0:f.data)?void 0:p.code)&&403!==(null==p?void 0:p.code)){e.next=40;break}return(h=SC.create(404===p.code?dC:hC,null,p)).errorType=fC,this._emitError(h),e.abrupt("return");case 40:if(200===(null==p?void 0:p.code)){e.next=42;break}throw new Error(fm(y="code: ".concat(null==p?void 0:p.code,", message:")).call(y,null==p?void 0:p.message));case 42:return SP.log("answer:"),SP.log(null===(a=p.remoteSdp)||void 0===a?void 0:a.sdp),this.emit(XC,{url:i,elapsed:oO()-l,answersdp:null===(u=p.remoteSdp)||void 0===u?void 0:u.sdp}),d=cP.parse(p.remoteSdp.sdp),ef(m=d.media).call(m,(function(e){e.fingerprint.hash=e.fingerprint.hash.toUpperCase()})),p.remoteSdp.sdp=cP.write(d),SP.log("answer modified:\n",p.remoteSdp.sdp),this._rctConnectStartTs=oO(),e.next=52,this._pc.setRemoteDescription(p.remoteSdp);case 52:e.next=57;break;case 54:e.prev=54,e.t0=e.catch(23),this._emitError(SC.network(e.t0));case 57:case"end":return e.stop()}}),e,this,[[23,54]])}))),function(){return t.apply(this,arguments)})},{key:"_disconnect",value:function(){var e;null===(e=this._loader)||void 0===e||e.cancel(),this._audioTransceicer=null,this._videoTransceicer=null,this._mediaStream=null}},{key:"_emitError",value:function(e){this.emit(VC,e)}},{key:"destroy",value:function(){var e;this._disconnect(),null===(e=this._media)||void 0===e||e.removeEventListener("timeupdate",this._mockWaitingByTimeupdate),this._media&&(this._media.srcObject=null),this._pc&&this._pc.close(),this._loader=null,this._media=null,this._pc=null}}],[{key:"enableLogger",value:function(){RC.enable()}},{key:"disableLogger",value:function(){RC.disable()}}]),o}(ok);try{localStorage.getItem("xgd")?kP.enableLogger():kP.disableLogger()}catch(EP){}function TP(){return!(!window.RTCPeerConnection||!window.RTCPeerConnection.prototype.addTransceiver)}var OP=function(e){tm(n,e);var t,r=im(n);function n(){var e,t;Jy(this,n);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return em(om(t=r.call.apply(r,fm(e=[this]).call(e,i))),"_rts",null),em(om(t),"_rtsOpts",null),em(om(t),"_init",(function(){var e;null===(e=t._rts)||void 0===e||e.destroy();var r=t.player.config,n=r.rts||{};t._rtsOpts=n,t._rts=new kP(Yy({media:t.player.video,preProcessUrl:function(e,r){var n,o;return(null===(n=(o=t.player).preProcessUrl)||void 0===n?void 0:n.call(o,e,r))||{url:e,ext:r}}},n)),t._rts.load(r.url)})),em(om(t),"_onSwitchURL",(function(e){t._rts&&(t.player.config.url=e,t._rts.switchURL(e))})),t}return Zy(n,[{key:"pc",get:function(){var e;return null===(e=this._rts)||void 0===e?void 0:e.pc}},{key:"videoTrack",get:function(){var e;return null===(e=this._rts)||void 0===e?void 0:e.videoTack}},{key:"audioTrack",get:function(){var e;return null===(e=this._rts)||void 0===e?void 0:e.audioTrack}},{key:"core",get:function(){return this._rts}},{key:"loader",get:function(){var e;return null===(e=this._rts)||void 0===e?void 0:e.loader}},{key:"version",get:function(){return"0.2.0-alpha.5"}},{key:"beforePlayerInit",value:function(){var e,t=this;this._init(),this.player.switchURL=this._onSwitchURL,null===(e=this.player)||void 0===e||e.useHooks("replay",(function(){var e;return null===(e=t._rts)||void 0===e?void 0:e.switchURL()})),this.on("urlchange",this._onSwitchURL),this.on("destroy",this.destroy),this._transErrorEvent(),this._transCoreEvent(WC),this._transCoreEvent(XC),this._transCoreEvent(YC),this._transCoreEvent(KC),this._transCoreEvent(JC),this._transCoreEvent(QC),this._transCoreEvent(sP);try{wO.defineGetterOrSetter(this.player,{__url:{get:function(){try{return bx.createObjectURL(new MediaSource)}catch(n){var e,r;null!==(e=t.player)&&void 0!==e&&null!==(r=e.video)&&void 0!==r&&r.srcObject||(t.player.video.srcObject=new MediaStream)}}}})}catch(r){}}},{key:"getStats",value:function(){var e;return null===(e=this._rts)||void 0===e?void 0:e.getStats()}},{key:"getStatsSnapshoot",value:function(){var e;return null===(e=this._rts)||void 0===e?void 0:e.getStatsSnapshoot()}},{key:"_transErrorEvent",value:function(){var e=this;this._rts.on(VC,(function(t){if(e.player){var r=new vO(e.player,t),n=e._rtsOpts,o=n.backupConstruct,i=n.backupURL;if(i){var a,u=e.player;u.emit("core_event",Yy(Yy({},{url:u.config.url,code:r.errorCode,message:r.message,httpCode:r.httpCode}),{},{eventName:"core.degrade"})),u.emit("degrade",{url:u.config.url,code:r.errorCode,message:r.message,httpCode:r.httpCode}),u.unRegisterPlugin("rts"),e._rts=null,u.rts=null,null!==(a=u.video)&&void 0!==a&&a.srcObject&&(u.video.srcObject=null),u.config.url=i,o?u.registerPlugin(o).beforePlayerInit():u.video.src=i}else e.player.emit("error",r)}})),this._rts.on("waiting",(function(){var t;null===(t=e.player)||void 0===t||t.emit("waiting")}))}},{key:"_transCoreEvent",value:function(e){var t=this;this._rts.on(e,(function(r){t.player&&t.player.emit("core_event",Yy(Yy({},r),{},{eventName:e}))}))}},{key:"destroy",value:function(){var e;null===(e=this._rts)||void 0===e||e.destroy()}}],[{key:"pluginName",get:function(){return"rts"}},{key:"isSupported",value:function(){return TP()}},{key:"getCapacity",value:function(){return function(){if(!TP())return Dg.reject("RTCPeerConnection no support");var e;try{(e=new RTCPeerConnection).addTransceiver("video",{direction:"recvonly"}),e.addTransceiver("audio",{direction:"recvonly"})}catch(t){return Dg.reject(null==t?void 0:t.message)}return e.createOffer().then((function(t){var r,n,o,i;SP.log(t.sdp);var a=cP.parse(t.sdp),u=null===(r=a.media)||void 0===r?void 0:jl(r).call(r,(function(e){return"video"===e.type}))[0],s=null===(n=a.media)||void 0===n?void 0:jl(n).call(n,(function(e){return"audio"===e.type}))[0],c=null==u||null===(o=u.rtp)||void 0===o?void 0:jl(o).call(o,(function(e){return"VP8"===e.codec||"VP9"===e.codec||"H264"===e.codec})),l=null==s||null===(i=s.rtp)||void 0===i?void 0:jl(i).call(i,(function(e){return"opus"===e.codec})),f=null==u?void 0:u.fmtp,p=null==s?void 0:s.fmtp;return f&&PS(c).call(c,(function(e){var t;e.config=null===(t=jl(f).call(f,(function(t){return t.payload===e.payload}))[0])||void 0===t?void 0:t.config})),p&&PS(l).call(l,(function(e){var t;e.config=null===(t=jl(p).call(p,(function(t){return t.payload===e.payload}))[0])||void 0===t?void 0:t.config})),e.close(),{video:c,audio:l}}))}()}},{key:"isSupportedH264",value:(t=Xy(Wy().mark((function e(t){var r,o,i,a,u;return Wy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n.getCapacity();case 3:if(o=e.sent,i=null===(r=o.video)||void 0===r?void 0:jl(r).call(r,(function(e){var r;return"H264"===e.codec&&(!t||-1!==Cx(r=e.config).call(r,t))})).length){e.next=10;break}return e.next=8,n.getCapacity();case 8:u=e.sent,i=null===(a=u.video)||void 0===a?void 0:jl(a).call(a,(function(e){var r;return"H264"===e.codec&&(!t||-1!==Cx(r=e.config).call(r,t))})).length;case 10:return e.abrupt("return",!!i);case 13:return e.prev=13,e.t0=e.catch(0),e.abrupt("return",!1);case 16:case"end":return e.stop()}}),e,null,[[0,13]])}))),function(e){return t.apply(this,arguments)})}]),n}(wO);return VePlayer.register("plugin:rtm",OP),OP}));
