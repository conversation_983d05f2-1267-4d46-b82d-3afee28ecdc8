!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var r=function(e){try{return!!e()}catch(t){return!0}},n=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),i=n,a=Function.prototype,o=a.call,s=i&&a.bind.bind(o,o),u=i?s:function(e){return function(){return o.apply(e,arguments)}},c=u({}.isPrototypeOf),l=function(e){return e&&e.Math==Math&&e},f=l("object"==typeof globalThis&&globalThis)||l("object"==typeof window&&window)||l("object"==typeof self&&self)||l("object"==typeof e&&e)||function(){return this}()||e||Function("return this")(),h=n,d=Function.prototype,p=d.apply,v=d.call,g="object"==typeof Reflect&&Reflect.apply||(h?v.bind(p):function(){return v.apply(p,arguments)}),y=u,m=y({}.toString),_=y("".slice),b=function(e){return _(m(e),8,-1)},w=b,k=u,S=function(e){if("Function"===w(e))return k(e)},x="object"==typeof document&&document.all,E={all:x,IS_HTMLDDA:void 0===x&&void 0!==x},T=E.all,A=E.IS_HTMLDDA?function(e){return"function"==typeof e||e===T}:function(e){return"function"==typeof e},O={},L=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),C=n,D=Function.prototype.call,P=C?D.bind(D):function(){return D.apply(D,arguments)},R={},U={}.propertyIsEnumerable,I=Object.getOwnPropertyDescriptor,M=I&&!U.call({1:2},1);R.f=M?function(e){var t=I(this,e);return!!t&&t.enumerable}:U;var B,j,F=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},N=r,V=b,z=Object,G=u("".split),q=N((function(){return!z("z").propertyIsEnumerable(0)}))?function(e){return"String"==V(e)?G(e,""):z(e)}:z,H=function(e){return null==e},K=H,W=TypeError,Y=function(e){if(K(e))throw W("Can't call method on "+e);return e},J=q,Q=Y,X=function(e){return J(Q(e))},$=A,Z=E.all,ee=E.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:$(e)||e===Z}:function(e){return"object"==typeof e?null!==e:$(e)},te={},re=te,ne=f,ie=A,ae=function(e){return ie(e)?e:void 0},oe=function(e,t){return arguments.length<2?ae(re[e])||ae(ne[e]):re[e]&&re[e][t]||ne[e]&&ne[e][t]},se="undefined"!=typeof navigator&&String(navigator.userAgent)||"",ue=f,ce=se,le=ue.process,fe=ue.Deno,he=le&&le.versions||fe&&fe.version,de=he&&he.v8;de&&(j=(B=de.split("."))[0]>0&&B[0]<4?1:+(B[0]+B[1])),!j&&ce&&(!(B=ce.match(/Edge\/(\d+)/))||B[1]>=74)&&(B=ce.match(/Chrome\/(\d+)/))&&(j=+B[1]);var pe=j,ve=pe,ge=r,ye=f.String,me=!!Object.getOwnPropertySymbols&&!ge((function(){var e=Symbol();return!ye(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&ve&&ve<41})),_e=me&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,be=oe,we=A,ke=c,Se=Object,xe=_e?function(e){return"symbol"==typeof e}:function(e){var t=be("Symbol");return we(t)&&ke(t.prototype,Se(e))},Ee=String,Te=function(e){try{return Ee(e)}catch(t){return"Object"}},Ae=A,Oe=Te,Le=TypeError,Ce=function(e){if(Ae(e))return e;throw Le(Oe(e)+" is not a function")},De=Ce,Pe=H,Re=function(e,t){var r=e[t];return Pe(r)?void 0:De(r)},Ue=P,Ie=A,Me=ee,Be=TypeError,je={exports:{}},Fe=f,Ne=Object.defineProperty,Ve=function(e,t){try{Ne(Fe,e,{value:t,configurable:!0,writable:!0})}catch(r){Fe[e]=t}return t},ze="__core-js_shared__",Ge=f[ze]||Ve(ze,{}),qe=Ge;(je.exports=function(e,t){return qe[e]||(qe[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.30.2",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.30.2/LICENSE",source:"https://github.com/zloirock/core-js"});var He=je.exports,Ke=Y,We=Object,Ye=function(e){return We(Ke(e))},Je=Ye,Qe=u({}.hasOwnProperty),Xe=Object.hasOwn||function(e,t){return Qe(Je(e),t)},$e=u,Ze=0,et=Math.random(),tt=$e(1..toString),rt=function(e){return"Symbol("+(void 0===e?"":e)+")_"+tt(++Ze+et,36)},nt=He,it=Xe,at=rt,ot=me,st=_e,ut=f.Symbol,ct=nt("wks"),lt=st?ut.for||ut:ut&&ut.withoutSetter||at,ft=function(e){return it(ct,e)||(ct[e]=ot&&it(ut,e)?ut[e]:lt("Symbol."+e)),ct[e]},ht=P,dt=ee,pt=xe,vt=Re,gt=function(e,t){var r,n;if("string"===t&&Ie(r=e.toString)&&!Me(n=Ue(r,e)))return n;if(Ie(r=e.valueOf)&&!Me(n=Ue(r,e)))return n;if("string"!==t&&Ie(r=e.toString)&&!Me(n=Ue(r,e)))return n;throw Be("Can't convert object to primitive value")},yt=TypeError,mt=ft("toPrimitive"),_t=function(e,t){if(!dt(e)||pt(e))return e;var r,n=vt(e,mt);if(n){if(void 0===t&&(t="default"),r=ht(n,e,t),!dt(r)||pt(r))return r;throw yt("Can't convert object to primitive value")}return void 0===t&&(t="number"),gt(e,t)},bt=xe,wt=function(e){var t=_t(e,"string");return bt(t)?t:t+""},kt=ee,St=f.document,xt=kt(St)&&kt(St.createElement),Et=function(e){return xt?St.createElement(e):{}},Tt=Et,At=!L&&!r((function(){return 7!=Object.defineProperty(Tt("div"),"a",{get:function(){return 7}}).a})),Ot=L,Lt=P,Ct=R,Dt=F,Pt=X,Rt=wt,Ut=Xe,It=At,Mt=Object.getOwnPropertyDescriptor;O.f=Ot?Mt:function(e,t){if(e=Pt(e),t=Rt(t),It)try{return Mt(e,t)}catch(r){}if(Ut(e,t))return Dt(!Lt(Ct.f,e,t),e[t])};var Bt=r,jt=A,Ft=/#|\.prototype\./,Nt=function(e,t){var r=zt[Vt(e)];return r==qt||r!=Gt&&(jt(t)?Bt(t):!!t)},Vt=Nt.normalize=function(e){return String(e).replace(Ft,".").toLowerCase()},zt=Nt.data={},Gt=Nt.NATIVE="N",qt=Nt.POLYFILL="P",Ht=Nt,Kt=Ce,Wt=n,Yt=S(S.bind),Jt=function(e,t){return Kt(e),void 0===t?e:Wt?Yt(e,t):function(){return e.apply(t,arguments)}},Qt={},Xt=L&&r((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),$t=ee,Zt=String,er=TypeError,tr=function(e){if($t(e))return e;throw er(Zt(e)+" is not an object")},rr=L,nr=At,ir=Xt,ar=tr,or=wt,sr=TypeError,ur=Object.defineProperty,cr=Object.getOwnPropertyDescriptor,lr="enumerable",fr="configurable",hr="writable";Qt.f=rr?ir?function(e,t,r){if(ar(e),t=or(t),ar(r),"function"==typeof e&&"prototype"===t&&"value"in r&&hr in r&&!r[hr]){var n=cr(e,t);n&&n[hr]&&(e[t]=r.value,r={configurable:fr in r?r[fr]:n[fr],enumerable:lr in r?r[lr]:n[lr],writable:!1})}return ur(e,t,r)}:ur:function(e,t,r){if(ar(e),t=or(t),ar(r),nr)try{return ur(e,t,r)}catch(n){}if("get"in r||"set"in r)throw sr("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var dr=Qt,pr=F,vr=L?function(e,t,r){return dr.f(e,t,pr(1,r))}:function(e,t,r){return e[t]=r,e},gr=f,yr=g,mr=S,_r=A,br=O.f,wr=Ht,kr=te,Sr=Jt,xr=vr,Er=Xe,Tr=function(e){var t=function(r,n,i){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(r);case 2:return new e(r,n)}return new e(r,n,i)}return yr(e,this,arguments)};return t.prototype=e.prototype,t},Ar=function(e,t){var r,n,i,a,o,s,u,c,l,f=e.target,h=e.global,d=e.stat,p=e.proto,v=h?gr:d?gr[f]:(gr[f]||{}).prototype,g=h?kr:kr[f]||xr(kr,f,{})[f],y=g.prototype;for(a in t)n=!(r=wr(h?a:f+(d?".":"#")+a,e.forced))&&v&&Er(v,a),s=g[a],n&&(u=e.dontCallGetSet?(l=br(v,a))&&l.value:v[a]),o=n&&u?u:t[a],n&&typeof s==typeof o||(c=e.bind&&n?Sr(o,gr):e.wrap&&n?Tr(o):p&&_r(o)?mr(o):o,(e.sham||o&&o.sham||s&&s.sham)&&xr(c,"sham",!0),xr(g,a,c),p&&(Er(kr,i=f+"Prototype")||xr(kr,i,{}),xr(kr[i],a,o),e.real&&y&&(r||!y[a])&&xr(y,a,o)))},Or=b,Lr=Array.isArray||function(e){return"Array"==Or(e)},Cr=Math.ceil,Dr=Math.floor,Pr=Math.trunc||function(e){var t=+e;return(t>0?Dr:Cr)(t)},Rr=function(e){var t=+e;return t!=t||0===t?0:Pr(t)},Ur=Rr,Ir=Math.min,Mr=function(e){return e>0?Ir(Ur(e),9007199254740991):0},Br=Mr,jr=function(e){return Br(e.length)},Fr=TypeError,Nr=function(e){if(e>9007199254740991)throw Fr("Maximum allowed index exceeded");return e},Vr=wt,zr=Qt,Gr=F,qr=function(e,t,r){var n=Vr(t);n in e?zr.f(e,n,Gr(0,r)):e[n]=r},Hr={};Hr[ft("toStringTag")]="z";var Kr="[object z]"===String(Hr),Wr=Kr,Yr=A,Jr=b,Qr=ft("toStringTag"),Xr=Object,$r="Arguments"==Jr(function(){return arguments}()),Zr=Wr?Jr:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(r){}}(t=Xr(e),Qr))?r:$r?Jr(t):"Object"==(n=Jr(t))&&Yr(t.callee)?"Arguments":n},en=A,tn=Ge,rn=u(Function.toString);en(tn.inspectSource)||(tn.inspectSource=function(e){return rn(e)});var nn=tn.inspectSource,an=u,on=r,sn=A,un=Zr,cn=nn,ln=function(){},fn=[],hn=oe("Reflect","construct"),dn=/^\s*(?:class|function)\b/,pn=an(dn.exec),vn=!dn.exec(ln),gn=function(e){if(!sn(e))return!1;try{return hn(ln,fn,e),!0}catch(t){return!1}},yn=function(e){if(!sn(e))return!1;switch(un(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return vn||!!pn(dn,cn(e))}catch(t){return!0}};yn.sham=!0;var mn=!hn||on((function(){var e;return gn(gn.call)||!gn(Object)||!gn((function(){e=!0}))||e}))?yn:gn,_n=Lr,bn=mn,wn=ee,kn=ft("species"),Sn=Array,xn=function(e){var t;return _n(e)&&(t=e.constructor,(bn(t)&&(t===Sn||_n(t.prototype))||wn(t)&&null===(t=t[kn]))&&(t=void 0)),void 0===t?Sn:t},En=function(e,t){return new(xn(e))(0===t?0:t)},Tn=r,An=pe,On=ft("species"),Ln=function(e){return An>=51||!Tn((function(){var t=[];return(t.constructor={})[On]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Cn=Ar,Dn=r,Pn=Lr,Rn=ee,Un=Ye,In=jr,Mn=Nr,Bn=qr,jn=En,Fn=Ln,Nn=pe,Vn=ft("isConcatSpreadable"),zn=Nn>=51||!Dn((function(){var e=[];return e[Vn]=!1,e.concat()[0]!==e})),Gn=function(e){if(!Rn(e))return!1;var t=e[Vn];return void 0!==t?!!t:Pn(e)};Cn({target:"Array",proto:!0,arity:1,forced:!zn||!Fn("concat")},{concat:function(e){var t,r,n,i,a,o=Un(this),s=jn(o,0),u=0;for(t=-1,n=arguments.length;t<n;t++)if(Gn(a=-1===t?o:arguments[t]))for(i=In(a),Mn(u+i),r=0;r<i;r++,u++)r in a&&Bn(s,u,a[r]);else Mn(u+1),Bn(s,u++,a);return s.length=u,s}});var qn=te,Hn=function(e){return qn[e+"Prototype"]},Kn=Hn("Array").concat,Wn=c,Yn=Kn,Jn=Array.prototype,Qn=t((function(e){var t=e.concat;return e===Jn||Wn(Jn,e)&&t===Jn.concat?Yn:t})),Xn=Zr,$n=String,Zn=function(e){if("Symbol"===Xn(e))throw TypeError("Cannot convert a Symbol value to a string");return $n(e)},ei={},ti=Rr,ri=Math.max,ni=Math.min,ii=function(e,t){var r=ti(e);return r<0?ri(r+t,0):ni(r,t)},ai=X,oi=ii,si=jr,ui=function(e){return function(t,r,n){var i,a=ai(t),o=si(a),s=oi(n,o);if(e&&r!=r){for(;o>s;)if((i=a[s++])!=i)return!0}else for(;o>s;s++)if((e||s in a)&&a[s]===r)return e||s||0;return!e&&-1}},ci={includes:ui(!0),indexOf:ui(!1)},li={},fi=Xe,hi=X,di=ci.indexOf,pi=li,vi=u([].push),gi=function(e,t){var r,n=hi(e),i=0,a=[];for(r in n)!fi(pi,r)&&fi(n,r)&&vi(a,r);for(;t.length>i;)fi(n,r=t[i++])&&(~di(a,r)||vi(a,r));return a},yi=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],mi=gi,_i=yi,bi=Object.keys||function(e){return mi(e,_i)},wi=L,ki=Xt,Si=Qt,xi=tr,Ei=X,Ti=bi;ei.f=wi&&!ki?Object.defineProperties:function(e,t){xi(e);for(var r,n=Ei(t),i=Ti(t),a=i.length,o=0;a>o;)Si.f(e,r=i[o++],n[r]);return e};var Ai,Oi=oe("document","documentElement"),Li=rt,Ci=He("keys"),Di=function(e){return Ci[e]||(Ci[e]=Li(e))},Pi=tr,Ri=ei,Ui=yi,Ii=li,Mi=Oi,Bi=Et,ji="prototype",Fi="script",Ni=Di("IE_PROTO"),Vi=function(){},zi=function(e){return"<"+Fi+">"+e+"</"+Fi+">"},Gi=function(e){e.write(zi("")),e.close();var t=e.parentWindow.Object;return e=null,t},qi=function(){try{Ai=new ActiveXObject("htmlfile")}catch(i){}var e,t,r;qi="undefined"!=typeof document?document.domain&&Ai?Gi(Ai):(t=Bi("iframe"),r="java"+Fi+":",t.style.display="none",Mi.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(zi("document.F=Object")),e.close(),e.F):Gi(Ai);for(var n=Ui.length;n--;)delete qi[ji][Ui[n]];return qi()};Ii[Ni]=!0;var Hi=Object.create||function(e,t){var r;return null!==e?(Vi[ji]=Pi(e),r=new Vi,Vi[ji]=null,r[Ni]=e):r=qi(),void 0===t?r:Ri.f(r,t)},Ki={},Wi=gi,Yi=yi.concat("length","prototype");Ki.f=Object.getOwnPropertyNames||function(e){return Wi(e,Yi)};var Ji={},Qi=ii,Xi=jr,$i=qr,Zi=Array,ea=Math.max,ta=function(e,t,r){for(var n=Xi(e),i=Qi(t,n),a=Qi(void 0===r?n:r,n),o=Zi(ea(a-i,0)),s=0;i<a;i++,s++)$i(o,s,e[i]);return o.length=s,o},ra=b,na=X,ia=Ki.f,aa=ta,oa="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Ji.f=function(e){return oa&&"Window"==ra(e)?function(e){try{return ia(e)}catch(t){return aa(oa)}}(e):ia(na(e))};var sa={};sa.f=Object.getOwnPropertySymbols;var ua=vr,ca=function(e,t,r,n){return n&&n.enumerable?e[t]=r:ua(e,t,r),e},la=Qt,fa=function(e,t,r){return la.f(e,t,r)},ha={},da=ft;ha.f=da;var pa,va,ga,ya=te,ma=Xe,_a=ha,ba=Qt.f,wa=function(e){var t=ya.Symbol||(ya.Symbol={});ma(t,e)||ba(t,e,{value:_a.f(e)})},ka=P,Sa=oe,xa=ft,Ea=ca,Ta=function(){var e=Sa("Symbol"),t=e&&e.prototype,r=t&&t.valueOf,n=xa("toPrimitive");t&&!t[n]&&Ea(t,n,(function(e){return ka(r,this)}),{arity:1})},Aa=Zr,Oa=Kr?{}.toString:function(){return"[object "+Aa(this)+"]"},La=Kr,Ca=Qt.f,Da=vr,Pa=Xe,Ra=Oa,Ua=ft("toStringTag"),Ia=function(e,t,r,n){if(e){var i=r?e:e.prototype;Pa(i,Ua)||Ca(i,Ua,{configurable:!0,value:t}),n&&!La&&Da(i,"toString",Ra)}},Ma=A,Ba=f.WeakMap,ja=Ma(Ba)&&/native code/.test(String(Ba)),Fa=f,Na=ee,Va=vr,za=Xe,Ga=Ge,qa=Di,Ha=li,Ka="Object already initialized",Wa=Fa.TypeError,Ya=Fa.WeakMap;if(ja||Ga.state){var Ja=Ga.state||(Ga.state=new Ya);Ja.get=Ja.get,Ja.has=Ja.has,Ja.set=Ja.set,pa=function(e,t){if(Ja.has(e))throw Wa(Ka);return t.facade=e,Ja.set(e,t),t},va=function(e){return Ja.get(e)||{}},ga=function(e){return Ja.has(e)}}else{var Qa=qa("state");Ha[Qa]=!0,pa=function(e,t){if(za(e,Qa))throw Wa(Ka);return t.facade=e,Va(e,Qa,t),t},va=function(e){return za(e,Qa)?e[Qa]:{}},ga=function(e){return za(e,Qa)}}var Xa={set:pa,get:va,has:ga,enforce:function(e){return ga(e)?va(e):pa(e,{})},getterFor:function(e){return function(t){var r;if(!Na(t)||(r=va(t)).type!==e)throw Wa("Incompatible receiver, "+e+" required");return r}}},$a=Jt,Za=q,eo=Ye,to=jr,ro=En,no=u([].push),io=function(e){var t=1==e,r=2==e,n=3==e,i=4==e,a=6==e,o=7==e,s=5==e||a;return function(u,c,l,f){for(var h,d,p=eo(u),v=Za(p),g=$a(c,l),y=to(v),m=0,_=f||ro,b=t?_(u,y):r||o?_(u,0):void 0;y>m;m++)if((s||m in v)&&(d=g(h=v[m],m,p),e))if(t)b[m]=d;else if(d)switch(e){case 3:return!0;case 5:return h;case 6:return m;case 2:no(b,h)}else switch(e){case 4:return!1;case 7:no(b,h)}return a?-1:n||i?i:b}},ao={forEach:io(0),map:io(1),filter:io(2),some:io(3),every:io(4),find:io(5),findIndex:io(6),filterReject:io(7)},oo=Ar,so=f,uo=P,co=u,lo=L,fo=me,ho=r,po=Xe,vo=c,go=tr,yo=X,mo=wt,_o=Zn,bo=F,wo=Hi,ko=bi,So=Ki,xo=Ji,Eo=sa,To=O,Ao=Qt,Oo=ei,Lo=R,Co=ca,Do=fa,Po=He,Ro=li,Uo=rt,Io=ft,Mo=ha,Bo=wa,jo=Ta,Fo=Ia,No=Xa,Vo=ao.forEach,zo=Di("hidden"),Go="Symbol",qo="prototype",Ho=No.set,Ko=No.getterFor(Go),Wo=Object[qo],Yo=so.Symbol,Jo=Yo&&Yo[qo],Qo=so.TypeError,Xo=so.QObject,$o=To.f,Zo=Ao.f,es=xo.f,ts=Lo.f,rs=co([].push),ns=Po("symbols"),is=Po("op-symbols"),as=Po("wks"),os=!Xo||!Xo[qo]||!Xo[qo].findChild,ss=lo&&ho((function(){return 7!=wo(Zo({},"a",{get:function(){return Zo(this,"a",{value:7}).a}})).a}))?function(e,t,r){var n=$o(Wo,t);n&&delete Wo[t],Zo(e,t,r),n&&e!==Wo&&Zo(Wo,t,n)}:Zo,us=function(e,t){var r=ns[e]=wo(Jo);return Ho(r,{type:Go,tag:e,description:t}),lo||(r.description=t),r},cs=function(e,t,r){e===Wo&&cs(is,t,r),go(e);var n=mo(t);return go(r),po(ns,n)?(r.enumerable?(po(e,zo)&&e[zo][n]&&(e[zo][n]=!1),r=wo(r,{enumerable:bo(0,!1)})):(po(e,zo)||Zo(e,zo,bo(1,{})),e[zo][n]=!0),ss(e,n,r)):Zo(e,n,r)},ls=function(e,t){go(e);var r=yo(t),n=ko(r).concat(ps(r));return Vo(n,(function(t){lo&&!uo(fs,r,t)||cs(e,t,r[t])})),e},fs=function(e){var t=mo(e),r=uo(ts,this,t);return!(this===Wo&&po(ns,t)&&!po(is,t))&&(!(r||!po(this,t)||!po(ns,t)||po(this,zo)&&this[zo][t])||r)},hs=function(e,t){var r=yo(e),n=mo(t);if(r!==Wo||!po(ns,n)||po(is,n)){var i=$o(r,n);return!i||!po(ns,n)||po(r,zo)&&r[zo][n]||(i.enumerable=!0),i}},ds=function(e){var t=es(yo(e)),r=[];return Vo(t,(function(e){po(ns,e)||po(Ro,e)||rs(r,e)})),r},ps=function(e){var t=e===Wo,r=es(t?is:yo(e)),n=[];return Vo(r,(function(e){!po(ns,e)||t&&!po(Wo,e)||rs(n,ns[e])})),n};fo||(Yo=function(){if(vo(Jo,this))throw Qo("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?_o(arguments[0]):void 0,t=Uo(e),r=function(e){this===Wo&&uo(r,is,e),po(this,zo)&&po(this[zo],t)&&(this[zo][t]=!1),ss(this,t,bo(1,e))};return lo&&os&&ss(Wo,t,{configurable:!0,set:r}),us(t,e)},Co(Jo=Yo[qo],"toString",(function(){return Ko(this).tag})),Co(Yo,"withoutSetter",(function(e){return us(Uo(e),e)})),Lo.f=fs,Ao.f=cs,Oo.f=ls,To.f=hs,So.f=xo.f=ds,Eo.f=ps,Mo.f=function(e){return us(Io(e),e)},lo&&Do(Jo,"description",{configurable:!0,get:function(){return Ko(this).description}})),oo({global:!0,constructor:!0,wrap:!0,forced:!fo,sham:!fo},{Symbol:Yo}),Vo(ko(as),(function(e){Bo(e)})),oo({target:Go,stat:!0,forced:!fo},{useSetter:function(){os=!0},useSimple:function(){os=!1}}),oo({target:"Object",stat:!0,forced:!fo,sham:!lo},{create:function(e,t){return void 0===t?wo(e):ls(wo(e),t)},defineProperty:cs,defineProperties:ls,getOwnPropertyDescriptor:hs}),oo({target:"Object",stat:!0,forced:!fo},{getOwnPropertyNames:ds}),jo(),Fo(Yo,Go),Ro[zo]=!0;var vs=me&&!!Symbol.for&&!!Symbol.keyFor,gs=Ar,ys=oe,ms=Xe,_s=Zn,bs=He,ws=vs,ks=bs("string-to-symbol-registry"),Ss=bs("symbol-to-string-registry");gs({target:"Symbol",stat:!0,forced:!ws},{for:function(e){var t=_s(e);if(ms(ks,t))return ks[t];var r=ys("Symbol")(t);return ks[t]=r,Ss[r]=t,r}});var xs=Ar,Es=Xe,Ts=xe,As=Te,Os=vs,Ls=He("symbol-to-string-registry");xs({target:"Symbol",stat:!0,forced:!Os},{keyFor:function(e){if(!Ts(e))throw TypeError(As(e)+" is not a symbol");if(Es(Ls,e))return Ls[e]}});var Cs=u([].slice),Ds=Lr,Ps=A,Rs=b,Us=Zn,Is=u([].push),Ms=Ar,Bs=oe,js=g,Fs=P,Ns=u,Vs=r,zs=A,Gs=xe,qs=Cs,Hs=function(e){if(Ps(e))return e;if(Ds(e)){for(var t=e.length,r=[],n=0;n<t;n++){var i=e[n];"string"==typeof i?Is(r,i):"number"!=typeof i&&"Number"!=Rs(i)&&"String"!=Rs(i)||Is(r,Us(i))}var a=r.length,o=!0;return function(e,t){if(o)return o=!1,t;if(Ds(this))return t;for(var n=0;n<a;n++)if(r[n]===e)return t}}},Ks=me,Ws=String,Ys=Bs("JSON","stringify"),Js=Ns(/./.exec),Qs=Ns("".charAt),Xs=Ns("".charCodeAt),$s=Ns("".replace),Zs=Ns(1..toString),eu=/[\uD800-\uDFFF]/g,tu=/^[\uD800-\uDBFF]$/,ru=/^[\uDC00-\uDFFF]$/,nu=!Ks||Vs((function(){var e=Bs("Symbol")();return"[null]"!=Ys([e])||"{}"!=Ys({a:e})||"{}"!=Ys(Object(e))})),iu=Vs((function(){return'"\\udf06\\ud834"'!==Ys("\udf06\ud834")||'"\\udead"'!==Ys("\udead")})),au=function(e,t){var r=qs(arguments),n=Hs(t);if(zs(n)||void 0!==e&&!Gs(e))return r[1]=function(e,t){if(zs(n)&&(t=Fs(n,this,Ws(e),t)),!Gs(t))return t},js(Ys,null,r)},ou=function(e,t,r){var n=Qs(r,t-1),i=Qs(r,t+1);return Js(tu,e)&&!Js(ru,i)||Js(ru,e)&&!Js(tu,n)?"\\u"+Zs(Xs(e,0),16):e};Ys&&Ms({target:"JSON",stat:!0,arity:3,forced:nu||iu},{stringify:function(e,t,r){var n=qs(arguments),i=js(nu?au:Ys,null,n);return iu&&"string"==typeof i?$s(i,eu,ou):i}});var su=sa,uu=Ye;Ar({target:"Object",stat:!0,forced:!me||r((function(){su.f(1)}))},{getOwnPropertySymbols:function(e){var t=su.f;return t?t(uu(e)):[]}}),wa("asyncIterator"),wa("hasInstance"),wa("isConcatSpreadable"),wa("iterator"),wa("match"),wa("matchAll"),wa("replace"),wa("search"),wa("species"),wa("split");var cu=Ta;wa("toPrimitive"),cu();var lu=oe,fu=Ia;wa("toStringTag"),fu(lu("Symbol"),"Symbol"),wa("unscopables"),Ia(f.JSON,"JSON",!0);var hu,du,pu,vu=te.Symbol,gu={},yu=L,mu=Xe,_u=Function.prototype,bu=yu&&Object.getOwnPropertyDescriptor,wu=mu(_u,"name"),ku={EXISTS:wu,PROPER:wu&&"something"===function(){}.name,CONFIGURABLE:wu&&(!yu||yu&&bu(_u,"name").configurable)},Su=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),xu=Xe,Eu=A,Tu=Ye,Au=Su,Ou=Di("IE_PROTO"),Lu=Object,Cu=Lu.prototype,Du=Au?Lu.getPrototypeOf:function(e){var t=Tu(e);if(xu(t,Ou))return t[Ou];var r=t.constructor;return Eu(r)&&t instanceof r?r.prototype:t instanceof Lu?Cu:null},Pu=r,Ru=A,Uu=ee,Iu=Hi,Mu=Du,Bu=ca,ju=ft("iterator"),Fu=!1;[].keys&&("next"in(pu=[].keys())?(du=Mu(Mu(pu)))!==Object.prototype&&(hu=du):Fu=!0);var Nu=!Uu(hu)||Pu((function(){var e={};return hu[ju].call(e)!==e}));Ru((hu=Nu?{}:Iu(hu))[ju])||Bu(hu,ju,(function(){return this}));var Vu={IteratorPrototype:hu,BUGGY_SAFARI_ITERATORS:Fu},zu=Vu.IteratorPrototype,Gu=Hi,qu=F,Hu=Ia,Ku=gu,Wu=function(){return this},Yu=function(e,t,r,n){var i=t+" Iterator";return e.prototype=Gu(zu,{next:qu(+!n,r)}),Hu(e,i,!1,!0),Ku[i]=Wu,e},Ju=u,Qu=Ce,Xu=A,$u=String,Zu=TypeError,ec=function(e,t,r){try{return Ju(Qu(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(n){}},tc=tr,rc=function(e){if("object"==typeof e||Xu(e))return e;throw Zu("Can't set "+$u(e)+" as a prototype")},nc=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=ec(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(n){}return function(r,n){return tc(r),rc(n),t?e(r,n):r.__proto__=n,r}}():void 0),ic=Ar,ac=P,oc=ku,sc=Yu,uc=Du,cc=Ia,lc=ca,fc=gu,hc=Vu,dc=oc.PROPER,pc=hc.BUGGY_SAFARI_ITERATORS,vc=ft("iterator"),gc="keys",yc="values",mc="entries",_c=function(){return this},bc=function(e,t,r,n,i,a,o){sc(r,t,n);var s,u,c,l=function(e){if(e===i&&v)return v;if(!pc&&e in d)return d[e];switch(e){case gc:case yc:case mc:return function(){return new r(this,e)}}return function(){return new r(this)}},f=t+" Iterator",h=!1,d=e.prototype,p=d[vc]||d["@@iterator"]||i&&d[i],v=!pc&&p||l(i),g="Array"==t&&d.entries||p;if(g&&(s=uc(g.call(new e)))!==Object.prototype&&s.next&&(cc(s,f,!0,!0),fc[f]=_c),dc&&i==yc&&p&&p.name!==yc&&(h=!0,v=function(){return ac(p,this)}),i)if(u={values:l(yc),keys:a?v:l(gc),entries:l(mc)},o)for(c in u)(pc||h||!(c in d))&&lc(d,c,u[c]);else ic({target:t,proto:!0,forced:pc||h},u);return o&&d[vc]!==v&&lc(d,vc,v,{name:i}),fc[t]=v,u},wc=function(e,t){return{value:e,done:t}},kc=X,Sc=gu,xc=Xa;Qt.f;var Ec=bc,Tc=wc,Ac="Array Iterator",Oc=xc.set,Lc=xc.getterFor(Ac);Ec(Array,"Array",(function(e,t){Oc(this,{type:Ac,target:kc(e),index:0,kind:t})}),(function(){var e=Lc(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,Tc(void 0,!0)):Tc("keys"==r?n:"values"==r?t[n]:[n,t[n]],!1)}),"values"),Sc.Arguments=Sc.Array;var Cc={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Dc=f,Pc=Zr,Rc=vr,Uc=gu,Ic=ft("toStringTag");for(var Mc in Cc){var Bc=Dc[Mc],jc=Bc&&Bc.prototype;jc&&Pc(jc)!==Ic&&Rc(jc,Ic,Mc),Uc[Mc]=Uc.Array}var Fc=vu;wa("dispose");var Nc=Fc;wa("asyncDispose");var Vc=Ar,zc=u,Gc=oe("Symbol"),qc=Gc.keyFor,Hc=zc(Gc.prototype.valueOf);Vc({target:"Symbol",stat:!0},{isRegistered:function(e){try{return void 0!==qc(Hc(e))}catch(t){return!1}}});for(var Kc=Ar,Wc=He,Yc=oe,Jc=u,Qc=xe,Xc=ft,$c=Yc("Symbol"),Zc=$c.isWellKnown,el=Yc("Object","getOwnPropertyNames"),tl=Jc($c.prototype.valueOf),rl=Wc("wks"),nl=0,il=el($c),al=il.length;nl<al;nl++)try{var ol=il[nl];Qc($c[ol])&&Xc(ol)}catch(EM){}Kc({target:"Symbol",stat:!0,forced:!0},{isWellKnown:function(e){if(Zc&&Zc(e))return!0;try{for(var t=tl(e),r=0,n=el(rl),i=n.length;r<i;r++)if(rl[n[r]]==t)return!0}catch(EM){}return!1}}),wa("matcher"),wa("metadataKey"),wa("observable"),wa("metadata"),wa("patternMatch"),wa("replaceAll");var sl=Nc,ul=t(sl),cl=u,ll=Rr,fl=Zn,hl=Y,dl=cl("".charAt),pl=cl("".charCodeAt),vl=cl("".slice),gl=function(e){return function(t,r){var n,i,a=fl(hl(t)),o=ll(r),s=a.length;return o<0||o>=s?e?"":void 0:(n=pl(a,o))<55296||n>56319||o+1===s||(i=pl(a,o+1))<56320||i>57343?e?dl(a,o):n:e?vl(a,o,o+2):i-56320+(n-55296<<10)+65536}},yl={codeAt:gl(!1),charAt:gl(!0)},ml=yl.charAt,_l=Zn,bl=Xa,wl=bc,kl=wc,Sl="String Iterator",xl=bl.set,El=bl.getterFor(Sl);wl(String,"String",(function(e){xl(this,{type:Sl,string:_l(e),index:0})}),(function(){var e,t=El(this),r=t.string,n=t.index;return n>=r.length?kl(void 0,!0):(e=ml(r,n),t.index+=e.length,kl(e,!1))}));var Tl=ha.f("iterator"),Al=t(Tl);function Ol(e){return(Ol="function"==typeof ul&&"symbol"==typeof Al?function(e){return typeof e}:function(e){return e&&"function"==typeof ul&&e.constructor===ul&&e!==ul.prototype?"symbol":typeof e})(e)}var Ll=Ye,Cl=bi;Ar({target:"Object",stat:!0,forced:r((function(){Cl(1)}))},{keys:function(e){return Cl(Ll(e))}});var Dl=t(te.Object.keys),Pl=t(te.Object.getOwnPropertySymbols),Rl=ao.filter;Ar({target:"Array",proto:!0,forced:!Ln("filter")},{filter:function(e){return Rl(this,e,arguments.length>1?arguments[1]:void 0)}});var Ul=Hn("Array").filter,Il=c,Ml=Ul,Bl=Array.prototype,jl=t((function(e){var t=e.filter;return e===Bl||Il(Bl,e)&&t===Bl.filter?Ml:t})),Fl={exports:{}},Nl=Ar,Vl=r,zl=X,Gl=O.f,ql=L;Nl({target:"Object",stat:!0,forced:!ql||Vl((function(){Gl(1)})),sham:!ql},{getOwnPropertyDescriptor:function(e,t){return Gl(zl(e),t)}});var Hl=te.Object,Kl=Fl.exports=function(e,t){return Hl.getOwnPropertyDescriptor(e,t)};Hl.getOwnPropertyDescriptor.sham&&(Kl.sham=!0);var Wl=t(Fl.exports),Yl=r,Jl=function(e,t){var r=[][e];return!!r&&Yl((function(){r.call(null,t||function(){return 1},1)}))},Ql=ao.forEach,Xl=Jl("forEach")?[].forEach:function(e){return Ql(this,e,arguments.length>1?arguments[1]:void 0)};Ar({target:"Array",proto:!0,forced:[].forEach!=Xl},{forEach:Xl});var $l=Hn("Array").forEach,Zl=Zr,ef=Xe,tf=c,rf=$l,nf=Array.prototype,af={DOMTokenList:!0,NodeList:!0},of=t((function(e){var t=e.forEach;return e===nf||tf(nf,e)&&t===nf.forEach||ef(af,Zl(e))?rf:t})),sf=oe,uf=Ki,cf=sa,lf=tr,ff=u([].concat),hf=sf("Reflect","ownKeys")||function(e){var t=uf.f(lf(e)),r=cf.f;return r?ff(t,r(e)):t},df=hf,pf=X,vf=O,gf=qr;Ar({target:"Object",stat:!0,sham:!L},{getOwnPropertyDescriptors:function(e){for(var t,r,n=pf(e),i=vf.f,a=df(n),o={},s=0;a.length>s;)void 0!==(r=i(n,t=a[s++]))&&gf(o,t,r);return o}});var yf=t(te.Object.getOwnPropertyDescriptors),mf={exports:{}},_f=Ar,bf=L,wf=ei.f;_f({target:"Object",stat:!0,forced:Object.defineProperties!==wf,sham:!bf},{defineProperties:wf});var kf=te.Object,Sf=mf.exports=function(e,t){return kf.defineProperties(e,t)};kf.defineProperties.sham&&(Sf.sham=!0);var xf=t(mf.exports),Ef={exports:{}},Tf=Ar,Af=L,Of=Qt.f;Tf({target:"Object",stat:!0,forced:Object.defineProperty!==Of,sham:!Af},{defineProperty:Of});var Lf=te.Object,Cf=Ef.exports=function(e,t,r){return Lf.defineProperty(e,t,r)};Lf.defineProperty.sham&&(Cf.sham=!0);var Df=t(Ef.exports),Pf=t(sl);Ar({target:"Object",stat:!0,sham:!L},{create:Hi});var Rf=te.Object,Uf=t((function(e,t){return Rf.create(e,t)})),If=Ye,Mf=Du,Bf=Su;Ar({target:"Object",stat:!0,forced:r((function(){Mf(1)})),sham:!Bf},{getPrototypeOf:function(e){return Mf(If(e))}});var jf=t(te.Object.getPrototypeOf);Ar({target:"Object",stat:!0},{setPrototypeOf:nc});var Ff=t(te.Object.setPrototypeOf),Nf=Xe,Vf=hf,zf=O,Gf=Qt,qf=ee,Hf=vr,Kf=Error,Wf=u("".replace),Yf=String(Kf("zxcasd").stack),Jf=/\n\s*at [^:]*:[^\n]*/,Qf=Jf.test(Yf),Xf=F,$f=!r((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",Xf(1,7)),7!==e.stack)})),Zf=vr,eh=function(e,t){if(Qf&&"string"==typeof e&&!Kf.prepareStackTrace)for(;t--;)e=Wf(e,Jf,"");return e},th=$f,rh=Error.captureStackTrace,nh=gu,ih=ft("iterator"),ah=Array.prototype,oh=function(e){return void 0!==e&&(nh.Array===e||ah[ih]===e)},sh=Zr,uh=Re,ch=H,lh=gu,fh=ft("iterator"),hh=function(e){if(!ch(e))return uh(e,fh)||uh(e,"@@iterator")||lh[sh(e)]},dh=P,ph=Ce,vh=tr,gh=Te,yh=hh,mh=TypeError,_h=function(e,t){var r=arguments.length<2?yh(e):t;if(ph(r))return vh(dh(r,e));throw mh(gh(e)+" is not iterable")},bh=P,wh=tr,kh=Re,Sh=function(e,t,r){var n,i;wh(e);try{if(!(n=kh(e,"return"))){if("throw"===t)throw r;return r}n=bh(n,e)}catch(EM){i=!0,n=EM}if("throw"===t)throw r;if(i)throw n;return wh(n),r},xh=Jt,Eh=P,Th=tr,Ah=Te,Oh=oh,Lh=jr,Ch=c,Dh=_h,Ph=hh,Rh=Sh,Uh=TypeError,Ih=function(e,t){this.stopped=e,this.result=t},Mh=Ih.prototype,Bh=function(e,t,r){var n,i,a,o,s,u,c,l=r&&r.that,f=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_RECORD),d=!(!r||!r.IS_ITERATOR),p=!(!r||!r.INTERRUPTED),v=xh(t,l),g=function(e){return n&&Rh(n,"normal",e),new Ih(!0,e)},y=function(e){return f?(Th(e),p?v(e[0],e[1],g):v(e[0],e[1])):p?v(e,g):v(e)};if(h)n=e.iterator;else if(d)n=e;else{if(!(i=Ph(e)))throw Uh(Ah(e)+" is not iterable");if(Oh(i)){for(a=0,o=Lh(e);o>a;a++)if((s=y(e[a]))&&Ch(Mh,s))return s;return new Ih(!1)}n=Dh(e,i)}for(u=h?e.next:n.next;!(c=Eh(u,n)).done;){try{s=y(c.value)}catch(EM){Rh(n,"throw",EM)}if("object"==typeof s&&s&&Ch(Mh,s))return s}return new Ih(!1)},jh=Zn,Fh=Ar,Nh=c,Vh=Du,zh=nc,Gh=function(e,t,r){for(var n=Vf(t),i=Gf.f,a=zf.f,o=0;o<n.length;o++){var s=n[o];Nf(e,s)||r&&Nf(r,s)||i(e,s,a(t,s))}},qh=Hi,Hh=vr,Kh=F,Wh=function(e,t){qf(t)&&"cause"in t&&Hf(e,"cause",t.cause)},Yh=function(e,t,r,n){th&&(rh?rh(e,t):Zf(e,"stack",eh(r,n)))},Jh=Bh,Qh=function(e,t){return void 0===e?arguments.length<2?"":t:jh(e)},Xh=ft("toStringTag"),$h=Error,Zh=[].push,ed=function(e,t){var r,n=Nh(td,this);zh?r=zh($h(),n?Vh(this):td):(r=n?this:qh(td),Hh(r,Xh,"Error")),void 0!==t&&Hh(r,"message",Qh(t)),Yh(r,ed,r.stack,1),arguments.length>2&&Wh(r,arguments[2]);var i=[];return Jh(e,Zh,{that:i}),Hh(r,"errors",i),r};zh?zh(ed,$h):Gh(ed,$h,{name:!0});var td=ed.prototype=qh($h.prototype,{constructor:Kh(1,ed),message:Kh(1,""),name:Kh(1,"AggregateError")});Fh({global:!0,constructor:!0,arity:2},{AggregateError:ed});var rd,nd,id,ad,od="undefined"!=typeof process&&"process"==b(process),sd=oe,ud=fa,cd=L,ld=ft("species"),fd=function(e){var t=sd(e);cd&&t&&!t[ld]&&ud(t,ld,{configurable:!0,get:function(){return this}})},hd=c,dd=TypeError,pd=function(e,t){if(hd(t,e))return e;throw dd("Incorrect invocation")},vd=mn,gd=Te,yd=TypeError,md=function(e){if(vd(e))return e;throw yd(gd(e)+" is not a constructor")},_d=tr,bd=md,wd=H,kd=ft("species"),Sd=function(e,t){var r,n=_d(e).constructor;return void 0===n||wd(r=_d(n)[kd])?t:bd(r)},xd=TypeError,Ed=function(e,t){if(e<t)throw xd("Not enough arguments");return e},Td=/(?:ipad|iphone|ipod).*applewebkit/i.test(se),Ad=f,Od=g,Ld=Jt,Cd=A,Dd=Xe,Pd=r,Rd=Oi,Ud=Cs,Id=Et,Md=Ed,Bd=Td,jd=od,Fd=Ad.setImmediate,Nd=Ad.clearImmediate,Vd=Ad.process,zd=Ad.Dispatch,Gd=Ad.Function,qd=Ad.MessageChannel,Hd=Ad.String,Kd=0,Wd={},Yd="onreadystatechange";Pd((function(){rd=Ad.location}));var Jd=function(e){if(Dd(Wd,e)){var t=Wd[e];delete Wd[e],t()}},Qd=function(e){return function(){Jd(e)}},Xd=function(e){Jd(e.data)},$d=function(e){Ad.postMessage(Hd(e),rd.protocol+"//"+rd.host)};Fd&&Nd||(Fd=function(e){Md(arguments.length,1);var t=Cd(e)?e:Gd(e),r=Ud(arguments,1);return Wd[++Kd]=function(){Od(t,void 0,r)},nd(Kd),Kd},Nd=function(e){delete Wd[e]},jd?nd=function(e){Vd.nextTick(Qd(e))}:zd&&zd.now?nd=function(e){zd.now(Qd(e))}:qd&&!Bd?(ad=(id=new qd).port2,id.port1.onmessage=Xd,nd=Ld(ad.postMessage,ad)):Ad.addEventListener&&Cd(Ad.postMessage)&&!Ad.importScripts&&rd&&"file:"!==rd.protocol&&!Pd($d)?(nd=$d,Ad.addEventListener("message",Xd,!1)):nd=Yd in Id("script")?function(e){Rd.appendChild(Id("script"))[Yd]=function(){Rd.removeChild(this),Jd(e)}}:function(e){setTimeout(Qd(e),0)});var Zd={set:Fd,clear:Nd},ep=function(){this.head=null,this.tail=null};ep.prototype={add:function(e){var t={item:e,next:null},r=this.tail;r?r.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}};var tp,rp,np,ip,ap,op=ep,sp=/ipad|iphone|ipod/i.test(se)&&"undefined"!=typeof Pebble,up=/web0s(?!.*chrome)/i.test(se),cp=f,lp=Jt,fp=O.f,hp=Zd.set,dp=op,pp=Td,vp=sp,gp=up,yp=od,mp=cp.MutationObserver||cp.WebKitMutationObserver,_p=cp.document,bp=cp.process,wp=cp.Promise,kp=fp(cp,"queueMicrotask"),Sp=kp&&kp.value;if(!Sp){var xp=new dp,Ep=function(){var e,t;for(yp&&(e=bp.domain)&&e.exit();t=xp.get();)try{t()}catch(EM){throw xp.head&&tp(),EM}e&&e.enter()};pp||yp||gp||!mp||!_p?!vp&&wp&&wp.resolve?((ip=wp.resolve(void 0)).constructor=wp,ap=lp(ip.then,ip),tp=function(){ap(Ep)}):yp?tp=function(){bp.nextTick(Ep)}:(hp=lp(hp,cp),tp=function(){hp(Ep)}):(rp=!0,np=_p.createTextNode(""),new mp(Ep).observe(np,{characterData:!0}),tp=function(){np.data=rp=!rp}),Sp=function(e){xp.head||tp(),xp.add(e)}}var Tp=Sp,Ap=function(e){try{return{error:!1,value:e()}}catch(EM){return{error:!0,value:EM}}},Op=f.Promise,Lp="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,Cp=!Lp&&!od&&"object"==typeof window&&"object"==typeof document,Dp=f,Pp=Op,Rp=A,Up=Ht,Ip=nn,Mp=ft,Bp=Cp,jp=Lp,Fp=pe,Np=Pp&&Pp.prototype,Vp=Mp("species"),zp=!1,Gp=Rp(Dp.PromiseRejectionEvent),qp={CONSTRUCTOR:Up("Promise",(function(){var e=Ip(Pp),t=e!==String(Pp);if(!t&&66===Fp)return!0;if(!Np.catch||!Np.finally)return!0;if(!Fp||Fp<51||!/native code/.test(e)){var r=new Pp((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};if((r.constructor={})[Vp]=n,!(zp=r.then((function(){}))instanceof n))return!0}return!t&&(Bp||jp)&&!Gp})),REJECTION_EVENT:Gp,SUBCLASSING:zp},Hp={},Kp=Ce,Wp=TypeError,Yp=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw Wp("Bad Promise constructor");t=e,r=n})),this.resolve=Kp(t),this.reject=Kp(r)};Hp.f=function(e){return new Yp(e)};var Jp,Qp,Xp=Ar,$p=od,Zp=f,ev=P,tv=ca,rv=Ia,nv=fd,iv=Ce,av=A,ov=ee,sv=pd,uv=Sd,cv=Zd.set,lv=Tp,fv=function(e,t){try{1==arguments.length?console.error(e):console.error(e,t)}catch(EM){}},hv=Ap,dv=op,pv=Xa,vv=Op,gv=qp,yv=Hp,mv="Promise",_v=gv.CONSTRUCTOR,bv=gv.REJECTION_EVENT,wv=pv.getterFor(mv),kv=pv.set,Sv=vv&&vv.prototype,xv=vv,Ev=Sv,Tv=Zp.TypeError,Av=Zp.document,Ov=Zp.process,Lv=yv.f,Cv=Lv,Dv=!!(Av&&Av.createEvent&&Zp.dispatchEvent),Pv="unhandledrejection",Rv=function(e){var t;return!(!ov(e)||!av(t=e.then))&&t},Uv=function(e,t){var r,n,i,a=t.value,o=1==t.state,s=o?e.ok:e.fail,u=e.resolve,c=e.reject,l=e.domain;try{s?(o||(2===t.rejection&&Fv(t),t.rejection=1),!0===s?r=a:(l&&l.enter(),r=s(a),l&&(l.exit(),i=!0)),r===e.promise?c(Tv("Promise-chain cycle")):(n=Rv(r))?ev(n,r,u,c):u(r)):c(a)}catch(EM){l&&!i&&l.exit(),c(EM)}},Iv=function(e,t){e.notified||(e.notified=!0,lv((function(){for(var r,n=e.reactions;r=n.get();)Uv(r,e);e.notified=!1,t&&!e.rejection&&Bv(e)})))},Mv=function(e,t,r){var n,i;Dv?((n=Av.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),Zp.dispatchEvent(n)):n={promise:t,reason:r},!bv&&(i=Zp["on"+e])?i(n):e===Pv&&fv("Unhandled promise rejection",r)},Bv=function(e){ev(cv,Zp,(function(){var t,r=e.facade,n=e.value;if(jv(e)&&(t=hv((function(){$p?Ov.emit("unhandledRejection",n,r):Mv(Pv,r,n)})),e.rejection=$p||jv(e)?2:1,t.error))throw t.value}))},jv=function(e){return 1!==e.rejection&&!e.parent},Fv=function(e){ev(cv,Zp,(function(){var t=e.facade;$p?Ov.emit("rejectionHandled",t):Mv("rejectionhandled",t,e.value)}))},Nv=function(e,t,r){return function(n){e(t,n,r)}},Vv=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,Iv(e,!0))},zv=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw Tv("Promise can't be resolved itself");var n=Rv(t);n?lv((function(){var r={done:!1};try{ev(n,t,Nv(zv,r,e),Nv(Vv,r,e))}catch(EM){Vv(r,EM,e)}})):(e.value=t,e.state=1,Iv(e,!1))}catch(EM){Vv({done:!1},EM,e)}}};_v&&(Ev=(xv=function(e){sv(this,Ev),iv(e),ev(Jp,this);var t=wv(this);try{e(Nv(zv,t),Nv(Vv,t))}catch(EM){Vv(t,EM)}}).prototype,(Jp=function(e){kv(this,{type:mv,done:!1,notified:!1,parent:!1,reactions:new dv,rejection:!1,state:0,value:void 0})}).prototype=tv(Ev,"then",(function(e,t){var r=wv(this),n=Lv(uv(this,xv));return r.parent=!0,n.ok=!av(e)||e,n.fail=av(t)&&t,n.domain=$p?Ov.domain:void 0,0==r.state?r.reactions.add(n):lv((function(){Uv(n,r)})),n.promise})),Qp=function(){var e=new Jp,t=wv(e);this.promise=e,this.resolve=Nv(zv,t),this.reject=Nv(Vv,t)},yv.f=Lv=function(e){return e===xv||undefined===e?new Qp(e):Cv(e)}),Xp({global:!0,constructor:!0,wrap:!0,forced:_v},{Promise:xv}),rv(xv,mv,!1,!0),nv(mv);var Gv=ft("iterator"),qv=!1;try{var Hv=0,Kv={next:function(){return{done:!!Hv++}},return:function(){qv=!0}};Kv[Gv]=function(){return this},Array.from(Kv,(function(){throw 2}))}catch(EM){}var Wv=function(e,t){if(!t&&!qv)return!1;var r=!1;try{var n={};n[Gv]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(EM){}return r},Yv=Op,Jv=qp.CONSTRUCTOR||!Wv((function(e){Yv.all(e).then(void 0,(function(){}))})),Qv=P,Xv=Ce,$v=Hp,Zv=Ap,eg=Bh;Ar({target:"Promise",stat:!0,forced:Jv},{all:function(e){var t=this,r=$v.f(t),n=r.resolve,i=r.reject,a=Zv((function(){var r=Xv(t.resolve),a=[],o=0,s=1;eg(e,(function(e){var u=o++,c=!1;s++,Qv(r,t,e).then((function(e){c||(c=!0,a[u]=e,--s||n(a))}),i)})),--s||n(a)}));return a.error&&i(a.value),r.promise}});var tg=Ar,rg=qp.CONSTRUCTOR;Op&&Op.prototype,tg({target:"Promise",proto:!0,forced:rg,real:!0},{catch:function(e){return this.then(void 0,e)}});var ng=P,ig=Ce,ag=Hp,og=Ap,sg=Bh;Ar({target:"Promise",stat:!0,forced:Jv},{race:function(e){var t=this,r=ag.f(t),n=r.reject,i=og((function(){var i=ig(t.resolve);sg(e,(function(e){ng(i,t,e).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}});var ug=P,cg=Hp;Ar({target:"Promise",stat:!0,forced:qp.CONSTRUCTOR},{reject:function(e){var t=cg.f(this);return ug(t.reject,void 0,e),t.promise}});var lg=tr,fg=ee,hg=Hp,dg=function(e,t){if(lg(e),fg(t)&&t.constructor===e)return t;var r=hg.f(e);return(0,r.resolve)(t),r.promise},pg=Ar,vg=Op,gg=qp.CONSTRUCTOR,yg=dg,mg=oe("Promise"),_g=!gg;pg({target:"Promise",stat:!0,forced:true},{resolve:function(e){return yg(_g&&this===mg?vg:this,e)}});var bg=P,wg=Ce,kg=Hp,Sg=Ap,xg=Bh;Ar({target:"Promise",stat:!0,forced:Jv},{allSettled:function(e){var t=this,r=kg.f(t),n=r.resolve,i=r.reject,a=Sg((function(){var r=wg(t.resolve),i=[],a=0,o=1;xg(e,(function(e){var s=a++,u=!1;o++,bg(r,t,e).then((function(e){u||(u=!0,i[s]={status:"fulfilled",value:e},--o||n(i))}),(function(e){u||(u=!0,i[s]={status:"rejected",reason:e},--o||n(i))}))})),--o||n(i)}));return a.error&&i(a.value),r.promise}});var Eg=P,Tg=Ce,Ag=oe,Og=Hp,Lg=Ap,Cg=Bh,Dg="No one promise resolved";Ar({target:"Promise",stat:!0,forced:Jv},{any:function(e){var t=this,r=Ag("AggregateError"),n=Og.f(t),i=n.resolve,a=n.reject,o=Lg((function(){var n=Tg(t.resolve),o=[],s=0,u=1,c=!1;Cg(e,(function(e){var l=s++,f=!1;u++,Eg(n,t,e).then((function(e){f||c||(c=!0,i(e))}),(function(e){f||c||(f=!0,o[l]=e,--u||a(new r(o,Dg)))}))})),--u||a(new r(o,Dg))}));return o.error&&a(o.value),n.promise}});var Pg=Ar,Rg=Op,Ug=r,Ig=oe,Mg=A,Bg=Sd,jg=dg,Fg=Rg&&Rg.prototype;Pg({target:"Promise",proto:!0,real:!0,forced:!!Rg&&Ug((function(){Fg.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=Bg(this,Ig("Promise")),r=Mg(e);return this.then(r?function(r){return jg(t,e()).then((function(){return r}))}:e,r?function(r){return jg(t,e()).then((function(){throw r}))}:e)}});var Ng=te.Promise,Vg=Hp,zg=Ap;Ar({target:"Promise",stat:!0,forced:!0},{try:function(e){var t=Vg.f(this),r=zg(e);return(r.error?t.reject:t.resolve)(r.value),t.promise}});var Gg=t(Ng),qg=Ar,Hg=Lr,Kg=u([].reverse),Wg=[1,2];qg({target:"Array",proto:!0,forced:String(Wg)===String(Wg.reverse())},{reverse:function(){return Hg(this)&&(this.length=this.length),Kg(this)}});var Yg=Hn("Array").reverse,Jg=c,Qg=Yg,Xg=Array.prototype,$g=t((function(e){var t=e.reverse;return e===Xg||Jg(Xg,e)&&t===Xg.reverse?Qg:t})),Zg=Ar,ey=Lr,ty=mn,ry=ee,ny=ii,iy=jr,ay=X,oy=qr,sy=ft,uy=Cs,cy=Ln("slice"),ly=sy("species"),fy=Array,hy=Math.max;Zg({target:"Array",proto:!0,forced:!cy},{slice:function(e,t){var r,n,i,a=ay(this),o=iy(a),s=ny(e,o),u=ny(void 0===t?o:t,o);if(ey(a)&&(r=a.constructor,(ty(r)&&(r===fy||ey(r.prototype))||ry(r)&&null===(r=r[ly]))&&(r=void 0),r===fy||void 0===r))return uy(a,s,u);for(n=new(void 0===r?fy:r)(hy(u-s,0)),i=0;s<u;s++,i++)s in a&&oy(n,i,a[s]);return n.length=i,n}});var dy=Hn("Array").slice,py=c,vy=dy,gy=Array.prototype,yy=t((function(e){var t=e.slice;return e===gy||py(gy,e)&&t===gy.slice?vy:t})),my=u,_y=Ce,by=ee,wy=Xe,ky=Cs,Sy=n,xy=Function,Ey=my([].concat),Ty=my([].join),Ay={},Oy=Sy?xy.bind:function(e){var t=_y(this),r=t.prototype,n=ky(arguments,1),i=function(){var r=Ey(n,ky(arguments));return this instanceof i?function(e,t,r){if(!wy(Ay,t)){for(var n=[],i=0;i<t;i++)n[i]="a["+i+"]";Ay[t]=xy("C,a","return new C("+Ty(n,",")+")")}return Ay[t](e,r)}(t,r.length,r):t.apply(e,r)};return by(r)&&(i.prototype=r),i},Ly=Oy;Ar({target:"Function",proto:!0,forced:Function.bind!==Ly},{bind:Ly});var Cy=Hn("Function").bind,Dy=c,Py=Cy,Ry=Function.prototype,Uy=t((function(e){var t=e.bind;return e===Ry||Dy(Ry,e)&&t===Ry.bind?Py:t})),Iy=Ar,My=g,By=Oy,jy=md,Fy=tr,Ny=ee,Vy=Hi,zy=r,Gy=oe("Reflect","construct"),qy=Object.prototype,Hy=[].push,Ky=zy((function(){function e(){}return!(Gy((function(){}),[],e)instanceof e)})),Wy=!zy((function(){Gy((function(){}))})),Yy=Ky||Wy;Iy({target:"Reflect",stat:!0,forced:Yy,sham:Yy},{construct:function(e,t){jy(e),Fy(t);var r=arguments.length<3?e:jy(arguments[2]);if(Wy&&!Ky)return Gy(e,t,r);if(e==r){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var n=[null];return My(Hy,n,t),new(My(By,e,n))}var i=r.prototype,a=Vy(Ny(i)?i:qy),o=My(e,a,t);return Ny(o)?o:a}});var Jy=t(te.Reflect.construct),Qy=t(ha.f("toPrimitive"));function Xy(e,t){var r=Dl(e);if(Pl){var n=Pl(e);t&&(n=jl(n).call(n,(function(t){return Wl(e,t).enumerable}))),r.push.apply(r,n)}return r}function $y(e){for(var t=1;t<arguments.length;t++){var r,n,i=null!=arguments[t]?arguments[t]:{};t%2?of(r=Xy(Object(i),!0)).call(r,(function(t){am(e,t,i[t])})):yf?xf(e,yf(i)):of(n=Xy(Object(i))).call(n,(function(t){Df(e,t,Wl(i,t))}))}return e}function Zy(){Zy=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Df||function(e,t,r){e[t]=r.value},i="function"==typeof Pf?Pf:{},a=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,r){return Df(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(A){u=function(e,t,r){return e[t]=r}}function c(e,t,r,i){var a=t&&t.prototype instanceof h?t:h,o=Uf(a.prototype),s=new x(i||[]);return n(o,"_invoke",{value:b(e,r,s)}),o}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(A){return{type:"throw",arg:A}}}e.wrap=c;var f={};function h(){}function d(){}function p(){}var v={};u(v,a,(function(){return this}));var g=jf&&jf(jf(E([])));g&&g!==t&&r.call(g,a)&&(v=g);var y=p.prototype=h.prototype=Uf(v);function m(e){var t;of(t=["next","throw","return"]).call(t,(function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function i(n,a,o,s){var u=l(e[n],e,a);if("throw"!==u.type){var c=u.arg,f=c.value;return f&&"object"==Ol(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,o,s)}),(function(e){i("throw",e,o,s)})):t.resolve(f).then((function(e){c.value=e,o(c)}),(function(e){return i("throw",e,o,s)}))}s(u.arg)}var a;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return a=a?a.then(n,n):n()}})}function b(e,t,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return{value:void 0,done:!0}}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=w(o,r);if(s){if(s===f)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=l(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===f)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function w(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var i=l(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,f;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],of(e).call(e,k,this),this.reset(!0)}function E(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:T}}function T(){return{value:void 0,done:!0}}return d.prototype=p,n(y,"constructor",{value:p,configurable:!0}),n(p,"constructor",{value:d,configurable:!0}),d.displayName=u(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Ff?Ff(e,p):(e.__proto__=p,u(e,s,"GeneratorFunction")),e.prototype=Uf(y),e},e.awrap=function(e){return{__await:e}},m(_.prototype),u(_.prototype,o,(function(){return this})),e.AsyncIterator=_,e.async=function(t,r,n,i,a){void 0===a&&(a=Gg);var o=new _(c(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},m(y),u(y,s,"Generator"),u(y,a,(function(){return this})),u(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return $g(r).call(r),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=E,x.prototype={constructor:x,reset:function(e){var t;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,of(t=this.tryEntries).call(t,S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+yy(n).call(n,1))&&(this[n]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return o.type="throw",o.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,f):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;S(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:E(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},e}function em(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(EM){return void r(EM)}s.done?t(u):Gg.resolve(u).then(n,i)}function tm(e){return function(){var t=this,r=arguments;return new Gg((function(n,i){var a=e.apply(t,r);function o(e){em(a,n,i,o,s,"next",e)}function s(e){em(a,n,i,o,s,"throw",e)}o(void 0)}))}}function rm(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Df(e,fm(n.key),n)}}function im(e,t,r){return t&&nm(e.prototype,t),r&&nm(e,r),Df(e,"prototype",{writable:!1}),e}function am(e,t,r){return(t=fm(t))in e?Df(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function om(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Uf(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Df(e,"prototype",{writable:!1}),t&&um(e,t)}function sm(e){var t;return(sm=Ff?Uy(t=jf).call(t):function(e){return e.__proto__||jf(e)})(e)}function um(e,t){var r;return(um=Ff?Uy(r=Ff).call(r):function(e,t){return e.__proto__=t,e})(e,t)}function cm(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lm(e){var t=function(){if("undefined"==typeof Reflect||!Jy)return!1;if(Jy.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Jy(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=sm(e);if(t){var i=sm(this).constructor;r=Jy(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Ol(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return cm(e)}(this,r)}}function fm(e){var t=function(e,t){if("object"!==Ol(e)||null===e)return e;var r=e[Qy];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ol(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ol(t)?t:String(t)}var hm=Ar,dm=ci.indexOf,pm=Jl,vm=S([].indexOf),gm=!!vm&&1/vm([1],1,-0)<0;hm({target:"Array",proto:!0,forced:gm||!pm("indexOf")},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return gm?vm(this,e,t)||0:dm(this,e,t)}});var ym=Hn("Array").indexOf,mm=c,_m=ym,bm=Array.prototype,wm=t((function(e){var t=e.indexOf;return e===bm||mm(bm,e)&&t===bm.indexOf?_m:t})),km=Ar,Sm=ao.find,xm="find",Em=!0;xm in[]&&Array(1)[xm]((function(){Em=!1})),km({target:"Array",proto:!0,forced:Em},{find:function(e){return Sm(this,e,arguments.length>1?arguments[1]:void 0)}});var Tm=Hn("Array").find,Am=c,Om=Tm,Lm=Array.prototype,Cm=t((function(e){var t=e.find;return e===Lm||Am(Lm,e)&&t===Lm.find?Om:t})),Dm=L,Pm=Lr,Rm=TypeError,Um=Object.getOwnPropertyDescriptor,Im=Dm&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(EM){return EM instanceof TypeError}}(),Mm=Te,Bm=TypeError,jm=function(e,t){if(!delete e[t])throw Bm("Cannot delete property "+Mm(t)+" of "+Mm(e))},Fm=Ar,Nm=Ye,Vm=ii,zm=Rr,Gm=jr,qm=Im?function(e,t){if(Pm(e)&&!Um(e,"length").writable)throw Rm("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t},Hm=Nr,Km=En,Wm=qr,Ym=jm,Jm=Ln("splice"),Qm=Math.max,Xm=Math.min;Fm({target:"Array",proto:!0,forced:!Jm},{splice:function(e,t){var r,n,i,a,o,s,u=Nm(this),c=Gm(u),l=Vm(e,c),f=arguments.length;for(0===f?r=n=0:1===f?(r=0,n=c-l):(r=f-2,n=Xm(Qm(zm(t),0),c-l)),Hm(c+r-n),i=Km(u,n),a=0;a<n;a++)(o=l+a)in u&&Wm(i,a,u[o]);if(i.length=n,r<n){for(a=l;a<c-n;a++)s=a+r,(o=a+n)in u?u[s]=u[o]:Ym(u,s);for(a=c;a>c-n+r;a--)Ym(u,a-1)}else if(r>n)for(a=c-n;a>l;a--)s=a+r-1,(o=a+n-1)in u?u[s]=u[o]:Ym(u,s);for(a=0;a<r;a++)u[a+l]=arguments[a+2];return qm(u,c-n+r),i}});var $m=Hn("Array").splice,Zm=c,e_=$m,t_=Array.prototype,r_=t((function(e){var t=e.splice;return e===t_||Zm(t_,e)&&t===t_.splice?e_:t}));Ar({target:"Number",stat:!0},{isNaN:function(e){return e!=e}});var n_=t(te.Number.isNaN),i_="\t\n\v\f\r                　\u2028\u2029\ufeff",a_=Y,o_=Zn,s_=i_,u_=u("".replace),c_=RegExp("^["+s_+"]+"),l_=RegExp("(^|[^"+s_+"])["+s_+"]+$"),f_=function(e){return function(t){var r=o_(a_(t));return 1&e&&(r=u_(r,c_,"")),2&e&&(r=u_(r,l_,"$1")),r}},h_={start:f_(1),end:f_(2),trim:f_(3)},d_=f,p_=r,v_=u,g_=Zn,y_=h_.trim,m_=i_,__=d_.parseInt,b_=d_.Symbol,w_=b_&&b_.iterator,k_=/^[+-]?0x/i,S_=v_(k_.exec),x_=8!==__(m_+"08")||22!==__(m_+"0x16")||w_&&!p_((function(){__(Object(w_))}))?function(e,t){var r=y_(g_(e));return __(r,t>>>0||(S_(k_,r)?16:10))}:__;Ar({global:!0,forced:parseInt!=x_},{parseInt:x_});var E_=t(te.parseInt),T_=ao.map;Ar({target:"Array",proto:!0,forced:!Ln("map")},{map:function(e){return T_(this,e,arguments.length>1?arguments[1]:void 0)}});var A_=Hn("Array").map,O_=c,L_=A_,C_=Array.prototype,D_=t((function(e){var t=e.map;return e===C_||O_(C_,e)&&t===C_.map?L_:t}));Ar({target:"Array",stat:!0},{isArray:Lr});var P_=t(te.Array.isArray),R_=t(Tl),U_=Xe,I_=P,M_=ee,B_=tr,j_=function(e){return void 0!==e&&(U_(e,"value")||U_(e,"writable"))},F_=O,N_=Du;Ar({target:"Reflect",stat:!0},{get:function e(t,r){var n,i,a=arguments.length<3?t:arguments[2];return B_(t)===a?t[r]:(n=F_.f(t,r))?j_(n)?n.value:void 0===n.get?void 0:I_(n.get,a):M_(i=N_(t))?e(i,r,a):void 0}});var V_=t(te.Reflect.get),z_=t(hh),G_=tr,q_=Sh,H_=Jt,K_=P,W_=Ye,Y_=function(e,t,r,n){try{return n?t(G_(r)[0],r[1]):t(r)}catch(EM){q_(e,"throw",EM)}},J_=oh,Q_=mn,X_=jr,$_=qr,Z_=_h,eb=hh,tb=Array,rb=function(e){var t=W_(e),r=Q_(this),n=arguments.length,i=n>1?arguments[1]:void 0,a=void 0!==i;a&&(i=H_(i,n>2?arguments[2]:void 0));var o,s,u,c,l,f,h=eb(t),d=0;if(!h||this===tb&&J_(h))for(o=X_(t),s=r?new this(o):tb(o);o>d;d++)f=a?i(t[d],d):t[d],$_(s,d,f);else for(l=(c=Z_(t,h)).next,s=r?new this:[];!(u=K_(l,c)).done;d++)f=a?Y_(c,i,[u.value,d],!0):u.value,$_(s,d,f);return s.length=d,s},nb=rb;Ar({target:"Array",stat:!0,forced:!Wv((function(e){Array.from(e)}))},{from:nb});var ib=t(te.Array.from);function ab(e,t){var r=Dl(e);if(Pl){var n=Pl(e);t&&(n=jl(n).call(n,(function(t){return Wl(e,t).enumerable}))),r.push.apply(r,n)}return r}function ob(e){for(var t=1;t<arguments.length;t++){var r,n,i=null!=arguments[t]?arguments[t]:{};t%2?of(r=ab(Object(i),!0)).call(r,(function(t){fb(e,t,i[t])})):yf?xf(e,yf(i)):of(n=ab(Object(i))).call(n,(function(t){Df(e,t,Wl(i,t))}))}return e}function sb(e){return(sb="function"==typeof Pf&&"symbol"==typeof R_?function(e){return typeof e}:function(e){return e&&"function"==typeof Pf&&e.constructor===Pf&&e!==Pf.prototype?"symbol":typeof e})(e)}function ub(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function cb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Df(e,pb(n.key),n)}}function lb(e,t,r){return t&&cb(e.prototype,t),r&&cb(e,r),Df(e,"prototype",{writable:!1}),e}function fb(e,t,r){return(t=pb(t))in e?Df(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hb(e){return function(e){if(P_(e))return db(e)}(e)||function(e){if(void 0!==Pf&&null!=z_(e)||null!=e["@@iterator"])return ib(e)}(e)||function(e,t){var r;if(!e)return;if("string"==typeof e)return db(e,t);var n=yy(r=Object.prototype.toString.call(e)).call(r,8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return ib(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return db(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function db(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pb(e){var t=function(e,t){if("object"!==Ol(e)||null===e)return e;var r=e[Qy];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ol(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ol(t)?t:String(t)}var vb=L,gb=u,yb=P,mb=r,_b=bi,bb=sa,wb=R,kb=Ye,Sb=q,xb=Object.assign,Eb=Object.defineProperty,Tb=gb([].concat),Ab=!xb||mb((function(){if(vb&&1!==xb({b:1},xb(Eb({},"a",{enumerable:!0,get:function(){Eb(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach((function(e){t[e]=e})),7!=xb({},e)[r]||_b(xb({},t)).join("")!=n}))?function(e,t){for(var r=kb(e),n=arguments.length,i=1,a=bb.f,o=wb.f;n>i;)for(var s,u=Sb(arguments[i++]),c=a?Tb(_b(u),a(u)):_b(u),l=c.length,f=0;l>f;)s=c[f++],vb&&!yb(o,u,s)||(r[s]=u[s]);return r}:xb,Ob=Ab;Ar({target:"Object",stat:!0,arity:2,forced:Object.assign!==Ob},{assign:Ob});var Lb=t(te.Object.assign),Cb={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw new TypeError("The listener must be a function");var s=new i(n,a||e,o),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],s]:e._events[u].push(s):(e._events[u]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Uf&&(n.prototype=Uf(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?yy(n).call(n,1):n);return Pl?Qn(i).call(i,Pl(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=new Array(a);i<a;i++)o[i]=n[i].fn;return o},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,a,o){var s=r?r+e:e;if(!this._events[s])return!1;var u,c,l=this._events[s],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,i),!0;case 5:return l.fn.call(l.context,t,n,i,a),!0;case 6:return l.fn.call(l.context,t,n,i,a,o),!0}for(c=1,u=new Array(f-1);c<f;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var h,d=l.length;for(c=0;c<d;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),f){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,n);break;case 4:l[c].fn.call(l[c].context,t,n,i);break;default:if(!u)for(h=1,u=new Array(f-1);h<f;h++)u[h-1]=arguments[h];l[c].fn.apply(l[c].context,u)}}return!0},s.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||o(this,a);else{for(var u=0,c=[],l=s.length;u<l;u++)(s[u].fn!==t||i&&!s[u].once||n&&s[u].context!==n)&&c.push(s[u]);c.length?this._events[a]=1===c.length?c[0]:c:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s}(Cb);var Db=t(Cb.exports),Pb=ao.some;Ar({target:"Array",proto:!0,forced:!Jl("some")},{some:function(e){return Pb(this,e,arguments.length>1?arguments[1]:void 0)}});var Rb=Hn("Array").some,Ub=c,Ib=Rb,Mb=Array.prototype,Bb=t((function(e){var t=e.some;return e===Mb||Ub(Mb,e)&&t===Mb.some?Ib:t})),jb=Rr,Fb=Zn,Nb=Y,Vb=RangeError,zb=u,Gb=Mr,qb=Zn,Hb=Y,Kb=zb((function(e){var t=Fb(Nb(this)),r="",n=jb(e);if(n<0||n==1/0)throw Vb("Wrong number of repetitions");for(;n>0;(n>>>=1)&&(t+=t))1&n&&(r+=t);return r})),Wb=zb("".slice),Yb=Math.ceil,Jb=function(e){return function(t,r,n){var i,a,o=qb(Hb(t)),s=Gb(r),u=o.length,c=void 0===n?" ":qb(n);return s<=u||""==c?o:((a=Kb(c,Yb((i=s-u)/c.length))).length>i&&(a=Wb(a,0,i)),e?o+a:a+o)}},Qb={start:Jb(!1),end:Jb(!0)},Xb=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(se),$b=Qb.start;Ar({target:"String",proto:!0,forced:Xb},{padStart:function(e){return $b(this,e,arguments.length>1?arguments[1]:void 0)}});var Zb,ew=Hn("String").padStart,tw=c,rw=ew,nw=String.prototype,iw=t((function(e){var t=e.padStart;return"string"==typeof e||e===nw||tw(nw,e)&&t===nw.padStart?rw:t})),aw="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,ow=f,sw=g,uw=A,cw=aw,lw=se,fw=Cs,hw=Ed,dw=ow.Function,pw=/MSIE .\./.test(lw)||cw&&((Zb=ow.Bun.version.split(".")).length<3||0==Zb[0]&&(Zb[1]<3||3==Zb[1]&&0==Zb[2])),vw=function(e,t){var r=t?2:1;return pw?function(n,i){var a=hw(arguments.length,1)>r,o=uw(n)?n:dw(n),s=a?fw(arguments,r):[],u=a?function(){sw(o,this,s)}:o;return t?e(u,i):e(u)}:e},gw=Ar,yw=f,mw=vw(yw.setInterval,!0);gw({global:!0,bind:!0,forced:yw.setInterval!==mw},{setInterval:mw});var _w=Ar,bw=f,ww=vw(bw.setTimeout,!0);_w({global:!0,bind:!0,forced:bw.setTimeout!==ww},{setTimeout:ww});var kw=t(te.setInterval),Sw=t(te.setTimeout),xw=ao.every;Ar({target:"Array",proto:!0,forced:!Jl("every")},{every:function(e){return xw(this,e,arguments.length>1?arguments[1]:void 0)}});var Ew=Hn("Array").every,Tw=c,Aw=Ew,Ow=Array.prototype,Lw=t((function(e){var t=e.every;return e===Ow||Tw(Ow,e)&&t===Ow.every?Aw:t})),Cw=f,Dw=r,Pw=Zn,Rw=h_.trim,Uw=i_,Iw=u("".charAt),Mw=Cw.parseFloat,Bw=Cw.Symbol,jw=Bw&&Bw.iterator,Fw=1/Mw(Uw+"-0")!=-1/0||jw&&!Dw((function(){Mw(Object(jw))}))?function(e){var t=Rw(Pw(e)),r=Mw(t);return 0===r&&"-"==Iw(t,0)?-0:r}:Mw;Ar({global:!0,forced:parseFloat!=Fw},{parseFloat:Fw});var Nw=t(te.parseFloat),Vw=ku.PROPER,zw=r,Gw=i_,qw=h_.trim;Ar({target:"String",proto:!0,forced:function(e){return zw((function(){return!!Gw[e]()||"​᠎"!=="​᠎"[e]()||Vw&&Gw[e].name!==e}))}("trim")},{trim:function(){return qw(this)}});var Hw=Hn("String").trim,Kw=c,Ww=Hw,Yw=String.prototype,Jw=t((function(e){var t=e.trim;return"string"==typeof e||e===Yw||Kw(Yw,e)&&t===Yw.trim?Ww:t})),Qw=ta,Xw=Math.floor,$w=function(e,t){var r=e.length,n=Xw(r/2);return r<8?Zw(e,t):ek(e,$w(Qw(e,0,n),t),$w(Qw(e,n),t),t)},Zw=function(e,t){for(var r,n,i=e.length,a=1;a<i;){for(n=a,r=e[a];n&&t(e[n-1],r)>0;)e[n]=e[--n];n!==a++&&(e[n]=r)}return e},ek=function(e,t,r,n){for(var i=t.length,a=r.length,o=0,s=0;o<i||s<a;)e[o+s]=o<i&&s<a?n(t[o],r[s])<=0?t[o++]:r[s++]:o<i?t[o++]:r[s++];return e},tk=$w,rk=se.match(/firefox\/(\d+)/i),nk=!!rk&&+rk[1],ik=/MSIE|Trident/.test(se),ak=se.match(/AppleWebKit\/(\d+)\./),ok=!!ak&&+ak[1],sk=Ar,uk=u,ck=Ce,lk=Ye,fk=jr,hk=jm,dk=Zn,pk=r,vk=tk,gk=Jl,yk=nk,mk=ik,_k=pe,bk=ok,wk=[],kk=uk(wk.sort),Sk=uk(wk.push),xk=pk((function(){wk.sort(void 0)})),Ek=pk((function(){wk.sort(null)})),Tk=gk("sort"),Ak=!pk((function(){if(_k)return _k<70;if(!(yk&&yk>3)){if(mk)return!0;if(bk)return bk<603;var e,t,r,n,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)wk.push({k:t+n,v:r})}for(wk.sort((function(e,t){return t.v-e.v})),n=0;n<wk.length;n++)t=wk[n].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}}));sk({target:"Array",proto:!0,forced:xk||!Ek||!Tk||!Ak},{sort:function(e){void 0!==e&&ck(e);var t=lk(this);if(Ak)return void 0===e?kk(t):kk(t,e);var r,n,i=[],a=fk(t);for(n=0;n<a;n++)n in t&&Sk(i,t[n]);for(vk(i,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:dk(t)>dk(r)?1:-1}}(e)),r=fk(i),n=0;n<r;)t[n]=i[n++];for(;n<a;)hk(t,n++);return t}});var Ok=Hn("Array").sort,Lk=c,Ck=Ok,Dk=Array.prototype,Pk=t((function(e){var t=e.sort;return e===Dk||Lk(Dk,e)&&t===Dk.sort?Ck:t})),Rk=Ar,Uk=Date,Ik=u(Uk.prototype.getTime);Rk({target:"Date",stat:!0},{now:function(){return Ik(new Uk)}});var Mk,Bk=t(te.Date.now),jk="undefined"!=typeof window&&window.location&&wm(Mk=window.location.href).call(Mk,"xgplayerdebugger=1")>-1,Fk="color: #525252; background-color: #90ee90;",Nk="color: #525252; background-color: red;",Vk="color: #525252; background-color: yellow; ",zk="%c[xgplayer]",Gk={config:{debug:jk?3:0},logInfo:function(e){for(var t,r,n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];this.config.debug>=3&&(r=console).log.apply(r,Qn(t=[zk,Fk,e]).call(t,i))},logWarn:function(e){for(var t,r,n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];this.config.debug>=1&&(r=console).warn.apply(r,Qn(t=[zk,Vk,e]).call(t,i))},logError:function(e){var t,r;if(!(this.config.debug<1)){for(var n=this.config.debug>=2?"trace":"error",i=arguments.length,a=new Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];(r=console)[n].apply(r,Qn(t=[zk,Nk,e]).call(t,a))}}},qk=function(){function e(t){ub(this,e),this.bufferedList=t}return lb(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}(),Hk={};Hk.createDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=document.createElement(t);return a.className=i,a.innerHTML=r,of(e=Dl(n)).call(e,(function(e){var r=e,i=n[e];"video"===t||"audio"===t||"live-video"===t?i&&a.setAttribute(r,i):a.setAttribute(r,i)})),a},Hk.createDomFromHtml=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{var n=document.createElement("div");n.innerHTML=e;var i=n.children;if(n=null,i.length>0){var a;if(i=i[0],r&&Hk.addClass(i,r),t)of(a=Dl(t)).call(a,(function(e){i.setAttribute(e,t[e])}));return i}return null}catch(o){return Gk.logError("util.createDomFromHtml",o),null}},Hk.hasClass=function(e,t){if(!e||!t)return!1;try{return Bb(Array.prototype).call(e.classList,(function(e){return e===t}))}catch(n){var r=e.className&&"object"===sb(e.className)?e.getAttribute("class"):e.className;return r&&!!r.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}},Hk.addClass=function(e,t){if(e&&t)try{var r;of(r=t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g)).call(r,(function(t){t&&e.classList.add(t)}))}catch(n){Hk.hasClass(e,t)||(e.className&&"object"===sb(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t)}},Hk.removeClass=function(e,t){if(e&&t)try{var r;of(r=t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g)).call(r,(function(t){t&&e.classList.remove(t)}))}catch(i){var n;if(Hk.hasClass(e,t))of(n=t.split(/\s+/g)).call(n,(function(t){var r=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===sb(e.className)?e.setAttribute("class",e.getAttribute("class").replace(r," ")):e.className=e.className.replace(r," ")}))}},Hk.toggleClass=function(e,t){var r;e&&of(r=t.split(/\s+/g)).call(r,(function(t){Hk.hasClass(e,t)?Hk.removeClass(e,t):Hk.addClass(e,t)}))},Hk.classNames=function(){for(var e=arguments,t=[],r=function(r){if("String"===Hk.typeOf(e[r]))t.push(e[r]);else if("Object"===Hk.typeOf(e[r])){var n;D_(n=Dl(e[r])).call(n,(function(n){e[r][n]&&t.push(n)}))}},n=0;n<arguments.length;n++)r(n);return t.join(" ")},Hk.findDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,r=arguments.length>1?arguments[1]:void 0;try{e=t.querySelector(r)}catch(n){Gk.logError("util.findDom",n),0===wm(r).call(r,"#")&&(e=t.getElementById(yy(r).call(r,1)))}return e},Hk.getCss=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,!1)[t]},Hk.padStart=function(e,t,r){for(var n=String(r),i=t>>0,a=Math.ceil(i/n.length),o=[],s=String(e);a--;)o.push(n);return o.join("").substring(0,i-s.length)+s},Hk.format=function(e){if(window.isNaN(e))return"";e=Math.round(e);var t=iw(Hk).call(Hk,Math.floor(e/3600),2,0),r=iw(Hk).call(Hk,Math.floor((e-3600*t)/60),2,0),n=iw(Hk).call(Hk,Math.floor(e-3600*t-60*r),2,0);return("00"===t?[r,n]:[t,r,n]).join(":")},Hk.event=function(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement},Hk.typeOf=function(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]},Hk.deepCopy=function(e,t){var r;if("Object"===Hk.typeOf(t)&&"Object"===Hk.typeOf(e))return of(r=Dl(t)).call(r,(function(r){if("Object"!==Hk.typeOf(t[r])||t[r]instanceof Node)if("Array"===Hk.typeOf(t[r])){var n;e[r]="Array"===Hk.typeOf(e[r])?Qn(n=e[r]).call(n,t[r]):t[r]}else e[r]=t[r];else void 0===e[r]||void 0===e[r]?e[r]=t[r]:Hk.deepCopy(e[r],t[r])})),e},Hk.deepMerge=function(e,t){var r;return D_(r=Dl(t)).call(r,(function(r){var n;"Array"===Hk.typeOf(t[r])&&"Array"===Hk.typeOf(e[r])?"Array"===Hk.typeOf(e[r])&&(n=e[r]).push.apply(n,hb(t[r])):Hk.typeOf(e[r])!==Hk.typeOf(t[r])||null===e[r]||"Object"!==Hk.typeOf(e[r])||t[r]instanceof window.Node?null!==t[r]&&(e[r]=t[r]):Hk.deepMerge(e[r],t[r])})),e},Hk.getBgImage=function(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var r=document.createElement("a");return r.href=t.replace(/url\("|"\)/g,""),r.href},Hk.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return of(Array.prototype).call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},Hk.setInterval=function(e,t,r,n){e._interval[t]||(e._interval[t]=kw(Uy(r).call(r,e),n))},Hk.clearInterval=function(e,t){clearInterval(e._interval[t]),e._interval[t]=null},Hk.setTimeout=function(e,t,r){e._timers||(e._timers=[]);var n=Sw((function(){t(),Hk.clearTimeout(e,n)}),r);return e._timers.push(n),n},Hk.clearTimeout=function(e,t){var r=e._timers;if("Array"===Hk.typeOf(r)){for(var n=0;n<r.length;n++)if(r[n]===t){r_(r).call(r,n,1),clearTimeout(t);break}}else clearTimeout(t)},Hk.clearAllTimers=function(e){var t=e._timers;"Array"===Hk.typeOf(t)&&(D_(t).call(t,(function(e){clearTimeout(e)})),e._timerIds=[])},Hk.createImgBtn=function(e,t,r,n){var i,a,o,s,u,c,l,f,h,d,p,v,g=Hk.createDom("xg-".concat(e),"",{},"xgplayer-".concat(e,"-img"));(g.style.backgroundImage='url("'.concat(t,'")'),r&&n)&&(Lw(i=["px","rem","em","pt","dp","vw","vh","vm","%"]).call(i,(function(e){var t,i;return!(wm(r).call(r,e)>-1&&wm(n).call(n,e)>-1)||(l=Nw(Jw(t=yy(r).call(r,0,wm(r).call(r,e))).call(t)),f=Nw(Jw(i=yy(n).call(n,0,wm(n).call(n,e))).call(i)),h=e,!1)})),g.style.width=Qn(a="".concat(l)).call(a,h),g.style.height=Qn(o="".concat(f)).call(o,h),g.style.backgroundSize=Qn(s=Qn(u=Qn(c="".concat(l)).call(c,h," ")).call(u,f)).call(s,h),g.style.margin="start"===e?Qn(d=Qn(p=Qn(v="-".concat(f/2)).call(v,h," auto auto -")).call(p,l/2)).call(d,h):"auto 5px auto 5px");return g},Hk.Hex2RGBA=function(e,t){var r,n=[];if(/^\#[0-9A-F]{3}$/i.test(e)){var i="#";e.replace(/[0-9A-F]/gi,(function(e){i+=e+e})),e=i}return/^#[0-9A-F]{6}$/i.test(e)?(e.replace(/[0-9A-F]{2}/gi,(function(e){n.push(E_(e,16))})),Qn(r="rgba(".concat(n.join(","),", ")).call(r,t,")")):"rgba(255, 255, 255, 0.1)"},Hk.getFullScreenEl=function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement},Hk.checkIsFunction=function(e){return e&&"function"==typeof e},Hk.checkIsObject=function(e){return null!==e&&"object"===sb(e)},Hk.hide=function(e){e.style.display="none"},Hk.show=function(e,t){e.style.display=t||"block"},Hk.isUndefined=function(e){if(null==e)return!0},Hk.isNotNull=function(e){return!(null==e)},Hk.setStyleFromCsstext=function(e,t){if(t)if("String"===Hk.typeOf(t)){var r=t.replace(/\s+/g,"").split(";");D_(r).call(r,(function(t){if(t){var r=t.split(":");r.length>1&&(e.style[r[0]]=r[1])}}))}else{var n;D_(n=Dl(t)).call(n,(function(r){e.style[r]=t[r]}))}},Hk.filterStyleFromText=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["width","height","top","left","bottom","right","position","z-index","padding","margin","transform"],n=e.style.cssText;if(!n)return{};var i=n.replace(/\s+/g,"").split(";"),a={},o={};return D_(i).call(i,(function(e){if(e){var t=e.split(":");t.length>1&&(!function(e,t){for(var r=0,n=t.length;r<n;r++)if(wm(e).call(e,t[r])>-1)return!0;return!1}(t[0],r)?o[t[0]]=t[1]:a[t[0]]=t[1])}})),e.setAttribute("style",""),D_(t=Dl(o)).call(t,(function(t){e.style[t]=o[t]})),a},Hk.getStyleFromCsstext=function(e){var t=e.style.cssText;if(!t)return{};var r=t.replace(/\s+/g,"").split(";"),n={};return D_(r).call(r,(function(e){if(e){var t=e.split(":");t.length>1&&(n[t[0]]=t[1])}})),n},Hk.preloadImg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){var n=new window.Image;n.onload=function(e){n=null,t&&t(e)},n.onerror=function(e){n=null,r&&r(e)},n.src=e}},Hk.stopPropagation=function(e){e&&e.stopPropagation()},Hk.scrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},Hk.scrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},Hk.checkTouchSupport=function(){return"ontouchstart"in window},Hk.getBuffered2=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,r=[],n=0;n<e.length;n++)r.push({start:e.start(n)<.5?0:e.start(n),end:e.end(n)});Pk(r).call(r,(function(e,t){var r=e.start-t.start;return r||t.end-e.end}));var i=[];if(t)for(var a=0;a<r.length;a++){var o=i.length;if(o){var s=i[o-1].end;r[a].start-s<t?r[a].end>s&&(i[o-1].end=r[a].end):i.push(r[a])}else i.push(r[a])}else i=r;return new qk(i)},Hk.getEventPos=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e.touches&&e.touches.length>0&&(e=e.touches[0]),{x:e.x/t,y:e.y/t,clientX:e.clientX/t,clientY:e.clientY/t,offsetX:e.offsetX/t,offsetY:e.offsetY/t,pageX:e.pageX/t,pageY:e.pageY/t}},Hk.requestAnimationFrame=function(e){var t=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(t)return t(e)},Hk.getHostFromUrl=function(e){if("String"!==Hk.typeOf(e))return"";var t=e.split("/"),r="";return t.length>3&&t[2]&&(r=t[2]),r},Hk.cancelAnimationFrame=function(e){var t=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.cancelRequestAnimationFrame;t&&t(e)},Hk.isMSE=function(e){return!!(e&&e instanceof HTMLMediaElement)&&(/^blob/.test(e.currentSrc)||/^blob/.test(e.src))},Hk.isBlob=function(e){return/^blob/.test(e)},Hk.generateSessionId=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=(new Date).getTime();try{e=E_(e)}catch(r){e=0}return t+=e,window.performance&&"function"==typeof window.performance.now&&(t+=E_(window.performance.now())),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var r=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?r:3&r|8).toString(16)}))},Hk.createEvent=function(e){var t;return"function"==typeof window.Event?t=new Event(e):(t=document.createEvent("Event")).initEvent(e,!0,!0),t},Hk.adjustTimeByDuration=function(e,t,r){return t&&e&&(e>t||r&&e<t)?t:e},Hk.createPositionBar=function(e,t){var r=Hk.createDom("xg-bar","",{"data-index":-1},e);return t.appendChild(r),r},Hk.getTransformStyle=function(){var e,t,r,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{x:0,y:0,scale:1,rotate:0};return Qn(e=Qn(t=Qn(r="scale(".concat(n.scale||1,") translate(")).call(r,n.x||0,"%, ")).call(t,n.y||0,"%) rotate(")).call(e,n.rotate||0,"deg)")},Hk.convertDeg=function(e){return Math.abs(e)<=1?360*e:e%360},Hk.getIndexByTime=function(e,t){var r=t.length,n=-1;if(r<1)return n;if(e<=t[0].end||r<2)n=0;else if(e>t[r-1].end)n=r-1;else for(var i=1;i<r;i++)if(e>t[i-1].end&&e<=t[i].end){n=i;break}return n},Hk.getOffsetCurrentTime=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,n=-1;if((n=r>=0&&r<t.length?r:Hk.getIndexByTime(e,t))<0)return-1;var i=t.length,a=t[n],o=a.start,s=a.end,u=a.cTime,c=a.offset;return e<o?u:e>=o&&e<=s?e-c:e>s&&n>=i-1?s:-1},Hk.getCurrentTimeByOffset=function(e,t){var r=-1;if(!t||t.length<0)return e;for(var n=0;n<t.length;n++)if(e<=t[n].duration){r=n;break}if(-1!==r){var i=t[r].start;return r-1<0?i+e:i+(e-t[r-1].duration)}return e};var Kk="3.0.10-alpha.4",Wk={1:5101,2:5102,3:5103,4:5104,5:5105,6:5106},Yk=lb((function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{errorType:"",errorCode:0,errorMessage:"",originError:"",ext:{},mediaError:null};ub(this,e);var n=t&&t.i18n?t.i18n.ERROR_TYPES:null;if(t.media){var i,a=r.mediaError?r.mediaError:t.media.error||{},o=t.duration,s=t.currentTime,u=t.ended,c=t.src,l=t.currentSrc,f=t.media,h=f.readyState,d=f.networkState,p=r.errorCode||a.code;Wk[p]&&(p=Wk[p]);var v={playerVersion:Kk,currentTime:s,duration:o,ended:u,readyState:h,networkState:d,src:c||l,errorType:r.errorType,errorCode:p,message:r.errorMessage||a.message,mediaError:a,originError:r.originError?r.originError.stack:"",host:Hk.getHostFromUrl(c||l)};return r.ext&&D_(i=Dl(r.ext)).call(i,(function(e){v[e]=r.ext[e]})),v}if(arguments.length>1){for(var g={playerVersion:Kk,domain:document.domain},y=["errorType","currentTime","duration","networkState","readyState","src","currentSrc","ended","errd","errorCode","mediaError"],m=0;m<arguments.length;m++)g[y[m]]=arguments[m];return g.ex=n?(n[arguments[0]]||{}).msg:"",g}}));function Jk(e,t,r){for(var n,i=arguments.length,a=new Array(i>3?i-3:0),o=3;o<i;o++)a[o-3]=arguments[o];var s,u=t.call.apply(t,Qn(n=[e]).call(n,a));r&&"function"==typeof r&&(u&&u.then?u.then((function(){for(var t,n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];r.call.apply(r,Qn(t=[e]).call(t,i))})):r.call.apply(r,Qn(s=[e]).call(s,a)))}function Qk(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{pre:null,next:null};return this.__hooks||(this.__hooks={}),!this.__hooks[e]&&(this.__hooks[e]=null),Uy(r=function(){var r,i=arguments,a=this;if(n.pre)try{var o,s;(s=n.pre).call.apply(s,Qn(o=[this]).call(o,yy(Array.prototype).call(arguments)))}catch(y){var u,c;throw y.message=Qn(u=Qn(c="[pluginName: ".concat(this.pluginName,":")).call(c,e,":pre error] >> ")).call(u,y.message),y}if(this.__hooks&&this.__hooks[e])try{var l,f,h,d=(f=this.__hooks[e]).call.apply(f,Qn(l=[this,this]).call(l,yy(Array.prototype).call(arguments)));if(d)if(d.then)d.then((function(e){var r;!1!==e&&Jk.apply(void 0,Qn(r=[a,t,n.next]).call(r,hb(i)))})).catch((function(e){throw e}));else Jk.apply(void 0,Qn(h=[this,t,n.next]).call(h,yy(Array.prototype).call(arguments)));else if(void 0===d){var p;Jk.apply(void 0,Qn(p=[this,t,n.next]).call(p,yy(Array.prototype).call(arguments)))}}catch(y){var v,g;throw y.message=Qn(v=Qn(g="[pluginName: ".concat(this.pluginName,":")).call(g,e,"] >> ")).call(v,y.message),y}else Jk.apply(void 0,Qn(r=[this,t,n.next]).call(r,yy(Array.prototype).call(arguments)))}).call(r,this)}function Xk(e,t){var r=this.__hooks;if(r)return r.hasOwnProperty(e)?(r&&(r[e]=t),!0):(console.warn("has no supported hook which name [".concat(e,"]")),!1)}function $k(e,t){var r=this.__hooks;r&&delete r[e]}function Zk(e,t){var r;Gk.logError(Qn(r="[".concat(e,"] event or callback cant be undefined or null when call ")).call(r,t))}var eS=function(){function e(t){ub(this,e),Hk.checkIsFunction(this.beforeCreate)&&this.beforeCreate(t),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.__hooks={},t&&D_(t).call(t,(function(t){e.__hooks[t]=null})),Df(e,"hooks",{get:function(){var t;return e.__hooks&&D_(t=Dl(e.__hooks)).call(t,(function(t){if(e.__hooks[t])return t}))}})}(this),this.__args=t,this.__events={},this.__onceEvents={},this.config=t.config||{},this.player=null,this.playerConfig={},this.pluginName="",this.__init(t)}return lb(e,[{key:"beforeCreate",value:function(e){}},{key:"afterCreate",value:function(){}},{key:"beforePlayerInit",value:function(){}},{key:"onPluginsReady",value:function(){}},{key:"afterPlayerInit",value:function(){}},{key:"destroy",value:function(){}},{key:"__init",value:function(e){this.player=e.player,this.playerConfig=e.player&&e.player.config,this.pluginName=e.pluginName?e.pluginName.toLowerCase():this.constructor.pluginName.toLowerCase(),this.logger=e.player&&e.player.logger}},{key:"updateLang",value:function(e){e||(e=this.lang)}},{key:"lang",get:function(){return this.player.lang}},{key:"i18n",get:function(){return this.player.i18n}},{key:"i18nKeys",get:function(){return this.player.i18nKeys}},{key:"domEventType",get:function(){var e=Hk.checkTouchSupport()?"touch":"mouse";return!this.playerConfig||"touch"!==this.playerConfig.domEventType&&"mouse"!==this.playerConfig.domEventType||(e=this.playerConfig.domEventType),e}},{key:"on",value:function(e,t){var r=this;e&&t&&this.player?"string"==typeof e?(this.__events[e]=t,this.player.on(e,t)):P_(e)&&of(e).call(e,(function(e){r.__events[e]=t,r.player.on(e,t)})):Zk(this.pluginName,"plugin.on(event, callback)")}},{key:"once",value:function(e,t){var r=this;e&&t&&this.player?"string"==typeof e?(this.__onceEvents[e]=t,this.player.once(e,t)):P_(e)&&of(e).call(e,(function(n){r.__onceEvents[n]=t,r.player.once(e,t)})):Zk(this.pluginName,"plugin.once(event, callback)")}},{key:"off",value:function(e,t){var r=this;e&&t&&this.player?"string"==typeof e?(delete this.__events[e],this.player.off(e,t)):P_(e)&&of(e).call(e,(function(n){delete r.__events[e],r.player.off(n,t)})):Zk(this.pluginName,"plugin.off(event, callback)")}},{key:"offAll",value:function(){var e,t=this;of(e=["__events","__onceEvents"]).call(e,(function(e){var r;of(r=Dl(t[e])).call(r,(function(r){t[e][r]&&t.off(r,t[e][r]),r&&delete t[e][r]}))})),this.__events={},this.__onceEvents={}}},{key:"emit",value:function(e){var t,r;if(this.player){for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];(r=this.player).emit.apply(r,Qn(t=[e]).call(t,i))}}},{key:"emitUserAction",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.player){var n=ob(ob({},r),{},{pluginName:this.pluginName});this.player.emitUserAction(e,t,n)}}},{key:"hook",value:function(e,t){var r;return Qk.call.apply(Qk,Qn(r=[this]).call(r,yy(Array.prototype).call(arguments)))}},{key:"useHooks",value:function(e,t){for(var r,n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a];return Xk.call.apply(Xk,Qn(r=[this]).call(r,yy(Array.prototype).call(arguments)))}},{key:"removeHooks",value:function(e,t){for(var r,n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a];return $k.call.apply($k,Qn(r=[this]).call(r,yy(Array.prototype).call(arguments)))}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(this.player)return r&&(t.pluginName=r),this.player.registerPlugin({plugin:e,options:t})}},{key:"getPlugin",value:function(e){return this.player?this.player.getPlugin(e):null}},{key:"__destroy",value:function(){var e,t=this,r=this.player,n=this.pluginName;this.offAll(),Hk.clearAllTimers(this),Hk.checkIsFunction(this.destroy)&&this.destroy(),D_(e=["player","playerConfig","pluginName","logger","__args","__hooks"]).call(e,(function(e){t[e]=null})),r.unRegisterPlugin(n),this.__hooks=null}}],[{key:"defineGetterOrSetter",value:function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&Df(e,r,t[r])}},{key:"defaultConfig",get:function(){return{}}},{key:"pluginName",get:function(){return"pluginName"}}]),e}(),tS=r,rS=ft("iterator"),nS=!tS((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,r="";return e.pathname="c%20d",t.forEach((function(e,n){t.delete("b"),r+=n+e})),!e.toJSON||!t.size&&true||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[rS]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==r||"x"!==new URL("http://x",void 0).host})),iS=u,aS=2147483647,oS=/[^\0-\u007E]/,sS=/[.\u3002\uFF0E\uFF61]/g,uS="Overflow: input needs wider integers to process",cS=RangeError,lS=iS(sS.exec),fS=Math.floor,hS=String.fromCharCode,dS=iS("".charCodeAt),pS=iS([].join),vS=iS([].push),gS=iS("".replace),yS=iS("".split),mS=iS("".toLowerCase),_S=function(e){return e+22+75*(e<26)},bS=function(e,t,r){var n=0;for(e=r?fS(e/700):e>>1,e+=fS(e/t);e>455;)e=fS(e/35),n+=36;return fS(n+36*e/(e+38))},wS=function(e){var t=[];e=function(e){for(var t=[],r=0,n=e.length;r<n;){var i=dS(e,r++);if(i>=55296&&i<=56319&&r<n){var a=dS(e,r++);56320==(64512&a)?vS(t,((1023&i)<<10)+(1023&a)+65536):(vS(t,i),r--)}else vS(t,i)}return t}(e);var r,n,i=e.length,a=128,o=0,s=72;for(r=0;r<e.length;r++)(n=e[r])<128&&vS(t,hS(n));var u=t.length,c=u;for(u&&vS(t,"-");c<i;){var l=aS;for(r=0;r<e.length;r++)(n=e[r])>=a&&n<l&&(l=n);var f=c+1;if(l-a>fS((aS-o)/f))throw cS(uS);for(o+=(l-a)*f,a=l,r=0;r<e.length;r++){if((n=e[r])<a&&++o>aS)throw cS(uS);if(n==a){for(var h=o,d=36;;){var p=d<=s?1:d>=s+26?26:d-s;if(h<p)break;var v=h-p,g=36-p;vS(t,hS(_S(p+v%g))),h=fS(v/g),d+=36}vS(t,hS(_S(h))),s=bS(o,f,c==u),o=0,c++}}o++,a++}return pS(t,"")},kS=ca,SS=function(e,t,r){for(var n in t)r&&r.unsafe&&e[n]?e[n]=t[n]:kS(e,n,t[n],r);return e},xS=Ar,ES=f,TS=P,AS=u,OS=L,LS=nS,CS=ca,DS=fa,PS=SS,RS=Ia,US=Yu,IS=Xa,MS=pd,BS=A,jS=Xe,FS=Jt,NS=Zr,VS=tr,zS=ee,GS=Zn,qS=Hi,HS=F,KS=_h,WS=hh,YS=Ed,JS=tk,QS=ft("iterator"),XS="URLSearchParams",$S=XS+"Iterator",ZS=IS.set,ex=IS.getterFor(XS),tx=IS.getterFor($S),rx=Object.getOwnPropertyDescriptor,nx=function(e){if(!OS)return ES[e];var t=rx(ES,e);return t&&t.value},ix=nx("fetch"),ax=nx("Request"),ox=nx("Headers"),sx=ax&&ax.prototype,ux=ox&&ox.prototype,cx=ES.RegExp,lx=ES.TypeError,fx=ES.decodeURIComponent,hx=ES.encodeURIComponent,dx=AS("".charAt),px=AS([].join),vx=AS([].push),gx=AS("".replace),yx=AS([].shift),mx=AS([].splice),_x=AS("".split),bx=AS("".slice),wx=/\+/g,kx=Array(4),Sx=function(e){return kx[e-1]||(kx[e-1]=cx("((?:%[\\da-f]{2}){"+e+"})","gi"))},xx=function(e){try{return fx(e)}catch(EM){return e}},Ex=function(e){var t=gx(e,wx," "),r=4;try{return fx(t)}catch(EM){for(;r;)t=gx(t,Sx(r--),xx);return t}},Tx=/[!'()~]|%20/g,Ax={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},Ox=function(e){return Ax[e]},Lx=function(e){return gx(hx(e),Tx,Ox)},Cx=US((function(e,t){ZS(this,{type:$S,iterator:KS(ex(e).entries),kind:t})}),"Iterator",(function(){var e=tx(this),t=e.kind,r=e.iterator.next(),n=r.value;return r.done||(r.value="keys"===t?n.key:"values"===t?n.value:[n.key,n.value]),r}),!0),Dx=function(e){this.entries=[],this.url=null,void 0!==e&&(zS(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===dx(e,0)?bx(e,1):e:GS(e)))};Dx.prototype={type:XS,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,r,n,i,a,o,s,u=WS(e);if(u)for(r=(t=KS(e,u)).next;!(n=TS(r,t)).done;){if(a=(i=KS(VS(n.value))).next,(o=TS(a,i)).done||(s=TS(a,i)).done||!TS(a,i).done)throw lx("Expected sequence with length 2");vx(this.entries,{key:GS(o.value),value:GS(s.value)})}else for(var c in e)jS(e,c)&&vx(this.entries,{key:c,value:GS(e[c])})},parseQuery:function(e){if(e)for(var t,r,n=_x(e,"&"),i=0;i<n.length;)(t=n[i++]).length&&(r=_x(t,"="),vx(this.entries,{key:Ex(yx(r)),value:Ex(px(r,"="))}))},serialize:function(){for(var e,t=this.entries,r=[],n=0;n<t.length;)e=t[n++],vx(r,Lx(e.key)+"="+Lx(e.value));return px(r,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var Px=function(){MS(this,Rx);var e=ZS(this,new Dx(arguments.length>0?arguments[0]:void 0));OS||(this.length=e.entries.length)},Rx=Px.prototype;if(PS(Rx,{append:function(e,t){YS(arguments.length,2);var r=ex(this);vx(r.entries,{key:GS(e),value:GS(t)}),OS||this.length++,r.updateURL()},delete:function(e){YS(arguments.length,1);for(var t=ex(this),r=t.entries,n=GS(e),i=0;i<r.length;)r[i].key===n?mx(r,i,1):i++;OS||(this.length=r.length),t.updateURL()},get:function(e){YS(arguments.length,1);for(var t=ex(this).entries,r=GS(e),n=0;n<t.length;n++)if(t[n].key===r)return t[n].value;return null},getAll:function(e){YS(arguments.length,1);for(var t=ex(this).entries,r=GS(e),n=[],i=0;i<t.length;i++)t[i].key===r&&vx(n,t[i].value);return n},has:function(e){YS(arguments.length,1);for(var t=ex(this).entries,r=GS(e),n=0;n<t.length;)if(t[n++].key===r)return!0;return!1},set:function(e,t){YS(arguments.length,1);for(var r,n=ex(this),i=n.entries,a=!1,o=GS(e),s=GS(t),u=0;u<i.length;u++)(r=i[u]).key===o&&(a?mx(i,u--,1):(a=!0,r.value=s));a||vx(i,{key:o,value:s}),OS||(this.length=i.length),n.updateURL()},sort:function(){var e=ex(this);JS(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){for(var t,r=ex(this).entries,n=FS(e,arguments.length>1?arguments[1]:void 0),i=0;i<r.length;)n((t=r[i++]).value,t.key,this)},keys:function(){return new Cx(this,"keys")},values:function(){return new Cx(this,"values")},entries:function(){return new Cx(this,"entries")}},{enumerable:!0}),CS(Rx,QS,Rx.entries,{name:"entries"}),CS(Rx,"toString",(function(){return ex(this).serialize()}),{enumerable:!0}),OS&&DS(Rx,"size",{get:function(){return ex(this).entries.length},configurable:!0,enumerable:!0}),RS(Px,XS),xS({global:!0,constructor:!0,forced:!LS},{URLSearchParams:Px}),!LS&&BS(ox)){var Ux=AS(ux.has),Ix=AS(ux.set),Mx=function(e){if(zS(e)){var t,r=e.body;if(NS(r)===XS)return t=e.headers?new ox(e.headers):new ox,Ux(t,"content-type")||Ix(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),qS(e,{body:HS(0,GS(r)),headers:HS(0,t)})}return e};if(BS(ix)&&xS({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return ix(e,arguments.length>1?Mx(arguments[1]):{})}}),BS(ax)){var Bx=function(e){return MS(this,sx),new ax(e,arguments.length>1?Mx(arguments[1]):{})};sx.constructor=Bx,Bx.prototype=sx,xS({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:Bx})}}var jx,Fx=Ar,Nx=L,Vx=nS,zx=f,Gx=Jt,qx=u,Hx=ca,Kx=fa,Wx=pd,Yx=Xe,Jx=Ab,Qx=rb,Xx=ta,$x=yl.codeAt,Zx=function(e){var t,r,n=[],i=yS(gS(mS(e),sS,"."),".");for(t=0;t<i.length;t++)r=i[t],vS(n,lS(oS,r)?"xn--"+wS(r):r);return pS(n,".")},eE=Zn,tE=Ia,rE=Ed,nE={URLSearchParams:Px,getState:ex},iE=Xa,aE=iE.set,oE=iE.getterFor("URL"),sE=nE.URLSearchParams,uE=nE.getState,cE=zx.URL,lE=zx.TypeError,fE=zx.parseInt,hE=Math.floor,dE=Math.pow,pE=qx("".charAt),vE=qx(/./.exec),gE=qx([].join),yE=qx(1..toString),mE=qx([].pop),_E=qx([].push),bE=qx("".replace),wE=qx([].shift),kE=qx("".split),SE=qx("".slice),xE=qx("".toLowerCase),EE=qx([].unshift),TE="Invalid scheme",AE="Invalid host",OE="Invalid port",LE=/[a-z]/i,CE=/[\d+-.a-z]/i,DE=/\d/,PE=/^0x/i,RE=/^[0-7]+$/,UE=/^\d+$/,IE=/^[\da-f]+$/i,ME=/[\0\t\n\r #%/:<>?@[\\\]^|]/,BE=/[\0\t\n\r #/:<>?@[\\\]^|]/,jE=/^[\u0000-\u0020]+/,FE=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,NE=/[\t\n\r]/g,VE=function(e){var t,r,n,i;if("number"==typeof e){for(t=[],r=0;r<4;r++)EE(t,e%256),e=hE(e/256);return gE(t,".")}if("object"==typeof e){for(t="",n=function(e){for(var t=null,r=1,n=null,i=0,a=0;a<8;a++)0!==e[a]?(i>r&&(t=n,r=i),n=null,i=0):(null===n&&(n=a),++i);return i>r&&(t=n,r=i),t}(e),r=0;r<8;r++)i&&0===e[r]||(i&&(i=!1),n===r?(t+=r?":":"::",i=!0):(t+=yE(e[r],16),r<7&&(t+=":")));return"["+t+"]"}return e},zE={},GE=Jx({},zE,{" ":1,'"':1,"<":1,">":1,"`":1}),qE=Jx({},GE,{"#":1,"?":1,"{":1,"}":1}),HE=Jx({},qE,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),KE=function(e,t){var r=$x(e,0);return r>32&&r<127&&!Yx(t,e)?e:encodeURIComponent(e)},WE={ftp:21,file:null,http:80,https:443,ws:80,wss:443},YE=function(e,t){var r;return 2==e.length&&vE(LE,pE(e,0))&&(":"==(r=pE(e,1))||!t&&"|"==r)},JE=function(e){var t;return e.length>1&&YE(SE(e,0,2))&&(2==e.length||"/"===(t=pE(e,2))||"\\"===t||"?"===t||"#"===t)},QE=function(e){return"."===e||"%2e"===xE(e)},XE={},$E={},ZE={},eT={},tT={},rT={},nT={},iT={},aT={},oT={},sT={},uT={},cT={},lT={},fT={},hT={},dT={},pT={},vT={},gT={},yT={},mT=function(e,t,r){var n,i,a,o=eE(e);if(t){if(i=this.parse(o))throw lE(i);this.searchParams=null}else{if(void 0!==r&&(n=new mT(r,!0)),i=this.parse(o,null,n))throw lE(i);(a=uE(new sE)).bindURL(this),this.searchParams=a}};mT.prototype={type:"URL",parse:function(e,t,r){var n,i,a,o,s,u=this,c=t||XE,l=0,f="",h=!1,d=!1,p=!1;for(e=eE(e),t||(u.scheme="",u.username="",u.password="",u.host=null,u.port=null,u.path=[],u.query=null,u.fragment=null,u.cannotBeABaseURL=!1,e=bE(e,jE,""),e=bE(e,FE,"$1")),e=bE(e,NE,""),n=Qx(e);l<=n.length;){switch(i=n[l],c){case XE:if(!i||!vE(LE,i)){if(t)return TE;c=ZE;continue}f+=xE(i),c=$E;break;case $E:if(i&&(vE(CE,i)||"+"==i||"-"==i||"."==i))f+=xE(i);else{if(":"!=i){if(t)return TE;f="",c=ZE,l=0;continue}if(t&&(u.isSpecial()!=Yx(WE,f)||"file"==f&&(u.includesCredentials()||null!==u.port)||"file"==u.scheme&&!u.host))return;if(u.scheme=f,t)return void(u.isSpecial()&&WE[u.scheme]==u.port&&(u.port=null));f="","file"==u.scheme?c=lT:u.isSpecial()&&r&&r.scheme==u.scheme?c=eT:u.isSpecial()?c=iT:"/"==n[l+1]?(c=tT,l++):(u.cannotBeABaseURL=!0,_E(u.path,""),c=vT)}break;case ZE:if(!r||r.cannotBeABaseURL&&"#"!=i)return TE;if(r.cannotBeABaseURL&&"#"==i){u.scheme=r.scheme,u.path=Xx(r.path),u.query=r.query,u.fragment="",u.cannotBeABaseURL=!0,c=yT;break}c="file"==r.scheme?lT:rT;continue;case eT:if("/"!=i||"/"!=n[l+1]){c=rT;continue}c=aT,l++;break;case tT:if("/"==i){c=oT;break}c=pT;continue;case rT:if(u.scheme=r.scheme,i==jx)u.username=r.username,u.password=r.password,u.host=r.host,u.port=r.port,u.path=Xx(r.path),u.query=r.query;else if("/"==i||"\\"==i&&u.isSpecial())c=nT;else if("?"==i)u.username=r.username,u.password=r.password,u.host=r.host,u.port=r.port,u.path=Xx(r.path),u.query="",c=gT;else{if("#"!=i){u.username=r.username,u.password=r.password,u.host=r.host,u.port=r.port,u.path=Xx(r.path),u.path.length--,c=pT;continue}u.username=r.username,u.password=r.password,u.host=r.host,u.port=r.port,u.path=Xx(r.path),u.query=r.query,u.fragment="",c=yT}break;case nT:if(!u.isSpecial()||"/"!=i&&"\\"!=i){if("/"!=i){u.username=r.username,u.password=r.password,u.host=r.host,u.port=r.port,c=pT;continue}c=oT}else c=aT;break;case iT:if(c=aT,"/"!=i||"/"!=pE(f,l+1))continue;l++;break;case aT:if("/"!=i&&"\\"!=i){c=oT;continue}break;case oT:if("@"==i){h&&(f="%40"+f),h=!0,a=Qx(f);for(var v=0;v<a.length;v++){var g=a[v];if(":"!=g||p){var y=KE(g,HE);p?u.password+=y:u.username+=y}else p=!0}f=""}else if(i==jx||"/"==i||"?"==i||"#"==i||"\\"==i&&u.isSpecial()){if(h&&""==f)return"Invalid authority";l-=Qx(f).length+1,f="",c=sT}else f+=i;break;case sT:case uT:if(t&&"file"==u.scheme){c=hT;continue}if(":"!=i||d){if(i==jx||"/"==i||"?"==i||"#"==i||"\\"==i&&u.isSpecial()){if(u.isSpecial()&&""==f)return AE;if(t&&""==f&&(u.includesCredentials()||null!==u.port))return;if(o=u.parseHost(f))return o;if(f="",c=dT,t)return;continue}"["==i?d=!0:"]"==i&&(d=!1),f+=i}else{if(""==f)return AE;if(o=u.parseHost(f))return o;if(f="",c=cT,t==uT)return}break;case cT:if(!vE(DE,i)){if(i==jx||"/"==i||"?"==i||"#"==i||"\\"==i&&u.isSpecial()||t){if(""!=f){var m=fE(f,10);if(m>65535)return OE;u.port=u.isSpecial()&&m===WE[u.scheme]?null:m,f=""}if(t)return;c=dT;continue}return OE}f+=i;break;case lT:if(u.scheme="file","/"==i||"\\"==i)c=fT;else{if(!r||"file"!=r.scheme){c=pT;continue}if(i==jx)u.host=r.host,u.path=Xx(r.path),u.query=r.query;else if("?"==i)u.host=r.host,u.path=Xx(r.path),u.query="",c=gT;else{if("#"!=i){JE(gE(Xx(n,l),""))||(u.host=r.host,u.path=Xx(r.path),u.shortenPath()),c=pT;continue}u.host=r.host,u.path=Xx(r.path),u.query=r.query,u.fragment="",c=yT}}break;case fT:if("/"==i||"\\"==i){c=hT;break}r&&"file"==r.scheme&&!JE(gE(Xx(n,l),""))&&(YE(r.path[0],!0)?_E(u.path,r.path[0]):u.host=r.host),c=pT;continue;case hT:if(i==jx||"/"==i||"\\"==i||"?"==i||"#"==i){if(!t&&YE(f))c=pT;else if(""==f){if(u.host="",t)return;c=dT}else{if(o=u.parseHost(f))return o;if("localhost"==u.host&&(u.host=""),t)return;f="",c=dT}continue}f+=i;break;case dT:if(u.isSpecial()){if(c=pT,"/"!=i&&"\\"!=i)continue}else if(t||"?"!=i)if(t||"#"!=i){if(i!=jx&&(c=pT,"/"!=i))continue}else u.fragment="",c=yT;else u.query="",c=gT;break;case pT:if(i==jx||"/"==i||"\\"==i&&u.isSpecial()||!t&&("?"==i||"#"==i)){if(".."===(s=xE(s=f))||"%2e."===s||".%2e"===s||"%2e%2e"===s?(u.shortenPath(),"/"==i||"\\"==i&&u.isSpecial()||_E(u.path,"")):QE(f)?"/"==i||"\\"==i&&u.isSpecial()||_E(u.path,""):("file"==u.scheme&&!u.path.length&&YE(f)&&(u.host&&(u.host=""),f=pE(f,0)+":"),_E(u.path,f)),f="","file"==u.scheme&&(i==jx||"?"==i||"#"==i))for(;u.path.length>1&&""===u.path[0];)wE(u.path);"?"==i?(u.query="",c=gT):"#"==i&&(u.fragment="",c=yT)}else f+=KE(i,qE);break;case vT:"?"==i?(u.query="",c=gT):"#"==i?(u.fragment="",c=yT):i!=jx&&(u.path[0]+=KE(i,zE));break;case gT:t||"#"!=i?i!=jx&&("'"==i&&u.isSpecial()?u.query+="%27":u.query+="#"==i?"%23":KE(i,zE)):(u.fragment="",c=yT);break;case yT:i!=jx&&(u.fragment+=KE(i,GE))}l++}},parseHost:function(e){var t,r,n;if("["==pE(e,0)){if("]"!=pE(e,e.length-1))return AE;if(t=function(e){var t,r,n,i,a,o,s,u=[0,0,0,0,0,0,0,0],c=0,l=null,f=0,h=function(){return pE(e,f)};if(":"==h()){if(":"!=pE(e,1))return;f+=2,l=++c}for(;h();){if(8==c)return;if(":"!=h()){for(t=r=0;r<4&&vE(IE,h());)t=16*t+fE(h(),16),f++,r++;if("."==h()){if(0==r)return;if(f-=r,c>6)return;for(n=0;h();){if(i=null,n>0){if(!("."==h()&&n<4))return;f++}if(!vE(DE,h()))return;for(;vE(DE,h());){if(a=fE(h(),10),null===i)i=a;else{if(0==i)return;i=10*i+a}if(i>255)return;f++}u[c]=256*u[c]+i,2!=++n&&4!=n||c++}if(4!=n)return;break}if(":"==h()){if(f++,!h())return}else if(h())return;u[c++]=t}else{if(null!==l)return;f++,l=++c}}if(null!==l)for(o=c-l,c=7;0!=c&&o>0;)s=u[c],u[c--]=u[l+o-1],u[l+--o]=s;else if(8!=c)return;return u}(SE(e,1,-1)),!t)return AE;this.host=t}else if(this.isSpecial()){if(e=Zx(e),vE(ME,e))return AE;if(t=function(e){var t,r,n,i,a,o,s,u=kE(e,".");if(u.length&&""==u[u.length-1]&&u.length--,(t=u.length)>4)return e;for(r=[],n=0;n<t;n++){if(""==(i=u[n]))return e;if(a=10,i.length>1&&"0"==pE(i,0)&&(a=vE(PE,i)?16:8,i=SE(i,8==a?1:2)),""===i)o=0;else{if(!vE(10==a?UE:8==a?RE:IE,i))return e;o=fE(i,a)}_E(r,o)}for(n=0;n<t;n++)if(o=r[n],n==t-1){if(o>=dE(256,5-t))return null}else if(o>255)return null;for(s=mE(r),n=0;n<r.length;n++)s+=r[n]*dE(256,3-n);return s}(e),null===t)return AE;this.host=t}else{if(vE(BE,e))return AE;for(t="",r=Qx(e),n=0;n<r.length;n++)t+=KE(r[n],zE);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"==this.scheme},includesCredentials:function(){return""!=this.username||""!=this.password},isSpecial:function(){return Yx(WE,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"==this.scheme&&1==t&&YE(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,r=e.username,n=e.password,i=e.host,a=e.port,o=e.path,s=e.query,u=e.fragment,c=t+":";return null!==i?(c+="//",e.includesCredentials()&&(c+=r+(n?":"+n:"")+"@"),c+=VE(i),null!==a&&(c+=":"+a)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?o[0]:o.length?"/"+gE(o,"/"):"",null!==s&&(c+="?"+s),null!==u&&(c+="#"+u),c},setHref:function(e){var t=this.parse(e);if(t)throw lE(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"==e)try{return new _T(e.path[0]).origin}catch(EM){return"null"}return"file"!=e&&this.isSpecial()?e+"://"+VE(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(eE(e)+":",XE)},getUsername:function(){return this.username},setUsername:function(e){var t=Qx(eE(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var r=0;r<t.length;r++)this.username+=KE(t[r],HE)}},getPassword:function(){return this.password},setPassword:function(e){var t=Qx(eE(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var r=0;r<t.length;r++)this.password+=KE(t[r],HE)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?VE(e):VE(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,sT)},getHostname:function(){var e=this.host;return null===e?"":VE(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,uT)},getPort:function(){var e=this.port;return null===e?"":eE(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""==(e=eE(e))?this.port=null:this.parse(e,cT))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+gE(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,dT))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""==(e=eE(e))?this.query=null:("?"==pE(e,0)&&(e=SE(e,1)),this.query="",this.parse(e,gT)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!=(e=eE(e))?("#"==pE(e,0)&&(e=SE(e,1)),this.fragment="",this.parse(e,yT)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var _T=function(e){var t=Wx(this,bT),r=rE(arguments.length,1)>1?arguments[1]:void 0,n=aE(t,new mT(e,!1,r));Nx||(t.href=n.serialize(),t.origin=n.getOrigin(),t.protocol=n.getProtocol(),t.username=n.getUsername(),t.password=n.getPassword(),t.host=n.getHost(),t.hostname=n.getHostname(),t.port=n.getPort(),t.pathname=n.getPathname(),t.search=n.getSearch(),t.searchParams=n.getSearchParams(),t.hash=n.getHash())},bT=_T.prototype,wT=function(e,t){return{get:function(){return oE(this)[e]()},set:t&&function(e){return oE(this)[t](e)},configurable:!0,enumerable:!0}};if(Nx&&(Kx(bT,"href",wT("serialize","setHref")),Kx(bT,"origin",wT("getOrigin")),Kx(bT,"protocol",wT("getProtocol","setProtocol")),Kx(bT,"username",wT("getUsername","setUsername")),Kx(bT,"password",wT("getPassword","setPassword")),Kx(bT,"host",wT("getHost","setHost")),Kx(bT,"hostname",wT("getHostname","setHostname")),Kx(bT,"port",wT("getPort","setPort")),Kx(bT,"pathname",wT("getPathname","setPathname")),Kx(bT,"search",wT("getSearch","setSearch")),Kx(bT,"searchParams",wT("getSearchParams")),Kx(bT,"hash",wT("getHash","setHash"))),Hx(bT,"toJSON",(function(){return oE(this).serialize()}),{enumerable:!0}),Hx(bT,"toString",(function(){return oE(this).serialize()}),{enumerable:!0}),cE){var kT=cE.createObjectURL,ST=cE.revokeObjectURL;kT&&Hx(_T,"createObjectURL",Gx(kT,cE)),ST&&Hx(_T,"revokeObjectURL",Gx(ST,cE))}tE(_T,"URL"),Fx({global:!0,constructor:!0,forced:!Vx,sham:!Nx},{URL:_T});var xT=Ar,ET=r,TT=Ed,AT=Zn,OT=nS,LT=oe("URL");xT({target:"URL",stat:!0,forced:!(OT&&ET((function(){LT.canParse()})))},{canParse:function(e){var t=TT(arguments.length,1),r=AT(e),n=t<2||void 0===arguments[1]?void 0:AT(arguments[1]);try{return!!new LT(r,n)}catch(EM){return!1}}});var CT=t(te.URL),DT=te,PT=g;DT.JSON||(DT.JSON={stringify:JSON.stringify});var RT=function(e,t,r){return PT(DT.JSON.stringify,null,arguments)},UT=t(RT),IT=ee,MT=b,BT=ft("match"),jT=function(e){var t;return IT(e)&&(void 0!==(t=e[BT])?!!t:"RegExp"==MT(e))},FT=TypeError,NT=ft("match"),VT=ci.includes;Ar({target:"Array",proto:!0,forced:r((function(){return!Array(1).includes()}))},{includes:function(e){return VT(this,e,arguments.length>1?arguments[1]:void 0)}});var zT=Hn("Array").includes,GT=Ar,qT=function(e){if(jT(e))throw FT("The method doesn't accept regular expressions");return e},HT=Y,KT=Zn,WT=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[NT]=!1,"/./"[e](t)}catch(n){}}return!1},YT=u("".indexOf);GT({target:"String",proto:!0,forced:!WT("includes")},{includes:function(e){return!!~YT(KT(HT(this)),KT(qT(e)),arguments.length>1?arguments[1]:void 0)}});var JT=Hn("String").includes,QT=c,XT=zT,$T=JT,ZT=Array.prototype,eA=String.prototype,tA=t((function(e){var t=e.includes;return e===ZT||QT(ZT,e)&&t===ZT.includes?XT:"string"==typeof e||e===eA||QT(eA,e)&&t===eA.includes?$T:t})),rA={exports:{}},nA=r((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),iA=r,aA=ee,oA=b,sA=nA,uA=Object.isExtensible,cA=iA((function(){uA(1)}))||sA?function(e){return!!aA(e)&&((!sA||"ArrayBuffer"!=oA(e))&&(!uA||uA(e)))}:uA,lA=!r((function(){return Object.isExtensible(Object.preventExtensions({}))})),fA=Ar,hA=u,dA=li,pA=ee,vA=Xe,gA=Qt.f,yA=Ki,mA=Ji,_A=cA,bA=lA,wA=!1,kA=rt("meta"),SA=0,xA=function(e){gA(e,kA,{value:{objectID:"O"+SA++,weakData:{}}})},EA=rA.exports={enable:function(){EA.enable=function(){},wA=!0;var e=yA.f,t=hA([].splice),r={};r[kA]=1,e(r).length&&(yA.f=function(r){for(var n=e(r),i=0,a=n.length;i<a;i++)if(n[i]===kA){t(n,i,1);break}return n},fA({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:mA.f}))},fastKey:function(e,t){if(!pA(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!vA(e,kA)){if(!_A(e))return"F";if(!t)return"E";xA(e)}return e[kA].objectID},getWeakData:function(e,t){if(!vA(e,kA)){if(!_A(e))return!0;if(!t)return!1;xA(e)}return e[kA].weakData},onFreeze:function(e){return bA&&wA&&_A(e)&&!vA(e,kA)&&xA(e),e}};dA[kA]=!0;var TA=rA.exports,AA=Ar,OA=f,LA=TA,CA=r,DA=vr,PA=Bh,RA=pd,UA=A,IA=ee,MA=Ia,BA=Qt.f,jA=ao.forEach,FA=L,NA=Xa.set,VA=Xa.getterFor,zA=function(e,t,r){var n,i=-1!==e.indexOf("Map"),a=-1!==e.indexOf("Weak"),o=i?"set":"add",s=OA[e],u=s&&s.prototype,c={};if(FA&&UA(s)&&(a||u.forEach&&!CA((function(){(new s).entries().next()})))){var l=(n=t((function(t,r){NA(RA(t,l),{type:e,collection:new s}),null!=r&&PA(r,t[o],{that:t,AS_ENTRIES:i})}))).prototype,f=VA(e);jA(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"==e||"set"==e;!(e in u)||a&&"clear"==e||DA(l,e,(function(r,n){var i=f(this).collection;if(!t&&a&&!IA(r))return"get"==e&&void 0;var o=i[e](0===r?0:r,n);return t?this:o}))})),a||BA(l,"size",{configurable:!0,get:function(){return f(this).collection.size}})}else n=r.getConstructor(t,e,i,o),LA.enable();return MA(n,e,!1,!0),c[e]=n,AA({global:!0,forced:!0},c),a||r.setStrong(n,e,i),n},GA=Hi,qA=fa,HA=SS,KA=Jt,WA=pd,YA=H,JA=Bh,QA=bc,XA=wc,$A=fd,ZA=L,eO=TA.fastKey,tO=Xa.set,rO=Xa.getterFor,nO={getConstructor:function(e,t,r,n){var i=e((function(e,i){WA(e,a),tO(e,{type:t,index:GA(null),first:void 0,last:void 0,size:0}),ZA||(e.size=0),YA(i)||JA(i,e[n],{that:e,AS_ENTRIES:r})})),a=i.prototype,o=rO(t),s=function(e,t,r){var n,i,a=o(e),s=u(e,t);return s?s.value=r:(a.last=s={index:i=eO(t,!0),key:t,value:r,previous:n=a.last,next:void 0,removed:!1},a.first||(a.first=s),n&&(n.next=s),ZA?a.size++:e.size++,"F"!==i&&(a.index[i]=s)),e},u=function(e,t){var r,n=o(e),i=eO(t);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key==t)return r};return HA(a,{clear:function(){for(var e=o(this),t=e.index,r=e.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete t[r.index],r=r.next;e.first=e.last=void 0,ZA?e.size=0:this.size=0},delete:function(e){var t=this,r=o(t),n=u(t,e);if(n){var i=n.next,a=n.previous;delete r.index[n.index],n.removed=!0,a&&(a.next=i),i&&(i.previous=a),r.first==n&&(r.first=i),r.last==n&&(r.last=a),ZA?r.size--:t.size--}return!!n},forEach:function(e){for(var t,r=o(this),n=KA(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!u(this,e)}}),HA(a,r?{get:function(e){var t=u(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),ZA&&qA(a,"size",{configurable:!0,get:function(){return o(this).size}}),i},setStrong:function(e,t,r){var n=t+" Iterator",i=rO(t),a=rO(n);QA(e,t,(function(e,t){tO(this,{type:n,target:e,state:i(e),kind:t,last:void 0})}),(function(){for(var e=a(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?XA("keys"==t?r.key:"values"==t?r.value:[r.key,r.value],!1):(e.target=void 0,XA(void 0,!0))}),r?"entries":"values",!r,!0),$A(t)}};zA("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),nO);var iO=te.Map,aO=Jt,oO=P,sO=Ce,uO=md,cO=H,lO=Bh,fO=[].push,hO=function(e){var t,r,n,i,a=arguments.length,o=a>1?arguments[1]:void 0;return uO(this),(t=void 0!==o)&&sO(o),cO(e)?new this:(r=[],t?(n=0,i=aO(o,a>2?arguments[2]:void 0),lO(e,(function(e){oO(fO,r,i(e,n++))}))):lO(e,fO,{that:r}),new this(r))};Ar({target:"Map",stat:!0,forced:!0},{from:hO});var dO=Cs,pO=function(){return new this(dO(arguments))};Ar({target:"Map",stat:!0,forced:!0},{of:pO});var vO=Te,gO=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"get"in e&&"set"in e&&"delete"in e&&"entries"in e)return e;throw TypeError(vO(e)+" is not a map")},yO=function(e,t){return 1==t?function(t,r){return t[e](r)}:function(t,r,n){return t[e](r,n)}},mO=yO,_O=oe("Map"),bO={Map:_O,set:mO("set",2),get:mO("get",1),has:mO("has",1),remove:mO("delete",1),proto:_O.prototype},wO=gO,kO=bO.remove;Ar({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=wO(this),r=!0,n=0,i=arguments.length;n<i;n++)e=kO(t,arguments[n]),r=r&&e;return!!r}});var SO=gO,xO=bO.get,EO=bO.has,TO=bO.set;Ar({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var r,n,i=SO(this);return EO(i,e)?(r=xO(i,e),"update"in t&&(r=t.update(r,e,i),TO(i,e,r)),r):(n=t.insert(e,i),TO(i,e,n),n)}});var AO=P,OO=function(e,t,r){for(var n,i,a=r||e.next;!(n=AO(a,e)).done;)if(void 0!==(i=t(n.value)))return i},LO=OO,CO=function(e,t,r){return r?LO(e.entries(),(function(e){return t(e[1],e[0])})):e.forEach(t)},DO=Jt,PO=gO,RO=CO;Ar({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=PO(this),r=DO(e,arguments.length>1?arguments[1]:void 0);return!1!==RO(t,(function(e,n){if(!r(e,n,t))return!1}),!0)}});var UO=Jt,IO=gO,MO=CO,BO=bO.Map,jO=bO.set;Ar({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=IO(this),r=UO(e,arguments.length>1?arguments[1]:void 0),n=new BO;return MO(t,(function(e,i){r(e,i,t)&&jO(n,i,e)})),n}});var FO=Jt,NO=gO,VO=CO;Ar({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=NO(this),r=FO(e,arguments.length>1?arguments[1]:void 0),n=VO(t,(function(e,n){if(r(e,n,t))return{value:e}}),!0);return n&&n.value}});var zO=Jt,GO=gO,qO=CO;Ar({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=GO(this),r=zO(e,arguments.length>1?arguments[1]:void 0),n=qO(t,(function(e,n){if(r(e,n,t))return{key:n}}),!0);return n&&n.key}});var HO=Ar,KO=P,WO=A,YO=Ce,JO=Bh,QO=bO.Map,XO=u([].push);HO({target:"Map",stat:!0,forced:!0},{groupBy:function(e,t){var r=new(WO(this)?this:QO);YO(t);var n=YO(r.has),i=YO(r.get),a=YO(r.set);return JO(e,(function(e){var o=t(e);KO(n,r,o)?XO(KO(i,r,o),e):KO(a,r,o,[e])})),r}});var $O=function(e,t){return e===t||e!=e&&t!=t},ZO=gO,eL=CO;Ar({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return!0===eL(ZO(this),(function(t){if($O(t,e))return!0}),!0)}});var tL=P,rL=Bh,nL=A,iL=Ce,aL=bO.Map;Ar({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var r=new(nL(this)?this:aL);iL(t);var n=iL(r.set);return rL(e,(function(e){tL(n,r,t(e),e)})),r}});var oL=gO,sL=CO;Ar({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){var t=sL(oL(this),(function(t,r){if(t===e)return{key:r}}),!0);return t&&t.key}});var uL=Jt,cL=gO,lL=CO,fL=bO.Map,hL=bO.set;Ar({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=cL(this),r=uL(e,arguments.length>1?arguments[1]:void 0),n=new fL;return lL(t,(function(e,i){hL(n,r(e,i,t),e)})),n}});var dL=Jt,pL=gO,vL=CO,gL=bO.Map,yL=bO.set;Ar({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=pL(this),r=dL(e,arguments.length>1?arguments[1]:void 0),n=new gL;return vL(t,(function(e,i){yL(n,i,r(e,i,t))})),n}});var mL=gO,_L=Bh,bL=bO.set;Ar({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=mL(this),r=arguments.length,n=0;n<r;)_L(arguments[n++],(function(e,r){bL(t,e,r)}),{AS_ENTRIES:!0});return t}});var wL=Ce,kL=gO,SL=CO,xL=TypeError;Ar({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=kL(this),r=arguments.length<2,n=r?void 0:arguments[1];if(wL(e),SL(t,(function(i,a){r?(r=!1,n=i):n=e(n,i,a,t)})),r)throw xL("Reduce of empty map with no initial value");return n}});var EL=Jt,TL=gO,AL=CO;Ar({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=TL(this),r=EL(e,arguments.length>1?arguments[1]:void 0);return!0===AL(t,(function(e,n){if(r(e,n,t))return!0}),!0)}});var OL=Ce,LL=gO,CL=TypeError,DL=bO.get,PL=bO.has,RL=bO.set;Ar({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var r=LL(this),n=arguments.length;OL(t);var i=PL(r,e);if(!i&&n<3)throw CL("Updating absent value");var a=i?DL(r,e):OL(n>2?arguments[2]:void 0)(e,r);return RL(r,e,t(a,e,r)),r}});var UL=P,IL=Ce,ML=A,BL=tr,jL=TypeError,FL=function(e,t){var r,n=BL(this),i=IL(n.get),a=IL(n.has),o=IL(n.set),s=arguments.length>2?arguments[2]:void 0;if(!ML(t)&&!ML(s))throw jL("At least one callback required");return UL(a,n,e)?(r=UL(i,n,e),ML(t)&&(r=t(r),UL(o,n,e,r))):ML(s)&&(r=s(),UL(o,n,e,r)),r};Ar({target:"Map",proto:!0,real:!0,forced:!0},{upsert:FL}),Ar({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:FL});var NL=t(iO);function VL(e,t){var r=Dl(e);if(Pl){var n=Pl(e);t&&(n=jl(n).call(n,(function(t){return Wl(e,t).enumerable}))),r.push.apply(r,n)}return r}function zL(e){for(var t=1;t<arguments.length;t++){var r,n,i=null!=arguments[t]?arguments[t]:{};t%2?of(r=VL(Object(i),!0)).call(r,(function(t){QL(e,t,i[t])})):yf?xf(e,yf(i)):of(n=VL(Object(i))).call(n,(function(t){Df(e,t,Wl(i,t))}))}return e}function GL(){GL=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Df||function(e,t,r){e[t]=r.value},i="function"==typeof Pf?Pf:{},a=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,r){return Df(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(A){u=function(e,t,r){return e[t]=r}}function c(e,t,r,i){var a=t&&t.prototype instanceof h?t:h,o=Uf(a.prototype),s=new x(i||[]);return n(o,"_invoke",{value:b(e,r,s)}),o}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(A){return{type:"throw",arg:A}}}e.wrap=c;var f={};function h(){}function d(){}function p(){}var v={};u(v,a,(function(){return this}));var g=jf&&jf(jf(E([])));g&&g!==t&&r.call(g,a)&&(v=g);var y=p.prototype=h.prototype=Uf(v);function m(e){var t;of(t=["next","throw","return"]).call(t,(function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function i(n,a,o,s){var u=l(e[n],e,a);if("throw"!==u.type){var c=u.arg,f=c.value;return f&&"object"==Ol(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,o,s)}),(function(e){i("throw",e,o,s)})):t.resolve(f).then((function(e){c.value=e,o(c)}),(function(e){return i("throw",e,o,s)}))}s(u.arg)}var a;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return a=a?a.then(n,n):n()}})}function b(e,t,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return{value:void 0,done:!0}}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=w(o,r);if(s){if(s===f)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=l(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===f)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function w(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var i=l(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,f;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],of(e).call(e,k,this),this.reset(!0)}function E(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:T}}function T(){return{value:void 0,done:!0}}return d.prototype=p,n(y,"constructor",{value:p,configurable:!0}),n(p,"constructor",{value:d,configurable:!0}),d.displayName=u(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Ff?Ff(e,p):(e.__proto__=p,u(e,s,"GeneratorFunction")),e.prototype=Uf(y),e},e.awrap=function(e){return{__await:e}},m(_.prototype),u(_.prototype,o,(function(){return this})),e.AsyncIterator=_,e.async=function(t,r,n,i,a){void 0===a&&(a=Gg);var o=new _(c(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},m(y),u(y,s,"Generator"),u(y,a,(function(){return this})),u(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return $g(r).call(r),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=E,x.prototype={constructor:x,reset:function(e){var t;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,of(t=this.tryEntries).call(t,S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+yy(n).call(n,1))&&(this[n]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return o.type="throw",o.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,f):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;S(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:E(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},e}function qL(e){return(qL="function"==typeof Pf&&"symbol"==typeof R_?function(e){return typeof e}:function(e){return e&&"function"==typeof Pf&&e.constructor===Pf&&e!==Pf.prototype?"symbol":typeof e})(e)}function HL(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(EM){return void r(EM)}s.done?t(u):Gg.resolve(u).then(n,i)}function KL(e){return function(){var t=this,r=arguments;return new Gg((function(n,i){var a=e.apply(t,r);function o(e){HL(a,n,i,o,s,"next",e)}function s(e){HL(a,n,i,o,s,"throw",e)}o(void 0)}))}}function WL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function YL(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Df(e,cC(n.key),n)}}function JL(e,t,r){return t&&YL(e.prototype,t),r&&YL(e,r),Df(e,"prototype",{writable:!1}),e}function QL(e,t,r){return(t=cC(t))in e?Df(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XL(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Uf(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Df(e,"prototype",{writable:!1}),t&&ZL(e,t)}function $L(e){var t;return($L=Ff?Uy(t=jf).call(t):function(e){return e.__proto__||jf(e)})(e)}function ZL(e,t){var r;return(ZL=Ff?Uy(r=Ff).call(r):function(e,t){return e.__proto__=t,e})(e,t)}function eC(){if("undefined"==typeof Reflect||!Jy)return!1;if(Jy.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Jy(Boolean,[],(function(){}))),!0}catch(e){return!1}}function tC(e,t,r){var n;eC()?tC=Uy(n=Jy).call(n):tC=function(e,t,r){var n=[null];n.push.apply(n,t);var i=new(Uy(Function).apply(e,n));return r&&ZL(i,r.prototype),i};return tC.apply(null,arguments)}function rC(e){var t="function"==typeof NL?new NL:void 0;return rC=function(e){if(null===e||(r=e,-1===wm(n=Function.toString.call(r)).call(n,"[native code]")))return e;var r,n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,i)}function i(){return tC(e,arguments,$L(this).constructor)}return i.prototype=Uf(e.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),ZL(i,e)},rC(e)}function nC(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Dl(e);for(n=0;n<a.length;n++)r=a[n],wm(t).call(t,r)>=0||(i[r]=e[r]);return i}(e,t);if(Pl){var a=Pl(e);for(n=0;n<a.length;n++)r=a[n],wm(t).call(t,r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function iC(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function aC(e){var t=eC();return function(){var r,n=$L(e);if(t){var i=$L(this).constructor;r=Jy(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===Ol(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return iC(e)}(this,r)}}function oC(){var e;"undefined"!=typeof Reflect&&V_?oC=Uy(e=V_).call(e):oC=function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=$L(e)););return e}(e,t);if(n){var i=Wl(n,t);return i.get?i.get.call(arguments.length<3?e:r):i.value}};return oC.apply(this,arguments)}function sC(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function uC(e,t){var r=void 0!==Pf&&z_(e)||e["@@iterator"];if(!r){if(P_(e)||(r=function(e,t){var r;if(e){if("string"==typeof e)return sC(e,t);var n=yy(r=Object.prototype.toString.call(e)).call(r,8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?ib(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?sC(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}function cC(e){var t=function(e,t){if("object"!==Ol(e)||null===e)return e;var r=e[Qy];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ol(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ol(t)?t:String(t)}function lC(){var e,t,r=new Gg((function(r,n){e=r,t=n}));return r.used=!1,r.resolve=function(){return r.used=!0,e.apply(void 0,arguments)},r.reject=function(){return r.used=!0,t.apply(void 0,arguments)},r}function fC(){try{return E_(performance.now(),10)}catch(e){return(new Date).getTime()}}var hC=Ce,dC=Ye,pC=q,vC=jr,gC=TypeError,yC=function(e){return function(t,r,n,i){hC(r);var a=dC(t),o=pC(a),s=vC(a),u=e?s-1:0,c=e?-1:1;if(n<2)for(;;){if(u in o){i=o[u],u+=c;break}if(u+=c,e?u<0:s<=u)throw gC("Reduce of empty array with no initial value")}for(;e?u>=0:s>u;u+=c)u in o&&(i=r(i,o[u],u,a));return i}},mC={left:yC(!1),right:yC(!0)}.left;Ar({target:"Array",proto:!0,forced:!od&&pe>79&&pe<83||!Jl("reduce")},{reduce:function(e){var t=arguments.length;return mC(this,e,t,t>1?arguments[1]:void 0)}});var _C,bC=Hn("Array").reduce,wC=c,kC=bC,SC=Array.prototype,xC=t((function(e){var t=e.reduce;return e===SC||wC(SC,e)&&t===SC.reduce?kC:t})),EC=function(){function e(){WL(this,e)}return JL(e,null,[{key:"start",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6||1===e.length&&e.start(0)<0?0:e.start(0):0}},{key:"end",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6?0:e.end(e.length-1):0}},{key:"get",value:function(e){if(e)try{return e.buffered}catch(EM){}}},{key:"buffers",value:function(e,t){if(!e||!e.length)return[];for(var r=[],n=0,i=e.length;n<i;n++){var a=r.length;if(a&&t){var o=r[a-1],s=o[1];if(e.start(n)-s<=t){var u=e.end(n);u>s&&(o[1]=u)}else r.push([e.start(n),e.end(n)])}else r.push([e.start(n),e.end(n)])}return r}},{key:"totalLength",value:function(e){return e&&e.length?xC(e).call(e,(function(e,t){return e+(t[1]-t[0])}),0):0}},{key:"info",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!t||!t.length)return{start:0,end:0,buffers:[]};for(var i=0,a=0,o=0,s=0,u=0,c=0,l=0,f=e.buffers(t,n),h=0,d=f.length;h<d;h++){var p=f[h];if(r+n>=p[0]&&r<=p[1])i=p[0],a=p[1],o=h;else{if(r+n<p[0]){s=p[0],u=p[1];break}r+n>p[1]&&(c=p[0],l=p[1])}}return{start:i,end:a,index:o,buffers:f,nextStart:s,nextEnd:u,prevStart:c,prevEnd:l,currentTime:r,behind:r-i,remaining:a?a-r:0,length:e.totalLength&&e.totalLength(f)}}}]),e}(),TC="network",AC="network_timeout",OC="network_forbidden",LC="network_notfound",CC="network_range_not_satisfiable",DC="demux",PC="remux",RC="media",UC="drm",IC="other",MC="runtime",BC={FLV:"FLV",HLS:"HLS",MP4:"MP4",FMP4:"FMP4",MSE_ADD_SB:"MSE_ADD_SB",MSE_APPEND_BUFFER:"MSE_APPEND_BUFFER",MSE_OTHER:"MSE_OTHER",MSE_FULL:"MSE_FULL",OPTION:"OPTION",DASH:"DASH",LICENSE:"LICENSE",CUSTOM_LICENSE:"CUSTOM_LICENSE",MSE_HIJACK:"MSE_HIJACK",EME_HIJACK:"EME_HIJACK",SIDX:"SIDX",NO_CANPLAY_ERROR:"NO_CANPLAY_ERROR",BUFFERBREAK_ERROR:"BUFFERBREAK_ERROR",WAITING_TIMEOUT_ERROR:"WAITING_TIMEOUT_ERROR",MEDIA_ERR_ABORTED:"MEDIA_ERR_ABORTED",MEDIA_ERR_NETWORK:"MEDIA_ERR_NETWORK",MEDIA_ERR_DECODE:"MEDIA_ERR_DECODE",MEDIA_ERR_SRC_NOT_SUPPORTED:"MEDIA_ERR_SRC_NOT_SUPPORTED",MEDIA_ERR_CODEC_NOT_SUPPORTED:"MEDIA_ERR_CODEC_NOT_SUPPORTED",MEDIA_ERR_URL_EMPTY:"MEDIA_ERR_URL_EMPTY"},jC=(QL(_C={},"manifest",{HLS:1100,DASH:1200}),QL(_C,TC,2100),QL(_C,AC,2101),QL(_C,OC,2103),QL(_C,LC,2104),QL(_C,CC,2116),QL(_C,DC,{FLV:3100,HLS:3200,MP4:3300,FMP4:3400,SIDX:3410}),QL(_C,PC,{FMP4:4100,MP4:4200}),QL(_C,RC,{MEDIA_ERR_ABORTED:5101,MEDIA_ERR_NETWORK:5102,MEDIA_ERR_DECODE:5103,MEDIA_ERR_SRC_NOT_SUPPORTED:5104,MEDIA_ERR_CODEC_NOT_SUPPORTED:5105,MEDIA_ERR_URL_EMPTY:5106,MSE_ADD_SB:5200,MSE_APPEND_BUFFER:5201,MSE_OTHER:5202,MSE_FULL:5203,MSE_HIJACK:5204,EME_HIJACK:5301}),QL(_C,UC,{LICENSE:7100,CUSTOM_LICENSE:7200}),QL(_C,IC,8e3),QL(_C,MC,{NO_CANPLAY_ERROR:9001,BUFFERBREAK_ERROR:9002,WAITING_TIMEOUT_ERROR:9003}),_C),FC=function(e){XL(r,e);var t=aC(r);function r(e,n,i,a,o){var s;return WL(this,r),(s=t.call(this,o||(null==i?void 0:i.message))).errorType=e===AC?TC:e,s.originError=i,s.ext=a,s.errorCode=jC[e][n]||jC[e],s.errorMessage=s.message,s.errorCode||(s.errorType=IC,s.errorCode=jC[s.errorType]),s}return JL(r,null,[{key:"create",value:function(e,t,n,i,a){return e instanceof r?e:(e instanceof Error&&(n=e,e=""),e||(e=IC),new r(e,t,n,i,a))}},{key:"network",value:function(e){var t;return new r(null!=e&&e.isTimeout?AC:TC,null,e instanceof Error?e:null,{url:null==e?void 0:e.url,response:null==e?void 0:e.response,httpCode:null==e||null===(t=e.response)||void 0===t?void 0:t.status})}}]),r}(rC(Error)),NC="undefined"!=typeof window,VC=1,zC=2,GC=3,qC=4,HC=["Boolean","Number","String","Undefined","Null","Date","Object"],KC=function(){function e(t,r){WL(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),this.logCacheLevel=(null==r?void 0:r.logCacheLevel)||3,this.logMaxSize=(null==r?void 0:r.logMaxSize)||204800,this.logSize=0,this.logTextArray=[]}return JL(e,[{key:"debug",value:function(){for(var t,r,n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];this.logCache.apply(this,Qn(t=[VC]).call(t,a)),e.disabled||(n=console).debug.apply(n,Qn(r=[this._prefix,WC()]).call(r,a))}},{key:"log",value:function(){for(var t,r,n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];this.logCache.apply(this,Qn(t=[zC]).call(t,a)),e.disabled||(n=console).log.apply(n,Qn(r=[this._prefix,WC()]).call(r,a))}},{key:"warn",value:function(){for(var t,r,n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];this.logCache.apply(this,Qn(t=[GC]).call(t,a)),e.disabled||(n=console).warn.apply(n,Qn(r=[this._prefix,WC()]).call(r,a))}},{key:"error",value:function(){for(var t,r,n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];this.logCache.apply(this,Qn(t=[qC]).call(t,a)),e.disabled||(n=console).error.apply(n,Qn(r=[this._prefix,WC()]).call(r,a))}},{key:"logCache",value:function(e){if(!(e<this.logCacheLevel)){var t="";try{for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];var a=D_(n).call(n,(function(e){return JC(e)}));t=this._prefix+WC()+UT(a)}catch(s){return}if(e>=this.logCacheLevel&&(this.logSize+=t.length,this.logTextArray.push(t)),this.logSize>this.logMaxSize){var o=this.logTextArray.shift();this.logSize-=o.length}}}},{key:"getLogCache",value:function(){var e=this.logTextArray.join("\n");return this.reset(),e}},{key:"reset",value:function(){this.logTextArray=[],this.logSize=0}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}},{key:"setLogLevel",value:function(e){this.logCacheLevel=e}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();function WC(){return(new Date).toLocaleString()}function YC(e){var t;if("object"!==qL(e))return e;var r=yy(t=Object.prototype.toString.call(e)).call(t,8,-1);switch(r){case"Array":case"Uint8Array":case"ArrayBuffer":return r+"["+e.length+"]";case"Object":return"{}";default:return r}}function JC(e,t,r){var n;r||(r=1),t||(t=2);var i={};if(!e||"object"!==qL(e))return e;var a=yy(n=Object.prototype.toString.call(e)).call(n,8,-1);if(!tA(HC).call(HC,a))return a;if(!(r>t)){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(r===t?i[o]=YC(e[o]):"object"===qL(e[o])?i[o]=JC(e[o],t,r+1):i[o]=e[o]);return i}}QL(KC,"disabled",!0);var QC=function(){try{return NC?window.MediaSource:null}catch(e){}}(),XC="appendBuffer",$C="removeBuffer",ZC="updateDuration",eD=function(){function e(t,r){var n=this;WL(this,e),QL(this,"media",null),QL(this,"mediaSource",null),QL(this,"_openPromise",lC()),QL(this,"_queue",Uf(null)),QL(this,"_sourceBuffer",Uf(null)),QL(this,"_mseFullFlag",{}),QL(this,"_st",0),QL(this,"_opst",0),QL(this,"_logger",null),QL(this,"_config",null),QL(this,"_url",null),QL(this,"_onSBUpdateEnd",(function(e){var t=n._queue[e];if(t){var r=t[0];if((null==r?void 0:r.opName)!==ZC&&t.shift(),r){var i=fC()-n._opst;n._logger.debug("UpdateEnd",r.opName,i,r.context),r.promise.resolve({name:r.opName,context:r.context,costtime:i}),n._startQueue(e)}}})),QL(this,"_onSBUpdateError",(function(e,t){var r=n._queue[e];if(r){var i=r[0];i&&(n._logger.error("UpdateError",e,i.opName,i.context),i.promise.reject(new FC(RC,BC.MSE_APPEND_BUFFER,t)))}})),this._config=Lb(e.getDefaultConfig(),r),t&&this.bindMedia(t),this._logger=new KC("MSE"),this._config.openLog&&KC.enable()}var t,r,n;return JL(e,[{key:"isOpened",get:function(){var e;return"open"===(null===(e=this.mediaSource)||void 0===e?void 0:e.readyState)}},{key:"url",get:function(){return this._url}},{key:"duration",get:function(){var e;return(null===(e=this.mediaSource)||void 0===e?void 0:e.duration)||-1}},{key:"isEnded",get:function(){return!!this.mediaSource&&"ended"===this.mediaSource.readyState}},{key:"isFull",value:function(t){return t?this._mseFullFlag[t]:this._mseFullFlag[e.VIDEO]}},{key:"updateDuration",value:function(e){var t=this,r=this.mediaSource&&this.mediaSource.duration>e;if(this.mediaSource&&this.mediaSource.duration>e){var n,i=0;if(of(n=Dl(this._sourceBuffer)).call(n,(function(e){try{i=Math.max(t.bufferEnd(e)||0,i)}catch(EM){}})),e<i)return Gg.resolve()}return this._enqueueBlockingOp((function(){t.isEnded?t._logger.debug("[debug mse] setDuration ended"):t.mediaSource&&(t.mediaSource.duration=e,t._logger.debug("[debug mse] setDuration"))}),ZC,{isReduceDuration:r})}},{key:"open",value:function(){var e=this;if(this._openPromise.used&&!this.isOpened&&this.mediaSource){var t=this.mediaSource;t.addEventListener("sourceopen",(function r(){var n=fC()-e._st;e._logger.debug("MSE OPEN",n),t.removeEventListener("sourceopen",r),e._openPromise.resolve({costtime:n})})),this._openPromise=lC()}return this._openPromise}},{key:"bindMedia",value:(n=KL(GL().mark((function e(t){var r,n,i=this;return GL().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.mediaSource&&!this.media){e.next=3;break}return e.next=3,this.unbindMedia();case 3:if(t&&QC){e.next=5;break}throw new Error("Param media or MediaSource does not exist");case 5:return this.media=t,r=this.mediaSource=new QC,this._st=fC(),n=function e(){var n=fC()-i._st;i._logger.debug("MSE OPEN"),r.removeEventListener("sourceopen",e),CT.revokeObjectURL(t.src),i._openPromise.resolve({costtime:n})},r.addEventListener("sourceopen",n),this._url=CT.createObjectURL(r),t.src=this._url,e.abrupt("return",this._openPromise);case 13:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"unbindMedia",value:(r=KL(GL().mark((function e(){var t,r,n,i=this;return GL().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._openPromise.used||this._openPromise.resolve(),t=this.mediaSource){var a,o;if(of(a=Dl(this._queue)).call(a,(function(e){var t=i._queue[e];t&&of(t).call(t,(function(e){var t,r;return null===(t=e.promise)||void 0===t||null===(r=t.resolve)||void 0===r?void 0:r.call(t)}))})),r=!!this.media&&this.media.readyState>=1,n="open"===t.readyState,r&&n)try{t.endOfStream()}catch(EM){}of(o=Dl(this._sourceBuffer)).call(o,(function(e){try{t.removeSourceBuffer(i._sourceBuffer[e])}catch(EM){}}))}if(this.media){this.media.removeAttribute("src");try{this.media.load()}catch(EM){}this.media=null}this.mediaSource=null,this._openPromise=lC(),this._queue=Uf(null),this._sourceBuffer=Uf(null);case 8:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"createSource",value:function(e,t){var r,n;if(!this._sourceBuffer[e]&&this.mediaSource){var i;try{i=this._sourceBuffer[e]=this.mediaSource.addSourceBuffer(t)}catch(EM){throw new FC(RC,BC.MSE_ADD_SB,EM)}i.mimeType=t,i.addEventListener("updateend",Uy(r=this._onSBUpdateEnd).call(r,this,e)),i.addEventListener("error",Uy(n=this._onSBUpdateError).call(n,this,e))}}},{key:"changeType",value:function(e,t){var r=this,n=this._sourceBuffer[e];return this.mediaSource&&n&&n.mimeType!==t?"function"!=typeof n.changeType?Gg.reject():this._enqueueOp(e,(function(){n.changeType(t),n.mimeType=t,r._onSBUpdateEnd(e)}),"changeType",{mimeType:t}):Gg.resolve()}},{key:"createOrChangeSource",value:function(e,t){return this.createSource(e,t),this.changeType(e,t)}},{key:"append",value:function(e,t,r){var n=this;return t&&t.byteLength&&this._sourceBuffer[e]?this._enqueueOp(e,(function(){var i;n.mediaSource&&!n.media.error&&(n._logger.debug("MSE APPEND START",r),n._opst=fC(),null===(i=n._sourceBuffer[e])||void 0===i||i.appendBuffer(t))}),XC,r):Gg.resolve()}},{key:"remove",value:function(e,t,r,n){var i=this,a=!1;return this._mseFullFlag[e]&&(a=!0),this._enqueueOp(e,(function(){if(i.mediaSource&&!i.media.error){var a=i._sourceBuffer[e];t>=r||!a?i._onSBUpdateEnd(e):(i._opst=fC(),i._logger.debug("MSE REMOVE START",e,t,r,n),a.remove(t,r))}}),$C,n,a)}},{key:"clearBuffer",value:function(e,t){var r,n,i=this;return of(r=Dl(this._sourceBuffer)).call(r,(function(r){n=i._enqueueOp(r,(function(){if(i.mediaSource&&!i.media.error){var n=i._sourceBuffer[r];i._logger.debug("MSE clearBuffer START",r,e,t),n.remove(e,t)}}),$C)})),n||Gg.resolve()}},{key:"clearAllBuffer",value:function(){var e,t,r=this;return of(e=Dl(this._sourceBuffer)).call(e,(function(e){t=r._enqueueOp(e,(function(){if(r.mediaSource&&!r.media.error){var t=r._sourceBuffer[e];r._logger.debug("MSE clearAllBuffer START",e),t.remove(0,EC.end(EC.get(t)))}}))})),t}},{key:"clearOpQueues",value:function(e,t){var r;this._logger.debug("MSE clearOpQueue START");var n=this._queue[e];if(t&&n)this._queue[e]=[];else if(n&&n[e]&&!(n.length<5)){var i=[];of(n).call(n,(function(e){e.context&&e.context.isinit&&i.push(e)})),this._queue[e]=yy(n).call(n,0,2),i.length>0&&(r=this._queue[e]).push.apply(r,i)}}},{key:"endOfStream",value:function(e){var t=this;return this.mediaSource&&"open"===this.mediaSource.readyState?this._enqueueBlockingOp((function(){var r=t.mediaSource;r&&"open"===r.readyState&&(t._logger.debug("MSE endOfStream START"),e?r.endOfStream(e):r.endOfStream())}),"endOfStream"):Gg.resolve()}},{key:"setLiveSeekableRange",value:function(e,t){var r=this.mediaSource;e<0||t<e||null==r||!r.setLiveSeekableRange||"open"!==r.readyState||r.setLiveSeekableRange(e,t)}},{key:"getSourceBuffer",value:function(e){return this._sourceBuffer[e]}},{key:"buffered",value:function(e){return EC.get(this._sourceBuffer[e])}},{key:"bufferStart",value:function(e){return EC.start(this.buffered(e))}},{key:"bufferEnd",value:function(e){return EC.end(this.buffered(e))}},{key:"_enqueueOp",value:function(e,t,r,n,i){var a=this;if(!this.mediaSource)return Gg.resolve();var o=this._queue[e]=this._queue[e]||[],s={exec:t,promise:lC(),opName:r,context:n};return i?(r_(o).call(o,0,0,s),this._mseFullFlag[e]=!1,this._startQueue(e)):o.push(s),this.isOpened||this.isEnded?1===o.length&&this._startQueue(e):this._openPromise.then((function(){1===o.length&&a._startQueue(e)})),s.promise}},{key:"_enqueueBlockingOp",value:(t=KL(GL().mark((function e(t,r,n){var i,a,o=this;return GL().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mediaSource){e.next=2;break}return e.abrupt("return",Gg.resolve());case 2:if((i=Dl(this._sourceBuffer)).length){e.next=5;break}return e.abrupt("return",t());case 5:return a=[],of(i).call(i,(function(e){var t=o._queue[e],i=lC();a.push(i),t.push({exec:function(){i.resolve()},promise:i,opName:r,context:n}),1===t.length&&o._startQueue(e)})),e.abrupt("return",Gg.all(a).then((function(){try{return t()}finally{of(i).call(i,(function(e){var t=o._queue[e],r=o._sourceBuffer[e];null==t||t.shift(),r&&r.updating||o._startQueue(e)}))}})));case 8:case"end":return e.stop()}}),e,this)}))),function(e,r,n){return t.apply(this,arguments)})},{key:"_startQueue",value:function(e){var t=this._queue[e];if(t){var r=t[0];if(r&&!this._mseFullFlag[e])try{r.exec()}catch(EM){var n;EM&&EM.message&&wm(n=EM.message).call(n,"SourceBuffer is full")>=0?(this._mseFullFlag[e]=!0,this._logger.error("[MSE error],  context,",r.context," ,name,",r.opName,",err,SourceBuffer is full"),r.promise.reject(new FC(RC,BC.MSE_FULL,EM))):(this._logger.error(EM),r.promise.reject(new FC(RC,BC.MSE_OTHER,EM)),t.shift(),this._startQueue(e))}}}},{key:"setTimeoffset",value:function(e,t,r){var n=this;return this._enqueueOp(e,(function(){t<0&&(t+=.001),n._sourceBuffer[e].timestampOffset=t,n._onSBUpdateEnd(e)}),"setTimeoffset",r)}},{key:"abort",value:function(e,t){var r=this;return this.isOpened?this._enqueueOp(e,(function(){r._sourceBuffer[e].abort(),r._onSBUpdateEnd(e)}),"abort",t):Gg.resolve()}}],[{key:"getDefaultConfig",value:function(){return{openLog:!1}}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if(!QC)return!1;try{return QC.isTypeSupported(e)}catch(EM){return this._logger.error(e,EM),!1}}}]),e}();QL(eD,"VIDEO","video"),QL(eD,"AUDIO","audio");var tD="fetch",rD="xhr",nD="arraybuffer",iD="text",aD="json",oD=function(e){XL(r,e);var t=aC(r);function r(e,n,i,a){var o;return WL(this,r),QL(iC(o=t.call(this,a)),"retryCount",0),QL(iC(o),"isTimeout",!1),QL(iC(o),"loaderType",tD),QL(iC(o),"startTime",0),QL(iC(o),"endTime",0),QL(iC(o),"options",{}),o.url=e,o.request=n,o.response=i,o}return JL(r)}(rC(Error)),sD=Object.prototype.toString;function uD(e){if("[object Object]"!==sD.call(e))return!1;var t=jf(e);return null===t||t===Object.prototype}function cD(e){if(e&&null!==e[0]&&void 0!==e[0]&&(0!==e[0]||null!==e[1]&&void 0!==e[1])){var t="bytes="+e[0]+"-";return e[1]&&(t+=e[1]),t}}function lD(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function fD(e,t){var r,n;if(e){if(!t)return e;var i,a=jl(r=D_(n=Dl(t)).call(n,(function(e){if(null!=(i=t[e]))return P_(i)?e+="[]":i=[i],D_(i).call(i,(function(t){var r,n;return n=t,"[object Date]"===sD.call(n)?t=t.toISOString():function(e){return null!==e&&"object"===qL(e)}(t)&&(t=UT(t)),Qn(r="".concat(lD(e),"=")).call(r,lD(t))})).join("&")}))).call(r,Boolean).join("&");if(a){var o=wm(e).call(e,"#");-1!==o&&(e=yy(e).call(e,0,o)),e+=(-1===wm(e).call(e,"?")?"?":"&")+a}return e}}function hD(e,t,r,n,i,a,o,s,u,c,l){return i=null!=i?Nw(i):null,n=E_(n||"0",10),n_(n)&&(n=0),{data:e,done:t,options:{range:u,vid:c,index:s,contentLength:n,age:i,startTime:a,firstByteTime:o,endTime:Bk(),priOptions:l},response:r}}function dD(e,t){return Math.round(8*e*1e3/t/1024)}var pD="error",vD="core.ttfb",gD="core.loadstart",yD="core.loadresponseheaders",mD="core.loadcomplete",_D="core.loadretry",bD="core.sourcebuffercreated",wD="core.analyzedurationexceeded",kD="core.removebuffer",SD="core.buffereos",xD="core.keyframe",ED="core.metadataparsed",TD="core.sei",AD="core.seiintime",OD="core.flvscriptdata",LD="core.lowdecode",CD="core.switchurlsuccess",DD="core.switchurlfailed",PD="core.demuxedtrack",RD="core.streamexception",UD="LARGE_AV_FIRST_FRAME_GAP_DETECT",ID="LARGE_VIDEO_DTS_GAP_DETECT",MD="LARGE_AUDIO_DTS_GAP_DETECT",BD="AUDIO_GAP_DETECT",jD="AUDIO_OVERLAP_DETECT",FD="MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT",ND="real_time_speed",VD=2097152,zD=function(e){XL(n,e);var t,r=aC(n);function n(){var e;return WL(this,n),QL(iC(e=r.call(this)),"_abortController",null),QL(iC(e),"_timeoutTimer",null),QL(iC(e),"_reader",null),QL(iC(e),"_response",null),QL(iC(e),"_aborted",!1),QL(iC(e),"_index",-1),QL(iC(e),"_range",null),QL(iC(e),"_receivedLength",0),QL(iC(e),"_running",!1),QL(iC(e),"_logger",null),QL(iC(e),"_vid",""),QL(iC(e),"_onProcessMinLen",0),QL(iC(e),"_onCancel",null),QL(iC(e),"_priOptions",null),e}return JL(n,[{key:"load",value:function(e){var t,r=this,n=e.url,i=e.vid,a=e.timeout,o=e.responseType,s=e.onProgress,u=e.index,c=e.onTimeout,l=e.onCancel,f=e.range,h=e.transformResponse,d=e.request,p=e.params,v=e.logger,g=e.method,y=e.headers,m=e.body,_=e.mode,b=e.credentials,w=e.cache,k=e.redirect,S=e.referrer,x=e.referrerPolicy,E=e.onProcessMinLen,T=e.priOptions;this._logger=v,this._aborted=!1,this._onProcessMinLen=E,this._onCancel=l,this._abortController="undefined"!=typeof AbortController&&new AbortController,this._running=!0,this._index=u,this._range=f||[0,0],this._vid=i||n,this._priOptions=T||{};var A={method:g,headers:y,body:m,mode:_,credentials:b,cache:w,redirect:k,referrer:S,referrerPolicy:x,signal:null===(t=this._abortController)||void 0===t?void 0:t.signal},O=!1;clearTimeout(this._timeoutTimer),n=fD(n,p);var L=cD(f);L&&(y=d?d.headers:A.headers=A.headers||(Headers?new Headers:{}),Headers&&y instanceof Headers?y.append("Range",L):y.Range=L),a&&(this._timeoutTimer=Sw((function(){if(O=!0,r.cancel(),c){var e=new oD(n,A,null,"timeout");e.isTimeout=!0,c(e,{index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions})}}),a));var C=Bk();return this._logger.debug("[fetch load start], index,",u,",range,",f),new Gg((function(e,t){fetch(d||n,d?void 0:A).then(function(){var i=KL(GL().mark((function i(a){var c,l,d,p;return GL().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(clearTimeout(r._timeoutTimer),r._response=a,!r._aborted&&r._running){i.next=4;break}return i.abrupt("return");case 4:if(h&&(a=h(a,n)||a),a.ok){i.next=7;break}throw new oD(n,A,a,"bad network response");case 7:if(c=Bk(),o!==iD){i.next=15;break}return i.next=11,a.text();case 11:l=i.sent,r._running=!1,i.next=37;break;case 15:if(o!==aD){i.next=22;break}return i.next=18,a.json();case 18:l=i.sent,r._running=!1,i.next=37;break;case 22:if(!s){i.next=29;break}return r.resolve=e,r.reject=t,r._loadChunk(a,s,C,c),i.abrupt("return");case 29:return i.next=31,a.arrayBuffer();case 31:l=i.sent,l=new Uint8Array(l),r._running=!1,d=Bk()-C,p=dD(l.byteLength,d),r.emit(ND,{speed:p,len:l.byteLength,time:d,vid:r._vid,index:r._index,range:r._range,priOptions:r._priOptions});case 37:r._logger.debug("[fetch load end], index,",u,",range,",f),e(hD(l,!0,a,a.headers.get("Content-Length"),a.headers.get("age"),C,c,u,f,r._vid,r._priOptions));case 39:case"end":return i.stop()}}),i)})));return function(e){return i.apply(this,arguments)}}()).catch((function(e){var i;clearTimeout(r._timeoutTimer),r._running=!1,r._aborted&&!O||((e=e instanceof oD?e:new oD(n,A,null,null===(i=e)||void 0===i?void 0:i.message)).startTime=C,e.endTime=Bk(),e.isTimeout=O,e.options={index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions},t(e))}))}))}},{key:"cancel",value:(t=KL(GL().mark((function e(){return GL().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._aborted){e.next=2;break}return e.abrupt("return");case 2:if(this._aborted=!0,this._running=!1,!this._response){e.next=14;break}if(e.prev=5,!this._reader){e.next=9;break}return e.next=9,this._reader.cancel();case 9:e.next=13;break;case 11:e.prev=11,e.t0=e.catch(5);case 13:this._response=this._reader=null;case 14:if(this._abortController){try{this._abortController.abort()}catch(EM){}this._abortController=null}this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions});case 16:case"end":return e.stop()}}),e,this,[[5,11]])}))),function(){return t.apply(this,arguments)})},{key:"_loadChunk",value:function(e,t,r,n){var i=this;if(!e.body||!e.body.getReader){this._running=!1;var a=new oD(e.url,"",e,"onProgress of bad response.body.getReader");return a.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},void this.reject(a)}this._onProcessMinLen>0&&(this._cache=new Uint8Array(VD),this._writeIdx=0);var o,s,u,c=this._reader=e.body.getReader(),l=function(){var a=KL(GL().mark((function a(){var f,h,d,p,v,g,y,m;return GL().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return s=Bk(),a.prev=1,a.next=4,c.read();case 4:o=a.sent,u=Bk(),a.next=13;break;case 8:return a.prev=8,a.t0=a.catch(1),u=Bk(),i._aborted||(i._running=!1,a.t0.options={index:i._index,range:i._range,vid:i._vid,priOptions:i._priOptions},i.reject(a.t0)),a.abrupt("return");case 13:if(h=(null===(f=i._range)||void 0===f?void 0:f.length)>0?i._range[0]:0,d=h+i._receivedLength,!i._aborted){a.next=19;break}return i._running=!1,t(void 0,!1,{range:[d,d],vid:i._vid,index:i._index,startTime:s,endTime:u,st:r,firstByteTime:n,priOptions:i._priOptions},e),a.abrupt("return");case 19:var _;if(p=o.value?o.value.byteLength:0,i._receivedLength+=p,i._logger.debug("【fetchLoader,onProgress call】,task,",i._range,", start,",d,", end,",h+i._receivedLength,", done,",o.done),i._onProcessMinLen>0){if(i._writeIdx+p>=i._onProcessMinLen||o.done)(v=new Uint8Array(i._writeIdx+p)).set(yy(_=i._cache).call(_,0,i._writeIdx),0),p>0&&v.set(o.value,i._writeIdx),i._writeIdx=0,i._logger.debug("【fetchLoader,onProgress enough】,done,",o.done,",len,",v.byteLength,", writeIdx,",i._writeIdx);else if(p>0&&i._writeIdx+p<VD)i._cache.set(o.value,i._writeIdx),i._writeIdx+=p,i._logger.debug("【fetchLoader,onProgress cache】,len,",p,", writeIdx,",i._writeIdx);else if(p>0){var b;g=new Uint8Array(i._writeIdx+p+2048),i._logger.debug("【fetchLoader,onProgress extra start】,size,",i._writeIdx+p+2048,", datalen,",p,", writeIdx,",i._writeIdx),g.set(yy(b=i._cache).call(b,0,i._writeIdx),0),p>0&&g.set(o.value,i._writeIdx),i._writeIdx+=p,delete i._cache,i._cache=g,i._logger.debug("【fetchLoader,onProgress extra end】,len,",p,", writeIdx,",i._writeIdx)}}else v=o.value;(v&&v.byteLength>0||o.done)&&t(v,o.done,{range:[i._range[0]+i._receivedLength-(v?v.byteLength:0),i._range[0]+i._receivedLength],vid:i._vid,index:i._index,startTime:s,endTime:u,st:r,firstByteTime:n,priOptions:i._priOptions},e),o.done?(y=Bk()-r,m=dD(i._receivedLength,y),i.emit(ND,{speed:m,len:i._receivedLength,time:y,vid:i._vid,index:i._index,range:i._range,priOptions:i._priOptions}),i._running=!1,i._logger.debug("[fetchLoader onProgress end],task,",i._range,",done,",o.done),i.resolve(hD(o,!0,e,e.headers.get("Content-Length"),e.headers.get("age"),r,n,i._index,i._range,i._vid,i._priOptions))):l();case 25:case"end":return a.stop()}}),a,null,[[1,8]])})));return function(){return a.apply(this,arguments)}}();l()}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}}],[{key:"isSupported",value:function(){return!("undefined"==typeof fetch)}}]),n}(Db);var GD=function(e){XL(r,e);var t=aC(r);function r(){var e;return WL(this,r),QL(iC(e=t.call(this)),"_xhr",null),QL(iC(e),"_aborted",!1),QL(iC(e),"_timeoutTimer",null),QL(iC(e),"_range",null),QL(iC(e),"_receivedLength",0),QL(iC(e),"_url",null),QL(iC(e),"_onProgress",null),QL(iC(e),"_index",-1),QL(iC(e),"_headers",null),QL(iC(e),"_currentChunkSizeKB",384),QL(iC(e),"_timeout",null),QL(iC(e),"_xhr",null),QL(iC(e),"_withCredentials",null),QL(iC(e),"_startTime",-1),QL(iC(e),"_loadCompleteResolve",null),QL(iC(e),"_loadCompleteReject",null),QL(iC(e),"_runing",!1),QL(iC(e),"_logger",!1),QL(iC(e),"_vid",""),QL(iC(e),"_responseType",void 0),QL(iC(e),"_credentials",void 0),QL(iC(e),"_method",void 0),QL(iC(e),"_transformResponse",void 0),QL(iC(e),"_firstRtt",void 0),QL(iC(e),"_onCancel",null),QL(iC(e),"_priOptions",null),e}return JL(r,[{key:"load",value:function(e){var t=this;clearTimeout(this._timeoutTimer),this._logger=e.logger,this._range=e.range,this._onProgress=e.onProgress,this._index=e.index,this._headers=e.headers,this._withCredentials="include"===e.credentials||"same-origin"===e.credentials,this._body=e.body||null,e.method&&(this._method=e.method),this._timeout=e.timeout||null,this._runing=!0,this._vid=e.vid||e.url,this._responseType=e.responseType,this._firstRtt=-1,this._onTimeout=e.onTimeout,this._onCancel=e.onCancel,this._request=e.request,this._priOptions=e.priOptions||{},this._logger.debug("【xhrLoader task】, range",this._range),this._url=fD(e.url,e.params);var r=Bk();return new Gg((function(e,r){t._loadCompleteResolve=e,t._loadCompleteReject=r,t._startLoad()})).catch((function(e){if(clearTimeout(t._timeoutTimer),t._runing=!1,!t._aborted)throw(e=e instanceof oD?e:new oD(t._url,t._request)).startTime=r,e.endTime=Bk(),e.options={index:t._index,vid:t._vid,priOptions:t._priOptions},e}))}},{key:"_startLoad",value:function(){var e=null;if(this._responseType===nD&&this._range&&this._range.length>1)if(this._onProgress){this._firstRtt=-1;var t=1024*this._currentChunkSizeKB,r=this._range[0]+this._receivedLength,n=this._range[1];t<this._range[1]-r&&(n=r+t),e=[r,n],this._logger.debug("[xhr_loader->],tast :",this._range,", SubRange, ",e)}else e=this._range,this._logger.debug("[xhr_loader->],tast :",this._range,", allRange, ",e);this._internalOpen(e)}},{key:"_internalOpen",value:function(e){var t=this;try{var r,n;this._startTime=Bk();var i=this._xhr=new XMLHttpRequest;i.open(this._method||"GET",this._url,!0),i.responseType=this._responseType,this._timeout&&(i.timeout=this._timeout),i.withCredentials=this._withCredentials,i.onload=Uy(r=this._onLoad).call(r,this),i.onreadystatechange=Uy(n=this._onReadyStatechange).call(n,this),i.onerror=function(e){var r,n,i;t._running=!1;var a=new oD(t._url,t._request,null==e||null===(r=e.currentTarget)||void 0===r?void 0:r.response,"xhr.onerror.status:"+(null==e||null===(n=e.currentTarget)||void 0===n?void 0:n.status)+",statusText,"+(null==e||null===(i=e.currentTarget)||void 0===i?void 0:i.statusText));a.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(a)},i.ontimeout=function(e){t.cancel();var r=new oD(t._url,t._request,{status:408},"timeout");t._onTimeout&&(r.isTimeout=!0,t._onTimeout(r,{index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions})),r.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(r)};var a,o=this._headers||{},s=cD(e);if(s&&(o.Range=s),o)of(a=Dl(o)).call(a,(function(e){i.setRequestHeader(e,o[e])}));this._logger.debug("[xhr.send->] tast,",this._range,",load sub range, ",e),i.send(this._body)}catch(u){u.options={index:this._index,range:e,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(u)}}},{key:"_onReadyStatechange",value:function(e){2===e.target.readyState&&this._firstRtt<0&&(this._firstRtt=Bk())}},{key:"_onLoad",value:function(e){var t,r=e.target.status;if(r<200||r>299){var n=new oD(this._url,null,zL(zL({},e.target.response),{},{status:r}),"bad response,status:"+r);return n.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(n)}var i,a=null,o=!1,s=(null===(t=this._range)||void 0===t?void 0:t.length)>0?this._range[0]:0;if(this._responseType===nD){var u,c=new Uint8Array(e.target.response);if(i=s+this._receivedLength,c&&c.byteLength>0){this._receivedLength+=c.byteLength;var l=Bk()-this._startTime,f=dD(this._receivedLength,l);this.emit(ND,{speed:f,len:this._receivedLength,time:l,vid:this._vid,index:this._index,range:[i,s+this._receivedLength],priOptions:this._priOptions})}a=c,o=!((null===(u=this._range)||void 0===u?void 0:u.length)>1&&this._range[1]&&this._receivedLength<this._range[1]-this._range[0]),this._logger.debug("[xhr load done->], tast :",this._range,", start",i,"end ",s+this._receivedLength,",dataLen,",c?c.byteLength:0,",receivedLength",this._receivedLength,",index,",this._index,", done,",o)}else o=!0,a=e.target.response;var h={ok:r>=200&&r<300,status:r,statusText:this._xhr.statusText,url:this._xhr.responseURL,headers:this._getHeaders(this._xhr),body:this._xhr.response};this._transformResponse&&(h=this._transformResponse(h,this._url)||h),this._onProgress&&this._onProgress(a,o,{index:this._index,vid:this._vid,range:[i,s+this._receivedLength],startTime:this._startTime,endTime:Bk(),priOptions:this._priOptions},h),o?(this._runing=!1,this._loadCompleteResolve&&this._loadCompleteResolve(hD(this._onProgress?null:a,o,h,h.headers["content-length"],h.headers.age,this._startTime,this._firstRtt,this._index,this._range,this._vid,this._priOptions))):this._startLoad()}},{key:"cancel",value:function(){if(!this._aborted)return this._aborted=!0,this._runing=!1,oC($L(r.prototype),"removeAllListeners",this).call(this),this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions}),this._xhr?this._xhr.abort():void 0}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}},{key:"_getHeaders",value:function(e){var t,r,n={},i=uC(Jw(t=e.getAllResponseHeaders()).call(t).split("\r\n"));try{for(i.s();!(r=i.n()).done;){var a=r.value.split(": ");n[a[0].toLowerCase()]=yy(a).call(a,1).join(": ")}}catch(o){i.e(o)}finally{i.f()}return n}}],[{key:"isSupported",value:function(){return"undefined"!=typeof XMLHttpRequest}}]),r}(Db),qD=["retry","retryDelay","onRetryError","transformError"],HD=function(){function e(t,r){WL(this,e),this.promise=lC(),this.alive=!!r.onProgress,!r.logger&&(r.logger=new KC("Loader")),this._loaderType=t,this._loader=t===tD&&window.fetch?new zD:new GD,this._config=r,this._retryCount=0,this._retryTimer=null,this._canceled=!1,this._retryCheckFunc=r.retryCheckFunc,this._logger=r.logger}var t;return JL(e,[{key:"exec",value:function(){var e=this,t=this._config,r=t.retry,n=t.retryDelay,i=t.onRetryError,a=t.transformError,o=nC(t,qD),s=function(){var t=KL(GL().mark((function t(){var u,c,l;return GL().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e._loader.load(o);case 3:u=t.sent,e.promise.resolve(u),t.next=27;break;case 7:if(t.prev=7,t.t0=t.catch(0),e._loader.running=!1,e._logger.debug("[task request catch err]",t.t0),!e._canceled){t.next=13;break}return t.abrupt("return");case 13:if(t.t0.loaderType=e._loaderType,t.t0.retryCount=e._retryCount,c=t.t0,a&&(c=a(c)||c),i&&e._retryCount>0&&i(c,e._retryCount,{index:o.index,vid:o.vid,range:o.range,priOptions:o.priOptions}),e._retryCount++,l=!0,e._retryCheckFunc&&(l=e._retryCheckFunc(t.t0)),!(l&&e._retryCount<=r)){t.next=26;break}return clearTimeout(e._retryTimer),e._logger.debug("[task request setTimeout],retry",e._retryCount,",retry range,",o.range),e._retryTimer=Sw(s,n),t.abrupt("return");case 26:e.promise.reject(c);case 27:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(){return t.apply(this,arguments)}}();return s(),this.promise}},{key:"cancel",value:(t=KL(GL().mark((function e(){return GL().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return clearTimeout(this._retryTimer),this._canceled=!0,this._loader.running=!1,e.abrupt("return",this._loader.cancel());case 4:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"running",get:function(){return this._loader&&this._loader.running}},{key:"loader",get:function(){return this._loader}}]),e}();function KD(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if((t=jl(t).call(t,Boolean)).length<2)return t[0];var n=new Uint8Array(xC(t).call(t,(function(e,t){return e+t.byteLength}),0)),i=0;return of(t).call(t,(function(e){n.set(e,i),i+=e.byteLength})),n}function WD(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new Gg((function(t){return Sw(t,e)}))}var YD=function(e){XL(n,e);var t,r=aC(n);function n(e){var t;return WL(this,n),QL(iC(t=r.call(this,e)),"type",tD),QL(iC(t),"_queue",[]),QL(iC(t),"_alive",[]),QL(iC(t),"_currentTask",null),QL(iC(t),"_finnalUrl",""),QL(iC(t),"_config",void 0),t._config=function(e){return zL({loaderType:tD,retry:0,retryDelay:0,timeout:0,request:null,onTimeout:void 0,onProgress:void 0,onRetryError:void 0,transformRequest:void 0,transformResponse:void 0,transformError:void 0,responseType:iD,range:void 0,url:"",params:void 0,method:"GET",headers:{},body:void 0,mode:void 0,credentials:void 0,cache:void 0,redirect:void 0,referrer:void 0,referrerPolicy:void 0,integrity:void 0,onProcessMinLen:0},e)}(e),t._config.loaderType!==rD&&zD.isSupported()||(t.type=rD),t.log=e.logger,t}return JL(n,[{key:"isFetch",value:function(){return this.type===tD}},{key:"load",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"string"!=typeof e&&e?r=e:r.url=e||r.url||this._config.url,(r=Lb({},this._config,r)).params&&(r.params=Lb({},r.params)),r.headers&&uD(r.headers)&&(r.headers=Lb({},r.headers)),r.body&&uD(r.body)&&(r.body=Lb({},r.body)),r.transformRequest&&(r=r.transformRequest(r)||r),r.logger=this.log;var n=new HD(this.type,r);return n.loader.on(ND,(function(e){t.emit(ND,e)})),this._queue.push(n),1!==this._queue.length||this._currentTask&&this._currentTask.running||this._processTask(),n.promise}},{key:"cancel",value:(t=KL(GL().mark((function e(){var t;return GL().wrap((function(e){for(var r,n,i;;)switch(e.prev=e.next){case 0:return t=Qn(r=D_(n=this._queue).call(n,(function(e){return e.cancel()}))).call(r,D_(i=this._alive).call(i,(function(e){return e.cancel()}))),this._currentTask&&t.push(this._currentTask.cancel()),this._queue=[],this._alive=[],e.next=6,Gg.all(t);case 6:return e.next=8,WD();case 8:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"_processTask",value:function(){var e=this;if(this._currentTask=this._queue.shift(),this._currentTask){this._currentTask.alive&&this._alive.push(this._currentTask);var t=this._currentTask.exec().catch((function(e){}));t&&"function"==typeof t.finally&&t.finally((function(){var t,r,n;null!==(t=e._currentTask)&&void 0!==t&&t.alive&&(null===(r=e._alive)||void 0===r?void 0:r.length)>0&&(e._alive=jl(n=e._alive).call(n,(function(t){return t&&t!==e._currentTask})));e._processTask()}))}}}],[{key:"isFetchSupport",value:function(){return zD.isSupported()}}]),n}(Db),JD=function(){function e(){WL(this,e),QL(this,"_prevCurrentTime",0)}return JL(e,[{key:"do",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2?arguments[2]:void 0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(e){var i=e.currentTime,a=0;if(this._prevCurrentTime===i){var o=EC.info(EC.get(e),i);if(!o.buffers.length)return;r&&o.nextStart||o.nextStart&&o.nextStart-i<t?a=o.nextStart+.1:o.end&&o.end-i>n&&!e.seeking&&(a=i+.1)}this._prevCurrentTime=i,a&&i!==a&&(e.currentTime=a)}}}]),e}();zA("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),nO);var QD=te.Set,XD=Te,$D=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"add"in e&&"delete"in e&&"keys"in e)return e;throw TypeError(XD(e)+" is not a set")},ZD=yO,eP=oe("Set"),tP=eP.prototype,rP={Set:eP,add:ZD("add",1),has:ZD("has",1),remove:ZD("delete",1),proto:tP},nP=OO,iP=function(e,t,r){return r?nP(e.keys(),t):e.forEach(t)},aP=iP,oP=rP.Set,sP=rP.add,uP=function(e){var t=new oP;return aP(e,(function(e){sP(t,e)})),t},cP=function(e){return e.size},lP=Ce,fP=tr,hP=P,dP=Rr,pP=TypeError,vP=Math.max,gP=function(e,t,r,n){this.set=e,this.size=t,this.has=r,this.keys=n};gP.prototype={getIterator:function(){return fP(hP(this.keys,this.set))},includes:function(e){return hP(this.has,this.set,e)}};var yP=function(e){fP(e);var t=+e.size;if(t!=t)throw pP("Invalid size");return new gP(e,vP(dP(t),0),lP(e.has),lP(e.keys))},mP=$D,_P=uP,bP=cP,wP=yP,kP=iP,SP=OO,xP=rP.has,EP=rP.remove,TP=function(e){var t=mP(this),r=wP(e),n=_P(t);return bP(t)<=r.size?kP(t,(function(e){r.includes(e)&&EP(n,e)})):SP(r.getIterator(),(function(e){xP(t,e)&&EP(n,e)})),n},AP=function(){return!1},OP=TP;Ar({target:"Set",proto:!0,real:!0,forced:!AP()},{difference:OP});var LP=$D,CP=cP,DP=yP,PP=iP,RP=OO,UP=rP.Set,IP=rP.add,MP=rP.has,BP=function(e){var t=LP(this),r=DP(e),n=new UP;return CP(t)>r.size?RP(r.getIterator(),(function(e){MP(t,e)&&IP(n,e)})):PP(t,(function(e){r.includes(e)&&IP(n,e)})),n},jP=BP;Ar({target:"Set",proto:!0,real:!0,forced:!AP()},{intersection:jP});var FP=$D,NP=rP.has,VP=cP,zP=yP,GP=iP,qP=OO,HP=Sh,KP=function(e){var t=FP(this),r=zP(e);if(VP(t)<=r.size)return!1!==GP(t,(function(e){if(r.includes(e))return!1}),!0);var n=r.getIterator();return!1!==qP(n,(function(e){if(NP(t,e))return HP(n,"normal",!1)}))},WP=KP;Ar({target:"Set",proto:!0,real:!0,forced:!AP()},{isDisjointFrom:WP});var YP=$D,JP=cP,QP=iP,XP=yP,$P=function(e){var t=YP(this),r=XP(e);return!(JP(t)>r.size)&&!1!==QP(t,(function(e){if(!r.includes(e))return!1}),!0)},ZP=$P;Ar({target:"Set",proto:!0,real:!0,forced:!AP()},{isSubsetOf:ZP});var eR=$D,tR=rP.has,rR=cP,nR=yP,iR=OO,aR=Sh,oR=function(e){var t=eR(this),r=nR(e);if(rR(t)<r.size)return!1;var n=r.getIterator();return!1!==iR(n,(function(e){if(!tR(t,e))return aR(n,"normal",!1)}))},sR=oR;Ar({target:"Set",proto:!0,real:!0,forced:!AP()},{isSupersetOf:sR});var uR=$D,cR=uP,lR=yP,fR=OO,hR=rP.add,dR=rP.has,pR=rP.remove,vR=function(e){var t=uR(this),r=lR(e).getIterator(),n=cR(t);return fR(r,(function(e){dR(t,e)?pR(n,e):hR(n,e)})),n},gR=vR;Ar({target:"Set",proto:!0,real:!0,forced:!AP()},{symmetricDifference:gR});var yR=$D,mR=rP.add,_R=uP,bR=yP,wR=OO,kR=function(e){var t=yR(this),r=bR(e).getIterator(),n=_R(t);return wR(r,(function(e){mR(n,e)})),n},SR=kR;Ar({target:"Set",proto:!0,real:!0,forced:!AP()},{union:SR});var xR=QD;Ar({target:"Set",stat:!0,forced:!0},{from:hO}),Ar({target:"Set",stat:!0,forced:!0},{of:pO});var ER=$D,TR=rP.add;Ar({target:"Set",proto:!0,real:!0,forced:!0},{addAll:function(){for(var e=ER(this),t=0,r=arguments.length;t<r;t++)TR(e,arguments[t]);return e}});var AR=$D,OR=rP.remove;Ar({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=AR(this),r=!0,n=0,i=arguments.length;n<i;n++)e=OR(t,arguments[n]),r=r&&e;return!!r}});var LR=Jt,CR=$D,DR=iP;Ar({target:"Set",proto:!0,real:!0,forced:!0},{every:function(e){var t=CR(this),r=LR(e,arguments.length>1?arguments[1]:void 0);return!1!==DR(t,(function(e){if(!r(e,e,t))return!1}),!0)}});var PR=Zr,RR=Xe,UR=H,IR=gu,MR=ft("iterator"),BR=Object,jR=A,FR=function(e){if(UR(e))return!1;var t=BR(e);return void 0!==t[MR]||"@@iterator"in t||RR(IR,PR(t))},NR=ee,VR=oe("Set"),zR=function(e){return function(e){return NR(e)&&"number"==typeof e.size&&jR(e.has)&&jR(e.keys)}(e)?e:FR(e)?new VR(e):e},GR=P,qR=zR,HR=TP;Ar({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(e){return GR(HR,this,qR(e))}});var KR=Jt,WR=$D,YR=iP,JR=rP.Set,QR=rP.add;Ar({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(e){var t=WR(this),r=KR(e,arguments.length>1?arguments[1]:void 0),n=new JR;return YR(t,(function(e){r(e,e,t)&&QR(n,e)})),n}});var XR=Jt,$R=$D,ZR=iP;Ar({target:"Set",proto:!0,real:!0,forced:!0},{find:function(e){var t=$R(this),r=XR(e,arguments.length>1?arguments[1]:void 0),n=ZR(t,(function(e){if(r(e,e,t))return{value:e}}),!0);return n&&n.value}});var eU=P,tU=zR,rU=BP;Ar({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(e){return eU(rU,this,tU(e))}});var nU=P,iU=zR,aU=KP;Ar({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(e){return nU(aU,this,iU(e))}});var oU=P,sU=zR,uU=$P;Ar({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(e){return oU(uU,this,sU(e))}});var cU=P,lU=zR,fU=oR;Ar({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(e){return cU(fU,this,lU(e))}});var hU=Ar,dU=u,pU=$D,vU=iP,gU=Zn,yU=dU([].join),mU=dU([].push);hU({target:"Set",proto:!0,real:!0,forced:!0},{join:function(e){var t=pU(this),r=void 0===e?",":gU(e),n=[];return vU(t,(function(e){mU(n,e)})),yU(n,r)}});var _U=Jt,bU=$D,wU=iP,kU=rP.Set,SU=rP.add;Ar({target:"Set",proto:!0,real:!0,forced:!0},{map:function(e){var t=bU(this),r=_U(e,arguments.length>1?arguments[1]:void 0),n=new kU;return wU(t,(function(e){SU(n,r(e,e,t))})),n}});var xU=Ce,EU=$D,TU=iP,AU=TypeError;Ar({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=EU(this),r=arguments.length<2,n=r?void 0:arguments[1];if(xU(e),TU(t,(function(i){r?(r=!1,n=i):n=e(n,i,i,t)})),r)throw AU("Reduce of empty set with no initial value");return n}});var OU=Jt,LU=$D,CU=iP;Ar({target:"Set",proto:!0,real:!0,forced:!0},{some:function(e){var t=LU(this),r=OU(e,arguments.length>1?arguments[1]:void 0);return!0===CU(t,(function(e){if(r(e,e,t))return!0}),!0)}});var DU=P,PU=zR,RU=vR;Ar({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(e){return DU(RU,this,PU(e))}});var UU=P,IU=zR,MU=kR;Ar({target:"Set",proto:!0,real:!0,forced:!0},{union:function(e){return UU(MU,this,IU(e))}});var BU=t(xR),jU=function(){function e(t){var r=this;WL(this,e),QL(this,"_seiSet",new BU),this.emitter=t,t.on(TD,(function(e){e&&r._seiSet.add(e)}))}return JL(e,[{key:"throw",value:function(e){var t,r=this;if(null!=e&&this._seiSet.size){var n=e-.2,i=e+.2,a=[];of(t=this._seiSet).call(t,(function(e){e.time>=n&&e.time<=i&&a.push(e)})),of(a).call(a,(function(e){r._seiSet.delete(e),r.emitter.emit(AD,e)}))}}},{key:"reset",value:function(){this._seiSet.clear()}}]),e}(),FU=function(){function e(){WL(this,e),QL(this,"_chunkSpeeds",[]),QL(this,"_speeds",[])}return JL(e,[{key:"addRecord",value:function(e,t){var r;e&&t&&(this._speeds.push(8e3*e/t),this._speeds=yy(r=this._speeds).call(r,-3))}},{key:"addChunkRecord",value:function(e,t){var r;e&&t&&(this._chunkSpeeds.push(8e3*e/t),this._chunkSpeeds=yy(r=this._chunkSpeeds).call(r,-100))}},{key:"getAvgSpeed",value:function(){var e,t;return this._chunkSpeeds.length||this._speeds.length?this._speeds.length?xC(t=this._speeds).call(t,(function(e,t){return e+t}))/this._speeds.length:xC(e=this._chunkSpeeds).call(e,(function(e,t){return e+t}))/this._chunkSpeeds.length:0}},{key:"getLatestSpeed",value:function(){return this._chunkSpeeds.length||this._speeds.length?this._speeds.length?this._speeds[this._speeds.length-1]:this._chunkSpeeds[this._chunkSpeeds.length-1]:0}},{key:"reset",value:function(){this._chunkSpeeds=[],this._speeds=[]}}]),e}(),NU=function(){function e(t){WL(this,e),QL(this,"encodeType",""),QL(this,"audioCodec",""),QL(this,"videoCodec",""),QL(this,"domain",""),QL(this,"fps",0),QL(this,"bitrate",0),QL(this,"width",0),QL(this,"height",0),QL(this,"samplerate",0),QL(this,"channelCount",0),QL(this,"gop",0),QL(this,"_bitsAccumulateSize",0),QL(this,"_bitsAccumulateDuration",0),this._timescale=t}return JL(e,[{key:"getStats",value:function(){return{encodeType:this.encodeType,audioCodec:this.audioCodec,videoCodec:this.videoCodec,domain:this.domain,fps:this.fps,bitrate:this.bitrate,width:this.width,height:this.height,samplerate:this.samplerate,channelCount:this.channelCount,gop:this.gop}}},{key:"setEncodeType",value:function(e){this.encodeType=e}},{key:"setFpsFromScriptData",value:function(e){var t,r=e.data,n=null==r||null===(t=r.onMetaData)||void 0===t?void 0:t.framerate;n&&n>0&&n<100&&(this.fps=n)}},{key:"setVideoMeta",value:function(e){if(this.width=e.width,this.height=e.height,this.videoCodec=e.codec,this.encodeType=e.codecType,e.fpsNum&&e.fpsDen){var t=e.fpsNum/e.fpsDen;t>0&&t<100&&(this.fps=t)}}},{key:"setAudioMeta",value:function(e){this.audioCodec=e.codec,this.samplerate=e.sampleRate,this.channelCount=e.channelCount}},{key:"setDomain",value:function(e){var t;this.domain=yy(t=e.split("/")).call(t,2,3)[0]}},{key:"updateBitrate",value:function(e){var t=this;if((!this.fps||this.fps>=100)&&e.length){var r=xC(e).call(e,(function(e,t){return e+t.duration}),0)/e.length;this.fps=Math.round(this._timescale/r)}of(e).call(e,(function(e){var r;1===e.gopId&&t.gop++,t._bitsAccumulateDuration+=e.duration/(t._timescale/1e3),t._bitsAccumulateSize+=xC(r=e.units).call(r,(function(e,t){return e+t.length}),0),t._bitsAccumulateDuration>=1e3&&(t.bitrate=8*t._bitsAccumulateSize,t._bitsAccumulateDuration=0,t._bitsAccumulateSize=0)}))}}]),e}(),VU=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;WL(this,e),QL(this,"_core",null),QL(this,"_samples",[]),this._core=t,this._timescale=r,this._stats=new NU(r),this._bindEvents()}return JL(e,[{key:"getStats",value:function(){var e,t,r,n,i,a,o,s=(null===(e=this._core)||void 0===e?void 0:e.media)||{},u=s.currentTime,c=void 0===u?0:u,l=s.decodeFps,f=void 0===l?0:l;return zL(zL({},this._stats.getStats()),{},{downloadSpeed:(null===(t=this._core)||void 0===t||null===(r=t.speedInfo)||void 0===r?void 0:r.call(t).speed)||0,avgSpeed:(null===(n=this._core)||void 0===n||null===(i=n.speedInfo)||void 0===i?void 0:i.call(n).avgSpeed)||0,currentTime:c,bufferEnd:(null===(a=this._core)||void 0===a||null===(o=a.bufferInfo())||void 0===o?void 0:o.remaining)||0,decodeFps:f})}},{key:"_bindEvents",value:function(){var e=this;this._core.on(PD,(function(t){var r=t.videoTrack;return e._stats.updateBitrate(r.samples)})),this._core.on(OD,(function(t){e._stats.setFpsFromScriptData(t)})),this._core.on(ED,(function(t){"video"===t.type?e._stats.setVideoMeta(t.track):e._stats.setAudioMeta(t.track)})),this._core.on(vD,(function(t){e._stats.setDomain(t.responseUrl)}))}},{key:"reset",value:function(){this._samples=[],this._stats=new NU(this._timescale)}}]),e}();function zU(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function GU(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Df(e,tI(n.key),n)}}function qU(e,t,r){return t&&GU(e.prototype,t),r&&GU(e,r),Df(e,"prototype",{writable:!1}),e}function HU(e,t,r){return(t=tI(t))in e?Df(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function KU(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Uf(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Df(e,"prototype",{writable:!1}),t&&YU(e,t)}function WU(e){var t;return(WU=Ff?Uy(t=jf).call(t):function(e){return e.__proto__||jf(e)})(e)}function YU(e,t){var r;return(YU=Ff?Uy(r=Ff).call(r):function(e,t){return e.__proto__=t,e})(e,t)}function JU(e,t){if(t&&("object"===Ol(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function QU(e){var t=function(){if("undefined"==typeof Reflect||!Jy)return!1;if(Jy.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Jy(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=WU(e);if(t){var i=WU(this).constructor;r=Jy(n,arguments,i)}else r=n.apply(this,arguments);return JU(this,r)}}function XU(e,t){return function(e){if(P_(e))return e}(e)||function(e,t){var r=null==e?null:void 0!==Pf&&z_(e)||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],u=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(l){c=!0,i=l}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}(e,t)||ZU(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $U(e){return function(e){if(P_(e))return eI(e)}(e)||function(e){if(void 0!==Pf&&null!=z_(e)||null!=e["@@iterator"])return ib(e)}(e)||ZU(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ZU(e,t){var r;if(e){if("string"==typeof e)return eI(e,t);var n=yy(r=Object.prototype.toString.call(e)).call(r,8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?ib(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?eI(e,t):void 0}}function eI(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function tI(e){var t=function(e,t){if("object"!==Ol(e)||null===e)return e;var r=e[Qy];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ol(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ol(t)?t:String(t)}var rI="video",nI="audio",iI="metadata",aI="avc",oI="hevc",sI="aac",uI="g7110a",cI="g7110m",lI="LARGE_AV_SHIFT",fI="LARGE_VIDEO_GAP",hI="LARGE_VIDEO_GAP_BETWEEN_CHUNK",dI="LARGE_AUDIO_GAP",pI="AUDIO_FILLED",vI="AUDIO_DROPPED",gI=function(){function e(){zU(this,e),HU(this,"id",1),HU(this,"type",rI),HU(this,"codecType",aI),HU(this,"pid",-1),HU(this,"hvcC",void 0),HU(this,"codec",""),HU(this,"timescale",0),HU(this,"formatTimescale",0),HU(this,"sequenceNumber",0),HU(this,"baseMediaDecodeTime",0),HU(this,"baseDts",0),HU(this,"duration",0),HU(this,"warnings",[]),HU(this,"samples",[]),HU(this,"pps",[]),HU(this,"sps",[]),HU(this,"vps",[]),HU(this,"fpsNum",0),HU(this,"fpsDen",0),HU(this,"sarRatio",[]),HU(this,"width",0),HU(this,"height",0),HU(this,"nalUnitSize",4),HU(this,"present",!1),HU(this,"isVideoEncryption",!1),HU(this,"isAudioEncryption",!1),HU(this,"isVideo",!0),HU(this,"kid",null),HU(this,"pssh",null),HU(this,"ext",void 0)}return qU(e,[{key:"reset",value:function(){this.sequenceNumber=this.width=this.height=this.fpsDen=this.fpsNum=this.duration=this.baseMediaDecodeTime=this.timescale=0,this.codec="",this.present=!1,this.pid=-1,this.pps=[],this.sps=[],this.vps=[],this.sarRatio=[],this.samples=[],this.warnings=[],this.hvcC=null}},{key:"exist",value:function(){return!!(this.pps.length&&this.sps.length&&this.codec)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isVideoEncryption}}]),e}(),yI=function(){function e(){zU(this,e),HU(this,"id",2),HU(this,"type",nI),HU(this,"codecType",sI),HU(this,"pid",-1),HU(this,"codec",""),HU(this,"sequenceNumber",0),HU(this,"sampleDuration",0),HU(this,"timescale",0),HU(this,"formatTimescale",0),HU(this,"baseMediaDecodeTime",0),HU(this,"duration",0),HU(this,"warnings",[]),HU(this,"samples",[]),HU(this,"baseDts",0),HU(this,"sampleSize",16),HU(this,"sampleRate",0),HU(this,"channelCount",0),HU(this,"objectType",0),HU(this,"sampleRateIndex",0),HU(this,"config",[]),HU(this,"present",!1),HU(this,"isVideoEncryption",!1),HU(this,"isAudioEncryption",!1),HU(this,"kid",null),HU(this,"ext",void 0)}return qU(e,[{key:"reset",value:function(){this.sequenceNumber=0,this.timescale=0,this.sampleDuration=0,this.sampleRate=0,this.channelCount=0,this.baseMediaDecodeTime=0,this.present=!1,this.pid=-1,this.codec="",this.samples=[],this.config=[],this.warnings=[]}},{key:"exist",value:function(){return!!(this.sampleRate&&this.channelCount&&this.codec&&this.codecType===sI)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isAudioEncryption}}]),e}(),mI=function(){function e(t,r,n){zU(this,e),HU(this,"flag",{}),HU(this,"keyframe",!1),HU(this,"gopId",0),HU(this,"duration",0),HU(this,"size",0),HU(this,"units",[]),HU(this,"chromaFormat",420),this.originPts=this.pts=t,this.originDts=this.dts=r,n&&(this.units=n)}return qU(e,[{key:"cts",get:function(){return this.pts-this.dts}},{key:"setToKeyframe",value:function(){this.keyframe=!0,this.flag.dependsOn=2,this.flag.isNonSyncSample=0}}]),e}(),_I=qU((function e(t,r,n,i){zU(this,e),HU(this,"duration",1024),HU(this,"flag",{dependsOn:2,isNonSyncSample:0}),HU(this,"keyframe",!0),this.originPts=this.pts=this.dts=t,this.data=r,this.size=r.byteLength,this.sampleOffset=i,n&&(this.duration=n)})),bI=qU((function e(t,r){zU(this,e),HU(this,"time",0),this.data=t,this.originPts=this.pts=r})),wI=function(e){KU(r,e);var t=QU(r);function r(){return zU(this,r),t.apply(this,arguments)}return qU(r)}(bI),kI=function(e){KU(r,e);var t=QU(r);function r(){return zU(this,r),t.apply(this,arguments)}return qU(r)}(bI),SI=function(){function e(){zU(this,e),HU(this,"id",3),HU(this,"type",iI),HU(this,"timescale",0),HU(this,"flvScriptSamples",[]),HU(this,"seiSamples",[])}return qU(e,[{key:"exist",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length||!this.timescale)}},{key:"reset",value:function(){this.timescale=0,this.flvScriptSamples=[],this.seiSamples=[]}},{key:"hasSample",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length)}}]),e}(),xI=f.isFinite;Ar({target:"Number",stat:!0},{isFinite:Number.isFinite||function(e){return"number"==typeof e&&xI(e)}});var EI=t(te.Number.isFinite),TI=function(){function e(t){zU(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]")}return qU(e,[{key:"warn",value:function(){var t,r;if(!e.disabled){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];(r=console).warn.apply(r,Qn(t=[this._prefix]).call(t,i))}}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();HU(TI,"disabled",!0);var AI="undefined"!=typeof window,OI=AI&&navigator.userAgent.toLocaleLowerCase(),LI=AI&&/^((?!chrome|android).)*safari/.test(OI),CI=AI&&tA(OI).call(OI,"firefox"),DI=AI&&tA(OI).call(OI,"android"),PI=function(){function e(){zU(this,e)}return qU(e,null,[{key:"getRateIndexByRate",value:function(t){var r;return wm(r=e.FREQ).call(r,t)}},{key:"parseADTS",value:function(t,r){for(var n=t.length,i=0;i+2<n&&(255!==t[i]||240!=(246&t[i+1]));)i++;if(!(i>=n)){var a=i,o=[],s=(60&t[i+2])>>>2,u=e.FREQ[s];if(!u)throw new Error("Invalid sampling index: ".concat(s));for(var c,l,f=1+((192&t[i+2])>>>6),h=(1&t[i+2])<<2|(192&t[i+3])>>>6,d=e._getConfig(s,h,f),p=d.config,v=d.codec,g=0,y=e.getFrameDuration(u);i+7<n;)if(255===t[i]&&240==(246&t[i+1])){if(n-i<(l=(3&t[i+3])<<11|t[i+4]<<3|(224&t[i+5])>>5))break;c=2*(1&~t[i+1]),o.push({pts:r+g*y,data:t.subarray(i+7+c,i+l)}),g++,i+=l}else i++;return{skip:a,remaining:i>=n?void 0:t.subarray(i),frames:o,samplingFrequencyIndex:s,sampleRate:u,objectType:f,channelCount:h,codec:v,config:p,originCodec:"mp4a.40.".concat(f)}}}},{key:"parseAudioSpecificConfig",value:function(t){if(t.length){var r=t[0]>>>3,n=(7&t[0])<<1|t[1]>>>7,i=(120&t[1])>>>3,a=e.FREQ[n];if(a){var o=e._getConfig(n,i,r);return{samplingFrequencyIndex:n,sampleRate:a,objectType:r,channelCount:i,config:o.config,codec:o.codec,originCodec:"mp4a.40.".concat(r)}}}}},{key:"getFrameDuration",value:function(e){return 1024*(arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4)/e}},{key:"_getConfig",value:function(e,t,r){var n,i,a=[];return CI?e>=6?(n=5,i=e-3):(n=2,i=e):DI?(n=2,i=e):(n=2===r||5===r?r:5,i=e,e>=6?i=e-3:1===t&&(n=2,i=e)),a[0]=n<<3,a[0]|=(14&e)>>1,a[1]=(1&e)<<7,a[1]|=t<<3,5===n&&(a[1]|=(14&i)>>1,a[2]=(1&i)<<7,a[2]|=8,a[3]=0),{config:a,codec:"mp4a.40.".concat(n)}}},{key:"getSilentFrame",value:function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}}}]),e}();HU(PI,"FREQ",[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350]);var RI=1e3,UI=5e3,II=function(){function e(t,r,n){zU(this,e),this.videoTrack=t,this.audioTrack=r,this.metadataTrack=n,this._baseDts=-1,this._baseDtsInited=!1,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastVideoDuration=0,this._keyFrameInNextChunk=!1,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0}return qU(e,[{key:"fix",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t=Math.round(1e3*t);var i=this.videoTrack,a=this.audioTrack;if(!r&&n||(this._videoLastSample=null,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0),r&&!n&&(this._baseDtsInited=!1),this._baseDtsInited||this._calculateBaseDts(a,i),!n&&t&&(this._audioNextPts=this._videoNextDts=t),this._baseDtsInited&&(this._videoTimestampBreak||!this.videoTrack.exist())&&(this._audioTimestampBreak||!this.audioTrack.exist())&&this._resetBaseDtsWhenStreamBreaked(),this._fixAudio(a),this._keyFrameInNextChunk=!1,this._fixVideo(i),this.metadataTrack.exist()){var o,s,u=this.metadataTrack.timescale;of(o=this.metadataTrack.seiSamples).call(o,(function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/u})),of(s=this.metadataTrack.flvScriptSamples).call(s,(function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/u}))}i.samples.length&&(i.baseMediaDecodeTime=i.samples[0].dts),a.samples.length&&(a.baseMediaDecodeTime=a.samples[0].pts*a.timescale/1e3)}},{key:"_fixVideo",value:function(e){var t=this,r=e.samples;if(r.length){var n;if(of(r).call(r,(function(e){e.dts-=t._baseDts,e.pts-=t._baseDts,e.keyframe&&(t._keyFrameInNextChunk=!0)})),e.fpsNum&&e.fpsDen)n=e.timescale*(e.fpsDen/e.fpsNum);else if(e.length>1){var i=e.samples[0],a=e.samples[r.length-1];n=Math.floor((a.dts-i.dts)/(r.length-1))}else n=this._lastVideoDuration||40;var o=r.pop();if(this._videoLastSample&&r.unshift(this._videoLastSample),this._videoLastSample=o,r.length){if(void 0===this._videoNextDts){var s=r[0];this._videoNextDts=s.dts}var u=r.length,c=0,l=r[0],f=this._videoNextDts-l.dts;if(Math.abs(f)>200){var h;if(Math.abs(l.dts-this._lastVideoExceptionChunkFirstDtsDot)>5e3)this._lastVideoExceptionChunkFirstDtsDot=l.dts,e.warnings.push({type:hI,nextDts:this._videoNextDts,firstSampleDts:l.dts,nextSampleDts:null===(h=r[1])||void 0===h?void 0:h.dts,sampleDuration:f});this._videoTimestampBreak>=5?(this._videoNextDts=l.dts,this._videoTimestampBreak=0):(l.dts+=f,l.pts+=f,this.audioTrack.exist()||(this._videoTimestampBreak=1))}for(var d=0;d<u;d++){var p=r[d].dts,v=r[d+1];((c=d<u-1?v.dts-p:o?o.dts-p:n)>1e3||c<0)&&(this._videoTimestampBreak++,Math.abs(p-this._lastVideoExceptionLargeGapDot)>5e3&&(this._lastVideoExceptionLargeGapDot=p,e.warnings.push({type:fI,time:p/e.timescale,dts:p,originDts:r[d].originDts,nextDts:this._videoNextDts,sampleDuration:c,refSampleDuration:n})),c=n),r[d].duration=c,this._videoNextDts+=c,this._lastVideoDuration=c}}}}},{key:"_fixAudio",value:function(e){var t=this,r=e.samples;r.length&&(of(r).call(r,(function(e){e.dts=e.pts-=t._baseDts})),this._doFixAudioInternal(e,r,1e3))}},{key:"_calculateBaseDts",value:function(e,t){var r=e.samples,n=t.samples;if(!r.length&&!n.length)return!1;var i=1/0,a=1/0;r.length&&(e.baseDts=i=r[0].pts),n.length&&(t.baseDts=a=n[0].dts),this._baseDts=Math.min(i,a);var o=a-i;return EI(o)&&Math.abs(o)>500&&t.warnings.push({type:lI,videoBaseDts:a,audioBasePts:i,baseDts:this._baseDts,delta:o}),this._baseDtsInited=!0,!0}},{key:"_resetBaseDtsWhenStreamBreaked",value:function(){this._calculateBaseDts(this.audioTrack,this.videoTrack)&&(this.audioTrack.exist()?this.videoTrack.exist()?this._baseDts-=Math.min(this._audioNextPts,this._videoNextDts):this._baseDts-=this._audioNextPts:this._baseDts-=this._videoNextDts,this._videoTimestampBreak=0,this._audioTimestampBreak=0)}},{key:"_doFixAudioInternal",value:function(e,t,r){e.sampleDuration||(e.sampleDuration=e.codecType===sI?PI.getFrameDuration(e.timescale,r):this._getG711Duration(e));var n=e.sampleDuration,i=e.codecType===sI?1024:n*e.timescale/1e3;if(void 0===this._audioNextPts){var a=t[0];this._audioNextPts=a.pts}for(var o=0;o<t.length;o++){var s=this._audioNextPts,u=t[o],c=u.pts-s;if(0===o&&this._audioTimestampBreak>=5&&this._keyFrameInNextChunk&&(s=this._audioNextPts=u.dts,c=0,this._audioTimestampBreak=0),!this._audioTimestampBreak&&c>=3*n&&c<=RI&&!LI){var l=this._getSilentFrame(e)||t[0].data.subarray(),f=Math.floor(c/n);Math.abs(u.pts-this._lastAudioExceptionGapDot)>UI&&(this._lastAudioExceptionGapDot=u.pts,e.warnings.push({type:pI,pts:u.pts,originPts:u.originPts,count:f,nextPts:s,refSampleDuration:n}));for(var h=0;h<f;h++){var d=new _I(Math.floor(this._audioNextPts+n)-Math.floor(this._audioNextPts),l,i);d.originPts=Math.floor(this._baseDts+s),r_(t).call(t,o,0,d),this._audioNextPts+=n,o++}o--}else c<=-3*n&&c>=-1e3?(Math.abs(u.pts-this._lastAudioExceptionOverlapDot)>UI&&(this._lastAudioExceptionOverlapDot=u.pts,e.warnings.push({type:vI,pts:u.pts,originPts:u.originPts,nextPts:s,refSampleDuration:n})),r_(t).call(t,o,1),o--):(Math.abs(c)>RI&&(this._audioTimestampBreak++,Math.abs(u.pts-this._lastAudioExceptionLargeGapDot)>UI&&(this._lastAudioExceptionLargeGapDot=u.pts,e.warnings.push({type:dI,time:u.pts/1e3,pts:u.pts,originPts:u.originPts,nextPts:s,sampleDuration:c,refSampleDuration:n}))),u.dts=u.pts=s,u.duration=i,this._audioNextPts+=n)}}},{key:"_getG711Duration",value:function(e){var t=e.sampleSize,r=e.channelCount,n=e.sampleRate,i=e.samples[0];if(i)return 2*i.data.byteLength/r/(t/8)/n*1e3}},{key:"_getSilentFrame",value:function(e){return e.codecType===sI?PI.getSilentFrame(e.codec,e.channelCount):new Uint8Array(8*e.sampleDuration*e.channelCount)}}]),e}();function MI(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t=jl(t).call(t,Boolean);var n=new Uint8Array(xC(t).call(t,(function(e,t){return e+t.byteLength}),0)),i=0;return of(t).call(t,(function(e){n.set(e,i),i+=e.byteLength})),n}function BI(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function jI(e){for(var t,r="avc1.",n=0;n<3;n++)(t=e[n].toString(16)).length<2&&(t="0".concat(t)),r+=t;return r}function FI(e){if(!P_(e)){for(var t=[],r="",n=0;n<e.length;n++)n%2&&(r=e[n-1]+e[n],t.push(E_(r,16)),r="");return t}return D_(e).call(e,(function(e){return E_(e,16)}))}var NI=function(){function e(){zU(this,e)}return qU(e,null,[{key:"parseAnnexB",value:function(e){for(var t=e.length,r=2,n=0;null!==e[r]&&void 0!==e[r]&&1!==e[r];)r++;if((n=++r+2)>=t)return[];for(var i=[];n<t;)switch(e[n]){case 0:if(0!==e[n-1]){n+=2;break}if(0!==e[n-2]){n++;break}r!==n-2&&i.push(e.subarray(r,n-2));do{n++}while(1!==e[n]&&n<t);n=(r=n+1)+2;break;case 1:if(0!==e[n-1]||0!==e[n-2]){n+=3;break}r!==n-2&&i.push(e.subarray(r,n-2)),n=(r=n+1)+2;break;default:n+=3}return r<t&&i.push(e.subarray(r)),i}},{key:"parseAvcC",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(!(e.length<4)){for(var r,n=e.length,i=[],a=0;a+t<n;)if(r=BI(e,a),3===t&&(r>>>=8),a+=t,r){if(a+r>n)break;i.push(e.subarray(a,a+r)),a+=r}return i}}},{key:"parseSEI",value:function(e,t){for(var r=e.length,n=t?2:1,i=0,a=0,o="";255===e[n];)i+=255,n++;for(i+=e[n++];255===e[n];)a+=255,n++;if(a+=e[n++],5===i&&r>n+16)for(var s=0;s<16;s++)o+=e[n].toString(16),n++;return{payload:e.subarray(n),type:i,size:a,uuid:o}}},{key:"removeEPB",value:function(e){for(var t=e.byteLength,r=[],n=1;n<t-2;)0===e[n]&&0===e[n+1]&&3===e[n+2]?(r.push(n+2),n+=2):n++;if(!r.length)return e;var i=t-r.length,a=new Uint8Array(i),o=0;for(n=0;n<i;o++,n++)o===r[0]&&(o++,r.shift()),a[n]=e[o];return a}}]),e}(),VI=function(){function e(t){if(zU(this,e),HU(this,"_bytesAvailable",void 0),HU(this,"_bitsAvailable",0),HU(this,"_word",0),!t)throw new Error("ExpGolomb data params is required");this._data=t,this._bytesAvailable=t.byteLength,this._bytesAvailable&&this._loadWord()}return qU(e,[{key:"_loadWord",value:function(){var e=this._data.byteLength-this._bytesAvailable,t=Math.min(4,this._bytesAvailable);if(0===t)throw new Error("No bytes available");var r=new Uint8Array(4);r.set(this._data.subarray(e,e+t)),this._word=new DataView(r.buffer).getUint32(0),this._bitsAvailable=8*t,this._bytesAvailable-=t}},{key:"skipBits",value:function(e){if(this._bitsAvailable>e)this._word<<=e,this._bitsAvailable-=e;else{e-=this._bitsAvailable;var t=Math.floor(e/8);e-=8*t,this._bytesAvailable-=t,this._loadWord(),this._word<<=e,this._bitsAvailable-=e}}},{key:"readBits",value:function(e){if(e>32)throw new Error("Cannot read more than 32 bits");var t=Math.min(this._bitsAvailable,e),r=this._word>>>32-t;return this._bitsAvailable-=t,this._bitsAvailable>0?this._word<<=t:this._bytesAvailable>0&&this._loadWord(),(t=e-t)>0&&this._bitsAvailable?r<<t|this.readBits(t):r}},{key:"skipLZ",value:function(){var e;for(e=0;e<this._bitsAvailable;++e)if(0!=(this._word&2147483648>>>e))return this._word<<=e,this._bitsAvailable-=e,e;return this._loadWord(),e+this.skipLZ()}},{key:"skipUEG",value:function(){this.skipBits(1+this.skipLZ())}},{key:"readUEG",value:function(){var e=this.skipLZ();return this.readBits(e+1)-1}},{key:"readEG",value:function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}},{key:"readBool",value:function(){return 1===this.readBits(1)}},{key:"readUByte",value:function(){return this.readBits(8)}},{key:"skipScalingList",value:function(e){for(var t=8,r=8,n=0;n<e;n++)0!==r&&(r=(t+this.readEG()+256)%256),t=0===r?t:r}}]),e}(),zI=function(){function e(){zU(this,e)}return qU(e,null,[{key:"parseAVCDecoderConfigurationRecord",value:function(t){if(!(t.length<7)){for(var r,n,i=1+(3&t[4]),a=[],o=[],s=6,u=31&t[5],c=0;c<u;c++)if(n=t[s]<<8|t[s+1],s+=2,n){var l=t.subarray(s,s+n);s+=n,a.push(l),r||(r=e.parseSPS(NI.removeEPB(l)))}var f,h=t[s];s++;for(var d=0;d<h;d++)f=t[s]<<8|t[s+1],s+=2,f&&(o.push(t.subarray(s,s+f)),s+=f);return{sps:r,spsArr:a,ppsArr:o,nalUnitSize:i}}}},{key:"parseSPS",value:function(e){var t=new VI(e);t.readUByte();var r=t.readUByte(),n=t.readUByte(),i=t.readUByte();t.skipUEG();var a=420;if(100===r||110===r||122===r||244===r||44===r||83===r||86===r||118===r||128===r||138===r||144===r){var o=t.readUEG();if(o<=3&&(a=[0,420,422,444][o]),3===o&&t.skipBits(1),t.skipUEG(),t.skipUEG(),t.skipBits(1),t.readBool())for(var s=3!==o?8:12,u=0;u<s;u++)t.readBool()&&(u<6?t.skipScalingList(16):t.skipScalingList(64))}t.skipUEG();var c=t.readUEG();if(0===c)t.readUEG();else if(1===c){t.skipBits(1),t.skipUEG(),t.skipUEG();for(var l=t.readUEG(),f=0;f<l;f++)t.skipUEG()}t.skipUEG(),t.skipBits(1);var h=t.readUEG(),d=t.readUEG(),p=t.readBits(1);0===p&&t.skipBits(1),t.skipBits(1);var v,g,y,m,_,b=0,w=0,k=0,S=0;if(t.readBool()&&(b=t.readUEG(),w=t.readUEG(),k=t.readUEG(),S=t.readUEG()),t.readBool()){if(t.readBool())switch(t.readUByte()){case 1:v=[1,1];break;case 2:v=[12,11];break;case 3:v=[10,11];break;case 4:v=[16,11];break;case 5:v=[40,33];break;case 6:v=[24,11];break;case 7:v=[20,11];break;case 8:v=[32,11];break;case 9:v=[80,33];break;case 10:v=[18,11];break;case 11:v=[15,11];break;case 12:v=[64,33];break;case 13:v=[160,99];break;case 14:v=[4,3];break;case 15:v=[3,2];break;case 16:v=[2,1];break;case 255:v=[t.readUByte()<<8|t.readUByte(),t.readUByte()<<8|t.readUByte()]}if(t.readBool()&&t.readBool(),t.readBool()&&(t.readBits(4),t.readBool()&&t.readBits(24)),t.readBool()&&(t.readUEG(),t.readUEG()),t.readBool()){var x=t.readBits(32),E=t.readBits(32);g=t.readBool(),_=(y=E)/(m=2*x)}}return{codec:jI(e.subarray(1,4)),profileIdc:r,profileCompatibility:n,levelIdc:i,chromaFormat:a,width:Math.ceil(16*(h+1)-2*(b+w)),height:(2-p)*(d+1)*16-(p?2:4)*(k+S),sarRatio:v,fpsNum:y,fpsDen:m,fps:_,fixedFrame:g}}}]),e}(),GI=function(){function e(){zU(this,e)}return qU(e,null,[{key:"parseHEVCDecoderConfigurationRecord",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(t.length<23)){r=r||{};for(var n,i,a,o,s,u=1+(3&t[21]),c=[],l=[],f=[],h=23,d=t[22],p=0;p<d;p++){a=63&t[h],o=t[h+1]<<8|t[h+2],h+=3;for(var v=0;v<o;v++)if(s=t[h]<<8|t[h+1],h+=2,s){switch(a){case 32:var g=t.subarray(h,h+s);n||(n=e.parseVPS(NI.removeEPB(g),r)),f.push(g);break;case 33:var y=t.subarray(h,h+s);i||(i=e.parseSPS(NI.removeEPB(y),r)),c.push(y);break;case 34:l.push(t.subarray(h,h+s))}h+=s}}return{hvcC:r,sps:i,spsArr:c,ppsArr:l,vpsArr:f,nalUnitSize:u}}}},{key:"parseVPS",value:function(t,r){r=r||{};var n=new VI(t);n.readUByte(),n.readUByte(),n.readBits(12);var i=n.readBits(3);return r.numTemporalLayers=Math.max(r.numTemporalLayers||0,i+1),n.readBits(17),e._parseProfileTierLevel(n,i,r),r}},{key:"parseSPS",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r=r||{};var n=new VI(t);n.readUByte(),n.readUByte(),n.readBits(4);var i=n.readBits(3);r.numTemporalLayers=Math.max(i+1,r.numTemporalLayers||0),r.temporalIdNested=n.readBits(1),e._parseProfileTierLevel(n,i,r),n.readUEG();var a=r.chromaFormatIdc=n.readUEG(),o=420;a<=3&&(o=[0,420,422,444][a]);var s=0;3===a&&(s=n.readBits(1));var u,c,l,f,h=n.readUEG(),d=n.readUEG(),p=n.readBits(1);(1===p&&(u=n.readUEG(),c=n.readUEG(),l=n.readUEG(),f=n.readUEG()),r.bitDepthLumaMinus8=n.readUEG(),r.bitDepthChromaMinus8=n.readUEG(),1===p)&&(h-=(1!==a&&2!==a||0!==s?1:2)*(c+u),d-=(1===a&&0===s?2:1)*(f+l));return{codec:"hev1.1.6.L93.B0",width:h,height:d,chromaFormat:o,hvcC:r}}},{key:"_parseProfileTierLevel",value:function(e,t,r){var n=r.generalTierFlag||0;r.generalProfileSpace=e.readBits(2),r.generalTierFlag=Math.max(e.readBits(1),n),r.generalProfileIdc=Math.max(e.readBits(5),r.generalProfileIdc||0),r.generalProfileCompatibilityFlags=e.readBits(32),r.generalConstraintIndicatorFlags=[e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8)];var i=e.readBits(8);n<r.generalTierFlag?r.generalLevelIdc=i:r.generalLevelIdc=Math.max(i,r.generalLevelIdc||0);for(var a=[],o=[],s=0;s<t;s++)a[s]=e.readBits(1),o[s]=e.readBits(1);t>0&&e.readBits(2*(8-t));for(var u=0;u<t;u++)0!==a[u]&&(e.readBits(2),e.readBits(1),e.readBits(5),e.readBits(16),e.readBits(16),e.readBits(4),e.readBits(16),e.readBits(16),e.readBits(12)),0!==o[u]&&e.readBits(8)}}]),e}(),qI=function(){function e(){zU(this,e)}return qU(e,null,[{key:"decode",value:function(t){for(var r=[],n=t,i=0,a=t.length;i<a;)if(n[i]<128)r.push(String.fromCharCode(n[i])),++i;else{if(n[i]<192);else if(n[i]<224){if(e._checkContinuation(n,i,1)){var o=(31&n[i])<<6|63&n[i+1];if(o>=128){r.push(String.fromCharCode(65535&o)),i+=2;continue}}}else if(n[i]<240){if(e._checkContinuation(n,i,2)){var s=(15&n[i])<<12|(63&n[i+1])<<6|63&n[i+2];if(s>=2048&&55296!=(63488&s)){r.push(String.fromCharCode(65535&s)),i+=3;continue}}}else if(n[i]<248&&e._checkContinuation(n,i,3)){var u=(7&n[i])<<18|(63&n[i+1])<<12|(63&n[i+2])<<6|63&n[i+3];if(u>65536&&u<1114112){u-=65536,r.push(String.fromCharCode(u>>>10|55296)),r.push(String.fromCharCode(1023&u|56320)),i+=4;continue}}r.push(String.fromCharCode(65533)),++i}return r.join("")}},{key:"_checkContinuation",value:function(e,t,r){var n=e;if(t+r<n.length){for(;r--;)if(128!=(192&n[++t]))return!1;return!0}return!1}}]),e}(),HI=function(){function e(){zU(this,e)}return qU(e,null,[{key:"parse",value:function(t){if(!(t.length<3)){var r={},n=e._parseValue(new DataView(t.buffer,t.byteOffset,t.byteLength)),i=e._parseValue(new DataView(t.buffer,t.byteOffset+n.size,t.byteLength-n.size));return r[n.data]=i.data,r}}},{key:"_parseValue",value:function(t){var r,n=t.byteLength,i=1,a=!1;switch(t.getUint8(0)){case 0:r=t.getFloat64(1),i+=8;break;case 1:r=!!t.getUint8(1),i+=1;break;case 2:var o=e._parseString(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i));r=o.data,i+=o.size;break;case 3:r={};var s=0;for(9==(16777215&t.getUint32(n-4))&&(s=3);i<n-4;){var u=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-s)),c=u.size,l=u.data;if(u.isEnd)break;r[l.name]=l.value,i+=c}if(i<=n-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 8:r={},i+=4;var f=0;for(9==(16777215&t.getUint32(n-4))&&(f=3);i<n-8;){var h=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-f)),d=h.size,p=h.data;if(h.isEnd)break;r[p.name]=p.value,i+=d}if(i<=n-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 9:r=void 0,i=1,a=!0;break;case 10:r=[];var v=t.getUint32(1);i+=4;for(var g=0;g<v;g++){var y=e._parseValue(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i)),m=y.data,_=y.size;r.push(m),i+=_}break;case 11:var b=t.getFloat64(i)+6e4*t.getInt16(i+8);r=new Date(b),i+=10;break;case 12:var w=t.getUint32(1);i+=4,r="",w>0&&(r=qI.decode(new Uint8Array(t.buffer,t.byteOffset+i,w))),i+=w;break;default:i=n}return{data:r,size:i,isEnd:a}}},{key:"_parseString",value:function(e){var t=e.getUint16(0),r="";return t>0&&(r=qI.decode(new Uint8Array(e.buffer,e.byteOffset+2,t))),{data:r,size:2+t}}},{key:"_parseObject",value:function(t){if(!(t.byteLength<3)){var r=e._parseString(t),n=e._parseValue(new DataView(t.buffer,t.byteOffset+r.size,t.byteLength-r.size));return{data:{name:r.data,value:n.data},size:r.size+n.size,isEnd:n.isEnd}}}}]),e}(),KI=new TI("FlvDemuxer"),WI=function(){function e(t,r,n){zU(this,e),HU(this,"_headerParsed",!1),HU(this,"_remainingData",null),HU(this,"_gopId",0),HU(this,"_needAddMetaBeforeKeyFrameNal",!0),this.videoTrack=t||new gI,this.audioTrack=r||new yI,this.metadataTrack=n||new SI,this._fixer=new II(this.videoTrack,this.audioTrack,this.metadataTrack)}return qU(e,[{key:"demux",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=this.audioTrack,a=this.videoTrack,o=this.metadataTrack;if(!r&&n||(this._remainingData=null,this._headerParsed=!1),r?(a.reset(),i.reset(),o.reset()):(a.samples=[],i.samples=[],o.seiSamples=[],o.flvScriptSamples=[],a.warnings=[],i.warnings=[],this._remainingData&&(t=MI(this._remainingData,t),this._remainingData=null)),!t.length)return{videoTrack:a,audioTrack:i,metadataTrack:o};var s=0;if(!this._headerParsed){if(!e.probe(t))throw new Error("Invalid flv file");i.present=(4&t[4])>>>2!=0,a.present=0!=(1&t[4]),this._headerParsed=!0,s=BI(t,5)+4}for(var u,c,l,f,h,d=t.length;s+15<d&&(u=t[s],!(s+15+(c=t[s+1]<<16|t[s+2]<<8|t[s+3])>d));){var p;l=(t[s+7]<<24>>>0)+(t[s+4]<<16)+(t[s+5]<<8)+t[s+6],s+=11,f=t.subarray(s,s+c),8===u?this._parseAudio(f,l):9===u?this._parseVideo(f,l):18===u?this._parseScript(f,l):KI.warn("Invalid tag type: ".concat(u)),(h=BI(t,s+=c))!==11+c&&KI.warn(Qn(p="Invalid PrevTagSize ".concat(h," (")).call(p,11+c,")")),s+=4}return s<d&&(this._remainingData=t.subarray(s)),i.formatTimescale=a.formatTimescale=a.timescale=o.timescale=1e3,i.timescale=i.sampleRate||0,!i.exist()&&i.hasSample()&&i.reset(),!a.exist()&&a.hasSample()&&a.reset(),{videoTrack:a,audioTrack:i,metadataTrack:o}}},{key:"fix",value:function(e,t,r){return this._fixer.fix(e,t,r),{videoTrack:this.videoTrack,audioTrack:this.audioTrack,metadataTrack:this.metadataTrack}}},{key:"demuxAndFix",value:function(e,t,r,n){return this.demux(e,t,r),this.fix(n,t,r)}},{key:"_parseAudio",value:function(t,r){if(t.length){var n=(240&t[0])>>>4,i=this.audioTrack;if(10!==n&&7!==n&&8!==n)return KI.warn("Unsupported sound format: ".concat(n)),void i.reset();if(10!==n){var a=(12&t[0])>>2,o=(2&t[0])>>1,s=1&t[0];i.sampleRate=e.AUDIO_RATE[a],i.sampleSize=o?16:8,i.channelCount=s+1}10===n?this._parseAac(t,r):this._parseG711(t,r,n)}}},{key:"_parseG711",value:function(e,t,r){var n=this.audioTrack;n.codecType=7===r?uI:cI,n.sampleRate=8e3,n.codec=n.codecType,n.samples.push(new _I(t,e.subarray(1)))}},{key:"_parseAac",value:function(e,t){var r=this.audioTrack;if(r.codecType=sI,0===e[1]){var n=PI.parseAudioSpecificConfig(e.subarray(2));n?(r.codec=n.codec,r.channelCount=n.channelCount,r.sampleRate=n.sampleRate,r.config=n.config,r.objectType=n.objectType,r.sampleRateIndex=n.samplingFrequencyIndex):(r.reset(),KI.warn("Cannot parse AudioSpecificConfig",e))}else if(1===e[1]){if(null==t)return;r.samples.push(new _I(t,e.subarray(2)))}else KI.warn("Unknown AACPacketType: ".concat(e[1]))}},{key:"_parseVideo",value:function(e,t){var r=this;if(!(e.length<6)){var n=(240&e[0])>>>4,i=15&e[0],a=this.videoTrack;if(7!==i&&12!==i)return a.reset(),void KI.warn("Unsupported codecId: ".concat(i));var o=12===i;a.codecType=o?oI:aI;var s=e[1],u=(e[2]<<16|e[3]<<8|e[4])<<8>>8;if(0===s){var c=e.subarray(5),l=o?GI.parseHEVCDecoderConfigurationRecord(c):zI.parseAVCDecoderConfigurationRecord(c);if(l){var f=l.hvcC,h=l.sps,d=l.ppsArr,p=l.spsArr,v=l.vpsArr,g=l.nalUnitSize;f&&(a.hvcC=a.hvcC||f),h&&(a.codec=h.codec,a.width=h.width,a.height=h.height,a.sarRatio=h.sarRatio,a.fpsNum=h.fpsNum,a.fpsDen=h.fpsDen),p.length&&(a.sps=p),d.length&&(a.pps=d),v&&v.length&&(a.vps=v),g&&(a.nalUnitSize=g)}else KI.warn("Cannot parse ".concat(o?"HEVC":"AVC","DecoderConfigurationRecord"),e)}else if(1===s){var y=NI.parseAvcC(e.subarray(5),a.nalUnitSize);if((y=this._checkAddMetaNalToUnits(o,y,a))&&y.length){var m=new mI(t+u,t,y);1===n&&m.setToKeyframe(),a.samples.push(m),of(y).call(y,(function(e){var n=o?e[0]>>>1&63:31&e[0];switch(n){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!o&&5!==n||o&&5===n)break;m.setToKeyframe();break;case 6:case 39:case 40:if(!o&&6!==n||o&&6===n)break;r.metadataTrack.seiSamples.push(new kI(NI.parseSEI(NI.removeEPB(e),o),t+u))}})),m.keyframe&&this._gopId++,m.gopId=this._gopId}else KI.warn("Cannot parse NALUs",e)}else 2===s||KI.warn("Unknown AVCPacketType: ".concat(s))}}},{key:"_checkAddMetaNalToUnits",value:function(e,t,r){if(!e||!this._needAddMetaBeforeKeyFrameNal)return this._needAddMetaBeforeKeyFrameNal=!1,t;var n=D_(t).call(t,(function(e){return e[0]>>>1&63}));return tA(n).call(n,32)?(this._needAddMetaBeforeKeyFrameNal=!1,t):(t.unshift(r.pps[0]),t.unshift(r.sps[0]),t.unshift(r.vps[0]),jl(t).call(t,Boolean))}},{key:"_parseScript",value:function(e,t){this.metadataTrack.flvScriptSamples.push(new wI(HI.parse(e),t))}}],[{key:"probe",value:function(e){return 70===e[0]&&76===e[1]&&86===e[2]&&1===e[3]&&BI(e,5)>=9}}]),e}();HU(WI,"AUDIO_RATE",[5500,11e3,22e3,44e3]);var YI=tr,JI=P,QI=Xe,XI=c,$I=function(){var e=YI(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t},ZI=RegExp.prototype,eM=c,tM=function(e){var t=e.flags;return void 0!==t||"flags"in ZI||QI(e,"flags")||!XI(ZI,e)?t:JI($I,e)},rM=RegExp.prototype,nM=t((function(e){return e===rM||eM(rM,e)?tM(e):e.flags})),iM=Hn("Array").entries,aM=Zr,oM=Xe,sM=c,uM=iM,cM=Array.prototype,lM={DOMTokenList:!0,NodeList:!0},fM=t((function(e){var t=e.entries;return e===cM||sM(cM,e)&&t===cM.entries||oM(lM,aM(e))?uM:t}));var hM,dM=function(){function e(){zU(this,e),this.buffer=new Uint8Array(0)}return qU(e,[{key:"write",value:function(){for(var e=this,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];of(r).call(r,(function(t){t?e.buffer=function(e){for(var t=0,r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];of(n).call(n,(function(e){t+=e.length}));var a=new e(t),o=0;return of(n).call(n,(function(e){a.set(e,o),o+=e.length})),a}(Uint8Array,e.buffer,t):window.console.warn(t)}))}}],[{key:"writeUint16",value:function(e){return new Uint8Array([e>>8&255,255&e])}},{key:"writeUint32",value:function(e){return new Uint8Array([e>>24,e>>16&255,e>>8&255,255&e])}}]),e}(),pM=Math.pow(2,32)-1,vM=function(){function e(){zU(this,e)}return qU(e,null,[{key:"box",value:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];r=jl(r).call(r,Boolean);var i=8+xC(r).call(r,(function(e,t){return e+t.byteLength}),0),a=new Uint8Array(i);a[0]=i>>24&255,a[1]=i>>16&255,a[2]=i>>8&255,a[3]=255&i,a.set(e,4);var o=8;return of(r).call(r,(function(e){a.set(e,o),o+=e.byteLength})),a}},{key:"ftyp",value:function(t){return Cm(t).call(t,(function(e){return e.type===rI&&e.codecType===oI}))?e.FTYPHEV1:e.FTYPAVC1}},{key:"initSegment",value:function(t){return MI(e.ftyp(t),e.moov(t))}},{key:"pssh",value:function(t){var r,n=new Uint8Array(Qn(r=[1,0,0,0]).call(r,[16,119,239,236,192,178,77,2,172,227,60,30,82,226,251,75],[0,0,0,1],FI(t.kid),[0,0,0,0]));return e.box(e.types.pssh,n)}},{key:"moov",value:function(t){if(t[0].useEME&&(t[0].encv||t[0].enca)){var r;t[0].pssh||(t[0].pssh={kid:t[0].kid});var n=this.pssh(t[0].pssh);return e.box.apply(e,Qn(r=[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale),e.mvex(t)]).call(r,$U(D_(t).call(t,(function(t){return e.trak(t)}))),[n]))}var i;return e.box.apply(e,Qn(i=[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale)]).call(i,$U(D_(t).call(t,(function(t){return e.trak(t)}))),[e.mvex(t)]))}},{key:"mvhd",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4;return e.box(e.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,t>>24&255,t>>16&255,t>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}},{key:"trak",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.tkhdDuration||0,t.width,t.height),e.mdia(t))}},{key:"tkhd",value:function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return e.box(e.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>24&255,t>>16&255,t>>8&255,255&t,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>8&255,255&n,0,0,i>>8&255,255&i,0,0]))}},{key:"mdia",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minf(t))}},{key:"mdhd",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4;return e.box(e.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,t>>24&255,t>>16&255,t>>8&255,255&t,85,196,0,0]))}},{key:"hdlr",value:function(t){return e.box(e.types.hdlr,e.HDLR_TYPES[t])}},{key:"minf",value:function(t){return e.box(e.types.minf,t.type===rI?e.VMHD:e.SMHD,e.DINF,e.stbl(t))}},{key:"stbl",value:function(t){var r=[];return t&&t.ext&&t.ext.stss&&r.push(e.stss(fM(t.ext.stss))),e.box(e.types.stbl,e.stsd(t),e.STTS,r[0],e.STSC,e.STSZ,e.STCO)}},{key:"stsd",value:function(t){var r;return r="audio"===t.type?t.useEME&&t.enca?e.enca(t):e.mp4a(t):t.useEME&&t.encv?e.encv(t):e.avc1hev1(t),e.box(e.types.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),r)}},{key:"enca",value:function(t){var r=t.enca.channelCount,n=t.enca.sampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,r,0,16,0,0,0,0,n>>8&255,255&n,0,0]),a=e.esds(t.config),o=e.sinf(t.enca);return e.box(e.types.enca,i,a,o)}},{key:"encv",value:function(t){var r,n,i,a=t.sps.length>0?t.sps[0]:[],o=t.pps.length>0?t.pps[0]:[],s=t.width,u=t.height,c=t.sarRatio[0],l=t.sarRatio[1],f=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,s>>8&255,255&s,u>>8&255,255&u,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),h=new Uint8Array(Qn(n=Qn(r=Qn(i=[1,a[1],a[2],a[3],255,225,a.length>>>8&255,255&a.length]).apply(i,$U(a))).call(r,[1,o.length>>>8&255,255&o.length])).apply(n,$U(o))),d=new Uint8Array([0,0,88,57,0,15,200,192,0,4,86,72]),p=e.sinf(t.encv),v=new Uint8Array([c>>24,c>>16&255,c>>8&255,255&c,l>>24,l>>16&255,l>>8&255,255&l]);return e.box(e.types.encv,f,e.box(e.types.avcC,h),e.box(e.types.btrt,d),p,e.box(e.types.pasp,v))}},{key:"schi",value:function(t){var r=new Uint8Array([]),n=e.tenc(t);return e.box(e.types.schi,r,n)}},{key:"tenc",value:function(t){var r,n=new Uint8Array(Qn(r=[0,0,0,0,0,0,255&t.default_IsEncrypted,255&t.default_IV_size]).call(r,FI(t.default_KID)));return e.box(e.types.tenc,n)}},{key:"sinf",value:function(t){var r=new Uint8Array([]),n=new Uint8Array([t.data_format.charCodeAt(0),t.data_format.charCodeAt(1),t.data_format.charCodeAt(2),t.data_format.charCodeAt(3)]),i=new Uint8Array([0,0,0,0,99,101,110,99,0,1,0,0]),a=e.schi(t);return e.box(e.types.sinf,r,e.box(e.types.frma,n),e.box(e.types.schm,i),a)}},{key:"avc1hev1",value:function(t){var r,n=t.codecType===oI,i=n?e.types.hvc1:e.types.avc1,a=n?e.hvcC(t):e.avcC(t),o=[new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t.width>>8&255,255&t.width,t.height>>8&255,255&t.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a];return n?o.push(e.box(e.types.fiel,new Uint8Array([1,0]))):t.sarRatio&&t.sarRatio.length>1&&o.push(e.pasp(t.sarRatio)),e.box.apply(e,Qn(r=[i]).call(r,o))}},{key:"avcC",value:function(t){var r,n,i,a,o,s,u=[],c=[];return of(r=t.sps).call(r,(function(e){s=e.byteLength,u.push(s>>>8&255),u.push(255&s),u.push.apply(u,$U(e))})),of(n=t.pps).call(n,(function(e){s=e.byteLength,c.push(s>>>8&255),c.push(255&s),c.push.apply(c,$U(e))})),e.box(e.types.avcC,new Uint8Array(Qn(a=Qn(i=Qn(o=[1,u[3],u[4],u[5],255,224|t.sps.length]).apply(o,u)).call(i,[t.pps.length])).apply(a,c)))}},{key:"hvcC",value:function(t){var r=t.hvcC;if(r instanceof ArrayBuffer||r instanceof Uint8Array)return r;var n,i=t.vps,a=t.sps,o=t.pps;if(r){var s=r.generalProfileCompatibilityFlags,u=r.generalConstraintIndicatorFlags,c=(i.length&&1)+(a.length&&1)+(o.length&&1);n=[1,r.generalProfileSpace<<6|r.generalTierFlag<<5|r.generalProfileIdc,s>>>24,s>>>16,s>>>8,s,u[0],u[1],u[2],u[3],u[4],u[5],r.generalLevelIdc,240,0,252,252|r.chromaFormatIdc,248|r.bitDepthLumaMinus8,248|r.bitDepthChromaMinus8,0,0,r.numTemporalLayers<<3|r.temporalIdNested<<2|3,c];var l=function(e){var t;n.push(e.length>>8,e.length),(t=n).push.apply(t,$U(e))};i.length&&(n.push(160,0,i.length),of(i).call(i,l)),a.length&&(n.push(161,0,a.length),of(a).call(a,l)),o.length&&(n.push(162,0,o.length),of(o).call(o,l))}else n=[1,1,96,0,0,0,144,0,0,0,0,0,93,240,0,252,253,248,248,0,0,15,3,160,0,1,0,24,64,1,12,1,255,255,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,153,152,9,161,0,1,0,45,66,1,1,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,160,2,128,128,45,22,89,153,164,147,43,154,128,128,128,130,0,0,3,0,2,0,0,3,0,50,16,162,0,1,0,7,68,1,193,114,180,98,64];return e.box(e.types.hvcC,new Uint8Array(n))}},{key:"pasp",value:function(t){var r=XU(t,2),n=r[0],i=r[1];return e.box(e.types.pasp,new Uint8Array([n>>24,n>>16&255,n>>8&255,255&n,i>>24,i>>16&255,i>>8&255,255&i]))}},{key:"mp4a",value:function(t){return e.box(e.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,t.sampleRate>>8&255,255&t.sampleRate,0,0]),t.config.length?e.esds(t.config):void 0)}},{key:"esds",value:function(t){var r,n,i,a=t.length;return e.box(e.types.esds,new Uint8Array(Qn(r=Qn(n=Qn(i=[0,0,0,0,3,23+a,0,0,0,4,15+a,64,21,0,6,0,0,0,218,192,0,0,218,192,5]).call(i,[a])).call(n,t)).call(r,[6,1,2])))}},{key:"mvex",value:function(t){var r;return e.box.apply(e,Qn(r=[e.types.mvex]).call(r,$U(D_(t).call(t,(function(t){return e.trex(t.id)})))))}},{key:"trex",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}},{key:"trex1",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,2,0,0,0,0,0,0,1,0,0]))}},{key:"trex2",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,4,0,0,0,0,0,2,0,0,0]))}},{key:"moof",value:function(t){var r;return e.box.apply(e,Qn(r=[e.types.moof,e.mfhd(t[0].samples?t[0].samples[0].gopId:0)]).call(r,$U(D_(t).call(t,(function(t){return e.traf(t)})))))}},{key:"mfhd",value:function(t){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"traf",value:function(t){var r,n=e.tfhd(t.id),i=e.tfdt(t,t.baseMediaDecodeTime),a=0;if(t.isVideo&&t.videoSenc&&(r=t.videoSenc,of(r).call(r,(function(e){a+=8,e.subsamples&&e.subsamples.length&&(a+=2,a+=6*e.subsamples.length)}))),t.videoSencLength=a,t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)){if(t.isVideoEncryption){if(t.isVideo){var o=e.saiz(t),s=e.saio(t),u=e.trun1(t),c=e.senc(t);return e.box(e.types.traf,n,i,o,s,u,c)}if(t.isAudioEncryption){var l=e.sbgp(),f=e.saiz(t),h=e.saio(t),d=e.senc(t),p=e.trun1(t);return e.box(e.types.traf,n,i,l,f,h,d,p)}var v=e.sbgp(),g=e.trun1(t);return e.box(e.types.traf,n,i,v,g)}if(t.isVideo){var y=e.trun1(t);return e.box(e.types.traf,n,i,y)}var m=e.sbgp(),_=e.saiz(t),b=e.saio(t),w=e.senc(t),k=e.trun1(t);return e.box(e.types.traf,n,i,m,_,b,w,k)}var S=e.sdtp(t);return e.box(e.types.traf,n,i,S,e.trun(t.samples,S.byteLength+76))}},{key:"sdtp",value:function(t){var r,n=new dM;return of(r=t.samples).call(r,(function(e){n.write(new Uint8Array(t.isVideo?[e.keyframe?32:16]:[16]))})),e.box(e.types.sdtp,this.extension(0,0),n.buffer)}},{key:"trun1",value:function(t){var r,n=new dM,i=dM.writeUint32(t.samples.length),a=null;if(t.isVideo){var o=t.videoSencLength;a=dM.writeUint32(16*t.samples.length+o+149),!t.isVideoEncryption&&t.isAudioEncryption&&(a=dM.writeUint32(16*t.samples.length+92))}else{var s=12*t.samples.length+124;t.isAudioEncryption&&(s=12*t.samples.length+8*t.audioSenc.length+177),a=dM.writeUint32(s)}return of(r=t.samples).call(r,(function(e){n.write(dM.writeUint32(e.duration)),n.write(dM.writeUint32(e.size)),n.write(dM.writeUint32(e.keyframe?33554432:65536)),t.isVideo&&n.write(dM.writeUint32(e.cts?e.cts:0))})),e.box(e.types.trun,this.extension(0,nM(t)),i,a,n.buffer)}},{key:"senc",value:function(t){var r=new dM,n=t.samples.length,i=t.isVideo?16:8,a=t.isVideo?2:0,o=[],s=0;return t.isVideo?(o=t.videoSenc,s=t.videoSencLength):o=t.audioSenc,s=s||i*n,r.write(dM.writeUint32(16+s),e.types.senc,this.extension(0,a)),r.write(dM.writeUint32(n)),of(o).call(o,(function(e){for(var t=0;t<e.InitializationVector.length;t++)r.write(new Uint8Array([e.InitializationVector[t]]));var n;e.subsamples&&e.subsamples.length&&(r.write(dM.writeUint16(e.subsamples.length)),of(n=e.subsamples).call(n,(function(e){r.write(dM.writeUint16(e.BytesOfClearData)),r.write(dM.writeUint32(e.BytesOfProtectedData))})))})),r.buffer}},{key:"saio",value:function(t){var r=12*t.samples.length+141;!t.isVideo&&t.isAudioEncryption&&(r=149);var n=new Uint8Array([1,0,0,0,0,0,0,1,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r]);return e.box(e.types.saio,n)}},{key:"saiz",value:function(t){var r=t.samples.length,n=new Uint8Array([0,0,0,0,16,r>>24&255,r>>16&255,r>>8&255,255&r]);return e.box(e.types.saiz,n)}},{key:"sbgp",value:function(){var t=new Uint8Array([114,111,108,108,0,0,0,1,0,0,1,25,0,0,0,1]);return e.box(e.types.sbgp,this.extension(0,0),t)}},{key:"extension",value:function(e,t){return new Uint8Array([e,t>>16&255,t>>8&255,255&t])}},{key:"tfhd",value:function(t){return e.box(e.types.tfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"tfdt",value:function(t,r){var n=Math.floor(r/(pM+1)),i=Math.floor(r%(pM+1));return t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)?e.box(e.types.tfdt,new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i])):e.box(e.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,i>>24,i>>16&255,i>>8&255,255&i]))}},{key:"trun",value:function(t,r){var n=t.length,i=12+16*n;r+=8+i;var a=new Uint8Array(i);a.set([0,0,15,1,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0);for(var o=0;o<n;o++){var s=t[o],u=s.duration,c=s.size,l=s.flag,f=void 0===l?{}:l,h=s.cts,d=void 0===h?0:h;a.set([u>>>24&255,u>>>16&255,u>>>8&255,255&u,c>>>24&255,c>>>16&255,c>>>8&255,255&c,f.isLeading<<2|(null===f.dependsOn||void 0===f.dependsOn?1:f.dependsOn),f.isDependedOn<<6|f.hasRedundancy<<4|f.paddingValue<<1|(null===f.isNonSyncSample||void 0===f.isNonSyncSample?1:f.isNonSyncSample),61440&f.degradationPriority,15&f.degradationPriority,d>>>24&255,d>>>16&255,d>>>8&255,255&d],12+16*o)}return e.box(e.types.trun,a)}},{key:"moovMP4",value:function(t){var r;return e.box.apply(e,Qn(r=[e.types.moov,e.mvhd(t[0].duration,t[0].timescale)]).call(r,$U(D_(t).call(t,(function(t){return e.trackMP4(t)})))))}},{key:"trackMP4",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.duration,t.width,t.height),e.mdiaMP4(t))}},{key:"mdiaMP4",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minfMP4(t))}},{key:"minfMP4",value:function(t){return e.box(e.types.minf,t.type===rI?e.VMHD:e.SMHD,e.DINF,e.stblMP4(t))}},{key:"stblMP4",value:function(t){var r,n=t.ext,i=[e.stsd(t),e.stts(n.stts),e.stsc(n.stsc),e.stsz(n.stsz),e.stco(n.stco)];return n.stss.length&&i.push(e.stss(n.stss)),n.ctts.length&&i.push(e.ctts(n.ctts)),e.box.apply(e,Qn(r=[e.types.stbl]).call(r,i))}},{key:"stts",value:function(t){var r=t.length,n=new Uint8Array(8*r),i=0;return of(t).call(t,(function(e){var t=e.value,r=e.count;n.set([r>>24,r>>16&255,r>>8&255,255&r,t>>24,t>>16&255,t>>8&255,255&t],i),i+=8})),e.box(e.types.stts,MI(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stsc",value:function(t){var r=t.length,n=new Uint8Array(12*r),i=0;return of(t).call(t,(function(e){var t=e.firstChunk,r=e.samplesPerChunk,a=e.sampleDescIndex;n.set([t>>24,t>>16&255,t>>8&255,255&t,r>>24,r>>16&255,r>>8&255,255&r,a>>24,a>>16&255,a>>8&255,255&a],i),i+=12})),e.box(e.types.stsc,MI(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stsz",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return of(t).call(t,(function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stsz,MI(new Uint8Array([0,0,0,0,0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stco",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return of(t).call(t,(function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stco,MI(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stss",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return of(t).call(t,(function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stss,MI(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"ctts",value:function(t){var r=t.length,n=new Uint8Array(8*r),i=0;return of(t).call(t,(function(e){var t=e.value,r=e.count;n.set([r>>24,r>>16&255,r>>8&255,255&r,t>>24,t>>16&255,t>>8&255,255&t],i),i+=8})),e.box(e.types.ctts,MI(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"styp",value:function(){return e.box(e.types.styp,new Uint8Array([109,115,100,104,0,0,0,0,109,115,100,104,109,115,105,120]))}},{key:"sidx",value:function(t){var r,n=t.timescale,i=t.samples[0].duration,a=i*t.samples.length,o=t.samples[0].sampleOffset*i,s=8;of(r=t.samples).call(r,(function(e){s+=e.size}));var u=0;if(t.isVideo){var c,l=0;t.videoSenc&&(c=t.videoSenc),t.isVideo&&of(c).call(c,(function(e){l+=8,e.subsamples&&e.subsamples.length&&(l+=2,l+=6*e.subsamples.length)})),t.videoSencLength=l,u=s+141+16*t.samples.length+l,t.useEME&&t.isAudioEncryption&&!t.isVideoEncryption&&(u=s+16*t.samples.length+84)}else u=s+116+12*t.samples.length,t.useEME&&t.isAudioEncryption&&(u=s+169+12*t.samples.length+8*t.audioSenc.length);var f=new Uint8Array([0,0,0,0,0,0,0,255&t.id,n>>24&255,n>>16&255,n>>8&255,255&n,o>>24&255,o>>16&255,o>>8&255,255&o,0,0,0,0,0,0,0,1,0,u>>16&255,u>>8&255,255&u,a>>24&255,a>>16&255,a>>8&255,255&a,144,0,0,0]);return e.box(e.types.sidx,f)}},{key:"mdat",value:function(t){return e.box(e.types.mdat,t)}}]),e}();HU(vM,"types",xC(hM=["avc1","avcC","hvc1","hvcC","dinf","dref","esds","ftyp","hdlr","mdat","mdhd","mdia","mfhd","minf","moof","moov","mp4a","mvex","mvhd","pasp","stbl","stco","stsc","stsd","stsz","stts","tfdt","tfhd","traf","trak","trex","tkhd","vmhd","smhd","ctts","stss","styp","pssh","sidx","sbgp","saiz","saio","senc","trun","encv","enca","sinf","btrt","frma","tenc","schm","schi","mehd","fiel","sdtp"]).call(hM,(function(e,t){return e[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)],e}),Uf(null))),HU(vM,"HDLR_TYPES",{video:new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),audio:new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0])}),HU(vM,"FTYPAVC1",vM.box(vM.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]))),HU(vM,"FTYPHEV1",vM.box(vM.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,104,101,118,49]))),HU(vM,"DINF",vM.box(vM.types.dinf,vM.box(vM.types.dref,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1])))),HU(vM,"VMHD",vM.box(vM.types.vmhd,new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]))),HU(vM,"SMHD",vM.box(vM.types.smhd,new Uint8Array([0,0,0,0,0,0,0,0]))),HU(vM,"StblTable",new Uint8Array([0,0,0,0,0,0,0,0])),HU(vM,"STTS",vM.box(vM.types.stts,vM.StblTable)),HU(vM,"STSC",vM.box(vM.types.stsc,vM.StblTable)),HU(vM,"STSZ",vM.box(vM.types.stsz,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]))),HU(vM,"STCO",vM.box(vM.types.stco,vM.StblTable));var gM=function(){function e(t,r){zU(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),e.disabled=r}return qU(e,[{key:"debug",value:function(){var t,r;if(!e.disabled){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];(r=console).debug.apply(r,Qn(t=[this._prefix]).call(t,i))}}},{key:"log",value:function(){var t,r;if(!e.disabled){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];(r=console).log.apply(r,Qn(t=[this._prefix]).call(t,i))}}},{key:"warn",value:function(){var t,r;if(!e.disabled){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];(r=console).warn.apply(r,Qn(t=[this._prefix]).call(t,i))}}},{key:"error",value:function(){var t,r;if(!e.disabled){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];(r=console).error.apply(r,Qn(t=[this._prefix]).call(t,i))}}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();HU(gM,"disabled",!0);var yM=function(){function e(t,r,n){zU(this,e),this.videoTrack=t,this.audioTrack=r;var i=/Chrome\/([^.]+)/.exec(navigator.userAgent);this.forceFirstIDR=i&&Number(i[1])<50,this.log=new gM("FMP4Remuxer",!n||!n.openLog||!n.openLog)}return qU(e,[{key:"remux",value:function(){var e,t,r,n,i,a=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.videoTrack,u=this.audioTrack,c=s.exist(),l=u.exist(),f=[];return a&&(o&&o.initMerge?(c&&f.push(this.videoTrack),l&&f.push(this.audioTrack),r=vM.initSegment(f)):(c&&(e=vM.initSegment([this.videoTrack])),l&&(t=vM.initSegment([this.audioTrack])))),c&&s.hasSample()&&(n=this._remuxVideo()),l&&u.hasSample()&&(i=this._remuxAudio()),s.samples=[],u.samples=[],{initSegment:r,videoInitSegment:e,audioInitSegment:t,videoSegment:n,audioSegment:i}}},{key:"_remuxVideo",value:function(){var e=this.videoTrack;this.forceFirstIDR&&(e.samples[0].flag={dependsOn:2,isNonSyncSample:0});var t=e.samples,r=0;of(t).call(t,(function(e){var t;r+=xC(t=e.units).call(t,(function(e,t){return e+t.byteLength}),0),r+=4*e.units.length}));for(var n,i=new Uint8Array(r),a=new DataView(i.buffer),o=function(e,r){var o;r=t[s];var u=0;of(o=r.units).call(o,(function(t){a.setUint32(e,t.byteLength),e+=4,i.set(t,e),e+=t.byteLength,u+=4+t.byteLength})),r.size=u,c=e,n=r},s=0,u=t.length,c=0;s<u;s++)o(c,n);var l=vM.mdat(i);return MI(vM.moof([e]),l)}},{key:"_remuxAudio",value:function(){var e,t,r=this.audioTrack,n=new Uint8Array(xC(e=r.samples).call(e,(function(e,t){return e+t.size}),0));xC(t=r.samples).call(t,(function(e,t){return n.set(t.data,e),e+t.size}),0);var i=vM.mdat(n);return MI(vM.moof([r]),i)}},{key:"reset",value:function(){this.videoTrack.reset(),this.audioTrack.reset()}}]),e}(),mM=new KC("BufferService"),_M=function(){function e(t,r,n){rm(this,e),am(this,"flv",null),am(this,"_demuxer",new WI),am(this,"_remuxer",null),am(this,"_mse",null),am(this,"_softVideo",null),am(this,"_sourceCreated",!1),am(this,"_needInitSegment",!0),am(this,"_discontinuity",!0),am(this,"_contiguous",!1),am(this,"_initSegmentId",""),am(this,"_cachedBuffer",null),am(this,"_demuxStartTime",0),am(this,"_opts",null),this.flv=t,this._opts=n,r?this._softVideo=r:(this._remuxer=new yM(this._demuxer.videoTrack,this._demuxer.audioTrack),this._mse=new eD,this._mse.bindMedia(t.media))}var t,r,n,i,a,o;return im(e,[{key:"baseDts",get:function(){var e,t;return null===(e=this._demuxer)||void 0===e||null===(t=e._fixer)||void 0===t?void 0:t._baseDts}},{key:"seamlessSwitch",value:function(){this._needInitSegment=!0,this._discontinuity=!0,this._contiguous=!0,this._initSegmentId=""}},{key:"unContiguous",value:function(e){this._contiguous=!1,this._demuxStartTime=e}},{key:"reset",value:(o=tm(Zy().mark((function e(){var t,r=arguments;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=r.length>0&&void 0!==r[0]&&r[0],!this._mse||t){e.next=6;break}return e.next=4,this._mse.unbindMedia();case 4:return e.next=6,this._mse.bindMedia(this.flv.media);case 6:this._needInitSegment=!0,this._discontinuity=!0,this._contiguous=!1,this._sourceCreated=!1,this._initSegmentId="";case 11:case"end":return e.stop()}}),e,this)}))),function(){return o.apply(this,arguments)})},{key:"endOfStream",value:(a=tm(Zy().mark((function e(){return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=7;break}if(!this._sourceCreated){e.next=5;break}return e.next=4,this._mse.endOfStream();case 4:this.flv.emit(SD);case 5:e.next=8;break;case 7:this._softVideo&&this._softVideo.endOfStream();case 8:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)})},{key:"updateDuration",value:(i=tm(Zy().mark((function e(t){return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=7;break}if(this._mse.isOpened){e.next=4;break}return e.next=4,this._mse.open();case 4:return mM.debug("update duration",t),e.next=7,this._mse.updateDuration(t);case 7:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"destroy",value:(n=tm(Zy().mark((function e(){return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=3;break}return e.next=3,this._mse.unbindMedia();case 3:this._mse=null,this._softVideo=null,this._demuxer=null,this._remuxer=null;case 7:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"appendBuffer",value:(r=tm(Zy().mark((function e(t){var r,n,i,a,o,s,u,c,l,f,h,d,p,v;return Zy().wrap((function(e){for(var g,y,m,_;;)switch(e.prev=e.next){case 0:if(this._cachedBuffer&&(t=KD(this._cachedBuffer,t),this._cachedBuffer=null),r=this._demuxer,t&&t.length&&r){e.next=4;break}return e.abrupt("return");case 4:e.prev=4,r.demuxAndFix(t,this._discontinuity,this._contiguous,this._demuxStartTime),e.next=11;break;case 8:throw e.prev=8,e.t0=e.catch(4),new FC(DC,BC.FLV,e.t0);case 11:if(n=r.videoTrack,i=r.audioTrack,a=r.metadataTrack,o=n.exist(),s=i.exist(),this._opts.onlyAudio&&(o=!1,n.present=!1),this._opts.onlyVideo&&(s=!1,i.present=!1),!(!o&&n.present||!s&&i.present)){e.next=29;break}if(u=0,(c=o?n:s?i:void 0)&&c.samples.length&&(u=(c.samples[c.samples.length-1].originPts-c.samples[0].originPts)/c.timescale*1e3),!(u>this._opts.analyzeDuration)){e.next=27;break}mM.warn("analyze duration exceeded, ".concat(u,"ms"),c),n.present=o,i.present=s,this.flv.emit(wD,{duration:u}),e.next=29;break;case 27:return this._cachedBuffer=t,e.abrupt("return");case 29:if(l=n.type,f=i.type,this._fireEvents(n,i,a),this._discontinuity=!1,this._contiguous=!0,this._demuxStartTime=0,h=this._mse,this.flv.emit(PD,{videoTrack:n}),(d=Qn(g=Qn(y=Qn(m=Qn(_="".concat(n.codec,"/")).call(_,n.width,"/")).call(m,n.height,"/")).call(y,i.codec,"/")).call(g,i.config))!==this._initSegmentId&&(this._needInitSegment=!0,this._initSegmentId=d,this._emitMetaParsedEvent(n,i)),!h){e.next=65;break}if(this._sourceCreated){e.next=47;break}return e.next=43,h.open();case 43:o&&(mM.log("codec: video/mp4;codecs=".concat(n.codec)),h.createSource(l,"video/mp4;codecs=".concat(n.codec))),s&&(mM.log("codec: audio/mp4;codecs=".concat(i.codec)),h.createSource(f,"audio/mp4;codecs=".concat(i.codec))),this._sourceCreated=!0,this.flv.emit(bD);case 47:e.prev=47,p=this._remuxer.remux(this._needInitSegment),e.next=54;break;case 51:throw e.prev=51,e.t1=e.catch(47),new FC(PC,BC.FMP4,e.t1);case 54:if(!this._needInitSegment||p.videoInitSegment||p.audioInitSegment){e.next=56;break}return e.abrupt("return");case 56:return this._needInitSegment=!1,v=[],p.videoInitSegment&&v.push(h.append(l,p.videoInitSegment)),p.audioInitSegment&&v.push(h.append(f,p.audioInitSegment)),p.videoSegment&&v.push(h.append(l,p.videoSegment)),p.audioSegment&&v.push(h.append(f,p.audioSegment)),e.abrupt("return",Gg.all(v));case 65:this._softVideo&&this._softVideo.appendBuffer(n,i);case 66:case"end":return e.stop()}}),e,this,[[4,8],[47,51]])}))),function(e){return r.apply(this,arguments)})},{key:"evictBuffer",value:(t=tm(Zy().mark((function e(t){var r,n,i,a=this;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.flv.media,this._mse&&this._demuxer&&r&&t&&!(t<0)){e.next=3;break}return e.abrupt("return");case 3:if(n=r.currentTime,!((i=n-t)<=0)){e.next=7;break}return e.abrupt("return");case 7:if(!(EC.start(EC.get(r))+1>=i)){e.next=10;break}return e.abrupt("return");case 10:return e.abrupt("return",this._mse.clearBuffer(0,i).then((function(){return a.flv.emit(kD,{removeEnd:i})})));case 11:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"_emitMetaParsedEvent",value:function(e,t){e.exist()&&this.flv.emit(ED,{type:"video",track:e,meta:{codec:e.codec,timescale:e.timescale,width:e.width,height:e.height,sarRatio:e.sarRatio,baseDts:e.baseDts}}),t.exist()&&this.flv.emit(ED,{type:"audio",track:t,meta:{codec:t.codec,channelCount:t.channelCount,sampleRate:t.sampleRate,timescale:t.timescale,baseDts:t.baseDts}}),mM.debug("track parsed",e,t)}},{key:"_fireEvents",value:function(e,t,r){var n,i,a,o,s,u=this;mM.debug(e.samples,t.samples),of(n=r.flvScriptSamples).call(n,(function(e){u.flv.emit(OD,e),mM.debug("flvScriptData",e)})),of(i=e.samples).call(i,(function(e){e.keyframe&&u.flv.emit(xD,{pts:e.pts})})),of(a=e.warnings).call(a,(function(e){var t;switch(e.type){case lI:t=UD;break;case fI:t=ID;break;case hI:t=FD}t&&u.flv.emit(RD,$y($y({},e),{},{type:t})),mM.warn("video exception",e)})),of(o=t.warnings).call(o,(function(e){var t;switch(e.type){case dI:t=MD;break;case pI:t=BD;break;case vI:t=jD}t&&u.flv.emit(RD,$y($y({},e),{},{type:t})),mM.warn("audio exception",e)})),of(s=r.seiSamples).call(s,(function(e){u.flv.emit(TD,$y($y({},e),{},{sei:{code:e.data.type,content:e.data.payload,dts:e.pts}}))}))}}]),e}();function bM(e,t){var r=0,n=e.length-1,i=0,a=0,o=n;for(t<e[0]&&(r=0,a=o+1);a<=o;){if((i=a+Math.floor((o-a)/2))===n||t>=e[i]&&t<e[i+1]){r=i;break}e[i]<t?a=i+1:o=i-1}return r}var wM=new KC("flv"),kM=function(e){om(c,e);var t,r,n,i,a,o,s,u=lm(c);function c(e){var t,r;return rm(this,c),am(cm(t=u.call(this)),"media",null),am(cm(t),"_loading",!1),am(cm(t),"_opts",null),am(cm(t),"_bufferService",null),am(cm(t),"_gapService",null),am(cm(t),"_stats",null),am(cm(t),"_mediaLoader",null),am(cm(t),"_maxChunkWaitTimer",null),am(cm(t),"_tickTimer",null),am(cm(t),"_tickInterval",500),am(cm(t),"_urlSwitching",!1),am(cm(t),"_seamlessSwitching",!1),am(cm(t),"_keyframes",null),am(cm(t),"_acceptRanges",!0),am(cm(t),"_onProgress",function(){var e=tm(Zy().mark((function e(r,n,i,a){var o,s,u,c,l,f,h,d;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=i.startTime,s=i.endTime,u=i.st,c=i.firstByteTime,t._loading=!n,t._firstProgressEmit){e.next=11;break}if(t.media){e.next=6;break}return null===(l=t._mediaLoader)||void 0===l||l.cancel(),e.abrupt("return");case 6:f=a.headers,t.emit(vD,{url:t._opts.url,responseUrl:a.url,elapsed:u?c-u:s-o}),t.emit(yD,{headers:f}),t._acceptRanges=!(null==f||!f.get("Accept-Ranges"))||!(null==f||!f.get("Content-Range")),t._firstProgressEmit=!0;case 11:if(t._bufferService){e.next=13;break}return e.abrupt("return");case 13:return clearTimeout(t._maxChunkWaitTimer),t._bandwidthService.addChunkRecord(null==r?void 0:r.byteLength,s-o),e.prev=15,e.next=18,t._bufferService.appendBuffer(r);case 18:null===(h=t._bufferService)||void 0===h||h.evictBuffer(t._opts.bufferBehind),e.next=24;break;case 21:return e.prev=21,e.t0=e.catch(15),e.abrupt("return",t._emitError(FC.create(e.t0)));case 24:if(t._urlSwitching&&(t._urlSwitching=!1,t.emit(CD,{url:t._opts.url})),t._seamlessSwitching&&(t._seamlessSwitching=!1,t._tick()),!n||t.media.seeking){e.next=32;break}return t.emit(mD),wM.debug("load done"),t._end(),!t.isLive&&t.media.readyState<=2&&t._tick(),e.abrupt("return");case 32:(d=t._opts.maxReaderInterval)&&(clearTimeout(t._maxChunkWaitTimer),t._maxChunkWaitTimer=Sw((function(){wM.debug("onMaxChunkWait",d),t._end()}),d));case 34:case"end":return e.stop()}}),e,null,[[15,21]])})));return function(t,r,n,i){return e.apply(this,arguments)}}()),am(cm(t),"_onRetryError",(function(e,r){wM.debug("load retry",e,r),t.emit(_D,{error:FC.network(e),retryTime:r})})),am(cm(t),"_end",(function(){t._clear(),t._bufferService&&t._bufferService.endOfStream(),wM.debug("end stream")})),am(cm(t),"_tick",(function(){clearTimeout(t._tickTimer);var e=cm(t).media;if(e){t._tickTimer=Sw(t._tick,t._tickInterval);var r=EC.end(EC.get(e));if(!(r<.1)&&e.readyState){var n=t._opts;if(function(e){return e&&!e.paused&&!e.ended&&0!==e.playbackRate&&0!==e.readyState}(e))t._gapService&&t._gapService.do(e,n.maxJumpDistance,t.isLive,3);else{if(!e.currentTime&&t._gapService)return void t._gapService.do(e,n.maxJumpDistance,t.isLive,3);n.isLive&&r>n.disconnectTime&&t.disconnect()}}}})),am(cm(t),"_onPlay",(function(){var e,r,n=t._opts.softDecode||(null===(e=t.media)||void 0===e||null===(r=e.buffered)||void 0===r?void 0:r.length);t.isLive&&!t._loading&&n&&t.replay(void 0,!0)})),am(cm(t),"_onSeeking",tm(Zy().mark((function e(){var r,n,i,a,o;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.isLive||!t.seekable){e.next=14;break}if(r=t._keyframes.times,n=t._keyframes.filepositions,null!=r&&r.length&&null!=n&&n.length){e.next=5;break}return e.abrupt("return");case 5:if(i=t.media.currentTime,a=bM(t._keyframes.times,i),null!=(o=n[a])){e.next=10;break}return e.abrupt("return");case 10:return e.next=12,t._mediaLoader.cancel();case 12:t._loadData(null,[o]),t._bufferService.unContiguous(r[a]);case 14:case"end":return e.stop()}}),e)})))),am(cm(t),"_onTimeupdate",(function(){if(t.media){var e=t._opts,r=t.media.currentTime;if(e.isLive&&e.maxLatency&&e.targetLatency){var n=EC.end(EC.get(t.media));n-r>=e.maxLatency&&(t.media.currentTime=n-e.targetLatency)}t._seiService.throw(r)}})),am(cm(t),"_onFlvScriptData",(function(e){var r,n,i,a,o=null===(r=e.data)||void 0===r||null===(n=r.onMetaData)||void 0===n?void 0:n.keyframes,s=null===(i=e.data)||void 0===i||null===(a=i.onMetaData)||void 0===a?void 0:a.duration;o&&(t._keyframes=o),!t._opts.isLive&&s&&t._bufferService.updateDuration(s)})),t._opts=((r=$y({retryCount:3,retryDelay:1e3,loadTimeout:1e4,maxReaderInterval:5e3,preloadTime:5,isLive:!1,softDecode:!1,bufferBehind:10,maxJumpDistance:3,analyzeDuration:2e4,seamlesslyReload:!1,keepStatusAfterSwitch:!0,onlyVideo:!1,onlyAudio:!1},e)).isLive&&r.preloadTime&&(r.maxLatency||(r.maxLatency=2*r.preloadTime),r.targetLatency||(r.targetLatency=r.preloadTime),null!==r.disconnectTime&&void 0!==r.disconnectTime||(r.disconnectTime=r.maxLatency)),r),t.media=t._opts.media||document.createElement("video"),t._opts.media=null,t._firstProgressEmit=!1,t._mediaLoader=new YD($y($y({},t._opts.fetchOptions),{},{retry:t._opts.retryCount,retryDelay:t._opts.retryDelay,timeout:t._opts.loadTimeout,onRetryError:t._onRetryError,onProgress:t._onProgress,responseType:"arraybuffer"})),t._bufferService=new _M(cm(t),t._opts.softDecode?t.media:void 0,t._opts),t._seiService=new jU(cm(t)),t._bandwidthService=new FU,t._stats=new VU(cm(t)),t._opts.softDecode||(t._gapService=new JD),t.media.addEventListener("play",t._onPlay),t.media.addEventListener("seeking",t._onSeeking),t.media.addEventListener("timeupdate",t._onTimeupdate),t.on(OD,t._onFlvScriptData),t}return im(c,[{key:"version",get:function(){return"3.0.10-alpha.4"}},{key:"isLive",get:function(){return this._opts.isLive}},{key:"baseDts",get:function(){var e;return null===(e=this._bufferService)||void 0===e?void 0:e.baseDts}},{key:"seekable",get:function(){return!!this._keyframes&&this._acceptRanges}},{key:"loader",get:function(){return this._mediaLoader}},{key:"speedInfo",value:function(){return{speed:this._bandwidthService.getLatestSpeed(),avgSpeed:this._bandwidthService.getAvgSpeed()}}},{key:"getStats",value:function(){return this._stats.getStats()}},{key:"bufferInfo",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1;return EC.info(EC.get(this.media),null===(e=this.media)||void 0===e?void 0:e.currentTime,t)}},{key:"playbackQuality",value:function(){return function(e){if(!e)return{};if("function"==typeof e.getVideoPlaybackQuality){var t=e.getVideoPlaybackQuality();return{droppedVideoFrames:t.droppedVideoFrames||t.corruptedVideoFrames,totalVideoFrames:t.totalVideoFrames,creationTime:t.creationTime}}return{droppedVideoFrames:e.webkitDroppedFrameCount,totalVideoFrames:e.webkitDecodedFrameCount,creationTime:performance.now()}}(this.media)}},{key:"load",value:(s=tm(Zy().mark((function e(t){var r,n=arguments;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.length>1&&void 0!==n[1]&&n[1],this._bufferService){e.next=3;break}return e.abrupt("return");case 3:return e.next=5,this._reset(r);case 5:this._loadData(t),clearTimeout(this._tickTimer),this._tickTimer=Sw(this._tick,this._tickInterval);case 8:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"replay",value:(o=tm(Zy().mark((function e(){var t,r,n=this,i=arguments;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=i.length>0&&void 0!==i[0]?i[0]:this._opts.seamlesslyReload,r=i.length>1?i[1]:void 0,this.media){e.next=4;break}return e.abrupt("return");case 4:if(!t){e.next=10;break}return e.next=7,this._clear();case 7:Sw((function(){n._loadData(n._opts.url),n._bufferService.seamlessSwitch(),n._seamlessSwitching=!0})),e.next=12;break;case 10:return e.next=12,this.load();case 12:return e.abrupt("return",this.media.play(!r).catch((function(){})));case 13:case"end":return e.stop()}}),e,this)}))),function(){return o.apply(this,arguments)})},{key:"disconnect",value:function(){return wM.debug("disconnect!"),this._clear()}},{key:"switchURL",value:(a=tm(Zy().mark((function e(t,r){var n=this;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._bufferService){e.next=2;break}return e.abrupt("return");case 2:if(r&&this._opts.isLive){e.next=7;break}return e.next=5,this.load(t);case 5:return this._urlSwitching=!0,e.abrupt("return",this.media.play(!0).catch((function(){})));case 7:return e.next=9,this._clear();case 9:Sw((function(){n._loadData(t),n._bufferService.seamlessSwitch(),n._urlSwitching=!0,n._seamlessSwitching=!0}));case 10:case"end":return e.stop()}}),e,this)}))),function(e,t){return a.apply(this,arguments)})},{key:"destroy",value:(i=tm(Zy().mark((function e(){return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.media){e.next=2;break}return e.abrupt("return");case 2:return this.removeAllListeners(),this._seiService.reset(),this.media.removeEventListener("play",this._onPlay),this.media.removeEventListener("seeking",this._onSeeking),this.media.removeEventListener("timeupdate",this._onTimeupdate),e.next=9,Gg.all([this._clear(),this._bufferService.destroy()]);case 9:this.media=null,this._bufferService=null;case 11:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"_emitError",value:function(e){var t,r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];wM.table(e),wM.error(e),wM.error(null===(t=this.media)||void 0===t?void 0:t.error),this._urlSwitching&&(this._urlSwitching=!1,this._seamlessSwitching=!1,this.emit(DD,e)),this.emit(pD,e),r&&(this._seiService.reset(),this._end())}},{key:"_reset",value:(n=tm(Zy().mark((function e(){var t,r=arguments;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]&&r[0],this._seiService.reset(),this._bandwidthService.reset(),this._stats.reset(),e.next=6,this._clear();case 6:return e.next=8,this._bufferService.reset(t);case 8:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"_loadData",value:(r=tm(Zy().mark((function e(t,r){var n;return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t&&(this._opts.url=t),n=t=this._opts.url,t){e.next=4;break}throw new Error("Source url is missing");case 4:if(this._opts.preProcessUrl&&(n=this._opts.preProcessUrl(t).url),this._mediaLoader.finnalUrl=n,this.emit(gD,{url:n}),wM.debug("load data, loading:",this._loading,n),!this._loading){e.next=11;break}return e.next=11,this._mediaLoader.cancel();case 11:return this._loading=!0,e.prev=12,e.next=15,this._mediaLoader.load({url:n,range:r});case 15:e.next=20;break;case 17:return e.prev=17,e.t0=e.catch(12),e.abrupt("return",this._emitError(FC.network(e.t0)));case 20:case"end":return e.stop()}}),e,this,[[12,17]])}))),function(e,t){return r.apply(this,arguments)})},{key:"_clear",value:(t=tm(Zy().mark((function e(){return Zy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mediaLoader){e.next=3;break}return e.next=3,this._mediaLoader.cancel();case 3:clearTimeout(this._maxChunkWaitTimer),clearTimeout(this._tickTimer),this._loading=!1,this._firstProgressEmit=!1;case 7:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})}],[{key:"isSupported",value:function(e){return e&&"video"!==e&&"audio"!==e?"undefined"!=typeof WebAssembly:eD.isSupported()}},{key:"enableLogger",value:function(){KC.enable(),TI.enable()}},{key:"disableLogger",value:function(){KC.disable(),TI.disable()}}]),c}(Db);try{localStorage.getItem("xgd")?kM.enableLogger():kM.disableLogger()}catch(EM){}var SM=function(){function e(t,r){var n=this;rm(this,e),am(this,"_onLowDecode",(function(){var e,t,r,i,a=n._opts,o=a.media,s=a.innerDegrade,u=a.backupURL;null===(e=n._plugin)||void 0===e||null===(t=e.player)||void 0===t||t.emit("lowdecode",o.degradeInfo),null===(r=n._plugin)||void 0===r||null===(i=r.player)||void 0===i||i.emit("core_event",$y($y({},o.degradeInfo),{},{eventName:LD})),1!==s&&3!==s||!u||n._degrade(u)})),am(this,"_degrade",(function(e){var t=n._plugin.player,r=t.video;if("MVideo"===(null==r?void 0:r.TAG)){var i=t.video.degradeVideo;t.video=i,r.degrade(e),e&&(t.config.url=e);var a=t.root.firstChild;"MVideo"===a.TAG&&t.root.replaceChild(i,a);var o=n._plugin.constructor.pluginName.toLowerCase();t.unRegisterPlugin(o),t.once("canplay",(function(){t.play()}))}})),am(this,"forceDegradeToVideo",(function(e){var t=n._opts.innerDegrade;1!==t&&3!==t||n._degrade(e)})),this._opts=t,this._plugin=r,this._init()}return im(e,[{key:"_init",value:function(){var e=this._opts,t=e.media,r=e.preloadTime,n=e.innerDegrade,i=e.decodeMode;t&&(n&&t.setAttribute("innerdegrade",n),r&&t.setAttribute("preloadtime",r),t.setDecodeMode&&t.setDecodeMode(i),this._bindEvents())}},{key:"_bindEvents",value:function(){this._opts.media.addEventListener("lowdecode",this._onLowDecode)}},{key:"destroy",value:function(){var e,t;null===(e=this._opts)||void 0===e||null===(t=e.media)||void 0===t||t.removeEventListener("lowdecode",this._onLowDecode),this._plugin=null}}]),e}(),xM=function(e){om(r,e);var t=lm(r);function r(){var e,n;rm(this,r);for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return am(cm(n=t.call.apply(t,Qn(e=[this]).call(e,a))),"flv",null),am(cm(n),"pluginExtension",null),am(cm(n),"getStats",(function(){var e;return null===(e=n.flv)||void 0===e?void 0:e.getStats()})),am(cm(n),"destroy",(function(){var e;n.flv&&(n.flv.destroy(),n.flv=null),null===(e=n.pluginExtension)||void 0===e||e.destroy(),n.pluginExtension=null})),am(cm(n),"_onSwitchURL",(function(e,t){var r,i;n.flv&&(n.player.config.url=e,n.flv.switchURL(e,t),!t&&null!==(r=n.player.config)&&void 0!==r&&null!==(i=r.flv)&&void 0!==i&&i.keepStatusAfterSwitch&&n._keepPauseStatus())})),am(cm(n),"_keepPauseStatus",(function(){n.player.paused&&n.player.once("canplay",(function(){n.player.pause()}))})),am(cm(n),"_onDefinitionChange",(function(e){var t=e.to;n.flv&&n.flv.switchURL(t)})),n}return im(r,[{key:"core",get:function(){return this.flv}},{key:"version",get:function(){var e;return null===(e=this.flv)||void 0===e?void 0:e.version}},{key:"softDecode",get:function(){var e,t,r=null===(e=this.player)||void 0===e||null===(t=e.config)||void 0===t?void 0:t.mediaType;return!!r&&"video"!==r&&"audio"!==r}},{key:"loader",get:function(){var e;return null===(e=this.flv)||void 0===e?void 0:e.loader}},{key:"beforePlayerInit",value:function(){var e=this,t=this.player.config;if(t.url){this.flv&&this.flv.destroy(),this.player.switchURL=this._onSwitchURL;var r,n=t.flv||{};if(null!==n.disconnectTime&&void 0!==n.disconnectTime||(n.disconnectTime=0),this.flv=new kM($y({softDecode:this.softDecode,isLive:t.isLive,media:this.player.video,preProcessUrl:function(t,r){var n,i;return(null===(n=(i=e.player).preProcessUrl)||void 0===n?void 0:n.call(i,t,r))||{url:t,ext:r}}},n)),this.softDecode||eS.defineGetterOrSetter(this.player,{url:{get:function(){var t,r;return null===(t=e.flv)||void 0===t||null===(r=t.media)||void 0===r?void 0:r.src},configurable:!0}}),this.softDecode&&(this.pluginExtension=new SM($y({media:this.player.video},t.flv),this),this.player.forceDegradeToVideo=function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return null===(t=e.pluginExtension)||void 0===t?void 0:t.forceDegradeToVideo.apply(t,n)}),t.isLive)null===(r=this.player)||void 0===r||r.useHooks("replay",(function(){var t;return null===(t=e.flv)||void 0===t?void 0:t.replay()}));this.on("urlchange",this._onSwitchURL),this.on("destroy",this.destroy),this._transError(),this._transCoreEvent(vD),this._transCoreEvent(gD),this._transCoreEvent(yD),this._transCoreEvent(mD),this._transCoreEvent(_D),this._transCoreEvent(bD),this._transCoreEvent(wD),this._transCoreEvent(kD),this._transCoreEvent(SD),this._transCoreEvent(xD),this._transCoreEvent(ED),this._transCoreEvent(TD),this._transCoreEvent(AD),this._transCoreEvent(OD),this._transCoreEvent(RD),this._transCoreEvent(CD),this._transCoreEvent(DD),this.flv.load(t.url,!0)}}},{key:"_transError",value:function(){var e=this;this.flv.on(pD,(function(t){e.player&&e.player.emit("error",new Yk(e.player,t))}))}},{key:"_transCoreEvent",value:function(e){var t=this;this.flv.on(e,(function(r){t.player&&t.player.emit("core_event",$y($y({},r),{},{eventName:e}))}))}}],[{key:"pluginName",get:function(){return"flv"}},{key:"isSupported",value:function(e,t){return kM.isSupported(e,t)}}]),r}(eS);am(xM,"Flv",kM),VePlayer.register("plugin:flv",xM)}));
