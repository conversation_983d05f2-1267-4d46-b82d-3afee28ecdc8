!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).VePlayer={})}(this,(function(e){"use strict";var t=function(e){return e.H265="h265",e.H264="h264",e}(t||{}),n=function(e){return e.SoftFirst="soft-first",e.H264First="h264-first",e}(n||{}),r=function(e){return e.Software="software",e.Hardware="hardware",e}(r||{}),i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var a=function(e){return e&&e.Math==Math&&e},s=a("object"==typeof globalThis&&globalThis)||a("object"==typeof window&&window)||a("object"==typeof self&&self)||a("object"==typeof i&&i)||function(){return this}()||i||Function("return this")(),l=function(e){try{return!!e()}catch(t){return!0}},u=!l((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),c=u,h=Function.prototype,f=h.apply,d=h.call,p="object"==typeof Reflect&&Reflect.apply||(c?d.bind(f):function(){return d.apply(f,arguments)}),v=u,g=Function.prototype,y=g.call,m=v&&g.bind.bind(y,y),_=v?m:function(e){return function(){return y.apply(e,arguments)}},b=_,w=b({}.toString),k=b("".slice),E=function(e){return k(w(e),8,-1)},T=E,S=_,C=function(e){if("Function"===T(e))return S(e)},x="object"==typeof document&&document.all,P={all:x,IS_HTMLDDA:void 0===x&&void 0!==x},A=P.all,I=P.IS_HTMLDDA?function(e){return"function"==typeof e||e===A}:function(e){return"function"==typeof e},R={},O=!l((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),D=u,M=Function.prototype.call,L=D?M.bind(M):function(){return M.apply(M,arguments)},N={},U={}.propertyIsEnumerable,F=Object.getOwnPropertyDescriptor,B=F&&!U.call({1:2},1);N.f=B?function(e){var t=F(this,e);return!!t&&t.enumerable}:U;var V,H,j=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},z=l,K=E,W=Object,G=_("".split),q=z((function(){return!W("z").propertyIsEnumerable(0)}))?function(e){return"String"==K(e)?G(e,""):W(e)}:W,Y=function(e){return null==e},X=Y,J=TypeError,Q=function(e){if(X(e))throw J("Can't call method on "+e);return e},$=q,Z=Q,ee=function(e){return $(Z(e))},te=I,ne=P.all,re=P.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:te(e)||e===ne}:function(e){return"object"==typeof e?null!==e:te(e)},ie={},oe=ie,ae=s,se=I,le=function(e){return se(e)?e:void 0},ue=function(e,t){return arguments.length<2?le(oe[e])||le(ae[e]):oe[e]&&oe[e][t]||ae[e]&&ae[e][t]},ce=_({}.isPrototypeOf),he="undefined"!=typeof navigator&&String(navigator.userAgent)||"",fe=s,de=he,pe=fe.process,ve=fe.Deno,ge=pe&&pe.versions||ve&&ve.version,ye=ge&&ge.v8;ye&&(H=(V=ye.split("."))[0]>0&&V[0]<4?1:+(V[0]+V[1])),!H&&de&&(!(V=de.match(/Edge\/(\d+)/))||V[1]>=74)&&(V=de.match(/Chrome\/(\d+)/))&&(H=+V[1]);var me=H,_e=me,be=l,we=s.String,ke=!!Object.getOwnPropertySymbols&&!be((function(){var e=Symbol();return!we(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&_e&&_e<41})),Ee=ke&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Te=ue,Se=I,Ce=ce,xe=Object,Pe=Ee?function(e){return"symbol"==typeof e}:function(e){var t=Te("Symbol");return Se(t)&&Ce(t.prototype,xe(e))},Ae=String,Ie=function(e){try{return Ae(e)}catch(t){return"Object"}},Re=I,Oe=Ie,De=TypeError,Me=function(e){if(Re(e))return e;throw De(Oe(e)+" is not a function")},Le=Me,Ne=Y,Ue=function(e,t){var n=e[t];return Ne(n)?void 0:Le(n)},Fe=L,Be=I,Ve=re,He=TypeError,je={exports:{}},ze=s,Ke=Object.defineProperty,We=function(e,t){try{Ke(ze,e,{value:t,configurable:!0,writable:!0})}catch(n){ze[e]=t}return t},Ge="__core-js_shared__",qe=s[Ge]||We(Ge,{}),Ye=qe;(je.exports=function(e,t){return Ye[e]||(Ye[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.30.2",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.30.2/LICENSE",source:"https://github.com/zloirock/core-js"});var Xe=je.exports,Je=Q,Qe=Object,$e=function(e){return Qe(Je(e))},Ze=$e,et=_({}.hasOwnProperty),tt=Object.hasOwn||function(e,t){return et(Ze(e),t)},nt=_,rt=0,it=Math.random(),ot=nt(1..toString),at=function(e){return"Symbol("+(void 0===e?"":e)+")_"+ot(++rt+it,36)},st=Xe,lt=tt,ut=at,ct=ke,ht=Ee,ft=s.Symbol,dt=st("wks"),pt=ht?ft.for||ft:ft&&ft.withoutSetter||ut,vt=function(e){return lt(dt,e)||(dt[e]=ct&&lt(ft,e)?ft[e]:pt("Symbol."+e)),dt[e]},gt=L,yt=re,mt=Pe,_t=Ue,bt=function(e,t){var n,r;if("string"===t&&Be(n=e.toString)&&!Ve(r=Fe(n,e)))return r;if(Be(n=e.valueOf)&&!Ve(r=Fe(n,e)))return r;if("string"!==t&&Be(n=e.toString)&&!Ve(r=Fe(n,e)))return r;throw He("Can't convert object to primitive value")},wt=TypeError,kt=vt("toPrimitive"),Et=function(e,t){if(!yt(e)||mt(e))return e;var n,r=_t(e,kt);if(r){if(void 0===t&&(t="default"),n=gt(r,e,t),!yt(n)||mt(n))return n;throw wt("Can't convert object to primitive value")}return void 0===t&&(t="number"),bt(e,t)},Tt=Pe,St=function(e){var t=Et(e,"string");return Tt(t)?t:t+""},Ct=re,xt=s.document,Pt=Ct(xt)&&Ct(xt.createElement),At=function(e){return Pt?xt.createElement(e):{}},It=At,Rt=!O&&!l((function(){return 7!=Object.defineProperty(It("div"),"a",{get:function(){return 7}}).a})),Ot=O,Dt=L,Mt=N,Lt=j,Nt=ee,Ut=St,Ft=tt,Bt=Rt,Vt=Object.getOwnPropertyDescriptor;R.f=Ot?Vt:function(e,t){if(e=Nt(e),t=Ut(t),Bt)try{return Vt(e,t)}catch(n){}if(Ft(e,t))return Lt(!Dt(Mt.f,e,t),e[t])};var Ht=l,jt=I,zt=/#|\.prototype\./,Kt=function(e,t){var n=Gt[Wt(e)];return n==Yt||n!=qt&&(jt(t)?Ht(t):!!t)},Wt=Kt.normalize=function(e){return String(e).replace(zt,".").toLowerCase()},Gt=Kt.data={},qt=Kt.NATIVE="N",Yt=Kt.POLYFILL="P",Xt=Kt,Jt=Me,Qt=u,$t=C(C.bind),Zt=function(e,t){return Jt(e),void 0===t?e:Qt?$t(e,t):function(){return e.apply(t,arguments)}},en={},tn=O&&l((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),nn=re,rn=String,on=TypeError,an=function(e){if(nn(e))return e;throw on(rn(e)+" is not an object")},sn=O,ln=Rt,un=tn,cn=an,hn=St,fn=TypeError,dn=Object.defineProperty,pn=Object.getOwnPropertyDescriptor,vn="enumerable",gn="configurable",yn="writable";en.f=sn?un?function(e,t,n){if(cn(e),t=hn(t),cn(n),"function"==typeof e&&"prototype"===t&&"value"in n&&yn in n&&!n[yn]){var r=pn(e,t);r&&r[yn]&&(e[t]=n.value,n={configurable:gn in n?n[gn]:r[gn],enumerable:vn in n?n[vn]:r[vn],writable:!1})}return dn(e,t,n)}:dn:function(e,t,n){if(cn(e),t=hn(t),cn(n),ln)try{return dn(e,t,n)}catch(r){}if("get"in n||"set"in n)throw fn("Accessors not supported");return"value"in n&&(e[t]=n.value),e};var mn=en,_n=j,bn=O?function(e,t,n){return mn.f(e,t,_n(1,n))}:function(e,t,n){return e[t]=n,e},wn=s,kn=p,En=C,Tn=I,Sn=R.f,Cn=Xt,xn=ie,Pn=Zt,An=bn,In=tt,Rn=function(e){var t=function(n,r,i){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(n);case 2:return new e(n,r)}return new e(n,r,i)}return kn(e,this,arguments)};return t.prototype=e.prototype,t},On=function(e,t){var n,r,i,o,a,s,l,u,c,h=e.target,f=e.global,d=e.stat,p=e.proto,v=f?wn:d?wn[h]:(wn[h]||{}).prototype,g=f?xn:xn[h]||An(xn,h,{})[h],y=g.prototype;for(o in t)r=!(n=Cn(f?o:h+(d?".":"#")+o,e.forced))&&v&&In(v,o),s=g[o],r&&(l=e.dontCallGetSet?(c=Sn(v,o))&&c.value:v[o]),a=r&&l?l:t[o],r&&typeof s==typeof a||(u=e.bind&&r?Pn(a,wn):e.wrap&&r?Rn(a):p&&Tn(a)?En(a):a,(e.sham||a&&a.sham||s&&s.sham)&&An(u,"sham",!0),An(g,o,u),p&&(In(xn,i=h+"Prototype")||An(xn,i,{}),An(xn[i],o,a),e.real&&y&&(n||!y[o])&&An(y,o,a)))},Dn=E,Mn=Array.isArray||function(e){return"Array"==Dn(e)},Ln=Math.ceil,Nn=Math.floor,Un=Math.trunc||function(e){var t=+e;return(t>0?Nn:Ln)(t)},Fn=function(e){var t=+e;return t!=t||0===t?0:Un(t)},Bn=Fn,Vn=Math.min,Hn=function(e){return e>0?Vn(Bn(e),9007199254740991):0},jn=Hn,zn=function(e){return jn(e.length)},Kn=TypeError,Wn=function(e){if(e>9007199254740991)throw Kn("Maximum allowed index exceeded");return e},Gn=St,qn=en,Yn=j,Xn=function(e,t,n){var r=Gn(t);r in e?qn.f(e,r,Yn(0,n)):e[r]=n},Jn={};Jn[vt("toStringTag")]="z";var Qn="[object z]"===String(Jn),$n=Qn,Zn=I,er=E,tr=vt("toStringTag"),nr=Object,rr="Arguments"==er(function(){return arguments}()),ir=$n?er:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=nr(e),tr))?n:rr?er(t):"Object"==(r=er(t))&&Zn(t.callee)?"Arguments":r},or=I,ar=qe,sr=_(Function.toString);or(ar.inspectSource)||(ar.inspectSource=function(e){return sr(e)});var lr=ar.inspectSource,ur=_,cr=l,hr=I,fr=ir,dr=lr,pr=function(){},vr=[],gr=ue("Reflect","construct"),yr=/^\s*(?:class|function)\b/,mr=ur(yr.exec),_r=!yr.exec(pr),br=function(e){if(!hr(e))return!1;try{return gr(pr,vr,e),!0}catch(t){return!1}},wr=function(e){if(!hr(e))return!1;switch(fr(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return _r||!!mr(yr,dr(e))}catch(t){return!0}};wr.sham=!0;var kr=!gr||cr((function(){var e;return br(br.call)||!br(Object)||!br((function(){e=!0}))||e}))?wr:br,Er=Mn,Tr=kr,Sr=re,Cr=vt("species"),xr=Array,Pr=function(e){var t;return Er(e)&&(t=e.constructor,(Tr(t)&&(t===xr||Er(t.prototype))||Sr(t)&&null===(t=t[Cr]))&&(t=void 0)),void 0===t?xr:t},Ar=function(e,t){return new(Pr(e))(0===t?0:t)},Ir=l,Rr=me,Or=vt("species"),Dr=function(e){return Rr>=51||!Ir((function(){var t=[];return(t.constructor={})[Or]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Mr=On,Lr=l,Nr=Mn,Ur=re,Fr=$e,Br=zn,Vr=Wn,Hr=Xn,jr=Ar,zr=Dr,Kr=me,Wr=vt("isConcatSpreadable"),Gr=Kr>=51||!Lr((function(){var e=[];return e[Wr]=!1,e.concat()[0]!==e})),qr=function(e){if(!Ur(e))return!1;var t=e[Wr];return void 0!==t?!!t:Nr(e)};Mr({target:"Array",proto:!0,arity:1,forced:!Gr||!zr("concat")},{concat:function(e){var t,n,r,i,o,a=Fr(this),s=jr(a,0),l=0;for(t=-1,r=arguments.length;t<r;t++)if(qr(o=-1===t?a:arguments[t]))for(i=Br(o),Vr(l+i),n=0;n<i;n++,l++)n in o&&Hr(s,l,o[n]);else Vr(l+1),Hr(s,l++,o);return s.length=l,s}});var Yr=ir,Xr=String,Jr=function(e){if("Symbol"===Yr(e))throw TypeError("Cannot convert a Symbol value to a string");return Xr(e)},Qr={},$r=Fn,Zr=Math.max,ei=Math.min,ti=function(e,t){var n=$r(e);return n<0?Zr(n+t,0):ei(n,t)},ni=ee,ri=ti,ii=zn,oi=function(e){return function(t,n,r){var i,o=ni(t),a=ii(o),s=ri(r,a);if(e&&n!=n){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((e||s in o)&&o[s]===n)return e||s||0;return!e&&-1}},ai={includes:oi(!0),indexOf:oi(!1)},si={},li=tt,ui=ee,ci=ai.indexOf,hi=si,fi=_([].push),di=function(e,t){var n,r=ui(e),i=0,o=[];for(n in r)!li(hi,n)&&li(r,n)&&fi(o,n);for(;t.length>i;)li(r,n=t[i++])&&(~ci(o,n)||fi(o,n));return o},pi=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],vi=di,gi=pi,yi=Object.keys||function(e){return vi(e,gi)},mi=O,_i=tn,bi=en,wi=an,ki=ee,Ei=yi;Qr.f=mi&&!_i?Object.defineProperties:function(e,t){wi(e);for(var n,r=ki(t),i=Ei(t),o=i.length,a=0;o>a;)bi.f(e,n=i[a++],r[n]);return e};var Ti,Si=ue("document","documentElement"),Ci=at,xi=Xe("keys"),Pi=function(e){return xi[e]||(xi[e]=Ci(e))},Ai=an,Ii=Qr,Ri=pi,Oi=si,Di=Si,Mi=At,Li="prototype",Ni="script",Ui=Pi("IE_PROTO"),Fi=function(){},Bi=function(e){return"<"+Ni+">"+e+"</"+Ni+">"},Vi=function(e){e.write(Bi("")),e.close();var t=e.parentWindow.Object;return e=null,t},Hi=function(){try{Ti=new ActiveXObject("htmlfile")}catch(i){}var e,t,n;Hi="undefined"!=typeof document?document.domain&&Ti?Vi(Ti):(t=Mi("iframe"),n="java"+Ni+":",t.style.display="none",Di.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(Bi("document.F=Object")),e.close(),e.F):Vi(Ti);for(var r=Ri.length;r--;)delete Hi[Li][Ri[r]];return Hi()};Oi[Ui]=!0;var ji=Object.create||function(e,t){var n;return null!==e?(Fi[Li]=Ai(e),n=new Fi,Fi[Li]=null,n[Ui]=e):n=Hi(),void 0===t?n:Ii.f(n,t)},zi={},Ki=di,Wi=pi.concat("length","prototype");zi.f=Object.getOwnPropertyNames||function(e){return Ki(e,Wi)};var Gi={},qi=ti,Yi=zn,Xi=Xn,Ji=Array,Qi=Math.max,$i=function(e,t,n){for(var r=Yi(e),i=qi(t,r),o=qi(void 0===n?r:n,r),a=Ji(Qi(o-i,0)),s=0;i<o;i++,s++)Xi(a,s,e[i]);return a.length=s,a},Zi=E,eo=ee,to=zi.f,no=$i,ro="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Gi.f=function(e){return ro&&"Window"==Zi(e)?function(e){try{return to(e)}catch(t){return no(ro)}}(e):to(eo(e))};var io={};io.f=Object.getOwnPropertySymbols;var oo=bn,ao=function(e,t,n,r){return r&&r.enumerable?e[t]=n:oo(e,t,n),e},so=en,lo=function(e,t,n){return so.f(e,t,n)},uo={},co=vt;uo.f=co;var ho,fo,po,vo=ie,go=tt,yo=uo,mo=en.f,_o=function(e){var t=vo.Symbol||(vo.Symbol={});go(t,e)||mo(t,e,{value:yo.f(e)})},bo=L,wo=ue,ko=vt,Eo=ao,To=function(){var e=wo("Symbol"),t=e&&e.prototype,n=t&&t.valueOf,r=ko("toPrimitive");t&&!t[r]&&Eo(t,r,(function(e){return bo(n,this)}),{arity:1})},So=ir,Co=Qn?{}.toString:function(){return"[object "+So(this)+"]"},xo=Qn,Po=en.f,Ao=bn,Io=tt,Ro=Co,Oo=vt("toStringTag"),Do=function(e,t,n,r){if(e){var i=n?e:e.prototype;Io(i,Oo)||Po(i,Oo,{configurable:!0,value:t}),r&&!xo&&Ao(i,"toString",Ro)}},Mo=I,Lo=s.WeakMap,No=Mo(Lo)&&/native code/.test(String(Lo)),Uo=s,Fo=re,Bo=bn,Vo=tt,Ho=qe,jo=Pi,zo=si,Ko="Object already initialized",Wo=Uo.TypeError,Go=Uo.WeakMap;if(No||Ho.state){var qo=Ho.state||(Ho.state=new Go);qo.get=qo.get,qo.has=qo.has,qo.set=qo.set,ho=function(e,t){if(qo.has(e))throw Wo(Ko);return t.facade=e,qo.set(e,t),t},fo=function(e){return qo.get(e)||{}},po=function(e){return qo.has(e)}}else{var Yo=jo("state");zo[Yo]=!0,ho=function(e,t){if(Vo(e,Yo))throw Wo(Ko);return t.facade=e,Bo(e,Yo,t),t},fo=function(e){return Vo(e,Yo)?e[Yo]:{}},po=function(e){return Vo(e,Yo)}}var Xo={set:ho,get:fo,has:po,enforce:function(e){return po(e)?fo(e):ho(e,{})},getterFor:function(e){return function(t){var n;if(!Fo(t)||(n=fo(t)).type!==e)throw Wo("Incompatible receiver, "+e+" required");return n}}},Jo=Zt,Qo=q,$o=$e,Zo=zn,ea=Ar,ta=_([].push),na=function(e){var t=1==e,n=2==e,r=3==e,i=4==e,o=6==e,a=7==e,s=5==e||o;return function(l,u,c,h){for(var f,d,p=$o(l),v=Qo(p),g=Jo(u,c),y=Zo(v),m=0,_=h||ea,b=t?_(l,y):n||a?_(l,0):void 0;y>m;m++)if((s||m in v)&&(d=g(f=v[m],m,p),e))if(t)b[m]=d;else if(d)switch(e){case 3:return!0;case 5:return f;case 6:return m;case 2:ta(b,f)}else switch(e){case 4:return!1;case 7:ta(b,f)}return o?-1:r||i?i:b}},ra={forEach:na(0),map:na(1),filter:na(2),some:na(3),every:na(4),find:na(5),findIndex:na(6),filterReject:na(7)},ia=On,oa=s,aa=L,sa=_,la=O,ua=ke,ca=l,ha=tt,fa=ce,da=an,pa=ee,va=St,ga=Jr,ya=j,ma=ji,_a=yi,ba=zi,wa=Gi,ka=io,Ea=R,Ta=en,Sa=Qr,Ca=N,xa=ao,Pa=lo,Aa=Xe,Ia=si,Ra=at,Oa=vt,Da=uo,Ma=_o,La=To,Na=Do,Ua=Xo,Fa=ra.forEach,Ba=Pi("hidden"),Va="Symbol",Ha="prototype",ja=Ua.set,za=Ua.getterFor(Va),Ka=Object[Ha],Wa=oa.Symbol,Ga=Wa&&Wa[Ha],qa=oa.TypeError,Ya=oa.QObject,Xa=Ea.f,Ja=Ta.f,Qa=wa.f,$a=Ca.f,Za=sa([].push),es=Aa("symbols"),ts=Aa("op-symbols"),ns=Aa("wks"),rs=!Ya||!Ya[Ha]||!Ya[Ha].findChild,is=la&&ca((function(){return 7!=ma(Ja({},"a",{get:function(){return Ja(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=Xa(Ka,t);r&&delete Ka[t],Ja(e,t,n),r&&e!==Ka&&Ja(Ka,t,r)}:Ja,os=function(e,t){var n=es[e]=ma(Ga);return ja(n,{type:Va,tag:e,description:t}),la||(n.description=t),n},as=function(e,t,n){e===Ka&&as(ts,t,n),da(e);var r=va(t);return da(n),ha(es,r)?(n.enumerable?(ha(e,Ba)&&e[Ba][r]&&(e[Ba][r]=!1),n=ma(n,{enumerable:ya(0,!1)})):(ha(e,Ba)||Ja(e,Ba,ya(1,{})),e[Ba][r]=!0),is(e,r,n)):Ja(e,r,n)},ss=function(e,t){da(e);var n=pa(t),r=_a(n).concat(hs(n));return Fa(r,(function(t){la&&!aa(ls,n,t)||as(e,t,n[t])})),e},ls=function(e){var t=va(e),n=aa($a,this,t);return!(this===Ka&&ha(es,t)&&!ha(ts,t))&&(!(n||!ha(this,t)||!ha(es,t)||ha(this,Ba)&&this[Ba][t])||n)},us=function(e,t){var n=pa(e),r=va(t);if(n!==Ka||!ha(es,r)||ha(ts,r)){var i=Xa(n,r);return!i||!ha(es,r)||ha(n,Ba)&&n[Ba][r]||(i.enumerable=!0),i}},cs=function(e){var t=Qa(pa(e)),n=[];return Fa(t,(function(e){ha(es,e)||ha(Ia,e)||Za(n,e)})),n},hs=function(e){var t=e===Ka,n=Qa(t?ts:pa(e)),r=[];return Fa(n,(function(e){!ha(es,e)||t&&!ha(Ka,e)||Za(r,es[e])})),r};ua||(Wa=function(){if(fa(Ga,this))throw qa("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?ga(arguments[0]):void 0,t=Ra(e),n=function(e){this===Ka&&aa(n,ts,e),ha(this,Ba)&&ha(this[Ba],t)&&(this[Ba][t]=!1),is(this,t,ya(1,e))};return la&&rs&&is(Ka,t,{configurable:!0,set:n}),os(t,e)},xa(Ga=Wa[Ha],"toString",(function(){return za(this).tag})),xa(Wa,"withoutSetter",(function(e){return os(Ra(e),e)})),Ca.f=ls,Ta.f=as,Sa.f=ss,Ea.f=us,ba.f=wa.f=cs,ka.f=hs,Da.f=function(e){return os(Oa(e),e)},la&&Pa(Ga,"description",{configurable:!0,get:function(){return za(this).description}})),ia({global:!0,constructor:!0,wrap:!0,forced:!ua,sham:!ua},{Symbol:Wa}),Fa(_a(ns),(function(e){Ma(e)})),ia({target:Va,stat:!0,forced:!ua},{useSetter:function(){rs=!0},useSimple:function(){rs=!1}}),ia({target:"Object",stat:!0,forced:!ua,sham:!la},{create:function(e,t){return void 0===t?ma(e):ss(ma(e),t)},defineProperty:as,defineProperties:ss,getOwnPropertyDescriptor:us}),ia({target:"Object",stat:!0,forced:!ua},{getOwnPropertyNames:cs}),La(),Na(Wa,Va),Ia[Ba]=!0;var fs=ke&&!!Symbol.for&&!!Symbol.keyFor,ds=On,ps=ue,vs=tt,gs=Jr,ys=Xe,ms=fs,_s=ys("string-to-symbol-registry"),bs=ys("symbol-to-string-registry");ds({target:"Symbol",stat:!0,forced:!ms},{for:function(e){var t=gs(e);if(vs(_s,t))return _s[t];var n=ps("Symbol")(t);return _s[t]=n,bs[n]=t,n}});var ws=On,ks=tt,Es=Pe,Ts=Ie,Ss=fs,Cs=Xe("symbol-to-string-registry");ws({target:"Symbol",stat:!0,forced:!Ss},{keyFor:function(e){if(!Es(e))throw TypeError(Ts(e)+" is not a symbol");if(ks(Cs,e))return Cs[e]}});var xs=_([].slice),Ps=Mn,As=I,Is=E,Rs=Jr,Os=_([].push),Ds=On,Ms=ue,Ls=p,Ns=L,Us=_,Fs=l,Bs=I,Vs=Pe,Hs=xs,js=function(e){if(As(e))return e;if(Ps(e)){for(var t=e.length,n=[],r=0;r<t;r++){var i=e[r];"string"==typeof i?Os(n,i):"number"!=typeof i&&"Number"!=Is(i)&&"String"!=Is(i)||Os(n,Rs(i))}var o=n.length,a=!0;return function(e,t){if(a)return a=!1,t;if(Ps(this))return t;for(var r=0;r<o;r++)if(n[r]===e)return t}}},zs=ke,Ks=String,Ws=Ms("JSON","stringify"),Gs=Us(/./.exec),qs=Us("".charAt),Ys=Us("".charCodeAt),Xs=Us("".replace),Js=Us(1..toString),Qs=/[\uD800-\uDFFF]/g,$s=/^[\uD800-\uDBFF]$/,Zs=/^[\uDC00-\uDFFF]$/,el=!zs||Fs((function(){var e=Ms("Symbol")();return"[null]"!=Ws([e])||"{}"!=Ws({a:e})||"{}"!=Ws(Object(e))})),tl=Fs((function(){return'"\\udf06\\ud834"'!==Ws("\udf06\ud834")||'"\\udead"'!==Ws("\udead")})),nl=function(e,t){var n=Hs(arguments),r=js(t);if(Bs(r)||void 0!==e&&!Vs(e))return n[1]=function(e,t){if(Bs(r)&&(t=Ns(r,this,Ks(e),t)),!Vs(t))return t},Ls(Ws,null,n)},rl=function(e,t,n){var r=qs(n,t-1),i=qs(n,t+1);return Gs($s,e)&&!Gs(Zs,i)||Gs(Zs,e)&&!Gs($s,r)?"\\u"+Js(Ys(e,0),16):e};Ws&&Ds({target:"JSON",stat:!0,arity:3,forced:el||tl},{stringify:function(e,t,n){var r=Hs(arguments),i=Ls(el?nl:Ws,null,r);return tl&&"string"==typeof i?Xs(i,Qs,rl):i}});var il=io,ol=$e;On({target:"Object",stat:!0,forced:!ke||l((function(){il.f(1)}))},{getOwnPropertySymbols:function(e){var t=il.f;return t?t(ol(e)):[]}}),_o("asyncIterator"),_o("hasInstance"),_o("isConcatSpreadable"),_o("iterator"),_o("match"),_o("matchAll"),_o("replace"),_o("search"),_o("species"),_o("split");var al=To;_o("toPrimitive"),al();var sl=ue,ll=Do;_o("toStringTag"),ll(sl("Symbol"),"Symbol"),_o("unscopables"),Do(s.JSON,"JSON",!0);var ul,cl,hl,fl=ie.Symbol,dl={},pl=O,vl=tt,gl=Function.prototype,yl=pl&&Object.getOwnPropertyDescriptor,ml=vl(gl,"name"),_l={EXISTS:ml,PROPER:ml&&"something"===function(){}.name,CONFIGURABLE:ml&&(!pl||pl&&yl(gl,"name").configurable)},bl=!l((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),wl=tt,kl=I,El=$e,Tl=bl,Sl=Pi("IE_PROTO"),Cl=Object,xl=Cl.prototype,Pl=Tl?Cl.getPrototypeOf:function(e){var t=El(e);if(wl(t,Sl))return t[Sl];var n=t.constructor;return kl(n)&&t instanceof n?n.prototype:t instanceof Cl?xl:null},Al=l,Il=I,Rl=re,Ol=ji,Dl=Pl,Ml=ao,Ll=vt("iterator"),Nl=!1;[].keys&&("next"in(hl=[].keys())?(cl=Dl(Dl(hl)))!==Object.prototype&&(ul=cl):Nl=!0);var Ul=!Rl(ul)||Al((function(){var e={};return ul[Ll].call(e)!==e}));Il((ul=Ul?{}:Ol(ul))[Ll])||Ml(ul,Ll,(function(){return this}));var Fl={IteratorPrototype:ul,BUGGY_SAFARI_ITERATORS:Nl},Bl=Fl.IteratorPrototype,Vl=ji,Hl=j,jl=Do,zl=dl,Kl=function(){return this},Wl=function(e,t,n,r){var i=t+" Iterator";return e.prototype=Vl(Bl,{next:Hl(+!r,n)}),jl(e,i,!1,!0),zl[i]=Kl,e},Gl=_,ql=Me,Yl=I,Xl=String,Jl=TypeError,Ql=function(e,t,n){try{return Gl(ql(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(r){}},$l=an,Zl=function(e){if("object"==typeof e||Yl(e))return e;throw Jl("Can't set "+Xl(e)+" as a prototype")},eu=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Ql(Object.prototype,"__proto__","set"))(n,[]),t=n instanceof Array}catch(r){}return function(n,r){return $l(n),Zl(r),t?e(n,r):n.__proto__=r,n}}():void 0),tu=On,nu=L,ru=_l,iu=Wl,ou=Pl,au=Do,su=ao,lu=dl,uu=Fl,cu=ru.PROPER,hu=uu.BUGGY_SAFARI_ITERATORS,fu=vt("iterator"),du="keys",pu="values",vu="entries",gu=function(){return this},yu=function(e,t,n,r,i,o,a){iu(n,t,r);var s,l,u,c=function(e){if(e===i&&v)return v;if(!hu&&e in d)return d[e];switch(e){case du:case pu:case vu:return function(){return new n(this,e)}}return function(){return new n(this)}},h=t+" Iterator",f=!1,d=e.prototype,p=d[fu]||d["@@iterator"]||i&&d[i],v=!hu&&p||c(i),g="Array"==t&&d.entries||p;if(g&&(s=ou(g.call(new e)))!==Object.prototype&&s.next&&(au(s,h,!0,!0),lu[h]=gu),cu&&i==pu&&p&&p.name!==pu&&(f=!0,v=function(){return nu(p,this)}),i)if(l={values:c(pu),keys:o?v:c(du),entries:c(vu)},a)for(u in l)(hu||f||!(u in d))&&su(d,u,l[u]);else tu({target:t,proto:!0,forced:hu||f},l);return a&&d[fu]!==v&&su(d,fu,v,{name:i}),lu[t]=v,l},mu=function(e,t){return{value:e,done:t}},_u=ee,bu=dl,wu=Xo;en.f;var ku=yu,Eu=mu,Tu="Array Iterator",Su=wu.set,Cu=wu.getterFor(Tu);ku(Array,"Array",(function(e,t){Su(this,{type:Tu,target:_u(e),index:0,kind:t})}),(function(){var e=Cu(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,Eu(void 0,!0)):Eu("keys"==n?r:"values"==n?t[r]:[r,t[r]],!1)}),"values"),bu.Arguments=bu.Array;var xu={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Pu=s,Au=ir,Iu=bn,Ru=dl,Ou=vt("toStringTag");for(var Du in xu){var Mu=Pu[Du],Lu=Mu&&Mu.prototype;Lu&&Au(Lu)!==Ou&&Iu(Lu,Ou,Du),Ru[Du]=Ru.Array}var Nu=fl;_o("dispose");var Uu=Nu;_o("asyncDispose");var Fu=On,Bu=_,Vu=ue("Symbol"),Hu=Vu.keyFor,ju=Bu(Vu.prototype.valueOf);Fu({target:"Symbol",stat:!0},{isRegistered:function(e){try{return void 0!==Hu(ju(e))}catch(t){return!1}}});for(var zu=On,Ku=Xe,Wu=ue,Gu=_,qu=Pe,Yu=vt,Xu=Wu("Symbol"),Ju=Xu.isWellKnown,Qu=Wu("Object","getOwnPropertyNames"),$u=Gu(Xu.prototype.valueOf),Zu=Ku("wks"),ec=0,tc=Qu(Xu),nc=tc.length;ec<nc;ec++)try{var rc=tc[ec];qu(Xu[rc])&&Yu(rc)}catch(PH){}zu({target:"Symbol",stat:!0,forced:!0},{isWellKnown:function(e){if(Ju&&Ju(e))return!0;try{for(var t=$u(e),n=0,r=Qu(Zu),i=r.length;n<i;n++)if(Zu[r[n]]==t)return!0}catch(PH){}return!1}}),_o("matcher"),_o("metadataKey"),_o("observable"),_o("metadata"),_o("patternMatch"),_o("replaceAll");var ic=Uu,oc=o(ic),ac=_,sc=Fn,lc=Jr,uc=Q,cc=ac("".charAt),hc=ac("".charCodeAt),fc=ac("".slice),dc=function(e){return function(t,n){var r,i,o=lc(uc(t)),a=sc(n),s=o.length;return a<0||a>=s?e?"":void 0:(r=hc(o,a))<55296||r>56319||a+1===s||(i=hc(o,a+1))<56320||i>57343?e?cc(o,a):r:e?fc(o,a,a+2):i-56320+(r-55296<<10)+65536}},pc={codeAt:dc(!1),charAt:dc(!0)},vc=pc.charAt,gc=Jr,yc=Xo,mc=yu,_c=mu,bc="String Iterator",wc=yc.set,kc=yc.getterFor(bc);mc(String,"String",(function(e){wc(this,{type:bc,string:gc(e),index:0})}),(function(){var e,t=kc(this),n=t.string,r=t.index;return r>=n.length?_c(void 0,!0):(e=vc(n,r),t.index+=e.length,_c(e,!1))}));var Ec=uo.f("iterator"),Tc=o(Ec);function Sc(e){return(Sc="function"==typeof oc&&"symbol"==typeof Tc?function(e){return typeof e}:function(e){return e&&"function"==typeof oc&&e.constructor===oc&&e!==oc.prototype?"symbol":typeof e})(e)}var Cc={exports:{}},xc=On,Pc=O,Ac=en.f;xc({target:"Object",stat:!0,forced:Object.defineProperty!==Ac,sham:!Pc},{defineProperty:Ac});var Ic=ie.Object,Rc=Cc.exports=function(e,t,n){return Ic.defineProperty(e,t,n)};Ic.defineProperty.sham&&(Rc.sham=!0);var Oc=Cc.exports,Dc=o(Oc);On({target:"Object",stat:!0,sham:!O},{create:ji});var Mc=ie.Object,Lc=o((function(e,t){return Mc.create(e,t)})),Nc=$e,Uc=Pl,Fc=bl;On({target:"Object",stat:!0,forced:l((function(){Uc(1)})),sham:!Fc},{getPrototypeOf:function(e){return Uc(Nc(e))}});var Bc=ie.Object.getPrototypeOf,Vc=o(Bc),Hc=l,jc=function(e,t){var n=[][e];return!!n&&Hc((function(){n.call(null,t||function(){return 1},1)}))},zc=ra.forEach,Kc=jc("forEach")?[].forEach:function(e){return zc(this,e,arguments.length>1?arguments[1]:void 0)};On({target:"Array",proto:!0,forced:[].forEach!=Kc},{forEach:Kc});var Wc=ie,Gc=function(e){return Wc[e+"Prototype"]},qc=Gc("Array").forEach,Yc=ir,Xc=tt,Jc=ce,Qc=qc,$c=Array.prototype,Zc={DOMTokenList:!0,NodeList:!0},eh=o((function(e){var t=e.forEach;return e===$c||Jc($c,e)&&t===$c.forEach||Xc(Zc,Yc(e))?Qc:t})),th=O,nh=Mn,rh=TypeError,ih=Object.getOwnPropertyDescriptor,oh=th&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(PH){return PH instanceof TypeError}}()?function(e,t){if(nh(e)&&!ih(e,"length").writable)throw rh("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t},ah=$e,sh=zn,lh=oh,uh=Wn;On({target:"Array",proto:!0,arity:1,forced:l((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(PH){return PH instanceof TypeError}}()},{push:function(e){var t=ah(this),n=sh(t),r=arguments.length;uh(n+r);for(var i=0;i<r;i++)t[n]=arguments[i],n++;return lh(t,n),n}});var ch=Gc("Array").push,hh=ce,fh=ch,dh=Array.prototype,ph=o((function(e){var t=e.push;return e===dh||hh(dh,e)&&t===dh.push?fh:t}));On({target:"Object",stat:!0},{setPrototypeOf:eu});var vh=ie.Object.setPrototypeOf,gh=o(vh),yh=ue,mh=zi,_h=io,bh=an,wh=_([].concat),kh=yh("Reflect","ownKeys")||function(e){var t=mh.f(bh(e)),n=_h.f;return n?wh(t,n(e)):t},Eh=tt,Th=kh,Sh=R,Ch=en,xh=re,Ph=bn,Ah=Error,Ih=_("".replace),Rh=String(Ah("zxcasd").stack),Oh=/\n\s*at [^:]*:[^\n]*/,Dh=Oh.test(Rh),Mh=j,Lh=!l((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",Mh(1,7)),7!==e.stack)})),Nh=bn,Uh=function(e,t){if(Dh&&"string"==typeof e&&!Ah.prepareStackTrace)for(;t--;)e=Ih(e,Oh,"");return e},Fh=Lh,Bh=Error.captureStackTrace,Vh=dl,Hh=vt("iterator"),jh=Array.prototype,zh=function(e){return void 0!==e&&(Vh.Array===e||jh[Hh]===e)},Kh=ir,Wh=Ue,Gh=Y,qh=dl,Yh=vt("iterator"),Xh=function(e){if(!Gh(e))return Wh(e,Yh)||Wh(e,"@@iterator")||qh[Kh(e)]},Jh=L,Qh=Me,$h=an,Zh=Ie,ef=Xh,tf=TypeError,nf=function(e,t){var n=arguments.length<2?ef(e):t;if(Qh(n))return $h(Jh(n,e));throw tf(Zh(e)+" is not iterable")},rf=L,of=an,af=Ue,sf=function(e,t,n){var r,i;of(e);try{if(!(r=af(e,"return"))){if("throw"===t)throw n;return n}r=rf(r,e)}catch(PH){i=!0,r=PH}if("throw"===t)throw n;if(i)throw r;return of(r),n},lf=Zt,uf=L,cf=an,hf=Ie,ff=zh,df=zn,pf=ce,vf=nf,gf=Xh,yf=sf,mf=TypeError,_f=function(e,t){this.stopped=e,this.result=t},bf=_f.prototype,wf=function(e,t,n){var r,i,o,a,s,l,u,c=n&&n.that,h=!(!n||!n.AS_ENTRIES),f=!(!n||!n.IS_RECORD),d=!(!n||!n.IS_ITERATOR),p=!(!n||!n.INTERRUPTED),v=lf(t,c),g=function(e){return r&&yf(r,"normal",e),new _f(!0,e)},y=function(e){return h?(cf(e),p?v(e[0],e[1],g):v(e[0],e[1])):p?v(e,g):v(e)};if(f)r=e.iterator;else if(d)r=e;else{if(!(i=gf(e)))throw mf(hf(e)+" is not iterable");if(ff(i)){for(o=0,a=df(e);a>o;o++)if((s=y(e[o]))&&pf(bf,s))return s;return new _f(!1)}r=vf(e,i)}for(l=f?e.next:r.next;!(u=uf(l,r)).done;){try{s=y(u.value)}catch(PH){yf(r,"throw",PH)}if("object"==typeof s&&s&&pf(bf,s))return s}return new _f(!1)},kf=Jr,Ef=On,Tf=ce,Sf=Pl,Cf=eu,xf=function(e,t,n){for(var r=Th(t),i=Ch.f,o=Sh.f,a=0;a<r.length;a++){var s=r[a];Eh(e,s)||n&&Eh(n,s)||i(e,s,o(t,s))}},Pf=ji,Af=bn,If=j,Rf=function(e,t){xh(t)&&"cause"in t&&Ph(e,"cause",t.cause)},Of=function(e,t,n,r){Fh&&(Bh?Bh(e,t):Nh(e,"stack",Uh(n,r)))},Df=wf,Mf=function(e,t){return void 0===e?arguments.length<2?"":t:kf(e)},Lf=vt("toStringTag"),Nf=Error,Uf=[].push,Ff=function(e,t){var n,r=Tf(Bf,this);Cf?n=Cf(Nf(),r?Sf(this):Bf):(n=r?this:Pf(Bf),Af(n,Lf,"Error")),void 0!==t&&Af(n,"message",Mf(t)),Of(n,Ff,n.stack,1),arguments.length>2&&Rf(n,arguments[2]);var i=[];return Df(e,Uf,{that:i}),Af(n,"errors",i),n};Cf?Cf(Ff,Nf):xf(Ff,Nf,{name:!0});var Bf=Ff.prototype=Pf(Nf.prototype,{constructor:If(1,Ff),message:If(1,""),name:If(1,"AggregateError")});Ef({global:!0,constructor:!0,arity:2},{AggregateError:Ff});var Vf,Hf,jf,zf,Kf="undefined"!=typeof process&&"process"==E(process),Wf=ue,Gf=lo,qf=O,Yf=vt("species"),Xf=function(e){var t=Wf(e);qf&&t&&!t[Yf]&&Gf(t,Yf,{configurable:!0,get:function(){return this}})},Jf=ce,Qf=TypeError,$f=function(e,t){if(Jf(t,e))return e;throw Qf("Incorrect invocation")},Zf=kr,ed=Ie,td=TypeError,nd=function(e){if(Zf(e))return e;throw td(ed(e)+" is not a constructor")},rd=an,id=nd,od=Y,ad=vt("species"),sd=function(e,t){var n,r=rd(e).constructor;return void 0===r||od(n=rd(r)[ad])?t:id(n)},ld=TypeError,ud=function(e,t){if(e<t)throw ld("Not enough arguments");return e},cd=/(?:ipad|iphone|ipod).*applewebkit/i.test(he),hd=s,fd=p,dd=Zt,pd=I,vd=tt,gd=l,yd=Si,md=xs,_d=At,bd=ud,wd=cd,kd=Kf,Ed=hd.setImmediate,Td=hd.clearImmediate,Sd=hd.process,Cd=hd.Dispatch,xd=hd.Function,Pd=hd.MessageChannel,Ad=hd.String,Id=0,Rd={},Od="onreadystatechange";gd((function(){Vf=hd.location}));var Dd=function(e){if(vd(Rd,e)){var t=Rd[e];delete Rd[e],t()}},Md=function(e){return function(){Dd(e)}},Ld=function(e){Dd(e.data)},Nd=function(e){hd.postMessage(Ad(e),Vf.protocol+"//"+Vf.host)};Ed&&Td||(Ed=function(e){bd(arguments.length,1);var t=pd(e)?e:xd(e),n=md(arguments,1);return Rd[++Id]=function(){fd(t,void 0,n)},Hf(Id),Id},Td=function(e){delete Rd[e]},kd?Hf=function(e){Sd.nextTick(Md(e))}:Cd&&Cd.now?Hf=function(e){Cd.now(Md(e))}:Pd&&!wd?(zf=(jf=new Pd).port2,jf.port1.onmessage=Ld,Hf=dd(zf.postMessage,zf)):hd.addEventListener&&pd(hd.postMessage)&&!hd.importScripts&&Vf&&"file:"!==Vf.protocol&&!gd(Nd)?(Hf=Nd,hd.addEventListener("message",Ld,!1)):Hf=Od in _d("script")?function(e){yd.appendChild(_d("script"))[Od]=function(){yd.removeChild(this),Dd(e)}}:function(e){setTimeout(Md(e),0)});var Ud={set:Ed,clear:Td},Fd=function(){this.head=null,this.tail=null};Fd.prototype={add:function(e){var t={item:e,next:null},n=this.tail;n?n.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}};var Bd,Vd,Hd,jd,zd,Kd=Fd,Wd=/ipad|iphone|ipod/i.test(he)&&"undefined"!=typeof Pebble,Gd=/web0s(?!.*chrome)/i.test(he),qd=s,Yd=Zt,Xd=R.f,Jd=Ud.set,Qd=Kd,$d=cd,Zd=Wd,ep=Gd,tp=Kf,np=qd.MutationObserver||qd.WebKitMutationObserver,rp=qd.document,ip=qd.process,op=qd.Promise,ap=Xd(qd,"queueMicrotask"),sp=ap&&ap.value;if(!sp){var lp=new Qd,up=function(){var e,t;for(tp&&(e=ip.domain)&&e.exit();t=lp.get();)try{t()}catch(PH){throw lp.head&&Bd(),PH}e&&e.enter()};$d||tp||ep||!np||!rp?!Zd&&op&&op.resolve?((jd=op.resolve(void 0)).constructor=op,zd=Yd(jd.then,jd),Bd=function(){zd(up)}):tp?Bd=function(){ip.nextTick(up)}:(Jd=Yd(Jd,qd),Bd=function(){Jd(up)}):(Vd=!0,Hd=rp.createTextNode(""),new np(up).observe(Hd,{characterData:!0}),Bd=function(){Hd.data=Vd=!Vd}),sp=function(e){lp.head||Bd(),lp.add(e)}}var cp=sp,hp=function(e){try{return{error:!1,value:e()}}catch(PH){return{error:!0,value:PH}}},fp=s.Promise,dp="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,pp=!dp&&!Kf&&"object"==typeof window&&"object"==typeof document,vp=s,gp=fp,yp=I,mp=Xt,_p=lr,bp=vt,wp=pp,kp=dp,Ep=me,Tp=gp&&gp.prototype,Sp=bp("species"),Cp=!1,xp=yp(vp.PromiseRejectionEvent),Pp={CONSTRUCTOR:mp("Promise",(function(){var e=_p(gp),t=e!==String(gp);if(!t&&66===Ep)return!0;if(!Tp.catch||!Tp.finally)return!0;if(!Ep||Ep<51||!/native code/.test(e)){var n=new gp((function(e){e(1)})),r=function(e){e((function(){}),(function(){}))};if((n.constructor={})[Sp]=r,!(Cp=n.then((function(){}))instanceof r))return!0}return!t&&(wp||kp)&&!xp})),REJECTION_EVENT:xp,SUBCLASSING:Cp},Ap={},Ip=Me,Rp=TypeError,Op=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw Rp("Bad Promise constructor");t=e,n=r})),this.resolve=Ip(t),this.reject=Ip(n)};Ap.f=function(e){return new Op(e)};var Dp,Mp,Lp=On,Np=Kf,Up=s,Fp=L,Bp=ao,Vp=Do,Hp=Xf,jp=Me,zp=I,Kp=re,Wp=$f,Gp=sd,qp=Ud.set,Yp=cp,Xp=function(e,t){try{1==arguments.length?console.error(e):console.error(e,t)}catch(PH){}},Jp=hp,Qp=Kd,$p=Xo,Zp=fp,ev=Pp,tv=Ap,nv="Promise",rv=ev.CONSTRUCTOR,iv=ev.REJECTION_EVENT,ov=$p.getterFor(nv),av=$p.set,sv=Zp&&Zp.prototype,lv=Zp,uv=sv,cv=Up.TypeError,hv=Up.document,fv=Up.process,dv=tv.f,pv=dv,vv=!!(hv&&hv.createEvent&&Up.dispatchEvent),gv="unhandledrejection",yv=function(e){var t;return!(!Kp(e)||!zp(t=e.then))&&t},mv=function(e,t){var n,r,i,o=t.value,a=1==t.state,s=a?e.ok:e.fail,l=e.resolve,u=e.reject,c=e.domain;try{s?(a||(2===t.rejection&&Ev(t),t.rejection=1),!0===s?n=o:(c&&c.enter(),n=s(o),c&&(c.exit(),i=!0)),n===e.promise?u(cv("Promise-chain cycle")):(r=yv(n))?Fp(r,n,l,u):l(n)):u(o)}catch(PH){c&&!i&&c.exit(),u(PH)}},_v=function(e,t){e.notified||(e.notified=!0,Yp((function(){for(var n,r=e.reactions;n=r.get();)mv(n,e);e.notified=!1,t&&!e.rejection&&wv(e)})))},bv=function(e,t,n){var r,i;vv?((r=hv.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),Up.dispatchEvent(r)):r={promise:t,reason:n},!iv&&(i=Up["on"+e])?i(r):e===gv&&Xp("Unhandled promise rejection",n)},wv=function(e){Fp(qp,Up,(function(){var t,n=e.facade,r=e.value;if(kv(e)&&(t=Jp((function(){Np?fv.emit("unhandledRejection",r,n):bv(gv,n,r)})),e.rejection=Np||kv(e)?2:1,t.error))throw t.value}))},kv=function(e){return 1!==e.rejection&&!e.parent},Ev=function(e){Fp(qp,Up,(function(){var t=e.facade;Np?fv.emit("rejectionHandled",t):bv("rejectionhandled",t,e.value)}))},Tv=function(e,t,n){return function(r){e(t,r,n)}},Sv=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,_v(e,!0))},Cv=function(e,t,n){if(!e.done){e.done=!0,n&&(e=n);try{if(e.facade===t)throw cv("Promise can't be resolved itself");var r=yv(t);r?Yp((function(){var n={done:!1};try{Fp(r,t,Tv(Cv,n,e),Tv(Sv,n,e))}catch(PH){Sv(n,PH,e)}})):(e.value=t,e.state=1,_v(e,!1))}catch(PH){Sv({done:!1},PH,e)}}};rv&&(uv=(lv=function(e){Wp(this,uv),jp(e),Fp(Dp,this);var t=ov(this);try{e(Tv(Cv,t),Tv(Sv,t))}catch(PH){Sv(t,PH)}}).prototype,(Dp=function(e){av(this,{type:nv,done:!1,notified:!1,parent:!1,reactions:new Qp,rejection:!1,state:0,value:void 0})}).prototype=Bp(uv,"then",(function(e,t){var n=ov(this),r=dv(Gp(this,lv));return n.parent=!0,r.ok=!zp(e)||e,r.fail=zp(t)&&t,r.domain=Np?fv.domain:void 0,0==n.state?n.reactions.add(r):Yp((function(){mv(r,n)})),r.promise})),Mp=function(){var e=new Dp,t=ov(e);this.promise=e,this.resolve=Tv(Cv,t),this.reject=Tv(Sv,t)},tv.f=dv=function(e){return e===lv||undefined===e?new Mp(e):pv(e)}),Lp({global:!0,constructor:!0,wrap:!0,forced:rv},{Promise:lv}),Vp(lv,nv,!1,!0),Hp(nv);var xv=vt("iterator"),Pv=!1;try{var Av=0,Iv={next:function(){return{done:!!Av++}},return:function(){Pv=!0}};Iv[xv]=function(){return this},Array.from(Iv,(function(){throw 2}))}catch(PH){}var Rv=function(e,t){if(!t&&!Pv)return!1;var n=!1;try{var r={};r[xv]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(PH){}return n},Ov=fp,Dv=Pp.CONSTRUCTOR||!Rv((function(e){Ov.all(e).then(void 0,(function(){}))})),Mv=L,Lv=Me,Nv=Ap,Uv=hp,Fv=wf;On({target:"Promise",stat:!0,forced:Dv},{all:function(e){var t=this,n=Nv.f(t),r=n.resolve,i=n.reject,o=Uv((function(){var n=Lv(t.resolve),o=[],a=0,s=1;Fv(e,(function(e){var l=a++,u=!1;s++,Mv(n,t,e).then((function(e){u||(u=!0,o[l]=e,--s||r(o))}),i)})),--s||r(o)}));return o.error&&i(o.value),n.promise}});var Bv=On,Vv=Pp.CONSTRUCTOR;fp&&fp.prototype,Bv({target:"Promise",proto:!0,forced:Vv,real:!0},{catch:function(e){return this.then(void 0,e)}});var Hv=L,jv=Me,zv=Ap,Kv=hp,Wv=wf;On({target:"Promise",stat:!0,forced:Dv},{race:function(e){var t=this,n=zv.f(t),r=n.reject,i=Kv((function(){var i=jv(t.resolve);Wv(e,(function(e){Hv(i,t,e).then(n.resolve,r)}))}));return i.error&&r(i.value),n.promise}});var Gv=L,qv=Ap;On({target:"Promise",stat:!0,forced:Pp.CONSTRUCTOR},{reject:function(e){var t=qv.f(this);return Gv(t.reject,void 0,e),t.promise}});var Yv=an,Xv=re,Jv=Ap,Qv=function(e,t){if(Yv(e),Xv(t)&&t.constructor===e)return t;var n=Jv.f(e);return(0,n.resolve)(t),n.promise},$v=On,Zv=fp,eg=Pp.CONSTRUCTOR,tg=Qv,ng=ue("Promise"),rg=!eg;$v({target:"Promise",stat:!0,forced:true},{resolve:function(e){return tg(rg&&this===ng?Zv:this,e)}});var ig=L,og=Me,ag=Ap,sg=hp,lg=wf;On({target:"Promise",stat:!0,forced:Dv},{allSettled:function(e){var t=this,n=ag.f(t),r=n.resolve,i=n.reject,o=sg((function(){var n=og(t.resolve),i=[],o=0,a=1;lg(e,(function(e){var s=o++,l=!1;a++,ig(n,t,e).then((function(e){l||(l=!0,i[s]={status:"fulfilled",value:e},--a||r(i))}),(function(e){l||(l=!0,i[s]={status:"rejected",reason:e},--a||r(i))}))})),--a||r(i)}));return o.error&&i(o.value),n.promise}});var ug=L,cg=Me,hg=ue,fg=Ap,dg=hp,pg=wf,vg="No one promise resolved";On({target:"Promise",stat:!0,forced:Dv},{any:function(e){var t=this,n=hg("AggregateError"),r=fg.f(t),i=r.resolve,o=r.reject,a=dg((function(){var r=cg(t.resolve),a=[],s=0,l=1,u=!1;pg(e,(function(e){var c=s++,h=!1;l++,ug(r,t,e).then((function(e){h||u||(u=!0,i(e))}),(function(e){h||u||(h=!0,a[c]=e,--l||o(new n(a,vg)))}))})),--l||o(new n(a,vg))}));return a.error&&o(a.value),r.promise}});var gg=On,yg=fp,mg=l,_g=ue,bg=I,wg=sd,kg=Qv,Eg=yg&&yg.prototype;gg({target:"Promise",proto:!0,real:!0,forced:!!yg&&mg((function(){Eg.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=wg(this,_g("Promise")),n=bg(e);return this.then(n?function(n){return kg(t,e()).then((function(){return n}))}:e,n?function(n){return kg(t,e()).then((function(){throw n}))}:e)}});var Tg=ie.Promise,Sg=Ap,Cg=hp;On({target:"Promise",stat:!0,forced:!0},{try:function(e){var t=Sg.f(this),n=Cg(e);return(n.error?t.reject:t.resolve)(n.value),t.promise}});var xg=Tg,Pg=o(xg),Ag=On,Ig=Mn,Rg=_([].reverse),Og=[1,2];Ag({target:"Array",proto:!0,forced:String(Og)===String(Og.reverse())},{reverse:function(){return Ig(this)&&(this.length=this.length),Rg(this)}});var Dg=Gc("Array").reverse,Mg=ce,Lg=Dg,Ng=Array.prototype,Ug=o((function(e){var t=e.reverse;return e===Ng||Mg(Ng,e)&&t===Ng.reverse?Lg:t})),Fg=On,Bg=Mn,Vg=kr,Hg=re,jg=ti,zg=zn,Kg=ee,Wg=Xn,Gg=vt,qg=xs,Yg=Dr("slice"),Xg=Gg("species"),Jg=Array,Qg=Math.max;Fg({target:"Array",proto:!0,forced:!Yg},{slice:function(e,t){var n,r,i,o=Kg(this),a=zg(o),s=jg(e,a),l=jg(void 0===t?a:t,a);if(Bg(o)&&(n=o.constructor,(Vg(n)&&(n===Jg||Bg(n.prototype))||Hg(n)&&null===(n=n[Xg]))&&(n=void 0),n===Jg||void 0===n))return qg(o,s,l);for(r=new(void 0===n?Jg:n)(Qg(l-s,0)),i=0;s<l;s++,i++)s in o&&Wg(r,i,o[s]);return r.length=i,r}});var $g=Gc("Array").slice,Zg=ce,ey=$g,ty=Array.prototype,ny=function(e){var t=e.slice;return e===ty||Zg(ty,e)&&t===ty.slice?ey:t},ry=o(ny);function iy(){iy=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Dc||function(e,t,n){e[t]=n.value},i="function"==typeof oc?oc:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(e,t,n){return Dc(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(x){l=function(e,t,n){return e[t]=n}}function u(e,t,n,i){var o=t&&t.prototype instanceof f?t:f,a=Lc(o.prototype),s=new T(i||[]);return r(a,"_invoke",{value:b(e,n,s)}),a}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(x){return{type:"throw",arg:x}}}e.wrap=u;var h={};function f(){}function d(){}function p(){}var v={};l(v,o,(function(){return this}));var g=Vc&&Vc(Vc(S([])));g&&g!==t&&n.call(g,o)&&(v=g);var y=p.prototype=f.prototype=Lc(v);function m(e){var t;eh(t=["next","throw","return"]).call(t,(function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function i(r,o,a,s){var l=c(e[r],e,o);if("throw"!==l.type){var u=l.arg,h=u.value;return h&&"object"==Sc(h)&&n.call(h,"__await")?t.resolve(h.__await).then((function(e){i("next",e,a,s)}),(function(e){i("throw",e,a,s)})):t.resolve(h).then((function(e){u.value=e,a(u)}),(function(e){return i("throw",e,a,s)}))}s(l.arg)}var o;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return o=o?o.then(r,r):r()}})}function b(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return{value:void 0,done:!0}}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=w(a,n);if(s){if(s===h)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=c(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===h)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}function w(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var i=c(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,h;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function k(e){var t,n={tryLoc:e[0]};1 in e&&(n.catchLoc=e[1]),2 in e&&(n.finallyLoc=e[2],n.afterLoc=e[3]),ph(t=this.tryEntries).call(t,n)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],eh(e).call(e,k,this),this.reset(!0)}function S(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:C}}function C(){return{value:void 0,done:!0}}return d.prototype=p,r(y,"constructor",{value:p,configurable:!0}),r(p,"constructor",{value:d,configurable:!0}),d.displayName=l(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return gh?gh(e,p):(e.__proto__=p,l(e,s,"GeneratorFunction")),e.prototype=Lc(y),e},e.awrap=function(e){return{__await:e}},m(_.prototype),l(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,i,o){void 0===o&&(o=Pg);var a=new _(u(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(y),l(y,s,"Generator"),l(y,o,(function(){return this})),l(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)ph(n).call(n,r);return Ug(n).call(n),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=S,T.prototype={constructor:T,reset:function(e){var t;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,eh(t=this.tryEntries).call(t,E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+ry(r).call(r,1))&&(this[r]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;E(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:S(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},e}function oy(e,t,n,r,i,o,a){try{var s=e[o](a),l=s.value}catch(PH){return void n(PH)}s.done?t(l):Pg.resolve(l).then(r,i)}function ay(e){return function(){var t=this,n=arguments;return new Pg((function(r,i){var o=e.apply(t,n);function a(e){oy(o,r,i,a,s,"next",e)}function s(e){oy(o,r,i,a,s,"throw",e)}a(void 0)}))}}On({target:"Array",stat:!0},{isArray:Mn});var sy=o(ie.Array.isArray);function ly(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var uy=Xh,cy=o(uy),hy=an,fy=sf,dy=Zt,py=L,vy=$e,gy=function(e,t,n,r){try{return r?t(hy(n)[0],n[1]):t(n)}catch(PH){fy(e,"throw",PH)}},yy=zh,my=kr,_y=zn,by=Xn,wy=nf,ky=Xh,Ey=Array,Ty=function(e){var t=vy(e),n=my(this),r=arguments.length,i=r>1?arguments[1]:void 0,o=void 0!==i;o&&(i=dy(i,r>2?arguments[2]:void 0));var a,s,l,u,c,h,f=ky(t),d=0;if(!f||this===Ey&&yy(f))for(a=_y(t),s=n?new this(a):Ey(a);a>d;d++)h=o?i(t[d],d):t[d],by(s,d,h);else for(c=(u=wy(t,f)).next,s=n?new this:[];!(l=py(c,u)).done;d++)h=o?gy(u,i,[l.value,d],!0):l.value,by(s,d,h);return s.length=d,s},Sy=Ty;On({target:"Array",stat:!0,forced:!Rv((function(e){Array.from(e)}))},{from:Sy});var Cy=ie.Array.from,xy=o(Cy);function Py(e,t){var n;if(e){if("string"==typeof e)return ly(e,t);var r=ry(n=Object.prototype.toString.call(e)).call(n,8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?xy(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ly(e,t):void 0}}function Ay(e){return function(e){if(sy(e))return ly(e)}(e)||function(e){if(void 0!==oc&&null!=cy(e)||null!=e["@@iterator"])return xy(e)}(e)||Py(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Iy(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Ry=uo.f("toPrimitive"),Oy=o(Ry);function Dy(e){var t=function(e,t){if("object"!==Sc(e)||null===e)return e;var n=e[Oy];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Sc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Sc(t)?t:String(t)}function My(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Dc(e,Dy(r.key),r)}}function Ly(e,t,n){return t&&My(e.prototype,t),n&&My(e,n),Dc(e,"prototype",{writable:!1}),e}function Ny(e,t,n){return(t=Dy(t))in e?Dc(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Uy=ie.Object.getOwnPropertySymbols,Fy=o(Uy),By=On,Vy=ai.indexOf,Hy=jc,jy=C([].indexOf),zy=!!jy&&1/jy([1],1,-0)<0;By({target:"Array",proto:!0,forced:zy||!Hy("indexOf")},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return zy?jy(this,e,t)||0:Vy(this,e,t)}});var Ky=Gc("Array").indexOf,Wy=ce,Gy=Ky,qy=Array.prototype,Yy=function(e){var t=e.indexOf;return e===qy||Wy(qy,e)&&t===qy.indexOf?Gy:t},Xy=o(Yy),Jy=$e,Qy=yi;On({target:"Object",stat:!0,forced:l((function(){Qy(1)}))},{keys:function(e){return Qy(Jy(e))}});var $y=ie.Object.keys,Zy=o($y);function em(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Zy(e);for(r=0;r<o.length;r++)n=o[r],Xy(t).call(t,n)>=0||(i[n]=e[n]);return i}(e,t);if(Fy){var o=Fy(e);for(r=0;r<o.length;r++)n=o[r],Xy(t).call(t,n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var tm=ra.filter;On({target:"Array",proto:!0,forced:!Dr("filter")},{filter:function(e){return tm(this,e,arguments.length>1?arguments[1]:void 0)}});var nm=Gc("Array").filter,rm=ce,im=nm,om=Array.prototype,am=function(e){var t=e.filter;return e===om||rm(om,e)&&t===om.filter?im:t},sm=o(am),lm={exports:{}},um=On,cm=l,hm=ee,fm=R.f,dm=O;um({target:"Object",stat:!0,forced:!dm||cm((function(){fm(1)})),sham:!dm},{getOwnPropertyDescriptor:function(e,t){return fm(hm(e),t)}});var pm=ie.Object,vm=lm.exports=function(e,t){return pm.getOwnPropertyDescriptor(e,t)};pm.getOwnPropertyDescriptor.sham&&(vm.sham=!0);var gm=lm.exports,ym=o(gm),mm=kh,_m=ee,bm=R,wm=Xn;On({target:"Object",stat:!0,sham:!O},{getOwnPropertyDescriptors:function(e){for(var t,n,r=_m(e),i=bm.f,o=mm(r),a={},s=0;o.length>s;)void 0!==(n=i(r,t=o[s++]))&&wm(a,t,n);return a}});var km=ie.Object.getOwnPropertyDescriptors,Em=o(km),Tm={exports:{}},Sm=On,Cm=O,xm=Qr.f;Sm({target:"Object",stat:!0,forced:Object.defineProperties!==xm,sham:!Cm},{defineProperties:xm});var Pm=ie.Object,Am=Tm.exports=function(e,t){return Pm.defineProperties(e,t)};Pm.defineProperties.sham&&(Am.sham=!0);var Im=Tm.exports,Rm=o(Im);function Om(e,t){var n=Zy(e);if(Fy){var r=Fy(e);t&&(r=sm(r).call(r,(function(t){return ym(e,t).enumerable}))),ph(n).apply(n,r)}return n}function Dm(e){for(var t=1;t<arguments.length;t++){var n,r,i=null!=arguments[t]?arguments[t]:{};t%2?eh(n=Om(Object(i),!0)).call(n,(function(t){Ny(e,t,i[t])})):Em?Rm(e,Em(i)):eh(r=Om(Object(i))).call(r,(function(t){Dc(e,t,ym(i,t))}))}return e}var Mm=Gc("Array").concat,Lm=ce,Nm=Mm,Um=Array.prototype,Fm=o((function(e){var t=e.concat;return e===Um||Lm(Um,e)&&t===Um.concat?Nm:t})),Bm=On,Vm=ra.find,Hm="find",jm=!0;Hm in[]&&Array(1)[Hm]((function(){jm=!1})),Bm({target:"Array",proto:!0,forced:jm},{find:function(e){return Vm(this,e,arguments.length>1?arguments[1]:void 0)}});var zm=Gc("Array").find,Km=ce,Wm=zm,Gm=Array.prototype,qm=o((function(e){var t=e.find;return e===Gm||Km(Gm,e)&&t===Gm.find?Wm:t})),Ym={exports:{}},Xm=l((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),Jm=l,Qm=re,$m=E,Zm=Xm,e_=Object.isExtensible,t_=Jm((function(){e_(1)}))||Zm?function(e){return!!Qm(e)&&((!Zm||"ArrayBuffer"!=$m(e))&&(!e_||e_(e)))}:e_,n_=!l((function(){return Object.isExtensible(Object.preventExtensions({}))})),r_=On,i_=_,o_=si,a_=re,s_=tt,l_=en.f,u_=zi,c_=Gi,h_=t_,f_=n_,d_=!1,p_=at("meta"),v_=0,g_=function(e){l_(e,p_,{value:{objectID:"O"+v_++,weakData:{}}})},y_=Ym.exports={enable:function(){y_.enable=function(){},d_=!0;var e=u_.f,t=i_([].splice),n={};n[p_]=1,e(n).length&&(u_.f=function(n){for(var r=e(n),i=0,o=r.length;i<o;i++)if(r[i]===p_){t(r,i,1);break}return r},r_({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:c_.f}))},fastKey:function(e,t){if(!a_(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!s_(e,p_)){if(!h_(e))return"F";if(!t)return"E";g_(e)}return e[p_].objectID},getWeakData:function(e,t){if(!s_(e,p_)){if(!h_(e))return!0;if(!t)return!1;g_(e)}return e[p_].weakData},onFreeze:function(e){return f_&&d_&&h_(e)&&!s_(e,p_)&&g_(e),e}};o_[p_]=!0;var m_=Ym.exports,__=On,b_=s,w_=m_,k_=l,E_=bn,T_=wf,S_=$f,C_=I,x_=re,P_=Do,A_=en.f,I_=ra.forEach,R_=O,O_=Xo.set,D_=Xo.getterFor,M_=ao,L_=function(e,t,n){for(var r in t)n&&n.unsafe&&e[r]?e[r]=t[r]:M_(e,r,t[r],n);return e},N_=ji,U_=lo,F_=L_,B_=Zt,V_=$f,H_=Y,j_=wf,z_=yu,K_=mu,W_=Xf,G_=O,q_=m_.fastKey,Y_=Xo.set,X_=Xo.getterFor,J_={getConstructor:function(e,t,n,r){var i=e((function(e,i){V_(e,o),Y_(e,{type:t,index:N_(null),first:void 0,last:void 0,size:0}),G_||(e.size=0),H_(i)||j_(i,e[r],{that:e,AS_ENTRIES:n})})),o=i.prototype,a=X_(t),s=function(e,t,n){var r,i,o=a(e),s=l(e,t);return s?s.value=n:(o.last=s={index:i=q_(t,!0),key:t,value:n,previous:r=o.last,next:void 0,removed:!1},o.first||(o.first=s),r&&(r.next=s),G_?o.size++:e.size++,"F"!==i&&(o.index[i]=s)),e},l=function(e,t){var n,r=a(e),i=q_(t);if("F"!==i)return r.index[i];for(n=r.first;n;n=n.next)if(n.key==t)return n};return F_(o,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,G_?e.size=0:this.size=0},delete:function(e){var t=this,n=a(t),r=l(t,e);if(r){var i=r.next,o=r.previous;delete n.index[r.index],r.removed=!0,o&&(o.next=i),i&&(i.previous=o),n.first==r&&(n.first=i),n.last==r&&(n.last=o),G_?n.size--:t.size--}return!!r},forEach:function(e){for(var t,n=a(this),r=B_(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!l(this,e)}}),F_(o,n?{get:function(e){var t=l(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),G_&&U_(o,"size",{configurable:!0,get:function(){return a(this).size}}),i},setStrong:function(e,t,n){var r=t+" Iterator",i=X_(t),o=X_(r);z_(e,t,(function(e,t){Y_(this,{type:r,target:e,state:i(e),kind:t,last:void 0})}),(function(){for(var e=o(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?K_("keys"==t?n.key:"values"==t?n.value:[n.key,n.value],!1):(e.target=void 0,K_(void 0,!0))}),n?"entries":"values",!n,!0),W_(t)}};(function(e,t,n){var r,i=-1!==e.indexOf("Map"),o=-1!==e.indexOf("Weak"),a=i?"set":"add",s=b_[e],l=s&&s.prototype,u={};if(R_&&C_(s)&&(o||l.forEach&&!k_((function(){(new s).entries().next()})))){var c=(r=t((function(t,n){O_(S_(t,c),{type:e,collection:new s}),null!=n&&T_(n,t[a],{that:t,AS_ENTRIES:i})}))).prototype,h=D_(e);I_(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"==e||"set"==e;!(e in l)||o&&"clear"==e||E_(c,e,(function(n,r){var i=h(this).collection;if(!t&&o&&!x_(n))return"get"==e&&void 0;var a=i[e](0===n?0:n,r);return t?this:a}))})),o||A_(c,"size",{configurable:!0,get:function(){return h(this).collection.size}})}else r=n.getConstructor(t,e,i,a),w_.enable();P_(r,e,!1,!0),u[e]=r,__({global:!0,forced:!0},u),o||n.setStrong(r,e,i)})("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),J_);var Q_=ie.Map,$_=Zt,Z_=L,eb=Me,tb=nd,nb=Y,rb=wf,ib=[].push,ob=function(e){var t,n,r,i,o=arguments.length,a=o>1?arguments[1]:void 0;return tb(this),(t=void 0!==a)&&eb(a),nb(e)?new this:(n=[],t?(r=0,i=$_(a,o>2?arguments[2]:void 0),rb(e,(function(e){Z_(ib,n,i(e,r++))}))):rb(e,ib,{that:n}),new this(n))};On({target:"Map",stat:!0,forced:!0},{from:ob});var ab=xs,sb=function(){return new this(ab(arguments))};On({target:"Map",stat:!0,forced:!0},{of:sb});var lb=Ie,ub=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"get"in e&&"set"in e&&"delete"in e&&"entries"in e)return e;throw TypeError(lb(e)+" is not a map")},cb=function(e,t){return 1==t?function(t,n){return t[e](n)}:function(t,n,r){return t[e](n,r)}},hb=ue("Map"),fb={Map:hb,set:cb("set",2),get:cb("get",1),has:cb("has",1),remove:cb("delete",1),proto:hb.prototype},db=ub,pb=fb.remove;On({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=db(this),n=!0,r=0,i=arguments.length;r<i;r++)e=pb(t,arguments[r]),n=n&&e;return!!n}});var vb=ub,gb=fb.get,yb=fb.has,mb=fb.set;On({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var n,r,i=vb(this);return yb(i,e)?(n=gb(i,e),"update"in t&&(n=t.update(n,e,i),mb(i,e,n)),n):(r=t.insert(e,i),mb(i,e,r),r)}});var _b=L,bb=function(e,t,n){for(var r,i,o=n||e.next;!(r=_b(o,e)).done;)if(void 0!==(i=t(r.value)))return i},wb=function(e,t,n){return n?bb(e.entries(),(function(e){return t(e[1],e[0])})):e.forEach(t)},kb=Zt,Eb=ub,Tb=wb;On({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=Eb(this),n=kb(e,arguments.length>1?arguments[1]:void 0);return!1!==Tb(t,(function(e,r){if(!n(e,r,t))return!1}),!0)}});var Sb=Zt,Cb=ub,xb=wb,Pb=fb.Map,Ab=fb.set;On({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=Cb(this),n=Sb(e,arguments.length>1?arguments[1]:void 0),r=new Pb;return xb(t,(function(e,i){n(e,i,t)&&Ab(r,i,e)})),r}});var Ib=Zt,Rb=ub,Ob=wb;On({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=Rb(this),n=Ib(e,arguments.length>1?arguments[1]:void 0),r=Ob(t,(function(e,r){if(n(e,r,t))return{value:e}}),!0);return r&&r.value}});var Db=Zt,Mb=ub,Lb=wb;On({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=Mb(this),n=Db(e,arguments.length>1?arguments[1]:void 0),r=Lb(t,(function(e,r){if(n(e,r,t))return{key:r}}),!0);return r&&r.key}});var Nb=On,Ub=L,Fb=I,Bb=Me,Vb=wf,Hb=fb.Map,jb=_([].push);Nb({target:"Map",stat:!0,forced:!0},{groupBy:function(e,t){var n=new(Fb(this)?this:Hb);Bb(t);var r=Bb(n.has),i=Bb(n.get),o=Bb(n.set);return Vb(e,(function(e){var a=t(e);Ub(r,n,a)?jb(Ub(i,n,a),e):Ub(o,n,a,[e])})),n}});var zb=function(e,t){return e===t||e!=e&&t!=t},Kb=ub,Wb=wb;On({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return!0===Wb(Kb(this),(function(t){if(zb(t,e))return!0}),!0)}});var Gb=L,qb=wf,Yb=I,Xb=Me,Jb=fb.Map;On({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var n=new(Yb(this)?this:Jb);Xb(t);var r=Xb(n.set);return qb(e,(function(e){Gb(r,n,t(e),e)})),n}});var Qb=ub,$b=wb;On({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){var t=$b(Qb(this),(function(t,n){if(t===e)return{key:n}}),!0);return t&&t.key}});var Zb=Zt,ew=ub,tw=wb,nw=fb.Map,rw=fb.set;On({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=ew(this),n=Zb(e,arguments.length>1?arguments[1]:void 0),r=new nw;return tw(t,(function(e,i){rw(r,n(e,i,t),e)})),r}});var iw=Zt,ow=ub,aw=wb,sw=fb.Map,lw=fb.set;On({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=ow(this),n=iw(e,arguments.length>1?arguments[1]:void 0),r=new sw;return aw(t,(function(e,i){lw(r,i,n(e,i,t))})),r}});var uw=ub,cw=wf,hw=fb.set;On({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=uw(this),n=arguments.length,r=0;r<n;)cw(arguments[r++],(function(e,n){hw(t,e,n)}),{AS_ENTRIES:!0});return t}});var fw=Me,dw=ub,pw=wb,vw=TypeError;On({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=dw(this),n=arguments.length<2,r=n?void 0:arguments[1];if(fw(e),pw(t,(function(i,o){n?(n=!1,r=i):r=e(r,i,o,t)})),n)throw vw("Reduce of empty map with no initial value");return r}});var gw=Zt,yw=ub,mw=wb;On({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=yw(this),n=gw(e,arguments.length>1?arguments[1]:void 0);return!0===mw(t,(function(e,r){if(n(e,r,t))return!0}),!0)}});var _w=Me,bw=ub,ww=TypeError,kw=fb.get,Ew=fb.has,Tw=fb.set;On({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var n=bw(this),r=arguments.length;_w(t);var i=Ew(n,e);if(!i&&r<3)throw ww("Updating absent value");var o=i?kw(n,e):_w(r>2?arguments[2]:void 0)(e,n);return Tw(n,e,t(o,e,n)),n}});var Sw=L,Cw=Me,xw=I,Pw=an,Aw=TypeError,Iw=function(e,t){var n,r=Pw(this),i=Cw(r.get),o=Cw(r.has),a=Cw(r.set),s=arguments.length>2?arguments[2]:void 0;if(!xw(t)&&!xw(s))throw Aw("At least one callback required");return Sw(o,r,e)?(n=Sw(i,r,e),xw(t)&&(n=t(n),Sw(a,r,e,n))):xw(s)&&(n=s(),Sw(a,r,e,n)),n};On({target:"Map",proto:!0,real:!0,forced:!0},{upsert:Iw}),On({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:Iw});var Rw=Q_,Ow=o(Rw),Dw=o(Rw),Mw=ra.map;On({target:"Array",proto:!0,forced:!Dr("map")},{map:function(e){return Mw(this,e,arguments.length>1?arguments[1]:void 0)}});var Lw=Gc("Array").map,Nw=ce,Uw=Lw,Fw=Array.prototype,Bw=o((function(e){var t=e.map;return e===Fw||Nw(Fw,e)&&t===Fw.map?Uw:t})),Vw=o(xg),Hw=o(am),jw=ai.includes;On({target:"Array",proto:!0,forced:l((function(){return!Array(1).includes()}))},{includes:function(e){return jw(this,e,arguments.length>1?arguments[1]:void 0)}});var zw=Gc("Array").includes,Kw=re,Ww=E,Gw=vt("match"),qw=function(e){var t;return Kw(e)&&(void 0!==(t=e[Gw])?!!t:"RegExp"==Ww(e))},Yw=TypeError,Xw=function(e){if(qw(e))throw Yw("The method doesn't accept regular expressions");return e},Jw=vt("match"),Qw=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[Jw]=!1,"/./"[e](t)}catch(PH){}}return!1},$w=On,Zw=Xw,ek=Q,tk=Jr,nk=Qw,rk=_("".indexOf);$w({target:"String",proto:!0,forced:!nk("includes")},{includes:function(e){return!!~rk(tk(ek(this)),tk(Zw(e)),arguments.length>1?arguments[1]:void 0)}});var ik=Gc("String").includes,ok=ce,ak=zw,sk=ik,lk=Array.prototype,uk=String.prototype,ck=o((function(e){var t=e.includes;return e===lk||ok(lk,e)&&t===lk.includes?ak:"string"==typeof e||e===uk||ok(uk,e)&&t===uk.includes?sk:t})),hk=o(Yy),fk=o($y),dk=o(ny),pk=Ie,vk=TypeError,gk=function(e,t){if(!delete e[t])throw vk("Cannot delete property "+pk(t)+" of "+pk(e))},yk=On,mk=$e,_k=ti,bk=Fn,wk=zn,kk=oh,Ek=Wn,Tk=Ar,Sk=Xn,Ck=gk,xk=Dr("splice"),Pk=Math.max,Ak=Math.min;yk({target:"Array",proto:!0,forced:!xk},{splice:function(e,t){var n,r,i,o,a,s,l=mk(this),u=wk(l),c=_k(e,u),h=arguments.length;for(0===h?n=r=0:1===h?(n=0,r=u-c):(n=h-2,r=Ak(Pk(bk(t),0),u-c)),Ek(u+n-r),i=Tk(l,r),o=0;o<r;o++)(a=c+o)in l&&Sk(i,o,l[a]);if(i.length=r,n<r){for(o=c;o<u-r;o++)s=o+n,(a=o+r)in l?l[s]=l[a]:Ck(l,s);for(o=u;o>u-r+n;o--)Ck(l,o-1)}else if(n>r)for(o=u-r;o>c;o--)s=o+n-1,(a=o+r-1)in l?l[s]=l[a]:Ck(l,s);for(o=0;o<n;o++)l[o+c]=arguments[o+2];return kk(l,u-r+n),i}});var Ik=Gc("Array").splice,Rk=ce,Ok=Ik,Dk=Array.prototype,Mk=o((function(e){var t=e.splice;return e===Dk||Rk(Dk,e)&&t===Dk.splice?Ok:t}));On({target:"Number",stat:!0},{isNaN:function(e){return e!=e}});var Lk=o(ie.Number.isNaN),Nk="\t\n\v\f\r                　\u2028\u2029\ufeff",Uk=Q,Fk=Jr,Bk=Nk,Vk=_("".replace),Hk=RegExp("^["+Bk+"]+"),jk=RegExp("(^|[^"+Bk+"])["+Bk+"]+$"),zk=function(e){return function(t){var n=Fk(Uk(t));return 1&e&&(n=Vk(n,Hk,"")),2&e&&(n=Vk(n,jk,"$1")),n}},Kk={start:zk(1),end:zk(2),trim:zk(3)},Wk=s,Gk=l,qk=_,Yk=Jr,Xk=Kk.trim,Jk=Nk,Qk=Wk.parseInt,$k=Wk.Symbol,Zk=$k&&$k.iterator,eE=/^[+-]?0x/i,tE=qk(eE.exec),nE=8!==Qk(Jk+"08")||22!==Qk(Jk+"0x16")||Zk&&!Gk((function(){Qk(Object(Zk))}))?function(e,t){var n=Xk(Yk(e));return Qk(n,t>>>0||(tE(eE,n)?16:10))}:Qk;On({global:!0,forced:parseInt!=nE},{parseInt:nE});var rE=o(ie.parseInt),iE=Gc("Array").keys,oE=ir,aE=tt,sE=ce,lE=iE,uE=Array.prototype,cE={DOMTokenList:!0,NodeList:!0},hE=o((function(e){var t=e.keys;return e===uE||sE(uE,e)&&t===uE.keys||aE(cE,oE(e))?lE:t})),fE=o(Uy),dE=o(gm),pE=o(km),vE=o(Im),gE=o(Oc),yE=o(ic),mE=o(Ec),_E=o(vh),bE=o(Bc),wE=_,kE=Me,EE=re,TE=tt,SE=xs,CE=u,xE=Function,PE=wE([].concat),AE=wE([].join),IE={},RE=CE?xE.bind:function(e){var t=kE(this),n=t.prototype,r=SE(arguments,1),i=function(){var n=PE(r,SE(arguments));return this instanceof i?function(e,t,n){if(!TE(IE,t)){for(var r=[],i=0;i<t;i++)r[i]="a["+i+"]";IE[t]=xE("C,a","return new C("+AE(r,",")+")")}return IE[t](e,n)}(t,n.length,n):t.apply(e,n)};return EE(n)&&(i.prototype=n),i},OE=On,DE=p,ME=RE,LE=nd,NE=an,UE=re,FE=ji,BE=l,VE=ue("Reflect","construct"),HE=Object.prototype,jE=[].push,zE=BE((function(){function e(){}return!(VE((function(){}),[],e)instanceof e)})),KE=!BE((function(){VE((function(){}))})),WE=zE||KE;OE({target:"Reflect",stat:!0,forced:WE,sham:WE},{construct:function(e,t){LE(e),NE(t);var n=arguments.length<3?e:LE(arguments[2]);if(KE&&!zE)return VE(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return DE(jE,r,t),new(DE(ME,e,r))}var i=n.prototype,o=FE(UE(i)?i:HE),a=DE(e,o,t);return UE(a)?a:o}});var GE=ie.Reflect.construct,qE=o(GE),YE=o(GE),XE=tt,JE=L,QE=re,$E=an,ZE=function(e){return void 0!==e&&(XE(e,"value")||XE(e,"writable"))},eT=R,tT=Pl;On({target:"Reflect",stat:!0},{get:function e(t,n){var r,i,o=arguments.length<3?t:arguments[2];return $E(t)===o?t[n]:(r=eT.f(t,n))?ZE(r)?r.value:void 0===r.get?void 0:JE(r.get,o):QE(i=tT(t))?e(i,n,o):void 0}});var nT=ie.Reflect.get,rT=o(nT),iT=o(nT),oT=o(uy),aT=o(Cy),sT=o(Ry);function lT(e,t){var n=fk(e);if(fE){var r=fE(e);t&&(r=Hw(r).call(r,(function(t){return dE(e,t).enumerable}))),n.push.apply(n,r)}return n}function uT(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?lT(Object(n),!0).forEach((function(t){pT(e,t,n[t])})):pE?vE(e,pE(n)):lT(Object(n)).forEach((function(t){gE(e,t,dE(n,t))}))}return e}function cT(e){return(cT="function"==typeof yE&&"symbol"==typeof mE?function(e){return typeof e}:function(e){return e&&"function"==typeof yE&&e.constructor===yE&&e!==yE.prototype?"symbol":typeof e})(e)}function hT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fT(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),gE(e,TT(r.key),r)}}function dT(e,t,n){return t&&fT(e.prototype,t),n&&fT(e,n),gE(e,"prototype",{writable:!1}),e}function pT(e,t,n){return(t=TT(t))in e?gE(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function vT(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),gE(e,"prototype",{writable:!1}),t&&yT(e,t)}function gT(e){return(gT=_E?bE.bind():function(e){return e.__proto__||bE(e)})(e)}function yT(e,t){return(yT=_E?_E.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mT(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _T(e,t){if(t&&("object"===Sc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return mT(e)}function bT(e){var t=function(){if("undefined"==typeof Reflect||!YE)return!1;if(YE.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(YE(Boolean,[],(function(){}))),!0}catch(AV){return!1}}();return function(){var n,r=gT(e);if(t){var i=gT(this).constructor;n=YE(r,arguments,i)}else n=r.apply(this,arguments);return _T(this,n)}}function wT(){return wT="undefined"!=typeof Reflect&&iT?iT.bind():function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=gT(e)););return e}(e,t);if(r){var i=dE(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},wT.apply(this,arguments)}function kT(e){return function(e){if(Array.isArray(e))return ET(e)}(e)||function(e){if(void 0!==yE&&null!=oT(e)||null!=e["@@iterator"])return aT(e)}(e)||function(e,t){var n;if(!e)return;if("string"==typeof e)return ET(e,t);var r=dk(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return aT(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ET(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ET(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function TT(e){var t=function(e,t){if("object"!==Sc(e)||null===e)return e;var n=e[sT];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Sc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Sc(t)?t:String(t)}var ST=O,CT=_,xT=L,PT=l,AT=yi,IT=io,RT=N,OT=$e,DT=q,MT=Object.assign,LT=Object.defineProperty,NT=CT([].concat),UT=!MT||PT((function(){if(ST&&1!==MT({b:1},MT(LT({},"a",{enumerable:!0,get:function(){LT(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol(),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach((function(e){t[e]=e})),7!=MT({},e)[n]||AT(MT({},t)).join("")!=r}))?function(e,t){for(var n=OT(e),r=arguments.length,i=1,o=IT.f,a=RT.f;r>i;)for(var s,l=DT(arguments[i++]),u=o?NT(AT(l),o(l)):AT(l),c=u.length,h=0;c>h;)s=u[h++],ST&&!xT(a,l,s)||(n[s]=l[s]);return n}:MT,FT=UT;On({target:"Object",stat:!0,arity:2,forced:Object.assign!==FT},{assign:FT});var BT=o(ie.Object.assign),VT={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function o(e,t,r,o,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,o||e,a),l=n?n+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(n?dk(r).call(r,1):r);return fE?Fm(i).call(i,fE(e)):i},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,o=r.length,a=new Array(o);i<o;i++)a[i]=r[i].fn;return a},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,i,o,a){var s=n?n+e:e;if(!this._events[s])return!1;var l,u,c=this._events[s],h=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),h){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,r),!0;case 4:return c.fn.call(c.context,t,r,i),!0;case 5:return c.fn.call(c.context,t,r,i,o),!0;case 6:return c.fn.call(c.context,t,r,i,o,a),!0}for(u=1,l=new Array(h-1);u<h;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var f,d=c.length;for(u=0;u<d;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),h){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,r);break;case 4:c[u].fn.call(c[u].context,t,r,i);break;default:if(!l)for(f=1,l=new Array(h-1);f<h;f++)l[f-1]=arguments[f];c[u].fn.apply(c[u].context,l)}}return!0},s.prototype.on=function(e,t,n){return o(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return o(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,i){var o=n?n+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==t||i&&!s.once||r&&s.context!==r||a(this,o);else{for(var l=0,u=[],c=s.length;l<c;l++)(s[l].fn!==t||i&&!s[l].once||r&&s[l].context!==r)&&u.push(s[l]);u.length?this._events[o]=1===u.length?u[0]:u:a(this,o)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s}(VT);var HT=o(VT.exports),jT=Fn,zT=Jr,KT=Q,WT=RangeError,GT=function(e){var t=zT(KT(this)),n="",r=jT(e);if(r<0||r==1/0)throw WT("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n},qT=_,YT=Hn,XT=Jr,JT=Q,QT=qT(GT),$T=qT("".slice),ZT=Math.ceil,eS=function(e){return function(t,n,r){var i,o,a=XT(JT(t)),s=YT(n),l=a.length,u=void 0===r?" ":XT(r);return s<=l||""==u?a:((o=QT(u,ZT((i=s-l)/u.length))).length>i&&(o=$T(o,0,i)),e?a+o:o+a)}},tS={start:eS(!1),end:eS(!0)},nS=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(he),rS=tS.start;On({target:"String",proto:!0,forced:nS},{padStart:function(e){return rS(this,e,arguments.length>1?arguments[1]:void 0)}});var iS=Gc("String").padStart,oS=ce,aS=iS,sS=String.prototype,lS=o((function(e){var t=e.padStart;return"string"==typeof e||e===sS||oS(sS,e)&&t===sS.padStart?aS:t})),uS=s,cS=l,hS=Jr,fS=Kk.trim,dS=Nk,pS=_("".charAt),vS=uS.parseFloat,gS=uS.Symbol,yS=gS&&gS.iterator,mS=1/vS(dS+"-0")!=-1/0||yS&&!cS((function(){vS(Object(yS))}))?function(e){var t=fS(hS(e)),n=vS(t);return 0===n&&"-"==pS(t,0)?-0:n}:vS;On({global:!0,forced:parseFloat!=mS},{parseFloat:mS});var _S=o(ie.parseFloat),bS=_l.PROPER,wS=l,kS=Nk,ES=Kk.trim;On({target:"String",proto:!0,forced:function(e){return wS((function(){return!!kS[e]()||"​᠎"!=="​᠎"[e]()||bS&&kS[e].name!==e}))}("trim")},{trim:function(){return ES(this)}});var TS=Gc("String").trim,SS=ce,CS=TS,xS=String.prototype,PS=o((function(e){var t=e.trim;return"string"==typeof e||e===xS||SS(xS,e)&&t===xS.trim?CS:t})),AS=$i,IS=Math.floor,RS=function(e,t){var n=e.length,r=IS(n/2);return n<8?OS(e,t):DS(e,RS(AS(e,0,r),t),RS(AS(e,r),t),t)},OS=function(e,t){for(var n,r,i=e.length,o=1;o<i;){for(r=o,n=e[o];r&&t(e[r-1],n)>0;)e[r]=e[--r];r!==o++&&(e[r]=n)}return e},DS=function(e,t,n,r){for(var i=t.length,o=n.length,a=0,s=0;a<i||s<o;)e[a+s]=a<i&&s<o?r(t[a],n[s])<=0?t[a++]:n[s++]:a<i?t[a++]:n[s++];return e},MS=RS,LS=he.match(/firefox\/(\d+)/i),NS=!!LS&&+LS[1],US=/MSIE|Trident/.test(he),FS=he.match(/AppleWebKit\/(\d+)\./),BS=!!FS&&+FS[1],VS=On,HS=_,jS=Me,zS=$e,KS=zn,WS=gk,GS=Jr,qS=l,YS=MS,XS=jc,JS=NS,QS=US,$S=me,ZS=BS,eC=[],tC=HS(eC.sort),nC=HS(eC.push),rC=qS((function(){eC.sort(void 0)})),iC=qS((function(){eC.sort(null)})),oC=XS("sort"),aC=!qS((function(){if($S)return $S<70;if(!(JS&&JS>3)){if(QS)return!0;if(ZS)return ZS<603;var e,t,n,r,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)eC.push({k:t+r,v:n})}for(eC.sort((function(e,t){return t.v-e.v})),r=0;r<eC.length;r++)t=eC[r].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}}));VS({target:"Array",proto:!0,forced:rC||!iC||!oC||!aC},{sort:function(e){void 0!==e&&jS(e);var t=zS(this);if(aC)return void 0===e?tC(t):tC(t,e);var n,r,i=[],o=KS(t);for(r=0;r<o;r++)r in t&&nC(i,t[r]);for(YS(i,function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:GS(t)>GS(n)?1:-1}}(e)),n=KS(i),r=0;r<n;)t[r]=i[r++];for(;r<o;)WS(t,r++);return t}});var sC,lC=Gc("Array").sort,uC=ce,cC=lC,hC=Array.prototype,fC=o((function(e){var t=e.sort;return e===hC||uC(hC,e)&&t===hC.sort?cC:t})),dC="undefined"!=typeof window&&window.location&&hk(sC=window.location.href).call(sC,"xgplayerdebugger=1")>-1,pC="color: #525252; background-color: #90ee90;",vC="color: #525252; background-color: red;",gC="color: #525252; background-color: yellow; ",yC="%c[xgplayer]",mC={config:{debug:dC?3:0},logInfo:function(e){for(var t,n,r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];this.config.debug>=3&&(n=console).log.apply(n,Fm(t=[yC,pC,e]).call(t,i))},logWarn:function(e){for(var t,n,r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];this.config.debug>=1&&(n=console).warn.apply(n,Fm(t=[yC,gC,e]).call(t,i))},logError:function(e){var t,n;if(!(this.config.debug<1)){for(var r=this.config.debug>=2?"trace":"error",i=arguments.length,o=new Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];(n=console)[r].apply(n,Fm(t=[yC,vC,e]).call(t,o))}}};var _C=function(){function e(t){hT(this,e),this.bufferedList=t}return dT(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}(),bC={};function wC(e){var t=cT(e);return null!==e&&("object"===t||"function"===t)}function kC(e,t,n){var r,i,o,a,s,l,u=0,c=!1,h=!1,f=!0,d=!t&&0!==t&&"function"==typeof window.requestAnimationFrame;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var n=r,o=i;return r=i=void 0,u=t,a=e.apply(o,n)}function v(e,t){return d?(window.cancelAnimationFrame(s),window.requestAnimationFrame(e)):setTimeout(e,t)}function g(e){var n=e-l;return void 0===l||n>=t||n<0||h&&e-u>=o}function y(){var e=Date.now();if(g(e))return m(e);s=v(y,function(e){var n=e-u,r=t-(e-l);return h?Math.min(r,o-n):r}(e))}function m(e){return s=void 0,f&&r?p(e):(r=i=void 0,a)}function _(){for(var e=Date.now(),n=g(e),o=arguments.length,f=new Array(o),d=0;d<o;d++)f[d]=arguments[d];if(r=f,i=this,l=e,n){if(void 0===s)return function(e){return u=e,s=v(y,t),c?p(e):a}(l);if(h)return s=v(y,t),p(l)}return void 0===s&&(s=v(y,t)),a}return t=+t||0,wC(n)&&(c=!!n.leading,o=(h="maxWait"in n)?Math.max(+n.maxWait||0,t):o,f="trailing"in n?!!n.trailing:f),_.cancel=function(){void 0!==s&&function(e){if(d)return window.cancelAnimationFrame(e);clearTimeout(e)}(s),u=0,r=l=i=s=void 0},_.flush=function(){return void 0===s?a:m(Date.now())},_.pending=function(){return void 0!==s},_}function EC(){var e=(document.documentElement.getAttribute("lang")||navigator.language||"zh-cn").toLocaleLowerCase();return"zh-cn"===e&&(e="zh"),e}bC.createDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=document.createElement(e);return i.className=r,i.innerHTML=t,fk(n).forEach((function(t){var r=t,o=n[t];"video"===e||"audio"===e||"live-video"===e?o&&i.setAttribute(r,o):i.setAttribute(r,o)})),i},bC.createDomFromHtml=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{var r=document.createElement("div");r.innerHTML=e;var i=r.children;return r=null,i.length>0?(i=i[0],n&&bC.addClass(i,n),t&&fk(t).forEach((function(e){i.setAttribute(e,t[e])})),i):null}catch(o){return mC.logError("util.createDomFromHtml",o),null}},bC.hasClass=function(e,t){if(!e||!t)return!1;try{return Array.prototype.some.call(e.classList,(function(e){return e===t}))}catch(AV){var n=e.className&&"object"===cT(e.className)?e.getAttribute("class"):e.className;return n&&!!n.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}},bC.addClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)}))}catch(AV){bC.hasClass(e,t)||(e.className&&"object"===cT(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t)}},bC.removeClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.remove(t)}))}catch(AV){bC.hasClass(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===cT(e.className)?e.setAttribute("class",e.getAttribute("class").replace(n," ")):e.className=e.className.replace(n," ")}))}},bC.toggleClass=function(e,t){e&&t.split(/\s+/g).forEach((function(t){bC.hasClass(e,t)?bC.removeClass(e,t):bC.addClass(e,t)}))},bC.classNames=function(){for(var e=arguments,t=[],n=function(n){if("String"===bC.typeOf(e[n]))t.push(e[n]);else if("Object"===bC.typeOf(e[n])){var r;Bw(r=fk(e[n])).call(r,(function(r){e[n][r]&&t.push(r)}))}},r=0;r<arguments.length;r++)n(r);return t.join(" ")},bC.findDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,n=arguments.length>1?arguments[1]:void 0;try{e=t.querySelector(n)}catch(AV){mC.logError("util.findDom",AV),0===hk(n).call(n,"#")&&(e=t.getElementById(dk(n).call(n,1)))}return e},bC.getCss=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,!1)[t]},bC.padStart=function(e,t,n){for(var r=String(n),i=t>>0,o=Math.ceil(i/r.length),a=[],s=String(e);o--;)a.push(r);return a.join("").substring(0,i-s.length)+s},bC.format=function(e){if(window.isNaN(e))return"";e=Math.round(e);var t=lS(bC).call(bC,Math.floor(e/3600),2,0),n=lS(bC).call(bC,Math.floor((e-3600*t)/60),2,0),r=lS(bC).call(bC,Math.floor(e-3600*t-60*n),2,0);return("00"===t?[n,r]:[t,n,r]).join(":")},bC.event=function(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement},bC.typeOf=function(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]},bC.deepCopy=function(e,t){if("Object"===bC.typeOf(t)&&"Object"===bC.typeOf(e))return fk(t).forEach((function(n){if("Object"!==bC.typeOf(t[n])||t[n]instanceof Node)if("Array"===bC.typeOf(t[n])){var r;e[n]="Array"===bC.typeOf(e[n])?Fm(r=e[n]).call(r,t[n]):t[n]}else e[n]=t[n];else void 0===e[n]||void 0===e[n]?e[n]=t[n]:bC.deepCopy(e[n],t[n])})),e},bC.deepMerge=function(e,t){var n;return Bw(n=fk(t)).call(n,(function(n){var r;"Array"===bC.typeOf(t[n])&&"Array"===bC.typeOf(e[n])?"Array"===bC.typeOf(e[n])&&(r=e[n]).push.apply(r,kT(t[n])):bC.typeOf(e[n])!==bC.typeOf(t[n])||null===e[n]||"Object"!==bC.typeOf(e[n])||t[n]instanceof window.Node?null!==t[n]&&(e[n]=t[n]):bC.deepMerge(e[n],t[n])})),e},bC.getBgImage=function(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var n=document.createElement("a");return n.href=t.replace(/url\("|"\)/g,""),n.href},bC.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},bC.setInterval=function(e,t,n,r){e._interval[t]||(e._interval[t]=window.setInterval(n.bind(e),r))},bC.clearInterval=function(e,t){clearInterval(e._interval[t]),e._interval[t]=null},bC.setTimeout=function(e,t,n){e._timers||(e._timers=[]);var r=setTimeout((function(){t(),bC.clearTimeout(e,r)}),n);return e._timers.push(r),r},bC.clearTimeout=function(e,t){var n=e._timers;if("Array"===bC.typeOf(n)){for(var r=0;r<n.length;r++)if(n[r]===t){Mk(n).call(n,r,1),clearTimeout(t);break}}else clearTimeout(t)},bC.clearAllTimers=function(e){var t=e._timers;"Array"===bC.typeOf(t)&&(Bw(t).call(t,(function(e){clearTimeout(e)})),e._timerIds=[])},bC.createImgBtn=function(e,t,n,r){var i,o,a,s,l,u,c,h,f,d,p,v=bC.createDom("xg-".concat(e),"",{},"xgplayer-".concat(e,"-img"));(v.style.backgroundImage='url("'.concat(t,'")'),n&&r)&&(["px","rem","em","pt","dp","vw","vh","vm","%"].every((function(e){var t,i;return!(hk(n).call(n,e)>-1&&hk(r).call(r,e)>-1)||(u=_S(PS(t=dk(n).call(n,0,hk(n).call(n,e))).call(t)),c=_S(PS(i=dk(r).call(r,0,hk(r).call(r,e))).call(i)),h=e,!1)})),v.style.width=Fm(i="".concat(u)).call(i,h),v.style.height=Fm(o="".concat(c)).call(o,h),v.style.backgroundSize=Fm(a=Fm(s=Fm(l="".concat(u)).call(l,h," ")).call(s,c)).call(a,h),v.style.margin="start"===e?Fm(f=Fm(d=Fm(p="-".concat(c/2)).call(p,h," auto auto -")).call(d,u/2)).call(f,h):"auto 5px auto 5px");return v},bC.Hex2RGBA=function(e,t){var n,r=[];if(/^\#[0-9A-F]{3}$/i.test(e)){var i="#";e.replace(/[0-9A-F]/gi,(function(e){i+=e+e})),e=i}return/^#[0-9A-F]{6}$/i.test(e)?(e.replace(/[0-9A-F]{2}/gi,(function(e){r.push(rE(e,16))})),Fm(n="rgba(".concat(r.join(","),", ")).call(n,t,")")):"rgba(255, 255, 255, 0.1)"},bC.getFullScreenEl=function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement},bC.checkIsFunction=function(e){return e&&"function"==typeof e},bC.checkIsObject=function(e){return null!==e&&"object"===cT(e)},bC.hide=function(e){e.style.display="none"},bC.show=function(e,t){e.style.display=t||"block"},bC.isUndefined=function(e){if(null==e)return!0},bC.isNotNull=function(e){return!(null==e)},bC.setStyleFromCsstext=function(e,t){if(t)if("String"===bC.typeOf(t)){var n=t.replace(/\s+/g,"").split(";");Bw(n).call(n,(function(t){if(t){var n=t.split(":");n.length>1&&(e.style[n[0]]=n[1])}}))}else{var r;Bw(r=fk(t)).call(r,(function(n){e.style[n]=t[n]}))}},bC.filterStyleFromText=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["width","height","top","left","bottom","right","position","z-index","padding","margin","transform"],r=e.style.cssText;if(!r)return{};var i=r.replace(/\s+/g,"").split(";"),o={},a={};return Bw(i).call(i,(function(e){if(e){var t=e.split(":");t.length>1&&(!function(e,t){for(var n=0,r=t.length;n<r;n++)if(hk(e).call(e,t[n])>-1)return!0;return!1}(t[0],n)?a[t[0]]=t[1]:o[t[0]]=t[1])}})),e.setAttribute("style",""),Bw(t=fk(a)).call(t,(function(t){e.style[t]=a[t]})),o},bC.getStyleFromCsstext=function(e){var t=e.style.cssText;if(!t)return{};var n=t.replace(/\s+/g,"").split(";"),r={};return Bw(n).call(n,(function(e){if(e){var t=e.split(":");t.length>1&&(r[t[0]]=t[1])}})),r},bC.preloadImg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){var r=new window.Image;r.onload=function(e){r=null,t&&t(e)},r.onerror=function(e){r=null,n&&n(e)},r.src=e}},bC.stopPropagation=function(e){e&&e.stopPropagation()},bC.scrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},bC.scrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},bC.checkTouchSupport=function(){return"ontouchstart"in window},bC.getBuffered2=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,n=[],r=0;r<e.length;r++)n.push({start:e.start(r)<.5?0:e.start(r),end:e.end(r)});fC(n).call(n,(function(e,t){var n=e.start-t.start;return n||t.end-e.end}));var i=[];if(t)for(var o=0;o<n.length;o++){var a=i.length;if(a){var s=i[a-1].end;n[o].start-s<t?n[o].end>s&&(i[a-1].end=n[o].end):i.push(n[o])}else i.push(n[o])}else i=n;return new _C(i)},bC.getEventPos=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e.touches&&e.touches.length>0&&(e=e.touches[0]),{x:e.x/t,y:e.y/t,clientX:e.clientX/t,clientY:e.clientY/t,offsetX:e.offsetX/t,offsetY:e.offsetY/t,pageX:e.pageX/t,pageY:e.pageY/t}},bC.requestAnimationFrame=function(e){var t=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(t)return t(e)},bC.getHostFromUrl=function(e){if("String"!==bC.typeOf(e))return"";var t=e.split("/"),n="";return t.length>3&&t[2]&&(n=t[2]),n},bC.cancelAnimationFrame=function(e){var t=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.cancelRequestAnimationFrame;t&&t(e)},bC.isMSE=function(e){return!!(e&&e instanceof HTMLMediaElement)&&(/^blob/.test(e.currentSrc)||/^blob/.test(e.src))},bC.isBlob=function(e){return/^blob/.test(e)},bC.generateSessionId=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=(new Date).getTime();try{e=rE(e)}catch(AV){e=0}return t+=e,window.performance&&"function"==typeof window.performance.now&&(t+=rE(window.performance.now())),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var n=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?n:3&n|8).toString(16)}))},bC.createEvent=function(e){var t;return"function"==typeof window.Event?t=new Event(e):(t=document.createEvent("Event")).initEvent(e,!0,!0),t},bC.adjustTimeByDuration=function(e,t,n){return t&&e&&(e>t||n&&e<t)?t:e},bC.createPositionBar=function(e,t){var n=bC.createDom("xg-bar","",{"data-index":-1},e);return t.appendChild(n),n},bC.getTransformStyle=function(){var e,t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{x:0,y:0,scale:1,rotate:0};return Fm(e=Fm(t=Fm(n="scale(".concat(r.scale||1,") translate(")).call(n,r.x||0,"%, ")).call(t,r.y||0,"%) rotate(")).call(e,r.rotate||0,"deg)")},bC.convertDeg=function(e){return Math.abs(e)<=1?360*e:e%360},bC.getIndexByTime=function(e,t){var n=t.length,r=-1;if(n<1)return r;if(e<=t[0].end||n<2)r=0;else if(e>t[n-1].end)r=n-1;else for(var i=1;i<n;i++)if(e>t[i-1].end&&e<=t[i].end){r=i;break}return r},bC.getOffsetCurrentTime=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,r=-1;if((r=n>=0&&n<t.length?n:bC.getIndexByTime(e,t))<0)return-1;var i=t.length,o=t[r],a=o.start,s=o.end,l=o.cTime,u=o.offset;return e<a?l:e>=a&&e<=s?e-u:e>s&&r>=i-1?s:-1},bC.getCurrentTimeByOffset=function(e,t){var n=-1;if(!t||t.length<0)return e;for(var r=0;r<t.length;r++)if(e<=t[r].duration){n=r;break}if(-1!==n){var i=t[n].start;return n-1<0?i+e:i+(e-t[n-1].duration)}return e};var TC=/(Android)\s([\d.]+)/,SC=/(Version)\/([\d.]+)/,CC=["avc1.42E01E, mp4a.40.2","avc1.58A01E, mp4a.40.2","avc1.4D401E, mp4a.40.2","avc1.64001E, mp4a.40.2","avc1.42E01E","mp4v.20.8","avc1.42E01E, mp4a.40.2","avc1.58A01E, mp4a.40.2","avc1.4D401E, mp4a.40.2","avc1.64001E, mp4a.40.2","mp4v.20.8, mp4a.40.2","mp4v.20.240, mp4a.40.2"],xC={get device(){return xC.os.isPc?"pc":"mobile"},get browser(){var e,t;if("undefined"==typeof navigator)return"";var n=navigator.userAgent.toLowerCase(),r={ie:/rv:([\d.]+)\) like gecko/,firefox:/firefox\/([\d.]+)/,chrome:/chrome\/([\d.]+)/,opera:/opera.([\d.]+)/,safari:/version\/([\d.]+).*safari/};return Fm(e=[]).call(e,Hw(t=fk(r)).call(t,(function(e){return r[e].test(n)})))[0]},get os(){if("undefined"==typeof navigator)return{};var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),n=/(?:SymbianOS)/.test(e)||t,r=/(?:Android)/.test(e),i=/(?:Firefox)/.test(e),o=/(?:iPad|PlayBook)/.test(e)||r&&!/(?:Mobile)/.test(e)||i&&/(?:Tablet)/.test(e),a=/(?:iPhone)/.test(e)&&!o,s=!(a||r||n||o),l=/(?:iPad|PlayBook)/.test(e);return{isTablet:o,isPhone:a,isIpad:l,isIos:a||l,isAndroid:r,isPc:s,isSymbian:n,isWindowsPhone:t,isFireFox:i}},get osVersion(){if("undefined"==typeof navigator)return 0;var e=navigator.userAgent,t="",n=(t=/(?:iPhone)|(?:iPad|PlayBook)/.test(e)?SC:TC)?t.exec(e):[];if(n&&n.length>=3){var r=n[2].split(".");return r.length>0?rE(r[0]):0}return 0},get isWeixin(){if("undefined"==typeof navigator)return!1;return!!/(micromessenger)\/([\d.]+)/.exec(navigator.userAgent.toLocaleLowerCase())},isSupportMP4:function(){var e={isSupport:!1,mime:""};if("undefined"==typeof document)return e;if(this.supportResult)return this.supportResult;var t=document.createElement("video");return"function"==typeof t.canPlayType&&Bw(CC).call(CC,(function(n){"probably"===t.canPlayType('video/mp4; codecs="'.concat(n,'"'))&&(e.isSupport=!0,e.mime+="||".concat(n))})),this.supportResult=e,t=null,e},isMSESupport:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if("undefined"==typeof MediaSource||!MediaSource)return!1;try{return MediaSource.isTypeSupported(e)}catch(PH){return this._logger.error(e,PH),!1}},isHevcSupported:function(){return!("undefined"==typeof MediaSource||!MediaSource.isTypeSupported)&&(MediaSource.isTypeSupported('video/mp4;codecs="hev1.1.6.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.2.4.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.3.E.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.4.10.L120.90"'))},probeConfigSupported:function(e){var t={supported:!1,smooth:!1,powerEfficient:!1};if(!e||"undefined"==typeof navigator)return Vw.resolve(t);if(navigator.mediaCapabilities&&navigator.mediaCapabilities.decodingInfo)return navigator.mediaCapabilities.decodingInfo(e);var n=e.video||{},r=e.audio||{};try{var i=MediaSource.isTypeSupported(n.contentType),o=MediaSource.isTypeSupported(r.contentType);return Vw.resolve({supported:i&&o,smooth:!1,powerEfficient:!1})}catch(AV){return Vw.resolve(t)}}},PC="3.0.10-alpha.4",AC={1:"media",2:"media",3:"media",4:"media",5:"media",6:"media"},IC={1:5101,2:5102,3:5103,4:5104,5:5105,6:5106},RC=dT((function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{errorType:"",errorCode:0,errorMessage:"",originError:"",ext:{},mediaError:null};hT(this,e);var r=t&&t.i18n?t.i18n.ERROR_TYPES:null;if(t.media){var i,o=n.mediaError?n.mediaError:t.media.error||{},a=t.duration,s=t.currentTime,l=t.ended,u=t.src,c=t.currentSrc,h=t.media,f=h.readyState,d=h.networkState,p=n.errorCode||o.code;IC[p]&&(p=IC[p]);var v={playerVersion:PC,currentTime:s,duration:a,ended:l,readyState:f,networkState:d,src:u||c,errorType:n.errorType,errorCode:p,message:n.errorMessage||o.message,mediaError:o,originError:n.originError?n.originError.stack:"",host:bC.getHostFromUrl(u||c)};return n.ext&&Bw(i=fk(n.ext)).call(i,(function(e){v[e]=n.ext[e]})),v}if(arguments.length>1){for(var g={playerVersion:PC,domain:document.domain},y=["errorType","currentTime","duration","networkState","readyState","src","currentSrc","ended","errd","errorCode","mediaError"],m=0;m<arguments.length;m++)g[y[m]]=arguments[m];return g.ex=r?(r[arguments[0]]||{}).msg:"",g}})),OC="play",DC="ended",MC="pause",LC="error",NC="seeked",UC="timeupdate",FC="waiting",BC="canplay",VC="durationchange",HC="volumechange",jC="loadeddata",zC="emptied",KC="focus",WC="blur",GC="ready",qC="urlNull",YC="autoplay_started",XC="autoplay_was_prevented",JC="complete",QC="replay",$C="destroy",ZC="urlchange",ex="download_speed_change",tx="fullscreen_change",nx="cssFullscreen_change",rx="mini_state_change",ix="definition_change",ox="before_definition_change",ax="after_definition_change",sx="retry",lx="video_resize",ux="pip_change",cx="playnext",hx="shortcut",fx="user_action",dx="reset",px="source_error",vx="source_success",gx=["play","playing","ended","pause","error","seeking","seeked","timeupdate","waiting","canplay","canplaythrough","durationchange","volumechange","loadeddata","loadedmetadata","ratechange","progress","loadstart","emptied","stalled","suspend","abort","lowdecode"],yx=Object.freeze({__proto__:null,ABORT:"abort",AFTER_DEFINITION_CHANGE:ax,AUTOPLAY_PREVENTED:XC,AUTOPLAY_STARTED:YC,BEFORE_DEFINITION_CHANGE:ox,BUFFER_CHANGE:"bufferedChange",CANPLAY:BC,CANPLAY_THROUGH:"canplaythrough",COMPLETE:JC,CSS_FULLSCREEN_CHANGE:nx,DEFINITION_CHANGE:ix,DESTROY:$C,DOWNLOAD_SPEED_CHANGE:ex,DURATION_CHANGE:VC,EMPTIED:zC,ENDED:DC,ERROR:LC,FPS_STUCK:"fps_stuck",FULLSCREEN_CHANGE:tx,LOADED_DATA:jC,LOADED_METADATA:"loadedmetadata",LOAD_START:"loadstart",MINI_STATE_CHANGE:rx,PAUSE:MC,PIP_CHANGE:ux,PLAY:OC,PLAYER_BLUR:WC,PLAYER_FOCUS:KC,PLAYING:"playing",PLAYNEXT:cx,PROGRESS:"progress",RATE_CHANGE:"ratechange",READY:GC,REPLAY:QC,RESET:dx,RETRY:sx,ROTATE:"rotate",SCREEN_SHOT:"screenShot",SEEKED:NC,SEEKING:"seeking",SEI_PARSED:"SEI_PARSED",SHORTCUT:hx,SOURCE_ERROR:px,SOURCE_SUCCESS:vx,STALLED:"stalled",STATS_EVENTS:{STATS_INFO:"stats_info",STATS_DOWNLOAD:"stats_download",STATS_RESET:"stats_reset"},SUSPEND:"suspend",SWITCH_SUBTITLE:"switch_subtitle",TIME_UPDATE:UC,URL_CHANGE:ZC,URL_NULL:qC,USER_ACTION:fx,VIDEO_EVENTS:gx,VIDEO_RESIZE:lx,VOLUME_CHANGE:HC,WAITING:FC,XGLOG:"xglog"});function mx(e,t){this&&this.emit&&("error"===e?this.errorHandler(e,t.error):this.emit(e,t))}var _x=function(e){vT(n,e);var t=bT(n);function n(e){var r;hT(this,n),(r=t.call(this,e))._hasStart=!1,r._currentTime=0,r._duration=0,r._internalOp={},r._lastMuted=!1,r.vtype="MP4",r._rate=-1,r.mediaConfig=BT({},{controls:!1,autoplay:e.autoplay,playsinline:e.playsinline,"x5-playsinline":e.playsinline,"webkit-playsinline":e.playsinline,"x5-video-player-fullscreen":e["x5-video-player-fullscreen"]||e.x5VideoPlayerFullscreen,"x5-video-orientation":e["x5-video-orientation"]||e.x5VideoOrientation,airplay:e.airplay,"webkit-airplay":e.airplay,tabindex:0|e.tabindex,mediaType:e.mediaType||"video","data-index":-1},e.videoConfig,e.videoAttributes);var i=e["x5-video-player-type"]||e.x5VideoPlayerType;return xC.isWeixin&&xC.os.isAndroid&&i&&(r.mediaConfig["x5-video-player-type"]=i,delete r.mediaConfig.playsinline,delete r.mediaConfig["webkit-playsinline"],delete r.mediaConfig["x5-playsinline"]),e.loop&&(r.mediaConfig.loop="loop"),e.autoplayMuted&&!Object.prototype.hasOwnProperty.call(r.mediaConfig,"muted")&&(r.mediaConfig.muted=!0),r.media=bC.createDom(r.mediaConfig.mediaType,"",r.mediaConfig,""),e.defaultPlaybackRate&&(r.media.defaultPlaybackRate=r.media.playbackRate=e.defaultPlaybackRate),"Number"===bC.typeOf(e.volume)&&(r.volume=e.volume),e.autoplayMuted&&(r.media.muted=!0,r._lastMuted=!0),e.autoplay&&(r.media.autoplay=!0),r._interval={},r.mediaEventMiddleware={},r.attachVideoEvents(),r}return dT(n,[{key:"setEventsMiddleware",value:function(e){var t,n=this;Bw(t=fk(e)).call(t,(function(t){n.mediaEventMiddleware[t]=e[t]}))}},{key:"removeEventsMiddleware",value:function(e){var t,n=this;Bw(t=fk(e)).call(t,(function(e){delete n.mediaEventMiddleware[e]}))}},{key:"attachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers||(this._evHandlers=Bw(gx).call(gx,(function(t){var n,r=Fm(n="on".concat(t.charAt(0).toUpperCase())).call(n,dk(t).call(t,1));return"function"==typeof e[r]&&e.on(t,e[r]),pT({},t,function(e,t){return function(n,r){var i={player:t,eventName:e,originalEvent:n,detail:n.detail||{},timeStamp:n.timeStamp,currentTime:t.currentTime,duration:t.duration,paused:t.paused,ended:t.ended,isInternalOp:!!t._internalOp[n.type],muted:t.muted,volume:t.volume,host:bC.getHostFromUrl(t.currentSrc),vtype:t.vtype};if(t.removeInnerOP(n.type),"timeupdate"===e&&(t._currentTime=t.media&&t.media.currentTime),"ratechange"===e){var o=t.media?t.media.playbackRate:0;if(o&&t._rate===o)return;t._rate=t.media&&t.media.playbackRate}if("durationchange"===e&&(t._duration=t.media.duration),"volumechange"===e&&(i.isMutedChange=t._lastMuted!==t.muted,t._lastMuted=t.muted),"error"===e&&(i.error=r||t.video.error),t.mediaEventMiddleware[e]){var a=mx.bind(t,e,i);try{t.mediaEventMiddleware[e].call(t,i,a)}catch(s){throw mx.call(t,e,i),s}}else mx.call(t,e,i)}}(t,e))}))),this._evHandlers.forEach((function(e){var n=fk(e)[0];t.addEventListener(n,e[n],!1)}))}},{key:"detachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers.forEach((function(e){var n=fk(e)[0];t.removeEventListener(n,e[n],!1)})),this._evHandlers.forEach((function(t){var n,r=fk(t)[0],i=Fm(n="on".concat(r.charAt(0).toUpperCase())).call(n,dk(r).call(r,1));"function"==typeof e[i]&&e.off(r,e[i])})),this._evHandlers=null}},{key:"_attachSourceEvents",value:function(e,t){var n=this;e.removeAttribute("src"),e.load(),t.forEach((function(e,t){n.media.appendChild(bC.createDom("source","",{src:"".concat(e.src),type:"".concat(e.type||""),"data-index":t+1}))}));var r=e.children;if(r){this._videoSourceCount=r.length,this._videoSourceIndex=r.length,this._vLoadeddata=function(e){n.emit(vx,{src:e.target.currentSrc,host:bC.getHostFromUrl(e.target.currentSrc)})};for(var i=null,o=0;o<this._evHandlers.length;o++)if("error"===fk(this._evHandlers[o])[0]){i=this._evHandlers[o];break}!this._sourceError&&(this._sourceError=function(e){var t=rE(e.target.getAttribute("data-index"),10);if(n._videoSourceIndex--,0===n._videoSourceIndex||t>=n._videoSourceCount){var r={code:4,message:"sources_load_error"};i?i.error(e,r):n.errorHandler("error",r)}var o=AC[4];n.emit(px,new RC(n,{errorType:o,errorCode:4,errorMessage:"sources_load_error",mediaError:{code:4,message:"sources_load_error"},src:e.target.src}))});for(var a=0;a<r.length;a++)r[a].addEventListener("error",this._sourceError);e.addEventListener("loadeddata",this._vLoadeddata)}}},{key:"_detachSourceEvents",value:function(e){var t=e.children;if(t&&0!==t.length&&this._sourceError){for(var n=0;n<t.length;n++)t[n].removeEventListener("error",this._sourceError);for(;t.length>0;)e.removeChild(t[0]);this._vLoadeddata&&e.removeEventListener("loadeddata",this._vLoadeddata)}}},{key:"errorHandler",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.media&&(this.media.error||t)){var n=this.media.error||t,r=n.code?AC[n.code]:"other";n.message;this.media.currentSrc||(n={code:6,message:"empty_src"}),this.emit(e,new RC(this,{errorType:r,errorCode:n.code,errorMessage:n.message||"",mediaError:n}))}}},{key:"destroy",value:function(){for(var e in this.media&&(this.media.pause&&(this.media.pause(),this.media.muted=!0),this.media.removeAttribute("src"),this.media.load()),this._currentTime=0,this._duration=0,this.mediaConfig=null,this._interval)Object.prototype.hasOwnProperty.call(this._interval,e)&&(clearInterval(this._interval[e]),this._interval[e]=null);this.detachVideoEvents(),this.media=null,this.mediaEventMiddleware={},this.removeAllListeners()}},{key:"video",get:function(){return this.media},set:function(e){this.media=e}},{key:"play",value:function(){return this.media?this.media.play():null}},{key:"pause",value:function(){this.media&&this.media.pause()}},{key:"load",value:function(){this.media&&this.media.load()}},{key:"canPlayType",value:function(e){return!!this.media&&this.media.canPlayType(e)}},{key:"getBufferedRange",value:function(e){var t=[0,0];if(!this.media)return t;e||(e=this.media.buffered);var n=this.media.currentTime;if(e)for(var r=0,i=e.length;r<i&&(t[0]=e.start(r),t[1]=e.end(r),!(t[0]<=n&&n<=t[1]));r++);return t[0]-n<=0&&n-t[1]<=0?t:[0,0]}},{key:"autoplay",get:function(){return!!this.media&&this.media.autoplay},set:function(e){this.media&&(this.media.autoplay=e)}},{key:"buffered",get:function(){return this.media?this.media.buffered:null}},{key:"buffered2",get:function(){return this.media&&this.media.buffered?bC.getBuffered2(this.media.buffered):null}},{key:"bufferedPoint",get:function(){var e={start:0,end:0};if(!this.media)return e;var t=this.media.buffered;if(!t||0===t.length)return e;for(var n=0;n<t.length;n++)if((t.start(n)<=this.currentTime||t.start(n)<.1)&&t.end(n)>=this.currentTime)return{start:t.start(n),end:t.end(n)};return e}},{key:"crossOrigin",get:function(){return this.media?this.media.crossOrigin:""},set:function(e){this.media&&(this.media.crossOrigin=e)}},{key:"currentSrc",get:function(){return this.media?this.media.currentSrc:""},set:function(e){this.media&&(this.media.currentSrc=e)}},{key:"currentTime",get:function(){return this.media?void 0!==this.media.currentTime?this.media.currentTime:this._currentTime:0},set:function(e){this.media&&(this.media.currentTime=e)}},{key:"defaultMuted",get:function(){return!!this.media&&this.media.defaultMuted},set:function(e){this.media&&(this.media.defaultMuted=e)}},{key:"duration",get:function(){return this._duration}},{key:"ended",get:function(){return!!this.media&&this.media.ended}},{key:"error",get:function(){return this.media.error}},{key:"errorNote",get:function(){if(!this.media.error)return"";return["MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED"][this.media.error.code-1]}},{key:"loop",get:function(){return!!this.media&&this.media.loop},set:function(e){this.media&&(this.media.loop=e)}},{key:"muted",get:function(){return!!this.media&&this.media.muted},set:function(e){this.media&&this.media.muted!==e&&(this._lastMuted=this.media.muted,this.media.muted=e)}},{key:"networkState",get:function(){return this.media.networkState}},{key:"paused",get:function(){return!this.media||this.media.paused}},{key:"playbackRate",get:function(){return this.media?this.media.playbackRate:0},set:function(e){this.media&&e!==1/0&&(this.media.defaultPlaybackRate=e,this.media.playbackRate=e)}},{key:"played",get:function(){return this.media?this.media.played:null}},{key:"preload",get:function(){return!!this.media&&this.media.preload},set:function(e){this.media&&(this.media.preload=e)}},{key:"readyState",get:function(){return this.media.readyState}},{key:"seekable",get:function(){return!!this.media&&this.media.seekable}},{key:"seeking",get:function(){return!!this.media&&this.media.seeking}},{key:"src",get:function(){return this.media?this.media.src:""},set:function(e){this.media&&(this.emit(ZC,e),this.emit(FC),this._currentTime=0,this._duration=0,bC.isMSE(this.media)?this.onWaiting():(this._detachSourceEvents(this.media),"Array"===bC.typeOf(e)?this._attachSourceEvents(this.media,e):e?this.media.src=e:this.media.removeAttribute("src"),this.load()))}},{key:"volume",get:function(){return this.media?this.media.volume:0},set:function(e){e!==1/0&&this.media&&(this.media.volume=e)}},{key:"addInnerOP",value:function(e){this._internalOp[e]=!0}},{key:"removeInnerOP",value:function(e){delete this._internalOp[e]}},{key:"emit",value:function(e,t){for(var r,i,o=arguments.length,a=new Array(o>2?o-2:0),s=2;s<o;s++)a[s-2]=arguments[s];(i=wT(gT(n.prototype),"emit",this)).call.apply(i,Fm(r=[this,e,t]).call(r,a))}},{key:"on",value:function(e,t){for(var r,i,o=arguments.length,a=new Array(o>2?o-2:0),s=2;s<o;s++)a[s-2]=arguments[s];(i=wT(gT(n.prototype),"on",this)).call.apply(i,Fm(r=[this,e,t]).call(r,a))}},{key:"once",value:function(e,t){for(var r,i,o=arguments.length,a=new Array(o>2?o-2:0),s=2;s<o;s++)a[s-2]=arguments[s];(i=wT(gT(n.prototype),"once",this)).call.apply(i,Fm(r=[this,e,t]).call(r,a))}},{key:"off",value:function(e,t){for(var r,i,o=arguments.length,a=new Array(o>2?o-2:0),s=2;s<o;s++)a[s-2]=arguments[s];(i=wT(gT(n.prototype),"off",this)).call.apply(i,Fm(r=[this,e,t]).call(r,a))}},{key:"offAll",value:function(){wT(gT(n.prototype),"removeAllListeners",this).call(this)}}]),n}(HT),bx=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{name:"xgplayer",version:1,db:null,ojstore:{name:"xg-m4a",keypath:"vid"}};hT(this,e),this.indexedDB=window.indexedDB||window.webkitindexedDB,this.IDBKeyRange=window.IDBKeyRange||window.webkitIDBKeyRange,this.myDB=t}return dT(e,[{key:"openDB",value:function(e){var t=this,n=this,r=this.myDB.version||1,i=n.indexedDB.open(n.myDB.name,r);i.onerror=function(e){},i.onsuccess=function(r){t.myDB.db=r.target.result,e.call(n)},i.onupgradeneeded=function(e){var t=e.target.result;e.target.transaction,t.objectStoreNames.contains(n.myDB.ojstore.name)||t.createObjectStore(n.myDB.ojstore.name,{keyPath:n.myDB.ojstore.keypath})}}},{key:"deletedb",value:function(){this.indexedDB.deleteDatabase(this.myDB.name)}},{key:"closeDB",value:function(){this.myDB.db.close()}},{key:"addData",value:function(e,t){for(var n,r=this.myDB.db.transaction(e,"readwrite").objectStore(e),i=0;i<t.length;i++)(n=r.add(t[i])).onerror=function(){},n.onsuccess=function(){}}},{key:"putData",value:function(e,t){for(var n,r=this.myDB.db.transaction(e,"readwrite").objectStore(e),i=0;i<t.length;i++)(n=r.put(t[i])).onerror=function(){},n.onsuccess=function(){}}},{key:"getDataByKey",value:function(e,t,n){var r=this,i=this.myDB.db.transaction(e,"readwrite").objectStore(e).get(t);i.onerror=function(){n.call(r,null)},i.onsuccess=function(e){var t=e.target.result;n.call(r,t)}}},{key:"deleteData",value:function(e,t){this.myDB.db.transaction(e,"readwrite").objectStore(e).delete(t)}},{key:"clearData",value:function(e){this.myDB.db.transaction(e,"readwrite").objectStore(e).clear()}}]),e}(),wx=["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],kx=["requestFullscreen","webkitRequestFullscreen","mozRequestFullScreen","msRequestFullscreen"],Ex=["exitFullscreen","webkitExitFullscreen","mozCancelFullScreen","msExitFullscreen"],Tx="data-xgplayerid";function Sx(e,t,n){for(var r,i=arguments.length,o=new Array(i>3?i-3:0),a=3;a<i;a++)o[a-3]=arguments[a];var s,l=t.call.apply(t,Fm(r=[e]).call(r,o));n&&"function"==typeof n&&(l&&l.then?l.then((function(){for(var t,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];n.call.apply(n,Fm(t=[e]).call(t,i))})):n.call.apply(n,Fm(s=[e]).call(s,o)))}function Cx(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{pre:null,next:null};return this.__hooks||(this.__hooks={}),!this.__hooks[e]&&(this.__hooks[e]=null),function(){var r,i=arguments,o=this;if(n.pre)try{var a,s;(s=n.pre).call.apply(s,Fm(a=[this]).call(a,dk(Array.prototype).call(arguments)))}catch(AV){var l,u;throw AV.message=Fm(l=Fm(u="[pluginName: ".concat(this.pluginName,":")).call(u,e,":pre error] >> ")).call(l,AV.message),AV}if(this.__hooks&&this.__hooks[e])try{var c,h,f,d=(h=this.__hooks[e]).call.apply(h,Fm(c=[this,this]).call(c,dk(Array.prototype).call(arguments)));if(d)if(d.then)d.then((function(e){var r;!1!==e&&Sx.apply(void 0,Fm(r=[o,t,n.next]).call(r,kT(i)))})).catch((function(e){throw e}));else Sx.apply(void 0,Fm(f=[this,t,n.next]).call(f,dk(Array.prototype).call(arguments)));else if(void 0===d){var p;Sx.apply(void 0,Fm(p=[this,t,n.next]).call(p,dk(Array.prototype).call(arguments)))}}catch(AV){var v,g;throw AV.message=Fm(v=Fm(g="[pluginName: ".concat(this.pluginName,":")).call(g,e,"] >> ")).call(v,AV.message),AV}else Sx.apply(void 0,Fm(r=[this,t,n.next]).call(r,dk(Array.prototype).call(arguments)))}.bind(this)}function xx(e,t){var n=this.__hooks;if(n)return n.hasOwnProperty(e)?(n&&(n[e]=t),!0):(console.warn("has no supported hook which name [".concat(e,"]")),!1)}function Px(e,t){var n=this.__hooks;n&&delete n[e]}function Ax(e){if(this.plugins&&this.plugins[e.toLowerCase()]){for(var t=this.plugins[e.toLowerCase()],n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.useHooks&&t.useHooks.apply(t,r)}}function Ix(e){if(this.plugins&&this.plugins[e.toLowerCase()]){var t=this.plugins[e.toLowerCase()];if(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.removeHooks&&t.removeHooks.apply(t,r)}}}function Rx(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.__hooks={},t&&Bw(t).call(t,(function(t){e.__hooks[t]=null})),gE(e,"hooks",{get:function(){var t;return e.__hooks&&Bw(t=fk(e.__hooks)).call(t,(function(t){if(e.__hooks[t])return t}))}})}function Ox(e){e.__hooks=null}function Dx(e,t,n){for(var r=arguments.length,i=new Array(r>3?r-3:0),o=3;o<r;o++)i[o-3]=arguments[o];var a;if(!e.__hooks||!e.__hooks[t])return n.call.apply(n,Fm(a=[e,e]).call(a,i));var s,l,u=(l=e.__hooks[t]).call.apply(l,Fm(s=[e,e]).call(s,i));if(u&&u.then)u.then((function(t){var r;return!1===t?null:n.call.apply(n,Fm(r=[e,e]).call(r,i))})).catch((function(e){console.warn("[runHooks]".concat(t," reject"),e.message)}));else if(!1!==u){var c;return n.call.apply(n,Fm(c=[e,e]).call(c,i))}}function Mx(e,t){var n;mC.logError(Fm(n="[".concat(e,"] event or callback cant be undefined or null when call ")).call(n,t))}var Lx=function(){function e(t){hT(this,e),bC.checkIsFunction(this.beforeCreate)&&this.beforeCreate(t),Rx(this),this.__args=t,this.__events={},this.__onceEvents={},this.config=t.config||{},this.player=null,this.playerConfig={},this.pluginName="",this.__init(t)}return dT(e,[{key:"beforeCreate",value:function(e){}},{key:"afterCreate",value:function(){}},{key:"beforePlayerInit",value:function(){}},{key:"onPluginsReady",value:function(){}},{key:"afterPlayerInit",value:function(){}},{key:"destroy",value:function(){}},{key:"__init",value:function(e){this.player=e.player,this.playerConfig=e.player&&e.player.config,this.pluginName=e.pluginName?e.pluginName.toLowerCase():this.constructor.pluginName.toLowerCase(),this.logger=e.player&&e.player.logger}},{key:"updateLang",value:function(e){e||(e=this.lang)}},{key:"lang",get:function(){return this.player.lang}},{key:"i18n",get:function(){return this.player.i18n}},{key:"i18nKeys",get:function(){return this.player.i18nKeys}},{key:"domEventType",get:function(){var e=bC.checkTouchSupport()?"touch":"mouse";return!this.playerConfig||"touch"!==this.playerConfig.domEventType&&"mouse"!==this.playerConfig.domEventType||(e=this.playerConfig.domEventType),e}},{key:"on",value:function(e,t){var n=this;e&&t&&this.player?"string"==typeof e?(this.__events[e]=t,this.player.on(e,t)):Array.isArray(e)&&e.forEach((function(e){n.__events[e]=t,n.player.on(e,t)})):Mx(this.pluginName,"plugin.on(event, callback)")}},{key:"once",value:function(e,t){var n=this;e&&t&&this.player?"string"==typeof e?(this.__onceEvents[e]=t,this.player.once(e,t)):Array.isArray(e)&&e.forEach((function(r){n.__onceEvents[r]=t,n.player.once(e,t)})):Mx(this.pluginName,"plugin.once(event, callback)")}},{key:"off",value:function(e,t){var n=this;e&&t&&this.player?"string"==typeof e?(delete this.__events[e],this.player.off(e,t)):Array.isArray(e)&&e.forEach((function(r){delete n.__events[e],n.player.off(r,t)})):Mx(this.pluginName,"plugin.off(event, callback)")}},{key:"offAll",value:function(){var e=this;["__events","__onceEvents"].forEach((function(t){fk(e[t]).forEach((function(n){e[t][n]&&e.off(n,e[t][n]),n&&delete e[t][n]}))})),this.__events={},this.__onceEvents={}}},{key:"emit",value:function(e){var t,n;if(this.player){for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];(n=this.player).emit.apply(n,Fm(t=[e]).call(t,i))}}},{key:"emitUserAction",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.player){var r=uT(uT({},n),{},{pluginName:this.pluginName});this.player.emitUserAction(e,t,r)}}},{key:"hook",value:function(e,t){var n;return Cx.call.apply(Cx,Fm(n=[this]).call(n,dk(Array.prototype).call(arguments)))}},{key:"useHooks",value:function(e,t){for(var n,r=arguments.length,i=new Array(r>2?r-2:0),o=2;o<r;o++)i[o-2]=arguments[o];return xx.call.apply(xx,Fm(n=[this]).call(n,dk(Array.prototype).call(arguments)))}},{key:"removeHooks",value:function(e,t){for(var n,r=arguments.length,i=new Array(r>2?r-2:0),o=2;o<r;o++)i[o-2]=arguments[o];return Px.call.apply(Px,Fm(n=[this]).call(n,dk(Array.prototype).call(arguments)))}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(this.player)return n&&(t.pluginName=n),this.player.registerPlugin({plugin:e,options:t})}},{key:"getPlugin",value:function(e){return this.player?this.player.getPlugin(e):null}},{key:"__destroy",value:function(){var e,t=this,n=this.player,r=this.pluginName;this.offAll(),bC.clearAllTimers(this),bC.checkIsFunction(this.destroy)&&this.destroy(),Bw(e=["player","playerConfig","pluginName","logger","__args","__hooks"]).call(e,(function(e){t[e]=null})),n.unRegisterPlugin(r),Ox(this)}}],[{key:"defineGetterOrSetter",value:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&gE(e,n,t[n])}},{key:"defaultConfig",get:function(){return{}}},{key:"pluginName",get:function(){return"pluginName"}}]),e}();if("undefined"!=typeof Element&&!Element.prototype.matches){var Nx=Element.prototype;Nx.matches=Nx.matchesSelector||Nx.mozMatchesSelector||Nx.msMatchesSelector||Nx.oMatchesSelector||Nx.webkitMatchesSelector}var Ux=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}};function Fx(e,t,n,r,i){var o=Bx.apply(this,arguments);return e.addEventListener(n,o,i),{destroy:function(){e.removeEventListener(n,o,i)}}}function Bx(e,t,n,r){return function(n){n.delegateTarget=Ux(n.target,t),n.delegateTarget&&r.call(e,n)}}var Vx=function(e,t,n,r,i){return"function"==typeof e.addEventListener?Fx.apply(null,arguments):"function"==typeof n?Fx.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Bw(Array.prototype).call(e,(function(e){return Fx(e,t,n,r,i)})))},Hx=o(Vx),jx={CONTROLS:"controls",ROOT:"root"},zx={ROOT:"root",ROOT_LEFT:"rootLeft",ROOT_RIGHT:"rootRight",ROOT_TOP:"rootTop",CONTROLS_LEFT:"controlsLeft",CONTROLS_RIGTH:"controlsRight",CONTROLS_RIGHT:"controlsRight",CONTROLS_CENTER:"controlsCenter",CONTROLS:"controls"};function Kx(e){return!!e&&(hk(e)&&/^(?:http|data:|\/)/.test(e))}function Wx(e,t){var n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",s=null;if(e instanceof window.Element)return bC.addClass(e,i),Bw(r=fk(o)).call(r,(function(t){e.setAttribute(t,o[t])})),e;if(Kx(e)||Kx(e.url))return o.src=Kx(e)?e:e.url||"",s=bC.createDom(e.tag||"img","",o,"xg-img ".concat(i));if("function"==typeof e)try{var l,u;return(s=e())instanceof window.Element?(bC.addClass(s,i),Bw(l=fk(o)).call(l,(function(e){s.setAttribute(e,o[e])})),s):(mC.logWarn(Fm(u="warn>>icons.".concat(t," in config of plugin named [")).call(u,a,"] is a function mast return an Element Object")),null)}catch(AV){return mC.logError("Plugin named [".concat(a,"]:createIcon"),AV),null}return"string"==typeof e?bC.createDomFromHtml(e,o,i):(mC.logWarn(Fm(n="warn>>icons.".concat(t," in config of plugin named [")).call(n,a,"] is invalid")),null)}function Gx(e,t){var n,r=t.config.icons||t.playerConfig.icons;Bw(n=fk(e)).call(n,(function(n){var i=e[n],o=i&&i.class?i.class:"",a=i&&i.attr?i.attr:{},s=null;r&&r[n]&&(o=function(e,t){var n;return"object"===cT(e)&&e.class&&"string"==typeof e.class?Fm(n="".concat(t," ")).call(n,e.class):t}(r[n],o),a=function(e,t){var n;return"object"===cT(e)&&e.attr&&"object"===cT(e.attr)&&Bw(n=fk(e.attr)).call(n,(function(n){t[n]=e.attr[n]})),t}(r[n],a),s=Wx(r[n],n,o,a,t.pluginName)),!s&&i&&(s=Wx(i.icon?i.icon:i,a,o,{},t.pluginName)),t.icons[n]=s}))}var qx=function(e){vT(n,e);var t=bT(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return hT(this,n),(e=t.call(this,r)).__delegates=[],e}return dT(n,[{key:"__init",value:function(e){if(wT(gT(n.prototype),"__init",this).call(this,e),e.root){var t=e.root,r=null;this.icons={},this.root=null,this.parent=null,Gx(this.registerIcons()||{},this),this.langText={};var i,o,a,s=this.registerLanguageTexts()||{};o=this,Bw(a=fk(i=s)).call(a,(function(e){gE(o.langText,e,{get:function(){var t=o.lang,n=o.i18n;return n[e]?n[e]:i[e]&&i[e][t]||""}})}));var l="";try{l=this.render()}catch(AV){var u;throw mC.logError("Plugin:".concat(this.pluginName,":render"),AV),new Error(Fm(u="Plugin:".concat(this.pluginName,":render:")).call(u,AV.message))}if(l)(r=n.insert(l,t,e.index)).setAttribute("data-index",e.index);else{if(!e.tag)return;(r=bC.createDom(e.tag,"",e.attr,e.name)).setAttribute("data-index",e.index),t.appendChild(r)}this.root=r,this.parent=t;var c=this.config.attr||{},h=this.config.style||{};this.setAttr(c),this.setStyle(h),this.config.index&&this.root.setAttribute("data-index",this.config.index),this.__registerChildren()}}},{key:"__registerChildren",value:function(){var e=this;if(this.root){this._children=[];var t,n=this.children();if(n&&"object"===cT(n))if(fk(n).length>0)Bw(t=fk(n)).call(t,(function(t){var r,i,o=t,a=n[o],s={root:e.root};"function"==typeof a?(r=e.config[o]||{},i=a):"object"===cT(a)&&"function"==typeof a.plugin&&(r=a.options?bC.deepCopy(e.config[o]||{},a.options):e.config[o]||{},i=a.plugin),s.config=r,void 0!==r.index&&(s.index=r.index),r.root&&(s.root=r.root),e.registerPlugin(i,s,o)}))}}},{key:"updateLang",value:function(e){e||(e=this.lang);var t=this.root,n=this.i18n,r=this.langText;t&&function e(t,n){for(var r=0;r<t.children.length;r++)t.children[r].children.length>0?e(t.children[r],n):n(t.children[r])}(t,(function(t){var i=t.getAttribute&&t.getAttribute("lang-key");if(i){var o=n[i.toUpperCase()]||r[i];o&&(t.innerHTML="function"==typeof o?o(e):o)}}))}},{key:"lang",get:function(){return this.player.lang}},{key:"changeLangTextKey",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=this.i18n||{},r=this.langText;e.setAttribute&&e.setAttribute("lang-key",t);var i=n[t]||r[t]||"";i&&(e.innerHTML=i)}},{key:"plugins",value:function(){return this._children}},{key:"children",value:function(){return{}}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";t.root=t.root||this.root;var i=wT(gT(n.prototype),"registerPlugin",this).call(this,e,t,r);return this._children.push(i),i}},{key:"registerIcons",value:function(){return{}}},{key:"registerLanguageTexts",value:function(){return{}}},{key:"find",value:function(e){if(this.root)return this.root.querySelector(e)}},{key:"bind",value:function(e,t,r){var i=this;if(arguments.length<3&&"function"==typeof t)Array.isArray(e)?e.forEach((function(e){i.bindEL(e,t)})):this.bindEL(e,t);else{var o,a=n.delegate.call(this,this.root,e,t,r);this.__delegates=Fm(o=this.__delegates).call(o,a)}}},{key:"unbind",value:function(e,t){var n=this;if(arguments.length<3&&"function"==typeof t)Array.isArray(e)?e.forEach((function(e){n.unbindEL(e,t)})):this.unbindEL(e,t);else for(var r,i=Fm(r="".concat(e,"_")).call(r,t),o=0;o<this.__delegates.length;o++)if(this.__delegates[o].key===i){var a;this.__delegates[o].destroy(),Mk(a=this.__delegates).call(a,o,1);break}}},{key:"setStyle",value:function(e,t){var n,r=this;if(this.root)return"String"===bC.typeOf(e)?this.root.style[e]=t:void("Object"===bC.typeOf(e)&&Bw(n=fk(e)).call(n,(function(t){r.root.style[t]=e[t]})))}},{key:"setAttr",value:function(e,t){var n,r=this;if(this.root)return"String"===bC.typeOf(e)?this.root.setAttribute(e,t):void("Object"===bC.typeOf(e)&&Bw(n=fk(e)).call(n,(function(t){r.root.setAttribute(t,e[t])})))}},{key:"setHtml",value:function(e,t){this.root&&(this.root.innerHTML=e,"function"==typeof t&&t())}},{key:"bindEL",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.addEventListener(e,t,n)}},{key:"unbindEL",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.removeEventListener(e,t,n)}},{key:"show",value:function(e){if(this.root)return this.root.style.display=void 0!==e?e:"block","none"===window.getComputedStyle(this.root,null).getPropertyValue("display")?this.root.style.display="block":void 0}},{key:"hide",value:function(){this.root&&(this.root.style.display="none")}},{key:"appendChild",value:function(e,t){if(!this.root)return null;if(arguments.length<2&&arguments[0]instanceof window.Element)return this.root.appendChild(arguments[0]);if(!(t&&t instanceof window.Element))return null;try{return"string"==typeof e?qm(this).call(this,e).appendChild(t):e.appendChild(t)}catch(n){return mC.logError("Plugin:appendChild",n),null}}},{key:"render",value:function(){return""}},{key:"destroy",value:function(){}},{key:"__destroy",value:function(){var e,t,r,i=this,o=this.player;(Bw(e=this.__delegates).call(e,(function(e){e.destroy()})),this.__delegates=[],this._children instanceof Array)&&(Bw(r=this._children).call(r,(function(e){o.unRegisterPlugin(e.pluginName)})),this._children=null);this.root&&(this.root.hasOwnProperty("remove")?this.root.remove():this.root.parentNode&&this.root.parentNode.removeChild(this.root)),wT(gT(n.prototype),"__destroy",this).call(this),this.icons={},Bw(t=["root","parent"]).call(t,(function(e){i[e]=null}))}}],[{key:"insert",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=t.children.length,i=Number(n),o=e instanceof window.Node;if(r){for(var a=0,s=null,l="";a<r;a++){s=t.children[a];var u=Number(s.getAttribute("data-index"));if(u>=i){l="beforebegin";break}u<i&&(l="afterend")}return o?"afterend"===l?t.appendChild(e):t.insertBefore(e,s):s.insertAdjacentHTML(l,e),"afterend"===l?t.children[t.children.length-1]:t.children[a]}return o?t.appendChild(e):t.insertAdjacentHTML("beforeend",e),t.children[t.children.length-1]}},{key:"defaultConfig",get:function(){return{}}},{key:"delegate",value:function(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=[];if(e instanceof window.Node&&"function"==typeof r)if(Array.isArray(n))n.forEach((function(n){var a,s=Hx(e,t,n,r,i);s.key=Fm(a="".concat(t,"_")).call(a,n),o.push(s)}));else{var a,s=Hx(e,t,n,r,i);s.key=Fm(a="".concat(t,"_")).call(a,n),o.push(s)}return o}},{key:"ROOT_TYPES",get:function(){return jx}},{key:"POSITIONS",get:function(){return zx}}]),n}(Lx),Yx=function(){function e(){var t=this;if(hT(this,e),pT(this,"__trigger",(function(e){var n=(new Date).getTime();t.timeStamp=n;for(var r=0;r<e.length;r++)t.__runHandler(e[r].target)})),this.__handlers=[],this.timeStamp=0,this.observer=null,window.ResizeObserver)try{this.observer=new window.ResizeObserver(function(e,t,n){var r=!0,i=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return wC(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),kC(e,t,{leading:r,trailing:i,maxWait:t})}(this.__trigger,100,{trailing:!0})),this.timeStamp=(new Date).getTime()}catch(AV){console.error(AV)}}return dT(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer&&this.observer.observe(e);for(var n=e.getAttribute(Tx),r=this.__handlers,i=-1,o=0;o<r.length;o++)r[o]&&e===r[o].target&&(i=o);i>-1?this.__handlers[i].handler=t:this.__handlers.push({target:e,handler:t,playerId:n})}}},{key:"unObserver",value:function(e){var t,n,r=-1;Bw(t=this.__handlers).call(t,(function(t,n){e===t.target&&(r=n)}));try{this.observer&&this.observer.unobserve(e)}catch(AV){}this.observer&&this.observer.unobserve(e),r>-1&&Mk(n=this.__handlers).call(n,r,1)}},{key:"destroyObserver",value:function(){this.observer&&this.observer.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e){for(var t=this.__handlers,n=0;n<t.length;n++)if(t[n]&&e===t[n].target){try{t[n].handler(e)}catch(PH){console.error(PH)}return!0}return!1}}]),e}(),Xx=null;var Jx={pluginGroup:{},init:function(e){var t,n,r=e._pluginInfoId;r||(r=(new Date).getTime(),e._pluginInfoId=r),!e.config.closeResizeObserver&&(t=e.root,n=function(){e.resize()},Xx||(Xx=new Yx),Xx.addObserver(t,n)),0===fk(this.pluginGroup).length&&(e.isUserActive=!0),this.pluginGroup[r]={_player:e,_originalOptions:e.config||{},_plugins:{}}},checkPlayerRoot:function(e){if(this.pluginGroup){for(var t=fk(this.pluginGroup),n=0;n<t.length;n++){var r=this.pluginGroup[t[n]]._player;if(r.root===e)return r}return null}return null},formatPluginInfo:function(e,t){var n=null,r=null;return e.plugin&&"function"==typeof e.plugin?(n=e.plugin,r=e.options):(n=e,r={}),t&&(r.config=t||{}),{PLUFGIN:n,options:r}},checkPluginIfExits:function(e,t){for(var n=0;n<t.length;n++)if(e.toLowerCase()===t[n].pluginName.toLowerCase())return!0;return!1},getRootByConfig:function(e,t){for(var n=fk(t),r=null,i=0;i<n.length;i++)if(e.toLowerCase()===n[i].toLowerCase()){r=t[n[i]];break}return"Object"===bC.typeOf(r)?{root:r.root,position:r.position}:{}},lazyRegister:function(e,t){var n=this,r=t.timeout||1500;return Vw.race([t.loader().then((function(t){var r;r=t&&t.__esModule?t.default:t,n.register(e,r,t.options)})),new Vw((function(e,t){setTimeout((function(){t(new Error("timeout"))}),r)}))])},register:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e&&t&&"function"==typeof t&&void 0!==t.prototype){var r=e._pluginInfoId;if(r&&this.pluginGroup[r]){this.pluginGroup[r]._plugins||(this.pluginGroup[r]._plugins={});var i=this.pluginGroup[r]._plugins,o=this.pluginGroup[r]._originalOptions;n.player=this.pluginGroup[r]._player;var a=n.pluginName||t.pluginName;if(!a)throw new Error("The property pluginName is necessary");if(!t.isSupported||t.isSupported(e.config.mediaType,e.config.codecType)){n.config||(n.config={});for(var s=fk(o),l=0;l<s.length;l++)if(a.toLowerCase()===s[l].toLowerCase()){var u=o[s[l]];"Object"===bC.typeOf(u)?n.config=BT({},n.config,o[s[l]]):"Boolean"===bC.typeOf(u)&&(n.config.disable=!u);break}t.defaultConfig&&fk(t.defaultConfig).forEach((function(e){void 0===n.config[e]&&(n.config[e]=t.defaultConfig[e])})),n.root?"string"==typeof n.root&&(n.root=e[n.root]):n.root=e.root,n.index=n.config.index||0;try{i[a.toLowerCase()]&&(this.unRegister(r,a.toLowerCase()),console.warn("the is one plugin with same pluginName [".concat(a,"] exist, destroy the old instance")));var c=new t(n);return i[a.toLowerCase()]=c,i[a.toLowerCase()].func=t,c&&"function"==typeof c.afterCreate&&c.afterCreate(),c}catch(h){throw console.error(h),h}}else console.warn("not supported plugin [".concat(a,"]"))}}},unRegister:function(e,t){e._pluginInfoId&&(e=e._pluginInfoId),t=t.toLowerCase();try{var n=this.pluginGroup[e]._plugins[t];n&&(n.pluginName&&n.__destroy(),delete this.pluginGroup[e]._plugins[t])}catch(AV){var r;console.error(Fm(r="[unRegister:".concat(t,"] cgid:[")).call(r,e,"] error"),AV)}},deletePlugin:function(e,t){var n=e._pluginInfoId;n&&this.pluginGroup[n]&&this.pluginGroup[n]._plugins&&delete this.pluginGroup[n]._plugins[t]},getPlugins:function(e){var t=e._pluginInfoId;return t&&this.pluginGroup[t]?this.pluginGroup[t]._plugins:{}},findPlugin:function(e,t){var n=e._pluginInfoId;if(!n||!this.pluginGroup[n])return null;var r=t.toLowerCase();return this.pluginGroup[n]._plugins[r]},beforeInit:function(e){var t=this;function n(e){return e&&e.then?e:new Vw((function(e){e()}))}return new Vw((function(r){if(t.pluginGroup)return(e._loadingPlugins&&e._loadingPlugins.length?Vw.all(e._loadingPlugins):Vw.resolve()).then((function(){var i,o=e._pluginInfoId;if(t.pluginGroup[o]){var a=t.pluginGroup[o]._plugins,s=[];fk(a).forEach((function(e){if(a[e]&&a[e].beforePlayerInit)try{var t=a[e].beforePlayerInit();s.push(n(t))}catch(AV){throw s.push(n(null)),AV}})),Vw.all(Fm(i=[]).call(i,s)).then((function(){r()})).catch((function(e){console.error(e),r()}))}else r()}))}))},afterInit:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var n=this.pluginGroup[t]._plugins;fk(n).forEach((function(e){n[e]&&n[e].afterPlayerInit&&n[e].afterPlayerInit()}))}},setLang:function(e,t){var n=t._pluginInfoId;if(n&&this.pluginGroup[n]){var r=this.pluginGroup[n]._plugins;fk(r).forEach((function(t){if(r[t].updateLang)r[t].updateLang(e);else try{r[t].lang=e}catch(PH){console.warn("".concat(t," setLang"))}}))}},reRender:function(e){var t=this,n=e._pluginInfoId;if(n&&this.pluginGroup[n]){var r=[],i=this.pluginGroup[n]._plugins;fk(i).forEach((function(e){"controls"!==e&&i[e]&&(r.push({plugin:i[e].func,options:i[e].__args}),t.unRegister(n,e))})),r.forEach((function(n){t.register(e,n.plugin,n.options)}))}},onPluginsReady:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var n=this.pluginGroup[t]._plugins||{};fk(n).forEach((function(e){n[e].onPluginsReady&&"function"==typeof n[e].onPluginsReady&&n[e].onPluginsReady()}))}},setCurrentUserActive:function(e,t){if(this.pluginGroup[e]){if(!t)return this.pluginGroup[e]._player.isUserActive=t,e;for(var n=fk(this.pluginGroup),r=0;r<n.length;r++){var i=this.pluginGroup[n[r]];i&&i._player&&(this.pluginGroup[n[r]]._player.isUserActive=!1)}return this.pluginGroup[e]._player.isUserActive=t,e}},getCurrentUseActiveId:function(){if(this.pluginGroup){for(var e=fk(this.pluginGroup),t=0;t<e.length;t++){var n=this.pluginGroup[e[t]];if(n&&n._player&&n._player.isUserActive)return e[t]}return null}},destroy:function(e){var t=e._pluginInfoId;if(this.pluginGroup[t]){var n,r;n=e.root,Xx.unObserver(n,r);for(var i=this.pluginGroup[t]._plugins,o=0,a=fk(i);o<a.length;o++){var s=a[o];this.unRegister(t,s)}var l=e.isUseActive;if(delete this.pluginGroup[t],delete e._pluginInfoId,l){var u=fk(this.pluginGroup);u.length>0&&this.setCurrentUserActive(u[u.length-1],!0)}}}},Qx="xgplayer",$x="xgplayer-is-enter",Zx="xgplayer-pause",eP="xgplayer-playing",tP="xgplayer-ended",nP="xgplayer-isloading",rP="xgplayer-is-error",iP="xgplayer-replay",oP="xgplayer-nostart",aP="xgplayer-inactive",sP="xgplayer-is-fullscreen",lP="xgplayer-is-cssfullscreen",uP="xgplayer-rotate-fullscreen",cP="xgplayer-rotate-parent",hP="xgplayer-fullscreen-parent",fP="xgplayer-fullscreen-inner",dP="no-controls",pP="flex-controls",vP="controls-autohide",gP="top-bar-autohide",yP="not-allow-autoplay",mP="seeking";function _P(){return{id:"",el:null,url:"",domEventType:"default",nullUrlStart:!1,width:600,height:337.5,fluid:!1,fitVideoSize:"fixed",videoFillMode:"auto",volume:.6,autoplay:!1,autoplayMuted:!1,loop:!1,isLive:!1,zoom:1,videoInit:!0,poster:"",isMobileSimulateMode:!1,defaultPlaybackRate:1,execBeforePluginsCall:null,allowSeekAfterEnded:!0,enableContextmenu:!0,closeVideoClick:!1,closeVideoDblclick:!1,closePlayerBlur:!1,closeDelayBlur:!1,leavePlayerTime:3e3,closePlayVideoFocus:!1,closePauseVideoFocus:!1,closeFocusVideoFocus:!0,closeControlsBlur:!0,topBarAutoHide:!0,videoAttributes:{},startTime:0,seekedStatus:"play",miniprogress:!1,disableSwipeHandler:function(){},enableSwipeHandler:function(){},preProcessUrl:null,ignores:[],whitelist:[],inactive:3e3,lang:EC(),controls:!0,marginControls:!1,fullscreenTarget:null,screenShot:!1,rotate:!1,pip:!1,download:!1,mini:!1,cssFullscreen:!0,keyShortcut:!0,presets:[],plugins:[],playbackRate:1,definition:{list:[]},playsinline:!0,customDuration:0,timeOffset:0,icons:{},i18n:[],tabindex:0,thumbnail:null,videoConfig:{},isHideTips:!1,minWaitDelay:200,commonStyle:{progressColor:"",playedColor:"",cachedColor:"",sliderBtnStyle:{},volumeColor:""}}}var bP=function(e){vT(n,e);var t=bT(n);function n(){var e,r;hT(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return pT(mT(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"onMouseEnter",(function(e){var t=mT(r),n=t.player;t.playerConfig.closeControlsBlur&&n.focus({autoHide:!1})})),pT(mT(r),"onMouseLeave",(function(e){mT(r).player.focus()})),r}return dT(n,[{key:"beforeCreate",value:function(e){e.config.mode||"mobile"!==xC.device||(e.config.mode="flex"),e.player.config.marginControls&&(e.config.autoHide=!1)}},{key:"afterCreate",value:function(){var e,t=this,n=this.config,r=n.disable,i=n.height,o=n.mode;if(!r){"flex"===o&&this.player.addClass(pP);var a={height:"".concat(i,"px")};Bw(e=fk(a)).call(e,(function(e){t.root.style[e]=a[e]})),this.left=qm(this).call(this,"xg-left-grid"),this.center=qm(this).call(this,"xg-center-grid"),this.right=qm(this).call(this,"xg-right-grid"),this.innerRoot=qm(this).call(this,"xg-inner-controls"),this.on(rx,(function(e){e?bC.addClass(t.root,"mini-controls"):bC.removeClass(t.root,"mini-controls")}));var s=this.playerConfig.isMobileSimulateMode;"mobile"!==xC.device&&"mobile"!==s&&(this.bind("mouseenter",this.onMouseEnter),this.bind("mouseleave",this.onMouseLeave))}}},{key:"focus",value:function(){this.player.focus({autoHide:!1})}},{key:"focusAwhile",value:function(){this.player.focus({autoHide:!0})}},{key:"blur",value:function(){this.player.blur({ignorePaused:!0})}},{key:"recoverAutoHide",value:function(){this.config.autoHide&&bC.addClass(this.root,vP)}},{key:"pauseAutoHide",value:function(){bC.removeClass(this.root,vP)}},{key:"show",value:function(){this.root.style.display="",this.player.focus()}},{key:"hide",value:function(){this.root.style.display="none"}},{key:"mode",get:function(){return this.config.mode}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;if(this.root){var i=e.defaultConfig||{};if(!t.root){switch(t.position?t.position:t.config&&t.config.position?t.config.position:i.position){case zx.CONTROLS_LEFT:t.root=this.left;break;case zx.CONTROLS_RIGHT:t.root=this.right;break;case zx.CONTROLS_CENTER:t.root=this.center;break;case zx.CONTROLS:t.root=this.root;break;default:t.root=this.left}return wT(gT(n.prototype),"registerPlugin",this).call(this,e,t,r)}}}},{key:"destroy",value:function(){"mobile"!==xC.device&&(this.unbind("mouseenter",this.onMouseEnter),this.unbind("mouseleave",this.onMouseLeave))}},{key:"render",value:function(){var e=this.config,t=e.mode,n=e.autoHide,r=e.initShow;if(!e.disable){var i=bC.classNames({"xgplayer-controls":!0},{"flex-controls":"flex"===t},{"bottom-controls":"bottom"===t},pT({},vP,n),{"xgplayer-controls-initshow":r||!n});return'<xg-controls class="'.concat(i,'" unselectable="on">\n    <xg-inner-controls class="xg-inner-controls xg-pos">\n      <xg-left-grid class="xg-left-grid">\n      </xg-left-grid>\n      <xg-center-grid class="xg-center-grid"></xg-center-grid>\n      <xg-right-grid class="xg-right-grid">\n      </xg-right-grid>\n    </xg-inner-controls>\n    </xg-controls>')}}}],[{key:"pluginName",get:function(){return"controls"}},{key:"defaultConfig",get:function(){return{disable:!1,autoHide:!0,mode:"",initShow:!1}}}]),n}(qx),wP={LANG:"en",TEXT:{ERROR_TYPES:{network:{code:1,msg:"video download error"},mse:{code:2,msg:"stream append error"},parse:{code:3,msg:"parsing error"},format:{code:4,msg:"wrong format"},decoder:{code:5,msg:"decoding error"},runtime:{code:6,msg:"grammatical errors"},timeout:{code:7,msg:"play timeout"},other:{code:8,msg:"other errors"}},HAVE_NOTHING:"There is no information on whether audio/video is ready",HAVE_METADATA:"Audio/video metadata is ready ",HAVE_CURRENT_DATA:"Data about the current play location is available, but there is not enough data to play the next frame/millisecond",HAVE_FUTURE_DATA:"Current and at least one frame of data is available",HAVE_ENOUGH_DATA:"The available data is sufficient to start playing",NETWORK_EMPTY:"Audio/video has not been initialized",NETWORK_IDLE:"Audio/video is active and has been selected for resources, but no network is used",NETWORK_LOADING:"The browser is downloading the data",NETWORK_NO_SOURCE:"No audio/video source was found",MEDIA_ERR_ABORTED:"The fetch process is aborted by the user",MEDIA_ERR_NETWORK:"An error occurred while downloading",MEDIA_ERR_DECODE:"An error occurred while decoding",MEDIA_ERR_SRC_NOT_SUPPORTED:"Audio/video is not supported",REPLAY:"Replay",ERROR:"Network is offline",PLAY_TIPS:"Play",PAUSE_TIPS:"Pause",PLAYNEXT_TIPS:"Play next",DOWNLOAD_TIPS:"Download",ROTATE_TIPS:"Rotate",RELOAD_TIPS:"Reload",FULLSCREEN_TIPS:"Fullscreen",EXITFULLSCREEN_TIPS:"Exit fullscreen",CSSFULLSCREEN_TIPS:"Cssfullscreen",EXITCSSFULLSCREEN_TIPS:"Exit cssfullscreen",TEXTTRACK:"Caption",PIP:"PIP",SCREENSHOT:"Screenshot",LIVE:"LIVE",OFF:"Off",OPEN:"Open",MINI_DRAG:"Click and hold to drag",MINISCREEN:"Miniscreen",REFRESH_TIPS:"Please Try",REFRESH:"Refresh",FORWARD:"forward",LIVE_TIP:"Live"}},kP={lang:{},langKeys:[],textKeys:[]};function EP(e,t){return fk(t).forEach((function(n){var r,i=bC.typeOf(t[n]),o=bC.typeOf(e[n]);"Array"===i?("Array"!==o&&(e[n]=[]),(r=e[n]).push.apply(r,kT(t[n]))):"Object"===i?("Object"!==o&&(e[n]={}),EP(e[n],t[n])):e[n]=t[n]})),e}function TP(){var e;Bw(e=fk(kP.lang.en)).call(e,(function(e){kP.textKeys[e]=e}))}function SP(e,t){var n=e.LANG;if(t||(t=kP),t.lang){var r=e.TEXT||{};"zh"===n&&(n="zh-cn"),t.lang[n]?EP(t.lang[n],r):(t.langKeys.push(n),t.lang[n]=r),TP()}}SP(wP);var CP={get textKeys(){return kP.textKeys},get langKeys(){return kP.langKeys},get lang(){var e,t={};return Bw(e=kP.langKeys).call(e,(function(e){t[e]=kP.lang[e]})),kP.lang["zh-cn"]&&(t.zh=kP.lang["zh-cn"]||{}),t},extend:function(e,t){var n=[];if(t||(t=kP),t.lang){var r;if("Array"!==bC.typeOf(e))n=Bw(r=fk(e)).call(r,(function(t){return{LANG:"zh"===t?"zh-cn":t,TEXT:e[t]}}));else n=e;var i=t.lang;Bw(n).call(n,(function(e){"zh"===e.LANG&&(e.LANG="zh-cn"),i[e.LANG]?EP(i[e.LANG]||{},e.TEXT||{}):SP(e,t)})),TP()}},use:SP,init:function(e){var t,n={lang:{},langKeys:[],textKeys:{},pId:e};return EP(n.lang,kP.lang),(t=n.langKeys).push.apply(t,kT(kP.langKeys)),EP(n.textKeys,kP.textKeys),n}},xP=1,PP=2,AP=3,IP=4,RP=5,OP=6,DP=7,MP=["ERROR","INITIAL","READY","ATTACHING","ATTACHED","NOTALLOW","RUNNING","ENDED","DESTROYED"],LP=["play","pause","replay","retry"],NP=0,UP=0,FP=function(e){vT(n,e);var t=bT(n);function n(e){var r;hT(this,n);var i,o=bC.deepMerge(_P(),e);pT(mT(r=t.call(this,o)),"canPlayFunc",(function(){if(r.config){var e=r.config,t=e.autoplay,n=e.startTime,i=e.defaultPlaybackRate;mC.logInfo("player","canPlayFunc, startTime",n),n&&(r.currentTime=n>r.duration?r.duration:n,r.config.startTime=0),r.playbackRate=i,(t||r._useAutoplay)&&r.mediaPlay(),r.off(BC,r.canPlayFunc),r.removeClass($x)}})),pT(mT(r),"onFullscreenChange",(function(e,t){var n=function(){bC.setTimeout(mT(r),(function(){r.resize()}),100)},i=bC.getFullScreenEl();r._fullActionFrom?r._fullActionFrom="":r.emit(fx,{eventType:"system",action:"switch_fullscreen",pluginName:"player",currentTime:r.currentTime,duration:r.duration,props:[{prop:"fullscreen",from:!0,to:!1}]});var o=function(e,t,n){if(e){var r=e.getAttribute(n);return!(!r||r!==t||"VIDEO"!==e.tagName&&"AUDIO"!==e.tagName)}}(i,r.playerId,Tx);if(t||i&&(i===r._fullscreenEl||o))n(),!r.config.closeFocusVideoFocus&&r.media.focus(),r.fullscreen=!0,r.changeFullStyle(r.root,i,sP),r.emit(tx,!0,r._fullScreenOffset),r.cssfullscreen&&r.exitCssFullscreen();else if(r.fullscreen){n();var a=mT(r),s=a._fullScreenOffset;a.config.needFullscreenScroll?(window.scrollTo(s.left,s.top),bC.setTimeout(mT(r),(function(){r.fullscreen=!1,r._fullScreenOffset=null}),100)):(!r.config.closeFocusVideoFocus&&r.media.focus(),r.fullscreen=!1,r._fullScreenOffset=null),r.cssfullscreen?r.removeClass(sP):r.recoverFullStyle(r.root,r._fullscreenEl,sP),r._fullscreenEl=null,r.emit(tx,!1)}})),pT(mT(r),"_onWebkitbeginfullscreen",(function(e){r._fullscreenEl=r.media,r.onFullscreenChange(e,!0)})),pT(mT(r),"_onWebkitendfullscreen",(function(e){r.onFullscreenChange(e,!1)})),Rx(mT(r),LP),r.config=o,r._pluginInfoId=bC.generateSessionId(),(i=mT(r)).logInfo=mC.logInfo.bind(i),i.logWarn=mC.logWarn.bind(i),i.logError=mC.logError.bind(i);var a=r.constructor.defaultPreset;if(r.config.presets.length){var s,l=hk(s=r.config.presets).call(s,"default");l>=0&&a&&(r.config.presets[l]=a)}else a&&r.config.presets.push(a);if(r.userTimer=null,r.waitTimer=null,r._state=xP,r.isError=!1,r._hasStart=!1,r.isSeeking=!1,r.isCanplay=!1,r._useAutoplay=!1,r.rotateDeg=0,r.isActive=!1,r.fullscreen=!1,r.cssfullscreen=!1,r.isRotateFullscreen=!1,r._fullscreenEl=null,r.timeSegments=[],r._cssfullscreenEl=null,r.curDefinition=null,r._orgCss="",r._fullScreenOffset=null,r._videoHeight=0,r._videoWidth=0,r.videoPos={pi:1,scale:0,rotate:-1,x:0,y:0,h:-1,w:-1,vy:0,vx:0},r.sizeInfo={width:0,height:0,left:0,top:0},r._accPlayed={t:0,acc:0,loopAcc:0},r._offsetInfo={currentTime:-1,duration:0},r.innerContainer=null,r.controls=null,r.topBar=null,r.root=null,r.__i18n=CP.init(r._pluginInfoId),xC.os.isAndroid&&xC.osVersion>0&&xC.osVersion<6&&(r.config.autoplay=!1),r.database=new bx,r.isUserActive=!1,r._onceSeekCanplay=null,r._isPauseBeforeSeek=0,r.innerStates={isActiveLocked:!1},!r._initDOM())return console.error(new Error("can't find the dom which id is ".concat(r.config.id," or this.config.el does not exist"))),_T(r);var u=r.config,c=u.definition,h=void 0===c?{}:c;if(!u.url&&h.list&&h.list.length>0){var f,d=qm(f=h.list).call(f,(function(e){return e.definition&&e.definition===h.defaultDefinition}));d||(h.defaultDefinition=h.list[0].definition,d=h.list[0]),r.config.url=d.url,r.curDefinition=d}return r._bindEvents(),r._registerPresets(),r._registerPlugins(),Jx.onPluginsReady(mT(r)),r.getInitDefinition(),r.setState(PP),bC.setTimeout(mT(r),(function(){r.emit(GC)}),0),r.onReady&&r.onReady(),(r.config.videoInit||r.config.autoplay)&&(!r.hasStart||r.state<IP)&&r.start(),r}return dT(n,[{key:"_initDOM",value:function(){var e,t,n,r=this;if(this.root=this.config.id?document.getElementById(this.config.id):null,!this.root){var i=this.config.el;if(!i||1!==i.nodeType)return this.emit(LC,new RC("use",this.config.vid,{line:32,handle:"Constructor",msg:"container id can't be empty"})),console.error("this.confg.id or this.config.el can't be empty"),!1;this.root=i}var o=Jx.checkPlayerRoot(this.root);o&&(mC.logWarn("The is an Player instance already exists in this.root, destroy it and reinitialize"),o.destroy()),this.root.setAttribute(Tx,this.playerId),Jx.init(this),this._initBaseDoms();var a=this.constructor.XgVideoProxy;if(a&&this.mediaConfig.mediaType===a.mediaType){var s=this.innerContainer||this.root;this.detachVideoEvents(this.media);var l=new a(s,this.config,this.mediaConfig);this.attachVideoEvents(l),this.media=l}if(this.media.setAttribute(Tx,this.playerId),this.config.controls){var u=this.config.controls.root||null,c=Jx.register(this,bP,{root:u});this.controls=c}var h="mobile"===this.config.isMobileSimulateMode?"mobile":xC.device;if(this.addClass(Fm(e=Fm(t=Fm(n="".concat(Qx," ")).call(n,aP," xgplayer-")).call(t,h," ")).call(e,this.config.controls?"":dP)),this.config.autoplay?this.addClass($x):this.addClass(oP),this.config.fluid){var f=this.config,d=f.width,p=f.height;"number"==typeof d&&"number"==typeof p||(d=600,p=337.5);var v={width:"100%",height:"0","max-width":"100%","padding-top":"".concat(100*p/d,"%")};fk(v).forEach((function(e){r.root.style[e]=v[e]}))}else["width","height"].forEach((function(e){r.config[e]&&("number"!=typeof r.config[e]?r.root.style[e]=r.config[e]:r.root.style[e]="".concat(r.config[e],"px"))}));var g=this.root.getBoundingClientRect(),y=g.width,m=g.height,_=g.left,b=g.top;return this.sizeInfo.width=y,this.sizeInfo.height=m,this.sizeInfo.left=_,this.sizeInfo.top=b,!0}},{key:"_initBaseDoms",value:function(){this.topBar=null,this.leftBar=null,this.rightBar=null,this.config.marginControls&&(this.innerContainer=bC.createDom("xg-video-container","",{"data-index":-1},"xg-video-container"),this.root.appendChild(this.innerContainer))}},{key:"_bindEvents",value:function(){var e=this;["focus","blur"].forEach((function(t){e.on(t,e["on"+t.charAt(0).toUpperCase()+dk(t).call(t,1)])})),wx.forEach((function(t){document&&document.addEventListener(t,e.onFullscreenChange)})),xC.os.isIos&&(this.media.addEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.addEventListener("webkitendfullscreen",this._onWebkitendfullscreen)),this.once(jC,this.resize),this.playFunc=function(){e.config.closeFocusVideoFocus||e.media.focus()},this.once(OC,this.playFunc)}},{key:"_unbindEvents",value:function(){var e=this;this.root.removeEventListener("mousemove",this.mousemoveFunc),wx.forEach((function(t){document.removeEventListener(t,e.onFullscreenChange)})),this.playFunc&&this.off(OC,this.playFunc),this.off(BC,this.canPlayFunc),this.media.removeEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.removeEventListener("webkitendfullscreen",this._onWebkitendfullscreen)}},{key:"_startInit",value:function(e){var t=this;if(this.media&&(e&&""!==e&&("Array"!==bC.typeOf(e)||0!==e.length)||(e="",this.emit(qC),mC.logWarn("config.url is null, please get url and run player._startInit(url)"),!this.config.nullUrlStart))){this._detachSourceEvents(this.media),"Array"===bC.typeOf(e)&&e.length>0?this._attachSourceEvents(this.media,e):this.media.src&&this.media.src===e?e||this.media.removeAttribute("src"):this.media.src=e,"Number"===bC.typeOf(this.config.volume)&&(this.volume=this.config.volume);var n=this.innerContainer?this.innerContainer:this.root;this.media instanceof window.Element&&!n.contains(this.media)&&n.insertBefore(this.media,n.firstChild);var r=this.media.readyState;mC.logInfo("_startInit readyState",r),this.config.autoplay&&(!bC.isMSE(this.media)&&this.load(),(xC.os.isIpad||xC.os.isPhone)&&this.mediaPlay()),r>=2?this.canPlayFunc():this.once(BC,this.canPlayFunc),(!this.hasStart||this.state<IP)&&Jx.afterInit(this),this.hasStart=!0,this.setState(IP),bC.setTimeout(this,(function(){t.emit(JC)}),0)}}},{key:"_registerPlugins",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this._loadingPlugins=[];var n=this.config.ignores||[],r=this.config.plugins||[],i=this.config.i18n||[];t&&CP.extend(i,this.__i18n);var o=n.join("||").toLowerCase().split("||"),a=this.plugins;r.forEach((function(n){try{var r=n.plugin?n.plugin.pluginName:n.pluginName;if(r&&hk(o).call(o,r.toLowerCase())>-1)return null;if(!t&&a[r.toLowerCase()])return;if(n.lazy&&n.loader){var i=Jx.lazyRegister(e,n);return void(n.forceBeforeInit&&(i.then((function(){var t,n;Mk(t=e._loadingPlugins).call(t,hk(n=e._loadingPlugins).call(n,i),1)})).catch((function(t){var n,r;mC.logError("_registerPlugins:loadingPlugin",t),Mk(n=e._loadingPlugins).call(n,hk(r=e._loadingPlugins).call(r,i),1)})),e._loadingPlugins.push(i)))}return e.registerPlugin(n)}catch(s){mC.logError("_registerPlugins:",s)}}))}},{key:"_registerPresets",value:function(){var e=this;this.config.presets.forEach((function(t){!function(e,t){var n,r,i,o=t.preset&&t.options?new t.preset(t.options,e.config):new t({},e.config),a=o.plugins,s=void 0===a?[]:a,l=o.ignores,u=void 0===l?[]:l,c=o.icons,h=void 0===c?{}:c,f=o.i18n,d=void 0===f?[]:f;e.config.plugins||(e.config.plugins=[]),e.config.ignores||(e.config.ignores=[]),(r=e.config.plugins).push.apply(r,kT(s)),(i=e.config.ignores).push.apply(i,kT(u)),Bw(n=fk(h)).call(n,(function(t){e.config.icons[t]||(e.config.icons[t]=h[t])}));var p=e.config.i18n||[];d.push.apply(d,kT(p)),e.config.i18n=d}(e,t)}))}},{key:"_getRootByPosition",value:function(e){var t=null;switch(e){case zx.ROOT_RIGHT:this.rightBar||(this.rightBar=bC.createPositionBar("xg-right-bar",this.root)),t=this.rightBar;break;case zx.ROOT_LEFT:this.leftBar||(this.leftBar=bC.createPositionBar("xg-left-bar",this.root)),t=this.leftBar;break;case zx.ROOT_TOP:this.topBar||(this.topBar=bC.createPositionBar("xg-top-bar",this.root),this.config.topBarAutoHide&&bC.addClass(this.topBar,gP)),t=this.topBar;break;default:t=this.innerContainer||this.root}return t}},{key:"registerPlugin",value:function(e,t){var n=Jx.formatPluginInfo(e,t),r=n.PLUFGIN,i=n.options,o=this.config.plugins;!Jx.checkPluginIfExits(r.pluginName,o)&&o.push(r);var a=Jx.getRootByConfig(r.pluginName,this.config);a.root&&(i.root=a.root),a.position&&(i.position=a.position);var s=i.position?i.position:i.config&&i.config.position||r.defaultConfig&&r.defaultConfig.position;return!i.root&&"string"==typeof s&&hk(s).call(s,"controls")>-1?this.controls&&this.controls.registerPlugin(r,i,r.pluginName):(i.root||(i.root=this._getRootByPosition(s)),Jx.register(this,r,i))}},{key:"deregister",value:function(e){"string"==typeof e?Jx.unRegister(this,e):e instanceof Lx&&Jx.unRegister(this,e.pluginName)}},{key:"unRegisterPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.deregister(e),t&&this.removePluginFromConfig(e)}},{key:"removePluginFromConfig",value:function(e){var t;if("string"==typeof e?t=e:e instanceof Lx&&(t=e.pluginName),t)for(var n=this.config.plugins.length-1;n>-1;n--){if(this.config.plugins[n].pluginName.toLowerCase()===t.toLowerCase()){var r;Mk(r=this.config.plugins).call(r,n,1);break}}}},{key:"plugins",get:function(){return Jx.getPlugins(this)}},{key:"getPlugin",value:function(e){var t=Jx.findPlugin(this,e);return t&&t.pluginName?t:null}},{key:"addClass",value:function(e){this.root&&(bC.hasClass(this.root,e)||bC.addClass(this.root,e))}},{key:"removeClass",value:function(e){this.root&&bC.removeClass(this.root,e)}},{key:"hasClass",value:function(e){if(this.root)return bC.hasClass(this.root,e)}},{key:"setAttribute",value:function(e,t){this.root&&this.root.setAttribute(e,t)}},{key:"removeAttribute",value:function(e,t){this.root&&this.root.removeAttribute(e,t)}},{key:"start",value:function(e){var t=this;if(!(this.state>AP))return e||this.config.url||this.getInitDefinition(),this.hasStart=!0,this.setState(AP),this._registerPlugins(!1),Jx.beforeInit(this).then((function(){if(t.config){e||(e=t.url||t.config.url);var n=t.preProcessUrl(e);return t._startInit(n.url)}})).catch((function(e){throw e.fileName="player",e.lineNumber="236",mC.logError("start:beforeInit:",e),e}))}},{key:"switchURL",value:function(e,t){var n=this,r=e;"Object"===bC.typeOf(e)&&(r=e.url),r=this.preProcessUrl(r).url;var i=this.currentTime,o=this.paused&&!this.isError;return this.src=r,new Vw((function(e,t){var a=function(e){n.off("timeupdate",s),n.off("canplay",s),t(e)},s=function(){n.currentTime=i,o&&n.pause(),n.off("error",a),e(!0)};n.once("error",a),r?(xC.os.isAndroid?n.once("timeupdate",s):n.once("canplay",s),n.play()):n.errorHandler("error",{code:6,message:"empty_src"})}))}},{key:"videoPlay",value:function(){this.mediaPlay()}},{key:"mediaPlay",value:function(){var e=this;if(!this.hasStart&&this.state<IP)return this.removeClass(oP),this.addClass($x),this.start(),void(this._useAutoplay=!0);this.state<OP&&(this.removeClass(oP),!this.isCanplay&&this.addClass($x));var t=wT(gT(n.prototype),"play",this).call(this);return void 0!==t&&t&&t.then?t.then((function(){e.removeClass(yP),e.addClass(eP),e.state<OP&&(mC.logInfo(">>>>playPromise.then"),e.setState(OP),e.emit(YC))})).catch((function(t){if(mC.logWarn(">>>>playPromise.catch",t.name),e.media&&e.media.error)return e.onError(),void e.removeClass($x);"NotAllowedError"===t.name&&(e._errorTimer=bC.setTimeout(e,(function(){e._errorTimer=null,e.emit(XC),e.addClass(yP),e.removeClass($x),e.pause(),e.setState(RP)}),0))})):(mC.logWarn("video.play not return promise"),this.state<OP&&(this.setState(OP),this.removeClass(yP),this.removeClass(oP),this.removeClass($x),this.addClass(eP),this.emit(YC))),t}},{key:"mediaPause",value:function(){wT(gT(n.prototype),"pause",this).call(this)}},{key:"videoPause",value:function(){wT(gT(n.prototype),"pause",this).call(this)}},{key:"play",value:function(){var e=this;return this.removeClass(Zx),Dx(this,"play",(function(){return e.mediaPlay()}))}},{key:"pause",value:function(){var e=this;Dx(this,"pause",(function(){wT(gT(n.prototype),"pause",e).call(e)}))}},{key:"seek",value:function(e,t){var n=this;if(this.media&&!Lk(Number(e))&&this.hasStart){var r=this.config,i=r.isSeekedPlay,o=r.seekedStatus,a=t||(i?"play":o);e=e<0?0:e>this.duration?rE(this.duration,10):e,!this._isPauseBeforeSeek&&(this._isPauseBeforeSeek=this.paused?2:1),this._onceSeekCanplay&&this.off(NC,this._onceSeekCanplay),this._onceSeekCanplay=function(){switch(n.removeClass($x),n.isSeeking=!1,a){case"play":n.play();break;case"pause":n.pause();break;default:n._isPauseBeforeSeek>1||n.paused?n.pause():n.play()}n._isPauseBeforeSeek=0,n._onceSeekCanplay=null},this.once(NC,this._onceSeekCanplay),this.state<OP?(this.removeClass(oP),this.currentTime=e,this.play()):this.currentTime=e}}},{key:"getInitDefinition",value:function(){var e,t=this,n=this.config,r=n.definition;!n.url&&r&&r.list&&r.list.length>0&&r.defaultDefinition&&Bw(e=r.list).call(e,(function(e){e.definition===r.defaultDefinition&&(t.config.url=e.url,t.curDefinition=e)}))}},{key:"changeDefinition",value:function(e,t){var n=this,r=this.config.definition;if(Array.isArray(null==r?void 0:r.list)&&r.list.forEach((function(t){(null==e?void 0:e.definition)===t.definition&&(n.curDefinition=t)})),null!=e&&e.bitrate&&"number"!=typeof e.bitrate&&(e.bitrate=rE(e.bitrate,10)||0),this.emit(ix,{from:t,to:e}),this.hasStart){var i=this.switchURL(e.url,uT({seamless:!1!==r.seamless&&"undefined"!=typeof MediaSource&&"function"==typeof MediaSource.isTypeSupported},e));i&&i.then?i.then((function(){n.emit(ax,{from:t,to:e})})):this.emit(ax,{from:t,to:e})}else this.config.url=e.url}},{key:"reload",value:function(){this.load(),this.reloadFunc=function(){this.play()},this.once(jC,this.reloadFunc)}},{key:"resetState",value:function(){var e=this,t=[yP,eP,oP,Zx,iP,$x,tP,rP,nP];this.hasStart=!1,this.isError=!1,this._useAutoplay=!1,this.mediaPause(),this._accPlayed.acc=0,this._accPlayed.t=0,this._accPlayed.loopAcc=0,t.forEach((function(t){e.removeClass(t)})),this.addClass($x),this.emit(dx)}},{key:"reset",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0;if(this.resetState(),this.plugins&&(Bw(t).call(t,(function(t){e.deregister(t)})),n)){var r,i=_P();hE(r=fk(this.config)).call(r,(function(t){"undefined"===e.config[t]||"plugins"!==t&&"presets"!==t&&"el"!==t&&"id"!==t||(e.config[t]=i[t])}))}}},{key:"destroy",value:function(){var e,t=this,r=this.innerContainer,i=this.root,o=this.media;if(i&&o){if(this.hasStart=!1,this._useAutoplay=!1,i.removeAttribute(Tx),this.updateAcc("destroy"),this._unbindEvents(),this._detachSourceEvents(this.media),bC.clearAllTimers(this),this.emit($C),Jx.destroy(this),Ox(this),wT(gT(n.prototype),"destroy",this).call(this),this.fullscreen&&this._fullscreenEl===this.root&&this.exitFullscreen(),r)for(var a=r.children,s=0;s<a.length;s++)r.removeChild(a[s]);!r&&o instanceof window.Node&&i.contains(o)&&i.removeChild(o),Bw(e=["topBar","leftBar","rightBar","innerContainer"]).call(e,(function(e){t[e]&&i.removeChild(t[e]),t[e]=null}));var l=i.className.split(" ");l.length>0?i.className=Hw(l).call(l,(function(e){return hk(e).call(e,"xgplayer")<0})).join(" "):i.className="",this.removeAttribute("data-xgfill"),["isSeeking","isCanplay","isActive","cssfullscreen","fullscreen"].forEach((function(e){t[e]=!1}))}}},{key:"replay",value:function(){var e=this;this.removeClass(tP),this.currentTime=0,this.isSeeking=!1,Dx(this,"replay",(function(){e.once(NC,(function(){var t=e.mediaPlay();t&&t.catch&&t.catch((function(e){console.log(e)}))})),e.play(),e.emit(QC),e.onPlay()}))}},{key:"retry",value:function(){var e=this;this.removeClass(rP),this.addClass(nP),Dx(this,"retry",(function(){var t=e.currentTime,n=e.config.url,r=bC.isMSE(e.media)?{url:n}:e.preProcessUrl(n);e.src=r.url,!e.config.isLive&&(e.currentTime=t),e.once(BC,(function(){e.mediaPlay()}))}))}},{key:"changeFullStyle",value:function(e,t,n,r){e&&(r||(r=hP),this._orgCss||(this._orgCss=bC.filterStyleFromText(e)),bC.addClass(e,n),t&&t!==e&&!this._orgPCss&&(this._orgPCss=bC.filterStyleFromText(t),bC.addClass(t,r),t.setAttribute(Tx,this.playerId)))}},{key:"recoverFullStyle",value:function(e,t,n,r){r||(r=hP),this._orgCss&&(bC.setStyleFromCsstext(e,this._orgCss),this._orgCss=""),bC.removeClass(e,n),t&&t!==e&&this._orgPCss&&(bC.setStyleFromCsstext(t,this._orgPCss),this._orgPCss="",bC.removeClass(t,r),t.removeAttribute(Tx))}},{key:"getFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget,t=this.root,n=this.media;if(e||(e=t),this._fullScreenOffset={top:bC.scrollTop(),left:bC.scrollLeft()},this._fullscreenEl=e,this._fullActionFrom="get",bC.getFullScreenEl()===this._fullscreenEl)return this.onFullscreenChange(),Vw.resolve();try{for(var r=0;r<kx.length;r++){var i=kx[r];if(e[i]){var o="webkitRequestFullscreen"===i?e.webkitRequestFullscreen(window.Element.ALLOW_KEYBOARD_INPUT):e[i]();return o&&o.then?o:Vw.resolve()}}return n.fullscreenEnabled||n.webkitSupportsFullscreen?(n.webkitEnterFullscreen(),Vw.resolve()):Vw.reject(new Error("call getFullscreen fail"))}catch(a){return Vw.reject(new Error("call getFullscreen fail"))}}},{key:"exitFullscreen",value:function(e){if(this.isRotateFullscreen&&this.exitRotateFullscreen(),this._fullscreenEl||bC.getFullScreenEl()){this.root;var t=this.media;this._fullActionFrom="exit";try{for(var n=0;n<Ex.length;n++){var r=Ex[n];if(document[r]){var i=document[r]();return i&&i.then?i:Vw.resolve()}}return t&&t.webkitSupportsFullscreen?(t.webkitExitFullScreen(),Vw.resolve()):Vw.reject(new Error("call exitFullscreen fail"))}catch(o){return Vw.reject(new Error("call exitFullscreen fail"))}}}},{key:"getCssFullscreen",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.isRotateFullscreen?this.exitRotateFullscreen():this.fullscreen&&this.exitFullscreen();var n=t?Fm(e="".concat(fP," ")).call(e,lP):lP;this.changeFullStyle(this.root,t,n);var r=this.config.fullscreen,i=void 0===r?{}:r;(!0===i.useCssFullscreen||"function"==typeof i.useCssFullscreen&&i.useCssFullscreen())&&(this.fullscreen=!0,this.emit(tx,!0)),this._cssfullscreenEl=t,this.cssfullscreen=!0,this.emit(nx,!0)}},{key:"exitCssFullscreen",value:function(){var e,t=this._cssfullscreenEl?Fm(e="".concat(fP," ")).call(e,lP):lP;if(this.fullscreen){var n=this.config.fullscreen,r=void 0===n?{}:n;!0===r.useCssFullscreen||"function"==typeof r.useCssFullscreen&&r.useCssFullscreen()?(this.recoverFullStyle(this.root,this._cssfullscreenEl,t),this.fullscreen=!1,this.emit(tx,!1)):this.removeClass(t)}else this.recoverFullStyle(this.root,this._cssfullscreenEl,t);this._cssfullscreenEl=null,this.cssfullscreen=!1,this.emit(nx,!1)}},{key:"getRotateFullscreen",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.cssfullscreen&&this.exitCssFullscreen(t);var n=t?Fm(e="".concat(fP," ")).call(e,uP):uP;this._fullscreenEl=t||this.root,this.changeFullStyle(this.root,t,n,cP),this.isRotateFullscreen=!0,this.fullscreen=!0,this.setRotateDeg(90),this._rootStyle=this.root.getAttribute("style"),this.root.style.width="".concat(window.innerHeight,"px"),this.emit(tx,!0)}},{key:"exitRotateFullscreen",value:function(e){var t,n=this._fullscreenEl!==this.root?Fm(t="".concat(fP," ")).call(t,uP):uP;this.recoverFullStyle(this.root,this._fullscreenEl,n,cP),this.isRotateFullscreen=!1,this.fullscreen=!1,this.setRotateDeg(0),this.emit(tx,!1),this._rootStyle&&(this.root.style.style=this._rootStyle,this._rootStyle=!1)}},{key:"setRotateDeg",value:function(e){90===window.orientation||-90===window.orientation?this.rotateDeg=0:this.rotateDeg=e}},{key:"focus",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!this.config.closeDelayBlur,delay:this.config.inactive};this.isActive?this.onFocus(e):this.emit(KC,uT({paused:this.paused,ended:this.ended},e))}},{key:"blur",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{ignorePaused:!1};this.isActive?this.emit(WC,uT({paused:this.paused,ended:this.ended},e)):this.onBlur(e)}},{key:"onFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!0,delay:3e3},n=this.innerStates;if(this.isActive=!0,this.removeClass(aP),this.userTimer&&(bC.clearTimeout(this,this.userTimer),this.userTimer=null),void 0!==t.isLock&&(n.isActiveLocked=t.isLock),!1===t.autoHide||!0===t.isLock||n.isActiveLocked)this.userTimer&&(bC.clearTimeout(this,this.userTimer),this.userTimer=null);else{var r=t&&t.delay?t.delay:this.config.inactive;this.userTimer=bC.setTimeout(this,(function(){e.userTimer=null,e.blur()}),r)}}},{key:"onBlur",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).ignorePaused,t=void 0!==e&&e;if(this.isActive&&!this.innerStates.isActiveLocked){var n=this.config.closePauseVideoFocus;this.isActive=!1,(t||n||!this.paused&&!this.ended)&&this.addClass(aP)}}},{key:"onEmptied",value:function(){this.updateAcc("emptied")}},{key:"onCanplay",value:function(){this.removeClass($x),this.removeClass(rP),this.removeClass(nP),this.isCanplay=!0,this.waitTimer&&bC.clearTimeout(this,this.waitTimer)}},{key:"onLoadeddata",value:function(){this.isError=!1,this.isSeeking=!1}},{key:"onLoadstart",value:function(){this.removeClass(rP),this.isCanplay=!1}},{key:"onPlay",value:function(){this.state===DP&&this.setState(OP),this.removeClass(Zx),this.ended&&this.removeClass(tP),!this.config.closePlayVideoFocus&&this.focus()}},{key:"onPause",value:function(){this.addClass(Zx),this.updateAcc("pause"),this.config.closePauseVideoFocus||(this.userTimer&&(bC.clearTimeout(this,this.userTimer),this.userTimer=null),this.focus())}},{key:"onEnded",value:function(){this.updateAcc("ended"),this.addClass(tP),this.setState(DP)}},{key:"onError",value:function(){this.isError=!0,this.updateAcc("error"),this.removeClass(yP),this.removeClass(oP),this.removeClass($x),this.removeClass(nP),this.addClass(rP)}},{key:"onSeeking",value:function(){this.isSeeking||this.updateAcc("seeking"),this.isSeeking=!0,this.addClass(mP)}},{key:"onSeeked",value:function(){this.isSeeking=!1,this.waitTimer&&bC.clearTimeout(this,this.waitTimer),this.removeClass(nP),this.removeClass(mP)}},{key:"onWaiting",value:function(){var e=this;this.waitTimer&&bC.clearTimeout(this,this.waitTimer),this.updateAcc("waiting"),this.waitTimer=bC.setTimeout(this,(function(){e.addClass(nP),bC.clearTimeout(e,e.waitTimer),e.waitTimer=null}),this.config.minWaitDelay)}},{key:"onPlaying",value:function(){var e=this;this.isError=!1,[oP,Zx,tP,rP,iP,nP].forEach((function(t){e.removeClass(t)}))}},{key:"onTimeupdate",value:function(){!this._videoHeight&&this.media.videoHeight&&this.resize(),(this.waitTimer||this.hasClass(nP))&&this.media.readyState>2&&(this.removeClass(nP),bC.clearTimeout(this,this.waitTimer),this.waitTimer=null),!this.paused&&this.state<OP&&this.duration&&(this.setState(OP),this.emit(YC)),this._accPlayed.t||this.paused||this.ended||(this._accPlayed.t=(new Date).getTime())}},{key:"onVolumechange",value:function(){"Number"===bC.typeOf(this.config.volume)&&(this.config.volume=this.volume)}},{key:"onRatechange",value:function(){this.config.defaultPlaybackRate=this.playbackRate}},{key:"emitUserAction",value:function(e,t,n){if(this.media&&t&&e){var r="String"===bC.typeOf(e)?e:e.type||"";n.props&&"Array"!==bC.typeOf(n.props)&&(n.props=[n.props]),this.emit(fx,uT({eventType:r,action:t,currentTime:this.currentTime,duration:this.duration,ended:this.ended,event:e},n))}}},{key:"updateAcc",value:function(e){if(this._accPlayed.t){var t=(new Date).getTime()-this._accPlayed.t;this._accPlayed.acc+=t,this._accPlayed.t=0,("ended"===e||this.ended)&&(this._accPlayed.loopAcc=this._accPlayed.acc)}}},{key:"checkBuffer",value:function(e){var t=this.media.buffered;if(!t||0===t.length||!this.duration)return!0;for(var n=e||this.media.currentTime||.2,r=t.length,i=0;i<r;i++)if(t.start(i)<=n&&t.end(i)>n)return!0;return!1}},{key:"resizePosition",value:function(){var e=this.videoPos,t=e.rotate,n=e.vy,r=e.vx,i=e.h,o=e.w;if(!(t<0)||n||r){var a=this.videoPos._pi;if(!a&&this.media.videoHeight&&(a=this.media.videoWidth/this.media.videoHeight*100),a){this.videoPos.pi=a;var s={rotate:t},l=0,u=0,c=1,h=Math.abs(t/90),f=this.root,d=this.innerContainer,p=f.offsetWidth,v=d?d.offsetHeight:f.offsetHeight,g=v,y=p;if(h%2==0)c=i>0?100/i:o>0?100/o:1,s.scale=c,l=n>0?(100-i)/2-n:0,s.y=2===h?0-l:l,u=r>0?(100-o)/2-r:0,s.x=2===h?0-u:u,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(g,"px");else if(h%2==1){g=p;var m=v-p;u=-m/2/(y=v)*100,s.x=3===h?u+n/2:u-n/2,l=m/2/g*100,s.y=3===h?l+r/2:l-r/2,s.scale=c,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(g,"px")}var _=bC.getTransformStyle(s);this.media.style.transform=_,this.media.style.webkitTransform=_}}}},{key:"position",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{h:0,y:0,x:0,w:0};if(this.media&&e&&e.h){var t=this.videoPos;t.h=100*e.h||0,t.w=100*e.w||0,t.vx=100*e.x||0,t.vy=100*e.y||0,this.resizePosition()}}},{key:"setConfig",value:function(e){var t,n=this;e&&Bw(t=fk(e)).call(t,(function(t){if("plugins"!==t){n.config[t]=e[t];var r=n.plugins[t.toLowerCase()];r&&"Function"===bC.typeOf(r.setConfig)&&r.setConfig(e[t])}}))}},{key:"playNext",value:function(e){var t=this;this.resetState(),this.setConfig(e),this._currentTime=0,this._duration=0,Dx(this,"playnext",(function(){t.start(),t.emit(cx,e)}))}},{key:"resize",value:function(){var e=this;if(this.media){var t=this.root.getBoundingClientRect();this.sizeInfo.width=t.width,this.sizeInfo.height=t.height,this.sizeInfo.left=t.left,this.sizeInfo.top=t.top;var n=this.media,r=n.videoWidth,i=n.videoHeight,o=this.config,a=o.fitVideoSize,s=o.videoFillMode;if("fill"!==s&&"cover"!==s&&"contain"!==s||this.setAttribute("data-xgfill",s),i&&r){this._videoHeight=i,this._videoWidth=r;var l=this.controls&&this.innerContainer?this.controls.root.getBoundingClientRect().height:0,u=t.width,c=t.height-l,h=rE(r/i*1e3,10),f=rE(u/c*1e3,10),d=u,p=c,v={};"auto"===a&&f>h||"fixWidth"===a?(p=u/h*1e3,this.config.fluid?v.paddingTop="".concat(100*p/d,"%"):v.height="".concat(p+l,"px")):("auto"===a&&f<h||"fixHeight"===a)&&(d=h*c/1e3,v.width="".concat(d,"px")),this.fullscreen||this.cssfullscreen||fk(v).forEach((function(t){e.root.style[t]=v[t]})),("fillHeight"===s&&f<h||"fillWidth"===s&&f>h)&&this.setAttribute("data-xgfill","cover");var g={videoScale:h,vWidth:d,vHeight:p,cWidth:d,cHeight:p+l};this.resizePosition(),this.emit(lx,g)}}}},{key:"updateObjectPosition",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.media.updateObjectPosition?this.media.updateObjectPosition(t,n):this.media.style.objectPosition=Fm(e="".concat(100*t,"% ")).call(e,100*n,"%")}},{key:"setState",value:function(e){var t;mC.logInfo("setState",Fm(t="state from:".concat(MP[this.state]," to:")).call(t,MP[e])),this._state=e}},{key:"preProcessUrl",value:function(e,t){var n=this.config.preProcessUrl;return!bC.isBlob(e)&&n&&"function"==typeof n?n(e,t):{url:e}}},{key:"state",get:function(){return this._state}},{key:"isFullscreen",get:function(){return this.fullscreen}},{key:"isCssfullScreen",get:function(){return this.cssfullscreen}},{key:"hasStart",get:function(){return this._hasStart},set:function(e){"boolean"==typeof e&&(this._hasStart=e,!1===e&&this.setState(PP),this.emit("hasstart"))}},{key:"isPlaying",get:function(){return this._state===OP||this._state===DP},set:function(e){e?this.setState(OP):this._state>=OP&&this.setState(IP)}},{key:"definitionList",get:function(){return this.config&&this.config.definition&&this.config.definition.list||[]},set:function(e){var t=this,n=this.config.definition,r=null,i=null;n.list=e,this.emit("resourceReady",e),e.forEach((function(e){var o;(null===(o=t.curDefinition)||void 0===o?void 0:o.definition)===e.definition&&(r=e),n.defaultDefinition===e.definition&&(i=e)})),!i&&e.length>0&&(i=e[0]),r?this.changeDefinition(r):i&&this.changeDefinition(i)}},{key:"videoFrameInfo",get:function(){var e={total:0,dropped:0,corrupted:0,droppedRate:0,droppedDuration:0};if(!this.media||!this.media.getVideoPlaybackQuality)return e;var t=this.media.getVideoPlaybackQuality();return e.dropped=t.droppedVideoFrames||0,e.total=t.totalVideoFrames||0,e.corrupted=t.corruptedVideoFrames||0,e.total>0&&(e.droppedRate=e.dropped/e.total*100,e.droppedDuration=rE(this.cumulateTime/e.total*e.dropped,0)),e}},{key:"lang",get:function(){return this.config.lang},set:function(e){var t,n;0!==Hw(t=CP.langKeys).call(t,(function(t){return t===e})).length||"zh"===e?(this.config.lang=e,Jx.setLang(e,this)):console.error(Fm(n="Sorry, set lang fail, because the language [".concat(e,"] is not supported now, list of all supported languages is [")).call(n,CP.langKeys.join(),"] "))}},{key:"i18n",get:function(){var e=this.config.lang;return"zh"===e&&(e="zh-cn"),this.__i18n.lang[e]||this.__i18n.lang.en}},{key:"i18nKeys",get:function(){return this.__i18n.textKeys||{}}},{key:"version",get:function(){return PC}},{key:"playerId",get:function(){return this._pluginInfoId}},{key:"url",get:function(){return this.__url||this.config.url},set:function(e){this.__url=e}},{key:"poster",get:function(){return this.plugins.poster?this.plugins.poster.config.poster:this.config.poster},set:function(e){this.plugins.poster&&this.plugins.poster.update(e)}},{key:"readyState",get:function(){return wT(gT(n.prototype),"readyState",this)}},{key:"error",get:function(){var e=wT(gT(n.prototype),"error",this);return this.i18n[e]||e}},{key:"networkState",get:function(){return wT(gT(n.prototype),"networkState",this)}},{key:"fullscreenChanging",get:function(){return!(null===this._fullScreenOffset)}},{key:"cumulateTime",get:function(){var e=this._accPlayed,t=e.acc,n=e.t;return n?(new Date).getTime()-n+t:t}},{key:"zoom",get:function(){return this.config.zoom},set:function(e){this.config.zoom=e}},{key:"videoRotateDeg",get:function(){return this.videoPos.rotate},set:function(e){(e=bC.convertDeg(e))%90==0&&e!==this.videoPos.rotate&&(this.videoPos.rotate=e,this.resizePosition())}},{key:"avgSpeed",get:function(){return UP},set:function(e){UP=e}},{key:"realTimeSpeed",get:function(){return NP},set:function(e){NP=e}},{key:"offsetCurrentTime",get:function(){return this._offsetInfo.currentTime||0},set:function(e){this._offsetInfo.currentTime=e}},{key:"offsetDuration",get:function(){return this._offsetInfo.duration||0},set:function(e){this._offsetInfo.duration=e||0}},{key:"hook",value:function(e,t){var n;return Cx.call.apply(Cx,Fm(n=[this]).call(n,dk(Array.prototype).call(arguments)))}},{key:"useHooks",value:function(e,t){var n;return xx.call.apply(xx,Fm(n=[this]).call(n,dk(Array.prototype).call(arguments)))}},{key:"removeHooks",value:function(e,t){var n;return Px.call.apply(Px,Fm(n=[this]).call(n,dk(Array.prototype).call(arguments)))}},{key:"usePluginHooks",value:function(e,t,n){for(var r,i=arguments.length,o=new Array(i>3?i-3:0),a=3;a<i;a++)o[a-3]=arguments[a];return Ax.call.apply(Ax,Fm(r=[this]).call(r,dk(Array.prototype).call(arguments)))}},{key:"removePluginHooks",value:function(e,t,n){for(var r,i=arguments.length,o=new Array(i>3?i-3:0),a=3;a<i;a++)o[a-3]=arguments[a];return Ix.call.apply(Ix,Fm(r=[this]).call(r,dk(Array.prototype).call(arguments)))}},{key:"setUserActive",value:function(e,t){"boolean"==typeof t&&t!==this.muted&&(this.addInnerOP("volumechange"),this.muted=t),Jx.setCurrentUserActive(this.playerId,e)}}],[{key:"debugger",get:function(){return mC.config.debug},set:function(e){mC.config.debug=e}},{key:"getCurrentUserActivePlayerId",value:function(){return Jx.getCurrentUseActiveId()}},{key:"setCurrentUserActive",value:function(e,t){Jx.setCurrentUserActive(e,t)}},{key:"isHevcSupported",value:function(){return xC.isHevcSupported()}},{key:"probeConfigSupported",value:function(e){return xC.probeConfigSupported(e)}},{key:"install",value:function(e,t){n.plugins||(n.plugins={}),n.plugins[e]||(n.plugins[e]=t)}},{key:"use",value:function(e,t){n.plugins||(n.plugins={}),n.plugins[e]=t}}]),n}(_x);pT(FP,"defaultPreset",null),pT(FP,"XgVideoProxy",null);function BP(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var VP=RE;On({target:"Function",proto:!0,forced:Function.bind!==VP},{bind:VP});var HP,jP,zP=Gc("Function").bind,KP=ce,WP=zP,GP=Function.prototype,qP=o((function(e){var t=e.bind;return e===GP||KP(GP,e)&&t===GP.bind?WP:t}));function YP(e,t){var n;return(YP=gh?qP(n=gh).call(n):function(e,t){return e.__proto__=t,e})(e,t)}function XP(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Lc(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Dc(e,"prototype",{writable:!1}),t&&YP(e,t)}function JP(e){var t;return(JP=gh?qP(t=Vc).call(t):function(e){return e.__proto__||Vc(e)})(e)}function QP(){if("undefined"==typeof Reflect||!qE)return!1;if(qE.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(qE(Boolean,[],(function(){}))),!0}catch(AV){return!1}}function $P(e){var t=QP();return function(){var n,r=JP(e);if(t){var i=JP(this).constructor;n=qE(r,arguments,i)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===Sc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return BP(e)}(this,n)}}function ZP(e,t,n){var r;QP()?ZP=qP(r=qE).call(r):ZP=function(e,t,n){var r=[null];ph(r).apply(r,t);var i=new(qP(Function).apply(e,r));return n&&YP(i,n.prototype),i};return ZP.apply(null,arguments)}function eA(e){var t="function"==typeof Ow?new Ow:void 0;return eA=function(e){if(null===e||(n=e,-1===Xy(r=Function.toString.call(n)).call(r,"[native code]")))return e;var n,r;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,i)}function i(){return ZP(e,arguments,JP(this).constructor)}return i.prototype=Lc(e.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),YP(i,e)},eA(e)}var tA=function(e){return e.Fatal="Fatal",e.Error="Error",e}(tA||{}),nA=function(e){return e[e.MANIFEST_HLS_ERROR=1100]="MANIFEST_HLS_ERROR",e[e.MANIFEST_DASH_ERROR=1200]="MANIFEST_DASH_ERROR",e[e.NETWORK=2100]="NETWORK",e[e.NETWORK_TIMEOUT=2101]="NETWORK_TIMEOUT",e[e.NETWORK_FORBIDDEN=2103]="NETWORK_FORBIDDEN",e[e.NETWORK_NOTFOUND=2104]="NETWORK_NOTFOUND",e[e.NETWORK_RANGE_NOT_SATISFIABLE=2116]="NETWORK_RANGE_NOT_SATISFIABLE",e[e.DEMUX_FLV_ERROR=3100]="DEMUX_FLV_ERROR",e[e.DEMUX_HLS_ERROR=3200]="DEMUX_HLS_ERROR",e[e.DEMUX_MP4_ERROR=3300]="DEMUX_MP4_ERROR",e[e.DEMUX_FMP4_ERROR=3400]="DEMUX_FMP4_ERROR",e[e.DEMUX_SIDX_ERROR=3410]="DEMUX_SIDX_ERROR",e[e.REMUX_FMP4_ERROR=4100]="REMUX_FMP4_ERROR",e[e.REMUX_MP4_ERROR=4200]="REMUX_MP4_ERROR",e[e.MEDIA_ERR_ABORTED=5101]="MEDIA_ERR_ABORTED",e[e.MEDIA_ERR_NETWORK=5102]="MEDIA_ERR_NETWORK",e[e.MEDIA_ERR_DECODE=5103]="MEDIA_ERR_DECODE",e[e.MEDIA_ERR_SRC_NOT_SUPPORTED=5104]="MEDIA_ERR_SRC_NOT_SUPPORTED",e[e.MEDIA_ERR_CODEC_NOT_SUPPORTED=5105]="MEDIA_ERR_CODEC_NOT_SUPPORTED",e[e.MEDIA_ERR_URL_EMPTY=5106]="MEDIA_ERR_URL_EMPTY",e[e.MEDIA_MSE_ADD_SB=5200]="MEDIA_MSE_ADD_SB",e[e.MEDIA_MSE_APPEND_BUFFER=5201]="MEDIA_MSE_APPEND_BUFFER",e[e.MEDIA_MSE_OTHER=5202]="MEDIA_MSE_OTHER",e[e.MEDIA_MSE_FULL=5203]="MEDIA_MSE_FULL",e[e.MEDIA_MSE_HIJACK=5204]="MEDIA_MSE_HIJACK",e[e.MEDIA_EME_HIJACK=5301]="MEDIA_EME_HIJACK",e[e.DRM_LICENSE=7100]="DRM_LICENSE",e[e.DRM_CUSTOM_LICENSE=7200]="DRM_CUSTOM_LICENSE",e[e.OTHER=8e3]="OTHER",e[e.RUNTIME_NO_CANPLAY_ERROR=9001]="RUNTIME_NO_CANPLAY_ERROR",e[e.RUNTIME_BUFFERBREAK_ERROR=9002]="RUNTIME_BUFFERBREAK_ERROR",e[e.RUNTIME_BWAITING_TIMEOUT_ERROR=9002]="RUNTIME_BWAITING_TIMEOUT_ERROR",e[e.MODULE_LOAD_ERROR=110]="MODULE_LOAD_ERROR",e.UNKNOWN="UNKNOWN",e}(nA||{}),rA=(Ny(HP={},2100,{messageTextKey:"NETWORK",level:"Error"}),Ny(HP,2101,{messageTextKey:"NETWORK_TIMEOUT",level:"Error"}),Ny(HP,2103,{messageTextKey:"NETWORK_FORBIDDEN",level:"Error"}),Ny(HP,2104,{messageTextKey:"NETWORK_NOTFOUND",level:"Error"}),Ny(HP,5101,{messageTextKey:"MEDIA_ERR_ABORTED",level:"Error"}),Ny(HP,5102,{messageTextKey:"MEDIA_ERR_NETWORK",level:"Error"}),Ny(HP,5103,{messageTextKey:"MEDIA_ERR_DECODE",level:"Error"}),Ny(HP,5104,{messageTextKey:"MEDIA_ERR_SRC_NOT_SUPPORTED",level:"Error"}),Ny(HP,5105,{messageTextKey:"MEDIA_ERR_CODEC_NOT_SUPPORTED",level:"Error"}),Ny(HP,5106,{messageTextKey:"MEDIA_ERR_URL_EMPTY",level:"Error"}),Ny(HP,110,{messageTextKey:"MODULE_LOAD_ERROR",level:"Fatal"}),HP),iA=(Ny(jP={},"manifest",{messageTextKey:"MANIFEST",level:"Error"}),Ny(jP,"network",{messageTextKey:"NETWORK",level:"Error"}),Ny(jP,"demux",{messageTextKey:"DEMUX",level:"Error"}),Ny(jP,"remux",{messageTextKey:"REMUX",level:"Error"}),Ny(jP,"media",{messageTextKey:"MEDIA",level:"Error"}),Ny(jP,"drm",{messageTextKey:"DRM",level:"Error"}),Ny(jP,"other",{messageTextKey:"OTHER",level:"Error"}),Ny(jP,"runtime",{messageTextKey:"RUNTIME",level:"Error"}),jP),oA=function(e){XP(n,e);var t=$P(n);function n(e,r){var i,o,a,s,l,u,c;Iy(this,n);var h="string"==typeof e?{message:e}:e,f=null!==(i=null!==(o=null!==(a=null==r?void 0:r.getText(null==h?void 0:h.messageTextKey))&&void 0!==a?a:null==h?void 0:h.message)&&void 0!==o?o:null==h||null===(s=h.error)||void 0===s?void 0:s.message)&&void 0!==i?i:null==r?void 0:r.getText("UNKNOWN");return Ny(BP(c=t.call(this,f)),"errorCode",void 0),Ny(BP(c),"level",void 0),Ny(BP(c),"ext",void 0),c.errorCode=null!==(l=null==h?void 0:h.errorCode)&&void 0!==l?l:"UNKNOWN",c.level=null!==(u=null==h?void 0:h.level)&&void 0!==u?u:"Error",c.ext=null==h?void 0:h.ext,c}return Ly(n)}(eA(Error));function aA(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;return new oA(Dm(Dm({errorCode:e},rA[e]),t),n)}function sA(e,t){var n,r,i=rA[e.errorCode]||iA[e.errorType],o=e.errorCode;return new oA({messageTextKey:null==i?void 0:i.messageTextKey,message:null!==(n=null==e?void 0:e.message)&&void 0!==n?n:o,errorCode:o,level:null!==(r=null==i?void 0:i.level)&&void 0!==r?r:"Error",ext:e},t)}var lA=function(e){vT(n,e);var t=bT(n);function n(){return hT(this,n),t.apply(this,arguments)}return dT(n,[{key:"isEndedShow",get:function(){return this.config.isEndedShow},set:function(e){this.config.isEndedShow=e}},{key:"hide",value:function(){bC.addClass(this.root,"hide")}},{key:"show",value:function(){bC.removeClass(this.root,"hide")}},{key:"beforeCreate",value:function(e){"string"==typeof e.player.config.poster&&(e.config.poster=e.player.config.poster)}},{key:"afterCreate",value:function(){var e=this;this.on(DC,(function(){e.isEndedShow&&bC.removeClass(e.root,"hide")})),this.config.hideCanplay?(this.once(UC,(function(){e.onTimeUpdate()})),this.on(ZC,(function(){bC.removeClass(e.root,"hide"),bC.addClass(e.root,"xg-showplay"),e.once(UC,(function(){e.onTimeUpdate()}))}))):this.on(OC,(function(){bC.addClass(e.root,"hide")}))}},{key:"onTimeUpdate",value:function(){var e=this;this.player.currentTime?bC.removeClass(this.root,"xg-showplay"):this.once(UC,(function(){e.onTimeUpdate()}))}},{key:"update",value:function(e){e&&(this.config.poster=e,this.root.style.backgroundImage="url(".concat(e,")"))}},{key:"render",value:function(){var e,t=this.config,n=t.poster,r=t.hideCanplay,i=n?"background-image:url(".concat(n,");"):"";return Fm(e='<xg-poster class="xgplayer-poster '.concat(r?"xg-showplay":"",'" style="')).call(e,i,'">\n    </xg-poster>')}}],[{key:"pluginName",get:function(){return"poster"}},{key:"defaultConfig",get:function(){return{isEndedShow:!0,hideCanplay:!1,poster:""}}}]),n}(qx);function uA(){return(new DOMParser).parseFromString('<svg class="play" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n',"image/svg+xml").firstChild}function cA(){return(new DOMParser).parseFromString('<svg class="pause" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n',"image/svg+xml").firstChild}var hA={};function fA(e){var t;e?window.clearTimeout(e):Bw(t=fk(hA)).call(t,(function(e){window.clearTimeout(hA[e].id),delete hA[e]}))}var dA=function(e){vT(n,e);var t=bT(n);function n(e){var r;return hT(this,n),pT(mT(r=t.call(this,e)),"onPlayerReset",(function(){r.autoPlayStart=!1;var e="auto"===r.config.mode?"auto-hide":"hide";r.setAttr("data-state","play"),bC.removeClass(r.root,e),r.show()})),pT(mT(r),"onAutoplayStart",(function(){if(!r.autoPlayStart){var e="auto"===r.config.mode?"auto-hide":"hide";bC.addClass(r.root,e),r.autoPlayStart=!0,r.onPlayPause("play")}})),r.autoPlayStart=!1,r}return dT(n,[{key:"afterCreate",value:function(){var e=this,t=this.player,n=this.playerConfig;this.initIcons(),this.once(GC,(function(){n&&(n.lang&&"en"===n.lang?bC.addClass(t.root,"lang-is-en"):"jp"===n.lang&&bC.addClass(t.root,"lang-is-jp"))})),this.on(YC,this.onAutoplayStart),n.autoplay||this.show(),this.on(XC,(function(){var t="auto"===e.config.mode?"auto-hide":"hide";e.setAttr("data-state","play"),bC.removeClass(e.root,t),e.show()})),this.on(OC,(function(){e.onPlayPause("play")})),this.on(MC,(function(){e.onPlayPause("pause")})),this.on(dx,(function(){e.onPlayerReset()})),this.clickHandler=this.hook("startClick",this.switchPausePlay,{pre:function(t){t.cancelable&&t.preventDefault(),t.stopPropagation();var n=e.player.paused;e.emitUserAction(t,"switch_play_pause",{props:"paused",from:n,to:!n})}}),this.bind(["click","touchend"],this.clickHandler)}},{key:"registerIcons",value:function(){return{startPlay:{icon:uA,class:"xg-icon-play"},startPause:{icon:cA,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild("xg-start-inner",e.startPlay),this.appendChild("xg-start-inner",e.startPause)}},{key:"hide",value:function(){bC.addClass(this.root,"hide")}},{key:"show",value:function(){bC.removeClass(this.root,"hide")}},{key:"focusHide",value:function(){bC.addClass(this.root,"focus-hide")}},{key:"recover",value:function(){bC.removeClass(this.root,"focus-hide")}},{key:"switchStatus",value:function(e){e?this.setAttr("data-state",this.player.paused?"pause":"play"):this.setAttr("data-state",this.player.paused?"play":"pause")}},{key:"animate",value:function(e){var t=this;this._animateId=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{start:null,end:null};return hA[e]&&window.clearTimeout(hA[e].id),hA[e]={},n.start&&n.start(),hA[e].id=window.setTimeout((function(){n.end&&n.end(),window.clearTimeout(hA[e].id),delete hA[e]}),t),hA[e].id}("pauseplay",400,{start:function(){bC.addClass(t.root,"interact"),t.show(),t.switchStatus(!0)},end:function(){bC.removeClass(t.root,"interact"),!e&&t.hide(),t._animateId=null}})}},{key:"endAnimate",value:function(){bC.removeClass(this.root,"interact"),fA(this._animateId),this._animateId=null}},{key:"switchPausePlay",value:function(e){var t=this.player;(e.cancelable&&e.preventDefault(),e.stopPropagation(),t.state<PP)||(this.player.paused||t.state!==OP?t.play():t.pause())}},{key:"onPlayPause",value:function(e){var t=this.config,n=this.player;if(n&&!(n.state<OP)&&this.autoPlayStart){if("show"===t.mode)return this.switchStatus(),void this.show();if("auto"!==t.mode){if(t.isShowPause&&n.paused&&!n.ended||t.isShowEnd&&n.ended)return this.switchStatus(),this.show(),void this.endAnimate();if(t.disableAnimate)return this.switchStatus(),void this.hide();if("play"===e)this.autoPlayStart?this.animate():this.hide();else{if(!this.autoPlayStart||n.ended)return;this.animate()}}else this.switchStatus()}}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.clickHandler),fA(this._animateId)}},{key:"render",value:function(){var e=this.playerConfig.autoplay?"auto"===this.config.mode?"auto-hide":"hide":"";return'\n    <xg-start class="xgplayer-start '.concat(e,'">\n    <xg-start-inner></xg-start-inner>\n    </xg-start>')}}],[{key:"pluginName",get:function(){return"start"}},{key:"defaultConfig",get:function(){return{isShowPause:!1,isShowEnd:!1,disableAnimate:!1,mode:"hide"}}}]),n}(qx),pA=function(e){vT(n,e);var t=bT(n);function n(){return hT(this,n),t.apply(this,arguments)}return dT(n,[{key:"render",value:function(){var e=this.config.innerHtml,t=bC.createDom("xg-enter","",{},"xgplayer-enter");if(e&&e instanceof window.HTMLElement)t.appendChild(e);else if(e&&"string"==typeof e)t.innerHTML=e;else{for(var n="",r=1;r<=12;r++)n+='<div class="xgplayer-enter-bar'.concat(r,'"></div>');t.innerHTML='<div class="xgplayer-enter-spinner">'.concat(n,"</div>")}return t}}],[{key:"pluginName",get:function(){return"enter"}},{key:"defaultConfig",get:function(){return{innerHtml:"",logo:""}}}]),n}(qx);function vA(e,t,n){try{var r,i;return Fm(r=Fm(i=' <div class="xg-tips '.concat(n?"hide":" ",'" lang-key="')).call(i,e.i18nKeys[t],'">\n    ')).call(r,e.i18n[t],"\n    </div>")}catch(AV){return'<div class="xg-tips hide"></div>'}}var gA=function(e){vT(n,e);var t=bT(n);function n(){return hT(this,n),t.apply(this,arguments)}return dT(n,[{key:"afterCreate",value:function(){this.getMini=this.getMini.bind(this),this.exitMini=this.exitMini.bind(this),this.bind("click",this.getMini)}},{key:"getMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"exitMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.getMini)}},{key:"render",value:function(){var e,t="MINISCREEN";return Fm(e='\n      <xg-icon class="xgplayer-miniicon">\n      <div class="xgplayer-icon btn-text"><span class="icon-text" lang-key="'.concat(this.i18nKeys[t],'">')).call(e,this.i18n[t],"</span></div>\n      </xg-icon>")}}],[{key:"pluginName",get:function(){return"miniscreenIcon"}},{key:"defaultConfig",get:function(){return{position:zx.CONTROLS_RIGHT,index:10}}}]),n}(qx);function yA(e){var t=_S(e);return-1===hk(e).call(e,"%")&&!Lk(t)&&t}var mA=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],_A=mA.length;function bA(e){if("string"==typeof e&&(e=document.querySelector(e)),e&&"object"===cT(e)&&e.nodeType){var t=function(e){return window.getComputedStyle(e)}(e);if("none"===t.display)return function(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<_A;t++)e[mA[t]]=0;return e}();var n={};n.width=e.offsetWidth,n.height=e.offsetHeight;for(var r=n.isBorderBox="border-box"===t.boxSizing,i=0;i<_A;i++){var o=mA[i],a=t[o],s=_S(a);n[o]=Lk(s)?0:s}var l=n.paddingLeft+n.paddingRight,u=n.paddingTop+n.paddingBottom,c=n.marginLeft+n.marginRight,h=n.marginTop+n.marginBottom,f=n.borderLeftWidth+n.borderRightWidth,d=n.borderTopWidth+n.borderBottomWidth,p=r,v=yA(t.width);!1!==v&&(n.width=v+(p?0:l+f));var g=yA(t.height);return!1!==g&&(n.height=g+(p?0:u+d)),n.innerWidth=n.width-(l+f),n.innerHeight=n.height-(u+d),n.outerWidth=n.width+c,n.outerHeight=n.height+h,n}}function wA(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.identifier===t)return r}}var kA="dragStart",EA="dragMove",TA="dragEnded",SA={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]},CA=function(e){vT(n,e);var t=bT(n);function n(e){var r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return hT(this,n),(r=t.call(this)).isEnabled=!0,r.isDragging=!1,r.isDown=!1,r.position={},r.downPoint={},r.dragPoint={x:0,y:0},r.startPos={x:0,y:0},r._root=e instanceof Element?e:document.querySelector(e),r._handlerDom=i.handle instanceof Element?i.handle:document.querySelector(i.handle),r._root&&r._handlerDom?(r._bindStartEvent(),r):_T(r)}return dT(n,[{key:"_bindStartEvent",value:function(){var e,t=this;"ontouchstart"in window?this._startKey="touchstart":this._startKey="mousedown",this["on".concat(this._startKey)]=this["on".concat(this._startKey)].bind(this),this._handlerDom.addEventListener(this._startKey,this["on".concat(this._startKey)]),Bw(e=SA[this._startKey]).call(e,(function(e){t["on".concat(e)]=t["on".concat(e)].bind(t)}))}},{key:"_unbindStartEvent",value:function(){this._handlerDom.removeEventListener(this._startKey,this["on".concat(this._startKey)])}},{key:"_bindPostStartEvents",value:function(e){var t=this;if(e){var n=SA[this._startKey];Bw(n).call(n,(function(e){window.addEventListener(e,t["on".concat(e)])})),this._boundPointerEvents=n}}},{key:"_unbindPostStartEvents",value:function(){var e,t=this;this._boundPointerEvents&&(Bw(e=this._boundPointerEvents).call(e,(function(e){window.removeEventListener(e,t["on".concat(e)])})),delete this._boundPointerEvents)}},{key:"enable",value:function(){this.isEnabled=!0}},{key:"disable",value:function(){this.isEnabled=!1,this.isDragging&&this.onUp()}},{key:"onDocUp",value:function(e){this.onUp()}},{key:"animate",value:function(){var e=this;this.isDragging&&(this.positionDrag(),window.requestAnimationFrame((function(){e.animate()})))}},{key:"positionDrag",value:function(){var e,t=Fm(e="translate3d(".concat(this.dragPoint.x,"px, ")).call(e,this.dragPoint.y,"px, 0)");this._root.style.transform=t,this._root.style.webKitTransform=t}},{key:"setLeftTop",value:function(){this._root.style.left=this.position.x+"px",this._root.style.top=this.position.y+"px"}},{key:"onmousedown",value:function(e){this.dragStart(e,e)}},{key:"onmousemove",value:function(e){this.dragMove(e,e)}},{key:"onmouseup",value:function(e){this.dragEnd(e,e)}},{key:"ontouchstart",value:function(e){var t=e.changedTouches[0];this.dragStart(e,t),this.touchIdentifier=void 0!==t.pointerId?t.pointerId:t.identifier,e.preventDefault()}},{key:"ontouchmove",value:function(e){var t=wA(e.changedTouches,this.touchIdentifier);t&&this.dragMove(e,t)}},{key:"ontouchend",value:function(e){var t=wA(e.changedTouches,this.touchIdentifier);t&&this.dragEnd(e,t),e.preventDefault()}},{key:"ontouchcancel",value:function(e){var t=wA(e.changedTouches,this.touchIdentifier);t&&this.dragCancel(e,t)}},{key:"dragStart",value:function(e,t){if(this._root&&!this.isDown&&this.isEnabled){this.downPoint=t,this.dragPoint.x=0,this.dragPoint.y=0,this._getPosition();var n=bA(this._root);this.startPos.x=this.position.x,this.startPos.y=this.position.y,this.startPos.maxY=window.innerHeight-n.height,this.startPos.maxX=window.innerWidth-n.width,this.setLeftTop(),this.isDown=!0,this._bindPostStartEvents(e)}}},{key:"dragRealStart",value:function(e,t){this.isDragging=!0,this.animate(),this.emit(kA,this.startPos)}},{key:"dragEnd",value:function(e,t){this._root&&(this._unbindPostStartEvents(),this.isDragging&&(this._root.style.transform="",this.setLeftTop(),this.emit(TA)),this.presetInfo())}},{key:"_dragPointerMove",value:function(e,t){var n={x:t.pageX-this.downPoint.pageX,y:t.pageY-this.downPoint.pageY};return!this.isDragging&&this.hasDragStarted(n)&&this.dragRealStart(e,t),n}},{key:"dragMove",value:function(e,t){if(e=e||window.event,this.isDown){var n=this.startPos,r=n.x,i=n.y,o=this._dragPointerMove(e,t),a=o.x,s=o.y;a=this.checkContain("x",a,r),s=this.checkContain("y",s,i),this.position.x=r+a,this.position.y=i+s,this.dragPoint.x=a,this.dragPoint.y=s,this.emit(EA,this.position)}}},{key:"dragCancel",value:function(e,t){this.dragEnd(e,t)}},{key:"presetInfo",value:function(){this.isDragging=!1,this.startPos={x:0,y:0},this.dragPoint={x:0,y:0},this.isDown=!1}},{key:"destroy",value:function(){this._unbindStartEvent(),this._unbindPostStartEvents(),this.isDragging&&this.dragEnd(),this.removeAllListeners(),this._handlerDom=null}},{key:"hasDragStarted",value:function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3}},{key:"checkContain",value:function(e,t,n){return t+n<0?0-n:"x"===e&&t+n>this.startPos.maxX?this.startPos.maxX-n:"y"===e&&t+n>this.startPos.maxY?this.startPos.maxY-n:t}},{key:"_getPosition",value:function(){var e=window.getComputedStyle(this._root),t=this._getPositionCoord(e.left,"width"),n=this._getPositionCoord(e.top,"height");this.position.x=Lk(t)?0:t,this.position.y=Lk(n)?0:n,this._addTransformPosition(e)}},{key:"_addTransformPosition",value:function(e){var t=e.transform;if(0===hk(t).call(t,"matrix")){var n=t.split(","),r=0===hk(t).call(t,"matrix3d")?12:4,i=rE(n[r],10),o=rE(n[r+1],10);this.position.x+=i,this.position.y+=o}}},{key:"_getPositionCoord",value:function(e,t){if(-1!==hk(e).call(e,"%")){var n=bA(this._root.parentNode);return n?_S(e)/100*n[t]:0}return rE(e,10)}}]),n}(HT),xA=function(e){vT(n,e);var t=bT(n);function n(e){var r;hT(this,n),pT(mT(r=t.call(this,e)),"onCancelClick",(function(e){r.exitMini(),r.isClose=!0})),pT(mT(r),"onCenterClick",(function(e){var t=mT(r).player;t.paused?t.play():t.pause()})),pT(mT(r),"onScroll",(function(e){if(!(!window.scrollY&&0!==window.scrollY||Math.abs(window.scrollY-r.pos.scrollY)<50)){var t=rE(bC.getCss(r.player.root,"height"));t+=r.config.scrollTop,r.pos.scrollY=window.scrollY,window.scrollY>t+5?!r.isMini&&!r.isClose&&r.getMini():window.scrollY<=t&&(r.isMini&&r.exitMini(),r.isClose=!1)}})),r.isMini=!1,r.isClose=!1;var i=mT(r).config;return r.pos={left:i.left<0?window.innerWidth-i.width-20:i.left,top:i.top<0?window.innerHeight-i.height-20:i.top,height:r.config.height,width:r.config.width,scrollY:window.scrollY||0},r.lastStyle=null,r}return dT(n,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.mini&&(e.config.isShowIcon=e.player.config.mini)}},{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.on(MC,(function(){e.setAttr("data-state","pause")})),this.on(OC,(function(){e.setAttr("data-state","play")}))}},{key:"onPluginsReady",value:function(){var e=this,t=this.player;if(!this.config.disable){if(this.config.isShowIcon){var n={config:{onClick:function(){e.getMini()}}};t.controls.registerPlugin(gA,n,gA.pluginName)}var r=bC.checkTouchSupport()?"touchend":"click";this.bind(".mini-cancel-btn",r,this.onCancelClick),this.bind(".play-icon",r,this.onCenterClick),this.config.disableDrag||(this._draggabilly=new CA(this.player.root,{handle:this.root})),this.config.isScrollSwitch&&window.addEventListener("scroll",this.onScroll)}}},{key:"registerIcons",value:function(){return{play:{icon:uA,class:"xg-icon-play"},pause:{icon:cA,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".play-icon",e.play),this.appendChild(".play-icon",e.pause)}},{key:"getMini",value:function(){var e,t=this;if(!this.isMini){var n=this.player,r=this.playerConfig,i=this.config.target||this.player.root;this.lastStyle={},bC.addClass(n.root,"xgplayer-mini"),Bw(e=["width","height","top","left"]).call(e,(function(e){t.lastStyle[e]=i.style[e],i.style[e]="".concat(t.pos[e],"px")})),r.fluid&&(i.style["padding-top"]=""),this.emit(rx,!0),n.isMini=this.isMini=!0}}},{key:"exitMini",value:function(){var e=this;if(!this.isMini)return!1;var t,n=this.player,r=this.playerConfig,i=this.config.target||this.player.root;(bC.removeClass(n.root,"xgplayer-mini"),this.lastStyle)&&Bw(t=fk(this.lastStyle)).call(t,(function(t){i.style[t]=e.lastStyle[t]}));this.lastStyle=null,r.fluid&&(n.root.style.width="100%",n.root.style.height="0",n.root.style["padding-top"]="".concat(100*r.height/r.width,"%")),this.emit(rx,!1),this.isMini=n.isMini=!1}},{key:"destroy",value:function(){window.removeEventListener("scroll",this.onScroll);var e=bC.checkTouchSupport()?"touchend":"click";this.unbind(".mini-cancel-btn",e,this.onCancelClick),this.unbind(".play-icon",e,this.onCenterClick),this._draggabilly&&this._draggabilly.destroy(),this._draggabilly=null,this.exitMini()}},{key:"render",value:function(){if(!this.config.disable)return'\n      <xg-mini-layer class="xg-mini-layer">\n      <xg-mini-header class="xgplayer-mini-header">\n      '.concat(vA(this,"MINI_DRAG",this.playerConfig.isHideTips),'\n      </xg-mini-header>\n      <div class="mini-cancel-btn">\n        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">\n          <path fill="#fff" fill-rule="evenodd" d="M3.99 3.49a1 1 0 0 1 1.414 0L10 8.085l4.596-4.595a1 1 0 1 1 1.414 1.414L11.414 9.5l4.596 4.596a1 1 0 0 1 .084 1.32l-.084.094a1 1 0 0 1-1.414 0L10 10.914 5.404 15.51a1 1 0 0 1-1.414-1.414L8.585 9.5 3.99 4.904a1 1 0 0 1-.084-1.32z"></path>\n        </svg>\n      </div>\n      <div class="play-icon">\n      </div>\n      </xg-mini-layer>')}}],[{key:"pluginName",get:function(){return"miniscreen"}},{key:"defaultConfig",get:function(){return{index:10,disable:!1,width:320,height:180,left:-1,top:-1,isShowIcon:!1,isScrollSwitch:!1,scrollTop:0,disableDrag:!1}}}]),n}(qx),PA={mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",mousemove:"onMouseMove"},AA=["videoClick","videoDbClick"],IA=function(e){vT(n,e);var t=bT(n);function n(){var e,r;hT(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return pT(mT(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"onMouseMove",(function(e){var t=mT(r),n=t.player,i=t.playerConfig;n.isActive||(n.focus({autoHide:!i.closeDelayBlur}),!i.closeFocusVideoFocus&&n.media.focus())})),pT(mT(r),"onMouseEnter",(function(e){var t=mT(r),n=t.playerConfig,i=t.player;!n.closeFocusVideoFocus&&i.media.focus(),n.closeDelayBlur?i.focus({autoHide:!1}):i.focus()})),pT(mT(r),"onMouseLeave",(function(e){var t=r.playerConfig,n=t.closePlayerBlur,i=t.leavePlayerTime,o=t.closeDelayBlur;n||o||(i?r.player.focus({autoHide:!0,delay:i}):r.player.blur())})),pT(mT(r),"onVideoClick",(function(e){var t=mT(r),n=t.player,i=t.playerConfig;e.target&&i.closeVideoClick||e.target!==n.root&&e.target!==n.media&&e.target!==n.innerContainer&&e.target!==n.media.__canvas||(e.preventDefault(),i.closeVideoStopPropagation||e.stopPropagation(),r._clickCount++,r.clickTimer&&(clearTimeout(r.clickTimer),r.clickTimer=null),r.clickTimer=setTimeout((function(){r._clickCount&&(r._clickCount--,Dx(mT(r),AA[0],(function(e,t){r.switchPlayPause(t.e)}),{e:e,paused:n.paused}),clearTimeout(r.clickTimer),r.clickTimer=null)}),300))})),pT(mT(r),"onVideoDblClick",(function(e){var t=mT(r),n=t.player,i=t.playerConfig;i.closeVideoDblclick||!e.target||e.target!==n.media&&e.target!==n.media.__canvas||(!i.closeVideoClick&&r._clickCount<2?r._clickCount=0:(r._clickCount=0,r.clickTimer&&(clearTimeout(r.clickTimer),r.clickTimer=null),e.preventDefault(),e.stopPropagation(),Dx(mT(r),AA[1],(function(e,t){r.emitUserAction(t.e,"switch_fullscreen",{props:"fullscreen",from:n.fullscreen,to:!n.fullscreen}),n.fullscreen?n.exitFullscreen():n.getFullscreen()}),{e:e,fullscreen:n.fullscreen})))})),r}return dT(n,[{key:"afterCreate",value:function(){var e=this;this._clickCount=0,Bw(AA).call(AA,(function(t){e.__hooks[t]=null})),"mobile"===this.playerConfig.isMobileSimulateMode||"mobile"===xC.device&&!xC.os.isIpad||this.initEvents()}},{key:"initEvents",value:function(){var e,t=this,n=this.player,r=n.video,i=n.root,o=this.playerConfig.enableContextmenu;i&&i.addEventListener("click",this.onVideoClick,!1),i&&i.addEventListener("dblclick",this.onVideoDblClick,!1),Bw(e=fk(PA)).call(e,(function(e){i.addEventListener(e,t[PA[e]],!1)})),o&&r&&r.addEventListener("contextmenu",this.onContextmenu,!1)}},{key:"switchPlayPause",value:function(e){var t=this.player;this.emitUserAction(e,"switch_play_pause",{props:"paused",from:t.paused,to:!t.paused}),t.ended?t.duration!==1/0&&t.duration>0&&t.replay():t.paused?t.play():t.pause()}},{key:"onContextmenu",value:function(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.stopPropagation?e.stopPropagation():(e.returnValue=!1,e.cancelBubble=!0)}},{key:"destroy",value:function(){var e,t=this,n=this.player,r=n.video,i=n.root;this.clickTimer&&clearTimeout(this.clickTimer),i.removeEventListener("click",this.onVideoClick,!1),i.removeEventListener("dblclick",this.onVideoDblClick,!1),r.removeEventListener("contextmenu",this.onContextmenu,!1),Bw(e=fk(PA)).call(e,(function(e){i.removeEventListener(e,t[PA[e]],!1)}))}}],[{key:"pluginName",get:function(){return"pc"}},{key:"defaultConfig",get:function(){return{}}}]),n}(Lx),RA="press",OA="pressend",DA="doubleclick",MA="click",LA="touchmove",NA="touchstart",UA="touchend",FA={start:"touchstart",end:"touchend",move:"touchmove",cancel:"touchcancel"},BA={start:"mousedown",end:"mouseup",move:"mousemove",cancel:"mouseleave"};function VA(e){return e&&e.length>0?e[e.length-1]:null}var HA=function(){function e(t){var n,r=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{eventType:"touch"};hT(this,e),pT(this,"onTouchStart",(function(e){var t=r._pos,n=r.root,i=VA(e.touches);t.x=i?rE(i.pageX,10):e.pageX,t.y=i?rE(i.pageX,10):e.pageX,t.start=!0,r.__setPress(e),n.addEventListener(r.events.end,r.onTouchEnd),n.addEventListener(r.events.cancel,r.onTouchCancel),n.addEventListener(r.events.move,r.onTouchMove),r.trigger(NA,e)})),pT(this,"onTouchCancel",(function(e){r.onTouchEnd(e)})),pT(this,"onTouchEnd",(function(e){var t=r._pos,n=r.root;r.__clearPress(),n.removeEventListener(r.events.cancel,r.onTouchCancel),n.removeEventListener(r.events.end,r.onTouchEnd),n.removeEventListener(r.events.move,r.onTouchMove),e.moving=t.moving,e.press=t.press,t.press&&r.trigger(OA,e),r.trigger(UA,e),!t.press&&!t.moving&&r.__setDb(e),t.press=!1,t.start=!1,t.moving=!1})),pT(this,"onTouchMove",(function(e){var t=r._pos,n=r.config,i=VA(e.touches),o=i?rE(i.pageX,10):e.pageX,a=i?rE(i.pageY,10):e.pageX,s=o-t.x,l=a-t.y;Math.abs(l)<n.miniStep&&Math.abs(s)<n.miniStep||(r.__clearPress(),t.press&&r.trigger(OA,e),t.press=!1,t.moving=!0,r.trigger(LA,e))})),this._pos={moving:!1,start:!1,x:0,y:0},this.config={pressDelay:600,dbClickDelay:200,disablePress:!1,disableDbClick:!1,miniStep:2,needPreventDefault:!0},Bw(n=fk(i)).call(n,(function(e){r.config[e]=i[e]})),this.root=t,this.events="mouse"===i.eventType?BA:FA,this.pressIntrvalId=null,this.dbIntrvalId=null,this.__handlers={},this._initEvent()}return dT(e,[{key:"_initEvent",value:function(){this.root.addEventListener(this.events.start,this.onTouchStart)}},{key:"__setPress",value:function(e){var t=this,n=this.config;this.pressIntrvalId&&this.__clearPress(),this.pressIntrvalId=setTimeout((function(){t.trigger(RA,e),t._pos.press=!0,t.__clearPress()}),n.pressDelay)}},{key:"__clearPress",value:function(){window.clearTimeout(this.pressIntrvalId),this.pressIntrvalId=null}},{key:"__setDb",value:function(e){var t=this,n=this.config;if(this.dbIntrvalId)return this.__clearDb(),void this.trigger(DA,e);this.dbIntrvalId=setTimeout((function(){t.__clearDb(),t._pos.start||t._pos.press||t._pos.moving||t.trigger(MA,e)}),n.dbClickDelay)}},{key:"__clearDb",value:function(){clearTimeout(this.dbIntrvalId),this.dbIntrvalId=null}},{key:"on",value:function(e,t){this.__handlers[e]||(this.__handlers[e]=[]),this.__handlers[e].push(t)}},{key:"off",value:function(e,t){if(this.__handlers[e]){for(var n,r=this.__handlers[e],i=-1,o=0;o<r.length;o++)if(r[o]===t){i=o;break}if(i>=0)Mk(n=this.__handlers[e]).call(n,i,1)}}},{key:"trigger",value:function(e,t){var n;this.__handlers[e]&&Bw(n=this.__handlers[e]).call(n,(function(n){try{n(t)}catch(PH){console.error("trigger>>:".concat(e),PH)}}))}},{key:"destroy",value:function(){var e=this,t={touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"};fk(t).forEach((function(n){e.root.removeEventListener(n,e[t[n]])}))}}]),e}();function jA(){return(new DOMParser).parseFromString('<svg width="20" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg"\n  xmlns:xlink="http://www.w3.org/1999/xlink">\n  <path opacity="0.54"\n    d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z"\n    fill="white" />\n  <path transform="translate(5 0)" d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z" fill="white"/>\n</svg>',"image/svg+xml").firstChild}var zA="auto",KA="seeking",WA="playbackrate",GA=["videoClick","videoDbClick"],qA=function(e){vT(n,e);var t=bT(n);function n(e){var r;return hT(this,n),pT(mT(r=t.call(this,e)),"onTouchStart",(function(e){var t=mT(r),n=t.player,i=t.config,o=t.pos,a=t.playerConfig,s=r.getTouche(e);if(s&&!i.disableGesture&&r.duration>0&&!n.ended){o.isStart=!0,bC.checkIsFunction(a.disableSwipeHandler)&&a.disableSwipeHandler(),qm(r).call(r,".xg-dur").innerHTML=bC.format(r.duration);var l=r.root.getBoundingClientRect();90===n.rotateDeg?(o.top=l.left,o.left=l.top,o.width=l.height,o.height=l.width):(o.top=l.top,o.left=l.left,o.width=l.width,o.height=l.height);var u=rE(s.pageX-o.left,10),c=rE(s.pageY-o.top,10);o.x=90===n.rotateDeg?c:u,o.y=90===n.rotateDeg?u:c,o.scopeL=i.scopeL*o.width,o.scopeR=(1-i.scopeR)*o.width,o.scopeM1=o.width*(1-i.scopeM)/2,o.scopeM2=o.width-o.scopeM1}})),pT(mT(r),"onTouchMove",(function(e){var t=r.getTouche(e),n=mT(r),i=n.pos,o=n.config,a=n.player;if(t&&!o.disableGesture&&r.duration&&i.isStart){var s=o.miniMoveStep,l=o.hideControlsActive,u=rE(t.pageX-i.left,10),c=rE(t.pageY-i.top,10),h=90===a.rotateDeg?c:u,f=90===a.rotateDeg?u:c;if(Math.abs(h-i.x)>s||Math.abs(f-i.y)>s){var d=h-i.x,p=f-i.y,v=i.scope;if(-1===v&&(0===(v=r.checkScope(h,f,d,p,i))&&(l?a.blur():a.focus({autoHide:!1}),!i.time&&(i.time=rE(1e3*a.currentTime,10)+1e3*r.timeOffset)),i.scope=v),-1===v||v>0&&!o.gestureY||0===v&&!o.gestureX)return;e.cancelable&&e.preventDefault(),r.executeMove(d,p,v,i.width,i.height),i.x=h,i.y=f}}})),pT(mT(r),"onTouchEnd",(function(e){var t=mT(r),n=t.player,i=t.pos,o=t.playerConfig;if(i.isStart){i.scope>-1&&e.cancelable&&e.preventDefault();var a=r.config,s=a.disableGesture,l=a.gestureX;!s&&l?(r.endLastMove(i.scope),setTimeout((function(){n.getPlugin("progress")&&n.getPlugin("progress").resetSeekState()}),10)):i.time=0,i.scope=-1,r.resetPos(),bC.checkIsFunction(o.enableSwipeHandler)&&o.enableSwipeHandler(),r.changeAction(zA)}})),pT(mT(r),"onRootTouchMove",(function(e){!r.config.disableGesture&&r.config.gestureX&&r.checkIsRootTarget(e)&&(e.stopPropagation(),r.pos.isStart?r.onTouchMove(e):r.onTouchStart(e))})),pT(mT(r),"onRootTouchEnd",(function(e){r.pos.isStart&&r.checkIsRootTarget(e)&&(e.stopPropagation(),r.onTouchEnd(e))})),r.pos={isStart:!1,x:0,y:0,time:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1},r.timer=null,r}return dT(n,[{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"registerIcons",value:function(){return{seekTipIcon:{icon:jA,class:"xg-seek-pre"}}}},{key:"afterCreate",value:function(){var e,t=this;Bw(GA).call(GA,(function(e){t.__hooks[e]=null}));var n=this.playerConfig,r=this.config,i=this.player;!0===n.closeVideoDblclick&&(r.closedbClick=!0),this.resetPos(),bC.isUndefined(n.disableGesture)||(r.disableGesture=!!n.disableGesture),this.appendChild(".xg-seek-icon",this.icons.seekTipIcon),this.xgMask=bC.createDom("xg-mask","",{},"xgmask"),i.root.appendChild(this.xgMask),this.initCustomStyle(),this.registerThumbnail();var o="mouse"===this.domEventType?"mouse":"touch";this.touch=new HA(this.root,{eventType:o,needPreventDefault:!this.config.disableGesture}),this.root.addEventListener("contextmenu",(function(e){e.preventDefault()})),i.root.addEventListener("touchmove",this.onRootTouchMove,!0),i.root.addEventListener("touchend",this.onRootTouchEnd,!0),this.on(VC,(function(){var e=t.player,n=t.config;1e3*e.duration<n.moveDuration&&(n.moveDuration=1e3*e.duration)})),this.on([BC,DC],(function(){var e=t.pos,n=e.time;!e.isStart&&n>0&&(t.pos.time=0)}));var a={touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd",press:"onPress",pressend:"onPressEnd",click:"onClick",doubleclick:"onDbClick"};if(Bw(e=fk(a)).call(e,(function(e){t.touch.on(e,(function(n){t[a[e]](n)}))})),!r.disableActive){var s=i.plugins.progress;s&&(s.addCallBack("dragmove",(function(e){t.activeSeekNote(e.currentTime,e.forward)})),s.addCallBack("dragend",(function(){t.changeAction(zA)})))}}},{key:"registerThumbnail",value:function(){var e=this.player.plugins.thumbnail;if(e&&e.usable){this.thumbnail=e.createThumbnail(null,"mobile-thumbnail");var t=qm(this).call(this,".time-preview");t.insertBefore(this.thumbnail,t.children[0])}}},{key:"initCustomStyle",value:function(){var e=(this.playerConfig||{}).commonStyle,t=e.playedColor,n=e.progressColor;t&&(qm(this).call(this,".xg-curbar").style.backgroundColor=t,qm(this).call(this,".xg-cur").style.color=t);n&&(qm(this).call(this,".xg-bar").style.backgroundColor=n,qm(this).call(this,".time-preview").style.color=n);this.config.disableTimeProgress&&bC.addClass(qm(this).call(this,".xg-timebar"),"hide")}},{key:"resetPos",value:function(){var e,t=this;this.pos?(this.pos.isStart=!1,this.pos.scope=-1,Bw(e=["x","y","width","height","scopeL","scopeR","scopeM1","scopeM2"]).call(e,(function(e){t.pos[e]=0}))):this.pos={isStart:!1,x:0,y:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1,time:0}}},{key:"changeAction",value:function(e){var t=this.player;this.root.setAttribute("data-xg-action",e);var n=t.plugins.start;n&&n.recover()}},{key:"getTouche",value:function(e){this.player.rotateDeg;var t=e.touches&&e.touches.length>0?e.touches[e.touches.length-1]:e;return{pageX:t.pageX,pageY:t.pageY}}},{key:"checkScope",value:function(e,t,n,r,i){var o=i.width,a=-1;if(e<0||e>o)return a;var s=0===r?Math.abs(n):Math.abs(n/r);return Math.abs(n)>0&&s>=1.73&&e>i.scopeM1&&e<i.scopeM2?a=0:(0===Math.abs(n)||s<=.57)&&(a=e<i.scopeL?1:e>i.scopeR?2:3),a}},{key:"executeMove",value:function(e,t,n,r,i){switch(n){case 0:this.updateTime(e/r*this.config.scopeM);break;case 1:this.updateBrightness(t/i);break;case 2:xC.os.isIos||this.updateVolume(t/i)}}},{key:"endLastMove",value:function(e){var t=this,n=this.pos,r=this.player,i=this.config,o=(n.time-this.timeOffset)/1e3;if(0===e)r.seek(Number(o).toFixed(1)),i.hideControlsEnd?r.blur():r.focus(),this.timer=setTimeout((function(){t.pos.time=0}),500);this.changeAction(zA)}},{key:"checkIsRootTarget",value:function(e){var t=this.player.plugins||{};return(!t.progress||!t.progress.root.contains(e.target))&&(t.start&&t.start.root.contains(e.target)||t.controls&&t.controls.root.contains(e.target))}},{key:"sendUseAction",value:function(e){var t=this.player.paused;this.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t,to:!t})}},{key:"clickHandler",value:function(e){var t=this.player,n=this.config,r=this.playerConfig;t.state<OP?r.closeVideoClick||(this.sendUseAction(bC.createEvent("click")),t.play()):!n.closedbClick||r.closeVideoClick?t.isActive?t.blur():t.focus():r.closeVideoClick||((t.isActive||n.focusVideoClick)&&(this.sendUseAction(bC.createEvent("click")),this.switchPlayPause()),t.focus())}},{key:"dbClickHandler",value:function(e){var t=this.config,n=this.player;!t.closedbClick&&n.state>=OP&&(this.sendUseAction(bC.createEvent("dblclick")),this.switchPlayPause())}},{key:"onClick",value:function(e){var t=this,n=this.player;Dx(this,GA[0],(function(e,n){t.clickHandler(n.e)}),{e:e,paused:n.paused})}},{key:"onDbClick",value:function(e){var t=this,n=this.player;Dx(this,GA[1],(function(e,n){t.dbClickHandler(n.e)}),{e:e,paused:n.paused})}},{key:"onPress",value:function(e){var t=this.pos,n=this.config,r=this.player;n.disablePress||(t.rate=this.player.playbackRate,this.emitUserAction("press","change_rate",{prop:"playbackRate",from:r.playbackRate,to:n.pressRate}),r.playbackRate=n.pressRate,this.changeAction(WA))}},{key:"onPressEnd",value:function(e){var t=this.pos,n=this.config,r=this.player;n.disablePress||(this.emitUserAction("pressend","change_rate",{prop:"playbackRate",from:r.playbackRate,to:t.rate}),r.playbackRate=t.rate,t.rate=1,this.changeAction(zA))}},{key:"updateTime",value:function(e){var t=this.player,n=this.config,r=this.player.duration;e=Number(e.toFixed(4));var i=rE(e*n.moveDuration,10)+this.timeOffset;i=(i+=this.pos.time)<0?0:i>1e3*r?1e3*r-200:i,t.getPlugin("time")&&t.getPlugin("time").updateTime(i/1e3),t.getPlugin("progress")&&t.getPlugin("progress").updatePercent(i/1e3/this.duration,!0),this.activeSeekNote(i/1e3,e>0),n.isTouchingSeek&&t.seek(Number((i-this.timeOffset)/1e3).toFixed(1)),this.pos.time=i}},{key:"updateVolume",value:function(e){this.player.rotateDeg&&(e=-e);var t=this.player,n=this.pos;if(e=rE(100*e,10),n.volume+=e,!(Math.abs(n.volume)<10)){var r=rE(10*t.volume,10)-rE(n.volume/10,10);r=r>10?10:r<1?0:r,t.volume=r/10,n.volume=0}}},{key:"updateBrightness",value:function(e){this.player.rotateDeg&&(e=-e);var t=this.pos,n=this.config,r=this.xgMask,i=t.light+.8*e;i=i>n.maxDarkness?n.maxDarkness:i<0?0:i,r&&(r.style.backgroundColor="rgba(0,0,0,".concat(i,")")),t.light=i}},{key:"activeSeekNote",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.player,r=this.config,i=!(this.duration!==1/0&&this.duration>0);if(e&&"number"==typeof e&&!i&&!r.disableActive){e<0?e=0:e>n.duration&&(e=n.duration-.2),this.changeAction(KA);var o=n.plugins.start;if(o&&o.focusHide(),qm(this).call(this,".xg-dur").innerHTML=bC.format(this.duration),qm(this).call(this,".xg-cur").innerHTML=bC.format(e),qm(this).call(this,".xg-curbar").style.width="".concat(e/this.duration*100,"%"),t)bC.removeClass(qm(this).call(this,".xg-seek-show"),"xg-back");else bC.addClass(qm(this).call(this,".xg-seek-show"),"xg-back");this.updateThumbnails(e)}}},{key:"updateThumbnails",value:function(e){var t=this.player.plugins.thumbnail;t&&t.usable&&this.thumbnail&&t.update(this.thumbnail,e,160,90)}},{key:"switchPlayPause",value:function(){var e=this.player;if(e.state<IP)return!1;e.ended||(e.paused?e.play():e.pause())}},{key:"disableGesture",value:function(){this.config.disableGesture=!1}},{key:"enableGesture",value:function(){this.config.disableGesture=!0}},{key:"destroy",value:function(){var e=this.player;this.timer&&clearTimeout(this.timer),this.thumbnail=null,e.root.removeChild(this.xgMask),this.xgMask=null,this.touch&&this.touch.destroy(),this.touch=null,e.root.removeEventListener("touchmove",this.onRootTouchMove,!0),e.root.removeEventListener("touchend",this.onRootTouchEnd,!0)}},{key:"render",value:function(){var e,t,n,r="normal"!==this.config.gradient?"gradient ".concat(this.config.gradient):"gradient";return Fm(e=Fm(t=Fm(n='\n     <xg-trigger class="trigger">\n     <div class="'.concat(r,'"></div>\n        <div class="time-preview">\n            <div class="xg-seek-show ')).call(n,this.config.disableSeekIcon?" hide-seek-icon":"",'">\n              <i class="xg-seek-icon"></i>\n              <span class="xg-cur">00:00</span>\n              <span>/</span>\n              <span class="xg-dur">00:00</span>\n            </div>\n              <div class="xg-bar xg-timebar">\n                <div class="xg-curbar"></div>\n              </div>\n        </div>\n        <div class="xg-playbackrate xg-top-note">\n            <span><i>')).call(t,this.config.pressRate,"X</i>")).call(e,this.i18n.FORWARD,"</span>\n        </div>\n     </xg-trigger>\n    ")}}],[{key:"pluginName",get:function(){return"mobile"}},{key:"defaultConfig",get:function(){return{index:0,disableGesture:!1,gestureX:!0,gestureY:!0,gradient:"normal",isTouchingSeek:!1,miniMoveStep:5,miniYPer:5,scopeL:.25,scopeR:.25,scopeM:.9,pressRate:2,darkness:!0,maxDarkness:.8,disableActive:!1,disableTimeProgress:!1,hideControlsActive:!1,hideControlsEnd:!1,moveDuration:36e4,closedbClick:!1,disablePress:!0,disableSeekIcon:!1,focusVideoClick:!1}}}]),n}(qx);On({target:"String",proto:!0},{repeat:GT});var YA=Gc("String").repeat,XA=ce,JA=YA,QA=String.prototype,$A=o((function(e){var t=e.repeat;return"string"==typeof e||e===QA||XA(QA,e)&&t===QA.repeat?JA:t}));function ZA(e){var t=e.tagName;return!("INPUT"!==t&&"TEXTAREA"!==t&&!e.isContentEditable)}var eI=function(e){vT(n,e);var t=bT(n);function n(){var e,r;hT(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return pT(mT(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"onBodyKeyDown",(function(e){if(r.player){var t=e||window.event,n=t.keyCode,i=mT(r),o=i._keyState,a=i.player,s=r.config,l=s.disable,u=s.disableBodyTrigger,c=s.isIgnoreUserActive;l||u||!a.isUserActive&&!c||ZA(t.target)||!r.checkIsVisible()||t.metaKey||t.altKey||t.ctrlKey?o.isBodyKeyDown=!1:($A(e)||o.isKeyDown||((t.target===document.body||r.config.isGlobalTrigger&&!ZA(t.target))&&r.checkCode(n,!0)&&(o.isBodyKeyDown=!0),document.addEventListener("keyup",r.onBodyKeyUp)),o.isBodyKeyDown&&r.handleKeyDown(t))}})),pT(mT(r),"onBodyKeyUp",(function(e){r.player&&(document.removeEventListener("keyup",r.onBodyKeyUp),r.handleKeyUp(e))})),pT(mT(r),"onKeydown",(function(e){if(r.player){var t=e||window.event,n=mT(r)._keyState;if(!$A(t)){if(r.config.disable||r.config.disableRootTrigger||t.metaKey||t.altKey||t.ctrlKey)return;if(!r.player.isUserActive&&!r.config.isIgnoreUserActive)return;!t||37!==t.keyCode&&!r.checkCode(t.keyCode)||t.target!==r.player.root&&t.target!==r.player.video&&t.target!==r.player.controls.el||(n.isKeyDown=!0),r.player.root.addEventListener("keyup",r.onKeyup)}n.isKeyDown&&r.handleKeyDown(t)}})),pT(mT(r),"onKeyup",(function(e){r.player&&(r.player.root.removeEventListener("keyup",r.onKeyup),r.handleKeyUp(e))})),r}return dT(n,[{key:"mergekeyCodeMap",value:function(){var e,t=this,n=this.config.keyCodeMap;n&&Bw(e=fk(n)).call(e,(function(e){var r;t.keyCodeMap[e]?Bw(r=["keyCode","action","disable","pressAction","disablePress","isBodyTarget"]).call(r,(function(r){n[e][r]&&(t.keyCodeMap[e][r]=n[e][r])})):t.keyCodeMap[e]=n[e]}))}},{key:"afterCreate",value:function(){this.config.disable=!this.playerConfig.keyShortcut;var e="function"==typeof this.config.seekStep?this.config.seekStep(this.player):this.config.seekStep;e&&"number"==typeof e&&(this.seekStep=e),this.keyCodeMap={space:{keyCode:32,action:"playPause",disable:!1,disablePress:!1,noBodyTarget:!1},up:{keyCode:38,action:"upVolume",disable:!1,disablePress:!1,noBodyTarget:!0},down:{keyCode:40,action:"downVolume",disable:!1,disablePress:!1,noBodyTarget:!0},left:{keyCode:37,action:"seekBack",disablePress:!1,disable:!1},right:{keyCode:39,action:"seek",pressAction:"changePlaybackRate",disablePress:!1,disable:!1},esc:{keyCode:27,action:"exitFullscreen",disablePress:!0,disable:!1}},this.mergekeyCodeMap(),this._keyState={isKeyDown:!1,isBodyKeyDown:!1,isPress:!1,tt:0,playbackRate:0},this.player.root.addEventListener("keydown",this.onKeydown),document.addEventListener("keydown",this.onBodyKeyDown)}},{key:"checkIsVisible",value:function(){if(!this.config.checkVisible)return!0;var e=this.player.root.getBoundingClientRect(),t=e.height,n=e.top,r=e.bottom,i=window.innerHeight;return!(n<0&&n<0-.9*t||r>0&&r-i>.9*t)}},{key:"checkCode",value:function(e,t){var n,r=this,i=!1;return Bw(n=fk(this.keyCodeMap)).call(n,(function(n){r.keyCodeMap[n]&&e===r.keyCodeMap[n].keyCode&&!r.keyCodeMap[n].disable&&(i=!t||t&&!r.keyCodeMap[n].noBodyTarget)})),i}},{key:"downVolume",value:function(e){var t=this.player;if(!(t.volume<=0)){var n=_S((t.volume-.1).toFixed(1)),r={volume:{from:t.volume,to:n}};this.emitUserAction(e,"change_volume",{props:r}),t.volume=n>=0?n:0}}},{key:"upVolume",value:function(e){var t=this.player;if(!(t.volume>=1)){var n=_S((t.volume+.1).toFixed(1)),r={volume:{from:t.volume,to:n}};this.emitUserAction(e,"change_volume",{props:r}),t.volume=n<=1?n:1}}},{key:"seek",value:function(e){var t=this.player,n=t.currentTime,r=t.offsetCurrentTime,i=t.duration,o=t.offsetDuration,a=t.timeSegments,s=r>-1?r:n,l=o||i,u=$A(e)&&this.seekStep>=4?rE(this.seekStep/2,10):this.seekStep;s+u<=l?s+=u:s=l;var c=bC.getCurrentTimeByOffset(s,a),h={currentTime:{from:n,to:c}};this.emitUserAction(e,"seek",{props:h}),this.player.currentTime=c}},{key:"seekBack",value:function(e){var t=this.player,n=t.currentTime,r=t.offsetCurrentTime,i=t.timeSegments,o=(r>-1?r:n)-($A(e)?rE(this.seekStep/2,10):this.seekStep);o<0&&(o=0);var a={currentTime:{from:n,to:o=bC.getCurrentTimeByOffset(o,i)}};this.emitUserAction(e,"seek",{props:a}),this.player.currentTime=o}},{key:"changePlaybackRate",value:function(e){var t=this._keyState,n=this.config,r=this.player;0===t.playbackRate&&(t.playbackRate=r.playbackRate,r.playbackRate=n.playbackRate)}},{key:"playPause",value:function(e){var t=this.player;t&&(this.emitUserAction(e,"switch_play_pause"),t.paused?t.play():t.pause())}},{key:"exitFullscreen",value:function(e){var t=this.player,n=t.fullscreen,r=t.cssfullscreen;n&&(this.emitUserAction("keyup","switch_fullscreen",{prop:"fullscreen",from:n,to:!n}),t.exitFullscreen()),r&&(this.emitUserAction("keyup","switch_css_fullscreen",{prop:"cssfullscreen",from:r,to:!r}),t.exitCssFullscreen())}},{key:"handleKeyDown",value:function(e){var t=this._keyState;if($A(e)){t.isPress=!0;var n=Date.now();if(n-t.tt<200)return;t.tt=n}!function(e){e.preventDefault(),e.returnValue=!1}(e),this.handleKeyCode(e.keyCode,e,t.isPress)}},{key:"handleKeyUp",value:function(e){var t=this._keyState;t.playbackRate>0&&(this.player.playbackRate=t.playbackRate,t.playbackRate=0),t.isKeyDown=!1,t.isPress=!1,t.tt=0}},{key:"handleKeyCode",value:function(e,t,n){for(var r=fk(this.keyCodeMap),i=0;i<r.length;i++){var o=this.keyCodeMap[r[i]],a=o.action,s=o.keyCode,l=o.disable,u=o.pressAction,c=o.disablePress;if(s===e){if(!(l||n&&c)){var h=n&&u||a;"function"==typeof h?a(t,this.player,n):"string"==typeof h&&"function"==typeof this[h]&&this[h](t,this.player,n),this.emit(hx,uT({key:r[i],target:t.target,isPress:n},this.keyCodeMap[r[i]]))}break}}}},{key:"destroy",value:function(){this.player.root.removeEventListener("keydown",this.onKeydown),document.removeEventListener("keydown",this.onBodyKeyDown),this.player.root.removeEventListener("keyup",this.onKeyup),document.removeEventListener("keyup",this.onBodyKeyUp)}},{key:"disable",value:function(){this.config.disable=!0}},{key:"enable",value:function(){this.config.disable=!1}}],[{key:"pluginName",get:function(){return"keyboard"}},{key:"defaultConfig",get:function(){return{seekStep:10,checkVisible:!1,disableBodyTrigger:!1,disableRootTrigger:!1,isGlobalTrigger:!1,keyCodeMap:{},disable:!1,playbackRate:2,isIgnoreUserActive:!1}}}]),n}(Lx);function tI(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="-5 -5 110 110">\n  <path d="M100,50A50,50,0,1,1,50,0" stroke-width="5" stroke="#ddd" stroke-dasharray="236" fill="none"></path>\n</svg>\n',"image/svg+xml").firstChild}var nI=function(e){vT(n,e);var t=bT(n);function n(){return hT(this,n),t.apply(this,arguments)}return dT(n,[{key:"registerIcons",value:function(){return{loadingIcon:tI}}},{key:"afterCreate",value:function(){this.appendChild("xg-loading-inner",this.icons.loadingIcon)}},{key:"render",value:function(){return'\n    <xg-loading class="xgplayer-loading">\n      <xg-loading-inner></xg-loading-inner>\n    </xg-loading>'}}],[{key:"pluginName",get:function(){return"loading"}},{key:"defaultConfig",get:function(){return{position:zx.ROOT}}}]),n}(qx),rI=On,iI=ra.findIndex,oI="findIndex",aI=!0;oI in[]&&Array(1)[oI]((function(){aI=!1})),rI({target:"Array",proto:!0,forced:aI},{findIndex:function(e){return iI(this,e,arguments.length>1?arguments[1]:void 0)}});var sI=Gc("Array").findIndex,lI=ce,uI=sI,cI=Array.prototype,hI=o((function(e){var t=e.findIndex;return e===cI||lI(cI,e)&&t===cI.findIndex?uI:t})),fI=function(e){vT(n,e);var t=bT(n);function n(){var e,r;hT(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return pT(mT(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"_onMouseenter",(function(e){r.emit("icon_mouseenter",{pluginName:r.pluginName})})),pT(mT(r),"_onMouseLeave",(function(e){r.emit("icon_mouseleave",{pluginName:r.pluginName})})),r}return dT(n,[{key:"afterCreate",value:function(){this.bind("mouseenter",this._onMouseenter),this.bind("mouseleave",this._onMouseLeave)}},{key:"destroy",value:function(){this.unbind("mouseenter",this._onMouseenter),this.unbind("mouseleave",this._onMouseLeave)}}]),n}(qx),dI=function(e){vT(n,e);var t=bT(n);function n(){return hT(this,n),t.apply(this,arguments)}return dT(n,[{key:"afterCreate",value:function(){var e=this;wT(gT(n.prototype),"afterCreate",this).call(this);var t=this.player;this.config.disable||(this.initIcons(),this.btnClick=this.btnClick.bind(this),this.bind(["touchend","click"],this.btnClick),this.on([MC,LC,zC],(function(){e.animate(t.paused)})),this.on(OC,(function(){e.animate(t.paused)})),this.animate(!0))}},{key:"registerIcons",value:function(){return{play:{icon:uA,class:"xg-icon-play"},pause:{icon:cA,class:"xg-icon-pause"}}}},{key:"btnClick",value:function(e){e.preventDefault(),e.stopPropagation();var t=this.player;return this.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t.paused,to:!t.paused}),t.ended?t.replay():t.paused?(t.play(),this.animate(!1)):(t.pause(),this.animate(!0)),!1}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.play),this.appendChild(".xgplayer-icon",e.pause)}},{key:"animate",value:function(e){if(this.player){var t=this.i18nKeys,n=qm(this).call(this,".xg-tips");e?(this.setAttr("data-state","pause"),n&&this.changeLangTextKey(n,t.PLAY_TIPS)):(this.setAttr("data-state","play"),n&&this.changeLangTextKey(n,t.PAUSE_TIPS))}}},{key:"destroy",value:function(){wT(gT(n.prototype),"destroy",this).call(this),this.unbind(["touchend","click"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-play">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(vA(this,"PLAY_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"play"}},{key:"defaultConfig",get:function(){return{position:zx.CONTROLS_LEFT,index:0,disable:!1}}}]),n}(fI);function pI(){return(new DOMParser).parseFromString('<svg width="32px" height="40px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M11.2374369,14 L17.6187184,7.61871843 C17.9604272,7.27700968 17.9604272,6.72299032 17.6187184,6.38128157 C17.2770097,6.03957281 16.7229903,6.03957281 16.3812816,6.38128157 L9.38128157,13.3812816 C9.03957281,13.7229903 9.03957281,14.2770097 9.38128157,14.6187184 L16.3812816,21.6187184 C16.7229903,21.9604272 17.2770097,21.9604272 17.6187184,21.6187184 C17.9604272,21.2770097 17.9604272,20.7229903 17.6187184,20.3812816 L11.2374369,14 L11.2374369,14 Z" fill="#FFFFFF"></path>\n    </g>\n</svg>',"image/svg+xml").firstChild}var vI=function(e){vT(n,e);var t=bT(n);function n(){return hT(this,n),t.apply(this,arguments)}return dT(n,[{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.onClick=function(t){t.preventDefault(),t.stopPropagation(),e.config.onClick(t)},this.bind(["click","touchend"],this.onClick)}},{key:"registerIcons",value:function(){return{screenBack:{icon:pI,class:"xg-fullscreen-back"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(this.root,e.screenBack)}},{key:"show",value:function(){bC.addClass(this.root,"show")}},{key:"hide",value:function(){bC.removeClass(this.root,"show")}},{key:"render",value:function(){return'<xg-icon class="xgplayer-back">\n    </xg-icon>'}}],[{key:"pluginName",get:function(){return"topbackicon"}},{key:"defaultConfig",get:function(){return{position:zx.ROOT_TOP,index:0}}}]),n}(qx);function gI(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598 214h212v212h-84v-128h-128v-84zM726 726v-128h84v212h-212v-84h128zM214 426v-212h212v84h-128v128h-84zM298 598v128h128v84h-212v-212h84z"></path>\n</svg>\n',"image/svg+xml").firstChild}function yI(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"></path>\n</svg>\n',"image/svg+xml").firstChild}var mI=function(e){vT(n,e);var t=bT(n);function n(){var e,r;hT(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return pT(mT(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"_onOrientationChange",(function(e){r.player.fullscreen&&r.config.rotateFullscreen&&(90===window.orientation||-90===window.orientation?r.player.setRotateDeg(0):r.player.setRotateDeg(90))})),r}return dT(n,[{key:"afterCreate",value:function(){var e=this;wT(gT(n.prototype),"afterCreate",this).call(this);var t=this.config,r=this.playerConfig;if(!t.disable){t.target&&(this.playerConfig.fullscreenTarget=this.config.target);var i=bC.getFullScreenEl();r.fullscreenTarget===i&&this.player.getFullscreen().catch((function(e){})),this.initIcons(),this.handleFullscreen=this.hook("fullscreenChange",this.toggleFullScreen,{pre:function(t){var n=e.player.fullscreen;e.emitUserAction(t,"switch_fullscreen",{prop:"fullscreen",from:n,to:!n})}}),this.bind(".xgplayer-fullscreen",["touchend","click"],this.handleFullscreen),this.on(tx,(function(t){var n=qm(e).call(e,".xg-tips");n&&e.changeLangTextKey(n,t?e.i18nKeys.EXITFULLSCREEN_TIPS:e.i18nKeys.FULLSCREEN_TIPS),e.animate(t)})),this.config.needBackIcon&&(this.topBackIcon=this.player.registerPlugin({plugin:vI,options:{config:{onClick:function(t){e.handleFullscreen(t)}}}})),"mobile"===xC.device&&window.addEventListener("orientationchange",this._onOrientationChange)}}},{key:"registerIcons",value:function(){return{fullscreen:{icon:gI,class:"xg-get-fullscreen"},exitFullscreen:{icon:yI,class:"xg-exit-fullscreen"}}}},{key:"destroy",value:function(){wT(gT(n.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon","mobile"===xC.device?"touchend":"click",this.handleFullscreen),"mobile"===xC.device&&window.removeEventListener("orientationchange",this._onOrientationChange)}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.fullscreen),this.appendChild(".xgplayer-icon",e.exitFullscreen)}},{key:"toggleFullScreen",value:function(e){e&&(e.preventDefault(),e.stopPropagation());var t=this.player,n=this.config;!0===n.useCssFullscreen||"function"==typeof n.useCssFullscreen&&n.useCssFullscreen()?(t.fullscreen?t.exitCssFullscreen():t.getCssFullscreen(),this.animate(t.fullscreen)):n.rotateFullscreen?(t.fullscreen?t.exitRotateFullscreen():t.getRotateFullscreen(),this.animate(t.fullscreen)):n.switchCallback&&"function"==typeof n.switchCallback?n.switchCallback(t.fullscreen):t.fullscreen?t.exitFullscreen():t.getFullscreen().catch((function(e){}))}},{key:"animate",value:function(e){e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.topBackIcon&&(e?(this.topBackIcon.show(),this.hide()):(this.topBackIcon.hide(),this.show()))}},{key:"show",value:function(){wT(gT(n.prototype),"show",this).call(this)}},{key:"hide",value:function(){wT(gT(n.prototype),"hide",this).call(this)}},{key:"render",value:function(){if(!this.config.disable){return'<xg-icon class="xgplayer-fullscreen">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(vA(this,"FULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}}],[{key:"pluginName",get:function(){return"fullscreen"}},{key:"defaultConfig",get:function(){return{position:zx.CONTROLS_RIGHT,index:0,useCssFullscreen:!1,rotateFullscreen:!1,switchCallback:null,target:null,disable:!1,needBackIcon:!1}}}]),n}(fI);function _I(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M940.632 837.632l-72.192-72.192c65.114-64.745 105.412-154.386 105.412-253.44s-40.299-188.695-105.396-253.424l-0.016-0.016 72.192-72.192c83.639 83.197 135.401 198.37 135.401 325.632s-51.762 242.434-135.381 325.612l-0.020 0.020zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function bI(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function wI(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M920.4 439.808l-108.544-109.056-72.704 72.704 109.568 108.544-109.056 108.544 72.704 72.704 108.032-109.568 108.544 109.056 72.704-72.704-109.568-108.032 109.056-108.544-72.704-72.704-108.032 109.568z"></path>\n</svg>\n',"image/svg+xml").firstChild}var kI=function(e){vT(n,e);var t=bT(n);function n(){var e,r;hT(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return pT(mT(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"onBarMousedown",(function(e){var t=mT(r).player,n=qm(r).call(r,".xgplayer-bar");bC.event(e);var i=n.getBoundingClientRect(),o=bC.getEventPos(e,t.zoom),a=i.height-(o.clientY-i.top);if(o.h=a,o.barH=i.height,r.pos=o,!(a<-2))return r.updateVolumePos(a,e),document.addEventListener("mouseup",r.onBarMouseUp),r._d.isStart=!0,!1})),pT(mT(r),"onBarMouseMove",(function(e){var t=mT(r)._d;if(t.isStart){var n=mT(r),i=n.pos,o=n.player;e.preventDefault(),e.stopPropagation(),bC.event(e);var a=bC.getEventPos(e,o.zoom);t.isMoving=!0;var s=i.h-a.clientY+i.clientY;s>i.barH||r.updateVolumePos(s,e)}})),pT(mT(r),"onBarMouseUp",(function(e){bC.event(e),document.removeEventListener("mouseup",r.onBarMouseUp);var t=mT(r)._d;t.isStart=!1,t.isMoving=!1})),pT(mT(r),"onMouseenter",(function(e){r._d.isActive=!0,r.focus(),r.emit("icon_mouseenter",{pluginName:r.pluginName})})),pT(mT(r),"onMouseleave",(function(e){r._d.isActive=!1,r.unFocus(100,!1,e),r.emit("icon_mouseleave",{pluginName:r.pluginName})})),pT(mT(r),"onVolumeChange",(function(e){if(r.player){var t=r.player,n=t.muted,i=t.volume;r._d.isMoving||(qm(r).call(r,".xgplayer-drag").style.height=n||0===i?"4px":"".concat(100*i,"%"),r.config.showValueLabel&&r.updateVolumeValue()),r.animate(n,i)}})),r}return dT(n,[{key:"registerIcons",value:function(){return{volumeSmall:{icon:bI,class:"xg-volume-small"},volumeLarge:{icon:_I,class:"xg-volume"},volumeMuted:{icon:wI,class:"xg-volume-mute"}}}},{key:"afterCreate",value:function(){var e=this;if(this._timerId=null,this._d={isStart:!1,isMoving:!1,isActive:!1},!this.config.disable){this.initIcons();var t=this.playerConfig,n=t.commonStyle,r=t.volume;if(n.volumeColor)qm(this).call(this,".xgplayer-drag").style.backgroundColor=n.volumeColor;this.changeMutedHandler=this.hook("mutedChange",(function(t){e.changeMuted(t)}),{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this._onMouseenterHandler=this.hook("mouseenter",this.onMouseenter),this._onMouseleaveHandler=this.hook("mouseleave",this.onMouseleave),"mobile"!==xC.device&&"mobile"!==this.playerConfig.isMobileSimulateMode&&(this.bind("mouseenter",this._onMouseenterHandler),this.bind(["blur","mouseleave"],this._onMouseleaveHandler),this.bind(".xgplayer-slider","mousedown",this.onBarMousedown),this.bind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.bind(".xgplayer-slider","mouseup",this.onBarMouseUp)),this.bind(".xgplayer-icon",["touchend","click"],this.changeMutedHandler),this.on(HC,this.onVolumeChange),this.once(jC,this.onVolumeChange),"Number"!==bC.typeOf(r)&&(this.player.volume=this.config.default),this.onVolumeChange()}}},{key:"updateVolumePos",value:function(e,t){var n=this.player,r=qm(this).call(this,".xgplayer-drag"),i=qm(this).call(this,".xgplayer-bar");if(i&&r){var o=rE(e/i.getBoundingClientRect().height*1e3,10);r.style.height="".concat(e,"px");var a=Math.max(Math.min(o/1e3,1),0),s={volume:{from:n.volume,to:a}};n.muted&&(s.muted={from:!0,to:!1}),this.emitUserAction(t,"change_volume",{muted:n.muted,volume:n.volume,props:s}),n.volume=Math.max(Math.min(o/1e3,1),0),n.muted&&(n.muted=!1),this.config.showValueLabel&&this.updateVolumeValue()}}},{key:"updateVolumeValue",value:function(){var e=this.player,t=e.volume,n=e.muted,r=qm(this).call(this,".xgplayer-value-label"),i=Math.max(Math.min(t,1),0);r.innerText=n?0:Math.ceil(100*i)}},{key:"focus",value:function(){this.player.focus({autoHide:!1}),this._timerId&&(bC.clearTimeout(this,this._timerId),this._timerId=null),bC.addClass(this.root,"slide-show")}},{key:"unFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=arguments.length>2?arguments[2]:void 0,i=this._d,o=this.player;i.isActive||(this._timerId&&(bC.clearTimeout(this,this._timerId),this._timerId=null),this._timerId=bC.setTimeout(this,(function(){i.isActive||(n?o.blur():o.focus(),bC.removeClass(e.root,"slide-show"),i.isStart&&e.onBarMouseUp(r)),e._timerId=null}),t))}},{key:"changeMuted",value:function(e){e&&e.stopPropagation();var t=this.player;this._d.isStart&&this.onBarMouseUp(e),this.emitUserAction(e,"change_muted",{muted:t.muted,volume:t.volume,props:{muted:{from:t.muted,to:!t.muted}}}),t.volume>0&&(t.muted=!t.muted),t.volume<.01&&(t.volume=this.config.miniVolume)}},{key:"animate",value:function(e,t){e||0===t?this.setAttr("data-state","mute"):t<.5&&this.icons.volumeSmall?this.setAttr("data-state","small"):this.setAttr("data-state","normal")}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.volumeSmall),this.appendChild(".xgplayer-icon",e.volumeLarge),this.appendChild(".xgplayer-icon",e.volumeMuted)}},{key:"destroy",value:function(){this._timerId&&(bC.clearTimeout(this,this._timerId),this._timerId=null),this.unbind("mouseenter",this.onMouseenter),this.unbind(["blur","mouseleave"],this.onMouseleave),this.unbind(".xgplayer-slider","mousedown",this.onBarMousedown),this.unbind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.unbind(".xgplayer-slider","mouseup",this.onBarMouseUp),document.removeEventListener("mouseup",this.onBarMouseUp),this.unbind(".xgplayer-icon","mobile"===xC.device?"touchend":"click",this.changeMutedHandler)}},{key:"render",value:function(){var e;if(!this.config.disable){var t=this.config.default||this.player.volume,n=this.config.showValueLabel;return Fm(e='\n    <xg-icon class="xgplayer-volume" data-state="normal">\n      <div class="xgplayer-icon">\n      </div>\n      <xg-slider class="xgplayer-slider">\n        '.concat(n?'<div class="xgplayer-value-label">'.concat(100*t,"</div>"):"",'\n        <div class="xgplayer-bar">\n          <xg-drag class="xgplayer-drag" style="height: ')).call(e,100*t,'%"></xg-drag>\n        </div>\n      </xg-slider>\n    </xg-icon>')}}}],[{key:"pluginName",get:function(){return"volume"}},{key:"defaultConfig",get:function(){return{position:zx.CONTROLS_RIGHT,index:1,disable:!1,showValueLabel:!1,default:.6,miniVolume:.2}}}]),n}(qx);function EI(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}function TI(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M9.4998 7.7C9.77595 7.7 9.9998 7.47614 9.9998 7.2V6.5C9.9998 6.22386 9.77595 6 9.4998 6H5.5402L5.52754 6.00016H5.5C5.22386 6.00016 5 6.22401 5 6.50016V10.4598C5 10.7359 5.22386 10.9598 5.5 10.9598H6.2C6.47614 10.9598 6.7 10.7359 6.7 10.4598V8.83005L8.76983 10.9386C8.96327 11.1357 9.27984 11.1386 9.47691 10.9451L9.97645 10.4548C10.1735 10.2613 10.1764 9.94476 9.983 9.7477L7.97289 7.7H9.4998Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}var SI="picture-in-picture",CI="inline",xI="fullscreen",PI=function(e){vT(n,e);var t=bT(n);function n(){var e,r;hT(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return pT(mT(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"switchPIP",(function(e){if(!r.isPIPAvailable())return!1;e.stopPropagation&&e.stopPropagation(),r.isPip?(r.exitPIP(),r.emitUserAction(e,"change_pip",{props:"pip",from:!0,to:!1}),r.setAttr("data-state","normal")):4===r.player.media.readyState&&(r.requestPIP(),r.emitUserAction(e,"change_pip",{props:"pip",from:!1,to:!0}),r.setAttr("data-state","pip"))})),r}return dT(n,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.pip&&(e.config.showIcon=e.player.config.pip)}},{key:"afterCreate",value:function(){var e=this;this.isPIPAvailable()&&(wT(gT(n.prototype),"afterCreate",this).call(this),this.pMode=CI,this.initPipEvents(),this.config.showIcon&&this.initIcons(),this.once(JC,(function(){e.config.showIcon&&(bC.removeClass(qm(e).call(e,".xgplayer-icon"),"xg-icon-disable"),e.bind("click",e.switchPIP))})))}},{key:"registerIcons",value:function(){return{pipIcon:{icon:EI,class:"xg-get-pip"},pipIconExit:{icon:TI,class:"xg-exit-pip"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.pipIcon),this.appendChild(".xgplayer-icon",e.pipIconExit)}},{key:"initPipEvents",value:function(){var e=this,t=this.player;this.leavePIPCallback=function(){var n=t.paused;bC.setTimeout(e,(function(){!n&&t.mediaPlay()}),0),!n&&t.mediaPlay(),e.setAttr("data-state","normal"),t.emit(ux,!1)},this.enterPIPCallback=function(n){t.emit(ux,!0),e.pipWindow=n.pictureInPictureWindow,e.setAttr("data-state","pip")},this.onWebkitpresentationmodechanged=function(n){var r=t.media.webkitPresentationMode;e.pMode===xI&&r!==xI&&t.onFullscreenChange(null,!1),e.pMode=r,r===SI?e.enterPIPCallback(n):r===CI&&e.leavePIPCallback(n)},t.media&&(t.media.addEventListener("enterpictureinpicture",this.enterPIPCallback),t.media.addEventListener("leavepictureinpicture",this.leavePIPCallback),n.checkWebkitSetPresentationMode(t.media)&&t.media.addEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged))}},{key:"requestPIP",value:function(){var e=this.player,t=this.playerConfig;if(this.isPIPAvailable()&&!this.isPip)try{var r=t.poster;return r&&(e.media.poster="String"===bC.typeOf(r)?r:r.poster),n.checkWebkitSetPresentationMode(e.media)?e.media.webkitSetPresentationMode("picture-in-picture"):e.media.requestPictureInPicture(),!0}catch(i){return console.error("requestPiP",i),!1}}},{key:"exitPIP",value:function(){var e=this.player;try{return this.isPIPAvailable()&&this.isPip&&(n.checkWebkitSetPresentationMode(e.media)?e.media.webkitSetPresentationMode("inline"):document.exitPictureInPicture()),!0}catch(t){return console.error("exitPIP",t),!1}}},{key:"isPip",get:function(){var e=this.player;return document.pictureInPictureElement&&document.pictureInPictureElement===e.media||e.media.webkitPresentationMode===SI}},{key:"isPIPAvailable",value:function(){var e=this.player.media;return("Boolean"!==bC.typeOf(document.pictureInPictureEnabled)||document.pictureInPictureEnabled)&&("Boolean"===bC.typeOf(e.disablePictureInPicture)&&!e.disablePictureInPicture||e.webkitSupportsPresentationMode&&"Function"===bC.typeOf(e.webkitSetPresentationMode))}},{key:"destroy",value:function(){wT(gT(n.prototype),"destroy",this).call(this);var e=this.player;e.media.removeEventListener("enterpictureinpicture",this.enterPIPCallback),e.media.removeEventListener("leavepictureinpicture",this.leavePIPCallback),n.checkWebkitSetPresentationMode(e.media)&&e.media.removeEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged),this.exitPIP(),this.unbind("click",this.btnClick)}},{key:"render",value:function(){if(this.config.showIcon&&this.isPIPAvailable())return'<xg-icon class="xgplayer-pip">\n      <div class="xgplayer-icon xg-icon-disable">\n      </div>\n      '.concat(vA(this,"PIP",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"pip"}},{key:"defaultConfig",get:function(){return{position:zx.CONTROLS_RIGHT,index:6,showIcon:!1}}},{key:"checkWebkitSetPresentationMode",value:function(e){return"function"==typeof e.webkitSetPresentationMode}}]),n}(fI),AI=l,II=vt("iterator"),RI=!AI((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,r){t.delete("b"),n+=r+e})),!e.toJSON||!t.size&&true||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[II]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host})),OI=_,DI=2147483647,MI=/[^\0-\u007E]/,LI=/[.\u3002\uFF0E\uFF61]/g,NI="Overflow: input needs wider integers to process",UI=RangeError,FI=OI(LI.exec),BI=Math.floor,VI=String.fromCharCode,HI=OI("".charCodeAt),jI=OI([].join),zI=OI([].push),KI=OI("".replace),WI=OI("".split),GI=OI("".toLowerCase),qI=function(e){return e+22+75*(e<26)},YI=function(e,t,n){var r=0;for(e=n?BI(e/700):e>>1,e+=BI(e/t);e>455;)e=BI(e/35),r+=36;return BI(r+36*e/(e+38))},XI=function(e){var t=[];e=function(e){for(var t=[],n=0,r=e.length;n<r;){var i=HI(e,n++);if(i>=55296&&i<=56319&&n<r){var o=HI(e,n++);56320==(64512&o)?zI(t,((1023&i)<<10)+(1023&o)+65536):(zI(t,i),n--)}else zI(t,i)}return t}(e);var n,r,i=e.length,o=128,a=0,s=72;for(n=0;n<e.length;n++)(r=e[n])<128&&zI(t,VI(r));var l=t.length,u=l;for(l&&zI(t,"-");u<i;){var c=DI;for(n=0;n<e.length;n++)(r=e[n])>=o&&r<c&&(c=r);var h=u+1;if(c-o>BI((DI-a)/h))throw UI(NI);for(a+=(c-o)*h,o=c,n=0;n<e.length;n++){if((r=e[n])<o&&++a>DI)throw UI(NI);if(r==o){for(var f=a,d=36;;){var p=d<=s?1:d>=s+26?26:d-s;if(f<p)break;var v=f-p,g=36-p;zI(t,VI(qI(p+v%g))),f=BI(v/g),d+=36}zI(t,VI(qI(f))),s=YI(a,h,u==l),a=0,u++}}a++,o++}return jI(t,"")},JI=On,QI=s,$I=L,ZI=_,eR=O,tR=RI,nR=ao,rR=lo,iR=L_,oR=Do,aR=Wl,sR=Xo,lR=$f,uR=I,cR=tt,hR=Zt,fR=ir,dR=an,pR=re,vR=Jr,gR=ji,yR=j,mR=nf,_R=Xh,bR=ud,wR=MS,kR=vt("iterator"),ER="URLSearchParams",TR=ER+"Iterator",SR=sR.set,CR=sR.getterFor(ER),xR=sR.getterFor(TR),PR=Object.getOwnPropertyDescriptor,AR=function(e){if(!eR)return QI[e];var t=PR(QI,e);return t&&t.value},IR=AR("fetch"),RR=AR("Request"),OR=AR("Headers"),DR=RR&&RR.prototype,MR=OR&&OR.prototype,LR=QI.RegExp,NR=QI.TypeError,UR=QI.decodeURIComponent,FR=QI.encodeURIComponent,BR=ZI("".charAt),VR=ZI([].join),HR=ZI([].push),jR=ZI("".replace),zR=ZI([].shift),KR=ZI([].splice),WR=ZI("".split),GR=ZI("".slice),qR=/\+/g,YR=Array(4),XR=function(e){return YR[e-1]||(YR[e-1]=LR("((?:%[\\da-f]{2}){"+e+"})","gi"))},JR=function(e){try{return UR(e)}catch(PH){return e}},QR=function(e){var t=jR(e,qR," "),n=4;try{return UR(t)}catch(PH){for(;n;)t=jR(t,XR(n--),JR);return t}},$R=/[!'()~]|%20/g,ZR={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},eO=function(e){return ZR[e]},tO=function(e){return jR(FR(e),$R,eO)},nO=aR((function(e,t){SR(this,{type:TR,iterator:mR(CR(e).entries),kind:t})}),"Iterator",(function(){var e=xR(this),t=e.kind,n=e.iterator.next(),r=n.value;return n.done||(n.value="keys"===t?r.key:"values"===t?r.value:[r.key,r.value]),n}),!0),rO=function(e){this.entries=[],this.url=null,void 0!==e&&(pR(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===BR(e,0)?GR(e,1):e:vR(e)))};rO.prototype={type:ER,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,r,i,o,a,s,l=_R(e);if(l)for(n=(t=mR(e,l)).next;!(r=$I(n,t)).done;){if(o=(i=mR(dR(r.value))).next,(a=$I(o,i)).done||(s=$I(o,i)).done||!$I(o,i).done)throw NR("Expected sequence with length 2");HR(this.entries,{key:vR(a.value),value:vR(s.value)})}else for(var u in e)cR(e,u)&&HR(this.entries,{key:u,value:vR(e[u])})},parseQuery:function(e){if(e)for(var t,n,r=WR(e,"&"),i=0;i<r.length;)(t=r[i++]).length&&(n=WR(t,"="),HR(this.entries,{key:QR(zR(n)),value:QR(VR(n,"="))}))},serialize:function(){for(var e,t=this.entries,n=[],r=0;r<t.length;)e=t[r++],HR(n,tO(e.key)+"="+tO(e.value));return VR(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var iO=function(){lR(this,oO);var e=SR(this,new rO(arguments.length>0?arguments[0]:void 0));eR||(this.length=e.entries.length)},oO=iO.prototype;if(iR(oO,{append:function(e,t){bR(arguments.length,2);var n=CR(this);HR(n.entries,{key:vR(e),value:vR(t)}),eR||this.length++,n.updateURL()},delete:function(e){bR(arguments.length,1);for(var t=CR(this),n=t.entries,r=vR(e),i=0;i<n.length;)n[i].key===r?KR(n,i,1):i++;eR||(this.length=n.length),t.updateURL()},get:function(e){bR(arguments.length,1);for(var t=CR(this).entries,n=vR(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){bR(arguments.length,1);for(var t=CR(this).entries,n=vR(e),r=[],i=0;i<t.length;i++)t[i].key===n&&HR(r,t[i].value);return r},has:function(e){bR(arguments.length,1);for(var t=CR(this).entries,n=vR(e),r=0;r<t.length;)if(t[r++].key===n)return!0;return!1},set:function(e,t){bR(arguments.length,1);for(var n,r=CR(this),i=r.entries,o=!1,a=vR(e),s=vR(t),l=0;l<i.length;l++)(n=i[l]).key===a&&(o?KR(i,l--,1):(o=!0,n.value=s));o||HR(i,{key:a,value:s}),eR||(this.length=i.length),r.updateURL()},sort:function(){var e=CR(this);wR(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){for(var t,n=CR(this).entries,r=hR(e,arguments.length>1?arguments[1]:void 0),i=0;i<n.length;)r((t=n[i++]).value,t.key,this)},keys:function(){return new nO(this,"keys")},values:function(){return new nO(this,"values")},entries:function(){return new nO(this,"entries")}},{enumerable:!0}),nR(oO,kR,oO.entries,{name:"entries"}),nR(oO,"toString",(function(){return CR(this).serialize()}),{enumerable:!0}),eR&&rR(oO,"size",{get:function(){return CR(this).entries.length},configurable:!0,enumerable:!0}),oR(iO,ER),JI({global:!0,constructor:!0,forced:!tR},{URLSearchParams:iO}),!tR&&uR(OR)){var aO=ZI(MR.has),sO=ZI(MR.set),lO=function(e){if(pR(e)){var t,n=e.body;if(fR(n)===ER)return t=e.headers?new OR(e.headers):new OR,aO(t,"content-type")||sO(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),gR(e,{body:yR(0,vR(n)),headers:yR(0,t)})}return e};if(uR(IR)&&JI({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return IR(e,arguments.length>1?lO(arguments[1]):{})}}),uR(RR)){var uO=function(e){return lR(this,DR),new RR(e,arguments.length>1?lO(arguments[1]):{})};DR.constructor=uO,uO.prototype=DR,JI({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:uO})}}var cO,hO=On,fO=O,dO=RI,pO=s,vO=Zt,gO=_,yO=ao,mO=lo,_O=$f,bO=tt,wO=UT,kO=Ty,EO=$i,TO=pc.codeAt,SO=function(e){var t,n,r=[],i=WI(KI(GI(e),LI,"."),".");for(t=0;t<i.length;t++)n=i[t],zI(r,FI(MI,n)?"xn--"+XI(n):n);return jI(r,".")},CO=Jr,xO=Do,PO=ud,AO={URLSearchParams:iO,getState:CR},IO=Xo,RO=IO.set,OO=IO.getterFor("URL"),DO=AO.URLSearchParams,MO=AO.getState,LO=pO.URL,NO=pO.TypeError,UO=pO.parseInt,FO=Math.floor,BO=Math.pow,VO=gO("".charAt),HO=gO(/./.exec),jO=gO([].join),zO=gO(1..toString),KO=gO([].pop),WO=gO([].push),GO=gO("".replace),qO=gO([].shift),YO=gO("".split),XO=gO("".slice),JO=gO("".toLowerCase),QO=gO([].unshift),$O="Invalid scheme",ZO="Invalid host",eD="Invalid port",tD=/[a-z]/i,nD=/[\d+-.a-z]/i,rD=/\d/,iD=/^0x/i,oD=/^[0-7]+$/,aD=/^\d+$/,sD=/^[\da-f]+$/i,lD=/[\0\t\n\r #%/:<>?@[\\\]^|]/,uD=/[\0\t\n\r #/:<>?@[\\\]^|]/,cD=/^[\u0000-\u0020]+/,hD=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,fD=/[\t\n\r]/g,dD=function(e){var t,n,r,i;if("number"==typeof e){for(t=[],n=0;n<4;n++)QO(t,e%256),e=FO(e/256);return jO(t,".")}if("object"==typeof e){for(t="",r=function(e){for(var t=null,n=1,r=null,i=0,o=0;o<8;o++)0!==e[o]?(i>n&&(t=r,n=i),r=null,i=0):(null===r&&(r=o),++i);return i>n&&(t=r,n=i),t}(e),n=0;n<8;n++)i&&0===e[n]||(i&&(i=!1),r===n?(t+=n?":":"::",i=!0):(t+=zO(e[n],16),n<7&&(t+=":")));return"["+t+"]"}return e},pD={},vD=wO({},pD,{" ":1,'"':1,"<":1,">":1,"`":1}),gD=wO({},vD,{"#":1,"?":1,"{":1,"}":1}),yD=wO({},gD,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),mD=function(e,t){var n=TO(e,0);return n>32&&n<127&&!bO(t,e)?e:encodeURIComponent(e)},_D={ftp:21,file:null,http:80,https:443,ws:80,wss:443},bD=function(e,t){var n;return 2==e.length&&HO(tD,VO(e,0))&&(":"==(n=VO(e,1))||!t&&"|"==n)},wD=function(e){var t;return e.length>1&&bD(XO(e,0,2))&&(2==e.length||"/"===(t=VO(e,2))||"\\"===t||"?"===t||"#"===t)},kD=function(e){return"."===e||"%2e"===JO(e)},ED={},TD={},SD={},CD={},xD={},PD={},AD={},ID={},RD={},OD={},DD={},MD={},LD={},ND={},UD={},FD={},BD={},VD={},HD={},jD={},zD={},KD=function(e,t,n){var r,i,o,a=CO(e);if(t){if(i=this.parse(a))throw NO(i);this.searchParams=null}else{if(void 0!==n&&(r=new KD(n,!0)),i=this.parse(a,null,r))throw NO(i);(o=MO(new DO)).bindURL(this),this.searchParams=o}};KD.prototype={type:"URL",parse:function(e,t,n){var r,i,o,a,s,l=this,u=t||ED,c=0,h="",f=!1,d=!1,p=!1;for(e=CO(e),t||(l.scheme="",l.username="",l.password="",l.host=null,l.port=null,l.path=[],l.query=null,l.fragment=null,l.cannotBeABaseURL=!1,e=GO(e,cD,""),e=GO(e,hD,"$1")),e=GO(e,fD,""),r=kO(e);c<=r.length;){switch(i=r[c],u){case ED:if(!i||!HO(tD,i)){if(t)return $O;u=SD;continue}h+=JO(i),u=TD;break;case TD:if(i&&(HO(nD,i)||"+"==i||"-"==i||"."==i))h+=JO(i);else{if(":"!=i){if(t)return $O;h="",u=SD,c=0;continue}if(t&&(l.isSpecial()!=bO(_D,h)||"file"==h&&(l.includesCredentials()||null!==l.port)||"file"==l.scheme&&!l.host))return;if(l.scheme=h,t)return void(l.isSpecial()&&_D[l.scheme]==l.port&&(l.port=null));h="","file"==l.scheme?u=ND:l.isSpecial()&&n&&n.scheme==l.scheme?u=CD:l.isSpecial()?u=ID:"/"==r[c+1]?(u=xD,c++):(l.cannotBeABaseURL=!0,WO(l.path,""),u=HD)}break;case SD:if(!n||n.cannotBeABaseURL&&"#"!=i)return $O;if(n.cannotBeABaseURL&&"#"==i){l.scheme=n.scheme,l.path=EO(n.path),l.query=n.query,l.fragment="",l.cannotBeABaseURL=!0,u=zD;break}u="file"==n.scheme?ND:PD;continue;case CD:if("/"!=i||"/"!=r[c+1]){u=PD;continue}u=RD,c++;break;case xD:if("/"==i){u=OD;break}u=VD;continue;case PD:if(l.scheme=n.scheme,i==cO)l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=EO(n.path),l.query=n.query;else if("/"==i||"\\"==i&&l.isSpecial())u=AD;else if("?"==i)l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=EO(n.path),l.query="",u=jD;else{if("#"!=i){l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=EO(n.path),l.path.length--,u=VD;continue}l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=EO(n.path),l.query=n.query,l.fragment="",u=zD}break;case AD:if(!l.isSpecial()||"/"!=i&&"\\"!=i){if("/"!=i){l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,u=VD;continue}u=OD}else u=RD;break;case ID:if(u=RD,"/"!=i||"/"!=VO(h,c+1))continue;c++;break;case RD:if("/"!=i&&"\\"!=i){u=OD;continue}break;case OD:if("@"==i){f&&(h="%40"+h),f=!0,o=kO(h);for(var v=0;v<o.length;v++){var g=o[v];if(":"!=g||p){var y=mD(g,yD);p?l.password+=y:l.username+=y}else p=!0}h=""}else if(i==cO||"/"==i||"?"==i||"#"==i||"\\"==i&&l.isSpecial()){if(f&&""==h)return"Invalid authority";c-=kO(h).length+1,h="",u=DD}else h+=i;break;case DD:case MD:if(t&&"file"==l.scheme){u=FD;continue}if(":"!=i||d){if(i==cO||"/"==i||"?"==i||"#"==i||"\\"==i&&l.isSpecial()){if(l.isSpecial()&&""==h)return ZO;if(t&&""==h&&(l.includesCredentials()||null!==l.port))return;if(a=l.parseHost(h))return a;if(h="",u=BD,t)return;continue}"["==i?d=!0:"]"==i&&(d=!1),h+=i}else{if(""==h)return ZO;if(a=l.parseHost(h))return a;if(h="",u=LD,t==MD)return}break;case LD:if(!HO(rD,i)){if(i==cO||"/"==i||"?"==i||"#"==i||"\\"==i&&l.isSpecial()||t){if(""!=h){var m=UO(h,10);if(m>65535)return eD;l.port=l.isSpecial()&&m===_D[l.scheme]?null:m,h=""}if(t)return;u=BD;continue}return eD}h+=i;break;case ND:if(l.scheme="file","/"==i||"\\"==i)u=UD;else{if(!n||"file"!=n.scheme){u=VD;continue}if(i==cO)l.host=n.host,l.path=EO(n.path),l.query=n.query;else if("?"==i)l.host=n.host,l.path=EO(n.path),l.query="",u=jD;else{if("#"!=i){wD(jO(EO(r,c),""))||(l.host=n.host,l.path=EO(n.path),l.shortenPath()),u=VD;continue}l.host=n.host,l.path=EO(n.path),l.query=n.query,l.fragment="",u=zD}}break;case UD:if("/"==i||"\\"==i){u=FD;break}n&&"file"==n.scheme&&!wD(jO(EO(r,c),""))&&(bD(n.path[0],!0)?WO(l.path,n.path[0]):l.host=n.host),u=VD;continue;case FD:if(i==cO||"/"==i||"\\"==i||"?"==i||"#"==i){if(!t&&bD(h))u=VD;else if(""==h){if(l.host="",t)return;u=BD}else{if(a=l.parseHost(h))return a;if("localhost"==l.host&&(l.host=""),t)return;h="",u=BD}continue}h+=i;break;case BD:if(l.isSpecial()){if(u=VD,"/"!=i&&"\\"!=i)continue}else if(t||"?"!=i)if(t||"#"!=i){if(i!=cO&&(u=VD,"/"!=i))continue}else l.fragment="",u=zD;else l.query="",u=jD;break;case VD:if(i==cO||"/"==i||"\\"==i&&l.isSpecial()||!t&&("?"==i||"#"==i)){if(".."===(s=JO(s=h))||"%2e."===s||".%2e"===s||"%2e%2e"===s?(l.shortenPath(),"/"==i||"\\"==i&&l.isSpecial()||WO(l.path,"")):kD(h)?"/"==i||"\\"==i&&l.isSpecial()||WO(l.path,""):("file"==l.scheme&&!l.path.length&&bD(h)&&(l.host&&(l.host=""),h=VO(h,0)+":"),WO(l.path,h)),h="","file"==l.scheme&&(i==cO||"?"==i||"#"==i))for(;l.path.length>1&&""===l.path[0];)qO(l.path);"?"==i?(l.query="",u=jD):"#"==i&&(l.fragment="",u=zD)}else h+=mD(i,gD);break;case HD:"?"==i?(l.query="",u=jD):"#"==i?(l.fragment="",u=zD):i!=cO&&(l.path[0]+=mD(i,pD));break;case jD:t||"#"!=i?i!=cO&&("'"==i&&l.isSpecial()?l.query+="%27":l.query+="#"==i?"%23":mD(i,pD)):(l.fragment="",u=zD);break;case zD:i!=cO&&(l.fragment+=mD(i,vD))}c++}},parseHost:function(e){var t,n,r;if("["==VO(e,0)){if("]"!=VO(e,e.length-1))return ZO;if(t=function(e){var t,n,r,i,o,a,s,l=[0,0,0,0,0,0,0,0],u=0,c=null,h=0,f=function(){return VO(e,h)};if(":"==f()){if(":"!=VO(e,1))return;h+=2,c=++u}for(;f();){if(8==u)return;if(":"!=f()){for(t=n=0;n<4&&HO(sD,f());)t=16*t+UO(f(),16),h++,n++;if("."==f()){if(0==n)return;if(h-=n,u>6)return;for(r=0;f();){if(i=null,r>0){if(!("."==f()&&r<4))return;h++}if(!HO(rD,f()))return;for(;HO(rD,f());){if(o=UO(f(),10),null===i)i=o;else{if(0==i)return;i=10*i+o}if(i>255)return;h++}l[u]=256*l[u]+i,2!=++r&&4!=r||u++}if(4!=r)return;break}if(":"==f()){if(h++,!f())return}else if(f())return;l[u++]=t}else{if(null!==c)return;h++,c=++u}}if(null!==c)for(a=u-c,u=7;0!=u&&a>0;)s=l[u],l[u--]=l[c+a-1],l[c+--a]=s;else if(8!=u)return;return l}(XO(e,1,-1)),!t)return ZO;this.host=t}else if(this.isSpecial()){if(e=SO(e),HO(lD,e))return ZO;if(t=function(e){var t,n,r,i,o,a,s,l=YO(e,".");if(l.length&&""==l[l.length-1]&&l.length--,(t=l.length)>4)return e;for(n=[],r=0;r<t;r++){if(""==(i=l[r]))return e;if(o=10,i.length>1&&"0"==VO(i,0)&&(o=HO(iD,i)?16:8,i=XO(i,8==o?1:2)),""===i)a=0;else{if(!HO(10==o?aD:8==o?oD:sD,i))return e;a=UO(i,o)}WO(n,a)}for(r=0;r<t;r++)if(a=n[r],r==t-1){if(a>=BO(256,5-t))return null}else if(a>255)return null;for(s=KO(n),r=0;r<n.length;r++)s+=n[r]*BO(256,3-r);return s}(e),null===t)return ZO;this.host=t}else{if(HO(uD,e))return ZO;for(t="",n=kO(e),r=0;r<n.length;r++)t+=mD(n[r],pD);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"==this.scheme},includesCredentials:function(){return""!=this.username||""!=this.password},isSpecial:function(){return bO(_D,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"==this.scheme&&1==t&&bD(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,n=e.username,r=e.password,i=e.host,o=e.port,a=e.path,s=e.query,l=e.fragment,u=t+":";return null!==i?(u+="//",e.includesCredentials()&&(u+=n+(r?":"+r:"")+"@"),u+=dD(i),null!==o&&(u+=":"+o)):"file"==t&&(u+="//"),u+=e.cannotBeABaseURL?a[0]:a.length?"/"+jO(a,"/"):"",null!==s&&(u+="?"+s),null!==l&&(u+="#"+l),u},setHref:function(e){var t=this.parse(e);if(t)throw NO(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"==e)try{return new WD(e.path[0]).origin}catch(PH){return"null"}return"file"!=e&&this.isSpecial()?e+"://"+dD(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(CO(e)+":",ED)},getUsername:function(){return this.username},setUsername:function(e){var t=kO(CO(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<t.length;n++)this.username+=mD(t[n],yD)}},getPassword:function(){return this.password},setPassword:function(e){var t=kO(CO(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<t.length;n++)this.password+=mD(t[n],yD)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?dD(e):dD(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,DD)},getHostname:function(){var e=this.host;return null===e?"":dD(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,MD)},getPort:function(){var e=this.port;return null===e?"":CO(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""==(e=CO(e))?this.port=null:this.parse(e,LD))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+jO(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,BD))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""==(e=CO(e))?this.query=null:("?"==VO(e,0)&&(e=XO(e,1)),this.query="",this.parse(e,jD)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!=(e=CO(e))?("#"==VO(e,0)&&(e=XO(e,1)),this.fragment="",this.parse(e,zD)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var WD=function(e){var t=_O(this,GD),n=PO(arguments.length,1)>1?arguments[1]:void 0,r=RO(t,new KD(e,!1,n));fO||(t.href=r.serialize(),t.origin=r.getOrigin(),t.protocol=r.getProtocol(),t.username=r.getUsername(),t.password=r.getPassword(),t.host=r.getHost(),t.hostname=r.getHostname(),t.port=r.getPort(),t.pathname=r.getPathname(),t.search=r.getSearch(),t.searchParams=r.getSearchParams(),t.hash=r.getHash())},GD=WD.prototype,qD=function(e,t){return{get:function(){return OO(this)[e]()},set:t&&function(e){return OO(this)[t](e)},configurable:!0,enumerable:!0}};if(fO&&(mO(GD,"href",qD("serialize","setHref")),mO(GD,"origin",qD("getOrigin")),mO(GD,"protocol",qD("getProtocol","setProtocol")),mO(GD,"username",qD("getUsername","setUsername")),mO(GD,"password",qD("getPassword","setPassword")),mO(GD,"host",qD("getHost","setHost")),mO(GD,"hostname",qD("getHostname","setHostname")),mO(GD,"port",qD("getPort","setPort")),mO(GD,"pathname",qD("getPathname","setPathname")),mO(GD,"search",qD("getSearch","setSearch")),mO(GD,"searchParams",qD("getSearchParams")),mO(GD,"hash",qD("getHash","setHash"))),yO(GD,"toJSON",(function(){return OO(this).serialize()}),{enumerable:!0}),yO(GD,"toString",(function(){return OO(this).serialize()}),{enumerable:!0}),LO){var YD=LO.createObjectURL,XD=LO.revokeObjectURL;YD&&yO(WD,"createObjectURL",vO(YD,LO)),XD&&yO(WD,"revokeObjectURL",vO(XD,LO))}xO(WD,"URL"),hO({global:!0,constructor:!0,forced:!dO,sham:!fO},{URL:WD});var JD=On,QD=l,$D=ud,ZD=Jr,eM=RI,tM=ue("URL");JD({target:"URL",stat:!0,forced:!(eM&&QD((function(){tM.canParse()})))},{canParse:function(e){var t=$D(arguments.length,1),n=ZD(e),r=t<2||void 0===arguments[1]?void 0:ZD(arguments[1]);try{return!!new tM(n,r)}catch(PH){return!1}}});var nM=o(ie.URL),rM=function(){function e(t){hT(this,e),this.config=t.config,this.parent=t.root,this.root=bC.createDom("ul","",{},"xg-options-list xg-list-slide-scroll ".concat(this.config.className)),t.root.appendChild(this.root);var n=this.config.maxHeight;n&&this.setStyle({maxHeight:n}),this.onItemClick=this.onItemClick.bind(this),this.renderItemList();var r="mobile"===xC.device?"touchend":"click";this._delegates=qx.delegate.call(this,this.root,"li",r,this.onItemClick)}return dT(e,[{key:"renderItemList",value:function(e){var t,n=this,r=this.config,i=this.root;(e?r.data=e:e=r.data,r.style)&&Bw(t=fk(r.style)).call(t,(function(e){i.style[e]=r[e]}));e.length>0&&(this.attrKeys=fk(e[0])),this.root.innerHTML="",Bw(e).call(e,(function(e,t){var r=e.selected?"option-item selected":"option-item";e["data-index"]=t,n.root.appendChild(bC.createDom("li","<span>".concat(e.showText,"</span>"),e,r))}))}},{key:"onItemClick",value:function(e){e.delegateTarget||(e.delegateTarget=e.target);var t=e.delegateTarget;if(t&&bC.hasClass(t,"selected"))return!1;var n="function"==typeof this.config.onItemClick?this.config.onItemClick:null,r=this.root.querySelector(".selected");bC.addClass(t,"selected"),r&&bC.removeClass(r,"selected"),n(e,{from:r?this.getAttrObj(r,this.attrKeys):null,to:this.getAttrObj(t,this.attrKeys)})}},{key:"getAttrObj",value:function(e,t){if(!e||!t)return{};var n={};Bw(t).call(t,(function(t){n[t]=e.getAttribute(t)}));var r=e.getAttribute("data-index");return r&&(n.index=Number(r)),n}},{key:"show",value:function(){bC.removeClass(this.root,"hide"),bC.addClass(this.root,"active")}},{key:"hide",value:function(){bC.removeClass(this.root,"active"),bC.addClass(this.root,"hide")}},{key:"setStyle",value:function(e){var t=this;fk(e).forEach((function(n){t.root.style[n]=e[n]}))}},{key:"destroy",value:function(){var e;this._delegates&&(Bw(e=this._delegates).call(e,(function(e){e.destroy&&e.destroy()})),this._delegates=null);this.root.innerHTML=null,this.parent.removeChild(this.root),this.root=null}}]),e}(),iM=ie,oM=p;iM.JSON||(iM.JSON={stringify:JSON.stringify});var aM=function(e,t,n){return oM(iM.JSON.stringify,null,arguments)},sM=o(aM),lM=o(uo.f("toStringTag")),uM=On,cM=C;R.f;var hM=Hn,fM=Jr,dM=Xw,pM=Q,vM=Qw,gM=cM("".startsWith),yM=cM("".slice),mM=Math.min;uM({target:"String",proto:!0,forced:!vM("startsWith")},{startsWith:function(e){var t=fM(pM(this));dM(e);var n=hM(mM(arguments.length>1?arguments[1]:void 0,t.length)),r=fM(e);return gM?gM(t,r,n):yM(t,n,n+r.length)===r}});var _M,bM=Gc("String").startsWith,wM=ce,kM=bM,EM=String.prototype,TM=o((function(e){var t=e.startsWith;return"string"==typeof e||e===EM||wM(EM,e)&&t===EM.startsWith?kM:t})),SM={SEI:"sei",PLAYER_CREATE_FINISH:"playerCreateFinish",FALLBACK_ERROR:"fallbackError",FALLBACK:"fallback",AUTOPLAY_STARTED:"autoplayStarted",AUTOPLAY_PREVENTED:"autoplayWasPrevented",DOWNLOAD_SPEED_CHANGE:"downloadSpeedChange",FULLSCREEN_CHANGE:"fullscreenChange",CSS_FULLSCREEN_CHANGE:"cssFullscreenChange",MINI_STATE_CHANGE:"miniStateChange",DEFINITION_CHANGE:"definitionChange",BEFORE_DEFINITION_CHANGE:"beforeDefinitionChange",AFTER_DEFINITION_CHANGE:"afterDefinitionChange",VIDEO_RESIZE:"videoResize",PIP_CHANGE:"pipChange",USER_ACTION:"userAction",AUTOPLAY_UNMUTE:"autoplayUnmute",LONG_WAITING:"longWaiting",DEFINITION_FALLBACK:"definitionFallback",AUTOPLAY_FAIL:"autoplayFail",AUTOPLAY_SUCCESS:"autoplaySuccess",ERROR_REFRESH_CLICK:"errorRefreshClick",SOURCE_CHANGE:"sourceChange"},CM=Dm(Dm({},yx),SM),xM=(Ny(_M={},SM.SEI,{event:"core_event",shouldCallback:function(e){return"core.sei"===(null==e?void 0:e.eventName)}}),Ny(_M,SM.AUTOPLAY_STARTED,{event:YC}),Ny(_M,SM.AUTOPLAY_PREVENTED,{event:XC}),Ny(_M,SM.FULLSCREEN_CHANGE,{event:tx}),Ny(_M,SM.CSS_FULLSCREEN_CHANGE,{event:nx}),Ny(_M,SM.MINI_STATE_CHANGE,{event:rx}),Ny(_M,SM.DEFINITION_CHANGE,{event:ix}),Ny(_M,SM.BEFORE_DEFINITION_CHANGE,{event:ox}),Ny(_M,SM.AFTER_DEFINITION_CHANGE,{event:ax}),Ny(_M,SM.VIDEO_RESIZE,{event:lx}),Ny(_M,SM.PIP_CHANGE,{event:ux}),Ny(_M,SM.USER_ACTION,{event:fx}),Ny(_M,SM.DOWNLOAD_SPEED_CHANGE,{event:ex}),_M);var PM=/["'&<>]/;function AM(e,t){return function(e){if(sy(e))return e}(e)||function(e,t){var n=null==e?null:void 0!==oc&&cy(e)||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(ph(s).call(s,r.value),s.length!==t);l=!0);}catch(c){u=!0,i=c}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||Py(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var IM=O,RM=_,OM=yi,DM=ee,MM=RM(N.f),LM=RM([].push),NM=function(e){return function(t){for(var n,r=DM(t),i=OM(r),o=i.length,a=0,s=[];o>a;)n=i[a++],IM&&!MM(r,n)||LM(s,e?[n,r[n]]:r[n]);return s}},UM={entries:NM(!0),values:NM(!1)},FM=UM.entries;On({target:"Object",stat:!0},{entries:function(e){return FM(e)}});var BM=o(ie.Object.entries);var VM=Me,HM=$e,jM=q,zM=zn,KM=TypeError,WM=function(e){return function(t,n,r,i){VM(n);var o=HM(t),a=jM(o),s=zM(o),l=e?s-1:0,u=e?-1:1;if(r<2)for(;;){if(l in a){i=a[l],l+=u;break}if(l+=u,e?l<0:s<=l)throw KM("Reduce of empty array with no initial value")}for(;e?l>=0:s>l;l+=u)l in a&&(i=n(i,a[l],l,o));return i}},GM={left:WM(!1),right:WM(!0)}.left;On({target:"Array",proto:!0,forced:!Kf&&me>79&&me<83||!jc("reduce")},{reduce:function(e){var t=arguments.length;return GM(this,e,t,t>1?arguments[1]:void 0)}});var qM=Gc("Array").reduce,YM=ce,XM=qM,JM=Array.prototype,QM=o((function(e){var t=e.reduce;return e===JM||YM(JM,e)&&t===JM.reduce?XM:t}));var $M=function(){function e(t){var n;Iy(this,e),Ny(this,"definition",void 0),Ny(this,"source",void 0),Ny(this,"text",void 0),Ny(this,"fallbackUrl",void 0),Ny(this,"urls",void 0),Ny(this,"_currentUrlRef",void 0);var r=t.url,i=t.definition,o=t.text,a=t.source,s=t.fallbackUrls;this.definition=i,this.text=o,this.source=a,this.urls=Fm(n=[r]).call(n,Ay(null!=s?s:[])),this.fallbackUrl=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t={url:e[0],next:null},n=t,r=1;r<e.length;r++){var i=r===e.length-1?t:null,o={url:e[r],next:i};n.next=o,n=o}return t}(this.urls),this._currentUrlRef=this.fallbackUrl}return Ly(e,[{key:"url",get:function(){return this._currentUrlRef.url},set:function(e){this._currentUrlRef.url=e}},{key:"next",value:function(){var e=this._currentUrlRef.next;return e&&(this._currentUrlRef=e),this._currentUrlRef}}]),e}(),ZM=function(){function e(t,n,r){var i=this;Iy(this,e),Ny(this,"name",void 0),Ny(this,"text",void 0),Ny(this,"definitions",void 0),this.definitions=Bw(t).call(t,(function(e){return new $M(BT({},e,{source:i}))})),this.name=n,this.text=r}return Ly(e,[{key:"defaultDefinition",get:function(){return this.definitions[0]}},{key:"add",value:function(e){this.definitions.push(e)}}],[{key:"normalize",value:function(t){if(void 0===t)throw new Error('"input" cannot be undefined');var n=void 0,r=void 0,i=void 0;if("string"==typeof t?n=t:Array.isArray(t)?i=t:(n=t.url,r=t.fallbackUrls,i=t.sources),"string"==typeof n)return e.normalizeUrl(n,r);if(Array.isArray(i)){if(0===i.length)throw new Error('"sources" cannot be an empty array');return Bw(i).call(i,(function(t){var n;return new e(Bw(n=t.definitions).call(n,(function(e){return"string"==typeof e?{url:e,definition:"unknown"}:e})),t.name,t.text)}))}return[]}},{key:"normalizeUrl",value:function(t,n){var r=new e([],"unknown");return r.add(new $M({definition:"unknown",url:t,source:r,fallbackUrls:n})),[r]}}]),e}(),eL=function(){function e(t){var n,r,i,o;Iy(this,e),Ny(this,"defaultSource",void 0),Ny(this,"defaultDefinition",void 0),Ny(this,"maxFallbackRound",void 0),Ny(this,"_currentDefinition",void 0),Ny(this,"_sources",void 0),Ny(this,"_fallbackCount",0);var a=t.url,s=t.defaultDefinition,l=t.defaultSource,u=t.sources,c=t.maxFallbackRound;this.defaultDefinition=s,this.defaultSource=l,this.maxFallbackRound=null!=c?c:1,this._sources=u;var h=a?qm(this).call(this,a):null===(n=qm(r=this.sources).call(r,(function(e){return void 0===l||e.name===l})))||void 0===n?void 0:qm(i=n.definitions).call(i,(function(e){return void 0===s||e.definition===s}));this._currentDefinition=null!=h?h:null===(o=this._sources)||void 0===o||null===(o=o[0])||void 0===o||null===(o=o.definitions)||void 0===o?void 0:o[0]}return Ly(e,[{key:"sources",get:function(){return this._sources}},{key:"definition",get:function(){return this._currentDefinition}},{key:"source",get:function(){var e;return null===(e=this.definition)||void 0===e?void 0:e.source}},{key:"url",get:function(){var e;return null===(e=this.definition)||void 0===e?void 0:e.url},set:function(e){this.definition&&(this.definition.url=e)}},{key:"find",value:function(e,t){var n,r,i,o,a,s=this;if("string"==typeof e)return qm(o=QM(a=this.sources).call(a,(function(e,t){return Fm(e).call(e,t.definitions)}),[])).call(o,(function(t){return t.url===e}));var l=qm(n=this.sources).call(n,(function(t){var n,r;return t.name===(null!==(n=null==e?void 0:e.source)&&void 0!==n?n:null===(r=s.source)||void 0===r?void 0:r.name)})),u=null==l?void 0:qm(r=l.definitions).call(r,(function(t){var n,r;return t.definition===(null!==(n=null==e?void 0:e.definition)&&void 0!==n?n:null===(r=s.definition)||void 0===r?void 0:r.definition)}));return u||(null!=t&&t.fallbackToFirstDefinition&&null!=l&&null!==(i=l.definitions)&&void 0!==i&&i.length?null==l?void 0:l.definitions[0]:void 0)}},{key:"fallback",value:function(){var e;if(this._fallbackCount++,!(this._fallbackCount>=this.maxFallbackRound*this.definition.urls.length)){var t=null===(e=this._currentDefinition)||void 0===e?void 0:e.next();return null==t?void 0:t.url}this.resetFallback()}},{key:"switch",value:function(e){return this._currentDefinition=e,this.resetFallback(),e}},{key:"updateSources",value:function(e,t){var n,r,i,o,a;return this._sources=ZM.normalize(e),this._currentDefinition=null!==(n=qm(this).call(this,null!=t?t:{source:null!==(r=this.defaultSource)&&void 0!==r?r:null===(i=this._currentDefinition)||void 0===i?void 0:i.source.name,definition:null!==(o=this.defaultDefinition)&&void 0!==o?o:null===(a=this._currentDefinition)||void 0===a?void 0:a.definition}))&&void 0!==n?n:this._sources[0].definitions[0],this.resetFallback(),this}},{key:"resetFallback",value:function(){this._fallbackCount=0}}]),e}();function tL(){var e;"undefined"!=typeof Reflect&&rT?tL=qP(e=rT).call(e):tL=function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=JP(e)););return e}(e,t);if(r){var i=ym(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}};return tL.apply(this,arguments)}function nL(){return(new DOMParser).parseFromString('<svg width="150" height="162" viewBox="0 0 150 162" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path d="M127.718 112.831H6.13675C4.19663 112.831 2.25651 108.974 2.90322 104.475C3.54993 99.9761 8.07687 99.9761 13.8972 99.9761C11.9571 97.4052 9.37028 88.4069 15.8373 81.3369C22.3044 74.2668 25.5379 74.2668 34.5918 75.5526C33.9451 63.9837 47.526 34.4178 77.2744 35.0605C107.023 35.7032 116.077 54.3421 119.957 63.3404C129.011 62.6976 147.765 66.554 149.706 87.1214C151.258 103.575 135.694 111.117 127.718 112.831Z" fill="#F1F4F8"/>\n<path d="M44.9346 91.6022L43.6782 99.8032" stroke="#D0D6E0" stroke-width="2" stroke-linecap="round"/>\n<path d="M47.3545 125.695L48.6109 117.494" stroke="#D0D6E0" stroke-width="2" stroke-linecap="round"/>\n<path d="M31.5347 97.7378L38.7295 101.869" stroke="#D0D6E0" stroke-width="2" stroke-linecap="round"/>\n<path d="M60.7544 119.559L53.5595 115.428" stroke="#D0D6E0" stroke-width="2" stroke-linecap="round"/>\n<path d="M69.6755 44.5318L138.35 26.0209C139.115 25.8148 139.804 26.5367 139.563 27.2909L121.081 85.1146C120.982 85.4243 120.739 85.6665 120.429 85.7641L52.0645 107.28C51.2996 107.521 50.5766 106.809 50.8062 106.04L68.9776 45.2111C69.0768 44.879 69.3409 44.622 69.6755 44.5318Z" fill="#D0D6E0" stroke="#D0D6E0" stroke-width="4" stroke-linecap="round"/>\n<path d="M83.1285 52.7415C91.6454 54.3438 107.322 62.0019 101.894 79.8155" stroke="white" stroke-width="3" stroke-linecap="round"/>\n<path d="M84.138 62.3385C88.3894 63.1384 96.2148 66.9611 93.5053 75.8531" stroke="white" stroke-width="3" stroke-linecap="round"/>\n<ellipse cx="83.6417" cy="71.98" rx="3.08443" ry="3.07025" transform="rotate(12.6375 83.6417 71.98)" fill="white"/>\n<path d="M43.4699 110.358C39.5425 109.114 30.6316 108.203 26.4077 114.511C21.1278 122.397 36.5031 152.755 2.11053 160.141" stroke="#D0D6E0" stroke-width="2" stroke-linecap="round"/>\n</svg>\n',"image/svg+xml").firstChild}var rL=function(e){XP(n,e);var t=$P(n);function n(){var e,r;Iy(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return Ny(BP(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"_error",void 0),r}return Ly(n,[{key:"_errorTip",get:function(){var e,t,n,r=this.player.config.i18nManager,i=null===(e=this._error)||void 0===e?void 0:e.errorCode;return this.config.errorTipsText?r.normalize(this.config.errorTipsText):Fm(t="".concat(i?"[".concat(i,"] "):"")).call(t,null===(n=this._error)||void 0===n?void 0:n.message)}},{key:"afterCreate",value:function(){this._handleRefresh=this._handleRefresh.bind(this),this._focus=this._focus.bind(this),this._initEvents(),this._initIcons(),"mobile"===xC.device&&this.bind("touchend",this._focus),this.bind(".veplayer-error-refresh","click",this._handleRefresh)}},{key:"registerIcons",value:function(){return{error:{icon:nL,class:"veplayer-error-svg"}}}},{key:"updateLang",value:function(){this.config.showRefresh&&this._renderRefresh(),this._renderExtraTips()}},{key:"showError",value:function(e){this._error=e instanceof oA?e:sA(e),this.config.showErrorTip&&this._renderText(this._errorTip),this._renderExtraTips(),tL(JP(n.prototype),"show",this).call(this)}},{key:"hideError",value:function(){tL(JP(n.prototype),"hide",this).call(this)}},{key:"destroy",value:function(){"mobile"===xC.device&&this.unbind("touchend",this._focus),this.unbind(".veplayer-error-refresh","click",this._handleRefresh),this.off(CM.FALLBACK_ERROR,this.showError.bind(this)),this.off(BC,this.hideError.bind(this))}},{key:"render",value:function(){var e,t=this.player.config.i18nManager;return Fm(e='<veplayer-error class="veplayer-error">\n      <div class="veplayer-error-cover">\n        '.concat(this.config.showErrorImg?'<div class="veplayer-error-img"></div>':"",'\n        <div class="veplayer-error-tips"></div>\n        ')).call(e,this.config.showRefresh?'<div class="veplayer-error-refresh">'.concat(t.getText("ERROR_REFRESH"),"</div>"):"",'\n        <div class="veplayer-error-extra"></div>\n      </div>\n    </veplayer-error>')}},{key:"_focus",value:function(){this.player.focus({autoHide:!0})}},{key:"_initIcons",value:function(){this.config.showErrorImg&&this.appendChild(".veplayer-error-img",this.icons.error)}},{key:"_initEvents",value:function(){this.on(CM.FALLBACK_ERROR,this.showError.bind(this)),this.on(BC,this.hideError.bind(this)),this.on(OC,this.hideError.bind(this)),this.on(sx,this.hideError.bind(this))}},{key:"_renderText",value:function(e){var t=qm(this).call(this,".veplayer-error-tips");t&&(t.innerText=e)}},{key:"_renderExtraTips",value:function(){var e,t=this,n=qm(this).call(this,".veplayer-error-extra");n&&(n.innerHTML="");var r=this.player.config.i18nManager;null===(e=this.config.extraTips)||void 0===e||e.forEach((function(e){if(e){var n,i=bC.createDom("div",Fm(n="".concat(r.normalize(e.label),": ")).call(n,r.normalize(e.value)),void 0,"veplayer-error-detail-info");t.appendChild(".veplayer-error-extra",i)}}))}},{key:"_handleRefresh",value:function(){var e,t;null===(e=this.player)||void 0===e||e.emit(CM.ERROR_REFRESH_CLICK),null===(t=this.player)||void 0===t||null===(t=t.config)||void 0===t||null===(t=t.veplayer)||void 0===t||t.retry(),this.hideError()}},{key:"_renderRefresh",value:function(){var e,t=this.player.config.i18nManager,n=qm(this).call(this,".veplayer-error-refresh");n&&(n.innerHTML=null!==(e=t.getText("ERROR_REFRESH"))&&void 0!==e?e:"")}}],[{key:"pluginName",get:function(){return"error"}},{key:"defaultConfig",get:function(){return{showErrorImg:!0,showErrorTip:!0,showRefresh:!0,errorTipsText:"",extraTips:[],position:qx.POSITIONS.ROOT}}}]),n}(qx),iL=function(e){XP(n,e);var t=$P(n);function n(){var e,r;Iy(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return Ny(BP(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"_toastNumber",0),Ny(BP(r),"_toastMap",{}),r}return Ly(n,[{key:"toast",value:function(e,t){var n=this,r=t||{},i=r.duration,o=void 0===i?2e3:i,a=r.closable,s=void 0!==a&&a,l=this._toastNumber+1;this._toastNumber=l;var u=this._renderToast(e,s,l);return this.root.appendChild(u),this._toastMap[l]=u,setTimeout((function(){n.remove(l)}),o),l}},{key:"remove",value:function(e){var t=this._toastMap[e];t&&(this.root.contains(t)&&this.root.removeChild(t),delete this._toastMap[e])}},{key:"render",value:function(){return'<veplayer-toast class="veplayer-toast"></veplayer-toast>'}},{key:"_renderToast",value:function(e,t,n){var r=this,i=bC.createDom("div","",void 0,"veplayer-toast-container"),o=bC.checkIsObject(e)?e:bC.createDom("div",e,void 0,"veplayer-toast-txt");if(i.appendChild(o),t){var a=bC.createDom("span","×",void 0,"veplayer-toast-close");a.addEventListener("click",(function(){r.remove(n)})),i.appendChild(a)}return i}}],[{key:"pluginName",get:function(){return"toast"}},{key:"defaultConfig",get:function(){return{index:5}}}]),n}(qx);function oL(){return(new DOMParser).parseFromString('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path fill-rule="evenodd" clip-rule="evenodd" d="M12.7123 1C11.85 1 11.1173 1.55472 10.8516 2.32675L4.94973 2.32675C3.16413 2.32675 1.71662 3.77427 1.71662 5.55987C1.71662 7.34547 3.16413 8.79298 4.94973 8.79298L11.0483 8.79298L11.1134 8.79418C11.9936 8.82748 12.697 9.55166 12.697 10.44C12.697 11.3497 11.9596 12.0871 11.05 12.0871H5.01323C4.67882 11.4781 4.03143 11.0654 3.28763 11.0654C2.20111 11.0654 1.32031 11.9462 1.32031 13.0327C1.32031 14.1192 2.20111 15 3.28763 15C4.14992 15 4.88264 14.4452 5.14834 13.6732H11.05C12.8356 13.6732 14.2831 12.2256 14.2831 10.44C14.2831 8.65444 12.8356 7.20693 11.05 7.20693L4.94973 7.20693C4.04009 7.20693 3.30267 6.46951 3.30267 5.55987C3.30267 4.65022 4.04009 3.91281 4.94973 3.91281L10.9866 3.91281C11.321 4.52183 11.9685 4.93464 12.7123 4.93464C13.7988 4.93464 14.6796 4.05384 14.6796 2.96732C14.6796 1.8808 13.7988 1 12.7123 1ZM12.7123 2.18954C12.2827 2.18954 11.9345 2.53777 11.9345 2.96732C11.9345 3.39688 12.2827 3.7451 12.7123 3.7451C13.1418 3.7451 13.4901 3.39688 13.4901 2.96732C13.4901 2.53777 13.1418 2.18954 12.7123 2.18954ZM2.50986 13.0327C2.50986 12.6031 2.85808 12.2549 3.28763 12.2549C3.71719 12.2549 4.06541 12.6031 4.06541 13.0327C4.06541 13.4622 3.71719 13.8105 3.28763 13.8105C2.85808 13.8105 2.50986 13.4622 2.50986 13.0327Z" fill="#FFFFFF"/>\n</svg>',"image/svg+xml").firstChild}var aL=function(){function e(t){Iy(this,e),Ny(this,"_root",void 0),Ny(this,"_config",void 0),Ny(this,"_parent",void 0),Ny(this,"_listDom",void 0),Ny(this,"_container",void 0),Ny(this,"_titleDom",void 0),Ny(this,"_player",void 0),Ny(this,"_delegates",void 0),Ny(this,"_listType",void 0);var n=t.config,r=t.root,i=t.player;this._config=n,this._parent=r,this._player=i,this._listType=n.listType,this.renderPanel(),this._initEvents(),this._bindDomEvent()}return Ly(e,[{key:"changeMode",value:function(e){this.updatePanel(e),this._listType=e}},{key:"renderItemList",value:function(e){var t,n=this,r=this._config;return e&&(r.list=e),this._listDom||(this._listDom=bC.createDom("div","",{},"item-list-panel-content")),this._listDom.innerHTML="",null===(t=r.list)||void 0===t||Bw(t).call(t,(function(e,t){var r,i,o=e.selected?"panel-item select-in":"panel-item ".concat(null!==(r=e.className)&&void 0!==r?r:"");e["data-index"]=t;var a=bC.createDom("div","",e,o);e.showText&&("string"==typeof e.showText?a.innerHTML=e.showText:a.appendChild(e.showText)),null===(i=n._listDom)||void 0===i||i.appendChild(a)})),this._listDom}},{key:"renderTitle",value:function(){var e,t,n=this._player.config.i18nManager;if(null!==(e=this._config.panel)&&void 0!==e&&e.title&&this._listType!==sL.Inner)return this._titleDom||(this._titleDom=bC.createDom("div","",{},"xg-mobile-panel-title")),this._titleDom.innerHTML=n.normalize(null===(t=this._config.panel)||void 0===t?void 0:t.title),this._titleDom}},{key:"updatePanel",value:function(e){this._root&&bC.removeClass(this._root,"panel-".concat(this._listType)),this._root&&bC.addClass(this._root,"panel-".concat(e));var t="mobile"===xC.device?"touchend":"click";e===sL.Inner?this._container&&this._bind(this._container,t,this._handleHide.bind(this)):this._container&&this._unbind(this._container,t,this._handleHide.bind(this))}},{key:"renderPanel",value:function(){var e,t,n,r=Fm(e="xg-mobile-panel panel-".concat(this._config.listType," ")).call(e,null!==(t=null===(n=this._config)||void 0===n||null===(n=n.panel)||void 0===n?void 0:n.className)&&void 0!==t?t:"");this._root=bC.createDom("div","",{},r),this._parent&&this._parent.appendChild(this._root);var i=this.renderTitle(),o=this.renderItemList();this._container=bC.createDom("div","",{},"xg-mobile-panel-content"),this._root.appendChild(this._container),i&&this._container.appendChild(i),o&&this._container.appendChild(o)}},{key:"show",value:function(){this._root&&(bC.removeClass(this._root,"hide"),bC.addClass(this._root,"active"))}},{key:"hide",value:function(){this._root&&(bC.removeClass(this._root,"active"),bC.addClass(this._root,"hide"))}},{key:"destroy",value:function(){this._parent&&this._root&&this._parent.removeChild(this._root),this._unbindDomEvent(),this._unbindEvents()}},{key:"_bind",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];e&&"on".concat(t)in e&&"function"==typeof n&&e.addEventListener(t,n,r)}},{key:"_unbind",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];e&&"on".concat(t)in e&&"function"==typeof n&&e.removeEventListener(t,n,r)}},{key:"_initEvents",value:function(){var e;this._config.isAutoChange&&(window.addEventListener("orientationchange",this._handleOrientationChange.bind(this)),null===(e=this._player)||void 0===e||e.on(tx,this._handleOrientationChange.bind(this)))}},{key:"_unbindEvents",value:function(){var e;window.removeEventListener("orientationchange",this._handleOrientationChange.bind(this)),null===(e=this._player)||void 0===e||e.off(tx,this._handleOrientationChange.bind(this))}},{key:"_handleOrientationChange",value:function(){var e,t,n,r=ck(e=[90,-90,"90","-90"]).call(e,window.orientation);if(null!==(t=this._player)&&void 0!==t&&t.fullscreen&&r)this.changeMode(sL.Fullscreen);else if(this._listType!==(null===(n=this._config)||void 0===n?void 0:n.listType)){var i;this.changeMode(null===(i=this._config)||void 0===i?void 0:i.listType)}}},{key:"_bindDomEvent",value:function(){var e="mobile"===xC.device?"touchend":"click";this._listType===sL.Inner&&this._container&&this._bind(this._container,e,this._handleHide.bind(this)),this._root&&this._bind(this._root,e,this._handleHide.bind(this)),this._container&&this._bind(this._container,e,this._stopPropagation),this._listDom&&(this._delegates=qx.delegate.call(this,this._listDom,".panel-item",e,this._onItemClick.bind(this),!0))}},{key:"_unbindDomEvent",value:function(){var e,t="mobile"===xC.device?"touchend":"click";(this._root&&this._unbind(this._root,t,this._handleHide.bind(this)),this._container&&this._unbind(this._container,t,this._handleHide.bind(this)),this._container&&this._unbind(this._container,t,this._stopPropagation),this._delegates)&&(Bw(e=this._delegates).call(e,(function(e){e.destroy&&e.destroy()})),this._delegates=null)}},{key:"_stopPropagation",value:function(e){e.stopPropagation(),e.preventDefault()}},{key:"_onItemClick",value:function(e){var t,n,r,i,o=e.delegateTarget,a="function"==typeof this._config.onOptionClick?this._config.onOptionClick:null,s="function"==typeof this._config.onItemClick?this._config.onItemClick:null,l=null===(t=this._root)||void 0===t?void 0:t.querySelector(".selected");bC.addClass(o,"selected"),l&&(bC.removeClass(l,"selected"),i=Number(l.getAttribute("data-index")));var u=Number(o.getAttribute("data-index")),c={from:void 0!==Sc(i)?null===(n=this._config.list)||void 0===n?void 0:n[i]:void 0,to:null===(r=this._config.list)||void 0===r?void 0:r[u]};null==a||a(e,c),o&&i===u||null==s||s(e,c)}},{key:"_handleHide",value:function(e){var t,n;e.stopPropagation(),e.preventDefault(),null===(t=(n=this._config).hide)||void 0===t||t.call(n)}}]),e}(),sL=function(e){return e.Middle="middle",e.Bottom="bottom",e.Fullscreen="fullscreen",e.Inner="inner",e}(sL||{}),lL=function(e){return e.Icon="Icon",e.Text="Text",e}(lL||{}),uL="mobile"===xC.device,cL=["bottom","fullscreen","inner"],hL=["middle"],fL=["bottom","inner","fullscreen"],dL=["bottom","inner","fullscreen"],pL=function(e){XP(n,e);var t=$P(n);function n(e){var r;return Iy(this,n),Ny(BP(r=t.call(this,e)),"optionsList",void 0),Ny(BP(r),"_isIcon",void 0),Ny(BP(r),"_isActive",void 0),Ny(BP(r),"_curIndex",void 0),Ny(BP(r),"activeEvent",void 0),Ny(BP(r),"onEnter",(function(e){e.preventDefault(),e.stopPropagation(),r.emit("icon_mouseenter",{pluginName:r.pluginName}),r.toggle(!0)})),Ny(BP(r),"onLeave",(function(e){e.preventDefault(),e.stopPropagation(),r.emit("icon_mouseleave",{pluginName:r.pluginName}),r._isActive&&r.toggle(!1)})),r._isIcon=!1,r._isActive=!1,r._curIndex=0,r}return Ly(n,[{key:"updateLang",value:function(){this.config.list&&this.renderItemList(this.config.list,this._curIndex)}},{key:"afterCreate",value:function(){var e=this,t=this.config;this.config.listType=this._getListType(),this.initIcons(),t.hidePortrait&&bC.addClass(this.root,"portrait"),uL&&this.on(KC,(function(){e._isActive&&(e.optionsList&&e.optionsList.hide(),e._isActive=!1)})),t.list&&this.renderItemList(t.list),this.activeEvent=uL?"touchend":"mouseenter",this.bind(this.activeEvent,this.onEnter),this.bind("mouseleave",this.onLeave)}},{key:"initIcons",value:function(){var e=this,t=this.icons,n=fk(t);if(n.length>0&&"Icon"===this.config.renderType)return n.forEach((function(n){e.appendChild(".xgplayer-icon",t[n])})),void(this._isIcon=!0);var r=this.appendChild(".xgplayer-icon",bC.createDom("span","",{},"icon-text"));r&&bC.addClass(r,"btn-text")}},{key:"show",value:function(){!this.config.list||this.config.list.length<1||this.config.hideOnSingleOption&&1===this.config.list.length||bC.addClass(this.root,"show")}},{key:"hide",value:function(){bC.removeClass(this.root,"show")}},{key:"toggle",value:function(e){if(e!==this._isActive){var t=this.player.controls,n=this.config.listType;e?(n&&ck(fL).call(fL,n)?t.blur():t.focus(),this.optionsList&&this.optionsList.show()):(n&&ck(fL).call(fL,n)?t.focus():t.focusAwhile(),this.optionsList&&this.optionsList.hide()),this._isActive=e}}},{key:"onItemClick",value:function(e,t){var n;e.preventDefault(),e.stopPropagation();var r=this.config.listType;this._curIndex=null===(n=t.to)||void 0===n?void 0:n.index,this.changeCurrentText(),(this.config.isItemClickHide||r&&ck(fL).call(fL,r))&&this.toggle(!1)}},{key:"onOptionClick",value:function(e,t){e.preventDefault(),e.stopPropagation()}},{key:"changeCurrentText",value:function(){if(!this._isIcon){var e=this.config.list,t=e&&this._curIndex<e.length?this._curIndex:0,n=null==e?void 0:e[t];if(n){var r,i=qm(this).call(this,".icon-text"),o=this.player.config.i18nManager;if(i)i.innerHTML=o.normalize(null!==(r=null==n?void 0:n.iconText)&&void 0!==r?r:null==n?void 0:n.text)}}}},{key:"renderItemList",value:function(e,t){var r,i=this,o=this.config,a=this.optionsList,s=this.player;if("number"==typeof t&&(this._curIndex=t),a)return a.renderItemList(e),this.changeCurrentText(),void(a instanceof aL&&a.renderTitle());this.config.isShowIcon&&(this.optionsList=o.listType&&ck(dL).call(dL,o.listType)?new aL({config:{list:e||[],onItemClick:function(e,t){i.onItemClick(e,t)},onOptionClick:function(e,t){i.onOptionClick(e,t)},panel:this.config.panel,listType:null!==(r=o.listType)&&void 0!==r?r:n.defaultConfig.listType,isAutoChange:o.isAutoChange,hide:function(){return i.toggle(!1)}},root:s.root,player:s}):new rM({config:{data:e||[],onItemClick:function(e,t){i.onItemClick(e,t)}},root:this.root}),this.changeCurrentText(),this.show())}},{key:"destroy",value:function(){this.activeEvent&&this.unbind(this.activeEvent,this.onEnter),this.unbind("mouseleave",this.onLeave),this.optionsList&&(this.optionsList.destroy(),this.optionsList=void 0)}},{key:"render",value:function(){return this.config.isShowIcon?'<xg-icon class="xg-options-icon '.concat(this.config.className||"",'">\n      <div class="xgplayer-icon">\n      </div>\n    </xg-icon>'):""}},{key:"_getListType",value:function(){var e,t=this.config.listType;return null!==(e=this.player)&&void 0!==e&&null!==(e=e.config)&&void 0!==e&&e.listType&&(t=this.player.config.listType),uL||t&&ck(cL).call(cL,t)||(t="bottom"),uL||t&&ck(hL).call(hL,t)||(t="middle"),t}}],[{key:"pluginName",get:function(){return"optionsIcon"}},{key:"defaultConfig",get:function(){return{position:uL?DN.ROOT_TOP_RIGHT:DN.CONTROLS_RIGHT,listType:uL?"bottom":"middle",index:100,list:[],listStyle:{},hidePortrait:!0,isShowIcon:!1,renderType:uL?"Icon":"Text",isItemClickHide:!0,hideOnSingleOption:!0,isAutoChange:!0}}}]),n}(qx),vL=function(e){XP(n,e);var t=$P(n);function n(){return Iy(this,n),t.apply(this,arguments)}return Ly(n,[{key:"updateLang",value:function(){this.renderItemList()}},{key:"beforeCreate",value:function(e){var t=e.player.config.sources.sourceManager;e.config.list=t.sources}},{key:"afterCreate",value:function(){tL(JP(n.prototype),"afterCreate",this).call(this),this.config.sourceManager.sources.length<2?this.hide():this.renderItemList()}},{key:"registerIcons",value:function(){return{source:{icon:oL}}}},{key:"renderItemList",value:function(){var e=this.config.sourceManager,t=e.sources,r=e.source,i=this.player.config.i18nManager,o=Bw(t).call(t,(function(e){var t;return Dm(Dm({},e),{},{showText:null!==(t=i.normalize(e.text))&&void 0!==t?t:e.name,selected:!1})})),a=hI(o).call(o,(function(e){return e.name===(null==r?void 0:r.name)}));this.config.list=t,a>-1&&(o[a].selected=!0),tL(JP(n.prototype),"renderItemList",this).call(this,o,a)}},{key:"onItemClick",value:function(e,t){var r,i=this;tL(JP(n.prototype),"onItemClick",this).call(this,e,t),null===(r=this.player.config)||void 0===r||r.veplayer.switch({source:t.to.name},{fallbackToFirstDefinition:!0}).catch((function(){i.renderItemList()}))}}],[{key:"pluginName",get:function(){return"sources"}},{key:"defaultConfig",get:function(){return Dm(Dm({},pL.defaultConfig),{},{index:4,panel:{title:{jp:"ライン選ぶ",en:"Line Choose","zh-cn":"线路选择","zh-hk":"線路選擇"}},list:[],className:"xgplayer-sources",hidePortrait:!1,isShowIcon:!0})}},{key:"textTips",get:function(){return{jp:"ライン",en:"Line","zh-cn":"线路","zh-hk":"線路"}}}]),n}(pL);function gL(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path fill-rule="evenodd" clip-rule="evenodd" d="M16 3.8001H4C3.55817 3.8001 3.2 4.15827 3.2 4.6001V12.6001C3.2 13.0419 3.55817 13.4001 4 13.4001H16C16.4418 13.4001 16.8 13.0419 16.8 12.6001V4.6001C16.8 4.15827 16.4418 3.8001 16 3.8001ZM4 2.6001C2.89543 2.6001 2 3.49553 2 4.6001V12.6001C2 13.7047 2.89543 14.6001 4 14.6001H16C17.1046 14.6001 18 13.7047 18 12.6001V4.6001C18 3.49553 17.1046 2.6001 16 2.6001H4ZM5.0249 6.24509V11.6001H5.9024V9.23009H8.6174V11.6001H9.4949V6.24509H8.6174V8.48009H5.9024V6.24509H5.0249ZM10.5474 6.24509V11.6001H12.5049C13.3749 11.6001 14.0274 11.3601 14.4774 10.8801C14.9049 10.4226 15.1224 9.77009 15.1224 8.92259C15.1224 8.06759 14.9049 7.41509 14.4774 6.96509C14.0274 6.48509 13.3749 6.24509 12.5049 6.24509H10.5474ZM11.4249 6.99509H12.3399C13.0074 6.99509 13.4949 7.14509 13.8024 7.45259C14.1024 7.75259 14.2524 8.24759 14.2524 8.92259C14.2524 9.58259 14.1024 10.0701 13.8024 10.3851C13.4949 10.6926 13.0074 10.8501 12.3399 10.8501H11.4249V6.99509ZM4.15 16.1001C3.79101 16.1001 3.5 16.3911 3.5 16.7501C3.5 17.1091 3.79101 17.4001 4.15 17.4001H15.85C16.209 17.4001 16.5 17.1091 16.5 16.7501C16.5 16.3911 16.209 16.1001 15.85 16.1001H4.15Z" fill="#ffffff"/>\n</svg>',"image/svg+xml").firstChild}var yL={exports:{}};!function(e,t){var n="__lodash_hash_undefined__",r=9007199254740991,o="[object Arguments]",a="[object Boolean]",s="[object Date]",l="[object Function]",u="[object GeneratorFunction]",c="[object Map]",h="[object Number]",f="[object Object]",d="[object Promise]",p="[object RegExp]",v="[object Set]",g="[object String]",y="[object Symbol]",m="[object WeakMap]",_="[object ArrayBuffer]",b="[object DataView]",w="[object Float32Array]",k="[object Float64Array]",E="[object Int8Array]",T="[object Int16Array]",S="[object Int32Array]",C="[object Uint8Array]",x="[object Uint8ClampedArray]",P="[object Uint16Array]",A="[object Uint32Array]",I=/\w*$/,R=/^\[object .+?Constructor\]$/,O=/^(?:0|[1-9]\d*)$/,D={};D[o]=D["[object Array]"]=D[_]=D[b]=D[a]=D[s]=D[w]=D[k]=D[E]=D[T]=D[S]=D[c]=D[h]=D[f]=D[p]=D[v]=D[g]=D[y]=D[C]=D[x]=D[P]=D[A]=!0,D["[object Error]"]=D[l]=D[m]=!1;var M="object"==Sc(i)&&i&&i.Object===Object&&i,L="object"==("undefined"==typeof self?"undefined":Sc(self))&&self&&self.Object===Object&&self,N=M||L||Function("return this")(),U=t&&!t.nodeType&&t,F=U&&e&&!e.nodeType&&e,B=F&&F.exports===U;function V(e,t){return e.set(t[0],t[1]),e}function H(e,t){return e.add(t),e}function j(e,t,n,r){var i=-1,o=e?e.length:0;for(r&&o&&(n=e[++i]);++i<o;)n=t(n,e[i],i,e);return n}function z(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(AV){}return t}function K(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function W(e,t){return function(n){return e(t(n))}}function G(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}var q,Y=Array.prototype,X=Function.prototype,J=Object.prototype,Q=N["__core-js_shared__"],$=(q=/[^.]+$/.exec(Q&&hE(Q)&&hE(Q).IE_PROTO||""))?"Symbol(src)_1."+q:"",Z=X.toString,ee=J.hasOwnProperty,te=J.toString,ne=RegExp("^"+Z.call(ee).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),re=B?N.Buffer:void 0,ie=N.Symbol,oe=N.Uint8Array,ae=W(bE,Object),se=Object.create,le=J.propertyIsEnumerable,ue=Mk(Y),ce=fE,he=re?re.isBuffer:void 0,fe=W(fk,Object),de=Fe(N,"DataView"),pe=Fe(N,"Map"),ve=Fe(N,"Promise"),ge=Fe(N,"Set"),ye=Fe(N,"WeakMap"),me=Fe(Object,"create"),_e=ze(de),be=ze(pe),we=ze(ve),ke=ze(ge),Ee=ze(ye),Te=ie?ie.prototype:void 0,Se=Te?Te.valueOf:void 0;function Ce(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function xe(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Pe(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Ae(e){this.__data__=new xe(e)}function Ie(e,t){var n=We(e)||function(e){return function(e){return function(e){return!!e&&"object"==Sc(e)}(e)&&Ge(e)}(e)&&ee.call(e,"callee")&&(!le.call(e,"callee")||te.call(e)==o)}(e)?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],r=n.length,i=!!r;for(var a in e)!t&&!ee.call(e,a)||i&&("length"==a||He(a,r))||n.push(a);return n}function Re(e,t,n){var r=e[t];ee.call(e,t)&&Ke(r,n)&&(void 0!==n||t in e)||(e[t]=n)}function Oe(e,t){for(var n=e.length;n--;)if(Ke(e[n][0],t))return n;return-1}function De(e,t,n,r,i,d,m){var R;if(r&&(R=d?r(e,i,d,m):r(e)),void 0!==R)return R;if(!Xe(e))return e;var O=We(e);if(O){if(R=function(e){var t=e.length,n=e.constructor(t);t&&"string"==typeof e[0]&&ee.call(e,"index")&&(n.index=e.index,n.input=e.input);return n}(e),!t)return function(e,t){var n=-1,r=e.length;t||(t=Array(r));for(;++n<r;)t[n]=e[n];return t}(e,R)}else{var M=Ve(e),L=M==l||M==u;if(qe(e))return function(e,t){if(t)return dk(e).call(e);var n=new e.constructor(e.length);return e.copy(n),n}(e,t);if(M==f||M==o||L&&!d){if(z(e))return d?e:{};if(R=function(e){return"function"!=typeof e.constructor||je(e)?{}:(t=ae(e),Xe(t)?se(t):{});var t}(L?{}:e),!t)return function(e,t){return Ne(e,Be(e),t)}(e,function(e,t){return e&&Ne(t,Je(t),e)}(R,e))}else{if(!D[M])return d?e:{};R=function(e,t,n,r){var i=e.constructor;switch(t){case _:return Le(e);case a:case s:return new i(+e);case b:return function(e,t){var n=t?Le(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,r);case w:case k:case E:case T:case S:case C:case x:case P:case A:return function(e,t){var n=t?Le(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}(e,r);case c:return function(e,t,n){var r=t?n(K(e),!0):K(e);return j(r,V,new e.constructor)}(e,r,n);case h:case g:return new i(e);case p:return function(e){var t=new e.constructor(e.source,I.exec(e));return t.lastIndex=e.lastIndex,t}(e);case v:return function(e,t,n){var r=t?n(G(e),!0):G(e);return j(r,H,new e.constructor)}(e,r,n);case y:return o=e,Se?Object(Se.call(o)):{}}var o}(e,M,De,t)}}m||(m=new Ae);var N=m.get(e);if(N)return N;if(m.set(e,R),!O)var U=n?function(e){return function(e,t,n){var r=t(e);return We(e)?r:function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}(r,n(e))}(e,Je,Be)}(e):Je(e);return function(e,t){for(var n=-1,r=e?e.length:0;++n<r&&!1!==t(e[n],n,e););}(U||e,(function(i,o){U&&(i=e[o=i]),Re(R,o,De(i,t,n,r,o,e,m))})),R}function Me(e){return!(!Xe(e)||(t=e,$&&$ in t))&&(Ye(e)||z(e)?ne:R).test(ze(e));var t}function Le(e){var t=new e.constructor(e.byteLength);return new oe(t).set(new oe(e)),t}function Ne(e,t,n,r){n||(n={});for(var i=-1,o=t.length;++i<o;){var a=t[i],s=r?r(n[a],e[a],a,n,e):void 0;Re(n,a,void 0===s?e[a]:s)}return n}function Ue(e,t){var n,r,i=e.__data__;return("string"==(r=Sc(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof t?"string":"hash"]:Bw(i)}function Fe(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return Me(n)?n:void 0}Ce.prototype.clear=function(){this.__data__=me?me(null):{}},Ce.prototype.delete=function(e){return this.has(e)&&delete this.__data__[e]},Ce.prototype.get=function(e){var t=this.__data__;if(me){var r=t[e];return r===n?void 0:r}return ee.call(t,e)?t[e]:void 0},Ce.prototype.has=function(e){var t=this.__data__;return me?void 0!==t[e]:ee.call(t,e)},Ce.prototype.set=function(e,t){return this.__data__[e]=me&&void 0===t?n:t,this},xe.prototype.clear=function(){this.__data__=[]},xe.prototype.delete=function(e){var t=this.__data__,n=Oe(t,e);return!(n<0)&&(n==t.length-1?t.pop():ue.call(t,n,1),!0)},xe.prototype.get=function(e){var t=this.__data__,n=Oe(t,e);return n<0?void 0:t[n][1]},xe.prototype.has=function(e){return Oe(this.__data__,e)>-1},xe.prototype.set=function(e,t){var n=this.__data__,r=Oe(n,e);return r<0?n.push([e,t]):n[r][1]=t,this},Pe.prototype.clear=function(){this.__data__={hash:new Ce,map:new(pe||xe),string:new Ce}},Pe.prototype.delete=function(e){return Ue(this,e).delete(e)},Pe.prototype.get=function(e){return Ue(this,e).get(e)},Pe.prototype.has=function(e){return Ue(this,e).has(e)},Pe.prototype.set=function(e,t){return Ue(this,e).set(e,t),this},Ae.prototype.clear=function(){this.__data__=new xe},Ae.prototype.delete=function(e){return this.__data__.delete(e)},Ae.prototype.get=function(e){return this.__data__.get(e)},Ae.prototype.has=function(e){return this.__data__.has(e)},Ae.prototype.set=function(e,t){var n=this.__data__;if(n instanceof xe){var r=n.__data__;if(!pe||r.length<199)return r.push([e,t]),this;n=this.__data__=new Pe(r)}return n.set(e,t),this};var Be=ce?W(ce,Object):function(){return[]},Ve=function(e){return te.call(e)};function He(e,t){return!!(t=null==t?r:t)&&("number"==typeof e||O.test(e))&&e>-1&&e%1==0&&e<t}function je(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||J)}function ze(e){if(null!=e){try{return Z.call(e)}catch(AV){}try{return e+""}catch(AV){}}return""}function Ke(e,t){return e===t||e!=e&&t!=t}(de&&Ve(new de(new ArrayBuffer(1)))!=b||pe&&Ve(new pe)!=c||ve&&Ve(ve.resolve())!=d||ge&&Ve(new ge)!=v||ye&&Ve(new ye)!=m)&&(Ve=function(e){var t=te.call(e),n=t==f?e.constructor:void 0,r=n?ze(n):void 0;if(r)switch(r){case _e:return b;case be:return c;case we:return d;case ke:return v;case Ee:return m}return t});var We=Array.isArray;function Ge(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}(e.length)&&!Ye(e)}var qe=he||function(){return!1};function Ye(e){var t=Xe(e)?te.call(e):"";return t==l||t==u}function Xe(e){var t=Sc(e);return!!e&&("object"==t||"function"==t)}function Je(e){return Ge(e)?Ie(e):function(e){if(!je(e))return fe(e);var t=[];for(var n in Object(e))ee.call(e,n)&&"constructor"!=n&&t.push(n);return t}(e)}e.exports=function(e){return De(e,!0,!0)}}(yL,yL.exports);var mL=o(yL.exports),_L=function(e){XP(r,e);var t,n=$P(r);function r(){var e,t;Iy(this,r);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return Ny(BP(t=n.call.apply(n,Fm(e=[this]).call(e,o))),"_waitingStartTime",0),Ny(BP(t),"_timer",void 0),Ny(BP(t),"_toastId",void 0),t}return Ly(r,[{key:"updateLang",value:function(){this.renderItemList()}},{key:"beforeCreate",value:function(e){var t,n=e.player.config.sources.sourceManager;e.config.list=null===(t=n.source)||void 0===t?void 0:t.definitions}},{key:"afterCreate",value:function(){var e,t;tL(JP(r.prototype),"afterCreate",this).call(this),(null!==(e=null===(t=this.player.config.sourceManager.source)||void 0===t?void 0:t.definitions)&&void 0!==e?e:[]).length<2?this.hide():(this.renderItemList(),this.on(CM.WAITING,this._waiting.bind(this)),this.on(CM.PLAYING,this._clearTimer.bind(this)),this.on(CM.PAUSE,this._clearTimer.bind(this)),this.on(CM.PLAY,this._clearTimer.bind(this)),this._initTimeUpdateEvent())}},{key:"registerIcons",value:function(){return{source:{icon:gL}}}},{key:"renderItemList",value:function(){var e,t,n,i=this,o=this.player.config.sourceManager,a=null!==(e=null===(t=o.source)||void 0===t?void 0:t.definitions)&&void 0!==e?e:[],s=Bw(a).call(a,(function(e){var t;return{url:e.url,definition:e.definition||"",showText:null!==(t=i.player.config.i18nManager.normalize(e.text))&&void 0!==t?t:e.definition,selected:!1}})),l=hI(a).call(a,(function(e){var t;return e.definition===(null===(t=o.definition)||void 0===t?void 0:t.definition)}));this.config.list=null===(n=o.source)||void 0===n?void 0:n.definitions,l>-1&&(s[l].selected=!0),tL(JP(r.prototype),"renderItemList",this).call(this,s,l)}},{key:"onItemClick",value:function(e,t){var n,i=this;this._showToast(t.to),tL(JP(r.prototype),"onItemClick",this).call(this,e,t),null===(n=this.player.config)||void 0===n||n.veplayer.switch({definition:t.to.definition}).catch((function(){i.renderItemList()}))}},{key:"fallback",value:(t=ay(iy().mark((function e(){var t,n,r,i,o,a,s,l=arguments;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=l.length>0&&void 0!==l[0]?l[0]:{toast:!0},i=this.player.config.sourceManager,!((o=hI(t=this.config.demotePriority).call(t,(function(e){var t;return e===(null===(t=i.definition)||void 0===t?void 0:t.definition)})))<0)){e.next=5;break}return e.abrupt("return");case 5:if(a=mL(i.definition),s=this._getNextDefinition(o)){e.next=9;break}return e.abrupt("return");case 9:if(!r.toast){e.next=15;break}return e.next=12,this._toast(s);case 12:if(e.sent){e.next=15;break}return e.abrupt("return");case 15:this.player.emit(CM.DEFINITION_FALLBACK,{from:a,to:s}),null===(n=this.player.config)||void 0===n||n.veplayer.switch({definition:s.definition}).catch((function(){}));case 17:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"_waiting",value:function(){var e=this;if(!this._timer){this._waitingStartTime=Date.now();var t=this.player.currentTime;this._timer=setTimeout((function(){e.player&&e.player.currentTime<=t+.1&&(e.player.emit(CM.LONG_WAITING),e.config.needFallback&&e.fallback({toast:!0}),e._clearTimer())}),this.config.longWaitingTime)}}},{key:"_clearTimer",value:function(){this._timer&&(clearTimeout(this._timer),this._timer=void 0),this._waitingStartTime&&(this._waitingStartTime=0)}},{key:"_toast",value:function(e){var t=this,n=this.player.config.i18nManager;return new Vw((function(r){var i,o,a=null===(i=t.player)||void 0===i?void 0:i.getPlugin("toast");if(a){var s=bC.createDom("div",n.getText("DEFINITION_FALLBACK_TOAST"),void 0,"veplayer-toast-txt"),l=null!==(o=t.player.config.i18nManager.normalize(e.text))&&void 0!==o?o:e.definition,u=bC.createDom("span",l,void 0,"veplayer-toast-txt-bt");s.appendChild(u);var c=a.toast(s,{duration:5e3,closable:!0});u.addEventListener("click",(function(){a.remove(c),r(!0)}))}else r(!1)}))}},{key:"_getNextDefinition",value:function(e){var t,n=this.player.config.sourceManager;if(null!==(t=n.source)&&void 0!==t&&t.definitions.length&&!(e+1>=this.config.demotePriority.length)){var r=e+1,i=qm(n).call(n,{definition:this.config.demotePriority[r]});return i||this._getNextDefinition(r)}}},{key:"_initTimeUpdateEvent",value:function(){var e=this,t=this.player.currentTime;this.on(CM.TIME_UPDATE,(function(){var n=e.player.currentTime;n!==t?(t=n,e._clearTimer()):t=n}))}},{key:"_showToast",value:function(e){var t,n,r,i=this,o=null===(t=this.player)||void 0===t?void 0:t.getPlugin("toast"),a=this.player.config.i18nManager;o&&(this._toastId&&o.remove(this._toastId),this._toastId=o.toast(Fm(n="".concat(a.getText("DEFINITION_SWITCHING")," ")).call(n,null!==(r=e.showText)&&void 0!==r?r:e.definition," ..."),{duration:2e3,closable:!0}),this.player.once(CM.AFTER_DEFINITION_CHANGE,(function(){void 0!==i._toastId&&o.remove(i._toastId)})))}}],[{key:"pluginName",get:function(){return"definition"}},{key:"defaultConfig",get:function(){return Dm(Dm({},pL.defaultConfig),{},{index:3,list:[],panel:{title:{jp:"クラリティセレクション",en:"Definition Choose","zh-cn":"清晰度选择","zh-hk":"清晰度選擇"}},className:"xgplayer-definition",hidePortrait:!1,isShowIcon:!0,longWaitingTime:5e3,needFallback:!1,demotePriority:["uhd","hd","sd","ld","ao"]})}},{key:"textTips",get:function(){return{jp:"明快さ",en:"Definition","zh-cn":"清晰度","zh-hk":"清晰度"}}}]),r}(pL),bL={"biz:vod":"veplayer.biz.vod.[env].[ext]","biz:live":"veplayer.biz.live.[env].[ext]","plugin:flv":"veplayer.plugin.flv.[env].[ext]","plugin:mp4":"veplayer.plugin.mp4.[env].[ext]","plugin:hls":"veplayer.plugin.hls.[env].[ext]","plugin:shaka":"veplayer.plugin.shaka.[env].[ext]","plugin:rtm":"veplayer.plugin.rtm.[env].[ext]","plugin:xgvideo":"veplayer.plugin.xgvideo.[env].[ext]"},wL=function(e){return e.BizVod="biz:vod",e.BizLive="biz:live",e.PluginFlv="plugin:flv",e.PluginMp4="plugin:mp4",e.PluginHls="plugin:hls",e.PluginShaka="plugin:shaka",e.PluginRtm="plugin:rtm",e.PluginXgvideo="plugin:xgvideo",e}(wL||{}),kL=function(){function e(t){var n=this;Iy(this,e),Ny(this,"modules",{}),Ny(this,"mountPromiseMap",{}),Ny(this,"_baseUrl",void 0),Ny(this,"_moduleSystem",void 0),Ny(this,"load",function(){var e=ay(iy().mark((function e(t,r){var i,o,a,s,l;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n._moduleSystem){e.next=2;break}throw new Error('You have to specify moduleSystem first, call "setModuleSystem(moduleSystem)" to set module system');case 2:if(bL[t]){e.next=4;break}throw new Error('Invalid module name: "'.concat(t,'"'));case 4:if("esm"!==n._moduleSystem){e.next=11;break}if(n.modules[t]){e.next=9;break}throw new Error('Dynamic module "'.concat(t,'" not found, you have to register it first under ESM'));case 9:return o=n.modules[t].exports,e.abrupt("return",null!==(i=o.default)&&void 0!==i?i:o);case 11:if(!n.modules[t]){e.next=21;break}if(1!==n.modules[t].state){e.next=16;break}return e.abrupt("return",n.modules[t].exports);case 16:if(0!==n.modules[t].state){e.next=20;break}return e.abrupt("return",n.mountPromiseMap[t].then((function(e){var t;return null!==(t=e.default)&&void 0!==t?t:e})));case 20:throw null!==(a=n.modules[t].error)&&void 0!==a?a:new Error('Dynamic module "'.concat(t,'" load failed.'));case 21:return s=[n._baseUrl,bL[t]].join("/").replace("[env]","production").replace("[ext]","js"),l={src:null!=r?r:s,name:t,state:0,exports:{}},n.modules[t]=l,n.mountPromiseMap[t]=n._mount(l),e.abrupt("return",n.mountPromiseMap[t]);case 26:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),Ny(this,"register",(function(e,t){if(!n._moduleSystem)throw new Error("You have to specify moduleSystem first, call setModuleSystem(moduleSystem) to set module system");return"esm"===n._moduleSystem?(n.modules[e]={src:"esm:".concat(e),name:e,state:1,exports:t},n.modules[e].exports):(n.modules[e]?n.modules[e].exports=t:n.modules[e]={src:"umd:".concat(e),name:e,state:1,exports:t},n.modules[e].exports)})),this._baseUrl=t.baseUrl}return Ly(e,[{key:"baseUrl",get:function(){return this._baseUrl}},{key:"moduleSystem",get:function(){return this._moduleSystem}},{key:"setModuleSystem",value:function(e){this._moduleSystem=e}},{key:"setBaseUrl",value:function(e){this._baseUrl=e}},{key:"_mount",value:function(e){var t=this.modules;return new Vw((function(n,r){var i=document.createElement("script");i.src=e.src,i.onload=function(){e.state=1,n(t[e.name].exports),i.onload=null},i.onerror=function(t){e.state=2,e.error=t,r(t),i.onerror=null},document.body.appendChild(i)}))}}]),e}(),EL=p,TL=ee,SL=Fn,CL=zn,xL=jc,PL=Math.min,AL=[].lastIndexOf,IL=!!AL&&1/[1].lastIndexOf(1,-0)<0,RL=xL("lastIndexOf"),OL=IL||!RL?function(e){if(IL)return EL(AL,this,arguments)||0;var t=TL(this),n=CL(t),r=n-1;for(arguments.length>1&&(r=PL(r,SL(arguments[1]))),r<0&&(r=n+r);r>=0;r--)if(r in t&&t[r]===e)return r||0;return-1}:AL;On({target:"Array",proto:!0,forced:OL!==[].lastIndexOf},{lastIndexOf:OL});var DL,ML,LL=Gc("Array").lastIndexOf,NL=ce,UL=LL,FL=Array.prototype,BL=o((function(e){var t=e.lastIndexOf;return e===FL||NL(FL,e)&&t===FL.lastIndexOf?UL:t})),VL=null!==(DL=null===(ML=document.currentScript)||void 0===ML?void 0:ML.src)&&void 0!==DL?DL:"https://tosv.byted.org/obj/lux/sdk/@byted/veplayer/".concat("2.1.0","/object/umd/"),HL=new kL({baseUrl:dk(VL).call(VL,0,BL(VL).call(VL,"/"))}),jL=HL.register;function zL(e){HL.setModuleSystem(e)}function KL(e,t){return WL.apply(this,arguments)}function WL(){return(WL=ay(iy().mark((function e(t,n){var r;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,HL.load(t,n);case 3:return e.abrupt("return",e.sent);case 6:throw e.prev=6,e.t0=e.catch(0),aA(nA.MODULE_LOAD_ERROR,{ext:{name:t,cdn:null===e.t0||void 0===e.t0||null===(r=e.t0.target)||void 0===r?void 0:r.src},error:e.t0});case 9:case"end":return e.stop()}}),e,null,[[0,6]])})))).apply(this,arguments)}function GL(e){return function(t){var n;return null==t?void 0:ck(n=t.split("?")[0].toLowerCase()).call(n,e)}}var qL=GL(".m3u8"),YL=GL(".mp4"),XL=GL(".flv"),JL=GL(".sdp"),QL=GL(".mpd");function $L(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:t.H264)===t.H265?xC.isHevcSupported():xC.isMSESupport()}function ZL(){return eN.apply(this,arguments)}function eN(){return(eN=ay(iy().mark((function e(){var t;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,KL(wL.PluginXgvideo);case 2:return t=e.sent,e.abrupt("return",null==t?void 0:t.isSupported());case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var tN=Dm(Dm({},bC),{},{getStreamType:function(e){return qL(e)?"hls":XL(e)?"flv":JL(e)?"rtm":YL(e)?"mp4":QL(e)?"dash":"unknown"},isMseSupported:$L,isSoftDecodeSupported:ZL}),nN=xC.isWeixin,rN=xC.os,iN=xC.device,oN=rN.isAndroid,aN=rN.isIos,sN="mobile"===iN,lN=0,uN=!1,cN=function(e){XP(n,e);var t=$P(n);function n(e){var r;return Iy(this,n),Ny(BP(r=t.call(this,e)),"_hasSetUserAutoplay",void 0),Ny(BP(r),"_timer",void 0),Ny(BP(r),"_wxTimer",void 0),Ny(BP(r),"_state",{mode:0,showUnmuteBt:!1}),Ny(BP(r),"userClickPlay",(function(){if(!uN){var e=r.player.play();e&&e.then?e.then((function(){console.log("user action autoplay success"),uN=!0})).catch((function(e){console.warn("user action autoplay fail:",e)})):(console.warn("no play promise"),uN=!0)}})),r}return Ly(n,[{key:"afterCreate",value:function(){this.initState(),this.initEvents(),this.setWXAutoPlay()}},{key:"updateLang",value:function(){this.renderUnmuteBt()}},{key:"initState",value:function(){this._state.mode=this.player.muted?1:0}},{key:"initEvents",value:function(){this.player.on(XC,this.handleAutoplayPrevented.bind(this)),this.player.on(YC,this.handleAutoplayStart.bind(this)),this.player.on(HC,this.handleVolumechange.bind(this)),this.setUserActionAutoPlay()}},{key:"setWXAutoPlay",value:function(){nN&&this.player.config.autoplay&&this.config.enableWxJsBridgeAutoplay&&(window.WeixinJSBridge?this.wxPlay():document.addEventListener("WeixinJSBridgeReady",this.wxPlay.bind(this),!1))}},{key:"wxCanPlayFn",value:function(){var e=this;window.WeixinJSBridge&&window.WeixinJSBridge.invoke("getNetworkType",{},(function(){e._wxTimer=setTimeout((function(){e._wxTimer&&clearTimeout(e._wxTimer);var t=e.player.play();t&&t.then?t.then((function(){console.log("wechat autoplay success")})).catch((function(t){console.warn("wechat autoplay fail:",t),e._state.showUnmuteBt=!1,e.renderUnmuteBt()})):console.warn("wechat browser no play promise")}))}))}},{key:"wxPlay",value:function(){this.player.readyState>=2?this.wxCanPlayFn():oN?this.player.once(BC,this.wxCanPlayFn.bind(this)):aN&&this.player.once(JC,this.wxCanPlayFn.bind(this))}},{key:"setUserActionAutoPlay",value:function(){var e=this;this.config.enableUserActionAutoplay&&this.player.once(SM.AUTOPLAY_FAIL,(function(){e._hasSetUserAutoplay=!0,(e.config.userActionDom instanceof window.Node?e.config.userActionDom:window.document.body).addEventListener("click",e.userClickPlay,!1)}))}},{key:"handleAutoplayPrevented",value:function(){return 0===this._state.mode?(console.warn("unmute autoplay fail"),this.player.emit(SM.AUTOPLAY_FAIL,{mode:"unmute"}),oN&&nN?void(this._state.mode=2):void(this.config.enableDegradeMuteAutoplay&&this.changeMuteMode())):1===this._state.mode?(console.warn("mute autoplay fail"),this.player.emit(SM.AUTOPLAY_FAIL,{mode:"mute"}),void(this._state.mode=2)):2===this._state.mode?(this.player.emit(SM.AUTOPLAY_FAIL,{mode:"noSupport"}),void console.warn("not support autoplay")):void 0}},{key:"changeMuteMode",value:function(){var e=this;this.player.muted=!0,this._state.showUnmuteBt=!0,this._state.mode=1,this.renderUnmuteBt(),this._timer=setTimeout((function(){e._timer&&clearTimeout(e._timer);var t=e.player.play();t&&t.then?t.then((function(){console.log("degrade mute autoplay success")})).catch((function(t){console.warn("degrade mute autoplay fail:",t),e.player.muted=!1,e._state.showUnmuteBt=!1,e.renderUnmuteBt()})):console.warn("no play promise")}))}},{key:"handleAutoplayStart",value:function(){if(0!==this._state.mode)return 1===this._state.mode?(this.player.emit(SM.AUTOPLAY_SUCCESS,{mode:"mute"}),this._state.showUnmuteBt=!0,void this.renderUnmuteBt()):2===this._state.mode?(this.player.emit(SM.AUTOPLAY_SUCCESS,{mode:"click"}),this.player.muted=!1,this._state.showUnmuteBt=!1,void this.renderUnmuteBt()):void 0;this.player.emit(SM.AUTOPLAY_SUCCESS,{mode:"unmute"})}},{key:"handleVolumechange",value:function(){this._state.showUnmuteBt&&(this._state.showUnmuteBt=!1,this.renderUnmuteBt(),this.player.paused&&this.player.readyState>=1&&this.player.play())}},{key:"renderUnmuteBt",value:function(){var e=this.root,t=this.player.config.i18nManager;if(e&&(e.innerHTML="",this._state.showUnmuteBt)){var n=t.getText("UNMUTE"),r=bC.createDom("div",n,void 0,"veplayer-unmute-bt");e.appendChild(r),this.bind(".veplayer-unmute-bt","click",this.cancelUnmute.bind(this)),this.bind(".veplayer-unmute-bt","touchend",this.cancelUnmute.bind(this))}}},{key:"cancelUnmute",value:function(){var e=Date.now();if(e-lN>200){lN=e,this.player.muted=!1,this._state.showUnmuteBt=!1,this.renderUnmuteBt(),this.emit(SM.AUTOPLAY_UNMUTE);var t="mobile"===xC.device,n=JL(this.player.config.url);t&&n&&(this.player.volume>.5?this.player.volume=this.player.volume-.1:this.player.volume=this.player.volume+.1)}}},{key:"destroy",value:function(){if(this.player.off(XC,this.handleAutoplayPrevented.bind(this)),this.player.off(YC,this.handleAutoplayStart.bind(this)),this.player.off(HC,this.handleVolumechange.bind(this)),this._timer&&window.clearTimeout(this._timer),this._wxTimer&&window.clearTimeout(this._wxTimer),document.removeEventListener("WeixinJSBridgeReady",this.wxPlay.bind(this),!1),this._hasSetUserAutoplay){var e=sN?"touch":"click";(this.config.userActionDom instanceof window.Node?this.config.userActionDom:window.document.body).removeEventListener(e,this.userClickPlay,!1)}}},{key:"render",value:function(){return'<div class="veplayer-unmute"></div>'}}],[{key:"pluginName",get:function(){return"autoplayPlugin"}},{key:"defaultConfig",get:function(){return{position:qx.POSITIONS.ROOT,enableDegradeMuteAutoplay:!1,enableWxJsBridgeAutoplay:!1,userActionDom:window.document.body,enableUserActionAutoplay:!1}}}]),n}(qx),hN=UM.values;On({target:"Object",stat:!0},{values:function(e){return hN(e)}});var fN=o(ie.Object.values);function dN(){return(new DOMParser).parseFromString('<svg width="18" height="18" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path fill-rule="evenodd" clip-rule="evenodd" d="M5.75 11C5.75 11.9665 4.9665 12.75 4 12.75C3.0335 12.75 2.25 11.9665 2.25 11C2.25 10.0335 3.0335 9.25 4 9.25C4.9665 9.25 5.75 10.0335 5.75 11ZM13.25 11C13.25 11.9665 12.4665 12.75 11.5 12.75C10.5335 12.75 9.75 11.9665 9.75 11C9.75 10.0335 10.5335 9.25 11.5 9.25C12.4665 9.25 13.25 10.0335 13.25 11ZM19 12.75C19.9665 12.75 20.75 11.9665 20.75 11C20.75 10.0335 19.9665 9.25 19 9.25C18.0335 9.25 17.25 10.0335 17.25 11C17.25 11.9665 18.0335 12.75 19 12.75Z" fill="white"/>\n</svg>\n',"image/svg+xml").firstChild}var pN,vN,gN=function(e){XP(n,e);var t=$P(n);function n(){return Iy(this,n),t.apply(this,arguments)}return Ly(n,[{key:"onItemClick",value:function(){}},{key:"onOptionClick",value:function(){this.toggle(!1)}},{key:"updateLang",value:function(){var e,t=this,n=this.player.config.i18nManager;null===(e=this.config.plugins)||void 0===e||e.forEach((function(e){var r,i=null!==(r=n.normalize(e.textTips))&&void 0!==r?r:e.pluginName;if(t.player.root){var o=bC.findDom(t.player.root,".panel-plugin-".concat(e.pluginName," .panel-icon-bt-text"));o&&(o.innerHTML=i)}}))}},{key:"beforeCreate",value:function(e){var t,n=e.player.config.i18nManager;e.config.list=null===(t=e.config.plugins)||void 0===t?void 0:Bw(t).call(t,(function(t,r){var i,o=bC.createDom("div","",{},"panel-icon-bt panel-plugin-".concat(t.pluginName)),a=bC.createDom("div","",{},"panel-icon-svg"),s=null!==(i=n.normalize(t.textTips))&&void 0!==i?i:t.pluginName;return o.appendChild(a),o.appendChild(bC.createDom("span",s,{"plugin-index":r},"panel-icon-bt-text")),{showText:o,plugin:e.player.registerPlugin({plugin:t,options:{root:a}})}}))}},{key:"hideChild",value:function(e){if(e){var t;null===(t=e.hide)||void 0===t||t.call(e);var n=document.querySelector(".panel-plugin-".concat(e.pluginName));n&&bC.addClass(n,"hide")}}},{key:"showChild",value:function(e){if(e){var t;null===(t=e.show)||void 0===t||t.call(e);var n=document.querySelector(".panel-plugin-".concat(e.pluginName));n&&bC.removeClass(n,"hide")}}},{key:"registerIcons",value:function(){return{more:{icon:dN}}}}],[{key:"pluginName",get:function(){return"more"}},{key:"defaultConfig",get:function(){return Dm(Dm({},pL.defaultConfig),{},{index:1,plugins:[],list:[],hideOnSingleOption:!1,isShowIcon:!0,hidePortrait:!1,renderType:lL.Icon,panel:{className:"more-panel-content"}})}}]),n}(pL),yN=function(e){return e.ROOT_TOP_RIGHT="rootTopRight",e.ROOT_TOP_RIGHT_COLLAPSE="rootTopRightCollapse",e}(yN||{}),mN=function(e){XP(n,e);var t=$P(n);function n(){return Iy(this,n),t.apply(this,arguments)}return Ly(n,[{key:"registerIcons",value:function(){return{more:{icon:dN,class:"xgplayer-more-svg"}}}},{key:"children",value:function(){var e=this,t=this._getPlugins(),n=t.plugins,r=t.collapsePlugins,i={};return n.forEach((function(t){i[t.pluginName]={plugin:t,options:{root:e.root}}})),r.length&&(i[gN.pluginName]={plugin:gN,options:{root:this.root,plugins:r}}),i}},{key:"render",value:function(){return'<xg-right-grid class="xg-right-grid xgplayer-rightTop-iconList"></xg-right-grid>'}},{key:"_getPlugins",value:function(){var e,t=this,n=[],r=[],i=null===(e=this.player.config.ignores)||void 0===e?void 0:Bw(e).call(e,(function(e){return e.toLowerCase()}));return this.config.plugins.forEach((function(e){var o,a=e.pluginName.toLowerCase();null!=i&&ck(i).call(i,a)||("rootTopRight"===(null!==(o=t.player.config[a].position)&&void 0!==o?o:e.defaultConfig.position)?n.push(e):r.push(e))})),{plugins:null!=n?n:[],collapsePlugins:null!=r?r:[]}}}],[{key:"pluginName",get:function(){return"topRightBar"}},{key:"defaultConfig",get:function(){return{position:qx.POSITIONS.ROOT_TOP,plugins:[]}}},{key:"isTopRightPlugin",value:function(e,t){var n,r,i,o,a,s=null!==(r=null===(i=t[null==e||null===(n=e.pluginName)||void 0===n?void 0:n.toLowerCase()])||void 0===i?void 0:i.position)&&void 0!==r?r:null==e||null===(o=e.defaultConfig)||void 0===o?void 0:o.position;return!!ck(a=fN(yN)).call(a,s)}}]),n}(qx),_N=[dI,mI,kI,xA,PI,lA,dA,nI,pA,rL,iL,vL,_L,cN],bN=Fm(pN=[]).call(pN,_N,[eI,IA,mN]),wN=Fm(vN=[]).call(vN,_N,[qA,mN]),kN="pc"===xC.device?bN:wN;function EN(){return(new DOMParser).parseFromString('<svg width="21" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.892 2h4.445v1.778H3.892v4.444H2.114V3.778C2.114 2.796 2.91 2 3.892 2zm4.445 16v-4.444c0-.982-.796-1.778-1.778-1.778H2.114v1.778h2.944L2.264 16.35a.9.9 0 001.272 1.273l2.988-2.987a.918.918 0 00.035-.037V18h1.778zm8-6.222v4.444h-4.445V18h4.445c.981 0 1.777-.796 1.777-1.778v-4.444h-1.777zM11.892 2v4.445c0 .981.796 1.777 1.778 1.777h4.444V6.445H15.17l2.568-2.568a.9.9 0 10-1.273-1.273L13.67 5.4V2h-1.778z" fill="#fff"/></svg>',"image/svg+xml").firstChild}function TN(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.778 2h4.444v1.778H3.778v4.444H2V3.778C2 2.796 2.796 2 3.778 2zM2 11.778v4.444C2 17.204 2.796 18 3.778 18h4.444v-1.778H4.823l2.313-2.313a.9.9 0 00-1.272-1.273l-2.086 2.086v-2.944H2zm14.222 0v4.444h-4.444V18h4.444c.982 0 1.778-.796 1.778-1.778v-4.444h-1.778zM18 8.222V3.778C18 2.796 17.204 2 16.222 2h-4.444v1.778h2.945l-2.587 2.586a.9.9 0 101.273 1.273l2.813-2.813v3.398H18z" fill="#fff"/></svg>',"image/svg+xml").firstChild}var SN={startPlay:function(){return(new DOMParser).parseFromString('<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="60" height="60" rx="30" fill="#000" fill-opacity=".4"/><path d="M40.243 28.3a2 2 0 010 3.401L24.06 41.712c-1.332.825-3.052-.134-3.052-1.7V19.988c0-1.566 1.72-2.525 3.052-1.7l16.184 10.01z" fill="#fff"/></svg>',"image/svg+xml").firstChild},play:function(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.121 8.299a2 2 0 010 3.402l-7.94 4.91c-1.332.825-3.051-.133-3.051-1.7V5.09c0-1.567 1.72-2.525 3.052-1.701l7.939 4.911z" fill="#fff"/></svg>',"image/svg+xml").firstChild},pause:function(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="5.313" y="3.75" width="3.125" height="12.5" rx=".625" fill="#fff"/><rect x="11.563" y="3.75" width="3.125" height="12.5" rx=".625" fill="#fff"/></svg>',"image/svg+xml").firstChild},fullscreen:TN,exitFullscreen:EN,replay:function(){return(new DOMParser).parseFromString('<svg width="72" height="72" viewBox="0 0 72 72" xmlns="http://www.w3.org/2000/svg"><g fill="#FFF" fill-rule="evenodd"><circle fill-opacity=".3" cx="36" cy="36" r="36"/><path d="M40.167 32.667a.833.833 0 01-.834-.834v-1.666c0-.46.373-.834.834-.834h5.409a11.653 11.653 0 00-9.576-5c-6.443 0-11.667 5.224-11.667 11.667S29.557 47.667 36 47.667c5.285 0 9.749-3.514 11.183-8.333h3.445C49.112 46.014 43.138 51 36 51c-8.284 0-15-6.716-15-15 0-8.284 6.716-15 15-15 4.713 0 8.918 2.173 11.668 5.572l-.001-4.739c0-.46.373-.833.833-.833h1.667c.46 0 .833.373.833.833V31c0 .879-.68 1.598-1.542 1.662l-.125.005h-9.166z"/></g></svg>',"image/svg+xml").firstChild},volumeLarge:function(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.867 2.5h.55c.44 0 .799.34.83.771l.003.062v13.334c0 .44-.34.799-.771.83l-.062.003h-.55a.833.833 0 01-.444-.128l-.064-.045-4.867-3.744a.831.831 0 01-.322-.59l-.003-.07V7.077c0-.235.099-.458.271-.615l.054-.045L9.36 2.673a.832.832 0 01.43-.17l.078-.003h.55-.55zm6.767 2.278A7.474 7.474 0 0118.75 10a7.477 7.477 0 01-2.128 ********* 0 01-.57-.004l-.587-.586a.442.442 0 01.005-.617A5.812 5.812 0 0017.083 10c0-1.557-.61-2.97-1.603-4.017a.442.442 0 01-.003-.615l.586-.586a.4.4 0 01.57-.004zM2.5 6.667c.23 0 .417.186.417.416v5.834c0 .23-.187.416-.417.416h-.833a.417.417 0 01-.417-.416V7.083c0-.23.187-.416.417-.416H2.5zm11.768.46A4.153 4.153 0 0115.417 10c0 1.12-.442 2.137-1.162 2.886a.388.388 0 01-.555-.007l-.577-.578c-.176-.176-.156-.467.009-.655A2.49 2.49 0 0013.75 10a2.49 2.49 0 00-.61-1.636c-.163-.188-.182-.477-.006-.653l.578-.578a.388.388 0 01.556-.006z" fill="#fff"/></svg>',"image/svg+xml").firstChild},volumeMuted:function(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.045 2.5h.55c.44 0 .8.34.831.771l.003.062v13.334c0 .44-.34.799-.771.83l-.063.003h-.55a.833.833 0 01-.443-.128l-.065-.045-4.866-3.744a.831.831 0 01-.323-.59l-.003-.07V7.077c0-.235.1-.458.272-.615l.054-.045 4.866-3.744a.832.832 0 01.43-.17l.078-.003h.55-.55zM2.68 6.667c.23 0 .416.186.416.416v5.834c0 .23-.186.416-.416.416h-.834a.417.417 0 01-.416-.416V7.083c0-.23.186-.416.416-.416h.834zm10.467.294a.417.417 0 01.59 0l1.767 1.768L17.27 6.96a.417.417 0 01.589 0l.59.59a.417.417 0 010 .589L16.68 9.908l1.768 1.767c.15.15.162.387.035.55l-.035.04-.589.589a.417.417 0 01-.59 0l-1.767-1.768-1.768 1.768a.417.417 0 01-.59 0l-.588-.59a.417.417 0 010-.589l1.767-1.768-1.767-1.767a.417.417 0 01-.035-.55l.035-.04.589-.589z" fill="#fff"/></svg>',"image/svg+xml").firstChild},volumeSmall:function(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.867 2.5h.55c.44 0 .799.34.83.771l.003.062v13.334c0 .44-.34.799-.771.83l-.062.003h-.55a.833.833 0 01-.444-.128l-.064-.045-4.867-3.744a.831.831 0 01-.322-.59l-.003-.07V7.077c0-.235.099-.458.271-.615l.054-.045L9.36 2.673a.832.832 0 01.43-.17l.078-.003h.55-.55zM2.5 6.667c.23 0 .417.186.417.416v5.834c0 .23-.187.416-.417.416h-.833a.417.417 0 01-.417-.416V7.083c0-.23.187-.416.417-.416H2.5zm11.768.46A4.153 4.153 0 0115.417 10c0 1.12-.442 2.137-1.162 2.886a.388.388 0 01-.555-.007l-.577-.578c-.176-.176-.156-.467.009-.655A2.49 2.49 0 0013.75 10a2.49 2.49 0 00-.61-1.636c-.163-.188-.182-.477-.006-.653l.578-.578a.388.388 0 01.556-.006z" fill="#fff"/></svg>',"image/svg+xml").firstChild}},CN=Dm(Dm({},SN),{},{fullscreen:TN,exitFullscreen:EN}),xN=Dm(Dm({},SN),{},{fullscreen:function(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12.125 3a.375.375 0 00-.375.375v.75c0 .207.168.375.375.375H15.5v3.375c0 .207.168.375.375.375h.75A.375.375 0 0017 7.875v-4.5A.375.375 0 0016.625 3h-4.5zm0 12.5a.375.375 0 00-.375.375v.75c0 .207.168.375.375.375h4.5a.375.375 0 00.375-.375v-4.5a.375.375 0 00-.375-.375h-.75a.375.375 0 00-.375.375V15.5h-3.375zM3 3.375C3 3.168 3.168 3 3.375 3h4.5c.207 0 .375.168.375.375v.75a.375.375 0 01-.375.375H4.5v3.375a.375.375 0 01-.375.375h-.75A.375.375 0 013 7.875v-4.5zM7.875 15.5H4.5v-3.375a.375.375 0 00-.375-.375h-.75a.375.375 0 00-.375.375v4.5c0 .207.168.375.375.375h4.5a.375.375 0 00.375-.375v-.75a.375.375 0 00-.375-.375z" fill="#fff"/></svg>',"image/svg+xml").firstChild},exitFullscreen:function(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.5 8.333h-4.167c-.92 0-1.666-.746-1.666-1.666V2.5h1.666v4.167H17.5v1.666zM2.5 6.667v1.666h4.167c.92 0 1.666-.746 1.666-1.666V2.5H6.667v4.167H2.5zM2.5 13.333h4.167V17.5h1.666v-4.167c0-.92-.746-1.666-1.666-1.666H2.5v1.666zM13.333 17.5v-4.167H17.5v-1.666h-4.167c-.92 0-1.666.746-1.666 1.666V17.5h1.666z" fill="#fff"/></svg>',"image/svg+xml").firstChild}}),PN="pc"===xC.device?CN:xN,AN=Dm(Dm({},wP.TEXT),{},{DEFINITION_FALLBACK_TOAST:"The current network is unstable, it is recommended to switch to ",DEFINITION_SWITCHING:"Switching to",ERROR_REFRESH:"Refresh",UNMUTE:"Click to unmute",MANIFEST:"Video parsing error",NETWORK:"Network error",NETWORK_TIMEOUT:"Network timeout",NETWORK_FORBIDDEN:"Authentication  error",NETWORK_NOTFOUND:"Stream does not exist",DEMUX:"Video parsing error",REMUX:"Video parsing error",MEDIA:"An error occurred, Please try again",MEDIA_ERR_CODEC_NOT_SUPPORTED:"Audio/video codec is not supported",MEDIA_ERR_URL_EMPTY:"The stream address is not specified",DRM:"Permission verification failed",OTHER:"Unknown error",RUNTIME:"An error occurred, Please try again",MODULE_LOAD_ERROR:"CDN fetch error",UNKNOWN:"Unknown error"}),IN=Dm(Dm({},{ERROR_TYPES:{network:{code:1,msg:"视频下载错误"},mse:{code:2,msg:"流追加错误"},parse:{code:3,msg:"解析错误"},format:{code:4,msg:"格式错误"},decoder:{code:5,msg:"解码错误"},runtime:{code:6,msg:"语法错误"},timeout:{code:7,msg:"播放超时"},other:{code:8,msg:"其他错误"}},HAVE_NOTHING:"没有关于音频/视频是否就绪的信息",HAVE_METADATA:"音频/视频的元数据已就绪",HAVE_CURRENT_DATA:"关于当前播放位置的数据是可用的，但没有足够的数据来播放下一帧/毫秒",HAVE_FUTURE_DATA:"当前及至少下一帧的数据是可用的",HAVE_ENOUGH_DATA:"可用数据足以开始播放",NETWORK_EMPTY:"音频/视频尚未初始化",NETWORK_IDLE:"音频/视频是活动的且已选取资源，但并未使用网络",NETWORK_LOADING:"浏览器正在下载数据",NETWORK_NO_SOURCE:"未找到音频/视频来源",MEDIA_ERR_ABORTED:"取回过程被用户中止",MEDIA_ERR_NETWORK:"网络错误",MEDIA_ERR_DECODE:"解码错误",MEDIA_ERR_SRC_NOT_SUPPORTED:"不支持的音频/视频格式",REPLAY:"重播",ERROR:"网络连接似乎出现了问题",PLAY_TIPS:"播放",PAUSE_TIPS:"暂停",PLAYNEXT_TIPS:"下一集",DOWNLOAD_TIPS:"下载",ROTATE_TIPS:"旋转",RELOAD_TIPS:"重新载入",FULLSCREEN_TIPS:"进入全屏",EXITFULLSCREEN_TIPS:"退出全屏",CSSFULLSCREEN_TIPS:"进入样式全屏",EXITCSSFULLSCREEN_TIPS:"退出样式全屏",TEXTTRACK:"字幕",PIP:"画中画",SCREENSHOT:"截图",LIVE:"正在直播",OFF:"关闭",OPEN:"开启",MINI_DRAG:"点击按住可拖动视频",MINISCREEN:"小屏幕",REFRESH_TIPS:"请试试",REFRESH:"刷新",FORWARD:"快进中",LIVE_TIP:"直播"}),{},{DEFINITION_FALLBACK_TOAST:"当前网络不稳定，建议切换到",DEFINITION_SWITCHING:"正在切换至",ERROR_REFRESH:"刷新",UNMUTE:"点击取消静音",MANIFEST:"视频解析错误",NETWORK:"网络错误",NETWORK_TIMEOUT:"网络超时",NETWORK_FORBIDDEN:"鉴权异常",NETWORK_NOTFOUND:"播放地址不存在",DEMUX:"视频解析错误",REMUX:"视频解析错误",MEDIA:"播放异常，请重试",MEDIA_ERR_CODEC_NOT_SUPPORTED:"不支持的音频/视频格式",MEDIA_ERR_URL_EMPTY:"当前播放地址为空",DRM:"权限验证失败",OTHER:"其他报错",RUNTIME:"播放异常，请重试",MODULE_LOAD_ERROR:"插件模块加载异常",UNKNOWN:"未知报错"}),RN=function(){function e(t){var n;if(Iy(this,e),Ny(this,"_lang",void 0),this._lang=(null==t?void 0:t.lang)||EC()||"en","zh"===this._lang&&(this._lang="zh-cn"),null!=t&&null!==(n=t.i18n)&&void 0!==n&&n.texts){var r,i,o=Bw(r=BM(null==t||null===(i=t.i18n)||void 0===i?void 0:i.texts)).call(r,(function(e){var t=AM(e,2);return{LANG:t[0],TEXT:t[1]}}));e.extend(o)}}return Ly(e,[{key:"setLang",value:function(e){this._lang=e}},{key:"getLang",value:function(){return this._lang}},{key:"normalize",value:function(e){return e?"string"==typeof e?e:e[this._lang]:""}},{key:"getText",value:function(e){var t,n,r;if(e){var i=null==CP?void 0:CP.lang;return null!==(t=null==i||null===(n=i[this._lang])||void 0===n?void 0:n[e])&&void 0!==t?t:null==i||null===(r=i.en)||void 0===r?void 0:r[e]}}}],[{key:"langKeys",get:function(){return CP.langKeys}},{key:"isLangValid",value:function(e){var t;return ck(t=CP.langKeys).call(t,e)||"zh"===e}},{key:"extend",value:function(e,t){CP.extend(e,t)}}]),e}(),ON=["autoplay"],DN=Dm(Dm({},qx.POSITIONS),yN),MN={id:"veplayer",width:"100%",height:"100%",decodeType:r.Hardware,fullscreen:{useCssFullscreen:!1}},LN=function(e){var t,n,r,i=e.autoplay,o=em(e,ON);(o.poster&&(o.poster=function(e){var t,n=""+e,r=PM.exec(n);if(!r)return n;var i,o="",a=0;for(i=r.index;i<n.length;i++){switch(n.charCodeAt(i)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#x27;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}a!==i&&(o+=n.substring(a,i)),a=i+1,o+=t}return a!==i?o+n.substring(a,i):o}(o.poster)),o.icons=Dm(Dm({},PN),o.icons?(t=o.icons,n={},BM(null!=t?t:{}).forEach((function(e){var t=AM(e,2),r=t[0],i=t[1];"string"==typeof i?console.warn("icons.".concat(r," has been removed")):n[r]=i})),n):{}),o.autoplay=Boolean(i),i&&"boolean"!=typeof i)&&(o.autoplayMuted=null==i?void 0:i.muted,o.videoAttributes=Dm(Dm({},null!==(r=o.videoAttributes)&&void 0!==r?r:{}),{},{muted:null==i?void 0:i.muted}),o.autoplayPlugin=i);return delete o.i18n,o},NN=function(){function e(){var t,n,r,i,o,a,s,l,u,c,h,f,d=this,p=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Iy(this,e),Ny(this,"_player",void 0),Ny(this,"_sourceManager",void 0),Ny(this,"_preparePlugins",void 0),Ny(this,"_previousPrepareResult",void 0),Ny(this,"_i18nManager",void 0),Ny(this,"_events",{}),Ny(this,"_errorCallback",void 0),this._sourceManager=p.sourceManager,this._preparePlugins=p.preparePlugins,this._previousPrepareResult=p.prepareResult,this._i18nManager=null!==(t=p.i18nManager)&&void 0!==t?t:new RN({i18n:p.i18n});var v=LN(Dm(Dm({},p),null===(n=this._previousPrepareResult)||void 0===n?void 0:n.options)),g=Fm(r=[]).call(r,Ay(null!==(i=null===(o=this._previousPrepareResult)||void 0===o?void 0:o.plugins)&&void 0!==i?i:[]),Ay(null!==(a=p.plugins)&&void 0!==a?a:kN)),y=[],m=[];g.forEach((function(e){mN.isTopRightPlugin(e,p)?y.push(e):m.push(e)})),this._player=new FP(Dm(Dm(Dm({},MN),v),{},{definition:Dm({list:null!==(s=null===(l=this._sourceManager.source)||void 0===l?void 0:l.definitions)&&void 0!==s?s:[],defaultDefinition:null===(u=this._sourceManager.definition)||void 0===u?void 0:u.definition},null!==(c=p.definition)&&void 0!==c?c:{}),url:null===(h=this._sourceManager.definition)||void 0===h?void 0:h.url,sources:Dm(Dm({},null!==(f=p.sources)&&void 0!==f?f:{}),{},{sourceManager:this._sourceManager}),topRightBar:{plugins:y},plugins:m,i18nManager:this._i18nManager,veplayer:this})),this.emit(CM.PLAYER_CREATE_FINISH,this._player),this._errorCallback=function(e){return d._handleFallback(e)},this._player.on(LC,this._errorCallback)}var t,n,r,i,o,a;return Ly(e,[{key:"readyState",get:function(){return this._player.readyState}},{key:"buffered",get:function(){return this._player.buffered}},{key:"played",get:function(){return this._player.played}},{key:"cumulateTime",get:function(){return this._player.cumulateTime}},{key:"isFocused",get:function(){return this._player.isActive}},{key:"isFullscreen",get:function(){return this._player.isFullscreen}},{key:"isCssFullscreen",get:function(){return this._player.isCssfullScreen}},{key:"networkState",get:function(){return this._player.networkState}},{key:"paused",get:function(){return this._player.paused}},{key:"ended",get:function(){return this._player.ended}},{key:"state",get:function(){return this._player.state}},{key:"url",get:function(){return this._player.config.url}},{key:"source",get:function(){var e;return null===(e=this._sourceManager.source)||void 0===e?void 0:e.name}},{key:"definition",get:function(){var e;return null===(e=this._sourceManager.definition)||void 0===e?void 0:e.definition}},{key:"crossOrigin",get:function(){return this._player.crossOrigin},set:function(e){this._player.crossOrigin=e}},{key:"volume",get:function(){return this._player.volume},set:function(e){this._player.volume=e}},{key:"muted",get:function(){return this._player.muted},set:function(e){this._player.muted=e}},{key:"lang",get:function(){return this._i18nManager.getLang()},set:function(e){if(this._player.lang!==e)if(RN.isLangValid(e))this._i18nManager.setLang(e),this._player.lang=e;else{var t,n,r,i,o=RN.langKeys.join(","),a={en:Fm(t="Sorry, we couldn't set the language to ".concat(e," because it's not currently supported. The list of supported languages includes ")).call(t,o,"."),"zh-cn":Fm(n="不支持当前设置的语言".concat(e,", 支持的语言有")).call(n,o,", 请重新设置")};console.error((null===(r=this._i18nManager)||void 0===r||null===(i=r.normalize)||void 0===i?void 0:i.call(r,a))||a.en)}}},{key:"_src",get:function(){return this._player.currentSrc}},{key:"switch",value:(a=ay(iy().mark((function e(t,n){var r,i,o,a;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("string"!=typeof t||1!==this._sourceManager.sources.length||1!==(null===(r=this._sourceManager.sources)||void 0===r||null===(r=r[0].definitions)||void 0===r?void 0:r.length)){e.next=5;break}return this._sourceManager.updateSources(t),e.next=4,this._switchUrl(t);case 4:return e.abrupt("return",this._sourceManager.definition);case 5:if(o=qm(i=this._sourceManager).call(i,t,n)){e.next=8;break}throw new Error("string"==typeof t?"No definition found for url: ".concat(t):Fm(a="No definition found for source: ".concat(t.source,", definition: ")).call(a,t.definition));case 8:return e.next=10,this._switch(o);case 10:return e.abrupt("return",o);case 11:case"end":return e.stop()}}),e,this)}))),function(e,t){return a.apply(this,arguments)})},{key:"updatePlaylist",value:function(e,t){var n=this._sourceManager.updateSources(e,t);return this._switch(n.definition)}},{key:"once",value:function(e,t){var n,r=this._transformEvent(e,t),i=r.xgCallback,o=r.xgEventName;i&&(null!==(n=this._events)&&void 0!==n&&n[e]||(this._events[e]=new Dw),this._events[e].set(t,i),this._player.once(o,i))}},{key:"off",value:function(e,t){var n,r=null===(n=this._events[e])||void 0===n?void 0:n.get(t),i=this._transformEvent(e).xgEventName;r&&(this._player.off(i,r),this._events[e].delete(t))}},{key:"emit",value:function(e,t){var n=this._transformEvent(e).xgEventName;this._player.emit(n,t)}},{key:"offAll",value:function(){this._events={},this._player.offAll()}},{key:"on",value:function(e,t){var n,r=this._transformEvent(e,t),i=r.xgCallback,o=r.xgEventName;i&&(null!==(n=this._events)&&void 0!==n&&n[e]||(this._events[e]=new Dw),this._events[e].set(t,i),this._player.on(o,i))}},{key:"play",value:function(){return this._player.play()}},{key:"pause",value:function(){return this._player.pause()}},{key:"requestPIP",value:function(){var e;null===(e=this._player.plugins.pip)||void 0===e||e.requestPIP()}},{key:"exitPIP",value:function(){var e;null===(e=this._player.plugins.pip)||void 0===e||e.exitPIP()}},{key:"retry",value:function(){return this._player.retry()}},{key:"focus",value:function(e){return this._player.focus(e)}},{key:"blur",value:function(e){return this._player.blur(e)}},{key:"requestFullscreen",value:function(e){return this._player.getFullscreen(e)}},{key:"exitFullscreen",value:function(e){return this._player.exitFullscreen(e)}},{key:"requestCssFullscreen",value:function(e){return this._player.getCssFullscreen(e)}},{key:"exitCssFullscreen",value:function(){return this._player.exitCssFullscreen()}},{key:"registerPlugin",value:function(e,t){return this._player.registerPlugin(e,t)}},{key:"unRegisterPlugin",value:function(e){return this._player.unRegisterPlugin(e)}},{key:"showIcon",value:function(e){var t=this,n=this._player.getPlugin("more");e.forEach((function(e){var r=t._player.getPlugin(e);r&&null!=r&&r.root&&(r.config.position===DN.ROOT_TOP_RIGHT_COLLAPSE?null==n||n.showChild(r):r.show())}))}},{key:"hideIcon",value:function(e){var t=this,n=this._player.getPlugin("more");e.forEach((function(e){var r=t._player.getPlugin(e);r&&null!=r&&r.root&&(r.config.position===DN.ROOT_TOP_RIGHT_COLLAPSE?null==n||n.hideChild(r):r.hide())}))}},{key:"destroy",value:function(){this._player.off(LC,this._errorCallback),this._player.destroy()}},{key:"prepare",value:(o=ay(iy().mark((function e(t){var n,r,i,o,a,s,l,u,c=this;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null===(n=this._preparePlugins)||void 0===n?void 0:n.call(this,t);case 2:return a=e.sent,s=this._diffPlugins(null!==(r=null===(i=this._previousPrepareResult)||void 0===i?void 0:i.plugins)&&void 0!==r?r:[],null!==(o=null==a?void 0:a.plugins)&&void 0!==o?o:[]),l=s.removedPlugins,u=s.addedPlugins,null==l||l.forEach((function(e){c._player.unRegisterPlugin(e)})),this._previousPrepareResult=a,null!=a&&a.options&&this._player.setConfig(a.options),e.abrupt("return",{plugins:Bw(u).call(u,(function(e){return c._player.registerPlugin(e)})),options:null==a?void 0:a.options});case 8:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"_handleFallback",value:(i=ay(iy().mark((function e(t){var n,r,i=this;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._player.addClass($x),n=this._sourceManager.url,r=this._sourceManager.fallback()){e.next=7;break}return this._player.removeClass($x),this.emit(CM.FALLBACK_ERROR,sA(t,this._i18nManager)),e.abrupt("return");case 7:this._player.once("canplay",(function(){i._sourceManager.resetFallback()})),this.emit(CM.FALLBACK,{from:{url:n},to:{url:r}}),this._switchUrl(r);case 10:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"_transformEvent",value:function(e,t){var n=this;return e===LC?{xgCallback:t?function(e){t(sA(e,n._i18nManager))}:void 0,xgEventName:e}:function(e,t){var n=xM[e];if(n)return{xgEventName:n.event,xgCallback:t?n.shouldCallback?function(){for(var e,r,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];null!==(e=n.shouldCallback)&&void 0!==e&&e.call.apply(e,Fm(r=[n]).call(r,o))&&(null==t||t.apply(void 0,o))}:t:void 0};return{xgEventName:e,xgCallback:t}}(e,t)}},{key:"_switch",value:(r=ay(iy().mark((function e(t){var n,r,i,o,a,s,l,u;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=mL(this._sourceManager.definition),e.next=3,this.prepare(t.url);case 3:if(e.t0=e.sent,e.t0){e.next=6;break}e.t0={};case 6:if(s=e.t0,l=s.plugins,null!=(u=s.options)&&u.url&&(t.url=null==u?void 0:u.url),this._sourceManager.switch(t),null===(n=this._player)||void 0===n||null===(n=n.plugins)||void 0===n||n.sources.renderItemList(),null===(r=this._player)||void 0===r||null===(r=r.plugins)||void 0===r||r.definition.renderItemList(),(null==t||null===(i=t.source)||void 0===i?void 0:i.name)!==(null==a||null===(o=a.source)||void 0===o?void 0:o.name)&&this.emit(CM.SOURCE_CHANGE),null==l||!l.length){e.next=21;break}return this.emit(CM.DEFINITION_CHANGE),e.next=18,this._callBeforePlayerInitForUrl(l,t.url);case 18:this.emit(CM.AFTER_DEFINITION_CHANGE),e.next=22;break;case 21:this._player.changeDefinition(t,a);case 22:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"_switchUrl",value:(n=ay(iy().mark((function e(t){var n,r,i,o,a,s,l,u=this;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.prepare(t);case 2:if(e.t0=e.sent,e.t0){e.next=5;break}e.t0={};case 5:if(r=e.t0,i=r.plugins,o=r.options,a=null!==(n=null==o?void 0:o.url)&&void 0!==n?n:t,this._sourceManager.url=a,null==i||!i.length){e.next=14;break}this._callBeforePlayerInitForUrl(i,a),e.next=21;break;case 14:if(s=this._player.switchURL(a,!1),l=this._player.currentTime,!s||!s.then){e.next=20;break}return e.abrupt("return",s);case 20:return e.abrupt("return",new Vw((function(e){var t=u._player.paused&&!u._player.isError,n=function(){u._player.currentTime=l,t&&u._player.once(BC,(function(){u._player.pause()})),e()};xC.os.isAndroid?u.once(UC,(function(){n()})):u.once(BC,(function(){n()})),u._player.play()})));case 21:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"_diffPlugins",value:function(e,t){var n=Bw(e).call(e,(function(e){return e.pluginName})),r=Bw(t).call(t,(function(e){return e.pluginName}));return{removedPlugins:Hw(n).call(n,(function(e){return!ck(r).call(r,e)})),addedPlugins:Hw(t).call(t,(function(e){return!ck(n).call(n,e.pluginName)}))}}},{key:"_callBeforePlayerInitForUrl",value:function(e,t){var n=this;return new Vw((function(r){n._player.config.url=t,null==e||e.forEach((function(e){e.beforePlayerInit()})),n._player.on(CM.CANPLAY,(function(){r(void 0)}))}))}}],[{key:"create",value:(t=ay(iy().mark((function t(){var n,r,i,o,a,s,l,u,c=arguments;return iy().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=c.length>0&&void 0!==c[0]?c[0]:{},a=c.length>1?c[1]:void 0,s=new eL({sources:ZM.normalize({url:o.url,fallbackUrls:o.fallbackUrls,sources:o.playlist}),url:o.url,defaultSource:o.defaultSource,defaultDefinition:o.defaultDefinition,maxFallbackRound:o.maxFallbackRound}),t.next=5,null==o||null===(n=o.preparePlugins)||void 0===n?void 0:n.call(o,null!==(r=s.url)&&void 0!==r?r:"");case 5:return null!=(l=t.sent)&&null!==(i=l.options)&&void 0!==i&&i.url&&(s.url=null==l||null===(u=l.options)||void 0===u?void 0:u.url),o.url=s.url,t.abrupt("return",new(null!=a?a:e)(Dm(Dm({},o),{},{prepareResult:l,sourceManager:s})));case 9:case"end":return t.stop()}}),t)}))),function(){return t.apply(this,arguments)})}]),e}();function UN(e){var t=QM(e).call(e,(function(e,t){return e+t.byteLength}),0),n=new Uint8Array(t),r=0;return e.forEach((function(e){n.set(e,r),r+=e.byteLength})),n}function FN(e,t){return _S(null==e?void 0:e.toFixed(t||4))}var BN,VN,HN=gE,jN=function(e,t,n){return function(e,t,n){t in e?HN(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n}(e,"symbol"!==Sc(t)?t+"":t,n),n},zN="ScriptData",KN="Sequence_Header",WN="RawData",GN=[5500,11e3,22e3,44e3],qN="aac",YN="pcma",XN="pcmu",JN="avc",QN="bytevc1",$N=function(){function e(t){var n=this;Iy(this,e),jN(this,"type",0),jN(this,"tagSize",0),jN(this,"timestamp",0),jN(this,"duration",0),jN(this,"header",null),jN(this,"body",null),jN(this,"bodyView",null),jN(this,"tailsize",null),fk(t).forEach((function(e){n[e]=t[e]}))}return Ly(e,[{key:"streamType",get:function(){switch(this.type){case 18:return"scriptData";case 9:return"video";case 8:return"audio"}}},{key:"packetType",get:function(){var e;return null==(e=this.bodyView)?void 0:e.packetType}},{key:"frameType",get:function(){var e;return null==(e=this.bodyView)?void 0:e.frameType},set:function(e){this.bodyView.frameType=e}},{key:"dts",get:function(){return this.timestamp}},{key:"pts",get:function(){var e;return((null==(e=this.bodyView)?void 0:e.cts)||0)+this.timestamp}},{key:"ptsTime",get:function(){return this.pts?FN(this.pts/1e3):0}},{key:"dtsTime",get:function(){return this.dts?FN(this.dts/1e3):0}},{key:"sampleView",get:function(){var e,t,n;if("Sequence_Header"!==(null==(e=this.bodyView)?void 0:e.packetType)&&18!==this.type)return{dts:this.timestamp,pts:((null==(t=this.bodyView)?void 0:t.cts)||0)+this.timestamp,data:this._getSampleData(),units:null==(n=this.bodyView)?void 0:n.units}}},{key:"size",get:function(){var e,t;return null==(t=null==(e=this.sampleView)?void 0:e.data)?void 0:t.length}},{key:"data",get:function(){return this._getSampleData()}},{key:"nalTypes",get:function(){var e,t;return null==(t=null==(e=this.bodyView)?void 0:e.units)?void 0:Bw(t).call(t,(function(e){return e.type}))}},{key:"nalSizes",get:function(){var e,t;return null==(t=null==(e=this.bodyView)?void 0:e.units)?void 0:Bw(t).call(t,(function(e){return e.unit.length}))}},{key:"keyframe",get:function(){return"I"===this.frameType}},{key:"isSample",get:function(){return"scriptData"!==this.streamType&&"Sequence_Header"!==this.packetType}},{key:"isVideo",get:function(){return this.isSample&&"video"===this.streamType}},{key:"isAudio",get:function(){return this.isSample&&"audio"===this.streamType}},{key:"isSqHeader",get:function(){return"Sequence_Header"===this.packetType}},{key:"timescale",get:function(){return 1e3}},{key:"isHevc",get:function(){var e;return"bytevc1"===(null==(e=this.bodyView)?void 0:e.codecType)}},{key:"_getSampleData",value:function(){var e,t;if("audio"===this.streamType)return this.bodyView.sample;var n=(null==(t=this.bodyView)?void 0:Hw(e=t.units).call(e,(function(e){return!e.isSei})))||[],r=QM(n).call(n,(function(e,t){return e+(t.unit.byteLength+4)}),0),i=new Uint8Array(r),o=0;return n.forEach((function(e){i.set([0,0,0,1],o),o+=4,i.set(e.unit,o),o+=e.unit.byteLength})),i}}]),e}(),ZN=function(){function e(){Iy(this,e)}return Ly(e,null,[{key:"decode",value:function(t){for(var n=[],r=t,i=0,o=t.length;i<o;)if(r[i]<128)n.push(String.fromCharCode(r[i])),++i;else{if(r[i]<192);else if(r[i]<224){if(e._checkContinuation(r,i,1)){var a=(31&r[i])<<6|63&r[i+1];if(a>=128){n.push(String.fromCharCode(65535&a)),i+=2;continue}}}else if(r[i]<240){if(e._checkContinuation(r,i,2)){var s=(15&r[i])<<12|(63&r[i+1])<<6|63&r[i+2];if(s>=2048&&55296!=(63488&s)){n.push(String.fromCharCode(65535&s)),i+=3;continue}}}else if(r[i]<248&&e._checkContinuation(r,i,3)){var l=(7&r[i])<<18|(63&r[i+1])<<12|(63&r[i+2])<<6|63&r[i+3];if(l>65536&&l<1114112){l-=65536,n.push(String.fromCharCode(l>>>10|55296)),n.push(String.fromCharCode(1023&l|56320)),i+=4;continue}}n.push(String.fromCharCode(65533)),++i}return n.join("")}},{key:"_checkContinuation",value:function(e,t,n){var r=e;if(t+n<r.length){for(;n--;)if(128!=(192&r[++t]))return!1;return!0}return!1}}]),e}(),eU=function(){function e(){Iy(this,e)}return Ly(e,null,[{key:"parse",value:function(t){if(!(t.length<3)){var n={},r=e._parseValue(new DataView(t.buffer,t.byteOffset,t.byteLength));try{var i=e._parseValue(new DataView(t.buffer,t.byteOffset+r.size,t.byteLength-r.size));n[r.data]=i.data}catch(AV){}return n}}},{key:"_parseValue",value:function(t){var n,r=t.byteLength,i=1,o=!1;switch(t.getUint8(0)){case 0:n=t.getFloat64(1),i+=8;break;case 1:n=!!t.getUint8(1),i+=1;break;case 2:var a=e._parseString(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i));n=a.data,i+=a.size;break;case 3:n={};var s=0;for(9==(16777215&t.getUint32(r-4))&&(s=3);i<r-4;){var l=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-s)),u=l.size,c=l.data;if(l.isEnd)break;n[c.name]=c.value,i+=u}if(i<=r-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 8:n={},i+=4;var h=0;for(9==(16777215&t.getUint32(r-4))&&(h=3);i<r-8;){var f=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-h)),d=f.size,p=f.data;if(f.isEnd)break;n[p.name]=p.value,i+=d}if(i<=r-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 9:n=void 0,i=1,o=!0;break;case 10:n=[];var v=t.getUint32(1);i+=4;for(var g=0;g<v;g++){var y=e._parseValue(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i)),m=y.data,_=y.size;n.push(m),i+=_}break;case 11:var b=t.getFloat64(i)+6e4*t.getInt16(i+8);n=new Date(b),i+=10;break;case 12:var w=t.getUint32(1);i+=4,n="",w>0&&(n=ZN.decode(new Uint8Array(t.buffer,t.byteOffset+i,w))),i+=w;break;default:i=r}return{data:n,size:i,isEnd:o}}},{key:"_parseString",value:function(e){var t=e.getUint16(0),n="";return t>0&&(n=ZN.decode(new Uint8Array(e.buffer,e.byteOffset+2,t))),{data:n,size:2+t}}},{key:"_parseObject",value:function(t){if(!(t.byteLength<3)){var n=e._parseString(t),r=e._parseValue(new DataView(t.buffer,t.byteOffset+n.size,t.byteLength-n.size));return{data:{name:n.data,value:r.data},size:n.size+r.size,isEnd:r.isEnd}}}}]),e}(),tU="undefined"!=typeof window,nU=tU&&navigator.userAgent.toLocaleLowerCase(),rU=tU&&ck(nU).call(nU,"firefox"),iU=tU&&ck(nU).call(nU,"android"),oU=gE,aU=function(){function e(){Iy(this,e)}return Ly(e,null,[{key:"parseADTS",value:function(t,n){for(var r=t.length,i=0;i+2<r&&(255!==t[i]||240!=(246&t[i+1]));)i++;if(!(i>=r)){var o=i,a=[],s=(60&t[i+2])>>>2,l=e.FREQ[s];if(!l)throw new Error("Invalid sampling index: ".concat(s));for(var u,c,h=1+((192&t[i+2])>>>6),f=(1&t[i+2])<<2|(192&t[i+3])>>>6,d=e._getConfig(s,f,h),p=d.config,v=d.codec,g=0,y=e.getFrameDuration(l);i+7<r;)if(255===t[i]&&240==(246&t[i+1])){if(r-i<(c=(3&t[i+3])<<11|t[i+4]<<3|(224&t[i+5])>>5))break;u=2*(1&~t[i+1]);var m=FN(n+g*y);a.push({pts:m,dts:m,ptsTime:FN(m/9e4),dtsTime:FN(m/9e4),data:t.subarray(i+7+u,i+c)}),g++,i+=c}else i++;return{skip:o,remaining:i>=r?void 0:t.subarray(i),frames:a,nbFrame:a.length,samplingFrequencyIndex:s,sampleRate:l,objectType:h,channelCount:f,codec:v,config:p,originCodec:"mp4a.40.".concat(h)}}}},{key:"parseAudioSpecificConfig",value:function(t){if(t.length){var n=t[0]>>>3,r=(7&t[0])<<1|t[1]>>>7,i=(120&t[1])>>>3,o=e.FREQ[r];if(!o)throw new Error("Invalid sampling index: ".concat(r));var a=e._getConfig(r,i,n);return{samplingFrequencyIndex:r,sampleRate:o,objectType:n,channelCount:i,config:a.config,codec:a.codec,originCodec:"mp4a.40.".concat(n)}}}},{key:"getFrameDuration",value:function(e){return 1024*(arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4)/e}},{key:"_getConfig",value:function(e,t,n){var r,i,o=[];return rU?e>=6?(r=5,i=e-3):(r=2,i=e):iU?(r=2,i=e):(r=n,i=e,e>=6?i=e-3:1===t&&(r=2,i=e)),o[0]=r<<3,o[0]|=(14&e)>>1,o[1]=(1&e)<<7,o[1]|=t<<3,5===r&&(o[1]|=(14&i)>>1,o[2]=(1&i)<<7,o[2]|=8,o[3]=0),{config:o,codec:"mp4a.40.".concat(r)}}},{key:"getSilentFrame",value:function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}}}]),e}(),sU=aU;VN=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],function(e,t,n){t in e?oU(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n}(sU,"symbol"!==Sc(BN="FREQ")?BN+"":BN,VN);var lU=gE,uU=function(e,t,n){return function(e,t,n){t in e?lU(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n}(e,"symbol"!==Sc(t)?t+"":t,n),n},cU=function(){function e(t){if(Iy(this,e),uU(this,"_bytesAvailable"),uU(this,"_bitsAvailable",0),uU(this,"_word",0),!t)throw new Error("ExpGolomb data params is required");this._data=t,this._bytesAvailable=t.byteLength,this._bytesAvailable&&this._loadWord()}return Ly(e,[{key:"_loadWord",value:function(){var e=this._data.byteLength-this._bytesAvailable,t=Math.min(4,this._bytesAvailable);if(0===t)throw new Error("No bytes available");var n=new Uint8Array(4);n.set(this._data.subarray(e,e+t)),this._word=new DataView(n.buffer).getUint32(0),this._bitsAvailable=8*t,this._bytesAvailable-=t}},{key:"skipBits",value:function(e){if(this._bitsAvailable>e)this._word<<=e,this._bitsAvailable-=e;else{e-=this._bitsAvailable;var t=Math.floor(e/8);e-=8*t,this._bytesAvailable-=t,this._loadWord(),this._word<<=e,this._bitsAvailable-=e}}},{key:"readBits",value:function(e){if(e>32)throw new Error("Cannot read more than 32 bits");var t=Math.min(this._bitsAvailable,e),n=this._word>>>32-t;return this._bitsAvailable-=t,this._bitsAvailable>0?this._word<<=t:this._bytesAvailable>0&&this._loadWord(),(t=e-t)>0&&this._bitsAvailable?n<<t|this.readBits(t):n}},{key:"skipLZ",value:function(){var e;for(e=0;e<this._bitsAvailable;++e)if(0!=(this._word&2147483648>>>e))return this._word<<=e,this._bitsAvailable-=e,e;return this._loadWord(),e+this.skipLZ()}},{key:"skipUEG",value:function(){this.skipBits(1+this.skipLZ())}},{key:"readUEG",value:function(){var e=this.skipLZ();return this.readBits(e+1)-1}},{key:"readEG",value:function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}},{key:"readBool",value:function(){return 1===this.readBits(1)}},{key:"readUByte",value:function(){return this.readBits(8)}},{key:"skipScalingList",value:function(e){for(var t=8,n=8,r=0;r<e;r++)0!==n&&(n=(t+this.readEG()+256)%256),t=0===n?t:n}},{key:"readSliceType",value:function(){return this.readUByte(),this.readUEG(),this.readUEG()}},{key:"firstMbInSlice",value:function(){return this.readUByte(),this.readUEG()}}]),e}();function hU(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}var fU=function(){function e(){Iy(this,e)}return Ly(e,null,[{key:"parseAnnexB",value:function(e){for(var t=e.length,n=2,r=0;null!==e[n]&&1!==e[n];)n++;if((r=++n+2)>=t)return[];for(var i=[];r<t;)switch(e[r]){case 0:if(0!==e[r-1]){r+=2;break}if(0!==e[r-2]){r++;break}n!==r-2&&i.push(e.subarray(n,r-2));do{r++}while(1!==e[r]&&r<t);r=(n=r+1)+2;break;case 1:if(0!==e[r-1]||0!==e[r-2]){r+=3;break}n!==r-2&&i.push(e.subarray(n,r-2)),r=(n=r+1)+2;break;default:r+=3}return n<t&&i.push(e.subarray(n)),i}},{key:"parseNalu",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(!(e.length<4)){for(var n,r=e.length,i=[],o=0;o+t<r;)if(n=hU(e,o),3===t&&(n>>>=8),o+=t,n){if(o+n>r)break;i.push(e.subarray(o,o+n)),o+=n}return i}}},{key:"parseNaluType",value:function(e,t){return null==e?void 0:Bw(e).call(e,(function(e){var n=t?e[0]>>>1&63:31&e[0],r=t&&(39===n||40===n),i={type:n,unit:e,isSei:r=r||!t&&6===n};switch(n){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!t&&5!==n||t&&5===n)break;i.frameType="I"}return t||1!==n||(i.frameType=function(e){var t=new cU(e).readSliceType();return 2===t||4===t||7===t||9===t?"I":0===t||3===t||5===t||8===t?"P":1===t||6===t?"B":void 0}(e)),!t||0!==n&&1!==n||(i.frameType="P"),!t&&n<=5&&(i.firstMbInSlice=new cU(e).firstMbInSlice()),t&&n<=21&&(i.firstMbInSlice=128==(128&e[2])),i}))}},{key:"parseSEI",value:function(e){for(var t=e.length,n=1,r=0,i=0,o="";255===e[n];)r+=255,n++;for(r+=e[n++];255===e[n];)i+=255,n++;if(i+=e[n++],5===r&&t>n+16)for(var a=0;a<16;a++)o+=e[n].toString(16),n++;return{payload:e.subarray(n),type:r,size:i,uuid:o}}},{key:"removeEPB",value:function(e){for(var t=e.byteLength,n=[],r=1;r<t-2;)0===e[r]&&0===e[r+1]&&3===e[r+2]?(n.push(r+2),r+=2):r++;if(!n.length)return e;var i=t-n.length,o=new Uint8Array(i),a=0;for(r=0;r<i;a++,r++)a===n[0]&&(a++,n.shift()),o[r]=e[a];return o}}]),e}(),dU=function(){function e(){Iy(this,e)}return Ly(e,null,[{key:"parseAVCDecoderConfigurationRecord",value:function(t){if(!(t.length<7)){for(var n,r,i=1+(3&t[4]),o=[],a=[],s=6,l=31&t[5],u=0;u<l;u++)if(r=t[s]<<8|t[s+1],s+=2,r){var c=t.subarray(s,s+r);s+=r,o.push(c),n||(n=e.parseSPS(fU.removeEPB(c)))}var h,f=t[s];s++;for(var d=0;d<f;d++)h=t[s]<<8|t[s+1],s+=2,h&&(a.push(t.subarray(s,s+h)),s+=h);return{sps:n,spsArr:o,ppsArr:a,nalUnitSize:i}}}},{key:"parseSPS",value:function(e){var t=new cU(e);t.readUByte();var n=t.readUByte(),r=t.readUByte(),i=t.readUByte();t.skipUEG();var o=420;if(100===n||110===n||122===n||244===n||44===n||83===n||86===n||118===n||128===n||138===n||144===n){var a=t.readUEG();if(a<=3&&(o=[0,420,422,444][a]),3===a&&t.skipBits(1),t.skipUEG(),t.skipUEG(),t.skipBits(1),t.readBool())for(var s=3!==a?8:12,l=0;l<s;l++)t.readBool()&&(l<6?t.skipScalingList(16):t.skipScalingList(64))}t.skipUEG();var u=t.readUEG();if(0===u)t.readUEG();else if(1===u){t.skipBits(1),t.skipUEG(),t.skipUEG();for(var c=t.readUEG(),h=0;h<c;h++)t.skipUEG()}t.skipUEG(),t.skipBits(1);var f=t.readUEG(),d=t.readUEG(),p=t.readBits(1);0===p&&t.skipBits(1),t.skipBits(1);var v,g,y,m,_,b=0,w=0,k=0,E=0;if(t.readBool()&&(b=t.readUEG(),w=t.readUEG(),k=t.readUEG(),E=t.readUEG()),t.readBool()){if(t.readBool())switch(t.readUByte()){case 1:v=[1,1];break;case 2:v=[12,11];break;case 3:v=[10,11];break;case 4:v=[16,11];break;case 5:v=[40,33];break;case 6:v=[24,11];break;case 7:v=[20,11];break;case 8:v=[32,11];break;case 9:v=[80,33];break;case 10:v=[18,11];break;case 11:v=[15,11];break;case 12:v=[64,33];break;case 13:v=[160,99];break;case 14:v=[4,3];break;case 15:v=[3,2];break;case 16:v=[2,1];break;case 255:v=[t.readUByte()<<8|t.readUByte(),t.readUByte()<<8|t.readUByte()]}if(t.readBool()&&t.readBool(),t.readBool()&&(t.readBits(4),t.readBool()&&t.readBits(24)),t.readBool()&&(t.readUEG(),t.readUEG()),t.readBool()){var T=t.readBits(32),S=t.readBits(32);g=t.readBool(),_=(y=S)/(m=2*T)}}for(var C=e.subarray(1,4),x="avc1.",P=0;P<3;P++){var A=C[P].toString(16);A.length<2&&(A="0".concat(A)),x+=A}return{codec:x,profileIdc:n,profileCompatibility:r,levelIdc:i,chromaFormat:o,width:Math.ceil(16*(f+1)-2*(b+w)),height:(2-p)*(d+1)*16-(p?2:4)*(k+E),sarRatio:v,fpsNum:y,fpsDen:m,fps:_,fixedFrame:g}}}]),e}(),pU=function(){function e(){Iy(this,e)}return Ly(e,null,[{key:"parseHEVCDecoderConfigurationRecord",value:function(t){if(!(t.length<23)){for(var n,r,i,o,a=1+(3&t[21]),s=[],l=[],u=[],c=23,h=t[22],f=0;f<h;f++){r=63&t[c],i=t[c+1]<<8|t[c+2],c+=3;for(var d=0;d<i;d++)if(o=t[c]<<8|t[c+1],c+=2,o){switch(r){case 32:u.push(t.subarray(c,c+o));break;case 33:var p=t.subarray(c,c+o);n||(n=e.parseSPS(fU.removeEPB(p))),s.push(p);break;case 34:l.push(t.subarray(c,c+o))}c+=o}}return{sps:n,spsArr:s,ppsArr:l,vpsArr:u,nalUnitSize:a}}}},{key:"parseSPS",value:function(e){var t=new cU(e);t.readUByte(),t.readUByte(),t.readBits(4);var n=t.readBits(3);t.readBits(1);var r=this._parseProfileTierLevel(t,n);t.readUEG();var i=t.readUEG(),o=420;i<=3&&(o=[0,420,422,444][i]);var a=0;3===i&&(a=t.readBits(1));var s,l,u,c,h=t.readUEG(),f=t.readUEG(),d=t.readBits(1);(1===d&&(s=t.readUEG(),l=t.readUEG(),u=t.readUEG(),c=t.readUEG()),1===d)&&(h-=(1!==i&&2!==i||0!==a?1:2)*(l+s),f-=(1===i&&0===a?2:1)*(c+u));return{codec:"hev1.1.6.L93.B0",width:h,height:f,chromaFormat:o,bitDepthLumaMinus8:t.readUEG(),bitDepthChromaMinus8:t.readUEG(),generalProfileSpace:r.generalProfileSpace,generalTierFlag:r.generalTierFlag,generalProfileIdc:r.generalProfileIdc,generalLevelIdc:r.generalLevelIdc}}},{key:"_parseProfileTierLevel",value:function(e,t){var n,r,i;(arguments.length>2&&void 0!==arguments[2]?arguments[2]:1)&&(n=e.readBits(2),r=e.readBits(1),i=e.readBits(5)),e.readBits(32),e.readBits(1),e.readBits(1),e.readBits(1),e.readBits(1),e.readBits(16),e.readBits(16),e.readBits(12);for(var o=e.readUByte(),a=[],s=[],l=0;l<t;l++)a[l]=e.readBits(1),s[l]=e.readBits(1);t>0&&e.readBits(2*(8-t));for(var u=0;u<t;u++)0!==a[u]&&(e.readBits(2),e.readBits(1),e.readBits(5),e.readBits(16),e.readBits(16),e.readBits(4),e.readBits(16),e.readBits(16),e.readBits(12)),0!==s[u]&&e.readBits(8);return{generalProfileSpace:n,generalTierFlag:r,generalProfileIdc:i,generalLevelIdc:o}}}]),e}();function vU(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=Hw(t=e.items).call(t,(function(e){return e.isSample&&e.isVideo}))[0],o=Hw(n=e.items).call(n,(function(e){return e.isSample&&e.isAudio}))[0],a=Math.min((null==o?void 0:o.dtsTime)||0,(null==i?void 0:i.dtsTime)||0);return a-=r,e.items.forEach((function(e){e.time=FN(e.dtsTime-a)})),e}function gU(e){var t,n=Hw(t=e.items).call(t,(function(e){return e.isVideo})),r=[],i=-1,o={},a={};return n.forEach((function(e,t){(e.keyframe||t===n.length-1)&&(i=-1===i?e.time:i,r.push({time:e.time,value:FN(e.time-i)}),i=e.time);var s=Math.ceil(e.time+1e-5);o[s]?o[s]++:o[s]=1,a[s]?a[s]+=8*e.size:a[s]=8*e.size})),{bitRates:yU(a),gops:Hw(r).call(r,(function(e){return e.value})),frameRates:yU(o)}}function yU(e){var t=fk(e);return Bw(t).call(t,(function(t){return{time:Number(t),value:e[t]}}))}function mU(e){var t,n,r,i,o=QM(t=dk(n=fC(r=Hw(i=dk(e).call(e)).call(i,(function(e){return e.isSample}))).call(r,(function(e,t){return e.time>t.time?1:-1}))).call(n,0,3)).call(t,(function(e,t){return e+t.size}),0);return o>1e3?FN(o/1e3)+"KB":o+"B"}function _U(e){return QM(e).call(e,(function(e,t){return e+t.size}),0)/QM(e).call(e,(function(e,t){return e+t.duration}),0)*8e3}function bU(e){if(null==e?void 0:e.profileIdc)switch(e.profileIdc){case 66:return"66(Baseline)";case 77:return"77(Main)";case 100:return"100(High)"}}var wU=gE,kU=function(e,t,n){return function(e,t,n){t in e?wU(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n}(e,"symbol"!==Sc(t)?t+"":t,n),n},EU=function(){function e(){Iy(this,e),kU(this,"_nalUnitSize",0),kU(this,"_lastAudio",null),kU(this,"_lastVideo",null),kU(this,"_videoStartDts",-1),kU(this,"_audioStartDts",-1)}return Ly(e,[{key:"parse",value:function(e){for(var t=e.byteLength,n=e.subarray(0,13),r=13,i=Dm(Dm({header:n},this._parseHeader(n)),{},{items:[]});r+11<t;){var o=e.subarray(r,r+11),a=o[1]<<16|o[2]<<8|o[3],s=r+11+a;if(s>t)break;var l=e.subarray(r+11,s),u=new $N(Dm({tagSize:11+a+4,header:o,body:l,tailsize:e.subarray(s,s+4)},this._parseTag(o,l)));u.isAudio&&(this._lastAudio&&(this._lastAudio.duration=u.dts-this._lastAudio.dts),this._lastAudio=u),u.isVideo&&(this._lastVideo&&(this._lastVideo.duration=u.dts-this._lastVideo.dts),this._lastVideo=u),i.items.push(u),r=s+4}var c=vU(i);return c.rates=gU(c),this._addMetadataToFirstKeyFrame(c),c}},{key:"getSnapshoot",value:function(t,n){var r,i,o,a,s=Hw(r=t.items).call(r,(function(e){return 8===e.type}))[0],l=Hw(i=t.items).call(i,(function(e){return 9===e.type}))[0],u=s&&s.bodyView||s,c=l&&l.bodyView||l,h=Hw(o=t.items).call(o,(function(e){return e.isVideo})).length,f=this._getNbFrame(t,"I"),d=this._getNbFrame(t,"P"),p=this._getNbFrame(t,"B"),v=0;if(null==n?void 0:n.gopCacheBuffer){var g,y=(new e).parse(n.gopCacheBuffer),m=Hw(g=y.items).call(g,(function(e){return e.isVideo})),_=null==(a=m[m.length-1])?void 0:a.time;_&&(v=_-n.take/1e3)}return{rates:t.rates,audio:u?BT({},u,this._getExt(t,"audio")):null,video:c?BT({},c,this._getExt(t,"video"),{topThreeFrameSize:mU(t.items),gopCache:FN(v),profile:bU(c.sps),iFrames:f,pFrames:d,bFrames:p,iPercent:FN(f/h*100),pPercent:FN(d/h*100),bPercent:FN(p/h*100)}):null}}},{key:"_addMetadataToFirstKeyFrame",value:function(e){var t,n,r,i=Hw(t=e.items).call(t,(function(e){return 9===e.type}))[0];if(i&&(null==(r=i.bodyView)?void 0:r.sps)){var o=Hw(n=e.items).call(n,(function(e){return e.isVideo&&e.keyframe}))[0];o&&(o.bodyView.sps=i.bodyView.sps,o.bodyView.vpsArr=i.bodyView.vpsArr,o.bodyView.spsArr=i.bodyView.spsArr,o.bodyView.ppsArr=i.bodyView.ppsArr)}}},{key:"_getExt",value:function(e,t){var n,r,i,o=Hw(n=e.items).call(n,(function(e){return e.streamType===t&&e.isSample})),a=Hw(r=e.items).call(r,(function(e){var n;return e.streamType===t&&(null==(n=e.bodyView)?void 0:n.packetType)===KN}))[0];if(!o.length)return 0;var s=o[0],l=o[o.length-1],u=0;return"audio"===t&&(u=a&&1024/(null==(i=a.bodyView)?void 0:i.sampleRate)*1e3),"video"===t&&(u=s.duration),{avgBits:_U(o),startDts:o[0].dts,duration:(l.timestamp-s.timestamp)/1e3,refDuration:u,frameLength:o.length}}},{key:"_getNbFrame",value:function(e,t){var n;return Hw(n=e.items).call(n,(function(e){return e.isVideo&&e.frameType===t})).length}},{key:"_parseHeader",value:function(e){return{hasAudio:!!(e[4]>>2&1),hasVideo:!!(1&e[4])}}},{key:"_parseTag",value:function(e,t){var n,r=31&e[0],i=(e[7]<<24>>>0)+(e[4]<<16)+(e[5]<<8)+e[6];switch(r){case 18:n=BT({packetType:zN},eU.parse(t));break;case 9:n=this._parseVideo(t,i);break;case 8:n=this._parseAudio(t,i)}return{type:r,timestamp:i,bodyView:n}}},{key:"_parseAudio",value:function(e){if(e.length){var t=(240&e[0])>>>4;if(10===t||7===t||8===t){if(10!==t){var n=(12&e[0])>>2,r=(2&e[0])>>1,i=1&e[0];return Dm({format:t,sampleRate:GN[n],sampleSize:r?2:1,channelCount:i+1},this._parseG711(e))}return this._parseAac(e,t)}console.warn("Unsupported sound format: ".concat(t))}}},{key:"_parseG711",value:function(e,t){return{codecType:7===t?YN:XN,sampleRate:8e3,sample:e.subarray(1),packetType:WN}}},{key:"_parseAac",value:function(e,t){if(0===e[1]){var n=e.subarray(2);return Dm(Dm({},sU.parseAudioSpecificConfig(n)),{},{format:t,timescale:1e3,codecType:qN,packetType:KN,sequenceData:[n],frameType:"A"})}if(1===e[1])return{sample:e.subarray(2),format:t,codecType:qN,packetType:WN,frameType:"A"}}},{key:"_parseVideo",value:function(e,t){if(!(e.length<6)){var n=(240&e[0])>>>4,r=15&e[0];if(7===r||12===r){var i=12===r,o=e[1],a=(e[2]<<16|e[3]<<8|e[4])<<8>>8;if(0===o){var s=e.subarray(5),l=i?pU.parseHEVCDecoderConfigurationRecord(s):dU.parseAVCDecoderConfigurationRecord(s);return this._nalUnitSize=l.nalUnitSize,Dm({codecId:r,codecType:i?QN:JN,packetType:KN,timescale:1e3,sequenceData:[s]},l)}if(1===o){var u=fU.parseNalu(e.subarray(5),this._nalUnitSize);if(!u||!u.length)return;var c={dts:t,cts:a,units:u=fU.parseNaluType(u,i)};return 1===n&&(c.keyframe=!0),u.forEach((function(e){var t=e.type,n=e.unit,r=e.frameType;switch(t){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!i&&5!==t||i&&5===t)break;c.keyframe=!0,c.frameType="I";break;case 6:case 39:case 40:if(!i&&6!==t||i&&6===t)break;c.seis?c.seis.push(fU.parseSEI(fU.removeEPB(n))):c.seis=[fU.parseSEI(fU.removeEPB(n))];break;case 32:if(!i)break;c.vpsArr?c.vpsArr.push(n):c.vpsArr=[n];break;case 7:case 33:if(!i&&7!==t||i&&7===t)break;c.spsArr?c.spsArr.push(n):c.spsArr=[n];var o=fU.removeEPB(n);c.sps=i?pU.parseSPS(o):dU.parseSPS(o);break;case 8:case 34:if(!i&&8!==t||i&&8===t)break;c.ppsArr?c.ppsArr.push(n):c.ppsArr=[n];break;case 1:case 0:c.frameType=r}})),Dm({codecId:r,codecType:i?QN:JN,packetType:WN},c)}}else console.warn("Unsupported codecId: ".concat(r))}}}]),e}(),TU="FLV",SU="MPEG-TS",CU="MP4";function xU(e,t){var n;return Bw(n=[e[t],e[t+1],e[t+2],e[t+3]]).call(n,(function(e){return String.fromCharCode(e)})).join("")}var PU=gE,AU=function(e,t,n){return function(e,t,n){t in e?PU(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n}(e,"symbol"!==Sc(t)?t+"":t,n),n},IU=Ly((function e(t){var n=t.startIndictor,r=t.packetType,i=t.pid,o=t.afc;Iy(this,e),AU(this,"startIndictor",0),AU(this,"pid",0),AU(this,"afc",0),AU(this,"eleInfo",null),AU(this,"packetType",""),this.startIndictor=n,this.packetType=r,this.pid=i,this.afc=o})),RU=function(){function e(t){var n=t.header,r=t.headerObject,i=t.body,o=t.eleInfo,a=t.bodyView,s=t.headerView;Iy(this,e),AU(this,"header",null),AU(this,"headerView",null),AU(this,"body",null),AU(this,"bodyView",null),AU(this,"eleInfo",null),AU(this,"duration",0),this.header=n,this.headerView=s||new IU(r),this.eleInfo=o,this.body=i,this.bodyView=a}return Ly(e,[{key:"type",get:function(){var e;return null==(e=this.headerView)?void 0:e.packetType}},{key:"streamType",get:function(){return this.eleInfo?this.eleInfo[this.headerView.pid]:""}},{key:"packetType",get:function(){var e;return null==(e=this.headerView)?void 0:e.packetType}},{key:"frameType",get:function(){var e;return null==(e=this.bodyView)?void 0:e.frameType}},{key:"pts",get:function(){var e;return null==(e=this.bodyView)?void 0:e.pts}},{key:"ptsTime",get:function(){return this.pts?FN(this.pts/9e4):0}},{key:"dtsTime",get:function(){return this.dts?FN(this.dts/9e4):0}},{key:"dts",get:function(){var e;return null==(e=this.bodyView)?void 0:e.dts}},{key:"keyframe",get:function(){return"I"===this.frameType}},{key:"nalTypes",get:function(){var e,t;return null==(t=null==(e=this.bodyView)?void 0:e.units)?void 0:Bw(t).call(t,(function(e){return e.type}))}},{key:"nalSizes",get:function(){var e,t;return null==(t=null==(e=this.bodyView)?void 0:e.units)?void 0:Bw(t).call(t,(function(e){return e.unit.length}))}},{key:"isSample",get:function(){return"PES"===this.packetType}},{key:"timescale",get:function(){return 9e4}},{key:"isHevc",get:function(){var e,t;return"bytevc1"===(null==(t=null==(e=this.eleInfo)?void 0:e.video)?void 0:t.codecType)}},{key:"sampleView",get:function(){var e;if("PES"===this.type)return{dts:this.dts,pts:this.pts,units:null==(e=this.bodyView)?void 0:e.units,data:this._getSampleData()}}},{key:"data",get:function(){return this._getSampleData()}},{key:"size",get:function(){var e,t;return null==(t=null==(e=this.sampleView)?void 0:e.data)?void 0:t.length}},{key:"isVideo",get:function(){return this.isSample&&"video"===this.streamType}},{key:"isAudio",get:function(){return this.isSample&&"audio"===this.streamType}},{key:"_getSampleData",value:function(){var e,t;if("audio"===this.streamType)return this.bodyView.data;var n=(null==(t=this.bodyView)?void 0:Hw(e=t.units).call(e,(function(e){return!e.isSei&&!e.isDivision})))||[],r=QM(n).call(n,(function(e,t){return e+(t.unit.byteLength+4)}),0),i=new Uint8Array(r),o=0;return n.forEach((function(e){i.set([0,0,0,1],o),o+=4,i.set(e.unit,o),o+=e.unit.byteLength})),i}}]),e}(),OU=function(){function e(){Iy(this,e)}return Ly(e,[{key:"parse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=function(e){for(var t=Math.min(1e3,e.byteLength-564),n=0;n<t;n++)if(71===e[n]&&71===e[n+188]&&71===e[n+376])return n;return-1}(e);if(-1===n)throw new Error("invalid ts file");var r=e.byteLength;r-=(r-n)%188;for(var i,o=[],a=[],s=[],l=0,u=n;u<r;){var c=e.subarray(u,u+188),h=c.subarray(0,4),f=this._parseTsHeader(c.subarray(0,4));u+=188;var d=void 0,p=void 0,v=4,g="";switch((null==f?void 0:f.afc)>1&&(v+=1+Math.min(c[v],183)),f.pid){case 0:v+=1+c[v],d=c.subarray(v),g="PAT",p={pmtId:l=this._parsePAT(d)};break;default:if(f.pid===l){v+=1+c[v],d=c.subarray(v),p=i=this._parsePMT(d),g="PMT";break}if(!i||!i[f.pid])continue;d=c.subarray(v),g="PES"}if(f.packetType=g,"PES"===g){var y="video"===i[f.pid]?a:s;f.startIndictor&&y.length&&(o.push(new RU(Dm(Dm({},this._concatPesChunk(y)),{},{eleInfo:i}))),y="video"===i[f.pid]?a=[]:s=[]),y.push({header:h,headerObject:f,body:d})}else o.push(new RU({header:h,headerObject:f,body:d,eleInfo:i,bodyView:p}))}a.length&&(o.push(new RU(Dm(Dm({},this._concatPesChunk(a)),{},{eleInfo:i}))),a=[]),s.length&&(o.push(new RU(Dm(Dm({},this._concatPesChunk(s)),{},{eleInfo:i}))),s=[]);var m=vU({items:this._parsePES(o)},t);return m.rates=gU(m),m}},{key:"getSnapshoot",value:function(e){var t,n,r,i=e.items,o=e.rates,a=Hw(i).call(i,(function(e){return"video"===e.streamType})),s=Hw(i).call(i,(function(e){return"audio"===e.streamType})),l=qm(i).call(i,(function(e){return"PMT"===e.type})),u=a[0],c=s[0],h=a.length,f=this._getNbFrame(a,"I"),d=this._getNbFrame(a,"P"),p=this._getNbFrame(a,"B"),v={rates:o,video:u?BT({},null==u?void 0:u.bodyView,{codecType:u.eleInfo.video.codecType,frameLength:h,topThreeFrameSize:mU(i),avgBits:_U(a),profile:bU(null==(t=null==u?void 0:u.bodyView)?void 0:t.sps),iFrames:f,pFrames:d,bFrames:p,iPercent:FN(f/h*100),pPercent:FN(d/h*100),bPercent:FN(p/h*100),timescale:9e4},this._getDuration(a,"video")):null,audio:c?BT({},null==c?void 0:c.bodyView,{codecType:c.eleInfo.audio.codecType,frameLength:s.length,timescale:9e4,avgBits:_U(s)},this._getDuration(s,"audio")):null};return l&&(null==(n=l.bodyView)?void 0:n.noSupported)&&(v.noSupported=null==(r=l.bodyView)?void 0:r.noSupported),v}},{key:"getRates",value:function(e){return gU(e)}},{key:"_getDuration",value:function(e,t){var n,r=e[0],i=e[e.length-1],o=0;return"audio"===t&&(o=1024/(null==(n=r.bodyView)?void 0:n.sampleRate)*1e3),"video"===t&&(o=r.duration),{duration:(i.pts-r.pts)/9e4,refDuration:o}}},{key:"_getNbKeyFrame",value:function(e){return Hw(e).call(e,(function(e){var t;return null==(t=e.bodyView)?void 0:t.keyframe})).length}},{key:"_getNbFrame",value:function(e,t){return Hw(e).call(e,(function(e){return e.frameType===t})).length}},{key:"_parseTsHeader",value:function(e){if(71===e[0]){var t={};return t.startIndictor=(64&e[1])>>6,t.pid=((31&e[1])<<8)+e[2],t.afc=(48&e[3])>>4,t}return null}},{key:"_parsePAT",value:function(e){return(31&e[10])<<8|e[11]}},{key:"_parsePMT",value:function(e){var t=0,n=3+((15&e[1])<<8|e[2])-4;e[3],e[4],t=t+11+((15&e[10])<<8|e[11])+1;for(var r,i={video:null,audio:null,noSupported:0};t<n;){r=e[t];var o=(31&e[t+1])<<8|e[t+2];switch(r){case 27:case 36:i.video={elePid:o,streamType:r,codecType:27===r?"avc":"bytevc1"},i[o]="video";break;case 15:i.audio={elePid:o,streamType:r,codecType:"aac"},i[o]="audio";break;default:i.noSupported=r,console.warn("unsupported streamType,",r)}t+=5+((15&e[t+3])<<8|e[t+4])}return i}},{key:"_concatPesChunk",value:function(e){var t=QM(e).call(e,(function(e,t){return e+t.body.byteLength}),0),n=new Uint8Array(t),r=0;return e.forEach((function(e){var t=e.body;n.set(t,r),r+=t.byteLength})),Dm(Dm({},e[0]),{},{body:n,packetType:"PES"})}},{key:"_parsePES",value:function(e){var t=this,n=[];return e.forEach((function(e,r){if(e.isSample){var i=e,o=t._parsePESInternal(i.body)||{},a=o.data,s=o.pts,l=o.dts;if(a){if(e.isVideo&&(i.bodyView=t._parseVideoFrame(a,s,l,"bytevc1"===i.eleInfo.video.codecType),t._lastVideo&&(t._lastVideo.duration=1e3*(e.dtsTime-t._lastVideo.dtsTime)),t._lastVideo=i),e.isAudio){var u=sU.parseADTS(a,s),c=u.frames;return delete u.frames,void(null==c||c.forEach((function(e){var r=new RU(BT({},i,{bodyView:Dm(Dm(Dm({},u),e),{},{frameType:"A"})}));n.push(r),t._lastAudio&&(t._lastAudio.duration=1e3*(r.dtsTime-t._lastAudio.dtsTime)),t._lastAudio=r})))}n.push(e)}}else n.push(e)})),n}},{key:"_parsePESInternal",value:function(e){var t=e[8];if(!(0===t||e.length<t+9)&&1===(e[0]<<16|e[1]<<8|e[2])){var n=(e[4]<<8)+e[5];if(!(n&&n>e.length-6)){var r,i,o=e[7];return 192&o&&(r=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2,64&o?r-(i=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2)>54e5&&(r=i):i=r),i=i||r,{data:e.subarray(9+t),pts:r,dts:i}}}}},{key:"_parseVideoFrame",value:function(e,t,n,r){var i=fU.parseAnnexB(e);if(i.length){var o={units:i=fU.parseNaluType(i,r),pts:t,dts:n};return i.forEach((function(e){var t=e.unit,n=e.type,i=e.frameType;switch(n){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!r&&5!==n||r&&5===n)break;o.keyframe=!0,o.frameType="I";break;case 6:case 39:case 40:if(!r&&6!==n||r&&6===n)break;o.seis?o.seis.push(fU.parseSEI(fU.removeEPB(t))):o.seis=[fU.parseSEI(fU.removeEPB(t))];break;case 32:if(!r)break;o.vpsArr?o.vpsArr.push(t):o.vpsArr=[t];break;case 7:case 33:if(!r&&7!==n||r&&7===n)break;o.spsArr?o.spsArr.push(t):o.spsArr=[t];var a=fU.removeEPB(t);o.sps=r?pU.parseSPS(a):dU.parseSPS(a);break;case 8:case 34:if(!r&&8!==n||r&&8===n)break;o.ppsArr?o.ppsArr.push(t):o.ppsArr=[t];break;case 9:case 35:break;case 0:case 1:o.frameType=i}})),o}}}]),e}(),DU=Gc("Array").entries,MU=ir,LU=tt,NU=ce,UU=DU,FU=Array.prototype,BU={DOMTokenList:!0,NodeList:!0},VU=o((function(e){var t=e.entries;return e===FU||NU(FU,e)&&t===FU.entries||LU(BU,MU(e))?UU:t})),HU=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Iy(this,e),this._offset=n,this._buffer=t,this._length=t.byteLength}return Ly(e,[{key:"offset",get:function(){return this._offset},set:function(e){this._offset=e}},{key:"forward",value:function(e){this._offset+=e}},{key:"subarray",value:function(e){return e?this._buffer.subarray(this._offset,this._offset+e):this._buffer.subarray(this._offset,e)}},{key:"slice",value:function(e){for(var t=this._offset,n=this._buffer,r=[],i=0;i<e;i++)r.push(n[t+i]);return new Uint8Array(r)}},{key:"readU8",value:function(){return this._buffer[this._offset]}},{key:"readU16",value:function(){var e=this._offset,t=this._buffer;return t[e]<<8|t[e+1]}},{key:"readU24",value:function(){var e=this._offset,t=this._buffer;return t[e]<<16|t[e+1]<<8|t[e+12]}},{key:"readU32",value:function(){var e=this._offset,t=this._buffer;return t[e]*(1<<24)+65536*t[e+1]+256*t[e+2]+t[e+3]}},{key:"read32",value:function(){var e=this._offset,t=this._buffer;return t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}}]),e}(),jU=gE,zU=function(e,t,n){return function(e,t,n){t in e?jU(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n}(e,"symbol"!==Sc(t)?t+"":t,n),n},KU=function(){function e(t,n,r,i,o,a,s,l,u,c){Iy(this,e),zU(this,"dts",0),zU(this,"pts",0),zU(this,"units",null),zU(this,"timescale",0),zU(this,"offset",0),zU(this,"duration",0),zU(this,"data",null),zU(this,"size",0),zU(this,"streamType",""),zU(this,"_frameType",""),zU(this,"isHevc",!1),this.dts=t,this.pts=n,this.units=r,this.timescale=i,this.offset=o,this.data=a,this.size=s,this.duration=l,this.streamType=u,this.isHevc=c}return Ly(e,[{key:"isSample",get:function(){return!0}},{key:"dtsTime",get:function(){return this.dts/this.timescale}},{key:"ptsTime",get:function(){return this.pts/this.timescale}},{key:"frameType",get:function(){var e,t;return"audio"===this.streamType?"A":this._frameType||(null==(t=this.units)?void 0:Hw(e=Bw(t).call(t,(function(e){return e.frameType}))).call(e,Boolean)[0])},set:function(e){this._frameType=e}},{key:"nalTypes",get:function(){var e;return null==(e=this.units)?void 0:Bw(e).call(e,(function(e){return e.type}))}},{key:"nalSizes",get:function(){var e;return null==(e=this.units)?void 0:Bw(e).call(e,(function(e){return e.unit.length}))}},{key:"keyframe",get:function(){return"I"===this.frameType}},{key:"isVideo",get:function(){return"video"===this.streamType}},{key:"isAudio",get:function(){return"audio"===this.streamType}}]),e}(),WU={ftyp:"file type and compatibility",free:"free box",moov:"container for all metadata",mvhd:"movie header,overall declarations",trak:"container for an individual track or stream",tkhd:"track header,overall information about the track",tref:"track reference container",mdia:"container for the media information in a track",mdhd:"media header,oervall information about the media",hdlr:"declares the media type",minf:"media information container",vmhd:"video media header",smhd:"sound media header",dinf:"data information box",stbl:"sample table box",stsd:"sample descriptions(codec types, etc)",stts:"decoding time to sample",ctts:"compostion time to sample",cslg:"compostion to decode timeline mapping",stsc:"sample to chunk",stsz:"sample size",stco:"chunk offset",stss:"sync sample table",sdtp:"independent abd disposable samples",sbgp:"sample to group",mvex:"movid extends box",moof:"movie fragment",mfhd:"movie fragment header",traf:"track fragment",tfhd:"track fragment header",trun:"track fragment run",tfdt:"track fragment decode time",mdat:"media data",meta:"metadata",avc1:"h264 codec info box",hev1:"hevc codec info box"};function GU(e,t){return{isLeading:(12&e[t])>>2,dependsOn:3&e[t],isDenpendedOn:(192&e[t+1])>>6,hasRedundancy:(48&e[t+1])>>4,padding:(14&e[t+1])>>1,nonSync:1&e[t+1],degradationPriority:e[t+2]<<8|e[t+3]}}function qU(e,t){var n;return Bw(n=[e[t],e[t+1],e[t+2],e[t+3]]).call(n,(function(e){return String.fromCharCode(e)})).join("")}function YU(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=[],r=0;r<e.byteLength;){var i,o=e[r]*(1<<24)+65536*e[r+1]+256*e[r+2]+e[r+3];if(1===o){var a=qU(e,r+4);if("mdat"===a){var s,l=new DataView(e.buffer),u=l.getUint32(r+8)*Math.pow(2,32)+l.getUint32(r+12);console.log(Fm(s="".concat(a,": largesize=")).call(s,u)),o=u}}if(o){var c=e.subarray(r,r+o),h=qU(c,4);n.push({size:o,length:c.byteLength,start:r+t,type:h,sizetypeRepresent:dk(i=c.subarray(0,8)).call(i),body:c.subarray(8),typeDes:WU[h]||""}),r+=o}else r+=4}return n}function XU(e,t,n){if(e)for(var r=0;r<e.length;r++){var i=e[r];if(i.type===t){if(!n)return i;if(n(i))return i}if(i.bodyView instanceof Array){var o=XU(i.bodyView,t,n);if(o){if(!n)return o;if(n(o))return o}}}}function JU(e,t){return XU(e,"trak",(function(e){var n=XU(e.bodyView,"hdlr");return n&&n.bodyView.handlerType===t}))}function QU(e,t,n){var r,i,o,a,s,l,u,c,h,f,d,p,v,g,y,m,_,b,w,k=JU(e,t);if(!k)return null;var E=XU(k.bodyView,"stbl"),T=XU(k.bodyView,"mdhd")||{},S=(null==(c=null==(u=Hw(r=E.bodyView).call(r,(function(e){return"stts"===e.type}))[0])?void 0:u.bodyView)?void 0:VU(c))||[];null==(f=null==(h=Hw(i=E.bodyView).call(i,(function(e){return"stss"===e.type}))[0])?void 0:h.bodyView)||VU(f);var C=(null==(p=null==(d=Hw(o=E.bodyView).call(o,(function(e){return"ctts"===e.type}))[0])?void 0:d.bodyView)?void 0:VU(p))||[],x=(null==(g=null==(v=Hw(a=E.bodyView).call(a,(function(e){return"stsc"===e.type}))[0])?void 0:v.bodyView)?void 0:VU(g))||[],P=(null==(m=null==(y=Hw(s=E.bodyView).call(s,(function(e){return"stsz"===e.type}))[0])?void 0:y.bodyView)?void 0:VU(m))||[],A=(null==(b=null==(_=Hw(l=E.bodyView).call(l,(function(e){return"stco"===e.type}))[0])?void 0:_.bodyView)?void 0:VU(b))||[],I=!!XU(E.bodyView,"hvcC"),R="vide"===t;if(!x.length||!A.length||!P.length)return[];for(var O,D=T.bodyView,M=D.timescale,L=void 0===M?1:M,N=D.duration,U=P.length,F=A.length,B=1,V=0,H=[],j=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=0,i=0,o=0,a=0,s=0,l=[];t=Bw(t).call(t,(function(e){return Dm({},e)})),n=Bw(n).call(n,(function(e){return Dm({},e)}));for(;o<e;){var u=t[r],c=n[i];u.sampleCount&&(a+=u.sampleDelta),u.sampleCount--,u.sampleCount||r++,c&&(s=c.sampleOffset,c.sampleCount--,c.sampleCount||i++),l.push({dts:a,pts:a+s}),o++}return l}(U,S,C);B<=F;){var z=x[V];(null==z?void 0:z.firstChunk)!==B?(H.push(BT({},O,{firstChunk:B,offset:A[B-1]})),B++):(H.push(Dm(Dm({},z),{},{offset:A[B-1]})),O=z,B++,V++)}var K=Bw(P).call(P,(function(e,t){var r=H[0],i=r.offset;r.offset+=e,r.samplesPreChunk--,r.samplesPreChunk||H.shift();var o=j[t],a=n.subarray(i,i+e);if(R){var s=fU.parseNalu(a);return new KU(o.dts,o.pts,fU.parseNaluType(s,I),L,i,a,e,0,"video",I)}return new KU(o.dts,o.pts,null,L,i,a,e,0,"audio")}));QM(K).call(K,(function(e,t){return e?(e.duration=(t.dts-e.dts)/L*1e3,t):t}),null);var W=null==(w=XU(null==k?void 0:k.bodyView,"vide"===t?I?"hvcC":"avcC":"mp4a"))?void 0:w.bodyView;return Dm(Dm(Dm({samples:K,iFrames:Hw(K).call(K,(function(e){return"I"===e.frameType})).length,pFrames:Hw(K).call(K,(function(e){return"P"===e.frameType})).length,bFrames:Hw(K).call(K,(function(e){return"B"===e.frameType})).length},T.bodyView),{},{frameLength:U,duration:N/L,timescale:L},W),null==W?void 0:W.ests)}function $U(e,t){var n=function(e){var t,n,r,i,o=JU(e,"vide"),a=JU(e,"soun"),s=XU(null==o?void 0:o.bodyView,"tkhd"),l=XU(null==a?void 0:a.bodyView,"tkhd"),u=XU(null==o?void 0:o.bodyView,"mdhd"),c=XU(null==a?void 0:a.bodyView,"mdhd"),h=XU(null==o?void 0:o.bodyView,"avcC")||XU(null==o?void 0:o.bodyView,"hvcC"),f=XU(null==a?void 0:a.bodyView,"mp4a"),d=a?Dm(Dm(Dm({type:"audio",trackId:null==(n=l.bodyView)?void 0:n.trackId},c.bodyView),null==f?void 0:f.bodyView),null==(r=null==f?void 0:f.bodyView)?void 0:r.ests):null,p=o?Dm(Dm({type:"video",trackId:null==(i=s.bodyView)?void 0:i.trackId},null==u?void 0:u.bodyView),null==h?void 0:h.bodyView):null;return Ny(t={},(null==d?void 0:d.trackId)||0,d),Ny(t,(null==p?void 0:p.trackId)||0,p),t}(e),r=hI(e).call(e,(function(e){return"moof"===e.type}));if(-1===r)return!1;n.lastVdts=-1,n.lastAdts=-1;for(var i=r;i<e.length&&"moof"===e[i].type;i+=2){var o=e[i];e[i+1].bodyView=ZU(o,t,n)}return!0}function ZU(e,t,n){var r,i,o=Hw(r=e.bodyView).call(r,(function(e){return"traf"===e.type})),a=Hw(i=Bw(o).call(o,(function(e){var t=XU(e.bodyView,"tfhd"),r=XU(e.bodyView,"tfdt"),i=XU(e.bodyView,"trun");if(t&&i){var o=t.bodyView.trackId;return Dm({trackId:o,baseDataOffset:t.bodyView.baseDataOffset,defaultSampleDuration:t.bodyView.defaultSampleDuration,defaultSampleSize:t.bodyView.defaultSampleSize,baseMediaDecodeTime:r.bodyView.baseMediaDecodeTime,dataOffset:i.bodyView.dataOffset,samples:i.bodyView.samples,sampleCount:i.bodyView.sampleCount},n[o])}}))).call(i,Boolean);return Bw(a).call(a,(function(e){var n,r,i,o=e.baseDataOffset,a=e.defaultSampleDuration,s=void 0===a?0:a,l=e.defaultSampleSize,u=void 0===l?0:l,c=e.dataOffset,h=e.baseMediaDecodeTime,f=void 0===h?0:h,d=e.samples,p=e.sps,v=e.timescale,g=e.type,y=o+c,m=0;return e.samples=Bw(d).call(d,(function(e){var n=t.subarray(y,y+e.size);m+=e.duration||s;var r=f+m;if("video"===g){var i="hev1.1.6.L93.B0"===(null==p?void 0:p.codec),o=fU.parseNalu(n);return y+=e.size,new KU(r,r+(e.ctOffset||0),fU.parseNaluType(o,i),v,y,n,e.size||u,(e.duration||s)/v*1e3,"video",i)}return y+=e.size,new KU(r,r+(e.ctOffset||0),null,v,y,n,e.size||u,(e.duration||s)/v*1e3,"audio")})),e.frameLength=e.samples.length,e.duration=m/v,e.iFrames=Hw(n=e.samples).call(n,(function(e){return"I"===e.frameType})).length,e.pFrames=Hw(r=e.samples).call(r,(function(e){return"P"===e.frameType})).length,e.bFrames=Hw(i=e.samples).call(i,(function(e){return"B"===e.frameType})).length,e}))}var eF=Math.pow(2,32);var tF=an,nF=L,rF=tt,iF=ce,oF=function(){var e=tF(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t},aF=RegExp.prototype,sF=ce,lF=function(e){var t=e.flags;return void 0!==t||"flags"in aF||rF(e,"flags")||!iF(aF,e)?t:nF(oF,e)},uF=RegExp.prototype,cF=o((function(e){return e===uF||sF(uF,e)?lF(e):e.flags})),hF=function(){var e=[];e[3]="ES_Descriptor",e[4]="DecoderConfigDescriptor",e[5]="DecoderSpecificInfo",e[6]="SLConfigDescriptor",this.getDescriptorName=function(t){return e[t]};var t=this,n={};return this.parseOneDescriptor=function(t){var r,i,o=0,a=t.readU8();for(t.forward(1),i=t.readU8(),t.forward(1);128&i;)o=(127&i)<<7,i=t.readU8(),t.forward(1);return o+=127&i,(r=e[a]?new n[e[a]](o):new n.Descriptor(o)).parse(t),r},n.Descriptor=function(e,t){this.tag=e,this.size=t,this.descs=[]},n.Descriptor.prototype.parse=function(e){this.data=dk(e).call(e,this.size),e.forward(this.size)},n.Descriptor.prototype.findDescriptor=function(e){for(var t=0;t<this.descs.length;t++)if(this.descs[t].tag===e)return this.descs[t];return null},n.Descriptor.prototype.parseRemainingDescriptors=function(e){for(var n=e.offset;e.offset<n+this.size;){var r=t.parseOneDescriptor(e);this.descs.push(r)}},n.ES_Descriptor=function(e){n.Descriptor.call(this,3,e)},n.ES_Descriptor.prototype=new n.Descriptor,n.ES_Descriptor.prototype.parse=function(e){if(this.ES_ID=e.readU16(),e.forward(2),this.flags=e.readU8(),e.forward(1),this.size-=3,128&cF(this)?(this.dependsOn_ES_ID=e.readU16(),e.forward(2),this.size-=2):this.dependsOn_ES_ID=0,64&cF(this)){var t,n=e.readU8();e.forward(1),this.URL=Bw(t=aT(dk(e).call(e,n))).call(t,(function(e){return String.fromCharCode(e)})),e.forward(n),this.size-=n+1}else this.URL="";32&cF(this)?(this.OCR_ES_ID=e.readU16(),e.forward(2),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(e)},n.ES_Descriptor.prototype.getOTI=function(e){var t=this.findDescriptor(4);return t?t.oti:0},n.ES_Descriptor.prototype.getAudioConfig=function(e){var t=this.findDescriptor(4);if(!t)return null;var n=t.findDescriptor(5);if(n&&n.data){var r=(248&n.data[0])>>3;return 31===r&&n.data.length>=2&&(r=32+((7&n.data[0])<<3)+((224&n.data[1])>>5)),r}return null},n.DecoderConfigDescriptor=function(e){n.Descriptor.call(this,4,e)},n.DecoderConfigDescriptor.prototype=new n.Descriptor,n.DecoderConfigDescriptor.prototype.parse=function(e){this.oti=e.readU8(),e.forward(1),this.streamType=e.readU8(),e.forward(1),this.bufferSize=e.readU24(),e.forward(3),this.maxBitrate=e.readU32(),e.forward(4),this.avgBitrate=e.readU32(),e.forward(4),this.size-=13,this.parseRemainingDescriptors(e)},n.DecoderSpecificInfo=function(e){n.Descriptor.call(this,5,e)},n.DecoderSpecificInfo.prototype=new n.Descriptor,n.SLConfigDescriptor=function(e){n.Descriptor.call(this,6,e)},n.SLConfigDescriptor.prototype=new n.Descriptor,this};function fF(e){var t,n,r,i=qm(t=e.descs).call(t,(function(e){return 4===e.tag}));i&&(n=qm(r=i.descs).call(r,(function(e){return 5===e.tag})));var o=BT({},i);return delete o.descs,o.config=(null==n?void 0:n.data.length)?aT(null==n?void 0:n.data):[],o}function dF(e){return Bw(e).call(e,(function(e){return e.toString(16)})).join("")}function pF(e){var t=e.body,n={},r=t[0],i=new HU(t);if(i.forward(4),n.systemId=dF(dk(i).call(i,16)),i.forward(16),r>0){var o=i.readU32();n.kids=[];var a=0;for(i.forward(4);a<o;)n.kids.push(dF(dk(i).call(i,16))),i.forward(16),a++}var s,l=i.readU32();return i.forward(4),n.dataSize=l,n.data=dk(i).call(i,l),n.dataToBase64=btoa((s=n.data,Bw(s).call(s,(function(e){return String.fromCharCode(e)})).join(""))),n}var vF=Math.pow(2,32);var gF=Math.pow(2,32);function yF(e){var t=e.body,n={samples:[]},r=new HU(t);n.version=r.readU8(),r.forward(1),n.flags=dk(r).call(r,3);var i,o,a,s,l=(i=cF(n),o=AM(i,3),a=o[1],{dataOffsetPresent:!!(1&(s=o[2])),firstSampleFlagsPresent:!!(4&s),sampleDurationPresent:!!(1&a),sampleSizePresent:!!((2&a)>>1),sampleFlagsPresent:!!((4&a)>>2),sampleCompositionTimeOffsetPresent:!!((8&a)>>3)}),u=l.dataOffsetPresent,c=l.firstSampleFlagsPresent,h=l.sampleDurationPresent,f=l.sampleSizePresent,d=l.sampleFlagsPresent,p=l.sampleCompositionTimeOffsetPresent;n.trFlags=l,r.forward(3),n.sampleCount=r.readU32(),r.forward(4),u&&(n.dataOffset=r.readU32(),r.forward(4)),c&&(n.firstSampleFlags=GU(t,r.offset),r.forward(4));for(var v=0;v<n.sampleCount;){var g={};h&&(g.duration=r.readU32(),r.forward(4)),f&&(g.size=r.readU32(),r.forward(4)),d&&(g.flags=GU(t,r.offset),r.forward(4)),p&&(g.ctOffset=r.readU32(),r.forward(4)),n.samples.push(g),v++}return n}function mF(e){var t=YU(e instanceof ArrayBuffer?new Uint8Array(e):e,0);if(_F(t),!$U(t,e)){var n,r=XU(t,"mdat");if(r)r.bodyView=Hw(n=[Dm({type:"video"},QU(t,"vide",e)),Dm({type:"audio"},QU(t,"soun",e))]).call(n,(function(e){return!!e.samples}))}return t}function _F(e){var t=0;e.forEach((function(e){switch(e.type){case"ftyp":case"styp":e.bodyView=function(e){var t=e.body,n=e.length,r=new HU(t),i={compatible:[]};i.major=xU(t,0),r.forward(4),i.version=r.readU32(),r.forward(4);for(var o=r.offset;o<n-8;)i.compatible.push(xU(t,o)),o+=4;return i}(e);break;case"moov":case"trak":case"mvex":case"mdia":case"minf":case"dinf":case"moof":case"traf":case"stbl":e.bodyView=YU(e.body,e.start+8),_F(e.bodyView);break;case"stsd":var n=function(e){var t=e.body,n=e.start,r=new HU(t);r.forward(4);var i=r.readU32();return r.forward(4),{entryCount:i,childData:YU(r.subarray(),n+8+8)}}(e),r=n.entryCount,i=n.childData;e.entryCount=r,e.bodyView=i,_F(e.bodyView);break;case"mvhd":e.bodyView=(h=e.body,f=new HU(h),d={},f.forward(4),d.createTime=f.readU32(),f.forward(4),d.modifyTime=f.readU32(),f.forward(4),d.timescale=f.readU32(),f.forward(4),d.duration=f.readU32(),f.forward(4),d.rate=f.readU32(),f.forward(4),f.forward(2),f.forward(70),d.nextTrackId=f.readU32(),d);break;case"tkhd":e.bodyView=function(e){var t=e.body,n=new HU(t,4),r={},i=n.readU8();r.version=i,1===i?n.forward(16):n.forward(8),r.trackId=n.readU32(),n.forward(4),1===i?(r.duration=n.readU32()*gF,n.forward(4),r.duration+=n.readU32()):r.duration=n.readU32();var o=t.byteLength;return o-=8,n.offset=o,r.width=n.readU32()>>16,n.forward(4),r.height=n.readU32()>>16,r}(e);break;case"mehd":e.bodyView=function(e){var t,n=e.body,r=new HU(n);if(r.forward(4),1===n[0]){var i=r.readU32();r.forward(4),t=i*(1<<30)*2+r.readU32()}else t=r.readU32();return{fragDuration:t}}(e);break;case"mdhd":e.bodyView=function(e){var t=e.body,n={},r=new HU(t),i=t[0];return r.forward(4),1===i?(r.forward(16),n.timescale=r.readU32(),r.forward(4),n.duration=r.readU32()*eF,r.forward(4),n.duration+=r.readU32()):(r.forward(8),n.timescale=r.readU32(),r.forward(4),n.duration=r.readU32()),n}(e);break;case"hdlr":e.bodyView=function(e){return{handlerType:xU(e.body,8)}}(e);break;case"trex":e.bodyView=function(e){var t=e.body,n={},r=new HU(t);return r.forward(4),n.trackId=r.readU32(),r.forward(4),n.defaultSdescriptionIndex=r.readU32(),r.forward(4),n.defaultSampleDuration=r.readU32(),r.forward(4),n.defaultSampleSize=r.readU32(),r.forward(4),n.defaultSampleFlags=GU(t,r.offset),n}(e);break;case"trun":e.bodyView=yF(e),t=e.bodyView.samples.length;break;case"tfdt":e.bodyView=function(e){var t=e.body,n=new HU(t),r=0;return n.forward(4),1===t[0]?(r=n.readU32()*vF,n.forward(4),r+=n.readU32()):r=n.readU32(),{baseMediaDecodeTime:r}}(e);break;case"avc1":case"hvc1":case"hev1":e.bodyView=function(e){var t=e.body,n=e.start,r=new HU(t);return r.forward(78),YU(r.subarray(),n+78+8)}(e),_F(e.bodyView);break;case"mp4a":e.bodyView=function(e){var t=e.body,n=dk(t).call(t,28),r=(new hF).parseOneDescriptor(new HU(dk(n).call(n,12),0)),i=(null==r?void 0:r.getAudioConfig)&&r.getAudioConfig();return{channelCount:t[17],samplesize:t[19],sampleRate:t[24]<<8|t[25],objectType:i,codec:"mp4a.40."+i,ests:fF(r)}}(e),e.bodyView&&(e.bodyView.codecType="aac");break;case"avcC":e.bodyView=function(e){var t=e.body,n={spsArr:[],ppsArr:[]},r=new HU(t);n.configVersion=t[0],n.avcProfile=t[1],n.profileComptibility=t[2],n.avcLevel=t[3],r.forward(4),r.forward(1);var i=31&dk(r).call(r,1);r.forward(1);for(var o=0;o<i;o++){var a=r.readU16();r.forward(2),n.spsArr.push(r.subarray(a)),r.forward(a)}var s=dk(r).call(r,1);r.forward(1);for(var l=0;l<s;l++){var u=r.readU16();r.forward(2),n.ppsArr.push(r.subarray(u)),r.forward(u)}return n}(e);var o=e.bodyView;if(o.codecType="avc",!(null==o?void 0:o.sps)){var a=o.spsArr[0],s=fU.removeEPB(a);o.sps=dU.parseSPS(s)}break;case"hvcC":e.bodyView=function(e){var t=e.body;if(!(t.length<23)){t[21];for(var n,r,i,o=[],a=[],s=[],l=23,u=t[22],c=0;c<u;c++){n=63&t[l],r=t[l+1]<<8|t[l+2],l+=3;for(var h=0;h<r;h++)if(i=t[l]<<8|t[l+1],l+=2,i){switch(n){case 32:s.push(t.subarray(l,l+i));break;case 33:o.push(t.subarray(l,l+i));break;case 34:a.push(t.subarray(l,l+i))}l+=i}}return{spsArr:o,ppsArr:a,vpsArr:s}}}(e);var l=e.bodyView;if(l.codecType="bytevc1",!(null==l?void 0:l.sps)){var u=l.spsArr[0],c=fU.removeEPB(u);l.sps=pU.parseSPS(c)}break;case"btrt":e.bodyView=function(e){var t=e.body,n=new HU(t),r=n.readU32();n.forward(4);var i=n.readU32();return n.forward(4),{bufferSizeDB:r,maxBitrate:i,avgBitrate:n.readU32()}}(e);break;case"stts":e.bodyView=function(e){var t=e.body,n=new HU(t);n.forward(4);var r=n.readU32(),i=[];n.forward(4);for(var o=0;o<r;o++){var a=n.readU32();n.forward(4);var s=n.readU32();n.forward(4),i.push({sampleCount:a,sampleDelta:s})}return{entries:i,count:i.length}}(e),VU(e.bodyView).length&&(t=VU(e.bodyView)[0].sampleCount);break;case"ctts":e.bodyView=function(e){var t=e.body,n=new HU(t);n.forward(4);var r=n.readU32(),i=[];n.forward(4);for(var o=0;o<r;o++){var a=n.readU32();n.forward(4);var s=n.readU32();n.forward(4),i.push({sampleCount:a,sampleOffset:s})}return{entries:i,count:i.length}}(e);break;case"cslg":e.bodyView=function(e){var t,n,r,i,o=e.body,a=new HU(o);return a.forward(4),t=a.read32(),a.forward(4),n=a.read32(),a.forward(4),r=a.read32(),a.forward(4),i=a.read32(),a.forward(4),{compositionToDTSShift:t,leastDecodeToDisplayDelta:n,greatestDecodeToDisplayDelta:r,compositionStartTime:i,compositionEndTime:a.read32()}}(e);break;case"stss":e.bodyView=function(e){var t=e.body,n=new HU(t);n.forward(4);var r=[],i=n.readU32();n.forward(4);for(var o=0;o<i;o++){var a=n.readU32();r.push(a),n.forward(4)}return{entries:r,count:r.length}}(e);break;case"stsz":e.bodyView=function(e){var t=e.body,n=new HU(t);n.forward(4);var r=[],i=n.readU32();n.forward(4);var o=n.readU32();if(n.forward(4),0===i)for(var a=0;a<o;a++){var s=n.readU32();r.push(s),n.forward(4)}else console.warn("parse stsz, sample size not equal 0");return{entries:r,count:r.length}}(e);break;case"stsc":e.bodyView=function(e){var t=e.body,n=new HU(t);n.forward(4);var r=[],i=n.readU32();n.forward(4);for(var o=0;o<i;o++){var a=n.readU32();n.forward(4);var s=n.readU32();n.forward(4);var l=n.readU32();n.forward(4),r.push({firstChunk:a,samplesPreChunk:s,sampleDesIndex:l})}return{entries:r,count:r.length}}(e);break;case"stco":e.bodyView=function(e){var t=e.body,n=new HU(t);n.forward(4);var r=n.readU32(),i=[];n.forward(4);for(var o=0;o<r;o++){var a=n.readU32();i.push(a),n.forward(4)}return{entries:i,count:i.length}}(e);break;case"sdtp":e.bodyView=function(e,t){var n=e.body,r=new HU(n);r.forward(4);for(var i,o,a,s,l=[],u=0;u<t;u++){var c=r.readU8();i=(192&c)>>6,o=(48&c)>>4,a=(12&c)>>2,s=3&c,l.push({isLeading:i,dependsOn:o,isDenpendedOn:a,hasRedundancy:s}),r.forward(1)}return{entries:l}}(e,t);break;case"mfhd":e.bodyView=function(e){var t=e.body,n=new HU(t);return n.forward(4),{seqNumber:n.readU32()}}(e);break;case"tfhd":e.bodyView=function(e){var t=e.body,n=new HU(t);n.forward(1);var r=dk(n).call(n,3),i=r[0]<<16|r[1]<<8|r[2],o={flags:r},a={baseDataOffsetPresent:!!(1&i),sampleDescriptionIndexPresent:!!(2&i),defaultSampleDurationPresent:!!(8&i),defaultSampleSizePresent:!!(16&i),defaultSampleFlagsPresent:!!(32&i),durationIsEmpty:!!(65536&i),defaultBaseIsMoof:!!(131072&i)};if(o.tfFlags=a,n.forward(3),o.trackId=n.readU32(),a.baseDataOffsetPresent){n.forward(4);var s=n.readU32();n.forward(4);var l=n.readU32();o.baseDataOffset=s<<32|l}else o.baseDataOffset=0,console.warn("base data offset not detected in tfhd");return a.sampleDescriptionIndexPresent&&(n.forward(4),o.sampleDescriptionIndex=n.readU32()),a.defaultSampleDurationPresent&&(n.forward(4),o.defaultSampleDuration=n.readU32()),a.defaultSampleSizePresent&&(n.forward(4),o.defaultSampleSize=n.readU32()),a.defaultSampleFlagsPresent&&(n.forward(4),o.defaultSampleFlags=n.readU32()),o}(e);break;case"pssh":e.bodyView=pF(e)}var h,f,d}))}var bF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"parse",value:function(e){return mF(e)}},{key:"parseV2",value:function(e,t){var n,r,i,o,a,s,l=Hw(e).call(e,(function(e){return"mdat"===e.type})),u=[],c=null==(r=null==(n=l[0])?void 0:n.bodyView)?void 0:Hw(r).call(r,(function(e){return"video"===e.type}))[0],h=null==(o=null==(i=l[0])?void 0:i.bodyView)?void 0:Hw(o).call(o,(function(e){return"audio"===e.type}))[0],f=Math.min((null==(a=null==h?void 0:h.samples[0])?void 0:a.dtsTime)||0,(null==(s=null==c?void 0:c.samples[0])?void 0:s.dtsTime)||0);l.forEach((function(e){e.bodyView.forEach((function(e){e.samples.forEach((function(e){e.time=FN(e.dtsTime-f),u.push(e)}))}))}));var d={items:fC(u).call(u,(function(e,t){return e.time>t.time?1:-1}))};return this._addMetadataToFirstKeyFrame(c),d.rates=gU(d),d}},{key:"getSnapshoot",value:function(e){return function(e){var t,n,r,i,o,a,s=Hw(e).call(e,(function(e){return"mdat"===e.type}));if(!s.length){var l=JU(e,"vide"),u=JU(e,"soun"),c=null==(n=XU(null==l?void 0:l.bodyView,"mdhd"))?void 0:n.bodyView,h=(null==(r=XU(null==l?void 0:l.bodyView,"avcC"))?void 0:r.bodyView)||(null==(i=XU(null==l?void 0:l.bodyView,"hvcC"))?void 0:i.bodyView),f=null==(o=XU(null==u?void 0:u.bodyView,"mdhd"))?void 0:o.bodyView,d=null==(a=XU(null==u?void 0:u.bodyView,"mp4a"))?void 0:a.bodyView;try{return{video:Dm(Dm({},c),{},{duration:(null==c?void 0:c.duration)/c.timescale},h),audio:Dm(Dm(Dm({},f),{},{duration:(null==f?void 0:f.duration)/f.timescale},d),d.ests)}}catch(AV){}return{}}var p=QM(t=Bw(s).call(s,(function(e){return e.bodyView}))).call(t,(function(e,t){return Fm(e).call(e,t)}),[]),v=QM(p).call(p,(function(e,t){var n,r=t.type,i=t.frameLength,o=t.iFrames,a=t.pFrames,s=t.bFrames,l=t.duration;return e[r]?(e[r].frameLength+=i,e[r].iFrames+=o,e[r].pFrames+=a,e[r].bFrames+=s,e[r].duration+=l,e):(e[r]=BT({},t),e[r].refDuration||(e[r].refDuration=null==(n=t.samples[0])?void 0:n.duration),e)}),{}),g=v.video;if(g){var y=g.frameLength,m=g.iFrames,_=g.pFrames,b=g.bFrames;g.iPercent=FN(m/y*100),g.pPercent=FN(_/y*100),g.bPercent=FN(b/y*100)}return v}(e)}},{key:"_addMetadataToFirstKeyFrame",value:function(e){var t;if(e){var n=Hw(t=e.samples).call(t,(function(e){return e.keyframe}))[0];n&&(n.sps=e.sps,n.vpsArr=e.vpsArr,n.spsArr=e.spsArr,n.ppsArr=e.ppsArr)}}}]),e}(),wF=.2,kF=40,EF=.3,TF=.5,SF=.1,CF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e){var t=null==e?void 0:qm(e).call(e,(function(e){return e.isSample&&"video"===e.streamType})),n=null==e?void 0:qm(e).call(e,(function(e){return e.isSample&&"audio"===e.streamType}));if(t&&n){var r,i,o,a=t.pts-n.pts,s=FN(a/t.timescale);if(a/t.timescale>=wF)return[{description:Fm(r=Fm(i=Fm(o="【音视频首帧gap差距过大】视频首帧pts: ".concat(t.pts,", 音频首帧pts: ")).call(o,n.pts,", delta: ")).call(i,a,"(")).call(r,s,"s)"),tips:"".concat(s>10?"可能造成无法起播问题":"")}]}}}]),e}(),xF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e){var t=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"video"===e.streamType})),n=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"audio"===e.streamType})),r=!!e[0]&&e[0]instanceof $N;return[this._checkBreak(t,"video"),this._checkBreak(n,"audio"),r&&this._checkConsecutiveFrameBreak(null==e?void 0:Hw(e).call(e,(function(e){return e.isSample})))]}},{key:"_checkBreak",value:function(e,t){var n=e.length;if(n)for(var r=0;r<n-1;r++){var i,o,a,s;if(Math.abs(e[r].dtsTime-e[r+1].dtsTime)>TF)return{description:Fm(i=Fm(o=Fm(a=Fm(s="【".concat(t,"时间戳发生跳变】，当前帧dts: ")).call(s,e[r].dts,", time:")).call(a,e[r].time,", 后一帧dts: ")).call(o,e[r+1].dts,", time:")).call(i,e[r+1].time)}}}},{key:"_checkConsecutiveFrameBreak",value:function(e){var t=e.length;if(t)for(var n=0;n<t-1;n++)if(Math.abs(e[n].dtsTime-e[n+1].dtsTime)>SF){var r,i,o,a,s,l=e[n],u=e[n+1];return{description:Fm(r=Fm(i=Fm(o=Fm(a=Fm(s="【前后帧dts差距过大】，当前帧(".concat(l.streamType,")dts: ")).call(s,l.dts,", time:")).call(a,l.time,", 后一帧(")).call(o,u.streamType,")dts: ")).call(i,u.dts,", time:")).call(r,u.time),tips:"可能造成播放画面卡顿或者声音卡顿"}}}}]),e}(),PF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e){return[this._checkKeyFrame(e),this._checkAudioFrameDataFull(e)]}},{key:"_checkKeyFrame",value:function(e){var t=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"video"===e.streamType}))[0];if(t&&"I"!==t.frameType)return{description:"【视频首帧非关键帧】",tips:"可能播放花屏或者报解码错误"}}},{key:"_checkAudioFrameDataFull",value:function(e){var t=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"audio"===e.streamType})),n=t.length;if(n)for(var r=0;r<n;r++){var i,o=t[r].data;if((null==o?void 0:o.length)<kF)return{description:Fm(i="【音频帧数据较少】 pts=".concat(t[r].pts,", time=")).call(i,t[r].time),tips:"可能由于音频数据原因导致解码报错"}}}}]),e}(),AF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e,t){return[this._hasVideoMetadata(e),this._hasAudioMetadata(e),this._checkRepeatedSps(e),this._metadataOnlyInSequeceHeader(e,t)]}},{key:"_hasVideoMetadata",value:function(e){var t,n=null==(t=null==e?void 0:Hw(e).call(e,(function(e){return"video"===e.streamType}))[0])?void 0:t.bodyView;if(n&&!n.sps)return{description:"【未检测到视频metadata信息】",tips:"可能视频无法播放或者报解码错误"}}},{key:"_hasAudioMetadata",value:function(e){var t,n=null==(t=null==e?void 0:Hw(e).call(e,(function(e){return"audio"===e.streamType}))[0])?void 0:t.bodyView;if(n&&(!n.config||!n.sampleRate||!n.channelCount))return{description:"【未检测到音频metadata信息】",tips:"可能视频无法播放或者报解码错误"}}},{key:"_checkRepeatedSps",value:function(e){var t,n=null==e?void 0:Hw(e).call(e,(function(e){return"video"===e.streamType}))[0],r=(null==(t=null==n?void 0:n.bodyView)?void 0:t.spsArr)||[];if(n&&r.length>1&&r[0].join(",")!==r[1].join(","))return{description:"【关键帧前包含多个sps信息】"}}},{key:"_metadataOnlyInSequeceHeader",value:function(e,t){var n;if("FLV"===t){var r=null==e?void 0:qm(e).call(e,(function(e){return e.keyframe}));if(r){var i=r.isHevc;return-1===hk(n=r.nalTypes).call(n,i?33:7)?{description:"【sps、pps信息只存在于Sequence Header中】"}:void 0}}}}]),e}(),IF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e){var t=null==e?void 0:qm(e).call(e,(function(e){return e.isSample&&"video"===e.streamType}));if(t)return t.isHevc?[{id:1,description:"【H265编码格式】",tips:"对于不支持H265解码的设备无法播放"}]:void 0}}]),e}(),RF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e){if(null==e?void 0:qm(e).call(e,(function(e){return e.isSample&&"B"===e.frameType})))return[{description:"【存在B帧】"}]}}]),e}(),OF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e){var t=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"video"===e.streamType})),n=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"audio"===e.streamType}));if(t.length&&n.length){var r,i,o=t[t.length-1].ptsTime-t[0].ptsTime,a=n[n.length-1].ptsTime-n[0].ptsTime;if(Math.abs(o-a)>=EF)return[{description:Fm(r=Fm(i="【音视频时长存在差距】, video=".concat(o,"s, audio=")).call(i,a,"s, delta=")).call(r,FN(o-a),"s"),tips:"可能造成播放画面卡顿或者声音卡顿"}]}}}]),e}(),DF=function(){function e(){Iy(this,e)}return Ly(e,[{key:"detect",value:function(e){var t=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"video"===e.streamType})),n=null==e?void 0:Hw(e).call(e,(function(e){return e.isSample&&"audio"===e.streamType}));if(t.length||n.length)return t.length&&n.length?void 0:[{description:"【流开始只包含".concat(t.length?"音频":"视频","】"),tips:"可能播放没有声音或者没有画面"}]}}]),e}();function MF(e,t){var n,r,i;return Hw(n=QM(r=Bw(i=[new IF,new CF,new xF,new PF,new AF,new OF,new RF,new DF]).call(i,(function(n){return n.detect(e,t)}))).call(r,(function(e,t){return Fm(e).call(e,t)}),[])).call(n,Boolean)}var LF=gE,NF=function(e,t,n){return function(e,t,n){t in e?LF(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n}(e,"symbol"!==Sc(t)?t+"":t,n),n},UF=function(){function e(t){var n=this;Iy(this,e),NF(this,"_opts",null),NF(this,"_controller",null),NF(this,"_reader",null),NF(this,"_loading",!1),NF(this,"_chunks",[]),NF(this,"_readStream",(function(e,t){var r=0;n._reader=e.getReader();var i=n._chunks;return function e(){var o;return null==(o=n._reader)?void 0:o.read().then((function(n){var o=n.value,a=n.done;return i.push(o),t(r,o),a?Vw.resolve(UN(Hw(i).call(i,Boolean))):(r+=o.byteLength,e())}))}()})),this._opts=t}return Ly(e,[{key:"request",value:function(){var e=this,t=this._opts,n=t.url,r=t.onProgress,i=t.contentType,o=void 0===i?"ArrayBuffer":i,a=t.loadTimeout,s=void 0===a?5e3:a,l=t.headers;return this._controller=new AbortController,Vw.race([fetch(n,{signal:this._controller.signal,headers:l}),new Vw((function(t,n){setTimeout((function(){var t;e._loading||(n({code:0,message:"timeout"}),null==(t=e._controller)||t.abort())}),s)}))]).then((function(t){return t.ok&&t.status>=200&&t.status<300?(e._loading=!0,t):Vw.reject({code:t.status,message:t.statusText})})).then((function(t){switch(o){case"TEXT":return t.text();case"JSON":return t.json();case"ArrayBuffer":return r?e._readStream(t.clone().body,r):t.arrayBuffer()}})).catch((function(t){if(e._loading=!1,"AbortError"!==t.name)return Vw.reject({code:t.code||0,message:t.message})}))}},{key:"cancel",value:function(){var e,t;try{null==(e=this._reader)||e.cancel(),null==(t=this._controller)||t.abort(),this._reader=null,this._controller=null}catch(AV){}}}]),e}(),FF=function(){function e(){Iy(this,e)}var t;return Ly(e,[{key:"parse",value:(t=ay(iy().mark((function e(t,n){var r,i,o,a,s,l,u,c,h,f,d,p=this;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return u=[],this._loader=new UF(Dm(Dm({url:t},n),{},{onProgress:function(e,t){u.push(t),e>n.probeSize&&p._loader.cancel()}})),e.next=4,this._loader.request();case 4:return c=new EU,h=c.parse(UN(u)),f=c.getSnapshoot(h),d=null==(r=f.video)?void 0:r.sps,e.abrupt("return",{codec:null==(i=f.video)?void 0:i.codecType,codecStr:null==d?void 0:d.codec,width:null==d?void 0:d.width,height:null==d?void 0:d.height,vDration:null==(o=null==f?void 0:f.video)?void 0:o.duration,aDuration:null==(a=null==f?void 0:f.audio)?void 0:a.duration,sampleRate:null==(s=null==f?void 0:f.audio)?void 0:s.sampleRate,channelCount:null==(l=null==f?void 0:f.audio)?void 0:l.channelCount,views:h,snap:f});case 9:case"end":return e.stop()}}),e,this)}))),function(e,n){return t.apply(this,arguments)})}]),e}(),BF={exports:{}};BF.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||gE(e,t,{enumerable:!0,get:r})},n.r=function(e){void 0!==yE&&lM&&gE(e,lM,{value:"Module"}),gE(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==Sc(e)&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),gE(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){function r(e,t){var n=fk(e);if(fE){var r=fE(e);t&&(r=Hw(r).call(r,(function(t){return dE(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){return(i="function"==typeof yE&&"symbol"==Sc(mE)?function(e){return Sc(e)}:function(e){return e&&"function"==typeof yE&&e.constructor===yE&&e!==yE.prototype?"symbol":Sc(e)})(e)}function o(e,t,n){return t in e?gE(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.r(t),n.d(t,"m3u8Parser",(function(){return h})),n.d(t,"mpdParser",(function(){return g}));var a=/EXT(?:-X-)?([^:]+):?(.*)$/,s=/([^,="]+)((="[^"]+")|(=[^,]+))*/g;function l(e){var t;return QM(t=e.split("-")).call(t,(function(e,t){return e+(e?t.charAt(0)+dk(t).call(t,1).toLowerCase():t.toLowerCase())}),"")}function u(e){if(!/^#EXT/.test(e)&&/^\s*#/.test(e))return null;var t,n,u=a.exec(e);return u?(t=l(u[1]),n=function(e,t){if(!e)return null;var n=Bw(t=e.match(s)).call(t,(function(e){return function(e){var t=PS(e).call(e).replace("=","|").split("|");if(2==t.length)return o({},l(t[0]),t[1].replace(/("|')/g,""));var n=_S(t[0]);return Lk(n)?t[0]:n}(e)}));return 1===n.length?n[0]:0===Hw(n).call(n,(function(e){return"object"===i(e)})).length?n:QM(n).call(n,(function(e,t){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){o(e,t,n[t])})):pE?vE(e,pE(n)):r(Object(n)).forEach((function(t){gE(e,t,dE(n,t))}))}return e}({},e,{},t)}),{})}(u[2])):(t="url",n=e),o({},t,n)}function c(e,t){if(/^https?/.test(e)||/^data:/.test(e)||/^sdk:/.test(e))return e;for(t=dk(n=t.split("/")).call(n,0,-1),e=e.split("/");e.length;){var n,r=e.shift();-1==hk(t).call(t,r)&&t.push(r)}return t.join("/")}var h=function(e,t,n){var r,i,o;if(!e||!t)return{error:1,msg:"invalid input"};var a,s=Hw(r=Bw(i=Hw(o=e.split("\n")).call(o,Boolean)).call(i,(function(e){return u(PS(e).call(e))}))).call(r,Boolean);if(!s.length)return{error:1,msg:"invalid m3u8"};a=0!==Hw(s).call(s,(function(e){return!!e.streamInf})).length?{master:!0,m3u8Url:t,levels:[],medias:[]}:{master:!1,m3u8Url:t,duration:0,startSN:0,endSN:0,segments:[],live:!0};try{a=function(e,t,n){for(var r=t.master,i=e.length,o=0,a=0,s=0,l=0,u=0,h=-1,f=0;f<i;f++){var d=e[f];for(var p in d){var v=d[p];switch(v&&v.uri&&(v.url=c(v.uri,t.m3u8Url)),p){case"inf":var g={start:a,end:a+(Array.isArray(v)?v[0]:v),cc:o,sn:s+l};h>=0&&(g.keyIndex=h),l++,a=g.end,t.segments.push(g);break;case"start":a=v;break;case"discontinuity":o++;break;case"mediaSequence":s=v;break;case"streamInf":u++,v.levelId=u,t.levels.push(v);break;case"media":t.medias.push(v);break;case"endlist":t.live=!1;break;case"key":h++,t.key?t.key.push(v):t.key=[v];break;case"url":var y=r?t.levels:t.segments;if(!y.length)throw new Error("invalid m3u8");r?y[u-1].url=c(v,t.m3u8Url):y[l-1].url=c(v,t.m3u8Url);break;default:v&&(t[p]=v)}}n&&(t=n(d,t))}return r||(t.startSN=s,t.endSN=s+l-1,t.duration=a),t}(s,a,n)}catch(l){return{error:1,msg:l.message}}return a},f=/\s*(<\/?[^>]+>)/,d=/\s*(?:<\/?([^\s>]+))?\s*([^>]+)*(?:\/?>)?/,p=/\s*([^=]+)="([^"]+)"/g;function v(e,t){for(var n,r,i,o=[],a=e.length,s=0;s<a;s++)if((n=e[s]).closed)if(r=o.pop(),"pureValue"!==n.tagName){if(r&&r.tagName===n.tagName?i=o.pop():(i=r,r=n),!i){o.push(r);break}var l=r,u=l.tagName,c=l.attrs,h=i.attrs[u];t&&(c=t(u,c)),i.attrs[u]=h?Array.isArray(h)?Fm(h).call(h,c):[h,c]:c,o.push(i)}else r.attrs?r.attrs.value=n.attrs.value:r.attrs=n.attrs,o.push(r);else o.push(n);var f,d,p,v=o[0];return v?"undefined"!=v.tagName?(f={},d=v.tagName,p=v.attrs,d in f?gE(f,d,{value:p,enumerable:!0,configurable:!0,writable:!0}):f[d]=p,f):v.tagName:null}var g=function(e,t){if(!e)return{error:1,msg:"invalid input"};try{var n,r,i;return v(Hw(n=Bw(r=Hw(i=e.split(f)).call(i,Boolean)).call(r,(function(e){return function(e){var t,n,r=e.match(d),i={},o=(t=r&&r[1])?QM(n=t.split("_")).call(n,(function(e,t){return e?e+=t.charAt(0).toUpperCase()+dk(t).call(t,1):e=t.charAt(0).toLowerCase()+dk(t).call(t,1)}),""):"";if(r&&r[1]&&r[2]){for(var a,s={};null!=(a=p.exec(r[2]));)s[a[1]]=a[2];return"?xml"===o?null:(i.tagName=o,i.attrs=s,i.closed=/\/$/.test(r[2]),i)}return r?/^<\//.test(r[0])?(i.tagName=o,i.closed=!0,i):/^</.test(r[0])?(i.tagName=o,i.attrs={},i):(i.tagName="pureValue",i.attrs={value:r[2]},i.closed=!0,i):null}(e)}))).call(n,Boolean),t)}catch(o){return{error:1,msg:o.message}}}}]);var VF=BF.exports;function HF(e){return jF.apply(this,arguments)}function jF(){return jF=ay(iy().mark((function e(t){var n,r,i,o=arguments;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=new UF(Dm(Dm({url:t},o.length>1&&void 0!==o[1]?o[1]:{}),{},{contentType:"TEXT"})),e.next=4,n.request();case 4:return r=e.sent,i=VF.m3u8Parser(r,t,(function(e,t){var n;if(!e.url||!/^\//.test(e.url))return t;var r=e.url,i=t.segments[t.segments.length-1];return i.url=dk(n=i.url.split("/")).call(n,0,3).join("/")+r,t})),e.abrupt("return",i);case 7:case"end":return e.stop()}}),e)}))),jF.apply(this,arguments)}var zF=function(){function e(){Iy(this,e)}var t;return Ly(e,[{key:"parse",value:(t=ay(iy().mark((function e(t,n){var r,i,o,a,s,l,u,c,h,f,d,p,v,g,y,m;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,HF(t,n);case 2:if(!(u=e.sent).master){e.next=7;break}return e.next=6,HF(u.levels[0].url);case 6:u=e.sent;case 7:if(null==u?void 0:u.segments.length){e.next=9;break}throw new Error("invalid m3u8");case 9:c=[],h=n.nbSegment,f=0,d=iy().mark((function e(){var t;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=new UF(Dm(Dm({url:u.segments[f].url},n),{},{onProgress:function(e,r){c.push(r),e>n.probeSize&&t.cancel()}})),e.next=3,t.request();case 3:h--,f++;case 5:case"end":return e.stop()}}),e)}));case 13:if(!h){e.next=17;break}return e.delegateYield(d(),"t0",15);case 15:e.next=13;break;case 17:return p=new Uint8Array(UN(Hw(c).call(c,Boolean))),v=new OU,g=v.parse(p),y=v.getSnapshoot(g),m=null==(r=y.video)?void 0:r.sps,e.abrupt("return",{codec:null==(i=y.video)?void 0:i.codecType,codecStr:null==m?void 0:m.codec,width:null==m?void 0:m.width,height:null==m?void 0:m.height,vDration:null==(o=null==y?void 0:y.video)?void 0:o.duration,aDuration:null==(a=null==y?void 0:y.audio)?void 0:a.duration,sampleRate:null==(s=null==y?void 0:y.audio)?void 0:s.sampleRate,channelCount:null==(l=null==y?void 0:y.audio)?void 0:l.channelCount,views:g,snap:y});case 23:case"end":return e.stop()}}),e)}))),function(e,n){return t.apply(this,arguments)})}]),e}(),KF=function(){function e(){Iy(this,e)}var t;return Ly(e,[{key:"parse",value:(t=ay(iy().mark((function e(t,n){var r,i,o,a,s,l,u,c,h,f,d,p,v,g,y,m,_,b,w;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return c=new UF(Dm(Dm({url:t},n),{},{contentType:"ArrayBuffer",headers:{Range:"bytes=0-1499"}})),h=[],e.next=4,c.request();case 4:return f=e.sent,d=new Uint8Array(f),p=(new bF).parse(d),v=qm(p).call(p,(function(e){return"mdat"===e.type})),(g=qm(p).call(p,(function(e){return"moov"===e.type})))?(h.push(g.start),h.push(g.start+g.size)):v&&h.push(v.start+v.size),c=new UF(Dm(Dm({url:t},n),{},{contentType:"ArrayBuffer",headers:{Range:Fm(r="bytes=".concat(h[0],"-")).call(r,h[1]||"")}})),e.next=13,c.request();case 13:return y=e.sent,m=new bF,_=m.parse(y),b=m.getSnapshoot(_),w=null==(i=b.video)?void 0:i.sps,e.abrupt("return",{codec:null==(o=b.video)?void 0:o.codecType,codecStr:null==w?void 0:w.codec,width:null==w?void 0:w.width,height:null==w?void 0:w.height,vDration:null==(a=null==b?void 0:b.video)?void 0:a.duration,aDuration:null==(s=null==b?void 0:b.audio)?void 0:s.duration,sampleRate:null==(l=null==b?void 0:b.audio)?void 0:l.sampleRate,channelCount:null==(u=null==b?void 0:b.audio)?void 0:u.channelCount,views:m.parseV2(_,!0),snap:b});case 19:case"end":return e.stop()}}),e)}))),function(e,n){return t.apply(this,arguments)})}]),e}(),WF=function(){function e(){Iy(this,e)}var t;return Ly(e,[{key:"parseUrl",value:(t=ay(iy().mark((function e(t){var n,r,i,o,a=arguments;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=BT({timeout:5e3,probeSize:8e4,nbSegment:1},r=a.length>1&&void 0!==a[1]?a[1]:{}),!/flv/.test(t)){e.next=9;break}return e.next=5,(new FF).parse(t,r);case 5:i=e.sent,o=TU,e.next=20;break;case 9:if(!/m3u8?/.test(t)){e.next=16;break}return e.next=12,(new zF).parse(t,r);case 12:i=e.sent,o=SU,e.next=20;break;case 16:return e.next=18,(new KF).parse(t,r);case 18:i=e.sent,o=CU;case 20:return i.exceps=Hw(n=MF(i.views.items,o)).call(n,(function(e){return 1!==e.id})),e.abrupt("return",i);case 22:case"end":return e.stop()}}),e)}))),function(e){return t.apply(this,arguments)})}]),e}(),GF=function(){var e=ay(iy().mark((function e(n){var r,i,o,a,s,l,u,c,h,f,d,p;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,$F(n);case 2:return u=e.sent,e.next=5,nB(n,u);case 5:if(e.sent&&(l=u===t.H265?QF():JF(),s=YF(n)),$L("unknown"===u?t.H264:u)&&(s=YF(n)),s||l){e.next=10;break}return e.abrupt("return",{});case 10:return e.next=12,Vw.all([(null===(r=s)||void 0===r?void 0:r.module)&&KL(s.module).catch((function(){})),(null===(i=l)||void 0===i?void 0:i.module)&&KL(l.module).catch((function(){}))]);case 12:return c=e.sent,h=AM(c,2),f=h[0],(d=h[1])&&(null===(o=l)||void 0===o||null===(a=o.afterLoad)||void 0===a||a.call(o,d)),p=iB([s,l]),e.abrupt("return",{options:p,plugins:f?[f]:[]});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),qF=function(){var e=ay(iy().mark((function e(n){var r,i,o,a,s,l,u,c,h,f,d,p,v;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,$F(n);case 2:return c=e.sent,e.next=5,nB(n,c);case 5:if(e.sent&&(u=c===t.H265?QF():JF(),l=XF(n)),("mobile"!==xC.device||null!=n&&null!==(r=n.hls)&&void 0!==r&&r.enableMSE)&&$L("unknown"===c?t.H264:c)&&(l=XF(n)),l||u){e.next=10;break}return e.abrupt("return",{});case 10:return e.next=12,Vw.all([(null===(i=l)||void 0===i?void 0:i.module)&&KL(l.module).catch((function(){})),(null===(o=u)||void 0===o?void 0:o.module)&&KL(u.module).catch((function(){}))]);case 12:return h=e.sent,f=AM(h,2),d=f[0],(p=f[1])&&(null===(a=u)||void 0===a||null===(s=a.afterLoad)||void 0===s||s.call(a,p)),v=iB([l,u]),e.abrupt("return",{options:v,plugins:d?[d]:[]});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),YF=function(e){var t;return{options:{flv:Dm({retryCount:0},null!==(t=null==e?void 0:e.flv)&&void 0!==t?t:{})},module:wL.PluginFlv}},XF=function(e){var t;return{options:{hls:Dm({retryCount:0},null!==(t=null==e?void 0:e.hls)&&void 0!==t?t:{})},module:wL.PluginHls}},JF=function(){return{options:{mediaType:"xg-video",innerDegrade:1,codecType:t.H264},module:wL.PluginXgvideo,afterLoad:function(e){e.isSupported()&&e.setDecodeCapacity({preloadDecoder:t.H264,wasmFallbackToAsm:!1,reuseWasmDecoder:!1,disabledWhenErrorOccur:!1})}}},QF=function(){return{options:{mediaType:"xg-video",innerDegrade:1,codecType:t.H265},module:wL.PluginXgvideo,afterLoad:function(e){e.isSupported()&&e.setDecodeCapacity({preloadDecoder:t.H265,wasmFallbackToAsm:!1,reuseWasmDecoder:!1})}}};function $F(e){return ZF.apply(this,arguments)}function ZF(){return(ZF=ay(iy().mark((function e(t){return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.codec){e.next=2;break}return e.abrupt("return",t.codec);case 2:if(t.degradation!==n.SoftFirst||!t.url){e.next=6;break}return e.next=5,eB(t.url);case 5:return e.abrupt("return",e.sent);case 6:return e.abrupt("return","unknown");case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function eB(e){return tB.apply(this,arguments)}function tB(){return(tB=ay(iy().mark((function e(n){var r,i;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(new WF).parseUrl(n);case 3:if(r=e.sent,"bytevc1"!==(i=r.codec)){e.next=9;break}return e.abrupt("return",t.H265);case 9:if("avc"!==i){e.next=11;break}return e.abrupt("return",t.H264);case 11:return e.abrupt("return","unknown");case 14:return e.prev=14,e.t0=e.catch(0),console.error("detect codec error: ",e.t0),e.abrupt("return","unknown");case 18:case"end":return e.stop()}}),e,null,[[0,14]])})))).apply(this,arguments)}function nB(e,t){return rB.apply(this,arguments)}function rB(){return(rB=ay(iy().mark((function e(i,o){return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i.decodeType!==r.Software){e.next=2;break}return e.abrupt("return",!0);case 2:if("unknown"!==o){e.next=4;break}return e.abrupt("return",!1);case 4:return e.next=6,$F(i);case 6:if(e.sent!==t.H265){e.next=12;break}if(!xC.isHevcSupported()){e.next=10;break}return e.abrupt("return",!1);case 10:if(i.degradation!==n.SoftFirst){e.next=12;break}return e.abrupt("return",!0);case 12:return e.abrupt("return",!1);case 13:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function iB(e){return QM(e).call(e,(function(e,t){return BT(e,null==t?void 0:t.options)}),{})}var oB=oA,aB=function(e){return e[e.INVALID_PARAMETER=210]="INVALID_PARAMETER",e}(aB||{}),sB=Dm(Dm({},aB),nA),lB=Dm(Dm({},rA),{},Ny({},210,{messageTextKey:"INVALID_PARAMETER",level:tA.Fatal}));function uB(e,t){return new oB(lB[e],t)}function cB(){return hB.apply(this,arguments)}function hB(){return(hB=ay(iy().mark((function e(){var t;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,KL(wL.PluginRtm);case 2:return t=e.sent,e.abrupt("return",t.isSupported());case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function fB(){return dB.apply(this,arguments)}function dB(){return dB=ay(iy().mark((function e(){var t,n,r=arguments;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:TH.H264,e.next=3,KL(wL.PluginRtm);case 3:if(n=e.sent,t!==TH.H264){e.next=6;break}return e.abrupt("return",n.isSupportedH264());case 6:return e.abrupt("return",!1);case 7:case"end":return e.stop()}}),e)}))),dB.apply(this,arguments)}var pB=["fallbackUrl","enableFallback"],vB={options:{},module:wL.PluginRtm},gB=function(e){return"pc"===xC.device?e.replace(".sdp",".flv"):e.replace(".sdp",".m3u8")},yB=function(){var e=ay(iy().mark((function e(n,r){var i,o,a,s,l,u,c,h,f,d,p,v,g,y,m,_,b,w,k,E;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s=n.url,l=n.rtm||{},u=l.fallbackUrl,c=l.enableFallback,h=void 0===c||c,f=em(l,pB),d=h?!u&&s?gB(s):u:"","flv"===(p=d&&tN.getStreamType(d))&&tN.isMseSupported(t.H264)?a=YF(n):"hls"===p&&("mobile"!==xC.device||null!=n&&null!==(i=n.hls)&&void 0!==i&&i.enableMSE)&&tN.isMseSupported(t.H264)&&(a=XF(n)),e.next=7,Vw.all([KL(vB.module).catch((function(){})),a&&KL(a.module).catch((function(){}))]);case 7:return v=e.sent,g=AM(v,2),y=g[0],m=g[1],e.next=13,Vw.all([cB(),fB()]);case 13:if(_=e.sent,b=AM(_,2),w=b[0],k=b[1],w&&k){e.next=20;break}return r&&r.emit("degrade",{url:d,originRtmUrl:s,code:"NOT_SUPPORT",message:"not support rtm or h264",isRTMSupported:w,isRTMSupportCodec:k}),e.abrupt("return",{options:Dm(Dm({},(null===(E=a)||void 0===E?void 0:E.options)||{}),{},{url:d,_RTMdegrade:{_originRtmUrl:s,_isRTMSupported:w,_isRTMSupportCodec:k}}),plugins:m?[m]:[]});case 20:return e.abrupt("return",{options:Dm(Dm({},(null===(o=a)||void 0===o?void 0:o.options)||{}),{},{_RTMdegrade:void 0,rts:Dm(Dm({retryCount:0},f),{},{backupURL:d,backupConstruct:m})}),plugins:y?[y]:[]});case 21:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),mB=function(){var e=ay(iy().mark((function e(t,n){var r;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((r=t.url?tN.getStreamType(t.url):"")&&"unknown"!==r){e.next=3;break}return e.abrupt("return",{options:{},plugins:[]});case 3:if("rtm"!==r){e.next=7;break}return e.next=6,yB(t,n);case 6:case 10:case 14:return e.abrupt("return",e.sent);case 7:if("flv"!==r){e.next=11;break}return e.next=10,GF(t);case 11:if("hls"!==r){e.next=15;break}return e.next=14,qF(t);case 15:return e.abrupt("return",{options:{},plugins:[]});case 16:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),_B=NaN,bB="[object Symbol]",wB=/^\s+|\s+$/g,kB=/^[-+]0x[0-9a-f]+$/i,EB=/^0b[01]+$/i,TB=/^0o[0-7]+$/i,SB=rE,CB="object"==Sc(i)&&i&&i.Object===Object&&i,xB="object"==("undefined"==typeof self?"undefined":Sc(self))&&self&&self.Object===Object&&self,PB=CB||xB||Function("return this")(),AB=Object.prototype.toString,IB=Math.max,RB=Math.min,OB=function(){return PB.Date.now()};function DB(e){var t=Sc(e);return!!e&&("object"==t||"function"==t)}function MB(e){return"symbol"==Sc(e)||function(e){return!!e&&"object"==Sc(e)}(e)&&AB.call(e)==bB}function LB(e){if("number"==typeof e)return e;if(MB(e))return _B;if(DB(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=DB(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(wB,"");var n=EB.test(e);return n||TB.test(e)?SB(dk(e).call(e,2),n?2:8):kB.test(e)?_B:+e}var NB=function(e,t,n){var r,i,o,a,s,l,u=0,c=!1,h=!1,f=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function d(t){var n=r,o=i;return r=i=void 0,u=t,a=e.apply(o,n)}function p(e){var n=e-l;return void 0===l||n>=t||n<0||h&&e-u>=o}function v(){var e=OB();if(p(e))return g(e);s=setTimeout(v,function(e){var n=t-(e-l);return h?RB(n,o-(e-u)):n}(e))}function g(e){return s=void 0,f&&r?d(e):(r=i=void 0,a)}function y(){var e=OB(),n=p(e);if(r=arguments,i=this,l=e,n){if(void 0===s)return function(e){return u=e,s=setTimeout(v,t),c?d(e):a}(l);if(h)return s=setTimeout(v,t),d(l)}return void 0===s&&(s=setTimeout(v,t)),a}return t=LB(t)||0,DB(n)&&(c=!!n.leading,o=(h="maxWait"in n)?IB(LB(n.maxWait)||0,t):o,f="trailing"in n?!!n.trailing:f),y.cancel=function(){void 0!==s&&clearTimeout(s),u=0,r=l=i=s=void 0},y.flush=function(){return void 0===s?a:g(OB())},y},UB=o(NB),FB=Dm(Dm({},CM),{},{TIME_SHIFT_CHANGE:"time_shift_change",REFRESH_CLICK:"refresh_click"});function BB(){return(new DOMParser).parseFromString('<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M10 2.488a7.48 7.48 0 00-4.154 1.253.908.908 0 101.005 1.512 5.695 5.695 0 018.844 4.746h-.908a.33.33 0 00-.264.528l1.816 2.422a.33.33 0 00.529 0l1.815-2.422A.33.33 0 0018.42 10h-.908A7.511 7.511 0 0010 2.49zM2.489 10a7.511 7.511 0 0011.665 6.26.908.908 0 00-1.005-1.513A5.695 5.695 0 014.305 10h.908a.33.33 0 00.264-.528L3.66 7.05a.33.33 0 00-.528 0L1.317 9.47a.33.33 0 00.264.53h.908z" fill="#fff"/></svg>',"image/svg+xml").firstChild}var VB=qx.POSITIONS,HB=function(e){XP(n,e);var t=$P(n);function n(){return Iy(this,n),t.apply(this,arguments)}return Ly(n,[{key:"afterCreate",value:function(){this.config.disable||(this.appendChild(".xgplayer-icon",this.icons.refresh),this._initEvents())}},{key:"registerIcons",value:function(){return{refresh:{icon:BB,class:"xgplayer-refresh-svg"}}}},{key:"destroy",value:function(){this.unbind(["touchend","click"],this._handleRefresh)}},{key:"render",value:function(){var e=this.player.config.i18nManager;return'\n    <xg-icon class="veplayer-refresh">\n      <div class="xgplayer-icon">\n      </div>\n      <div class="xg-tips" lang-key="refresh">'.concat(e.getText("REFRESH"),"</div>\n    </xg-icon>\n    ")}},{key:"_handleRefresh",value:function(e){var t=this.player;if(t){var n;if(e.preventDefault(),e.stopPropagation(),t.addClass("xgplayer-is-enter"),t.pause(),t)t.emit(FB.REFRESH_CLICK),null===(n=this.player)||void 0===n||null===(n=n.config)||void 0===n||null===(n=n.veplayer)||void 0===n||n.retry();t.once(BC,(function(){t.removeClass("xgplayer-is-enter")}))}}},{key:"_initEvents",value:function(){this._handleRefresh=UB(this._handleRefresh.bind(this),200);var e="mobile"===xC.device?"touchend":"click";this.bind(e,this._handleRefresh),this.show()}}],[{key:"pluginName",get:function(){return"refresh"}},{key:"defaultConfig",get:function(){return{position:VB.CONTROLS_LEFT,index:1,disable:!1}}}]),n}(qx),jB=o(ie.URLSearchParams),zB=nE;On({target:"Number",stat:!0,forced:Number.parseInt!=zB},{parseInt:zB});var KB=o(ie.Number.parseInt),WB=mS;On({target:"Number",stat:!0,forced:Number.parseFloat!=WB},{parseFloat:WB});var GB=o(ie.Number.parseFloat);function qB(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),gE(e,r.key,r)}}function YB(e,t,n){return t&&qB(e.prototype,t),n&&qB(e,n),e}function XB(e,t,n){return t in e?gE(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function JB(e,t){var n=fk(e);if(fE){var r=fE(e);t&&(r=Hw(r).call(r,(function(t){return dE(e,t).enumerable}))),n.push.apply(n,r)}return n}function QB(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?JB(Object(n),!0).forEach((function(t){XB(e,t,n[t])})):pE?vE(e,pE(n)):JB(Object(n)).forEach((function(t){gE(e,t,dE(n,t))}))}return e}var $B="xgplayer_device_id",ZB="xgplayer_user_id",eV=function(e){if(!document.cookie)return null;for(var t=document.cookie.split(";"),n=0,r=t.length;n<r;n++){var i,o=t[n];if(hk(i=t[n]).call(i,e)>=0)return o.split("=")[1]||null}},tV=function(e){return String(Math.floor(Math.random()*Math.pow(10,e)))},nV=function(){var e=eV($B);if(e)return e;var t=tV(11);return document.cookie=$B+"="+t+";max-age=31536000;path=/",t},rV=function(){var e=eV(ZB);if(e)return e;var t=tV(12);return document.cookie=ZB+"="+t+";max-age=31536000;path=/",t};var iV={get device(){return iV.os.isPc?"pc":"mobile"},get browser(){var e=function(){var e=navigator.userAgent.toLowerCase(),t={},n={IE:window.ActiveXObject||"ActiveXObject"in window,Chrome:hk(e).call(e,"chrome")>-1&&hk(e).call(e,"safari")>-1,Firefox:hk(e).call(e,"firefox")>-1,Opera:hk(e).call(e,"opera")>-1,Safari:hk(e).call(e,"safari")>-1&&-1==hk(e).call(e,"chrome"),Edge:hk(e).call(e,"edge")>-1,QQBrowser:/qqbrowser/.test(e),WeixinBrowser:/MicroMessenger/i.test(e)};for(var r in n)if(n[r]){var i="";if("IE"===r)i=e.match(/(msie\s|trident.*rv:)([\w.]+)/)[2];else if("Chrome"===r){for(var o in navigator.mimeTypes)"application/360softmgrplugin"===navigator.mimeTypes[o].type&&(r="360");var a=e.match(/chrome\/([\d.]+)/);i=a?a[1]:"-1"}else if("Firefox"===r){var s=e.match(/firefox\/([\d.]+)/);i=s?s[1]:"-1"}else if("Opera"===r){var l=e.match(/opera\/([\d.]+)/);i=l?l[1]:"-1"}else if("Safari"===r){var u=e.match(/version\/([\d.]+)/);i=u?u[1]:"-1"}else if("Edge"===r){var c=e.match(/edge\/([\d.]+)/);i=c?c[1]:"-1"}else if("QQBrowser"===r){var h=e.match(/qqbrowser\/([\d.]+)/);i=h?h[1]:"-1"}t.type=r,t.versions=rE(i)}return t}();return e.type+"_"+e.versions},get os(){var e=navigator.userAgent;navigator.platform.toLowerCase();var t=/(?:Windows Phone)/.test(e),n=/(?:SymbianOS)/.test(e)||t,r=/(?:Android)/.test(e),i=/(?:Firefox)/.test(e),o=/(?:iPad|PlayBook)/.test(e)||r&&!/(?:Mobile)/.test(e)||i&&/(?:Tablet)/.test(e),a=/(?:iPhone)/.test(e)&&!o;return{isTablet:o,isPhone:a,isAndroid:r,isPc:!(a||r||n||o),isSymbian:n,isWindowsPhone:t,isFireFox:i}},get operation_os(){for(var e=navigator.userAgent,t=[{s:"windows",r:/(Windows 10.0|Windows NT 10.0|Windows NT 10.1|Windows 8.1|Windows NT 6.3|Windows 8|Windows NT 6.2|Windows 7|Windows NT 6.1)/},{s:"android",r:/Android/},{s:"linux",r:/(Linux|X11)/},{s:"ios",r:/(iPhone|iPad|iPod)/},{s:"mac",r:/Mac OS X/},{s:"mac",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/}],n=0;n<t.length;n++){var r=t[n];if(r.r.test(e))return r.s}return"unknown"}};function oV(){return Date.now()}function aV(e){var t="unknown";return"string"!=typeof e?t:hk(e).call(e,".flv")>-1?"flv":hk(e).call(e,".m3u8")>-1?"m3u8":hk(e).call(e,".mp4")>-1?"mp4":hk(e).call(e,".sdp")>-1?"rts":t}function sV(e){if(e.plugins){if(e.plugins.flv)return e.plugins.flv;if(e.plugins.hls)return e.plugins.hls;if(e.plugins.rts)return e.plugins.rts;if(e.plugins.rtm)return e.plugins.rtm}return null}var lV={1:"MEDIA_ERR_ABORTED",2:"MEDIA_ERR_NETWORK",3:"MEDIA_ERR_DECODE",4:"MEDIA_ERR_SRC_NOT_SUPPORTED"},uV=function(){function e(e){var t=e.Tea;if(!t)throw new Error("lack Tea option");t.event?this.Tea=t.event.bind(t):this.Tea=t,this.logOptions=e,this._player=e.player}YB(e,[{key:"videoSize",get:function(){return this._player&&this._player.video?{width:this._player.video.videoWidth,height:this._player.video.videoHeight}:{width:0,height:0}}}]);var t=e.prototype;return t.push=function(e){var t=this._player.config.url;this._player.video&&(e.play_current_time=this._player.currentTime),this.core&&this.core.loader&&this.core.loader.finnalUrl?e.cdn_play_url=this.core.loader.finnalUrl:e.cdn_play_url=t&&TM(t).call(t,"//")?window.location.protocol+t:t,e.live_stream_session_id=function(e,t,n){var r=new jB(e.split("?")[1]);return r.get(t)||r.get(n)}(e.cdn_play_url,"_session_id","session_id"),e.play_format=aV(e.cdn_play_url),e.timestamp=oV();var n=function(){var e={cpu_core_number:-1,memory_usage:-1,network_downlink:-1};try{e.cpu_core_number=navigator.hardwareConcurrency||-1,e.memory_usage=navigator.deviceMemory||-1,e.network_downlink=navigator.connection&&navigator.connection.downlink,null==e.network_downlink&&(e.network_downlink=-1)}catch(PH){}return e}(),r=n.cpu_core_number,i=n.memory_usage,o=n.network_downlink;e.cpu_core_number=r,e.memory_usage=i,e.network_downlink=o,BT(e,QB({},this.videoSize)),this.send(e)},t.send=function(e){e.event_key&&(this.logOptions.onReport&&this.logOptions.onReport(e.event_key,e),this.Tea(e.event_key,QB({},e)))},e}(),cV=function(){return void 0!==navigator.onLine?navigator.onLine?(e=navigator.connection)&&(e.effectiveType||e.type)||iV.device:"none":"unknown";var e},hV=["play","playing","pause","ended","error","seeking","seeked","timeupdate","waiting","canplay","canplaythrough","durationchange","ratechange","volumechange","loadeddata"],fV=function(){function e(e){var t;this.options=e,this.logger=new dV(e),this.restart=this.restart.bind(this),this.bindUrlChange(),ck(t=iV.browser).call(t,"Chrome")}var t=e.prototype;return t.restart=function(e){this.destroy("string"!=typeof e&&Boolean(e)),this.logger=new dV(this.options),this.bindUrlChange(),this.logger.reportStartPlay(),this.logger.handlePlayerComlete()},t.bindUrlChange=function(){this.options&&this.options.player&&!this.options.manual_restart&&this.options.player.on("urlchange",this.restart)},t.setNTP=function(e){this.logger.setNTP(e)},t.updateExt=function(e){this.options.ext=BT(this.options.ext||{},e),this.logger&&this.logger.updateExt(e)},t.destroy=function(e){this.logger.destroy(e),this.logger=null,this.options&&this.options.player&&!this.options.manual_restart&&this.options.player.off("urlchange",this.restart)},e}(),dV=function(){function e(e){if(!e.player)throw new Error("option player is necessary");this.options=BT({},e,function(e){return{device_id:e.device_id||nV(),user_id:e.user_id||rV(),error_report_stop:!!e.error_report_stop,project_key:e.project_key||"live",product_line:e.product_line||"live",onReport:e.onReport||function(){}}}(e)),this._player=this.options.player,this._url=this._player.config.url,this.project_key=e.project_key||"live",this.product_line=e.product_line||"live",this.ntpDelta=this.options.ntp?Date.now()-this.options.ntp:0,this.inWaitingStart=0,this.errorNumber=0,this.tempDroppedFrameCount=0,this.tempTotalFrameCount=0,this._logmanager=new uV(this.options),this.init(),this.started=!1,this.destroyed=!1,this.completed=!1,this.firstFrameViewed=!1,this.cacheRtcStats={}}YB(e,[{key:"videoSize",get:function(){return this.player?{width:this.player.video.videoWidth,height:this.player.video.videoHeight}:{width:0,height:0}}},{key:"player",get:function(){return this._player}},{key:"logmanager",get:function(){return this._logmanager}},{key:"ntpTime",get:function(){return Date.now()-this.ntpDelta}}]);var t=e.prototype;return t.setNTP=function(e){this.options&&(this.options.ntp=e,this.ntpDelta=Date.now()-this.options.ntp)},t.init=function(){this.bindCtx(),this.initLog(),this.initEvt()},t.bindCtx=function(){this.handlePlayerComlete=this.handlePlayerComlete.bind(this),this.handlePlayerPlaying=this.handlePlayerPlaying.bind(this),this.destroyFunc=this.destroyFunc.bind(this),this.handleUserLeave=this.handleUserLeave.bind(this),this.handlePlayerError=this.handlePlayerError.bind(this),this.handlePlayerReady=this.handlePlayerReady.bind(this),this.handlePlayerWaiting=this.handlePlayerWaiting.bind(this),this.handleAVUnsync=this.handleAVUnsync.bind(this),this.handlePlayerPause=this.handlePlayerPause.bind(this),this.handleTTFB=this.handleTTFB.bind(this),this.handleUrlChange=this.handleUrlChange.bind(this),this.handleKeyframe=this.handleKeyframe.bind(this),this.handleSourceOpen=this.handleSourceOpen.bind(this),this.handleBufferAppend=this.handleBufferAppend.bind(this),this.handleCoreEvent=this.handleCoreEvent.bind(this),this.handleDegrade=this.handleDegrade.bind(this)},t.initLog=function(){var e=this,t=this.options,n=t.player,r=void 0!==n.hlsOps;this.commonParams=QB({os:iV.operation_os},function(e){var t,n=e.player,r=n.config.mediaType;return QB({project_key:e.project_key,live_sdk_version:sV(n)||n.hlsOps?"2":"-1",player_sdk_version:n.version,logger_version:"1.1.0-alpha.7",report_version:"5",product_line:e.product_line,user_id:e.user_id,device_id:e.device_id,logger_id:tV(12),app_name:e.app_name,cdn_ip:e.cdn_ip,is_wasm:+(!!r&&"video"!==r),review_is_live:n.config.isLive?2:1,aid:e.aid,live_id:e.live_id||e.aid+"-"+e.user_id+"-"+Date.now(),is_preview:e.is_preview?1:0,codec_type:e.codec_type||"h264",width:Math.floor(n.config.width),height:Math.floor(n.config.height),browser:iV.browser,ua:navigator.userAgent,href:window.location.href,timestamp:0,fps:-1,logger_init_time:Date.now(),play_current_time:0,cdn_play_url:n.config.url&&TM(t=n.config.url).call(t,"//")?window.location.protocol+n.config.url:n.config.url,play_format:aV(n.config.url),codec_support:-1},e.ext)}(t)),this.log=BT({},{start_play:{start_play_time:0,event_key:"start_play"},first_frame:{start:0,first_frame_view:0,event_key:"first_frame",first_sei_delay:0,ntp_sync:0,ntp_delta:0,ttfb:-1,ttdb:-1,ttfb_end:0},first_frame_failed:{code:void 0,msg:"",event_key:"first_frame_failed"},stall_start:{ready_state:-1,stall_start_time:0,event_key:"stall_start"},stalling:{event_key:"stalling"},stall:{reason:0,stall_start:0,stall_end:0,ready_state:0,event_key:"stall"},play_stop:{code:0,stop_time:0,drop_percent:0,is_stream_received:0,stall_count:0,stall_time:0,retry_count:0,duration:0,stall_count_per_100sec:0,stall_time_per_100sec:0,event_key:"play_stop",dns_parse_time:-1,redirect:-1,ttfb:-1,buffered:[],is_firstframe_received:0,meta:null},playing:{index:0,stall_count:0,stall_time:0,play_time:1e4,retry_count:0,is_last:0,video_buffer_time:0,audio_buffer_time:0,speed_switch_count:0,speed_switch_info:"none",download_speed:0,decode_fps:0,render_fps:0,drop_count:0,drop_percent:0,sei_source:"",sei_delay:0,ntp_sync:0,ntp_delta:0,event_key:"playing",stop_time:0,duration:0,stall_count_per_100sec:0,stall_time_per_100sec:0},play_error:{code:void 0,info:"",event_key:"play_error"},play_low_decode:{fps:-1,decode_fps:-1,bitrate:-1,url:-1,msg:"",event_key:"play_low_decode"},play_av_unsync:{aDts:-1,vDts:-1,frameLength:-1,currentTime:-1,duration:0,event_key:"play_av_unsync"},play_loadstream:{loadstream_url:"",event_key:"play_loadstream"},play_stream_loaded:{event_key:"play_stream_loaded"},play_metadata_loaded:{metadata_type:"",event_key:"play_metadata_loaded"},stream_exception:{event_key:"stream_exception"},pause:{event_key:"pause"},play_url_change:{event_key:"play_url_change"},source_opened:{event_key:"source_opened"},source_updateend:{event_key:"source_updateend"},play_result:{event_key:"play_result",result:0,threshold:15e3,is_threshold:0},degrade:{event_key:"degrade",payload:{}},rtcstatechange:{event_key:"rtcstatechange"}});var i=this;for(var o in this.log){var a=function(t){gE(e.log[o],t,{get:function(){return null==this["_"+t]?i.commonParams[t]:this["_"+t]},set:function(e){this["_"+t]=e},enumerable:!0})};for(var s in this.commonParams)a(s);r&&gE(this.log[o],"m3u8",{get:function(){if(n.__core__){var e=n.__core__.m3u8Text;return e?dk(e).call(e,0,25e4):""}return""},enumerable:!0}),gE(this.log[o],"volume",{get:function(){if(n&&n.video)return n.volume||0},enumerable:!0}),gE(this.log[o],"access",{get:function(){return cV()},enumerable:!0})}},t.updateExt=function(e){for(var t in this.log)for(var n in e)this.log[t][n]=e[n]},t.reportStartPlay=function(){this.started||(this.core=sV(this.player),this.core&&(this.commonParams.live_sdk_version="2",this.logmanager.core=this.core),this.started=!0,this.log.start_play.start_play_time=this.log.start_play.timestamp=oV(),this.logmanager.push(this.log.start_play,!0))},t.initWindowListener=function(){"pc"===iV.device?window.addEventListener("beforeunload",this.handleUserLeave):"mobile"===iV.device&&(window.addEventListener("beforeunload",this.handleUserLeave),window.addEventListener("pagehide",this.handleUserLeave))},t.removeWindowListener=function(){"pc"===iV.device?window.removeEventListener("beforeunload",this.handleUserLeave):"mobile"===iV.device&&(window.removeEventListener("beforeunload",this.handleUserLeave),window.removeEventListener("pagehide",this.handleUserLeave))},t.initEvt=function(){var e=this,t=this.player;hV.forEach((function(t){e["handleVideo"+t+"Evt"]=function(){e["handleVideo"+t]&&e["handleVideo"+t]()},e.initWindowListener(),e.bindVideoEvt(t,e["handleVideo"+t+"Evt"])})),t.once("error",this.handlePlayerError),t.once("complete",this.handlePlayerComlete),t.once("playing",this.handlePlayerPlaying),t.once("timeupdate",this.handlePlayerPlaying),t.once("destroy",this.destroyFunc),t.on("ready",this.handlePlayerReady),t.on("waiting",this.handlePlayerWaiting),t.on("pause",this.handlePlayerPause),t.on("urlchange",this.handleUrlChange),t.once("sourceopen",this.handleSourceOpen),t.once("bufferappend",this.handleBufferAppend),t.on("core_event",this.handleCoreEvent),t.on("degrade",this.handleDegrade),this.hiJackPlayerStart()},t.handleCoreEvent=function(e){var t=e.eventName,n=e.headers,r=e.pts,i=e.type,o=e.meta,a=e.sei,s=e.decodeFps,l=e.url,u=e.state;switch(t){case"core.ttfb":this.handleTTFB(e);break;case"core.loadresponseheaders":var c=n.get("X-Server-Ip");void 0!==this.player.hlsOps&&(this.log.play_stop.review_ts_count+=1),c&&(this.commonParams.cdn_ip=c);break;case"core.loadretry":this.log.play_stop.retry_count++;break;case"core.keyframe":this.handleKeyframe(r);break;case"core.metadataparsed":this.handleMetaDataLoaded(i,o);break;case"core.sei":this.handleSEIParsed(a);break;case"core.lowdecode":this.commonParams.is_wasm&&(BT(this.log.play_low_decode,e||{}),this.log.play_low_decode.decode_fps=s,this.log.play_low_decode.timestamp=oV(),this.logmanager.push(this.log.play_low_decode,!0),this.commonParams.is_wasm=0);break;case"core.largeavgap":this.handleAVUnsync(e);break;case"core.loadstart":this.handlePlayerLoadStream(e),this.reportStartPlay();break;case"core.loadcomplete":this.handlePlayerLoadedStream(e);break;case"core.streamexception":this.handleStreamException(e);break;case"core.rtcstatechange":this.logmanager.push(BT({url:l,state:u},this.log.rtcstatechange))}},t.handleDegrade=function(e){this.log.degrade.payload=e,this.logmanager.push(this.log.degrade,!0)},t.hiJackPlayerStart=function(){var e=arguments,t=this;this.player._start=this.player.start;var n=this.player;n.start=function(){try{if(n._start)return n._start.apply(n,dk(Array.prototype).call(e,0))}catch(AV){}t.destroyed||t.handlePlayerComlete()}},t.handleTTFB=function(e){var t=e||{},n=t.end,r=t.elapsed;this.log.first_frame.ttfb=r||-1,this.log.first_frame.ttfb_end=n||0},t.handleUrlChange=function(e){this.logmanager.push(BT(this.log.play_url_change,{url:e,timestamp:oV()}))},t.handleKeyframe=function(e){this.log.play_stop.is_firstframe_received=1,this.log.play_stop.firstframe_pts=e},t.handleSourceOpen=function(){this.log.source_opened.timestamp=oV(),this.logmanager.push(this.log.source_opened)},t.handleBufferAppend=function(){this.log.source_updateend.timestamp=oV(),this.logmanager.push(this.log.source_updateend)},t.handlePlayerPause=function(){this.log.pause.timestamp=oV(),this.logmanager.push(this.log.pause,!0),this.stallEnd()},t.handlePlayerError=function(e){this.errorNumber=1;var t=e.errorType,n=e.errorCode;BT(this.log.play_error,QB({timestamp:oV(),code:e.httpCode||n||("parse"===t?31:21),info:e.message,detail:sM(e)},this.videoSize)),this.logmanager.push(this.log.play_error,!0),this.destroyed||(0===this.log.first_frame.first_frame_view&&(BT(this.log.first_frame_failed,{code:n||("parse"===t?31:21),msg:e.message}),void 0!==this.log.first_frame_failed.timestamp&&(this.log.first_frame_failed.timestamp=oV()),this.logmanager.push(this.log.first_frame_failed,!0),this.log.play_result.is_threshold||(this.clearPlayResultTimer(),this.log.play_result.reason=e.message||"first frame failed",this.log.play_result.code=this.log.play_error.code,this.logmanager.push(this.log.play_result,!0))),this.options.error_report_stop?this.handleUserLeave():this.updatePlayStopParams())},t.handlePlayerLoadStream=function(e){var t=e.url,n=e.offersdp,r=e.sessionId;if("string"==typeof t){var i=this.log.play_loadstream;i.timestamp=oV(),i.loadstream_url=t,i.offersdp=n,i.sessionId=r,this.logmanager.push(i)}},t.handlePlayerLoadedStream=function(e){var t=this.log.play_stream_loaded;t.timestamp=oV(),BT(t,e),this.logmanager.push(t)},t.handleSEIParsed=function(e){var t=this.log,n=t.playing,r=t.first_frame;if(100===e.code){var i=function(e){var t,n,r,i,o,a;for(t="",r=e.length,n=0;n<r;)switch((i=e[n++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:t+=String.fromCharCode(i);break;case 12:case 13:o=e[n++],t+=String.fromCharCode((31&i)<<6|63&o);break;case 14:o=e[n++],a=e[n++],t+=String.fromCharCode((15&i)<<12|(63&o)<<6|(63&a)<<0)}return t}(e.content);i=dk(i).call(i,hk(i).call(i,"{"),BL(i).call(i,"}")+1);try{var o=JSON.parse(i),a=this.getPlayerBuffer()+this.ntpTime-o.ts;n.sei_delay=a,n.sei_source=o.source,n.ntp_sync=this.options.ntp?1:0,n.ntp_delta=this.ntp_delta,r.first_sei_delay=a,r.ntp_sync=this.options.ntp?1:0,r.ntp_delta=this.ntp_delta}catch(AV){}}},t.handleMetaDataLoaded=function(e,t){var n;this.log.play_stop.meta=this.log.play_stop.meta||{},e&&(this.log.play_stop.meta[e]=t),this.log.play_metadata_loaded.timestamp=oV(),this.log.play_metadata_loaded.metadata_type=e,t&&(this.log.play_metadata_loaded.meta=t,this.log.play_metadata_loaded.codec=t.codec,"video"===e&&(this.commonParams.codec_type=t.codec,this.commonParams.codec_support=(n=t.codec,"undefined"!=typeof MediaSource?+MediaSource.isTypeSupported("video/mp4; codecs="+n):0))),t||(this.log.play_metadata_loaded.meta=void 0,this.log.play_metadata_loaded.codec=void 0,"video"===e&&(this.commonParams.codec_type="unknown")),this.logmanager.push(this.log.play_metadata_loaded)},t.handleAVUnsync=function(e){BT(this.log.play_av_unsync,e||{}),this.log.play_av_unsync.timestamp=oV(),this.logmanager.push(this.log.play_av_unsync,!1)},t.handleStreamException=function(e){this.logmanager.push(BT({},this.log.stream_exception,e))},t.handlePlayerComlete=function(){this.completed||(this.completed=!0,this.core=sV(this.player),this.core&&(this.commonParams.live_sdk_version="2",this.logmanager.core=this.core),(this.player.config.autoplay||this.player.config.videoInit)&&this.reportStartPlay())},t.getPlayerBuffer=function(){var e=this.player.video.buffered;if(e){var t=Math.max(0,e.length-1);try{var n=KB(1e3*(e.end(t)-this.player.video.currentTime));return this.log.playing.audio_buffer_time=this.log.playing.video_buffer_time=n,n}catch(AV){return 0}}},t.getPlayerDownloadSpeed=function(){if(this.core&&this.core.core&&"function"==typeof this.core.core.speedInfo)return this.core.core.speedInfo().speed},t.getPlayerQuality=function(){var e=this.player.video;if(e.getVideoPlaybackQuality){var t=e.getVideoPlaybackQuality();this.log.playing.drop_count=t.droppedVideoFrames-this.tempDroppedFrameCount,this.log.playing.drop_percent=GB(this.tempTotalFrameCount/t.totalVideoFrames).toFixed(1),this.log.play_stop.drop_percent=GB(t.droppedVideoFrames/t.totalVideoFrames).toFixed(1),this.tempDroppedFrameCount=t.droppedVideoFrames,this.tempTotalFrameCount=t.totalVideoFrames}},t.getRtcStats=function(e,t){try{var n=this,r=n.player.plugins.rts||n.player.plugins.rtm,i=r&&r.pc,o={};return i?Vw.resolve(i.getStats()).then((function(t){return t.forEach((function(t){var r=t.type,i=t.mediaType,a=t.bytesReceived,s=t.packetsReceived,l=t.audioLevel,u=t.concealmentEvents,c=t.remoteTimestamp,h=t.timestamp,f=t.framesDropped,d=t.keyFramesDecoded,p=t.totalDecodeTime,v=t.framesDecoded,g=t.frameHeight,y=t.frameWidth,m=t.framesReceived,_=t.jitterBufferDelay,b=t.jitterBufferEmittedCount,w=t.jitter,k=t.nackCount,E=t.packetsLost,T=t.pliCount,S=t.totalSamplesReceived,C=t.concealedSamples,x=t.currentRoundTripTime,P=t.decoderImplementation,A=t.mimeType,I=t.freezeCount,R=t.totalFreezesDuration,O=t.totalPausesDuration;switch(r){case"inbound-rtp":"audio"===i?(o.audio_bytes_received=a-(n.cacheRtcStats.audioBytesReceived||0),n.cacheRtcStats.audioBytesReceived=a,o.audio_packets_lost=E-(n.cacheRtcStats.audioPacketsLost||0),o.audio_packets_received=s-(n.cacheRtcStats.audioPacketsReceived||0),n.cacheRtcStats.audioPacketsLost=E,n.cacheRtcStats.audioPacketsReceived=s,o.audio_level=l,o.concealment_event=u,o.audio_fraction_lost=o.audio_packets_lost/o.audio_packets_received,void 0!==n.cacheRtcStats.totalSamplesReceived&&(o.audio_rec_sample_rate=(S-n.cacheRtcStats.totalSamplesReceived)/e),n.cacheRtcStats.totalSamplesReceived=S,o.concealed_samples=C,o.total_samples_received=S):(o.frames_dropped=f,o.iframes_decoded=d,o.video_bytes_received=a-(n.cacheRtcStats.videoBytesReceived||0),n.cacheRtcStats.videoBytesReceived=a,o.video_dec_time=p,o.video_decoded_delay_ms=o.video_dec_elapse/v,o.video_frame_height=g,o.video_frame_width=y,void 0!==n.cacheRtcStats.framesDecoded&&(o.video_decode_framerate=(v-n.cacheRtcStats.framesDecoded)/e),n.cacheRtcStats.framesDecoded=v,void 0!==n.cacheRtcStats.framesReceived&&(o.video_recv_framerate=(m-n.cacheRtcStats.framesReceived)/e),void 0!==n.cacheRtcStats.freezeCount&&(o.freeze_count=I-n.cacheRtcStats.freezeCount,o.freeze_duration=R-n.cacheRtcStats.totalFreezesDuration),n.cacheRtcStats.freezeCount=I,n.cacheRtcStats.totalFreezesDuration=R,n.cacheRtcStats.framesReceived=m,o.total_pauses_duration=O,o.video_jb_delay=_/b*1e3,o.video_jitter=1e3*w,o.video_nack_count=k,o.video_packets_lost=E-(n.cacheRtcStats.videoPacketsLost||0),o.video_packets_received=s-(n.cacheRtcStats.videoPacketsReceived||0),o.video_fraction_lost=o.video_packets_lost/o.video_packets_received,n.cacheRtcStats.videoPacketsLost=E,n.cacheRtcStats.videoPacketsReceived=s,o.video_pli_count=T,o.decoder_name=P);break;case"remote-outbound-rtp":o.e2e_delay_ms=h-c;break;case"track":void 0!==y&&(o.video_frame_width=y,o.video_frame_height=g);break;case"candidate-pair":o.rtt=1e3*x;break;case"codec":ck(A).call(A,"video")?o.video_codec=A:o.audio_codec=i}o.total_delay=o.e2e_delay_ms+(o.video_decoded_delay_ms||0)+(o.video_jb_delay||0)})),BT(n.log.playing,{video_download_size:o.video_bytes_received,decode_fps:o.video_decode_framerate,sei_delay:o.total_delay,sei_network_delay:o.e2e_delay_ms,width:o.video_frame_width,height:o.video_frame_height,current_speed:(o.audio_bytes_received+o.video_bytes_received)/e}),o})):Vw.resolve(o)}catch(AV){return Vw.reject(AV)}},t.reportPlaying=function(e){try{var t=this;t.commonParams.is_wasm&&(t.commonParams.fps=t.player.video.fps,t.log.playing.render_fps=t.player.video.fps,t.log.playing.decode_fps=t.player.video.decodeFps),t.log.playing.index++;var n,r=t.log.playing.timestamp;t.inWaitingStart&&t.inWaitingStart<oV()&&(n=Math.min(t.options.playingInterval||t.log.playing.play_time,oV()-Math.max(t.inWaitingStart,r))),t.getPlayerQuality(),t.getPlayerBuffer(),t.options.downloadSpeedInterval||(t.log.playing.current_speed=t.getPlayerDownloadSpeed());var i=oV(),o=i-r,a=(n>200?n:0)+t.log.playing.stall_time,s=(n>200?1:0)+t.log.playing.stall_count;return t.options.aggregationDowngradeStallTime&&(t.log.playing.aggregation_downgrade_stall=(n>t.options.aggregationDowngradeStallTime?n:0)+(t.log.playing.aggregation_downgrade_stall||0)),BT(t.log.playing,QB({timestamp:i,stop_time:i,duration:e?0:o/1e3,stall_time_per_100sec:a/o*100,stall_count_per_100sec:1e3*s/o*100,stall_count:s,stall_time:a,play_current_time:t.player.currentTime},t.videoSize)),Vw.resolve(t.getRtcStats(o,e)).then((function(n){t.logmanager.push(BT({},t.log.playing,QB({play_time:e?0:o},n)),!0),t.log.playing.download_speed_array=[],t.log.playing.stall_time=0,t.log.playing.stall_count=0,t.options.aggregationDowngradeStallTime&&(t.log.playing.aggregation_downgrade_stall=0)}))}catch(AV){return Vw.reject(AV)}},t.handlePlayerPlaying=function(){try{var e=this;if(e.player&&e.player.video.readyState<3)return Vw.resolve();if(e.player.off("playing",e.handlePlayerPlaying),e.player.off("timeupdate",e.handlePlayerPlaying),e.core){var t=(e.core.getStats()||{}).fps;e.commonParams.fps=t}return Vw.resolve(e.reportPlaying(!0)).then((function(){e.clearPlayingInterval();var t=e.options.playingInterval||e.log.playing.play_time;e.intervalId=setInterval((function(){try{var n=function(){if(Math.abs(Date.now()-e.log.playing.timestamp-t)<100||Date.now()-e.log.playing.timestamp>=t){var n=function(){if(e.player.video){var t=function(){if(!e.player.paused)return Vw.resolve(e.reportPlaying()).then((function(){}));e.handleVideopause()}();if(t&&t.then)return t.then((function(){}))}else e.destroyFunc()}();if(n&&n.then)return n.then((function(){}))}}();return Vw.resolve(n&&n.then?n.then((function(){})):void 0)}catch(AV){return Vw.reject(AV)}}),1e3),e.clearStallTimer(),e.handleVideoplaying(),e.started||e.reportStartPlay(),e.saveBuffered()}))}catch(AV){return Vw.reject(AV)}},t.handlePlayerReady=function(){var e=this;this.log.start_play.start_play_time=oV(),this.player.config.autoplay&&this.reportStartPlay(),this.options.playResultThreshold&&(this.log.play_result.threshold=this.options.playResultThreshold),this.clearPlayResultTimer(),this.log.play_result.start=oV(),this.playResultTimer=setTimeout((function(){e.log.play_result.timestamp>=e.log.play_result.start||(e.log.play_result.is_threshold=1,e.logmanager.push(e.log.play_result,!0))}),this.log.play_result.threshold)},t.handlePlayerWaiting=function(){var e=this;!this.inWaitingStart&&this.log.first_frame.timestamp&&(this.log.stall.timestamp=this.inWaitingStart=this.log.stall.stall_start=oV(),this.log.stall_start.ready_state=this.log.stall.ready_state=this.player.video.readyState,this.log.stall_start.timestamp=oV(),this.log.stall_start.stall_start_time=oV(),this.log.stall_start.video_buffer_time=this.player.video.readyState,this.log.stall_start.play_current_time=this.player.currentTime,this.log.stall.play_current_time=this.player.currentTime,this.log.stall_start.buffered=this.getBuffered(),this.log.stall.buffered=this.log.stall_start.buffered,this.log.stall.current_speed=this.getPlayerDownloadSpeed(),BT(this.log.stall_start,this.videoSize),this.clearStallTimer(),this.stallStartTimer=setTimeout((function(){e.logmanager.push(e.log.stall_start,!0)}),205))},t.handleVideoseeking=function(){this.player&&this.player.video&&this.inWaitingStart&&this.player.video.duration<1/0&&(this.inWaitingStart=0,this.clearStallTimer())},t.handleVideoseeked=function(){this.player&&this.player.video&&this.inWaitingStart&&this.player.video.duration<1/0&&(this.inWaitingStart=0,this.log.play_stop.stall_count>0&&this.log.play_stop.stall_count--)},t.handleVideoplay=function(){this.reportStartPlay(),this.removeVideoEvt("play",this.handleVideoplay)},t.handleVideotimeupdate=function(){Math.abs(this.player.currentTime-this.log.stall_start.play_current_time)>.2&&this.stallEnd(),this.saveBuffered()},t.handleVideocanplay=function(){this.stallEnd(),this.reportFirstframe()},t.handleVideoplaying=function(){this.stallEnd(),this.log.play_stop.is_stream_received=1;var e=oV();this.reportStartPlay(),this.log.first_frame.start=e},t.stallEnd=function(){this.clearStallTimer();var e=oV();if(this.inWaitingStart){var t=e-this.inWaitingStart;t>200&&(this.log.play_stop.stall_count++,this.log.play_stop.stall_time+=e-this.inWaitingStart,this.log.playing.stall_time+=e-Math.max(this.inWaitingStart,this.log.playing.timestamp),this.log.stall.stall_end=e,this.log.stall.timestamp=oV(),this.log.playing.stall_count++,BT(this.log.stall,QB({},this.videoSize)),this.logmanager.push(this.log.stall,!0)),this.options.aggregationDowngradeStallTime&&t>this.options.aggregationDowngradeStallTime&&(this.log.playing.aggregation_downgrade_stall=(this.log.playing.aggregation_downgrade_stall||0)+(e-Math.max(this.inWaitingStart,this.log.playing.timestamp)))}this.inWaitingStart=0},t.handleVideoloadeddata=function(){this.reportFirstframe()},t.handleVideoratechange=function(){this.log.playing.speed_switch_count++,this.log.playing.speed_switch_info=this.player.video.playbackRate},t.handleVideoended=function(){this.clearPlayingInterval(),this.clearStallTimer(),BT(this.log.playing,QB({timestamp:oV(),is_last:1},this.videoSize)),this.logmanager.push(this.log.playing)},t.handleVideoerror=function(){var e=this;this.errorNumber=1;var t=this.player.video.error;BT(this.log.play_error,QB(QB({timestamp:oV()},this.videoSize),t&&{code:t.code,info:t.message})),this.logmanager.push(this.log.play_error,!0),0!==e.log.first_frame.first_frame_view||e.log.first_frame_failed.code||t&&(e.log.first_frame_failed.code=t.code,e.log.first_frame_failed.msg=lV[t.code],void 0!==e.log.first_frame_failed.timestamp&&(e.log.first_frame_failed.timestamp=oV()),e.logmanager.push(e.log.first_frame_failed,!0),e.log.play_result.is_threshold||(e.log.play_result.reason=lV[t.code]||"first frame failed",e.log.play_result.code=t.code,e.clearPlayResultTimer(),e.logmanager.push(e.log.play_result,!0))),e.options.error_report_stop?e.handleUserLeave():e.updatePlayStopParams()},t.handleVideopause=function(){this.stallEnd(),this.clearPlayingInterval(),this.clearStallTimer(),clearTimeout(this.collectDownloadSpeedTimer),this.player.once("playing",this.handlePlayerPlaying),this.player.once("timeupdate",this.handlePlayerPlaying)},t.destroyFunc=function(e){var t=this;this.destroyed||(e||this.handleUserLeave(),this.destroyed=!0,"pc"===iV.device?window.removeEventListener("beforeunload",this.handleUserLeave):"mobile"===iV.device&&window.removeEventListener("pagehide",this.handleUserLeave),hV.forEach((function(e){t.removeVideoEvt(e,t["handleVideo"+e+"Evt"])})),this.player&&(this.player._start&&(this.player.start=this.player._start,delete this.player._start),this.player.off("destroy",this.destroyFunc),this.player.off("complete",this.handlePlayerComlete),this.player.off("playing",this.handlePlayerPlaying),this.player.off("timeupdate",this.handlePlayerPlaying),this.player.off("ready",this.handlePlayerReady),this.player.off("waiting",this.handlePlayerWaiting),this.player.off("largeavgap",this.handleAVUnsync),this.player.off("error",this.handlePlayerError),this.player.off("SEI_PARSED",this.handleSEIParsed),this.player.off("pause",this.handlePlayerPause),this.player.off("ttfb",this.handleTTFB),this.player.off("urlchange",this.handleUrlChange),this.player.off("isKeyframe",this.handleKeyframe),this.player.off("sourceopen",this.handleSourceOpen),this.player.off("bufferappend",this.handleBufferAppend),this.player.off("core_event",this.handleCoreEvent),this.player.off("degrade",this.handleDegrade),this.clearPlayingInterval(),this.clearStallTimer(),this.clearPlayResultTimer(),clearTimeout(this.collectDownloadSpeedTimer),this._player=null))},t.handleUserLeave=function(){!this.destroyed&&this.started&&(this.destroyed=!0,this.updatePlayStopParams(),this.reportPlayStop(),this.clearPlayingInterval(),this.clearStallTimer(),clearTimeout(this.collectDownloadSpeedTimer),this.removeWindowListener())},t.saveBuffered=function(){try{var e=this.getBuffered();e&&e.length&&(this.log.play_stop.buffered=e)}catch(PH){}},t.getBuffered=function(){try{for(var e=[],t=this.player.video,n=0;n<t.buffered.length;n++)e.push([t.buffered.start(n),t.buffered.end(n)]);return e}catch(PH){}},t.reportPlayStop=function(){this.log.play_stop.play_current_time=this.player.currentTime||this.log.playing.play_current_time,this.saveBuffered(),this.logmanager.push(this.log.play_stop,!0)},t.reportFirstframe=function(){if(!this.firstFrameViewed){if(this.core){var e=(this.core.getStats()||{}).fps;this.commonParams.fps=e}this.firstFrameViewed=!0;var t=oV();BT(this.log.first_frame,QB({first_frame_view:t-this.log.start_play.start_play_time||t,timestamp:t},this.videoSize)),this.log.first_frame.ttfb_end&&(this.log.first_frame.ttdb=t-this.log.first_frame.ttfb_end),this.logmanager.push(this.log.first_frame,!0),this.clearPlayResultTimer(),this.log.play_result.result=1,this.logmanager.push(this.log.play_result,!0),this.log.play_stop.is_stream_received=1}},t.updatePlayStopParams=function(){var e=function(e){var t,n=Hw(t=performance.getEntriesByType("resource")).call(t,(function(t){return t.name===e}));if(n.length){var r=n[0],i=n[n.length-1];return{dns_parse_time:KB(r.domainLookupEnd-r.domainLookupStart),redirect:KB(r.redirectEnd-r.redirectStart),ttfb:KB(i.responseStart-i.requestStart)}}return{dns_parse_time:-1,redirect:-1,ttfb:-1}}(this._url),t=oV();this.playedTime=t-this.log.start_play.start_play_time;var n=this.log.play_stop;BT(n,QB({},0===n.stall_count&&{stall_time:0})),this.inWaitingStart&&t-this.inWaitingStart>200&&(n.stall_time+=t-this.inWaitingStart),this.inWaitingStart=0,BT(n,QB({stall_time_per_100sec:n.stall_time/this.playedTime*100,stall_count_per_100sec:1e3*n.stall_count/this.playedTime*100},e)),0===this.log.play_stop.stall_count_per_100sec&&(this.log.play_stop.stall_time_per_100sec=0),this.log.play_stop.stall_time_per_100sec>100||this.log.play_stop.stall_time_per_100sec<0||(this.log.playing.index?(BT(this.log.playing,QB({timestamp:oV(),is_last:1},this.videoSize)),this.logmanager.push(this.log.playing,!0)):(this.log.play_stop.error_code=this.log.play_error.code,this.log.play_stop.error_info=this.log.play_error.info),t=oV(),BT(n,QB({timestamp:t,stop_time:t,duration:this.playedTime/1e3,play_time:this.playedTime/1e3},this.videoSize)),0===this.errorNumber&&(n.play_time_on_no_frame=n.stop_time-this.log.start_play.start_play_time))},t.clearPlayingInterval=function(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)},t.clearStallTimer=function(){clearTimeout(this.stallStartTimer)},t.clearPlayResultTimer=function(){clearTimeout(this.playResultTimer)},t.destroy=function(e){this.destroyFunc(!e)},t.bindVideoEvt=function(e,t){this.player&&this.player.video.addEventListener(e,t,!1)},t.removeVideoEvt=function(e,t){this.player&&this.player.video&&this.player.video.removeEventListener(e,t,!1)},t.send=function(e,t){this.logmanager.send(e,t)},e}(),pV=On,vV=n_,gV=l,yV=re,mV=m_.onFreeze,_V=Object.freeze;pV({target:"Object",stat:!0,forced:gV((function(){_V(1)})),sham:!vV},{freeze:function(e){return _V&&yV(e)?_V(mV(e)):e}});var bV,wV=o(ie.Object.freeze),kV=function(){return kV=BT||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},kV.apply(this,arguments)};function EV(e,t){var n="function"==typeof yE&&oT(e);if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(s){i={error:s}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function TV(){for(var e=[],t=0;t<arguments.length;t++)e=Fm(e).call(e,EV(arguments[t]));return e}var SV,CV,xV,PV,AV,IV=function(){function e(){this._hooks={},this._cache=[],this._hooksCache={}}return e.prototype.on=function(e,t){e&&t&&"function"==typeof t&&(this._hooks[e]||(this._hooks[e]=[]),this._hooks[e].push(t))},e.prototype.once=function(e,t){var n=this;e&&t&&"function"==typeof t&&this.on(e,(function r(i){t(i),n.off(e,r)}))},e.prototype.off=function(e,t){if(e&&this._hooks[e]&&this._hooks[e].length)if(t){var n,r,i=hk(n=this._hooks[e]).call(n,t);-1!==i&&Mk(r=this._hooks[e]).call(r,i,1)}else this._hooks[e]=[]},e.prototype.emit=function(e,t,n){if(n){var r;if(!e)return;-1!==hk(r=this._cache).call(r,n)?this._emit(e,t):(this._hooksCache.hasOwnProperty(n)||(this._hooksCache[n]={}),this._hooksCache[n].hasOwnProperty(e)||(this._hooksCache[n][e]=[]),this._hooksCache[n][e].push(t))}else this._emit(e,t)},e.prototype._emit=function(e,t){e&&this._hooks[e]&&this._hooks[e].length&&TV(this._hooks[e]).forEach((function(e){try{e(t)}catch(n){}}))},e.prototype.set=function(e){var t;e&&-1===hk(t=this._cache).call(t,e)&&this._cache.push(e)},e}(),RV=function(e){return null!=e&&"[object Object]"==Object.prototype.toString.call(e)},OV=(SV=+Date.now()+Number(dk(bV=""+Math.random()).call(bV,2,8)),function(){return SV+=1}),DV=function(e){var t=document.createElement("a");return t.href=e,t},MV=function(){function e(e){this.appid=e,this.userAgent=window.navigator.userAgent,this.appVersion=window.navigator.appVersion}return e.prototype.init=function(){var e=window.navigator.userAgent,t=window.navigator.language,n=document.referrer,r=n?DV(n).hostname:"";!function(e){var t={};try{var n=DV(e).search;(n=dk(n).call(n,1)).split("&").forEach((function(e){var n,r,i=e.split("=");i.length&&(n=i[0],r=i[1]);try{t[n]=decodeURIComponent(void 0===r?"":r)}catch(o){t[n]=r}}))}catch(r){}}(window.location.href);var i=/Mobile|htc|mini|Android|iP(ad|od|hone)/.test(this.appVersion)?"wap":"web",o=this.browser(),a=this.os();return{browser:o.browser,browser_version:o.browser_version,platform:i,os_name:a.os_name,os_version:a.os_version,userAgent:e,screen_width:window.screen&&window.screen.width,screen_height:window.screen&&window.screen.height,device_model:this.getDeviceModel(a.os_name),language:t,referrer:n,referrer_host:r}},e.prototype.browser=function(){var e,t,n="",r=""+_S(this.appVersion),i=this.userAgent;return-1!==hk(i).call(i,"Edge")||-1!==hk(i).call(i,"Edg")?(n="Microsoft Edge",-1!==hk(i).call(i,"Edge")?(e=hk(i).call(i,"Edge"),r=i.substring(e+5)):(e=hk(i).call(i,"Edg"),r=i.substring(e+4))):-1!==(e=hk(i).call(i,"MSIE"))?(n="Microsoft Internet Explorer",r=i.substring(e+5)):-1!==(e=hk(i).call(i,"Lark"))?(n="Lark",r=i.substring(e+5,e+11)):-1!==(e=hk(i).call(i,"MetaSr"))?(n="sougoubrowser",r=i.substring(e+7,e+10)):-1!==hk(i).call(i,"MQQBrowser")||-1!==hk(i).call(i,"QQBrowser")?(n="qqbrowser",-1!==hk(i).call(i,"MQQBrowser")?(e=hk(i).call(i,"MQQBrowser"),r=i.substring(e+11,e+15)):-1!==hk(i).call(i,"QQBrowser")&&(e=hk(i).call(i,"QQBrowser"),r=i.substring(e+10,e+17))):-1!==hk(i).call(i,"Chrome")?-1!==(e=hk(i).call(i,"MicroMessenger"))?(n="weixin",r=i.substring(e+15,e+20)):-1!==(e=hk(i).call(i,"360"))?(n="360browser",r=i.substring(hk(i).call(i,"Chrome")+7)):-1!==hk(i).call(i,"baidubrowser")||-1!==hk(i).call(i,"BIDUBrowser")?(-1!==hk(i).call(i,"baidubrowser")?(e=hk(i).call(i,"baidubrowser"),r=i.substring(e+13,e+16)):-1!==hk(i).call(i,"BIDUBrowser")&&(e=hk(i).call(i,"BIDUBrowser"),r=i.substring(e+12,e+15)),n="baidubrowser"):-1!==(e=hk(i).call(i,"xiaomi"))?-1!==hk(i).call(i,"openlanguagexiaomi")?(n="openlanguage xiaomi",r=i.substring(e+7,e+13)):(n="xiaomi",r=i.substring(e-7,e-1)):-1!==(e=hk(i).call(i,"TTWebView"))?(n="TTWebView",r=i.substring(e+10,e+23)):(-1!==(e=hk(i).call(i,"Chrome"))||-1!==(e=hk(i).call(i,"Chrome")))&&(n="Chrome",r=i.substring(e+7)):-1!==hk(i).call(i,"Safari")?-1!==(e=hk(i).call(i,"QQ"))?(n="qqbrowser",r=i.substring(e+10,e+16)):-1!==(e=hk(i).call(i,"Safari"))&&(n="Safari",r=i.substring(e+7),-1!==(e=hk(i).call(i,"Version"))&&(r=i.substring(e+8))):-1!==(e=hk(i).call(i,"Firefox"))?(n="Firefox",r=i.substring(e+8)):-1!==(e=hk(i).call(i,"MicroMessenger"))?(n="weixin",r=i.substring(e+15,e+20)):-1!==(e=hk(i).call(i,"QQ"))?(n="qqbrowser",r=i.substring(e+3,e+8)):-1!==(e=hk(i).call(i,"Opera"))&&(n="Opera",r=i.substring(e+6),-1!==(e=hk(i).call(i,"Version"))&&(r=i.substring(e+8))),-1!==(t=hk(r).call(r,";"))&&(r=r.substring(0,t)),-1!==(t=hk(r).call(r," "))&&(r=r.substring(0,t)),-1!==(t=hk(r).call(r,")"))&&(r=r.substring(0,t)),{browser:n,browser_version:r}},e.prototype.os=function(){for(var e="",t="",n=[{s:"Windows 10",r:/(Windows 10.0|Windows NT 10.0|Windows NT 10.1)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Android",r:/Android/},{s:"Sun OS",r:/SunOS/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/}],r=0;r<n.length;r++){var i=n[r];if(i.r.test(this.userAgent)){"Mac OS X"===(e=i.s)&&this.isNewIpad()&&(e="iOS");break}}var o,a,s=function(e,t){var n=e.exec(t);return n&&n[1]?n[1]:""},l=function(e,t){var n=RegExp("(?:^|[^A-Z0-9-_]|[^A-Z0-9-]_|sprd-)(?:"+e+")","i").exec(t);return n?dk(n).call(n,1)[0]:""};switch(/Windows/.test(e)&&(t=s(/Windows (.*)/,e),e="windows"),e){case"Mac OS X":t=l("Mac[ +]OS[ +]X(?:[ /](?:Version )?(\\d+(?:[_\\.]\\d+)+))?",this.userAgent),e="mac";break;case"Android":(a=s(/Android ([\.\_\d]+)/,o=this.userAgent))||(a=s(/Android\/([\.\_\d]+)/,o)),t=a,e="android";break;case"iOS":t=this.isNewIpad()?l("Mac[ +]OS[ +]X(?:[ /](?:Version )?(\\d+(?:[_\\.]\\d+)+))?",this.userAgent):(t=/OS (\d+)_(\d+)_?(\d+)?/.exec(this.appVersion))?t[1]+"."+t[2]+"."+(0|t[3]):"",e="ios"}return{os_name:e,os_version:t}},e.prototype.getDeviceModel=function(e){var t="";try{if("android"===e)navigator.userAgent.split(";").forEach((function(e){hk(e).call(e,"Build/")>-1&&(t=dk(e).call(e,0,hk(e).call(e,"Build/")))}));else if("ios"===e||"mac"===e||"windows"===e)if(this.isNewIpad())t="iPad";else{var n=navigator.userAgent.replace("Mozilla/5.0 (",""),r=hk(n).call(n,";");t=dk(n).call(n,0,r)}}catch(i){return PS(t).call(t)}return PS(t).call(t)},e.prototype.isNewIpad=function(){return void 0!==this.userAgent&&"MacIntel"===navigator.platform&&"number"==typeof navigator.maxTouchPoints&&navigator.maxTouchPoints>1},e}(),LV=(CV=function(e,t){e.exports=function(){function e(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}return function t(n,r){function i(t,i,o){if("undefined"!=typeof document){"number"==typeof(o=e({},r,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var s in o)o[s]&&(a+="; "+s,!0!==o[s]&&(a+="="+o[s].split(";")[0]));return document.cookie=t+"="+n.write(i,t)+a}}return Object.create({set:i,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],r={},i=0;i<t.length;i++){var o=t[i].split("="),a=dk(o).call(o,1).join("=");try{var s=decodeURIComponent(o[0]);if(r[s]=n.read(a,s),e===s)break}catch(l){}}return e?r[e]:r}},remove:function(t,n){i(t,"",e({},n,{expires:-1}))},withAttributes:function(n){return t(this.converter,e({},this.attributes,n))},withConverter:function(n){return t(e({},this.converter,n),this.attributes)}},{attributes:{value:wV(r)},converter:{value:wV(n)}})}({read:function(e){return'"'===e[0]&&(e=dk(e).call(e,1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})}()},CV(xV={exports:{}}),xV.exports),NV=LV,UV=function(){function e(){this.cache={}}return e.prototype.setItem=function(e,t){this.cache[e]=t},e.prototype.getItem=function(e){return this.cache[e]},e.prototype.removeItem=function(e){this.cache[e]=void 0},e.prototype.getCookie=function(e){this.getItem(e)},e.prototype.setCookie=function(e,t){this.setItem(e,t)},e}(),FV={getItem:function(e){try{var t=localStorage.getItem(e),n=t;try{t&&"string"==typeof t&&(n=JSON.parse(t))}catch(r){}return n||{}}catch(r){}return{}},setItem:function(e,t){try{var n="string"==typeof t?t:sM(t);localStorage.setItem(e,n)}catch(r){}},removeItem:function(e){try{localStorage.removeItem(e)}catch(t){}},getCookie:function(e,t){try{return NV.get(e,{domain:t||document.domain})}catch(n){return""}},setCookie:function(e,t,n,r){try{var i=r||document.domain,o=+new Date+n;NV.set(e,t,{expires:new Date(o),path:"/",domain:i})}catch(a){}},isSupportLS:function(){try{return localStorage.setItem("_ranger-test-key","hi"),localStorage.getItem("_ranger-test-key"),localStorage.removeItem("_ranger-test-key"),!0}catch(AV){return!1}}()},BV={getItem:function(e){try{var t=sessionStorage.getItem(e),n=t;try{t&&"string"==typeof t&&(n=JSON.parse(t))}catch(r){}return n||{}}catch(r){}return{}},setItem:function(e,t){try{var n="string"==typeof t?t:sM(t);sessionStorage.setItem(e,n)}catch(r){}},removeItem:function(e){try{sessionStorage.removeItem(e)}catch(t){}},getCookie:function(e){this.getItem(e)},setCookie:function(e,t){this.setItem(e,t)},isSupportSession:function(){try{return sessionStorage.setItem("_ranger-test-key","hi"),sessionStorage.getItem("_ranger-test-key"),sessionStorage.removeItem("_ranger-test-key"),!0}catch(AV){return!1}}()},VV=function(){function e(e,t){this._storage=t&&"session"===t?BV:!e&&FV.isSupportLS?FV:new UV}return e.prototype.getItem=function(e){return this._storage.getItem(e)},e.prototype.setItem=function(e,t){this._storage.setItem(e,t)},e.prototype.getCookie=function(e,t){return this._storage.getCookie(e,t)},e.prototype.setCookie=function(e,t,n,r){this._storage.setCookie(e,t,n,r)},e.prototype.removeItem=function(e){this._storage.removeItem(e)},e}(),HV={cn:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z4az24z1mz1jz1az1cz18z1nz1nz1jz1mz1ez4az1az1mz1k",va:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z4az1gz22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k",sg:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z4az22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k"},jV=void 0,zV=(new Date).getTimezoneOffset(),KV=rE(""+-zV/60,10),WV=60*zV,GV=function(){function e(e,t){this.is_first_time=!0,this.initConfig=t,this.collect=e;var n=new MV(t.app_id).init(),r="__tea_cache_first_"+t.app_id;this.configKey="__tea_cache_config_"+t.app_id,this.sessionStorage=new VV(!1,"session"),this.localStorage=new VV(!1,"local");var i=this.localStorage.getItem(r);i&&1==i?this.is_first_time=!1:(this.is_first_time=!0,this.localStorage.setItem(r,"1")),this.envInfo={user:{user_unique_id:jV,user_type:jV,user_id:jV,user_is_auth:jV,user_is_login:jV,device_id:jV,web_id:jV,user_unique_id_type:jV},header:{app_id:jV,app_name:jV,app_install_id:jV,install_id:jV,app_package:jV,app_channel:jV,app_version:jV,ab_version:jV,os_name:n.os_name,os_version:n.os_version,device_model:n.device_model,ab_client:jV,traffic_type:jV,client_ip:jV,device_brand:jV,os_api:jV,access:jV,language:n.language,region:jV,app_language:jV,app_region:jV,creative_id:jV,ad_id:jV,campaign_id:jV,log_type:jV,rnd:jV,platform:n.platform,sdk_version:"0.0.3-lite",sdk_lib:"js",province:jV,city:jV,timezone:KV,tz_offset:WV,tz_name:jV,sim_region:jV,carrier:jV,resolution:n.screen_width+"x"+n.screen_height,browser:n.browser,browser_version:n.browser_version,referrer:n.referrer,referrer_host:n.referrer_host,width:n.screen_width,height:n.screen_height,screen_width:n.screen_width,screen_height:n.screen_height,utm_term:jV,utm_content:jV,utm_source:jV,utm_medium:jV,utm_campaign:jV,custom:{},wechat_unionid:jV,wechat_openid:jV}},this.evtParams={},this.reportErrorCallback=function(){},this.initDomain()}return e.prototype.initDomain=function(){var e=this.initConfig.channel_domain;if(e)this.domain=e;else{var t=this.initConfig.channel;this.domain=function(e){return function(e,t,n){if("string"==typeof e&&"number"==typeof n){var r,i=[];n=n<=25?n:n%25;var o=String.fromCharCode(n+97);r=e.split(o);for(var a=0;a<r.length;a++){var s=rE(r[a],n);s=1*s^64;var l=String.fromCharCode(s);i.push(l)}return i.join("")}}(e,0,25)}(HV[t])}},e.prototype.setDomain=function(e){this.domain=e},e.prototype.getDomain=function(){return this.domain},e.prototype.getUrl=function(e){var t="";switch(e){case"event":t=this.initConfig.report_url||"/list";break;case"webid":t="/webid"}return""+this.getDomain()+t},e.prototype.set=function(e){var t=this;fk(e).forEach((function(n){var r;"evtParams"===n?t.evtParams=kV({},t.evtParams||{},e.evtParams||{}):"_staging_flag"===n?t.evtParams=kV({},t.evtParams||{},{_staging_flag:e._staging_flag}):"reportErrorCallback"===n&&"function"==typeof e[n]?t.reportErrorCallback=e[n]:Object.hasOwnProperty.call(t.envInfo.user,n)?hk(r=["user_id","web_id","user_unique_id"]).call(r,n)>-1&&(t.envInfo.user[n]=e[n]?String(e[n]):e[n]):Object.hasOwnProperty.call(t.envInfo.header,n)?t.envInfo.header[n]=e[n]:t.envInfo.header.custom[n]=e[n]}))},e.prototype.get=function(e){try{return e?"evtParams"===e?this.evtParams:"reportErrorCallback"===e?this[e]:Object.hasOwnProperty.call(this.envInfo.user,e)?this.envInfo.user[e]:Object.hasOwnProperty.call(this.envInfo.header,e)?this.envInfo.header[e]:JSON.parse(sM(this.envInfo[e])):JSON.parse(sM(this.envInfo))}catch(t){console.log("get config stringify error ")}},e}();(AV=PV||(PV={})).Init="init",AV.Config="config",AV.Start="start",AV.Ready="ready",AV.TokenComplete="token-complete",AV.TokenStorage="token-storage",AV.TokenFetch="token-fetch",AV.TokenError="token-error",AV.ConfigUuid="config-uuid",AV.TokenChange="token-change",AV.TokenReset="token-reset",AV.SessionReset="session-reset",AV.SessionResetTime="session-reset-time",AV.Event="event",AV.Events="events",AV.EventNow="event-now",AV.CleanEvents="clean-events",AV.BeconEvent="becon-event";var qV=PV;function YV(e,t,n,r,i,o,a,s,l){try{var u=new XMLHttpRequest,c=s||"POST";u.open(c,""+e,!0),u.setRequestHeader("Content-Type","application/json; charset=utf-8"),n&&(u.timeout=n,u.ontimeout=function(){o&&o(t,5005)}),u.onload=function(){if(i){var e=null;if(u.responseText){try{e=JSON.parse(u.responseText)}catch(n){e={}}i(e,t)}}},u.onerror=function(){u.abort(),o&&o(t,500)},l?u.send(t):u.send(sM(t))}catch(h){}}var XV,JV=function(){function e(){this.eventLimit=50,this.eventCache=[],this.beconEventCache=[]}return e.prototype.apply=function(e,t){var n=this;this.collect=e,this.config=t,this.configManager=e.configManager,this.cacheStorgae=new VV(!0),this.localStorage=new VV(!1),this.maxReport=t.max_report||20,this.reportTime=t.reportTime||30,this.timeout=t.timeout||1e5,this.reportUrl=this.configManager.getUrl("event"),this.eventKey="__tea_cache_events_"+this.configManager.get("app_id"),this.beconKey="__tea_cache_events_becon_"+this.configManager.get("app_id"),this.collect.on(qV.Ready,(function(){n.reportAll(!1)})),this.collect.on(qV.Event,(function(e){n.event(e)})),this.collect.on(qV.BeconEvent,(function(e){n.beconEvent(e)})),this.linster()},e.prototype.linster=function(){var e,t=this;window.addEventListener("unload",(function(){t.reportAll(!0)}),!1),e=function(){t.reportAll(!0)},navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)?window.addEventListener("pagehide",e,!1):window.addEventListener("beforeunload",e,!1),document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&t.reportAll(!0)}),!1)},e.prototype.reportAll=function(e){this.report(e),this.reportBecon()},e.prototype.event=function(e){var t=this;try{var n=TV(e,this.cacheStorgae.getItem(this.eventKey)||[]);if(this.cacheStorgae.setItem(this.eventKey,n),this.reportTimeout&&clearTimeout(this.reportTimeout),n.length>=this.maxReport)this.report(!1);else{var r=this.reportTime;this.reportTimeout=setTimeout((function(){t.report(!1),t.reportTimeout=null}),r)}}catch(i){}},e.prototype.beconEvent=function(e){var t=TV(e,this.cacheStorgae.getItem(this.beconKey)||[]);this.cacheStorgae.setItem(this.beconKey,t),this.collect.destroyInstance||this.collect.tokenManager.getReady()&&this.collect.sdkReady&&(this.cacheStorgae.removeItem(this.beconKey),this.send(this.split(this.merge(t)),!0))},e.prototype.reportBecon=function(){var e=this.cacheStorgae.getItem(this.beconKey)||[];e&&e.length&&(this.cacheStorgae.removeItem(this.beconKey),this.send(this.split(this.merge(e)),!0))},e.prototype.report=function(e){if(!this.collect.destroyInstance&&this.collect.tokenManager.getReady()&&this.collect.sdkReady){var t=this.cacheStorgae.getItem(this.eventKey)||[];t.length&&(this.cacheStorgae.removeItem(this.eventKey),this.sliceEvent(t,e))}},e.prototype.sliceEvent=function(e,t){if(e.length>this.eventLimit)for(var n=0;n<e.length;n+=this.eventLimit){var r;r=dk(e).call(e,n,n+this.eventLimit);var i=this.split(this.merge(r));this.send(i,t)}else i=this.split(this.merge(e)),this.send(i,t)},e.prototype.merge=function(e){var t=this,n=this.configManager.get(),r=n.header,i=n.user;r.custom=sM(r.custom);var o=this.configManager.get("evtParams"),a=Bw(e).call(e,(function(e){try{return fk(o).length&&(e.params=kV({},o,e.params)),e.session_id=t.collect.sessionManager.getSessionId(),e.params=sM(e.params),e}catch(AV){return e}})),s=[];if(!fk(i).length)return console.warn("user info error，cant report"),s;var l=JSON.parse(sM({events:a,user:i,header:r}));return l.local_time=Math.floor(Date.now()/1e3),l.verbose=1,s.push(l),s},e.prototype.split=function(e){return Bw(e).call(e,(function(e){var t=[];return t.push(e),t}))},e.prototype.send=function(e,t){var n=this;e.length&&e.forEach((function(e){try{if(!e.length)return;r=n.reportUrl,i=e,o=n.timeout,a=function(e,t){},s=function(e,t){n.configManager.get("reportErrorCallback")(e,t)},!1,t&&window.navigator&&window.navigator.sendBeacon?window.navigator.sendBeacon(r,sM(i))||s(r,i):YV(r,i,o,0,a,s,0,"",false)}catch(l){console.warn("something error, "+sM(l.stack))}var r,i,o,a,s}))},e}(),QV=function e(t){return t?(t^16*Math.random()>>t/4).toString(10):([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,e)},$V=function(){var e;return dk(e=QV().replace(/-/g,"")).call(e,0,19)},ZV=function(){function e(){this.cacheToken={}}return e.prototype.apply=function(e,t){var n=this;this.collect=e,this.config=t,this.configManager=this.collect.configManager,this.storage=new VV(!1),this.tokenKey="__tea_cache_tokens_"+t.app_id,this.collect.on(qV.ConfigUuid,(function(e){n.setUuid(e)})),this.checkStorage()},e.prototype.checkStorage=function(){this.cacheToken=this.storage.getItem(this.tokenKey)||{},this.check()},e.prototype.check=function(){this.cacheToken&&this.cacheToken.web_id?this.complete(this.cacheToken):this.remoteWebid()},e.prototype.remoteWebid=function(){var e=this,t=this.configManager.getUrl("webid"),n={app_key:this.config.app_key,app_id:this.config.app_id,url:location.href,user_agent:window.navigator.userAgent,referer:document.referrer,user_unique_id:""},r=$V();YV(t,n,3e5,0,(function(t){var n;t&&0===t.e?n=t.web_id:(n=r,e.collect.configManager.set({localWebId:r}),e.collect.logger.warn("appid: "+e.config.app_id+" get webid error, use local webid~")),e.complete({web_id:e.configManager.get("web_id")||n,user_unique_id:e.configManager.get("user_unique_id")||n})}),(function(){e.complete({web_id:e.configManager.get("web_id")||r,user_unique_id:e.configManager.get("user_unique_id")||r}),e.collect.configManager.set({localWebId:r}),e.collect.logger.warn("appid: "+e.config.app_id+", get webid error, use local webid~")}))},e.prototype.complete=function(e){var t=e.web_id,n=e.user_unique_id;t||n?(e.timestamp=Date.now(),this.collect.configManager.set({web_id:t,user_unique_id:n}),this.setStorage(e),this.tokenReady=!0,this.collect.emit(qV.TokenComplete)):console.warn("token error")},e.prototype.setUuid=function(e){var t;if(e&&-1===hk(t=["null","undefined","Null","None"]).call(t,e)){var n=String(e),r=this.configManager.get("user_unique_id"),i=this.cacheToken&&this.cacheToken.user_unique_id;if(n===r&&n===i)return;this.configManager.set({user_unique_id:n}),this.cacheToken||(this.cacheToken={}),this.cacheToken.user_unique_id=n,this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken)}else this.clearUuid()},e.prototype.clearUuid=function(){this.config.enable_ttwebid||this.configManager.get("web_id")&&(this.configManager.set({user_unique_id:this.configManager.get("web_id")}),this.cacheToken&&this.cacheToken.web_id&&(this.cacheToken.user_unique_id=this.cacheToken.web_id,this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken)),this.collect.emit(qV.TokenReset))},e.prototype.setStorage=function(e){e._type_="default",this.storage.setItem(this.tokenKey,e),this.cacheToken=e},e.prototype.getReady=function(){return this.tokenReady},e}(),eH=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},tH=function(){function e(){}return e.prototype.apply=function(e,t){var n=this;this.collect=e,this.storage=new VV(!1,"session"),this.sessionKey="__tea_session_id_"+t.app_id,this.expireTime=t.expireTime||18e5,this.disableSession=t.disable_session,this.disableSession||(this.setSessionId(),this.collect.on(qV.SessionReset,(function(){n.resetSessionId()})),this.collect.on(qV.SessionResetTime,(function(){n.updateSessionIdTime()})))},e.prototype.updateSessionIdTime=function(){var e=this.storage.getItem(this.sessionKey);if(e&&e.sessionId){var t=e.timestamp;Date.now()-t>this.expireTime?e={sessionId:eH(),timestamp:Date.now()}:e.timestamp=Date.now(),this.storage.setItem(this.sessionKey,e),this.resetExpTime()}},e.prototype.setSessionId=function(){var e=this,t=this.storage.getItem(this.sessionKey);t&&t.sessionId?t.timestamp=Date.now():t={sessionId:eH(),timestamp:Date.now()},this.storage.setItem(this.sessionKey,t),this.sessionExp=setInterval((function(){e.checkEXp()}),this.expireTime)},e.prototype.getSessionId=function(){var e=this.storage.getItem(this.sessionKey);return this.disableSession?"":e&&e.sessionId?e.sessionId:""},e.prototype.resetExpTime=function(){var e=this;this.sessionExp&&(clearInterval(this.sessionExp),this.sessionExp=setInterval((function(){e.checkEXp()}),this.expireTime))},e.prototype.resetSessionId=function(){var e={sessionId:eH(),timestamp:Date.now()};this.storage.setItem(this.sessionKey,e)},e.prototype.checkEXp=function(){var e=this.storage.getItem(this.sessionKey);e&&e.sessionId&&Date.now()-e.timestamp+30>=this.expireTime&&(e={sessionId:eH(),timestamp:Date.now()},this.storage.setItem(this.sessionKey,e))},e}(),nH=function(){function e(e){this.disableAutoPageView=!1,this.staging=!1,this.sended=!1,this.started=!1,this.adapters={},this.sdkReady=!1,this.name=e,this.hook=new IV,this.Types=qV,this.adapters.fetch=YV,this.adapters.storage=VV}return e.prototype.init=function(e){var t,n,r=this;this.inited?console.log("init can be call only one time"):e&&RV(e)?e.app_id&&"number"==typeof(n=e.app_id)&&!isNaN(n)?e.app_key&&"string"!=typeof e.app_key?console.warn("app_key param is error, must be string, please check!"):(e.channel_domain||-1!==hk(t=["cn","sg","va"]).call(t,e.channel)||(console.warn("channel must be `cn`, `sg`,`va` !!!"),e.channel="cn"),this.inited=!0,this.configManager=new GV(this,e),this.initConfig=e,e.disable_auto_pv&&(this.disableAutoPageView=!0),this.configManager.set({app_id:e.app_id}),this.eventManager=new JV,this.tokenManager=new ZV,this.sessionManager=new tH,Vw.all([new Vw((function(e){r.once(qV.TokenComplete,(function(){e(!0)}))})),new Vw((function(e){r.once(qV.Start,(function(){e(!0)}))}))]).then((function(){r.sdkReady=!0,r.emit(qV.Ready),console.info("appid: "+e.app_id+", userInfo:"+sM(r.configManager.get("user"))),console.info("appid: "+e.app_id+", sdk is ready, version is 0.0.3-lite, you can report now !!!"),r.pageView()})),this.tokenManager.apply(this,e),this.eventManager.apply(this,e),this.sessionManager.apply(this,e),this.emit(qV.Init)):console.warn("app_id param is error, must be number, please check!"):console.warn("init params error,please check")},e.prototype.config=function(e){if(this.inited)if(e&&RV(e)){e._staging_flag&&1===e._staging_flag&&(this.staging=!0),e.disable_auto_pv&&(this.disableAutoPageView=!0,delete e.disable_auto_pv);var t=kV({},e);t.user_unique_id;var n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&hk(t).call(t,r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof fE){var i=0;for(r=fE(e);i<r.length;i++)hk(t).call(t,r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}(t,["user_unique_id"]);t.hasOwnProperty("user_unique_id")&&this.emit(qV.ConfigUuid,t.user_unique_id),this.configManager.set(n)}else console.warn("config params is error, please check");else console.warn("config must be use after function init")},e.prototype.getConfig=function(e){return this.configManager.get(e)},e.prototype.start=function(){this.inited&&!this.sended&&(this.sended=!0,this.emit(qV.Start))},e.prototype.event=function(e,t){var n=this;try{var r=[];Array.isArray(e)?e.forEach((function(e){r.push(n.processEvent(e[0],e[1]||{}))})):r.push(this.processEvent(e,t)),r.length&&(this.emit(qV.Event,r),this.emit(qV.SessionResetTime))}catch(i){console.warn("something error, please check")}},e.prototype.beconEvent=function(e,t){if(Array.isArray(e))console.warn("beconEvent not support batch report, please check");else{var n=[];n.push(this.processEvent(e,t||{})),n.length&&(this.emit(qV.BeconEvent,n),this.emit(qV.SessionResetTime))}},e.prototype.processEvent=function(e,t){void 0===t&&(t={});try{var n=e;/^event\./.test(e)&&(n=dk(e).call(e,6));var r=t;"object"!=Sc(r)&&(r={}),r.event_index=OV();var i=void 0;return r.local_ms?(i=r.local_ms,delete r.local_ms):i=+new Date,{event:n,params:r,local_time_ms:i}}catch(o){return{event:e,params:t}}},e.prototype.on=function(e,t){this.hook.on(e,t)},e.prototype.once=function(e,t){this.hook.once(e,t)},e.prototype.off=function(e,t){this.hook.off(e,t)},e.prototype.emit=function(e,t,n){this.hook.emit(e,t,n)},e.prototype.set=function(e){this.hook.set(e)},e.prototype.pageView=function(){this.disableAutoPageView||this.predefinePageView()},e.prototype.predefinePageView=function(e){void 0===e&&(e={});var t={title:document.title||location.pathname,url:location.href,url_path:location.pathname,time:Date.now(),referrer:window.document.referrer,$is_first_time:""+(this.configManager&&this.configManager.is_first_time)},n=kV({},t,e);this.event("predefine_pageview",n)},e}(),rH=new nH("default"),iH=new Uint8Array(16);function oH(){if(!XV&&!(XV="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return XV(iH)}for(var aH=[],sH=0;sH<256;++sH){var lH;aH.push(dk(lH=(sH+256).toString(16)).call(lH,1))}var uH={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function cH(e,t,n){if(uH.randomUUID&&!t&&!e)return uH.randomUUID();var r=(e=e||{}).random||(e.rng||oH)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var i=0;i<16;++i)t[n+i]=r[i];return t}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return aH[e[t+0]]+aH[e[t+1]]+aH[e[t+2]]+aH[e[t+3]]+"-"+aH[e[t+4]]+aH[e[t+5]]+"-"+aH[e[t+6]]+aH[e[t+7]]+"-"+aH[e[t+8]]+aH[e[t+9]]+"-"+aH[e[t+10]]+aH[e[t+11]]+aH[e[t+12]]+aH[e[t+13]]+aH[e[t+14]]+aH[e[t+15]]}(r)}var hH="veplayer_live_device_id",fH="veplayer_live_user_id",dH=function(e){return Math.floor((9*Math.random()+1)*Math.pow(10,e-1)).toString()},pH=function(e){if(!e)return"";var t,n=TM(e).call(e,"//");n&&(e=location.protocol+e);try{var r,i,o=new nM(e);if(null==o||null===(r=o.searchParams)||void 0===r||!r.get("_session_id"))null==o||null===(i=o.searchParams)||void 0===i||i.append("_session_id",vH());e=o.toString()}catch(PH){}return n?null===(t=e)||void 0===t?void 0:t.replace(location.protocol,""):e},vH=function(){return cH().replace(/-/g,"")+"."+Date.now()+"."+function(e){for(var t=0,n=0,r=(e+="").length,i=0;i<r;i++)((t=31*t+e.charCodeAt(n++))>0x7fffffffffff||t<-0x800000000000)&&(t&=0xffffffffffff);return t<0&&(t+=0x7ffffffffffff),t}(cH().replace(/-/g,""))},gH=function(e){XP(n,e);var t=$P(n);function n(){var e,r;Iy(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return Ny(BP(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"_userId",void 0),Ny(BP(r),"_deviceId",void 0),Ny(BP(r),"_liveLogger",void 0),r}return Ly(n,[{key:"afterCreate",value:function(){var e,t,n,r;this.config.enable&&(this.config.appId?(this._userId=(null===(e=this.config)||void 0===e?void 0:e.userId)||((n=localStorage.getItem(fH))||(n=dH(12),localStorage.setItem(fH,n),n)),this._deviceId=(null===(t=this.config)||void 0===t?void 0:t.deviceId)||((r=localStorage.getItem(hH))||(r=dH(11),localStorage.setItem(hH,r),r)),this.open()):console.info("not found appId, please generate an appId"))}},{key:"destroy",value:function(){this.close()}},{key:"close",value:function(){var e;null===(e=this._liveLogger)||void 0===e||e.destroy(!1),this._liveLogger=void 0}},{key:"open",value:function(){this._liveLogger||(this._start(),this.config.showUserIdInErrorPanel&&this.player.plugins.error&&(this.player.plugins.error.config.extraTips=[{label:"userId",value:null==rH?void 0:rH.getConfig("user_unique_id")}]))}},{key:"_createTea",value:function(){rH.init({app_id:468759,channel:"cn",channel_domain:"//mcs.volceapplog.com"}),rH.start(),rH.config({user_unique_id:this._userId,device_id:this._deviceId})}},{key:"_start",value:function(){this._createTea(),this._liveLogger=new fV({Tea:rH,player:this.player,aid:this.config.appId,project_key:this.config.appId,app_name:this.config.appName||this.config.appId,user_id:this._userId,device_id:this._deviceId,ext:{veplayer_version:"2.1.0",flv_version:"3.0.10-alpha.4",hls_version:"3.0.10-alpha.4",rts_version:"0.2.0-alpha.5"}})}}],[{key:"pluginName",get:function(){return"logger"}},{key:"defaultConfig",get:function(){return{appId:"",enable:!0,showUserIdInErrorPanel:!0}}}]),n}(qx);function yH(e,t){return"number"!=typeof e?"-":t?t(e):e.toString()}var mH,_H=function(e){XP(n,e);var t=$P(n);function n(){var e,r;Iy(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return Ny(BP(r=t.call.apply(t,Fm(e=[this]).call(e,o))),"_pollTimer",void 0),Ny(BP(r),"_visible",void 0),Ny(BP(r),"_playPlugin",void 0),r}return Ly(n,[{key:"_streamType",get:function(){return tN.getStreamType(this.player.config.url)}},{key:"afterCreate",value:function(){var e=this;this._playPlugin=this.player.plugins[this._streamType],this.player.on(FB.ERROR,(function(){return e.close()})),this.config.visible&&this.player.once(FB.LOADED_DATA,(function(){return e.open()}))}},{key:"open",value:function(){var e;this._visible||(ck(e=["flv","hls"]).call(e,this._streamType)?this.player.plugins[this._streamType]?(this.root.style.display="block",this._tick(),this._visible=!0):console.warn("不支持或未加载".concat(this._streamType,"插件")):console.warn("仅支持flv和hls格式的信息展示"))}},{key:"close",value:function(){this._visible&&(clearTimeout(this._pollTimer),this.root.innerHTML="",this.root.style.display="none",this._visible=!1)}},{key:"destroy",value:function(){var e=this;this.close(),this.player.off(FB.LOADED_DATA,(function(){return e.open()})),this.player.off(FB.ERROR,(function(){return e.close()}))}},{key:"render",value:function(){return'\n    <xg-live-panel class="veplayer-live-panel">\n    </xg-live-panel>\n    '}},{key:"_tick",value:function(){var e=this;clearTimeout(this._pollTimer),this._renderPanel(),this._pollTimer=setTimeout((function(){e._tick()}),this.config.interval)}},{key:"_getStats",value:function(){var e;return null===(e=this._playPlugin)||void 0===e?void 0:e.getStats()}},{key:"_getPanelData",value:function(){var e,t,n,r,i=this._getStats()||{},o="".concat(this._streamType,"_live"),a=this._playPlugin.softDecode,s=[(e=Dm(Dm({},i),{},{format:o}),{rows:[{key:"format",labelTextKey:"FORMAT",value:null!==(t=null==e?void 0:e.format)&&void 0!==t?t:"-"},{key:"fps",labelTextKey:"FPS",value:yH(e.fps,(function(e){return"".concat(e," fps")}))},{key:"bitrate",labelTextKey:"BITRATE",value:yH(e.bitrate,(function(e){return"".concat(e/1e3," kbps")}))},{key:"gop",labelTextKey:"GOP",value:yH(e.gop,(function(e){return"".concat(e," frames")}))},{key:"resolution",labelTextKey:"RESOLUTION",value:Fm(n="".concat(yH(e.height)," * ")).call(n,yH(e.height))},{key:"encodeType",labelTextKey:"ENCODE_TYPE",value:null!==(r=e.encodeType)&&void 0!==r?r:"-"},{key:"bufferEnd",labelTextKey:"BUFFER_END",value:yH(e.bufferEnd)},{key:"currentTime",labelTextKey:"CURRENT_TIME",value:yH(e.currentTime,(function(e){return e.toFixed(6)+"s"}))}]})];return a&&s.push(function(e){return{titleTextKey:"DECODE_INFO",rows:[{key:"decodeEfficiency",labelTextKey:"DECODE_EFFICIENCY",value:yH(e.decodeFps,(function(e){return e+"frames/s"}))},{key:"decodeCost",labelTextKey:"DECODE_COST",value:yH(e.decodeFps,(function(e){return(e?(1e3/e).toFixed():0)+"ms"}))}]}}(i)),s}},{key:"_renderPanel",value:function(){var e=this,t=this._getPanelData(),n=this.root;n&&(n.innerHTML="",t.forEach((function(t){e._renderGroup(n,t)})))}},{key:"_renderGroup",value:function(e,t){var n=this,r=t.title,i=t.titleTextKey,o=t.rows,a=this.player.config.i18nManager;if(r||i){var s,l=this._genTitleDom(null!==(s=a.getText(i))&&void 0!==s?s:r);e.appendChild(l)}o.forEach((function(t){var r=n._genRowDom(t);e.appendChild(r)}))}},{key:"_genTitleDom",value:function(e){var t=bC.createDom("xg-live-panel","",void 0,"veplayer-live-panel-row"),n=bC.createDom("xg-live-panel",e,void 0,"veplayer-live-panel-row-title");return t.appendChild(n),t}},{key:"_genRowDom",value:function(e){var t=this.player.config.i18nManager,n=bC.createDom("veplayer-live-panel-row","",void 0,"veplayer-live-panel-row"),r=bC.createDom("xg-live-panel",t.getText(e.labelTextKey),void 0,"veplayer-live-panel-row-label");n.appendChild(r);var i=bC.createDom("xg-live-panel",e.value);return n.appendChild(i),n}}],[{key:"pluginName",get:function(){return"infoPanel"}},{key:"defaultConfig",get:function(){return{visible:!1,interval:500}}}]),n}(qx),bH=Dm(Dm({},AN),{},{INVALID_PARAMETER:"The imported parameter is empty, please pass in necessary parameters such as the stream address",FORMAT:"format",FPS:"fps",BITRATE:"bitrate",GOP:"gop",RESOLUTION:"resolution",ENCODE_TYPE:"encodeType",BUFFER_END:"bufferEnd",CURRENT_TIME:"currentTime",DECODE_EFFICIENCY:"efficiency",DECODE_COST:"cost",DECODE_INFO:"decodeInfo",REFRESH:"refresh"}),wH=Dm(Dm({},IN),{},{INVALID_PARAMETER:"入参为空，请传入流地址等必要参数",FORMAT:"格式",FPS:"帧率",BITRATE:"码率",GOP:"GOP",RESOLUTION:"视频分辨率",ENCODE_TYPE:"编码方式",BUFFER_END:"Buffer 水位",CURRENT_TIME:"播放进度",DECODE_EFFICIENCY:"解码效率",DECODE_COST:"解码消耗",DECODE_INFO:"软解信息",REFRESH:"刷新"});RN.extend([{LANG:"zh-cn",TEXT:wH},{LANG:"en",TEXT:bH}]);var kH={autoplay:{muted:!0}},EH=Fm(mH=[]).call(mH,Ay(kN),[HB,gH,_H]),TH=function(e){return e.H264="h264",e}(TH||{}),SH=function(e){XP(n,e);var t=$P(n);function n(e){return Iy(this,n),t.call(this,e)}return Ly(n,[{key:"openLog",value:function(){var e;null===(e=this._player.plugins)||void 0===e||e.logger.open()}},{key:"closeLog",value:function(){var e;null===(e=this._player.plugins)||void 0===e||e.logger.close()}},{key:"openInfoPanel",value:function(){var e;null===(e=this._player.plugins)||void 0===e||e.infopanel.open()}},{key:"closeInfoPanel",value:function(){var e;null===(e=this._player.plugins)||void 0===e||e.infopanel.close()}}]),n}(NN);function CH(){return(CH=ay(iy().mark((function e(t){var n,r,i,o,a,s,l;return iy().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=void 0,t&&(t.url||t.playlist)){e.next=3;break}throw uB(sB.INVALID_PARAMETER,new RN);case 3:return o=new RN({lang:null==t?void 0:t.lang,i18n:null==t?void 0:t.i18n}),a=Dm(Dm({},t),{},{plugins:Fm(n=[]).call(n,Ay(EH),Ay(null!==(r=t.plugins)&&void 0!==r?r:[]))}),e.next=7,NN.create(Dm(Dm(Dm({},kH),a),{},{isLive:!0,i18nManager:o,preProcessUrl:function(e){return{url:pH(e)}},preparePlugins:function(e){return ay(iy().mark((function t(){return iy().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",mB(Dm(Dm({},a),{},{url:e}),i));case 1:case"end":return t.stop()}}),t)})))()}}),SH);case 7:return(i=e.sent)&&(l=null===(s=i)||void 0===s||null===(s=s._player)||void 0===s||null===(s=s.config)||void 0===s?void 0:s._RTMdegrade)&&i.emit("degrade",{originRtmUrl:l._originRtmUrl,code:"NOT_SUPPORT",message:"not support rtm or h264",isRTMSupported:l._isRTMSupported,isRTMSupportCodec:l._isRTMSupportCodec}),e.abrupt("return",i);case 10:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var xH=Object.freeze({__proto__:null,ErrorCode:sB,Events:FB});zL("esm"),tN.isMseSupported,tN.isSoftDecodeSupported,zL("umd"),e.Codec=t,e.DecodeType=r,e.Degradation=n,e.DynamicModule=wL,e.EN=AN,e.POSITIONS=DN,e.Plugin=qx,e.RTMCodec=TH,e.Sniffer=xC,e.ZH_CN=IN,e.createLivePlayer=function(e){return CH.apply(this,arguments)},e.isMseSupported=$L,e.isRTMSupportCodec=fB,e.isRTMSupported=cB,e.isSoftDecodeSupported=ZL,e.live=xH,e.load=KL,e.register=jL,e.util=tN}));
