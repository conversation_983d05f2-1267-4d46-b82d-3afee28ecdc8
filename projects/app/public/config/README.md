# 运行时配置说明

本目录包含应用程序的运行时配置文件，可以在部署后修改这些配置来控制应用的行为，而无需重新构建应用。

## 配置文件

- `runtime-config.json`: 主要配置文件，包含各种功能特性的开关

## 可配置项

在`runtime-config.json`中，您可以配置以下选项：

### 界面元素显示控制

- `show_backToTop`: 控制是否显示"返回顶部"按钮（默认：true）
- `show_appTip`: 控制是否显示小程序提示（默认：true）

## 如何修改配置

1. 在部署环境中，找到应用静态资源目录下的`config/runtime-config.json`文件
2. 使用文本编辑器打开文件
3. 修改配置项的值（true表示显示，false表示隐藏）
4. 保存文件
5. 刷新应用页面，新的配置将会生效

> **注意**: 应用会直接通过HTTP请求加载此配置文件，确保该文件在Web服务器上可正常访问。

## 示例配置

```json
{
  "show_backToTop": true,
  "show_appTip": false
}
```

上述配置将显示"返回顶部"按钮，但隐藏小程序提示。

## 配置加载原理

应用程序会在启动时通过HTTP请求加载`/config/runtime-config.json`文件，并将其中的配置与系统默认配置合并。这种方式允许您在不重新构建应用的情况下修改界面元素的显示状态。 