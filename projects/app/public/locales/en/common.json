{"App": "App", "Create New": "", "Export": "Export", "Folder": "Folder", "Move": "Move", "Name": "Name", "Rename": "<PERSON><PERSON>", "Running": "Running", "Select value is empty": "Select value is empty", "UnKnow": "UnKnow", "Warning": "Warning", "app": {"AI Advanced Settings": "Advanced Settings", "AI Settings": "AI Settings", "Advance App TestTip": "The current application is advanced editing mode \n. If you need to switch to [simple mode], please click the save button on the left", "App Detail": "App Detail", "Basic Settings": "Basic Settings", "Chat Debug": "Chat Debug", "Chat Logs Tips": "Logs record the app's online, shared, and API(chatId is existing) conversations", "Chat logs": "Chat Logs", "Confirm Del App Tip": "Confirm to delete the app and all its chats", "Connection is invalid": "Connecting is invalid", "Connection type is different": "Connection type is different", "Copy Module Config": "Copy config", "Dataset Quote Template": "Dataset Mode", "Export Config Successful": "The configuration has been copied. Please check for important data", "Export Configs": "Export Configs", "Feedback Count": "User <PERSON>", "Import Configs": "Import Configs", "Import Configs Failed": "Failed to import the configuration, please ensure that the configuration is normal!", "Input Field Settings": "Input Field Settings", "Logs Empty": "Logs is empty", "Logs Message Total": "Message Count", "Logs Source": "Source", "Logs Time": "Time", "Logs Title": "Title", "Mark Count": "<PERSON>", "My Apps": "My Apps", "My AI Copilot": "My AI Copilot", "Open AI Advanced Settings": "Advanced Settings", "Output Field Settings": "Output Field Settings", "Paste Config": "<PERSON>e Config", "To Chat": "To <PERSON><PERSON>", "To Settings": "To <PERSON><PERSON><PERSON> Page", "Variable Key Repeat Tip": "Variable Key Repeat", "module": {"Combine Modules": "<PERSON><PERSON><PERSON>", "Custom Title Tip": "The title name is displayed during the conversation", "My Modules": "My Custom Modules", "No Modules": "No module", "System Module": "System Module", "type": "{{type}}\n{{description}}"}, "modules": {"Title is required": "Title is required"}}, "common": {"Add": "Add", "Add New": "Add", "All": "All", "Back": "Back", "Beta": "Beta", "Business edition features": "This is the commercial version function ~", "Choose": "<PERSON><PERSON>", "Close": "Close", "Collect": "Collect", "Config": "Config", "Confirm": "Confirm", "Confirm Create": "Create", "Confirm Import": "Import", "Confirm Move": "Move here", "Confirm Update": "Update", "Copy": "Copy", "Copy Successful": "Copy Successful", "CopyToDataset": "CopyToDataset", "Course": "", "Create Failed": "Create Failed", "Create New": "Create", "Create Success": "Create Success", "Create Time": "Create time", "Custom Title": "Custom Title", "Delete": "Delete", "Delete Failed": "Delete Failed", "Delete Success": "Delete Successful", "Delete Tip": "Delete Confirm", "Delete Warning": "Warning", "Done": "Done", "Edit": "Edit", "Exit": "Exit", "Expired Time": "Expired", "File": "File", "Filed is repeat": "Filed is repeated", "Filed is repeated": "", "Finish": "Finish", "Input": "Input", "Intro": "Intro", "Invalid Json": "<PERSON><PERSON><PERSON>", "Last Step": "Last", "Last use time": "Last use time", "Load Failed": "Load Failed", "Loading": "Loading", "Max credit": "Credit", "Max credit tips": "What is the maximum amount of money that can be consumed by the link? If the link is exceeded, it will be banned. -1 indicates no limit.", "More settings": "More settings", "Name": "Name", "Name Can": "Name Can't Be Empty", "Name is empty": "Name is empty", "New Create": "Create", "New Create AI Copilot": "New Create AI Copilot", "Next Step": "Next", "Not open": "Close", "Number of words": "{{amount}} words", "OK": "OK", "Opened": "Opened", "Output": "Output", "Params": "Params", "Password inconsistency": "Password inconsistency", "Please Input Name": "Please Input Name", "Price used": "Usage", "Read document": "Read document", "Read intro": "Read intro", "Rename": "<PERSON><PERSON>", "Rename Failed": "Rename Failed", "Rename Success": "Rename Success", "Request Error": "Request Error", "Require Input": "Required", "Root folder": "Root folder", "Save": "Save", "Save Failed": "Save Failed", "Save Success": "Save Success", "Search": "Search", "Select File Failed": "Select File Failed", "Select One Dataset": "Select a Dataset", "Select One Folder": "Select a folder", "Select template": "Select template", "Set Avatar": "Set Avatar", "Set Name": "Make a nice name", "Setting": "Setting", "Status": "Status", "Submit failed": "Submit failed", "Submit success": "Update Success", "Team": "Team", "Test": "Test", "Time": "Time", "Un used": "Unused", "UnKnow": "UnKnow", "UnKnow Source": "UnKnow Source", "Unlimited": "Unlimited", "Update Failed": "Update Failed", "Update Success": "Update Success", "Update Successful": "Update Successful", "Update Time": "Update Time", "Update success": "Update success", "Upload File Failed": "Upload File Failed", "Username": "UserName", "Waiting": "Waiting", "Website": "Website", "avatar": {"Select Avatar": "Select Avatar", "Select Failed": "Select Failed"}, "change": "Change", "choosable": "choosable", "confirm": {"Common Tip": "Operational Confirm"}, "course": {"Read Course": "Read Course"}, "empty": {"Common Tip": "No data"}, "error": {"Select avatar failed": "Select avatar failed", "Update error": "Update error", "unKnow": "There was an accident"}, "export": "", "file": {"Empty file tip": "The file content is empty. The file may be unreadable or pure image file content.", "File Content": "File Content", "File Name": "File Name", "File content can not be empty": "File content can not be empty", "Filename Can not Be Empty": "Filename Can not Be Empty", "Read File Error": "Read file error", "Select and drag file tip": "Click or drag the file here to upload", "Select failed": "Select file failed", "Select file amount limit": "A maximum of {{max}} files can be selected", "Select file amount limit 100": "You can select a maximum of 100 files at a time", "Some file size exceeds limit": "Some files exceed: {{maxSize}}, have been filtered", "Support file type": "Support {{fileType}} files", "Support max count": "A maximum of {{maxCount}} files are supported.", "Support max size": "Maximum for a single file {{maxSize}}.", "Upload failed": "Upload failed"}, "folder": {"Drag Tip": "Click and move", "Move Success": "Move Success", "No Folder": "There's no subdirectory. Just put it here", "Root Path": "Root Folder", "empty": "There is nothing to choose from in this directory"}, "input": {"Repeat Value": "Repeat Value"}, "jsonEditor": {"Parse error": "Json parse error, please check it"}, "link": {"UnValid": "UnValid Link"}, "month": "Month", "price": {"Amount": "{{amount}}{{unit}}"}, "speech": {"error tip": "Speech Failed"}, "system": {"Help Chatbot": "<PERSON><PERSON><PERSON>", "Use Helper": "UsingHelp"}, "time": {"Just now": "Just now", "The day before yesterday": "2 days", "Yesterday": "Yesterday"}, "ui": {"textarea": {"Magnifying": "Magnifying"}}}, "core": {"Chat": "Cha<PERSON>", "Chat test": "Chat test", "Max Token": "MaxTokens", "Start chat": "Start chat", "Total chars": "Total chars: {{total}}", "Total tokens": "Tokens: {{total}}", "ai": {"Model": "Model", "Prompt": "Prompt", "model": {"Dataset Agent Model": "Agent Model", "Vector Model": "Vector Model"}}, "app": {"Ai response": "Ai response", "Api request": "Api request", "Api request desc": "Access to the existing system through API, or enterprise micro, flying book, etc", "App intro": "App intro", "App params config": "App Config", "Chat Variable": "", "External using": "External use", "Make a brief introduction of your app": "Make a brief introduction of your app", "Max tokens": "Max tokens", "Name and avatar": "Avatar & Name", "Next Step Guide": "Next step guide", "Question Guide": "", "Question Guide Tip": "At the end of the conversation, three leading questions will be asked.", "Quote prompt": "Quote prompt", "Quote templates": "Quote templates", "Random": "Random", "Save and preview": "Save", "Select TTS": "Select TTS", "Select app from template": "Select from the template", "Select quote template": "Select quote template", "Set a name for your app": "App name", "Share link": "Share", "Share link desc": "Share links with other users and use them directly without logging in", "Share link desc detail": "You can share the model directly with other users to have a conversation, and the other user can have a conversation directly without logging in. Note that this function will consume the balance of your account, please keep the link!", "Simple Config Tip": "Only basic functions are included. For complex agent functions, use advanced orchestration.", "TTS": "Audio Speech", "TTS Tip": "After this function is enabled, the voice playback function can be used after each conversation. Use of this feature may incur additional charges.", "Temperature": "Temperature", "Welcome Text": "Welcome Text", "create app": "Create App", "Create AI Copilot": "Create AI Copilot", "deterministic": "Deterministic", "edit": {"Confirm Save App Tip": "The application may be in advanced orchestration mode, and the advanced orchestration configuration will be overwritten after saving, please confirm!", "Out Ad Edit": "You are about to exit the Advanced orchestration page, please confirm", "Prompt Editor": "Prompt Editor", "Query extension background prompt": "Chat background description", "Query extension background tip": "Describing the scope of the current conversation makes it easier for AI to complete first or vague questions, thereby enhancing the knowledge base's ability to continue conversations.\nIf the value is empty, the question completion function is not used for [first question].", "Save and out": "Save out", "UnSave": "UnSave"}, "error": {"App name can not be empty": "App name can not be empty", "Get app failed": "Get app failed"}, "feedback": {"Custom feedback": "Custom feedback", "close custom feedback": "<PERSON> Feedback"}, "logs": {"Source And Time": "Source & Time"}, "navbar": {"External": "External", "Flow mode": "Flow mode", "Publish": "Publish", "Publish app": "Publish App", "Simple mode": "Simple mode"}, "outLink": {"Can Drag": "Icon Drag", "Default open": "Default Open", "Iframe block title": "Copy the Iframe below and add it to your web page", "Link block title": "Copy the link below to your browser to open", "Script Close Icon": "Close Icon", "Script Icon": "Icon", "Script Open Icon": "Open Script", "Script block title": "Add the following code to your website", "Select Mode": "Select Mode", "Select Using Way": "Select use mode", "Show History": "Show History", "Web Link": "Web Link"}, "setting": "App Setting", "share": {"Amount limit tip": "A maximum of 10 groups can be created", "Create link": "Create share", "Create link tip": "The creation is successful. The share address has been copied and can be shared directly", "Ip limit title": "IP limiting (person/minute)", "Is response quote": "Response quote", "Not share link": "No share link created", "Role check": "Custom role check"}, "simple": {"mode template select": "Template"}, "template": {"Classify and dataset": "Classification + Dataset", "Classify and dataset desc": "Classify the user's problems first, then perform different actions according to the different types of problems.", "Common template": "Common", "Common template tip": "Common template\nCan completely self-configure AI properties and knowledge base", "Dataset and guide": "Dataset + dialogue guide", "Dataset and guide desc": "Conduct a knowledge base search each time a question is asked, inject the search results into the LLM model for reference answers ", "Guide and variables": "Dialogue guide + Variables ", "Guide and variables desc": "You can send a prompt at the beginning of the conversation, or ask the user to fill in something as a variable for the conversation ", "Simple chat": "Simple chat", "Simple chat desc": "An extremely simple AI conversation application ", "Simple template": "Simple", "Simple template tip": "Simple template\nHas built-in parameter details"}, "tip": {"Add a intro to app": "Add a intro to app", "chatNodeSystemPromptTip": "Indicates the fixed guide word of the model. If this content is adjusted, the model chat direction can be guided. The content is fixed at the beginning of the context. You can use variables such as {{language}}", "userGuideTip": "You can set the guide language before the session, set global variables, set next guidelines ", "variableTip": "You can ask the user to fill in something as a specific variable for this round of conversation before the conversation starts. This module is located after the opening boot.\nvariables can be injected into other modules with string input in the form of {{variable key}}, such as: prompts, qualifiers, etc.", "welcomeTextTip": "Before each conversation begins, send an initial content. Support standard Markdown syntax, additional tags can be used :\n[shortcut button]: The user can send the question directly after clicking "}, "tts": {"Close": "NoUse", "Model alloy": "Female - Alloy", "Model echo": "Male - Echo", "Speech model": "Speech model", "Speech speed": "Speed", "Test Listen": "Test", "Test Listen Text": "Hello, this is a voice test, if you can hear this sentence, it means that the voice playback function is normal", "Web": "Browser (free)"}}, "chat": {"Admin Mark Content": "Corrected response", "Audio Speech Error": "Audio Speech Error", "Chat API is error or undefined": "The session interface reported an error or returned null", "Confirm to clear history": "Confirm to clear history?", "Confirm to clear share chat history": " Are you sure to delete all chats?", "Converting to text": "Converting to text...", "Custom History Title": "Custom history title", "Custom History Title Description": "If set to empty, chat history will be followed automatically.", "Exit Chat": "Exit", "Failed to initialize chat": "Failed to initialize chat", "Feedback Failed": "Feed<PERSON> Failed", "Feedback Mark": "<PERSON>", "Feedback Modal": "<PERSON><PERSON><PERSON>", "Feedback Modal Tip": "Enter what you find unsatisfactory", "Feedback Submit": "Submit", "Feedback Success": "Feedback Success", "Feedback Update Failed": "Feedback Update Failed", "History": "History", "History Amount": "{{amount}} records", "Mark": "<PERSON>", "Mark Description": "The annotation feature is currently in beta. \n\n After clicking Add annotation, you need to select a knowledge base in order to store annotation data. You can use this feature to quickly annotate questions and expected answers to guide the model to the next answer. At present, the annotation function, like other data in the knowledge base, is affected by the model, which does not mean that the annotation meets 100% expectations. The \n\n annotation data is only unidirectional synchronization with the knowledge base. If the knowledge base modifies the annotation data, the annotation data displayed in the log cannot be synchronized", "Mark Description Title": "<PERSON>", "New Chat": "New Chat", "Pin": "<PERSON>n", "Question Guide Tips": "I guess what you're asking is", "Quote": "Quote", "Quote Amount": "Dataset Quote:{{amount}}", "Read Mark Description": "Read mark description", "Record": "Speech", "Restart": "<PERSON><PERSON>", "Select File": "Select file", "Select Image": "Select Image", "Select dataset": "Select Dataset", "Select dataset Desc": "Select a dataset to store the expected answers", "Send Message": "Send Message", "Speaking": "I'm listening...", "Start Chat": "Start Chat", "Stop Speak": "Stop Speak", "Type a message": "Input problem", "Unpin": "Unpin", "You need to a chat app": "You need to a chat app", "error": {"Chat error": "Chat error", "Messages empty": "Interface content is empty, maybe the text is too long ~", "Select dataset empty": "You didn't choose any dataset.", "User input empty": "User question is empty"}, "feedback": {"Close User Good Feedback": "", "Close User Like": "The user like\n<PERSON><PERSON> to close the tag", "Feedback Close": "<PERSON> Feedback", "No Content": "The user did not fill in the specific feedback content", "Read User dislike": "User dislike\nClick to view content"}, "logs": {"api": "API", "online": "Online Chat", "share": "Share", "test": "Test Chat "}, "markdown": {"Edit Question": "Edit Question", "Quick Question": "Ask the question immediately", "Send Question": "Send Question"}, "quote": {"Quote Tip": "Only the actual reference content is displayed here. If the data is updated, it will not be updated in real time", "Read Quote": "Read Quote", "Read Source": "Read Source"}, "response": {"Complete Response": "Complete Response", "Extension model": "Extension model", "Plugin Resonse Detail": "Plugin <PERSON>", "Read complete response": "Read Detail", "Read complete response tips": "Click to see the detailed process", "context total length": "Context Length", "module cq": "Question classification list", "module cq result": "Classification Result", "module extract description": "Extract Description", "module extract result": "Extract Result", "module historyPreview": "Messages", "module http body": "Body", "module http result": "Response", "module http url": "Request Url", "module limit": "Count <PERSON>", "module maxToken": "MaxTokens", "module model": "Model", "module name": "Name", "module price": "Price", "module query": "Question/Query", "module question": "Question", "module quoteList": "Quotes", "module runningTime": "Time", "module search query": "Query", "module search response": "Search Result", "module similarity": "Similarity", "module temperature": "Temperature", "module time": "Running Time", "module tokens": "Tokens", "plugin output": "Plugin Output", "search using reRank": "ReRank", "text output": "Text Output"}, "retry": "Retry", "tts": {"Stop Speech": "Stop"}}, "common": {"tip": {"leave page": "Content has been modified, are you sure to leave the page?"}}, "dataset": {"All Dataset": "All Dataset", "Avatar": "Avatar", "Choose Dataset": "Choose Dataset", "Chunk amount": "Chunks", "Collection": "Collection", "Common Dataset": "Common Dataset", "Common Dataset Desc": "Knowledge bases can be built by importing files, web links, or manual entry", "Create dataset": "Create Dataset", "Dataset": "Dataset", "Dataset ID": "Dataset ID", "Dataset Type": "Dataset Type", "Delete Confirm": "Are you sure to delete the knowledge base? Data cannot be recovered after deletion, please confirm!", "Delete Website Tips": "Confirm to delete the website", "Empty Dataset": "", "Empty Dataset Tips": "There is no knowledge base yet, go create one!", "File collection": "File collection", "Folder Dataset": "Folder", "Folder placeholder": "This is a folder", "Go Dataset": "To Dataset", "Intro Placeholder": "This dataset has not yet been introduced~", "Manual collection": "Manual collection", "My Dataset": "My Dataset", "Name": "Name", "Quote Length": "Quote Length", "Read Dataset": "Read Dataset", "Search score tip": "{{scoreText}}Here are the rankings and scores:\n----\n{{detailScore}}", "Select dataset": "Select dataset", "Set Empty Result Tip": ",Response empty text", "Set Website Config": "Configuring Website", "Similarity": "Similarity", "Sync Time": "Update Time", "Table collection": "Table collection", "Text collection": "Text collection", "Total chunks": "Chunks: {{total}}", "Website Dataset": "Website Sync", "Website Dataset Desc": "Web site synchronization allows you to build a knowledge base directly from a web link", "collection": {"Click top config website": "Config", "Collection name": "Collection name", "Collection raw text": "Collection raw text", "Empty Tip": "The collection is empty", "QA Prompt": "QA Prompt", "Start Sync Tip": "Are you sure to start synchronizing data? The old data will be deleted and then re-acquired, please confirm!", "Sync": "Data Sync", "Sync Collection": "Data Sync", "Website Create Success": "Created successfully, data is being synchronized", "Website Empty Tip": "No associated website yet", "Website Link": "Website Link", "Website Sync": "Website", "id": "Id", "metadata": {"Chunk Size": "Chunk Size", "Createtime": "Create Time", "Raw text length": "Raw text length", "Read Metadata": "Read Metadata", "Training Type": "Training Type", "Updatetime": "Update Time", "Web page selector": "Web Selector", "metadata": "<PERSON><PERSON><PERSON>", "read source": "Read Source", "source": "Source", "source name": "Source Name", "source size": "Source Size"}, "status": {"active": "Ready", "syncing": "Syncing"}, "sync": {"result": {"sameRaw": "The content has not changed and no update is required.", "success": "Start synchronization"}}}, "data": {"Auxiliary Data": "Auxiliary Data", "Auxiliary Data Placeholder": "This section is optional, usually in conjunction with the previous [data content], to build a structured prompt word, for special scenarios, up to {{maxToken}} words.", "Auxiliary Data Tip": "This section is divided into optional fields \n This content is usually designed to work with the previous data content to build structured prompt words for special scenarios", "Data Content": "Data Content", "Data Content Placeholder": "The input field is required, and the content is usually a description of the knowledge point, or it can be a user question, at most {{maxToken}} words.", "Data Content Tip": "The input box is a required field \n The content is usually a description of the knowledge point, but also can be a user question.", "Default Index Tip": "Cannot be edited, the default index will use the text of [related data content] and [auxiliary data] to generate an index directly, if the default index is not needed, you can delete it. Each piece of data must have more than one index. After all indexes are deleted, a default index is automatically generated.", "Edit": "Edit Data", "Empty Tip": "This collection has no data yet", "Main Content": "Main content", "Search data placeholder": "Search relevant data", "Too Long": "Content is too long", "Total Amount": "{{total}} Chunks", "data is deleted": "Data is deleted", "get data error": "Get data error", "id": "Data ID", "unit": "pieces"}, "embedding model tip": "The index model can transform the natural language into vectors for semantic retrieval. Note that different index models cannot be used together and cannot be modified after selecting the index model.", "error": {"Data not found": "The data does not exist or has been deleted", "Start Sync Failed": "Start Sync Failed", "Template does not exist": "Template does not exist", "unAuthDataset": "No access to this knowledge base ", "unAuthDatasetCollection": "Not authorized to manipulate this data set ", "unAuthDatasetData": "Not authorized to manipulate this data ", "unAuthDatasetFile": "No permission to manipulate this file ", "unCreateCollection": "No permission to manipulate this data ", "unLinkCollection": "not a network link collection"}, "file": "File", "folder": "Folder", "import": {"Auto process": "Auto", "Auto process desc": "Automatically set segmentation and preprocessing rules", "CSV Import": "CSV QA Import", "CSV Import Tip": "Import q and a from csv, data is required to be sorted out in advance", "Chunk Range": "Range: {{min}}~{{max}}", "Chunk Split": "Chunk Split", "Chunk Split Tip": "After the text is segmented according to certain rules, it is converted into a format that can conduct semantic search, which is suitable for most scenarios.", "Chunk length": "Chunk length", "Csv format error": "The csv file format is incorrect, please ensure that the index and content columns are two", "Custom file": "", "Custom process": "Custom rule", "Custom process desc": "Customize scoring and preprocessing rules", "Custom prompt": "Custom prompt", "Custom split char": "Custom split char", "Custom split char Tips": "Allows you to block according to custom delimiters. It is usually used for processed data, using specific delimiters to precisely block it.", "Custom text": "Custom text", "Custom text desc": "Manually enter a piece of text as the collection", "Data Preprocessing": "Data Preprocessing", "Data file progress": "Data upload progress", "Data process params": "Data process params", "Down load csv template": "Down load csv template", "Embedding Estimated Price Tips": "Index billing: {{price}}/1k chars", "Estimated Price": "Estimated Price: : {{amount}}{{unit}}", "Estimated Price Tips": "QA charges\nInput: {{inputPrice}}/1k tokens\nOutput: {{outputPrice}}/1k tokens", "Fetch Error": "Get link failed", "Fetch Url": "Url", "Fetch url placeholder": "Up to 10 links, one per line.", "Fetch url tip": "Only static links can be read, please check the results", "File chunk amount": "Chunks: {{amount}}", "File list": "Files", "Ideal chunk length": "Chunk length", "Ideal chunk length Tips": "Segment by end symbol. We recommend that your document should be properly punctuated to ensure that each complete sentence length does not exceed this value \n Chinese document recommended 400~1000\n English document recommended 600~1200", "Import Failed": "Import Failed", "Import Success Tip": "The {{num}} group data is imported successfully. Please wait for training.", "Import Tip": "This task cannot be terminated and takes some time to generate indexes. Please confirm the import. If the balance is insufficient, the unfinished task will be suspended and can continue after topping up.", "Link name": "Link name", "Link name placeholder": "Only static links are supported\nOne per line, up to 10 links at a time", "Local file": "Local file", "Local file desc": "Upload files in PDF, TXT, DOCX and other formats", "Only Show First 50 Chunk": "Show only part", "Preview chunks": "Chunks", "Preview raw text": "Preview file text (max show 10000 words)", "Process way": "Process way", "QA Estimated Price Tips": "QA billing: {{price}}/1k characters (including input and output)", "QA Import": "QA Split", "QA Import Tip": "According to certain rules, the text is broken into a larger paragraph, and the AI is invoked to generate a question and answer pair for the paragraph.", "Re Preview": "RePreview", "Select file": "Select file", "Select source": "Select source", "Set Chunk Error": "Split chunks error", "Source name": "Source name", "Sources list": "Sources", "Start upload": "Start", "Total Chunk Preview": "Chunk Preview: {{totalChunks}} ", "Total files": "Total {{total}} files", "Total tokens": "Tokens", "Training mode": "Training mode", "Upload data": "Upload data", "Upload file progress": "File upload progress", "Upload status": "Upload status", "Upload success": "Upload success", "Web link": "Web link", "Web link desc": "Fetch static web content as a collection"}, "link": "Link", "search": {"Basic params": "Basic settings", "Dataset Search Params": "Dataset search configuration", "Embedding score": "Embedding score", "Empty result response": "Empty Response", "Empty result response Tips": "If you fill in the content, if no suitable content is found, you will directly reply to the content.", "Filter": "Search filter", "Limit": "Search limit", "Max Tokens": "<PERSON>", "Max Tokens Tips": "The maximum number of Tokens in a single search, about 1 word in Chinese =1.7Tokens, about 1 word in English =1 tokens", "Min Similarity": "Min Similarity", "Min Similarity Tips": "The similarity of different index models is different, please use the search test to select the appropriate value", "Nonsupport": "Nonsupport", "Not similarity": "", "Params Setting": "Params Setting", "Quote index": "Quote index", "Rank": "Rank", "Rank Tip": "Ranking in all data", "ReRank": "ReRank", "ReRank desc": "The rearrangement model is used for secondary ranking to enhance the overall ranking.", "Read score": "Read score", "Rerank score": "ReRank score", "Score": "Score", "Search type": "Type", "Source id": "Source ID", "Source name": "Source", "Top K": "Top K", "Using cfr": "Open query extension", "Using query extension": "", "mode": {"embedding": "Vector recall", "embedding desc": "Use vectors for text correlation queries", "fullTextRecall": "Full text recall ", "fullTextRecall desc": "Using traditional full-text search, suitable for finding data with specific keywords and main predicates", "mixedRecall": "Mixed recall", "mixedRecall desc": "Returns the combined results of vector and full-text searches, sorted using the RRF algorithm."}, "score": {"embedding": "Embedding", "embedding desc": "Text correlation query using vectors", "fullText": "FullText", "fullText desc": "Calculate the score of the same keyword, ranging from 0 to infinity.", "fullTextRecall": "Full text recall", "fullTextRecall desc": "Using traditional full-text search, suitable for finding specific data with keywords and main predicates ", "mixedRecall": "Mixed recall", "mixedRecall desc": "Returns the combined results of vector and full-text searches, sorted using the RRF algorithm.", "reRank": "ReRank", "reRank desc": "The correlation degree between sentences was calculated by ReRank model, ranging from 0 to 1.", "rrf": "RRF", "rrf desc": "Multiple search results are combined by inverting calculation."}, "search mode": "Search Mode"}, "settings": {"Search basic params": ""}, "status": {"active": "Ready", "syncing": "Syncing"}, "test": {"Batch test": "Batch test", "Batch test Placeholder": "Select one csv file", "Search Test": "Search Test", "Test": "Start", "Test File": "", "Test Result": "Results", "Test Text": "Text", "Test Text Placeholder": "Enter the text you want to test", "Test params": "Search Params", "delete test history": "Delete the test result", "test history": "Test History", "test result placeholder": "The test results will be presented here", "test result tip": "The contents of the knowledge base are sorted according to their similarity to the test text, and you can adjust the corresponding text according to the test results. Note: The data in the test record may have been modified, clicking on a test data will show the latest data."}, "training": {"Agent queue": "QA wait list", "Chunk mode": "Chunk split", "Full": "Expect more than 5 minutes", "Leisure": "Leisure", "Manual": "Manual import", "Manual mode": "", "QA mode": "QA learning", "Vector queue": "Vector wait list", "Waiting": "Waiting", "Website Sync": "Website Sync"}, "website": {"Base Url": "BaseUrl", "Config": "Website Configuring", "Config Description": "The Web site synchronization function allows you to fill in the root address of a website, and the system will automatically crawl the relevant pages deeply for knowledge base training. Only crawls static websites, mainly project documents and blogs.", "Confirm Create Tips": "Confirm to synchronize the site, the synchronization task will start later, please confirm!", "Confirm Update Tips": "Are you sure to update the site configuration? The synchronization starts immediately with the new configuration. Please confirm", "Selector": "Selector", "Selector Course": "Instructions", "Start Sync": "Start Sync", "UnValid Website Tip": "Your site may not be static and cannot be synchronized"}}, "module": {"Add question type": "Add type", "Can not connect self": "Cannot connect itself", "Data Type": "Data Type", "Dataset quote": {"Add quote": "Add quote", "Concat result": "Concat result", "Input description": "", "label": "Dataset quote"}, "Field Description": "Description", "Field Name": "Name", "Field Type": "Type", "Field key": "Key", "Http request props": "Request props", "Http request settings": "Request settings", "Http reqiest timeout": "Request timeout", "Input Type": "Input Type", "Plugin output must connect": "Custom outputs must all be connected", "QueryExtension": {"placeholder": "Questions about python introduction and usage, etc. The current conversation is related to the game GTA5.", "tip": "Describes the scope of the current conversation, making it easier for the AI to complete first or vague questions, thereby enhancing the knowledge base's ability to continue conversations.If \n is empty, the question completion function is not used in the first conversation. "}, "Unlink tip": "[{{name}}] An unfilled or unconnected parameter exists", "Variable": "Variables", "Variable Setting": "Variable Setting", "edit": {"Field Already Exist": "Key already exist", "Field Edit": "Field Edit"}, "extract": {"Enum Description": "Lists the possible values for the field, one per row", "Enum Value": "Enum", "Field Description Placeholder": "Name/age /sql statement......", "Field Setting Title": "Extract field configuration"}, "http": {"Add props": "Add props", "AppId": "appId", "ChatId": "chatId", "Current time": "Current time", "Histories": "histories", "Key already exists": "Key already exists", "Key cannot be empty": "Name cannot be empty", "Props name": "Name", "Props tip": "You can set parameters related to Http requests\nGlobal changes or external parameter inputs can be invoked by {{key}}", "Props value": "Value", "ResponseChatItemId": "Ai response id", "Url and params have been split": "Path parameters are automatically added to Params", "Variables": "Global variables", "params": ""}, "input": {"Add Input": "Add Input", "Input Number": "Input: {{length}}", "description": {"Background": "", "Http Request Header": "User-defined request header, please strictly fill in the JSON string.\n1. Make sure the last attribute has no commas\n2. Make sure key contains double quotes\nFor example: {\"Authorization\":\"Bearer xxx\"}", "Http Request Url": "New HTTP request address. If two 'request addresses' appear, the module can be deleted and rejoined, and the latest module configuration will be pulled.", "Quote": "Object array format, structure:\n[{q:' question ',a:' answer '}]", "Response content": "You can use \\n to achieve continuous line wrapping.\nReplies can be achieved by external module input, which overwrites the content currently filled in.\nIf passed non-string type data will be automatically converted to a string", "TFSwitch textarea": "Allows you to define a number of strings to achieve false matching, one per line, and supports regular expressions.", "Trigger": "Most of the time, you don't need to concatenate this property. You can connect this property when you need to delay execution, or precisely control the timing of execution.", "dynamic input": "Receives parameters dynamically added by the user and will be tiled in at run time ", "textEditor textarea": "The passed variable can be referenced by {{key}}. Variables support only strings or numbers."}, "label": {"Background": "Background", "Classify model": "Classify model", "Http Request Header": "Request header ", "Http Request Method": "Request Method", "Http Request Url": "Request address ", "Http Request Timeout": "Request timeout ", "LLM": "", "Quote": "Quote", "Response content": "Response content", "Select dataset": "Select dataset", "TFSwitch input tip": "", "TFSwitch textarea": "Custom False matching rule ", "aiModel": "AI model ", "anyInput": "Any input", "chat history": "chat history", "switch": "<PERSON><PERSON>", "textEditor textarea": "Text edit", "user question": "User question", "user question text": "User question text", "user question image": "User question image"}, "placeholder": {"Classify background": "For example:\n1.AIGC (Artificial Intelligence Generates content) refers to the automatic or semi-automatic generation of digital content, such as text, images, music, videos, and so on, using artificial intelligence technologies. AIGC technologies include, but are not limited to, natural language processing, computer vision, machine learning, and deep learning. These technologies can create new content or modify existing content to meet specific creative, educational, entertainment or information needs."}}, "inputType": {"chat history": "History", "dynamicTargetInput": "dynamic Target Input", "input": "Input", "selectApp": "App Selector", "selectChatModel": "Select Chat Model", "selectDataset": "Dataset Selector", "switch": "Switch", "target": "Target Data", "textarea": "Textarea"}, "output": {"Add Output": "Add Output", "Output Number": "Output: {{length}}", "description": {"Ai response content": "Will be triggered after the stream reply is complete", "New context": "Concatenate the reply content with history and return it as a new context", "Quote": "Always return an array, if you want the search results to be empty to perform additional operations, you need to use the above two inputs and the trigger of the target module", "running done": "Triggered when the module call finish"}, "label": {"Ai response content": "Response Content", "New context": "New context", "Quote": "Quote", "Search result empty": "Search result empty", "Search result not empty": "Search result not empty", "cfr result": "Response text", "result false": "False", "result true": "True", "running done": "done", "text": "Text output"}}, "template": {"Ai chat": "LLM Chat", "Ai chat intro": "Request LLM chat", "Assigned reply": "Assigned reply", "Assigned reply intro": "The module can respond directly to a specified piece of content. Often used to guide and prompt. When non-string content is passed in, it is converted to a string for output.", "Chat entrance": "Chat entrance", "Chat entrance intro": "When the user sends a content, the flow will start from this module.", "Classify question": "Classify question", "Classify question intro": "Determine the type of question based on the user's history and current issue. Multiple sets of question types can be added, here is a template example: \n type 1: Hello\ntype 2: Questions about 'use'\ntype 3: Questions about 'purchase'\ntype 4: Other questions", "Dataset search": "Dataset search", "Dataset search intro": "Invoke the Dataset search capability to find content that may be relevant to the problem", "Dataset search result concat intro": "Multiple knowledge base search results can be combined and output", "External module": "External call", "Extract field": "Text content extraction ", "Extract field intro": "Can extract specified data from the text, such as: sql statements, search keywords, code, etc.", "Function module": " Function call", "Guide module": "Guides", "Http request": "Http request", "Http request intro": " Can issue an HTTP request to implement more complex operations (Internet search, database query, etc.)", "My plugin module": "Personal plugins", "Query extension": "Query extension", "Query extension intro": "If the problem completion function is enabled, the accuracy of knowledge base search can be improved in continuous conversations. After this function is enabled, when searching the knowledge base, AI will be used to complete the missing information of the problem according to the conversation records.", "Response module": "Text output", "Running app": "Running app", "Running app intro": "You can select a different app to run", "System input module": "System input", "TFSwitch": "TF Switch", "TFSwitch intro": "Output True False based on what is passed in. By default, false is printed when the content passed in is false, undefined, null, 0, none. You can also add some custom strings to supplement the output of false.", "Tool module": "Tools", "UnKnow Module": "UnKnow Module", "User guide": "User guide", "textEditor": "Text Editor", "textEditor intro": "Output of fixed or incoming text after edit"}, "textEditor": {"Text Edit": "Text Edit"}, "valueType": {"any": "Any", "boolean": "Boolean", "chatHistory": "History", "datasetQuote": "Dataset Quote", "dynamicTargetInput": "Dynamic Input", "number": "Number", "selectApp": "Select App", "selectDataset": "Select Dataset", "string": "String"}, "variable": {"add option": "Add Option", "input type": "Text", "key": "Key", "key is required": "variable key is required", "select type": "Select", "text max length": "Max Length", "textarea type": "Textarea", "variable key is required": "", "variable name": "Name", "variable name is required": "variable name is required", "variable option is required": "Variable option is required", "variable option is value is required": "Variable option is value is required", "variable options": "Options"}, "variable add option": "Add Option"}, "plugin": {"Get Plugin Module Detail Failed": "Load plugin failed"}, "shareChat": {"Init Error": "Init Chat Error", "Init History Error": "Init History Error"}}, "dataset": {"Confirm move the folder": "Confirm Move", "Confirm to delete the data": "Confirm to delete the data?", "Confirm to delete the file": "Are you sure to delete the file and all its data?", "Create Folder": "Create Folder", "Create manual collection": "Manual collection", "Delete Dataset Error": "Delete dataset failed", "Edit Folder": "Edit <PERSON>", "Export": "Export", "Export Dataset Limit Error": "Export Data Error", "File Input": "Import File", "File Size": "File Size", "Filename": "Filename", "Files": "{{total}} Files", "Folder Name": "Input folder name", "Insert Data": "Insert", "Manual Data": "Manual Data", "Manual Input": "Manual Input", "Manual Mark": "Manual Mark", "Manual collection Tip": "Manual Collections allow you to create a custom container to hold data", "Mark Data": "<PERSON>", "Move Failed": "Move Failed", "Queue Desc": "This data refers to the current amount of training for the entire system. FastGPT uses queued training, and if you have too much data to train, you may need to wait for a while", "Select Dataset": "Select Dataset", "Select Dataset Tips": "Select only knowledge bases with the same index model", "Select Folder": "Enter folder", "System Data Queue": "Data Queue", "Training Name": "Dataset Training", "Upload Time": "Upload Time", "collections": {"Click to view file": "View File Data", "Click to view folder": "To Folder", "Collection Embedding": "{{total}}Embedding", "Confirm to delete the folder": "Are you sure to delete this folder and all its contents?", "Create And Import": "Create/Import", "Create Training Data": "Training-{{filename}}", "Create manual collection Success": "Create manual collection Success", "Data Amount": "Data Amount", "Select Collection": "Select Collection", "Select One Collection To Store": "Select the collection to store"}, "data": {"Add Index": "Add Index", "Can not delete tip": "No modification permission", "Can not edit": "No edit permission", "Custom Index Number": "Custom index{{number}}", "Default Index": "Default Index", "Delete Success Tip": "", "Delete Tip": "Confirm to delete the data?", "File import": "File Import", "Index Edit": "Data Index", "Index Placeholder": "Enter the index text content", "Input Data": "Import Data", "Input Success Tip": "Succeeded in importing data", "Update Data": "Update Data", "Update Success Tip": "Update data successfully", "edit": {"Content": "Content", "Course": "Document", "Delete": "Delete", "Index": "Index({{amount}})"}, "input is empty": "The data content cannot be empty"}, "deleteFolderTips": "Are you sure to delete this folder and all the knowledge bases it contains? Data cannot be recovered after deletion, please confirm!", "import csv tip": "Ensure that the CSV is in UTF-8 format; otherwise, garbled characters will be displayed", "test": {"noResult": "Search results are empty"}}, "error": {"fileNotFound": "File not found ~", "team": {"overSize": "Team member exceeds limit"}}, "file": {"Click to download file template": "Download Template: {{name}}", "Click to view file": "Click to view file", "Create File": "Create File", "Create file": "Create file", "Drag and drop": "Drag and drop files here", "Fetch Url": "Fetch Url", "If the imported file is garbled, please convert CSV to UTF-8 encoding format": "If the imported file is garbled, please convert CSV to UTF-8 encoding format", "Parse": "{{name}} Parsing...", "Release the mouse to upload the file": "Release the mouse to upload the file", "Select a maximum of 10 files": "Select a maximum of 10 files", "Uploading": "Uploading: {{name}}, Progress: {{percent}}%", "max 10": "Max 10 files", "max 100": "Max 100 files", "select a document": "select a document", "support": "support {{fileExtension}} file", "upload error description": "Only upload multiple files or one folder at a time"}, "home": {"AI Assistant": "AI Assistant", "AI Assistant Desc": "", "Advanced Settings": "", "Advanced Settings Desc": "", "Choice Debug": "Convenient Debugging", "Choice Debug Desc": "Search testing, reference modification, full conversation preview and many other debugging ways", "Choice Desc": "FastGPT follows the Apache License 2.0 open source protocol", "Choice Extension": "Infinite Extension", "Choice Extension Desc": "HTTP based extension, easy to achieve custom functions", "Choice Fast": "Fast", "Choice Fast Desc": "{{title}} provides out-of-the-box visual actions to build AI applications point-by-point", "Choice Models": "Multiple Models", "Choice Models Desc": "Supports multiple models such as GPT, Claude, Spark, and ChatGLM", "Choice Open": "Open", "Choice Open Desc": "{{title}} follows the Apache License 2.0 open source protocol", "Choice QA": "QA Structure", "Choice QA Desc": "The index is constructed with the structure of QA pairs, and ADAPTS to various scenarios such as Q&A and reading", "Choice Visual": "Visual workflow", "Choice Visual Desc": "Visualize modular operations, easily implement complex workflows, and make your AI no longer monolithic", "Commercial": "Commercial", "Community": "Community", "Dateset": "", "Dateset Desc": "", "Docs": "Docs", "FastGPT Ability": "{{title}} Ability", "FastGPT Desc": "{{title}} is a dataset question answering system based on LLM large language model, which provides out-of-the-box data processing, model invocation and other capabilities. At the same time, workflow orchestration can be performed through Flow visualization to achieve complex Q&A scenarios!", "Features": "Features", "Footer Developer": "Developer", "Footer Docs": "Docs", "Footer FastGPT Cloud": "{{title}} Cloud", "Footer Feedback": "<PERSON><PERSON><PERSON>", "Footer Git": "Code", "Footer Product": "Product", "Footer Support": "Support", "Login": "<PERSON><PERSON>", "Open": "", "OpenAPI": "OpenAPI", "OpenAPI Desc": "", "Quickly build AI question and answer library": "Quickly build AI question and answer library", "Start Now": "Start Now", "Visual AI orchestration": "Visual AI orchestration", "Why FastGPT": "Why {{title}}", "desc": "AI knowledge base question and answer platform based on LLM large model", "navbar": {"Use guidance": "Use Guidance", "chatbot": "<PERSON><PERSON><PERSON>"}, "slogan": "Let the AI know more about you"}, "module": {"Confirm Delete Module": "Confirm to delete the custom module?", "Confirm Sync Plugin": "Confirm the latest sync plugin information? The plug-in connection and input content will be cleared, please confirm!", "Create Your Module": "Create <PERSON>", "Intro": "<PERSON><PERSON><PERSON>", "Load Module Failed": "Load Module Failed", "Plugin input is not value": "User-defined input parameters cannot be null", "Plugin input is required": "The plug setting must contain an input module", "Plugin input must connect": "Custom input modules must all be connected", "Preview Plugin": "Preview Plugin", "Save Config": "Save", "Update Your Module": "Update Module"}, "navbar": {"Account": "Account", "Apps": "App", "AI Copilot": "AI Copilot", "Chat": "Cha<PERSON>", "Datasets": "DataSet", "Module": "<PERSON><PERSON><PERSON>", "Plugin": "Plugin", "Store": "Store", "Tools": "Tools", "Workbench": "Workbench"}, "openapi": {"app key tips": "These keys have the identification of the current application and can be used by external access.", "key alias": "<PERSON>as of key, for display only", "key tips": "You can use the API Key to access certain interfaces (you can't access the application, you need to use the API key within the application to access the application)."}, "outlink": {"Copy IFrame": "<PERSON><PERSON>", "Copy Link": "Copy", "Create API Key": "Create Key", "Create Link": "Create Link", "Delete Link": "Delete", "Edit API Key": "Edit Key", "Edit IFrame Link": "Edit IFrame Link", "Edit Link": "Edit", "Edit Share Window": "Edit Share Window", "Link Name": "Link Name", "Link is empty": "", "QPM": "QPM", "QPM Tips": "The maximum number of queries per IP address per minute", "QPM is empty": "QPM is empty", "token auth": "<PERSON><PERSON>", "token auth Tips": "Identity verification server address. If this value is set, the server will be specified to send a request for identity verification before each session", "token auth use cases": "Review the authentication instructions"}, "permission": {"Private": "Private", "Private Tip": "Be used only to oneself", "Public": "Public", "Public Tip": "Available to all team members", "Set Private": "Set Private", "Set Public": "Set to public", "Set Share": "Set Share"}, "plugin": {"Confirm Delete": "Confirm to delete the plugin?", "Create Your Plugin": "Create Plugin", "Get Plugin Module Detail Failed": "Get plugin detail failed", "Intro": "Plugin <PERSON>", "Load Plugin Failed": "Load Plugin Failed", "My Plugins": "My Plugins", "No Intro": "This plugin is not introduced", "Plugin Module": "Plugin", "Set Name": "Plugin Name", "Synchronous version": "Sync Version", "To Edit Plugin": "To Edit", "Update Your Plugin": "Update Plugin"}, "support": {"openapi": {"Api baseurl": "<PERSON><PERSON><PERSON>", "Api manager": "API key management", "Copy success": "The API address has been copied", "Max usage": "Max usage", "New api key": "New API key", "New api key tip": "Please keep your secret key, the secret key will not be displayed again~", "Usage": "Usage"}, "outlink": {"share": {"Response Quote": "Show Quote", "Response Quote tips": "The referenced content is returned in the share link, but the user is not allowed to download the original document."}}, "subscription": {"Cancel subscription": "Cancel"}, "user": {"Need to login": "Please log in first", "Price": "Price", "auth": {"Sending Code": "Sending"}, "login": {"Github": "<PERSON><PERSON><PERSON>", "Google": "Google", "Provider error": "Login exception, please try again"}, "team": {"Dataset usage": "Dataset usage", "member": "Member"}}, "wallet": {"Balance not enough tip": "The balance is insufficient, please go to the account page first", "Buy more": "Buy more", "Confirm pay": "Confirm pay", "Pay error": "Pay error", "Pay success": "Pay success", "bill": {"AI Model": "AI Model", "AI Type": "AI Type", "Price": "Price（￥）"}, "subscription": {"Ai points": "AI Points Standard", "Buy now": "Buy now", "Change will take effect after the current subscription expires": "Change will take effect after the current subscription expires", "Current dataset store": "Current dataset store subscription", "Current plan": "Current plan", "Dataset store": "Dataset store size", "Dataset store price tip": "Deduct it from the account balance on the 1st of each month", "Expand size": "Expand size", "Extra dataset size": "Extra dataset size", "Extra plan": "Extra Plan", "Extra plan tip": "When the standard plan is not enough, you can purchase an additional plan to continue using", "FAQ": "Pricing FAQs", "Next plan": "Future plan", "Next sub dataset size": "", "Nonsupport": "Nonsupport", "Refund plan and pay confirm": "There is no extra cost for you to switch this package and {{amount}} will be refunded to the balance.", "Standard plan pay confirm": "To switch this package, you need to pay {{payPrice}} Yuan.", "Standard update fail": "Update plan failed.", "Standard update success": "Change subscription plan successful!", "Sub plan": "Pricing Plans", "Sub plan tip": "Use FastGPT for free or upgrade to a higher plan", "Training weight": "Training weight: {{weight}}", "Update extra dataset size": "Update size", "function": {"History store": "", "Max app": "", "Max dataset": "", "Max dataset size": "", "Max members": "", "Points": ""}, "mode": {"Month": "Monthly", "Year": "Yearly", "Year sale": "2 months free"}, "standardSubLevel": {"enterprise": "", "experience": "", "experience desc": "", "free": "", "free desc": "", "team": ""}, "type": {"extraDatasetSize": "Extra dataset size", "standard": ""}}}}, "system": {"Help Document": "Document"}, "template": {"Quote Content Tip": "This configuration takes effect only when reference content is passed in (knowledge base search).\nYou can customize the structure of the reference content to better suit different scenarios. Some variables can be used for template configuration:\n{{q}} - retrieve content, {{a}} - expected content, {{source}} - source, {{sourceId}} - source file name, {{index}} - the first n references, {{with}} - the reference points (0-1), they are optional, Here are the default values:\n{{default}}", "Quote Prompt Tip": "This configuration takes effect only when the knowledge base is searched.\nYou can use {{quote}} to insert the reference content template and {{question}} to insert the question. Here are the default values:\n{{default}}"}, "user": {"Account": "Account", "Amount of earnings": "Earnings", "Amount of inviter": "Inviter", "Application Name": "Application Name", "Avatar": "Avatar", "Balance": "Balance", "Bill Detail": "<PERSON>", "Change": "Change", "Copy invite url": "Copy invitation link", "Edit name": "Click to modify nickname", "Invite Url": "Invite U<PERSON>", "Invite url tip": "Friends who register through this link will be permanently bound to you, and you will get a certain balance reward when they recharge. In addition, when friends register with their mobile phone number, you will get 5 yuan reward immediately.", "Language": "Language", "Member Name": "Name", "Notice": "Notice", "Old password is error": "Old password is error", "OpenAI Account Setting": "OpenAI Account Setting", "Password": "Password", "Pay": "Pay", "Permission": "Permission", "Personal Information": "Personal", "Promotion": "Promotion", "Promotion Rate": "Promotion Rate", "Promotion Record": "Promotion", "Promotion rate tip": "You will be rewarded with a percentage of the balance when your friends top up", "Recharge Record": "Recharge", "Replace": "Replace", "Set OpenAI Account Failed": "Set OpenAI account failed", "Sign Out": "Sign Out", "Source": "Source", "Team": "Team", "Tenant": "Tenant", "Time": "Time", "Timezone": "Timezone", "Total Amount": "Total Amount", "Update Password": "Update Password", "Update password failed": "Update password failed", "Update password successful": "Update password successful", "Usage Record": "Usage", "apikey": {"key": "API Keys"}, "promotion": {"pay": "", "register": ""}, "team": {"Balance": "Team Balance", "Check Team": "Switch", "Confirm Invite": "Confirm Invite", "Create Team": "Create Team", "Invite Member": "Invite", "Invite Member Failed Tip": "Invite Member Failed", "Invite Member Result Tip": "Invite Member Result", "Invite Member Success Tip": "Invite member completed \n successful :{{success}} Person \n username invalid :{{inValid}}\n Already on team :{{inTeam}}", "Invite Member Tips": "They can access or use other resources within your team", "Invite Role Admin Alias": "Invite an Administrator to join", "Invite Role Admin Tip": "Admin\nCan create, edit, and use team resources", "Invite Role Visitor Alias": "Invite visitors to Join", "Invite Role Visitor Tip": "Visitors\nCan only use resources and have no permission to create and edit them", "Leave Team": "Leave", "Leave Team Failed": "Leave Team Failed", "Manage": "Team Manage", "Member": "Member", "Member Name": "Member", "Over Max Member Tip": "Team max {{max}} people", "Personal Team": "Personal", "Processing invitations": "Processing invitations", "Processing invitations Tips": "You have {{amount}} of team invitations that need to be processed", "Reinvite": "Reinvite", "Remove Member Confirm Tip": "Are you sure you want to move {{username}} off the team? All its resources are transferred to the team creator's account.", "Remove Member Failed": "Removing a team member failed", "Remove Member Success": "Removing a team member succeeded", "Remove Member Tip": "Move out team", "Role": "Role", "Select Team": "Select Team", "Set Name": "Team Name", "Switch Team Failed": "Switch Team Failed", "Team Name": "Team Name", "Update Team": "Update Team", "invite": {"Accept Confirm": "Want to join the team?", "Accepted": "Accepted", "Deal Width Footer Tip": "It will automatically shut down after processing~", "Reject": "Rejected", "Reject Confirm": "Confirm to decline the invitation?", "accept": "Accept", "reject": "Reject"}, "member": {"Confirm Leave": "Confirm leave the team?", "active": "Accept", "reject": "Rejected", "waiting": "Waiting"}, "role": {"Admin": "Admin", "Member": "Member", "Owner": "Owner", "Update to Visitor": "Set to visitor", "Visitor": "Visitor"}}}, "wallet": {"bill": {"Ai model": "Ai Model", "App name": "App name", "Audio Speech": "Audio Speech", "Bill Module": "<PERSON>", "Chars length": "Chars length", "Data Length": "Data length", "Dataset store": "", "Duration": "Duration(s)", "Extension Input Token Length": "Extension input tokens", "Extension Output Token Length": "Extension output tokens", "Extension result": "Extension result", "Input Token Length": "Input tokens", "Module name": "Module name", "Next Step Guide": "", "Number": "Bill ID", "Output Token Length": "Output tokens", "ReRank": "ReRank", "Source": "Source", "Text Length": "Text length", "Time": "Time", "Token Length": "Tokens", "Total": "Total", "Whisper": "Whisper", "bill username": "User"}, "moduleName": {"index": "Index Generation", "qa": "QA Generation"}}}