<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OnlyOffice文档预览</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        height: 100%;
        width: 100%;
      }
      #placeholder {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
      .control-panel {
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 1000;
        background: white;
        padding: 5px;
        border-radius: 5px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
      .control-panel button {
        margin-right: 5px;
        padding: 5px 10px;
        border: none;
        border-radius: 3px;
        background-color: #7d4dff;
        color: white;
        cursor: pointer;
      }
      .control-panel button:hover {
        background-color: #6030e0;
      }
    </style>
    <!-- 引入OnlyOffice的API脚本 -->
    <script
      type="text/javascript"
      src="http://**********:8080/web-apps/apps/api/documents/api.js"
    ></script>
    <script src="https://cdn.jsdelivr.net/npm/jwt-js/build/jwt.min.js"></script>
  </head>
  <body>
    <div id="placeholder"></div>

    <script>
      window.onload = function () {
        // 基础配置
        const docConfig = {
          // 文档服务器地址和相关配置
          document: {
            fileType: 'pptx', // 文件类型: docx
            key: '1760758d1b990a45284431f1eb436c83222', // 文档唯一标识符
            title: '1760758d1b990a45284431f1eb436c83.pptx', // 文档标题
            url: 'https://huayun-ai-obs.huayuntiantu.com/1760758d1b990a45284431f1eb436c83.pptx?AccessKeyId=LIXZYLTASKIDIGHB6K3U&Expires=1755162569&filename=25%2B%2B%25E6%25B4%25BB%25E6%259D%25BF.pptx&response-content-disposition=inline%3B+filename%3D25%2B%2B%25E6%25B4%25BB%25E6%259D%25BF.pptx&Signature=c9m8fMKTzl7ucY7IwzYgandBZeo%3D' // 文档下载地址
          },
          // 文档编辑器配置
          editorConfig: {
            mode: 'view', // 查看模式：view或edit
            lang: 'zh-CN' // 界面语言
          },
          events: {
            onAppReady: function () {
              console.log('OnlyOffice文档编辑器已准备就绪');
              // 如果需要在编辑器准备就绪后执行某些操作
            },
            onDocumentReady: function () {
              console.log('文档已加载完成');
              // 文档加载完成后可以执行一些操作

              // 如果需要自动计时释放连接，可以在这里设置定时器
              /* 
              // 自动释放示例 - 120秒后自动释放
              window.autoReleaseTimer = setTimeout(function() {
                window.releaseConnection();
              }, 120000); // 120秒后自动释放连接
              */
            },
            onError: function (event) {
              console.error('发生错误:', event);
              alert('加载文档时发生错误: ' + event.data);
            },
            onDocumentStateChange: function (event) {
              // 文档状态改变时的回调
              console.log('文档状态改变:', event.data);
            }
          }
        };

        // 初始化文档编辑器
        (async function () {
          try {
            // 生成JWT令牌并添加到配置中
            docConfig.token =
              'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkb2N1bWVudCI6eyJmaWxlVHlwZSI6ImRvY3giLCJrZXkiOiJyYW5kb20iLCJ0aXRsZSI6IuaWh-Wtl-aWh-eovzIuZG9jeCIsInVybCI6Imh0dHBzOi8vaHVheXVuLWFpLW9icy5odWF5dW50aWFudHUuY29tLzdkY2M1MWM5NmYyMDliYjExZTcyMjU0MjMyMTk0ZWM1LmRvY3g_QWNjZXNzS2V5SWQ9TElYWllMVEFTS0lESUdIQjZLM1UmRXhwaXJlcz0xNzU1MTU2NDE4JmZpbGVuYW1lPSUyNUU2JTI1OTYlMjU4NyUyNUU1JTI1QUQlMjU5NyUyNUU2JTI1OTYlMjU4NyUyNUU3JTI1QTglMjVCRjIuZG9jeCZyZXNwb25zZS1jb250ZW50LWRpc3Bvc2l0aW9uPWlubGluZSUzQitmaWxlbmFtZSUzRCUyNUU2JTI1OTYlMjU4NyUyNUU1JTI1QUQlMjU5NyUyNUU2JTI1OTYlMjU4NyUyNUU3JTI1QTglMjVCRjIuZG9jeCZTaWduYXR1cmU9TWNGbm1tVldKQThXbmRmaExSV2JsTlZVRjVvJTNEIn0sImVkaXRvckNvbmZpZyI6eyJtb2RlIjoidmlldyIsImxhbmciOiJ6aC1DTiJ9fQ.5hr9hFUygGXuZaoDwpyPoTT5y1MeOb-6CVnwtVReUQk';
            console.log(docConfig);

            const docEditor = new DocsAPI.DocEditor('placeholder', docConfig);
            console.log('文档编辑器已初始化');

            // 存储编辑器实例供外部使用
            window.docEditor = docEditor;
          } catch (error) {
            console.error('初始化编辑器时发生错误:', error);
            alert('初始化编辑器时发生错误: ' + error.message);
          }
        })();

        // 示例：如何从外部修改编辑器状态
        window.switchToEdit = function () {
          if (window.docEditor) {
            window.docEditor.setMode('edit');
          }
        };

        window.switchToView = function () {
          if (window.docEditor) {
            window.docEditor.setMode('view');
          }
        };

        // 释放连接的方法
        window.releaseConnection = function () {
          if (window.docEditor) {
            // 如果有自动释放定时器，先清除它
            if (window.autoReleaseTimer) {
              clearTimeout(window.autoReleaseTimer);
              window.autoReleaseTimer = null;
            }

            // 销毁编辑器实例
            window.docEditor.destroyEditor();
            window.docEditor = null;
            // console.log('文档编辑器连接已释放');

            // // 显示已释放的状态
            // document.getElementById('placeholder').innerHTML =
            //   '<div style="display:flex;justify-content:center;align-items:center;height:100%;"><p style="font-size:16px;color:#666;">文档编辑器连接已释放</p></div>';

            // 可以在此处执行其他清理工作
            return true;
          } else {
            console.log('没有活动的编辑器实例');
            return false;
          }
        };

        // 重新加载文档
        window.reloadDocument = function () {
          if (!window.docEditor) {
            location.reload();
          } else {
            alert('请先释放当前连接');
          }
        };

        // 在页面关闭或离开时自动释放连接
        window.addEventListener('beforeunload', function () {
          window.releaseConnection();
        });
      };
    </script>

    <!-- 控制按钮 -->
    <div class="control-panel">
      <button onclick="switchToEdit()">编辑模式</button>
      <button onclick="switchToView()">查看模式</button>
      <button onclick="releaseConnection()">释放连接</button>
      <button onclick="reloadDocument()">重新加载</button>
    </div>
  </body>
</html>
